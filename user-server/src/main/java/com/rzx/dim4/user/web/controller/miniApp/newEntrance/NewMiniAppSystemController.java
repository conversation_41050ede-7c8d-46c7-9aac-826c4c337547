package com.rzx.dim4.user.web.controller.miniApp.newEntrance;

import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.user.service.newMiniApp.NewMiniAppSystemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023年10月30日 16:51
 */
@Slf4j
@RestController
@RequestMapping("/newMiniApp/system")
public class NewMiniAppSystemController {

    @Autowired
    private NewMiniAppSystemService newMiniAppSystemService;

    /**
     * 经营监管
     * @param placeId
     * @param sign
     * @return
     */
    @GetMapping("/queryRegulatorMsg")
    public GenericResponse<?> queryRegulatorMsg(@RequestParam String placeId, @RequestParam String openId,
                                                @RequestParam String sign){

        log.info("经营监管查询接口 placeId:::{},openId:::{}" , placeId,openId);
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(sign)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        StringBuffer toSignSB = new StringBuffer();
        toSignSB.append("placeId=").append(placeId).append("&");
        toSignSB.append("openId=").append(openId).append("&");
        toSignSB.append("rzx1218rzx");
        String toSign = DigestUtils.md5DigestAsHex(toSignSB.toString().getBytes());

        if (!sign.equalsIgnoreCase(toSign)) {
            return new GenericResponse<>(ServiceCodes.BAD_SIGN);
        }

        return newMiniAppSystemService.queryRegulatorMsg(placeId,openId);
    }
    /**
     * 消息已读接口
     * @param headingCode
     * @param sign
     * @return
     */
    @GetMapping("/checkMsgState")
    public GenericResponse<?> checkMsgState(@RequestParam String headingCode, @RequestParam String openId,
                                                @RequestParam String sign){

        log.info("消息已读接口 headingCode:::{},openId:::{}" , headingCode,openId);
        if (StringUtils.isEmpty(headingCode) || StringUtils.isEmpty(sign)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        StringBuffer toSignSB = new StringBuffer();
        toSignSB.append("headingCode=").append(headingCode).append("&");
        toSignSB.append("openId=").append(openId).append("&");
        toSignSB.append("rzx1218rzx");
        String toSign = DigestUtils.md5DigestAsHex(toSignSB.toString().getBytes());

        if (!sign.equalsIgnoreCase(toSign)) {
            return new GenericResponse<>(ServiceCodes.BAD_SIGN);
        }
        return newMiniAppSystemService.checkMsgState(headingCode,openId);
    }

    /**
     * 服务到期通知接口
     * @param placeId
     * @param sign
     * @return
     */
    @GetMapping("/noticeOfExpiry")
    public GenericResponse<?> noticeOfExpiry(@RequestParam String placeId,
                                                @RequestParam String sign){

        log.info("服务到期通知接口 placeId:::{}" , placeId);
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(sign)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        StringBuffer toSignSB = new StringBuffer();
        toSignSB.append("placeId=").append(placeId).append("&");
        toSignSB.append("rzx1218rzx");
        String toSign = DigestUtils.md5DigestAsHex(toSignSB.toString().getBytes());

        if (!sign.equalsIgnoreCase(toSign)) {
            return new GenericResponse<>(ServiceCodes.BAD_SIGN);
        }
        return newMiniAppSystemService.noticeOfExpiry(placeId);
    }

    /**
     * 我的场所
     * @param openId
     * @param sign
     * @return
     */
    @GetMapping("/myPlaceProfiles")
    public GenericResponse<?> myPlaceProfiles(@RequestParam String openId,@RequestParam(required = false) String placeName,
                                                @RequestParam String sign){

        log.info("我的场所 openId:::{}" , openId);
        if (StringUtils.isEmpty(openId) || StringUtils.isEmpty(sign) || "undefined".equals(openId) || "UNDEFINED".equals(openId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        StringBuffer toSignSB = new StringBuffer();
        toSignSB.append("openId=").append(openId).append("&");
        toSignSB.append("rzx1218rzx");
        String toSign = DigestUtils.md5DigestAsHex(toSignSB.toString().getBytes());

        if (!sign.equalsIgnoreCase(toSign)) {
            return new GenericResponse<>(ServiceCodes.BAD_SIGN);
        }
        return newMiniAppSystemService.myPlaceProfiles(openId,placeName);
    }
}
