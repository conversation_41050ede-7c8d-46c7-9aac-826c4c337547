package com.rzx.dim4.user.web.controller.mp;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.billing.BillingPayApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.user.entity.Dim4User;
import com.rzx.dim4.user.entity.WeChatUser;
import com.rzx.dim4.user.service.Dim4UserService;
import com.rzx.dim4.user.service.WeChatTemplateMessageService;
import com.rzx.dim4.user.service.WeChatUserService;
import com.rzx.dim4.user.service.customer.BillingCardService;
import com.rzx.dim4.user.web.cons.WebConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Controller
@RequestMapping("/user/weChat/payOrder/")
public class WeChatPayOrderController {

	@Autowired
	private BillingServerService billingServerService;

	@Autowired
	private PlaceServerService placeServerService;

	@Autowired
	private WeChatUserService weChatUserService;

	@Autowired
	WeChatTemplateMessageService weChatTemplateMessageService;

	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Autowired
	private Dim4UserService dim4UserService;

	@Autowired private BillingPayApi billingPayApi;

	@Autowired private BillingCardService billingCardService;

	/**
	 * 创建支付订单,微信公众号支付
	 *
	 * @param model   模版对象
	 * @param session session对象
	 * @param amount  充值金额
	 * @param createCard  0 充值，1 带钱开卡
	 * @return 跳转至充值链接或错误提示模版
	 */
	@RequestMapping("/create")
	public String createPayOrder(Model model,
								 HttpSession session,
								 @RequestParam int amount,
								 @RequestParam int createCard) {

		WeChatUser weChatUser = (WeChatUser) session.getAttribute(WebConstants.WECHAT_USER_SESSION_KEY);
		PlaceProfileBO placeProfile = (PlaceProfileBO) session.getAttribute(WebConstants.PLACE_PROFILE_SESSION_KEY);

		if (weChatUser == null || placeProfile == null) {
			model.addAttribute("tips", "请关闭页面后重新打卡");
			return "page/mp/common/tips";
		}

		if (placeProfile.getIsRegistered() == 0) {
			model.addAttribute("tips", "该场所未注册商户");
			return "page/mp/common/tips";
		}

		Optional<Dim4User> optDim4User = dim4UserService.findByIdNumber(weChatUser.getIdNumber());
		if (!optDim4User.isPresent()) {
			return "page/mp/common/404";
		}
		Dim4User dim4User = optDim4User.get();
		String name = dim4User.getName();

		int minCreateCardAmount = billingCardService.getDefaultCardTypeMinCreateCardAmount(placeProfile.getPlaceId(),"1001");
		if (1 == createCard && amount < minCreateCardAmount) {
			model.addAttribute("tips", "开卡金额不能小于" + minCreateCardAmount / 100 + "元");
			return "page/mp/common/tips";
		}

		String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
		stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

		GenericResponse<SimpleDTO> response =
				billingPayApi.mpCreateOrder(requestTicket, placeProfile.getPlaceId(), weChatUser.getIdNumber(), amount, null, name);

//		GenericResponse<SimpleDTO> response = billingServerService.weChatTopupOrderCreate(requestTicket,
//				placeProfile.getPlaceId(), weChatUser.getIdNumber(), amount);

		if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			model.addAttribute("tips", response.getMessage());
			return "page/mp/common/tips";
		} else {
			return "redirect:" + response.getData().getResult();
		}
	}

	@RequestMapping("/query")
	public String queryPayOrder(Model model, @RequestParam String orderId) {
		log.info("查询订单信息,orderId:{}", orderId);

		if (StringUtils.isEmpty(orderId)) {
			return "page/mp/common/404";
		}

		String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
		stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
		GenericResponse<ObjDTO<PaymentOrderBO>> response = billingServerService.queryWechatTopupOrder(requestTicket,
				orderId);
		if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			model.addAttribute("tips", "获取订单信息失败");
			return "page/mp/common/tips";
		}
		PaymentOrderBO paymentOrderBO = response.getData().getObj();
		model.addAttribute("paymentOrder", paymentOrderBO);
		return "page/mp/query";
	}

	@ResponseBody
	@GetMapping("/sendTopupSuccess")
	public void weChatSendTopupSuccess(@RequestParam("paramMapJson") String paramMapJson) {
		Gson gson = new Gson();
		HashMap<?, ?> paramMap = gson.fromJson(paramMapJson, HashMap.class);
		Object object = paramMap.get("logTopupBO");
		LogTopupBO logTopupBO = gson.fromJson((String) object, LogTopupBO.class);

		int presentAmount = Integer.parseInt((String) paramMap.get("presentAmount"));

		GenericResponse<ObjDTO<PlaceProfileBO>> respPlaceProfile = placeServerService
				.findByPlaceId(logTopupBO.getPlaceId());
		String placeName = "获取场所名称失败";
		if (respPlaceProfile.getCode() == ServiceCodes.NO_ERROR.getCode()) {
			placeName = respPlaceProfile.getData().getObj().getDisplayName();
		}
		Optional<WeChatUser> optWeChatUser = weChatUserService.findByIdNumber(logTopupBO.getIdNumber());
		if (optWeChatUser.isPresent()) {
			WeChatUser weChatUser = optWeChatUser.get();
			weChatTemplateMessageService.sendTopupSuccess(weChatUser.getOpenid(), placeName, logTopupBO.getCreated(),
					logTopupBO.getIdNumber(), logTopupBO.getCashAmount(), presentAmount);
		}

	}
}
