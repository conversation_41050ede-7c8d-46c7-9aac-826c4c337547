package com.rzx.dim4.user.service.newMiniApp.Impl;

import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.iot.LogFaceAuthBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.user.service.newMiniApp.NewMiniAppMemberService;
import com.rzx.dim4.user.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年10月07日 18:20
 */
@Slf4j
@Service
public class NewMiniAppMemberServiceImpl implements NewMiniAppMemberService {

    @Autowired
    private BillingServerService billingServerService;

    @Autowired
    private PlaceServerService placeServerService;

    @Override
    public GenericResponse<ListDTO<DashboardVO>> queryOnlineMembers(List<String> placeIds) {
        List<DashboardVO> datas = new ArrayList<>();
        for (String placeId : placeIds) {
            DashboardVO vo = new DashboardVO();
            vo.setPlaceId(placeId);
            List<BillingOnlineBO> unfinishedByPlaceId = billingServerService.findUnfinishedByPlaceId(placeId);
            vo.setCountBillingOnline(unfinishedByPlaceId.size());

            List<BillingOnlineBO> collect = unfinishedByPlaceId.stream().filter(it -> (!it.getCardTypeId().equals("1000"))).collect(Collectors.toList());
            List<BillingOnlineBO> lsVOs = unfinishedByPlaceId.stream().filter(it -> it.getCardTypeId().equals("1000")).collect(Collectors.toList());
            vo.setCountCreateMemberCard(collect.size());
            vo.setCountCreateTemporaryCard(lsVOs.size());
            datas.add(vo);
        }
        // 获取场所名称
        GenericResponse<ListDTO<PlaceProfileBO>> listDTOGenericResponse = placeServerService.findByPlaceIds(placeIds);
        if (listDTOGenericResponse.isResult()) {
            List<PlaceProfileBO> placeProfileBOS = listDTOGenericResponse.getData().getList();
            placeProfileBOS.forEach(e->{
                datas.forEach(m->{
                    if (e.getPlaceId().equals(m.getPlaceId())) {
                        m.setPlaceName(e.getDisplayName());
                    }
                });
            });
        }
        List<DashboardVO> collect = datas.stream().sorted(Comparator.comparing(DashboardVO::getCountBillingOnline).reversed()).collect(Collectors.toList());
        return new GenericResponse<>(new ListDTO<>(collect));
    }

    @Override
    public GenericResponse<ObjDTO<MiniResultObjVO<CheckoutCardVO>>> initOnlineMemberTable(String placeId) {


        MiniResultObjVO<CheckoutCardVO> objVO = new MiniResultObjVO();
        List<CheckoutCardVO> vos = new ArrayList<>();
        List<String> idNumbers = new ArrayList<>();
        List<BillingOnlineBO> billingOnlineBOS = billingServerService.findUnfinishedByPlaceId(placeId);
//        GenericResponse<ListDTO<BillingCardBO>> billingCardBOGene = billingServerService.queryTemporaryCard(placeId);

        // 在线用户
        billingOnlineBOS.forEach(e->{
            CheckoutCardVO checkoutCardVO = new CheckoutCardVO();
            BeanUtils.copyProperties(e, checkoutCardVO);
            checkoutCardVO.setStatus(1);

            // 计算上网时长
            Duration duration = Duration.between(checkoutCardVO.getBillingTime(), LocalDateTime.now());
            checkoutCardVO.setOnlineTime(duration.toMinutes());
            vos.add(checkoutCardVO);
            idNumbers.add(e.getIdNumber());
        });

        // 临时卡未上机用户
//        if (billingCardBOGene.getCode() == ServiceCodes.NO_ERROR.getCode()) {
//            List<BillingCardBO> billingCardBOS = billingCardBOGene.getData().getList();
//            billingCardBOS.forEach(e->{
//                if (!idNumbers.contains(e.getIdNumber())) {
//                    CheckoutCardVO checkoutCardVO = new CheckoutCardVO();
//                    BeanUtils.copyProperties(e, checkoutCardVO);
//                    if ((!StringUtils.isEmpty(e.getActiveTime())
//                            && Duration.between(e.getActiveTime(), LocalDateTime.now()).toMinutes() <= 30)) {
//                        checkoutCardVO.setStatus(2); // 激活中
//                    } else {
//                        checkoutCardVO.setStatus(3); // 激活中
//                    }
//                    vos.add(checkoutCardVO);
//                }
//            });
//        }

        // 获取机器名称
        List<String> clientIds = vos.stream().map(CheckoutCardVO::getClientId).collect(Collectors.toList());
        GenericResponse<ListDTO<PlaceClientBO>> listDTOGenericRes = placeServerService.queryAllClientByClientIds(placeId,clientIds);
        if (listDTOGenericRes.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            List<PlaceClientBO> placeClientBOS = listDTOGenericRes.getData().getList();
            placeClientBOS.forEach(e->{
                vos.forEach(o->{
                    if (e.getClientId().equals(o.getClientId())) {
                        o.setClientName(e.getHostName());
                    }
                });
            });
        }

        // 获取卡类型名称
        GenericResponse<ListDTO<BillingCardTypeBO>> genericResponse = billingServerService.findCardTypeByPlaceId(placeId);
        if (genericResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            List<BillingCardTypeBO> billingCardTypeBOS = genericResponse.getData().getList();
            billingCardTypeBOS.forEach(e->{
                vos.forEach(o->{
                    if (e.getCardTypeId().equals(o.getCardTypeId())) {
                        o.setCardTypeName(e.getTypeName());
                    }
                });
            });
        }

        List<CheckoutCardVO> collect = vos.stream().filter(it -> ( !it.getCardTypeId().equals("1000"))).collect(Collectors.toList());
        List<CheckoutCardVO> lsVOs = vos.stream().filter(it -> it.getCardTypeId().equals("1000")).collect(Collectors.toList());
        objVO.setDatas(vos);
        objVO.setTotalOnline(vos.size());
        objVO.setCountCreateMemberCard(collect.size());
        objVO.setCountCreateTemporaryCard(lsVOs.size());

        return new GenericResponse<>(new ObjDTO<>(objVO));
    }

    @Override
    public GenericResponse<?> batchLogout(String placeId, String clientIds, String cardIds, String loginName, String loginPass, String sourceType) {
        return billingServerService.batchLogout(placeId,clientIds,cardIds,loginName,loginPass, SourceType.MINIAPP.name());
    }

    @Override
    public GenericResponse<PagerDTO<LogLoginBO>> queryPageLogout(String placeId, String startLogoutTime, String endLogoutTime, int logoutType, String loginIds,int size, int page, String[] orderColumns, String order) {
        GenericResponse<PagerDTO<LogLoginBO>> pagerDTOGenericResponse = billingServerService.queryPageLogout(placeId, startLogoutTime, endLogoutTime, 1,loginIds, size, page, orderColumns, order);
        if(pagerDTOGenericResponse.isResult() && pagerDTOGenericResponse.getData() != null){
            PagerDTO<LogLoginBO> data = pagerDTOGenericResponse.getData();
            List<LogLoginBO> list = data.getList();
            list.stream().forEach(it-> it.setLogoutOperationCreaterName("四维网管小程序"));

            return new GenericResponse<>(new PagerDTO<>((int) data.getTotal(), list));
        }
        return new GenericResponse<>(new PagerDTO<>(0,new ArrayList<>()));
    }

    @Override
    public GenericResponse<ListDTO<DashboardVO>> incomeTodayByMember(List<String> placeIds, String startTime, String endTime) {
        List<DashboardVO> bos = new ArrayList<>();
        for (String placeId : placeIds) {
            DashboardVO bo = new DashboardVO();
//            GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> objDTOGenericResponse = billingServerService.sumStatisticsOperationByDay(Arrays.asList(placeId), startTime, endTime);
//            if(objDTOGenericResponse.isResult()){
//                bo.setSumMemberTopupIncome(objDTOGenericResponse.getData().getObj().getSumMemberTopupIncome());
//            }
            List<Map<String, String>> consumerRanking = billingServerService.findConsumerRanking(placeId, startTime, endTime, "1");
            int count = (int)consumerRanking.stream().mapToInt(it-> Integer.valueOf(it.getOrDefault("aggregate","0"))).sum();
            bo.setSumMemberTopupIncome(count);
            bo.setPlaceId(placeId);
            bos.add(bo);
        }
        // 获取场所名称
        GenericResponse<ListDTO<PlaceProfileBO>> listDTOGenericResponse = placeServerService.findByPlaceIds(placeIds);
        if (listDTOGenericResponse.isResult()) {
            List<PlaceProfileBO> placeProfileBOS = listDTOGenericResponse.getData().getList();
            placeProfileBOS.forEach(e->{
                bos.forEach(m->{
                    if (e.getPlaceId().equals(m.getPlaceId())) {
                        m.setPlaceName(e.getDisplayName());
                    }
                });
            });
        }
        List<DashboardVO> collect = bos.stream().sorted(Comparator.comparing(DashboardVO::getSumMemberTopupIncome).reversed()).collect(Collectors.toList());
        return new GenericResponse<>(new ListDTO<>(collect));
    }

    @Override
    public GenericResponse<ListDTO<MyConsumptionDetailsVO>> queryMemberConsumerRanking(String placeId, String startTime, String endTime, String type) {
        List<MyConsumptionDetailsVO> vos = new ArrayList<>();
        List<Map<String, String>> consumerRanking = billingServerService.findConsumerRanking(placeId, startTime, endTime, type);


        if(consumerRanking.size() > 0){
            for (Map<String, String> map : consumerRanking) {
                MyConsumptionDetailsVO vo = new MyConsumptionDetailsVO();

                vo.setIdName(map.getOrDefault("idName","用户"));
                vo.setIdNumber(map.getOrDefault("idNumber","-"));
                vo.setCardTypeName(map.getOrDefault("cardTypeName","普通会员"));
                vo.setAggregate(map.getOrDefault("aggregate","0"));
                vo.setTopupCount(map.getOrDefault("topupCount","0"));
                vo.setSumCost(map.getOrDefault("sumCost","0"));
                vo.setSumPresent(map.getOrDefault("sumPresent","0"));
                vo.setCardId(map.getOrDefault("cardId",""));
                vo.setSumConsumptionTotal(map.getOrDefault("sumConsumptionTotal","0"));
                vo.setSumOnlineTime(map.getOrDefault("sumOnlineTime","0"));
                vos.add(vo);
            }
        }
        return new GenericResponse<>(new ListDTO<>(vos));
    }

    @Override
    public GenericResponse<ListDTO<DashboardVO>> queryTotalConsumptionByPlaceIds(List<String> placeIds, String startTime, String endTime) {
        List<DashboardVO> datas = new ArrayList<>();
        List<Map<String, String>> maps = billingServerService.queryTotalConsumptionByPlaceIds(startTime, endTime, placeIds);
        if(!CollectionUtils.isEmpty(maps)){
            for (Map<String, String> map : maps) {
                DashboardVO vo = new DashboardVO();
                vo.setSumConsumptionTotal(Math.abs(Integer.valueOf(map.getOrDefault("totalConsumption","0"))));
                vo.setPlaceId(map.get("placeId"));
                datas.add(vo);
            }
            // 获取场所名称
            GenericResponse<ListDTO<PlaceProfileBO>> listDTOGenericResponse = placeServerService.findByPlaceIds(placeIds);
            if (listDTOGenericResponse.isResult()) {
                List<PlaceProfileBO> placeProfileBOS = listDTOGenericResponse.getData().getList();
                placeProfileBOS.forEach(e->{
                    datas.forEach(m->{
                        if (e.getPlaceId().equals(m.getPlaceId())) {
                            m.setPlaceName(e.getDisplayName());
                        }
                    });
                });
            }
        }
        List<DashboardVO> collect = datas.stream().sorted(Comparator.comparing(DashboardVO::getSumConsumptionTotal).reversed()).collect(Collectors.toList());
        return new GenericResponse<>(new ListDTO<>(collect));
    }

    @Override
    public GenericResponse<ListDTO<MiniAppStatisticsVO>> queryOnlineStatistics(String placeId, String startTime, String endTime) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<MiniAppStatisticsVO> exportOnlineVOS = new ArrayList<>();
        List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = new ArrayList<>();
        List<StatisticsByDayBO> statisticsByDayBOS = new ArrayList<>();

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotal(30,
                0, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);
        if (resp.isResult()) {
            statisticsOperationByDayBOS = resp.getData().getList();
        }

        GenericResponse<PagerDTO<StatisticsByDayBO>> rep = billingServerService.queryDashboardStatistics(30, 0, startTime, endTime, placeId);
        if (rep.isResult()) {
            statisticsByDayBOS = rep.getData().getList();
        }

        for (StatisticsOperationByDayBO bo : statisticsOperationByDayBOS) {
            MiniAppStatisticsVO exportOnlineVO = new MiniAppStatisticsVO();
            for (StatisticsByDayBO statisticsByDayBO : statisticsByDayBOS) {
                if (bo.getCountDay().equals(statisticsByDayBO.getCountDay())) {
                    exportOnlineVO.setCountDay(bo.getCountDay());
                    exportOnlineVO.setCountPackage(bo.getCountPackage());
                    exportOnlineVO.setCreateMemberCardNum(bo.getCreateMemberCardNum());
                    exportOnlineVO.setCreateTemporaryCardNum(bo.getCreateTemporaryCardNum());
                    exportOnlineVO.setSumConsumptionTotal(statisticsByDayBO.getSumConsumptionTotal());
                    exportOnlineVO.setSumOnlineNum(statisticsByDayBO.getSumOnlineNum());
                    exportOnlineVO.setSumOnlineVisits(statisticsByDayBO.getSumOnlineVisits());
                    exportOnlineVO.setSumOnlineTime(DateTimeUtils.minutesTransformToStringDes(statisticsByDayBO.getSumOnlineTime()));
                    exportOnlineVO.setSumPackageTotal(bo.getSumPackageTotal());
                    exportOnlineVO.setSumOnlineTimeNum(statisticsByDayBO.getSumOnlineTime());
                    exportOnlineVOS.add(exportOnlineVO);
                    break;
                }
            }
        }
        //添加一个合计
        if(exportOnlineVOS.size() > 0){
            MiniAppStatisticsVO exportOnlineVO = new MiniAppStatisticsVO();
            exportOnlineVO.setCountDay("合计");
            exportOnlineVO.setCountPackage(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCountPackage).sum());
            exportOnlineVO.setCreateMemberCardNum(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCreateMemberCardNum).sum());
            exportOnlineVO.setCreateTemporaryCardNum(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCreateTemporaryCardNum).sum());
            exportOnlineVO.setSumConsumptionTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumConsumptionTotal).sum());
            exportOnlineVO.setSumOnlineNum(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumOnlineNum).sum());
            exportOnlineVO.setSumOnlineVisits(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumOnlineVisits).sum());
            exportOnlineVO.setSumOnlineTimeNum(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumOnlineTimeNum).sum());
            exportOnlineVO.setSumOnlineTime(DateTimeUtils.minutesTransformToStringDes(exportOnlineVO.getSumOnlineTimeNum()));
            exportOnlineVO.setSumPackageTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumPackageTotal).sum());
            exportOnlineVOS.add(exportOnlineVO);
        }
        return new GenericResponse<>(new ListDTO<>(exportOnlineVOS));
    }

    @Override
    public GenericResponse<ListDTO<MiniAppStatisticsVO>> queryTopupPresentTable(String placeId, String startTime, String endTime) {

        List<MiniAppStatisticsVO> exportOnlineVOS = new ArrayList<>();

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotal(30,
                0, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);

        if (resp.isResult()) {
            // 处理数据
            List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();

            for (StatisticsOperationByDayBO bo : statisticsOperationByDayBOS) {
                MiniAppStatisticsVO vo = new MiniAppStatisticsVO();
                // 处理数据
                int sumCashIncomeTopupTotal = bo.getSumMemberTopupIncome() + bo.getSumTemporaryTopupIncome(); // 充值本金 = 会员充值 + 临时卡充值的 cost
                vo.setCountDay(bo.getCountDay());
                vo.setCountTopup(bo.getCountTopup()); // 充值次数
                vo.setSumPresentTupupTotal((bo.getSumPresentTupupTotal() + bo.getSumPresentIncome())); // 充值奖励 = 赠送充值 + 赠送 + 老带新
                vo.setSumCashTopupIncome(sumCashIncomeTopupTotal); // 充值本金
                vo.setSumTopupTotal(vo.getSumCashTopupIncome() + vo.getSumPresentTupupTotal()); // 合计充值
                vo.setCountReversal(bo.getCountReversal()); // 冲正次数
                vo.setSumTotalReversal(bo.getSumTotalReversal() ); // 冲正本金
                vo.setSumPresentReversalTotal(bo.getSumPresentReversalTotal() ); // 冲正奖励
                vo.setSumReversalTotal(vo.getSumPresentReversalTotal() + vo.getSumTotalReversal()); // 合计冲正
                vo.setCountRefund(bo.getCountRefund()); // 退款次数
                vo.setSumCashOutIncome(bo.getSumCashOutIncome()); // 现金退款
                vo.setSumOnlineOutIncome(bo.getSumOnlineOutIncome()); // 线上退款
                vo.setSumRefundTotal(vo.getSumCashOutIncome() + vo.getSumOnlineOutIncome()); // 合计退款
                exportOnlineVOS.add(vo);
            }
        }

        //添加一个合计
        if(exportOnlineVOS.size() > 0){
            MiniAppStatisticsVO vo = new MiniAppStatisticsVO();
            vo.setCountDay("合计");
            vo.setCountTopup(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCountTopup).sum()); // 充值次数
            vo.setSumPresentTupupTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumPresentTupupTotal).sum()); // 充值奖励 = 赠送充值 + 赠送 + 老带新
            vo.setSumCashTopupIncome(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumCashTopupIncome).sum()); // 充值本金
            vo.setSumTopupTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumTopupTotal).sum()); // 合计充值
            vo.setCountReversal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCountReversal).sum()); // 冲正次数
            vo.setSumTotalReversal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumTotalReversal).sum()); // 冲正本金
            vo.setSumPresentReversalTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumPresentReversalTotal).sum()); // 冲正奖励
            vo.setSumReversalTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumReversalTotal).sum()); // 合计冲正
            vo.setCountRefund(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCountRefund).sum()); // 退款次数
            vo.setSumCashOutIncome(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumCashOutIncome).sum()); // 现金退款
            vo.setSumRefundTotal(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumRefundTotal).sum()); // 合计退款
            vo.setSumOnlineOutIncome(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getSumOnlineOutIncome).sum()); // 线上退款

            vo.setCountPackage(exportOnlineVOS.stream().mapToInt(MiniAppStatisticsVO::getCountPackage).sum());
            exportOnlineVOS.add(vo);
        }

        return new GenericResponse<>(new ListDTO<>(exportOnlineVOS));
    }


}
