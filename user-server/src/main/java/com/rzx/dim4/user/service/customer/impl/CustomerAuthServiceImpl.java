package com.rzx.dim4.user.service.customer.impl;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.PlaceBizConfigBO;
import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.billing.BillingCardApi;
import com.rzx.dim4.base.service.feign.iot.IotAuthConfigApi;
import com.rzx.dim4.base.service.feign.place.PlaceProfileApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.user.entity.Dim4User;
import com.rzx.dim4.user.entity.WeChatUser;
import com.rzx.dim4.user.service.Dim4UserService;
import com.rzx.dim4.user.service.WeChatUserService;
import com.rzx.dim4.user.service.customer.CustomerAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/25
 **/
@Slf4j
@Service
public class CustomerAuthServiceImpl implements CustomerAuthService {

    @Autowired private Dim4UserService dim4UserService;

    @Autowired private PlaceProfileApi placeProfileApi;

    @Autowired private IotAuthConfigApi iotAuthConfigApi;

    @Autowired private StringRedisTemplate stringRedisTemplate;

    @Autowired private WeChatUserService weChatUserService;

    @Autowired private BillingServerService billingServerService;

    @Autowired private BillingCardApi billingCardApi;

    @Override
    public GenericResponse<ObjDTO<LogAuthFeeBO>> success(String placeId, String clientId, String idNumber,String serialno) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)) {
            log.warn("入参错误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        GenericResponse<ObjDTO<PlaceProfileBO>> response = placeProfileApi.findPlaceByPlaceId(placeId);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.warn("调用  place-server  查询场所信息失败");
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
        PlaceProfileBO placeProfileBO = response.getData().getObj();

        Optional<Dim4User> optionalDim4User1 = dim4UserService.findByIdNumber(idNumber);
        Dim4User dim4User = optionalDim4User1.orElseThrow(() -> {
            log.warn("没有查询到用户之前登录时留下的身份信息, idNumber={}", idNumber);
            return new ServiceException(ServiceCodes.ADMIN_BAD_ACCOUNT);
        });
        Dim4UserBO dim4UserBO = dim4User.toBO();

        IotAuthConfigApi.SaveLogAfterSuccessBO saveLogAfterSuccessBO = new IotAuthConfigApi.SaveLogAfterSuccessBO();
        saveLogAfterSuccessBO.setPlaceProfileBO(placeProfileBO);
        saveLogAfterSuccessBO.setDim4UserBO(dim4UserBO);
        saveLogAfterSuccessBO.setSerialno(serialno);

        GenericResponse<ObjDTO<LogAuthFeeBO>> response1 = iotAuthConfigApi.saveLogAfterSuccess(saveLogAfterSuccessBO);
        if(ServiceCodes.NO_SERVICE.getCode() ==  response1.getCode()){
            log.warn("iot服务调用失败，取消写入人脸日志，参数：placeId:{},证件号:{}",placeId,idNumber);
            return new GenericResponse(ServiceCodes.NO_ERROR);
        }
        if (ServiceCodes.NO_ERROR.getCode() != response1.getCode()) {
            log.warn("向  iot-server  保存实名认证日志失败, code={}, msg={}", response1.getCode(), response1.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response1.getCode()));
        }

        return response1;
    }

    @Override
    public GenericResponse<?> activeCard (String placeId, String idNumber, int activeType) {
        // 查询卡信息
        GenericResponse<ObjDTO<BillingCardBO>> cardGeneric = billingServerService.findBillingCardAllType(placeId, idNumber);
        if (!cardGeneric.isResult()) {
            log.warn("小程序查询卡信息, placeId={}, idNumber={}, msg={}", placeId, idNumber, cardGeneric.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(cardGeneric.getCode()));
        }
        String name = null;
        Optional<WeChatUser> byIdNumber = weChatUserService.findByIdNumber(idNumber);
        if(byIdNumber.isPresent() && !StringUtils.isEmpty(byIdNumber.get().getNickname())){
            name = byIdNumber.get().getNickname();
        }
        BillingCardBO billingCardBO = cardGeneric.getData().getObj();
        // 激活有效时间内，不需要重新激活
        if (StringUtils.isEmpty(billingCardBO.getActiveTime())
                || Duration.between(billingCardBO.getActiveTime(), LocalDateTime.now()).toMinutes() > 30) {

            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

            GenericResponse<?> response1 = billingCardApi.newActivation(requestTicket, placeId, idNumber,name, activeType, "");
            if (ServiceCodes.NO_ERROR.getCode() != response1.getCode()) {
                log.warn("小程序激活计费卡失败，placeId:{},idNumber:{},msg:{}", placeId, idNumber, response1.getMessage());
                throw new ServiceException(ServiceCodes.getByCode(response1.getCode()));
            } else {
                log.info("小程序激活计费卡成功，placeId:{},idNumber:{},activeTime:{}", placeId, idNumber, billingCardBO.getActiveTime());
            }
        } else {
            log.info("小程序激活计费卡成功，placeId:{},idNumber:{},activeTime:{}", placeId, idNumber, billingCardBO.getActiveTime());
        }
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    @Override
    public List<IotAuthConfigBO> queryAuthChargeList(String placeId) {

        // 查询场所扫码终端配置
        String faceClientType = "";
        String placeType = "";
        GenericResponse<ObjDTO<PlaceBizConfigBO>> placeBizRes = billingServerService.queryPlaceBizConfig(placeId);
        if (placeBizRes.isResult()) {
            PlaceBizConfigBO placeBizConfigBO = placeBizRes.getData().getObj();
            placeType = String.valueOf(placeBizConfigBO.getType());
            String clientQrCodeAuth = placeBizConfigBO.getClientQrCodeAuth();
            if (!StringUtils.isEmpty(clientQrCodeAuth) && clientQrCodeAuth.contains("V8MPWECHAT")) {
                faceClientType = "5";
            } else if (!StringUtils.isEmpty(clientQrCodeAuth) && clientQrCodeAuth.contains("ZFDJMP")) {
                faceClientType = "5";
            }else if (!StringUtils.isEmpty(clientQrCodeAuth) && clientQrCodeAuth.contains("MPWECHAT")) {
                faceClientType = "1";
            } else if ("MINIAPP".equals(clientQrCodeAuth)) {
                faceClientType = "2";
            } else if ("ALIRZXAPP".equals(clientQrCodeAuth)) {
                faceClientType = "4";
            }

            String cashierQrCodeAuth = placeBizConfigBO.getCashierQrCodeAuth();
            if (!StringUtils.isEmpty(cashierQrCodeAuth) && cashierQrCodeAuth.contains("MPWECHAT")) {
                faceClientType = "1";
            } else if (!StringUtils.isEmpty(cashierQrCodeAuth) && cashierQrCodeAuth.contains("V8MPWECHAT")) {
                faceClientType = "5";
            } else if ("MINIAPP".equals(cashierQrCodeAuth)) {
                faceClientType = "2";
            } else if ("ALIRZXAPP".equals(cashierQrCodeAuth)) {
                faceClientType = "4";
            }


        }
       try {
           GenericResponse<ListDTO<IotAuthConfigBO>> response = iotAuthConfigApi.queryAuthConfig(placeId, faceClientType, placeType);
           if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
               log.warn("获取  iot-server  实名认证套餐列表失败");
               throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
           }
           List<IotAuthConfigBO> iotAuthConfigBOS = response.getData().getList();
           if (!CollectionUtils.isEmpty(iotAuthConfigBOS)) {
               iotAuthConfigBOS.forEach(o -> {
                   o.setPlaceAuthFeeTypeDesc(o.getPlaceAuthFeeType().getName());
               });
           }

           return iotAuthConfigBOS;
       }catch (Exception e){
           log.warn("获取  iot-server  实名认证套餐列表失败");
           return new ArrayList<>();
       }
    }

    @Override
    public PaymentResultBO createAuthPayOrder(String placeId, String idNumber, String openId, long authChargeRuleId, int amount, String appId,String faceId) {
        String key = "createAuthPayOrder:" + placeId + idNumber + openId + authChargeRuleId + amount + appId;
        if (stringRedisTemplate.hasKey(key)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
        // 2秒内不允许重复提交
        stringRedisTemplate.opsForValue().set(key,key,2, TimeUnit.SECONDS);
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)
        || StringUtils.isEmpty(openId) || amount < 0) {
            log.warn("入参有误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<WeChatUser> optionalWeChatUser = weChatUserService.findByIdNumber(idNumber);
        WeChatUser weChatUser = optionalWeChatUser.orElseThrow(() -> {
            log.warn("没有查询到对应的微信用户");
            return new ServiceException(ServiceCodes.USER_INFO_WRONG);
        });
        boolean equals = openId.equals(weChatUser.getMiniOpenId());
        if (!equals) {
            log.warn("没有查询到对应的微信用户");
            throw new ServiceException(ServiceCodes.USER_INFO_WRONG);
        }

        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<ObjDTO<PaymentResultBO>> response =
                iotAuthConfigApi.createAuthPayOrder(requestTicket, placeId, idNumber, openId, authChargeRuleId, amount,appId,faceId);

        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.warn("调用 iot-server  失败，code={}, msg={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }

        return response.getData().getObj();
    }

    @Override
    public LogAuthFeeBO queryAuthPayOrder(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            log.warn("入参有误，orderId={}", orderId);
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<ObjDTO<LogAuthFeeBO>> response = iotAuthConfigApi.queryAuthPayOrder(requestTicket, orderId);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.warn("调用 iot-server  失败，code={}, msg={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }

        return response.getData().getObj();
    }
}
