<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<!-- begin::Head -->
<head>
<meta charset="utf-8" />
<title>四维管家 | 管理控制台</title>
<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
<link href="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
	<!-- begin:: Page -->

	<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->

	<div class="kt-grid kt-grid--hor kt-grid--root">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
			<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

				<!-- begin:: Header --> 
				<#include "/base/header.html"> 
				<!-- end:: Header -->

				<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
					<div class="kt-container  kt-container--fluid ">

						<!-- begin:: Aside --> 
						<#include "/base/aside.html"> 
						<!-- end:: Aside -->

						<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

							<!-- begin:: Subheader -->
							<div class="kt-subheader   kt-grid__item" id="kt_subheader">
								<div class="kt-container  kt-container--fluid ">
									<div class="kt-subheader__main">
										<h3 class="kt-subheader__title">认证管理</h3>
										<span class="kt-subheader__separator kt-subheader__separator--v"></span>
										<div class="kt-subheader__group" id="kt_subheader_search">
											<span class="kt-subheader__desc" id="kt_subheader_total">批量停用</span>
										</div>
									</div>
									<div class="kt-subheader__toolbar">
										<a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
									</div>
								</div>
							</div>
							<!-- end:: Subheader -->

							<!-- begin:: Content -->
							<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
								<div class="row">
									<div class="col-lg-12">
										<!--begin::Portlet-->
										<div class="kt-portlet">
											<!--begin::Form-->
											<form class="kt-form kt-form--label-right">
												<input type="hidden" id="id" name="id" value="<#if (iotAuthConfig.id)??>${iotAuthConfig.id?c}<#else>0</#if>" >
												<div class="kt-portlet__body">
													<div class="kt-section kt-section--first">
														<h3 class="kt-section__title">基本信息:</h3>
														<div class="kt-section__body">
															<div class="form-group row">
																<label class="col-lg-3 col-form-label">网吧编号:</label>
																<div class="col-lg-5 col-form-label">
																	<textarea class="form-control" name="placeIds" id="placeIds" rows="4"></textarea>
																	<span class="form-text text-muted">参输入格式:<br/> placeId1<br/>placeId2<br/>placeId3<br/>（没有空格，英文符号,以回车分割）</span>
																</div>
															</div>
															<div class="form-group row">
																<label class="col-lg-3 col-form-label">套餐类型:</label>
																<div class="col-lg-3">
																	<div class="kt-checkbox-inline">
																		<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																			<input type="checkbox" id="allChange">全选
																			<span></span>
																		</label>
																	</div>
																</div>
															</div>
															<div class="form-group row">
																<div class="col-lg-3"></div>
																<div class="kt-checkbox-inline" style="margin-left: 10px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="0" name="placeAuthFeeType">次卡
																		<span></span>
																	</label>
																</div>
																<div class="kt-checkbox-inline" style="margin-left: 30px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="1" name="placeAuthFeeType">日卡
																		<span></span>
																	</label>
																</div>
																<div class="kt-checkbox-inline" style="margin-left: 30px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="7" name="placeAuthFeeType">周卡
																		<span></span>
																	</label>
																</div>
																<div class="kt-checkbox-inline" style="margin-left: 30px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="30" name="placeAuthFeeType">月卡
																		<span></span>
																	</label>
																</div>
																<div class="kt-checkbox-inline" style="margin-left: 30px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="90" name="placeAuthFeeType">季卡
																		<span></span>
																	</label>
																</div>
																<div class="kt-checkbox-inline" style="margin-left: 30px;">
																	<label class="kt-checkbox kt-checkbox--bold kt-checkbox--brand">
																		<input type="checkbox" value="365" name="placeAuthFeeType">年卡
																		<span></span>
																	</label>
																</div>
															</div>
														</div>
													</div>
												</div>
												<div class="kt-portlet__foot">
													<div class="kt-form__actions">
														<div class="row">
															<div class="col-lg-4"></div>
															<div class="col-lg-6">
																<button type="button" id="kt_submit" class="btn btn-success btn-wide">确定</button>
																&nbsp;&nbsp;&nbsp;&nbsp;
																<button type="reset" class="btn btn-secondary btn-wide">取消</button>
															</div>
														</div>
													</div>
												</div>
											</form>
											<!--end::Form-->
										</div>
										<!--end::Portlet-->
									</div>
								</div>
							</div>
							<!-- end:: Content -->
						</div>
					</div>
				</div>

				<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
			</div>
		</div>
	</div>
	<!-- end:: Page --> 
	
	<#include "/base/script.html"> 

	<script src="/assets/js/pages/iot/authconfig/stop.js" type="text/javascript"></script>
	<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
