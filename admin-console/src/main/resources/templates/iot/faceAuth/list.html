<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<!-- begin::Head -->
<head>
<meta charset="utf-8" />
<title>四维管家 | 管理控制台</title>
<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
<link href="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
	<!-- begin:: Page -->

	<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->
	<div class="kt-grid kt-grid--hor kt-grid--root">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
			<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

				<!-- begin:: Header --> 
				<#include "/base/header.html"> 
				<!-- end:: Header -->

				<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
					<div class="kt-container  kt-container--fluid ">

						<!-- begin:: Aside --> 
						<#include "/base/aside.html"> 
						<!-- end:: Aside -->

						<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

							<!-- begin:: Subheader -->
							<div class="kt-subheader   kt-grid__item" id="kt_subheader">
								<div class="kt-container  kt-container--fluid ">
									<div class="kt-subheader__main">
										<h3 class="kt-subheader__title">扫脸记录</h3>
									</div>
									<div class="kt-subheader__toolbar">
										<a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
									</div>
								</div>
							</div>
							<!-- end:: Subheader -->

							<!-- begin:: Content -->
							<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
								<div class="kt-portlet kt-portlet--mobile">

									<div class="row kt-margin-b-20" style="margin-top: 20px;">

										<div class="col-xl-2 col-lg-2 col-md-2 col-sm-2 form-group row" style="margin-bottom: 0px;margin-left: 25px;">
											<input type='text' class="form-control" id="idNumber" name="idNumber" placeholder="证件号码" style="width: auto" autocomplete="off" />
										</div>

										<div class="col-xl-2 col-lg-2 col-md-2 col-sm-2 form-group row" style="margin-bottom: 0px;margin-left: 25px;">
											<input type='text' class="form-control" id="idName" name="idName" placeholder="姓名" style="width: auto" autocomplete="off" />
										</div>

										<div class="col-xl-2 col-lg-2 col-md-2 col-sm-2 form-group row" style="margin-bottom: 0px;">
											<input type='text' class="form-control" id="placeId" name="placeId" placeholder="场所编码" style="width: auto" autocomplete="off" />
										</div>

										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
											<select class="form-control" id="provinceSelect" name="province">
												<option value="">请选择</option>
											</select>
										</div>
										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
											<select class="form-control" id="citySelect" name="city">
												<option value="">请选择</option>
											</select>
										</div>
										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
											<select class="form-control" id="countrySelect" name="country">
												<option value="">请选择</option>
											</select>
										</div>

										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1" style="margin-left: 25px;">
											<select class="form-control" id="type">
												<option value="">场所类型</option>
												<option value="0">网吧</option>
												<option value="1">酒店</option>
												<option value="2">网租</option>
												<option value="3">普通场所</option>
												<option value="4">营销大师</option>
												<option value="5">九威</option>
												<option value="6">大巴掌</option>
												<option value="7">PMS</option>
												<option value="8">龙管家</option>
												<option value="301">万象</option>
												<option value="302">嘟嘟牛</option>
												<option value="303">轻网联盟</option>
												<option value="304">佳星</option>
                                                <option value="305">百果树</option>
                                                <option value="306">奥比特</option>
                                                <option value="307">丕微</option>
											</select>
										</div>
									</div>

									<div class="row kt-margin-b-20" style="margin-top: 10px;margin-left: 25px;">
										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
											<select class="form-control" id="deviceSelect" name="deviceSelect">
												<option value="">来源</option>
												<option value="1">微信小程序</option>
												<option value="2">支付宝IOT</option>
												<option value="3">微信公众号</option>
												<option value="4">支付宝小程序</option>
											</select>
										</div>

										<div class="col-xl-2 col-lg-2 col-md-2 col-sm-2 form-group row" style="margin-bottom: 0px;margin-left: 25px;">
											<input type='text' class="form-control" id="orderId" name="orderId" placeholder="业务订单Id" style="width: auto" autocomplete="off" />
										</div>

										<div class="col-xl-3 col-lg-3 col-md-3 col-sm-3 form-group row" style="margin-bottom: 0px;">
											<label class="col-form-label" style="margin-left: 20px;margin-right: 10px;">人脸时间 :</label>
											<input type='text' class="form-control" id="kt_daterangepicker_1" placeholder="选择时间段" style="width: auto"/>
										</div>

										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1" style="margin-left: 12%">
											<button type="button" class="btn btn-success btn-wide" style="width: 110.5px;" id="search">查询</button>
										</div>

										<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1">
											<button type="button" class="btn btn-secondary btn-wide" style="width: 110.5px;" id="reset">重置</button>
										</div>

										<div class="ol-lg-1 col-md-1 col-sm-1" style="margin-bottom: 0px;margin-left: 0px;">
											<button id="export" class="btn btn-brand btn-elevate btn-icon-sm">
												<i class="la la-plus"></i> 导出
											</button>
										</div>
									</div>

									<div class="row kt-margin-b-20" style="margin-left: 25px;">
										<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-bottom: 0px;margin-left: 0px;">
											<span class="kt-widget26__desc" style="font-size: 14px;color: rgba(16, 16, 16, 100)">认证场所：</span>
											<span class="kt-widget26__desc" style="font-size: 14px;color: red" id="authProfileNum"></span>
										</div>
										<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-bottom: 0px;margin-left: 0px;">
											<span class="kt-widget26__desc" style="font-size: 14px;color: rgba(16, 16, 16, 100)">认证人数：</span>
											<span class="kt-widget26__desc" style="font-size: 14px;color: red" id="authPeopleNum"></span>
										</div>
										<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-bottom: 0px;margin-left: 0px;">
											<span class="kt-widget26__desc" style="font-size: 14px;color: rgba(16, 16, 16, 100)">认证次数：</span>
											<span class="kt-widget26__desc" style="font-size: 14px;color: red" id="authNum"></span>
										</div>
									</div>

									<div class="kt-portlet__body" style="padding-top: 0px;">
										<!--begin: Datatable -->
										<table class="table table-striped table-bordered table-hover table-checkable" id="kt_table_1">
											<thead>
												<tr>
													<th>人脸时间</th>
													<th>证件号码</th>
													<th>姓名</th>
													<th>场所编码</th>
													<th>场所名称</th>
													<th>场所地址</th>
													<th>场所类型</th>
													<th>来源</th>
													<th>付费详情</th>
												</tr>
											</thead>
										</table>
										<!--end: Datatable -->
									</div>
								</div>
							</div>
							<!-- end:: Content -->
						</div>
					</div>
				</div>
				<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
			</div>
		</div>
	</div>
	
	<div class="modal fade" id="viewAuthFeeDetailModal" tabindex="-1" role="dialog" aria-labelledby="viewAuthFeeDetailModalTitle" aria-hidden="true">
		<div class="modal-dialog modal-centered modal-lg" role="document">
			<div class="modal-content" style="width: 110%;">
				<div class="modal-header">
					<h5 class="modal-title" id="viewAuthFeeDetailModalTitle">付费详情</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
				  <form class="kt-form kt-form--fit kt-margin-b-20 form-inline" id="viewAuthFeeDetailForm">
				    <div class="form-group" style="margin-bottom: 10px;">
					  <label class="col-sm-1.5 control-label">网吧编号：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="placeId4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">&ensp;&ensp;&ensp;&ensp;姓名：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="idName4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">证件号码：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="idNumber4view" readonly="readonly"/>
					</div>
					
				    <div class="form-group" style="margin-bottom: 10px;">
					  <label class="col-sm-1.5 control-label">是否收费：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="isPay4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">订单金额：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="authFee4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">套餐类型：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="authFeeType4view" readonly="readonly"/>
					</div>
					
					<div class="form-group" style="margin-bottom: 10px;">
					  <label class="col-sm-1.5 control-label">有效时间：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="effectiveTime4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">支付状态：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="effectived4view" readonly="readonly"/>
					  
					  <label class="col-sm-1.5 control-label">支付时间：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="payTime4view" readonly="readonly"/>
					</div>
					
					<div class="form-group" style="margin-bottom: 10px;">
					  <label class="col-sm-1.5 control-label">&ensp;&ensp;订单号：</label>
					  <input type="text" class="col-sm-2.5 form-control" style="border: none;" id="orderId4view" readonly="readonly"/>
					</div>
				   </form>	  
				</div>
			</div>
		</div>
	</div>
	
	<!-- end:: Page --> 
	
	<#include "/base/script.html">
	
	<!--begin::Page Vendors(used by this page) -->
	<script src="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.js" type="text/javascript"></script>
	<script src="/assets/js/pages/iot/faceAuth/list.js" type="text/javascript"></script>
	<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
