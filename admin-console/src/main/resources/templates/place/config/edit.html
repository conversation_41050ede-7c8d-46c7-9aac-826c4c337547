<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<!-- begin::Head -->
<head>
<meta charset="utf-8" />
<title>四维管家 | 管理控制台</title>
<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
<link href="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
	<!-- begin:: Page -->

	<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->

	<div class="kt-grid kt-grid--hor kt-grid--root">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
			<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

				<!-- begin:: Header --> 
				<#include "/base/header.html"> 
				<!-- end:: Header -->

				<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
					<div class="kt-container  kt-container--fluid ">

						<!-- begin:: Aside --> 
						<#include "/base/aside.html"> 
						<!-- end:: Aside -->

						<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

							<!-- begin:: Subheader -->
							<div class="kt-subheader   kt-grid__item" id="kt_subheader">
								<div class="kt-container  kt-container--fluid ">
									<div class="kt-subheader__main">
										<h3 class="kt-subheader__title">场所设置</h3>
										<span class="kt-subheader__separator kt-subheader__separator--v"></span>
										<div class="kt-subheader__group" id="kt_subheader_search">
											<span class="kt-subheader__desc" id="kt_subheader_total">${title}</span>
										</div>
									</div>
									<div class="kt-subheader__toolbar">
										<a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
									</div>
								</div>
							</div>
							<!-- end:: Subheader -->

							<!-- begin:: Content -->
							<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
								<div class="row">
									<div class="col-lg-12">
										<!--begin::Portlet-->
										<div class="kt-portlet">
											<!--begin::Form-->
											<form class="kt-form kt-form--label-right">
												<input type="text" name="placeId" value="<#if (profile.id)??>${profile.placeId}</#if>" hidden>
												<div class="kt-portlet__body">
													<div class="kt-section kt-section--first">
														<h3 class="kt-section__title">场所基本设置:</h3>
														<div class="kt-section__body">
															<div class="form-group row">
																<label class="col-lg-3 col-form-label">网吧名称:</label>
																<div class="col-lg-3">
																	<input type="text" class="form-control" value="<#if (profile.id)??>${profile.name}</#if>" disabled>
																</div>
																<label class="col-lg-1 col-form-label">网吧编码:</label>
																<div class="col-lg-3">
																	<input type="text" class="form-control"value="<#if (profile.id)??>${profile.placeId}</#if>" disabled>
																</div>
															</div>
															<div class="form-group row">
																<label class="col-lg-3 col-form-label">开启实名:</label>
																<div class="col-lg-3">
																	<input id="switch-realname" var="${config.realname}" name="realname" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="未开启" data-on-color="success" >	
																</div>
																<label class="col-lg-1 col-form-label">开启注册卡:</label>
																<div class="col-lg-3">
																	<input id="switch-regcard" var="${config.regcard}" name="regcard" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="未开启" data-on-color="success" >
																</div>
															</div>

															<div class="form-group row">
																<label class="col-lg-3 col-form-label">开启在线充值:</label>
																<div class="col-lg-3">
																	<input id="switch-onlineTopup" var="${config.onlineTopup}" name="onlineTopup" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="未开启" data-on-color="success" >
																</div>

																<label class="col-lg-1 col-form-label">开启网吧续费:</label>
																<div class="col-lg-3">
																	<input id="switch-renew" var="${config.renew}" name="renew" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="未开启" data-on-color="success" >
																</div>
															</div>

															<div class="form-group row">
																<label class="col-lg-3 col-form-label">重启设置:</label>
																<div class="col-lg-3">
																	<input id="switch-lockAndLoginOut" var="${config.lockAndLoginOut}" name="lockAndLoginOut" data-switch="true" type="checkbox" data-on-text="结账" data-handle-width="40" data-off-text="登入" data-on-color="success" >
																</div>
																<label class="col-lg-1 col-form-label">扫码登入设置:</label>
																<div class="col-lg-3">
																	<input id="switch-wechatQRCodeLogin" var="${config.wechatQRCodeLogin}" name="wechatQRCodeLogin" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="未开启" data-on-color="success" >
																</div>
															</div>
															
															<div class="form-group row">
																<label class="col-lg-3 col-form-label">收银台实名二维码:</label>
																<div class="col-lg-3">
																	<input id="switch-cashierRealnameQrcode" var="${config.cashierRealnameQrcode}" name="cashierRealnameQrcode" data-switch="true" type="checkbox" data-on-text="开启" data-handle-width="40" data-off-text="关闭" data-on-color="success" >
																</div>
															</div>
															
														</div>
													</div>
												</div>
												<div class="kt-portlet__foot">
													<div class="kt-form__actions">
														<div class="row">
															<div class="col-lg-5"></div>
															<div class="col-lg-6">
																<button type="button" id="kt_submit" class="btn btn-success">确定</button>
																&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
																<button type="reset" class="btn btn-secondary">取消</button>
															</div>
														</div>
													</div>
												</div>
											</form>
											<!--end::Form-->
										</div>
										<!--end::Portlet-->
									</div>
								</div>
							</div>
							<!-- end:: Content -->
						</div>
					</div>
				</div>

				<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
			</div>
		</div>
	</div>
	<!-- end:: Page --> 
	
	<#include "/base/script.html"> 
	
	<!--begin::Page Vendors(used by this page) -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/locales/bootstrap-datepicker.zh-CN.min.js"></script>
	<script src="/assets/js/pages/place/config/edit.js" type="text/javascript"></script> 
	<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
