<!DOCTYPE html>
<html lang="zh-cmn-Hans" xmlns="http://www.w3.org/1999/html">
<!-- begin::Head -->
<head>
    <meta charset="utf-8"/>
    <title>四维管家 | 管理控制台</title>
    <#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
    <link href="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet"
          type="text/css"/>
    <!--end::Page Vendors Styles -->
    <style>
    /* 额外的样式来确保下拉菜单可以显示复选框 */
    .dropdown-menu {
        padding: 10px;
    }
    .checkbox label, .checkbox-inline {
        padding-left: 0;
    }
</style>

</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
<!-- begin:: Page -->

<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->

<div class="kt-grid kt-grid--hor kt-grid--root">
    <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
        <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

            <!-- begin:: Header -->
            <#include "/base/header.html">
            <!-- end:: Header -->

            <div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
                <div class="kt-container  kt-container--fluid ">

                    <!-- begin:: Aside -->
                    <#include "/base/aside.html">
                    <!-- end:: Aside -->

                    <div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

                        <!-- begin:: Subheader -->
                        <div class="kt-subheader   kt-grid__item" id="kt_subheader">
                            <div class="kt-container  kt-container--fluid ">
                                <div class="kt-subheader__main">
                                    <h3 class="kt-subheader__title">场所管理</h3>
                                    <span class="kt-subheader__separator kt-subheader__separator--v"></span>
                                    <div class="kt-subheader__group" id="kt_subheader_search">
                                        <span class="kt-subheader__desc" id="kt_subheader_total">${title}</span>
                                    </div>
                                </div>
                                <div class="kt-subheader__toolbar">
                                    <a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
                                </div>
                            </div>
                        </div>
                        <!-- end:: Subheader -->

                        <!-- begin:: Content -->
                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                            <div class="row">
                                <div class="col-lg-12">
                                    <!--begin::Portlet-->
                                    <div class="kt-portlet">
                                        <div class="kt-portlet__body">
                                            <ul class="nav nav-tabs  nav-tabs-line nav-tabs-bold nav-tabs-line-3x nav-tabs-line-success"
                                                role="tablist">
                                                <li class="nav-item">
                                                    <a class="nav-link active" data-toggle="tab" href="#kt_tabs_config"
                                                       role="tab">
                                                        <i class="la la-gear"></i>网吧配置
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_profile"
                                                       role="tab"><i class="la la-info-circle"></i>基本信息</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_account"
                                                       role="tab"><i class="la la-user"></i>网吧账号</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_area"
                                                       role="tab"><i class="la la-desktop"></i></i>网吧区域</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_card_type"
                                                       role="tab"><i class="la la-credit-card"></i>计费卡类型</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_topup_rule"
                                                       role="tab"><i class="la la-yen"></i>充值规则</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_area1"
                                                       role="tab"><i class="la la-desktop"></i>客户端</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_member"
                                                       role="tab"><i class="la la-user"></i>会员导入</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_regcard"
                                                       role="tab"><i class="la la-user"></i>注册卡导入</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_wanxiangUpdate"
                                                       role="tab"><i class="la la-user"></i>pw会员更新导入(临时功能)</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_wanxiangAdd"
                                                       role="tab"><i class="la la-user"></i>pw会员新增导入(临时功能)</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" data-toggle="tab" href="#kt_tabs_submit_place_shift"
                                                       role="tab"><i class="la la-user"></i>后台确认交接班</a>
                                                </li>
                                            </ul>
                                            <div class="tab-content">
                                                <div class="tab-pane active" id="kt_tabs_config" role="tabpanel">
                                                    <form class="kt-form kt-form--label-right">
                                                        <input type="hidden" id="placeId" name="placeId"
                                                               value="${profile.placeId}">
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">实名认证:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-realname" var="${config.realname!''}"
                                                                       name="realname" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">注 册 卡:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-regcard" var="${config.regcard}"
                                                                       name="regcard" data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>

                                                            <#if (config.regcard == 1)>
                                                                <label class="col-lg-1.5 col-form-label">移动端扫码跳过注册卡绑定
                                                                    :</label>
                                                                <div class="col-lg-1">
                                                                    <input id="switch-check"
                                                                           var="${config.checkRegcard}"
                                                                           name="checkRegcard" data-switch="true"
                                                                           type="checkbox" data-on-text="是"
                                                                           data-handle-width="40" data-off-text="否"
                                                                           data-on-color="success">
                                                                </div>
                                                                <!-- 需要跳过注册卡的客户端复选配置 -->
                                                                <input type="text" name="regcardCheckedClients" id="regcardCheckedClients" class="form-control" value="${config.regcardCheckedClients}" hidden>
                                                                <div class="dropdown">
                                                                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                                                                                                                                                                                         请选择
                                                                        <span class="caret"></span>
                                                                    </button>
                                                                    <ul id="clientTypeSelect" class="dropdown-menu" aria-labelledby="dropdownMenu3">
                                                                        <li>
                                                                            <div class="checkbox">
                                                                                <label><input id="clientType4All" type="checkbox" value="0" checked>全部</label>
                                                                            </div>
                                                                        </li>
                                                                        <li>
                                                                            <div class="checkbox">
                                                                                <label><input type="checkbox" value="1">四维新版&龙大师网咖管家</label>
                                                                            </div>
                                                                        </li>
                                                                        <li>
                                                                            <div class="checkbox">
                                                                                <label><input type="checkbox" value="2">易上网APP</label>
                                                                            </div>
                                                                        </li>
                                                                        <li>
                                                                            <div class="checkbox">
                                                                                <label><input type="checkbox" value="3">IOT自助机</label>
                                                                            </div>
                                                                        </li>
                                                                        <li>
                                                                            <div class="checkbox">
                                                                                <label><input type="checkbox" value="4">支付宝小程序</label>
                                                                            </div>
                                                                        </li>
                                                                    </ul>
                                                                </div>

                                                            </#if>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">在线支付:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-onlineTopup"
                                                                       var="${config.onlineTopup}" name="onlineTopup"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">自主续费:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-renew" var="${config.renew}"
                                                                       name="renew" data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">重启设置:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-lockAndLoginOut"
                                                                       var="${config.lockAndLoginOut}"
                                                                       name="lockAndLoginOut" data-switch="true"
                                                                       type="checkbox" data-on-text="结账"
                                                                       data-handle-width="40" data-off-text="登入"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">扫码登入:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-wechatQRCodeLogin"
                                                                       var="${config.wechatQRCodeLogin}"
                                                                       name="wechatQRCodeLogin" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">注册卡场所名称:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input type="text" id="name" name="name"
                                                                       class="form-control"
                                                                       placeholder="请输入注册卡场所名称"
                                                                       value="<#if (profile.id)??>${profile.name}</#if>"
                                                                       disabled>
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">场所名称:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input type="text" id="displayName" name="displayName"
                                                                       class="form-control" placeholder="请输入网吧名称"
                                                                       value="<#if (profile.id)??>${profile.displayName}</#if>"
                                                                       disabled>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">收银台实名二维码:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-cashierRealnameQrcode"
                                                                       var="${config.cashierRealnameQrcode}"
                                                                       name="cashierRealnameQrcode" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>

                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">客户端扫码方式:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <select class="form-control" id="clientQrCodeAuth"
                                                                        name="clientQrCodeAuth">

                                                                    <option value="V8MPWECHAT"
                                                                            <#if clientQrCodeAuth=='V8MPWECHAT'>selected</#if> >
                                                                     龙大师网咖管家
                                                                    </option>

                                                                <option value="ALIRZXAPP"
                                                                <#if clientQrCodeAuth=='ALIRZXAPP'>selected</#if> >
                                                            支付宝任子行网吧认证解锁小程序
                                                                 </option>
                                                                    <option value="ZFDJMP"
                                                                            <#if clientQrCodeAuth=='ZFDJMP'>selected</#if> >
                                                                      紫枫电竞
                                                                    </option>

                                                                </select>
                                                            </div>
                                                       </div>

                                        <div class="form-group row justify-content-center">
                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">收银台扫码方式:</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                <select class="form-control" id="cashierQrCodeAuth"
                                                        name="cashierQrCodeAuth">

                                                    <option value="V8MPWECHAT"
                                                    <#if cashierQrCodeAuth=='V8MPWECHAT'>selected</#if> >
                                                龙大师网咖管家
                                                </option>

                                                <option value="ALIRZXAPP"
                                                <#if cashierQrCodeAuth=='ALIRZXAPP'>selected</#if> >
                                            支付宝任子行网吧认证解锁小程序
                                            </option>
                                            <option value="ZFDJMP"
                                            <#if cashierQrCodeAuth=='ZFDJMP'>selected</#if> >
                                        紫枫电竞
                                        </option>

                                        </select>


                                    </div>
                                </div>



                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">禁止客户端直接扫码开卡/激活：</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-forbiddenClientActiveDirectly"
                                                                       var="${placeBizConfigBO.forbiddenClientActiveDirectly!0}"
                                                                       name="forbiddenClientActiveDirectly" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">收银台是否获取认证图片:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-cashierGetAuthImageFlag"
                                                                       var="${config.cashierGetAuthImageFlag}"
                                                                       name="cashierGetAuthImageFlag" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <!-- 支付宝小程序配置 -->
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">支付宝小程序扫码：</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-alipayAppScancodeFlag"
                                                                       var="${placeBizConfigBO.alipayAppScancodeFlag!0}"
                                                                       name="alipayAppScancodeFlag" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">支付宝小程序人脸比对：</label>
                                                            <div class="col-lg-1.5">
                                                                <input id="switch-alipayAppFaceFlag"
                                                                       var="${placeBizConfigBO.alipayAppFaceFlag!0}"
                                                                       name="alipayAppFaceFlag" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-lg-2 col-form-label">支付宝小程序激活卡：</label>
                                                            <div class="col-lg-1.5">
                                                                <input id="switch-alipayAppActiveFlag"
                                                                       var="${placeBizConfigBO.alipayAppActiveFlag!0}"
                                                                       name="alipayAppActiveFlag" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">支付宝小程序跳过广告：</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-aliappSkipAd"
                                                                       var="${placeBizConfigBO.aliappSkipAd!0}"
                                                                       name="aliappSkipAd" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">场所支持碰一碰设备：</label>
                                                            <div class="col-lg-1.5">
                                                                <input id="switch-aliappFaceType"
                                                                       var="${placeBizConfigBO.aliappFaceType!0}"
                                                                       name="aliappFaceType" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">上机年龄区间配置:</label>
                                                            <input id="wechatAgeConfig" name="wechatAgeConfig"
                                                                   type="text" class="form-control" autocomplete="off"
                                                                   value="<#if config??>${config.wechatAgeConfig}</#if>"
                                                                   hidden>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="wechatAgeConfigMin" name="wechatAgeConfigMin"
                                                                       type="text" class="form-control"
                                                                       autocomplete="off"
                                                                       value="<#if wechatAgeConfigMin??>${wechatAgeConfigMin}</#if>">
                                                            </div>

                                                            <label class="col-lg-1 col-form-label"
                                                                   style="text-align: center;">到:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="wechatAgeConfigMax" name="wechatAgeConfigMax"
                                                                       type="text" class="form-control"
                                                                       autocomplete="off"
                                                                       value="<#if wechatAgeConfigMax??>${wechatAgeConfigMax}</#if>">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">人脸认证有效时间(分):</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="kt_faceEffectiveTime" type="text"
                                                                       name="faceEffectiveTime" class="form-control"
                                                                       value="<#if (config.id)??>${config.faceEffectiveTime}<#else>0</#if>">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">特殊证件激活(次/天):</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="kt_nonIdNumber" type="text"
                                                                       name="nonIdNumber" class="form-control"
                                                                       value="${nonIdNumber}">
                                                            </div>

                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">客户端二维码刷新时间(秒):</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="kt_qrcodeRefreshTime" type="text"
                                                                       name="qrcodeRefreshTime" class="form-control"
                                                                       value="<#if (config.id)??>${config.qrcodeRefreshTime}<#else>0</#if>">
                                                            </div>
                                                            <label class="col-xl-1 col-lg-1 col-md-1 col-sm-1 col-form-label">收银台二维码刷新时间(秒):</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="kt_qrcodeRefreshTimeCashier" type="text"
                                                                       name="qrcodeRefreshTimeCashier"
                                                                       class="form-control"
                                                                       value="<#if (config.id)??>${config.qrcodeRefreshTimeCashier!0}<#else>0</#if>">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">强制密码:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <select id="kt_forcePassword" name="forcePassword"
                                                                        class="form-control">
                                                                    <#if forcePassword?? && forcePassword != ''><!-- forcePassword 不为空时 -->
                                                                    <option value="******" selected>身份证后六位
                                                                    </option>
                                                                    <option value="">无强制</option>
                                                                    <#else>
                                                                    <option value="" selected>无强制</option>
                                                                    <option value="******">身份证后六位</option>
                                                                </#if>
                                                                </select>
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">是否允许修改密码:</label>
                                                            <div class="col-lg-2">
                                                                <input id="switch-modifyPassword"
                                                                       var="${modifyPassword!''}" name="modifyPassword"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="允许" data-handle-width="40"
                                                                       data-off-text="禁止" data-on-color="success">
                                                            </div>


                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">是否允许查询密码:</label>
                                                            <div class="col-lg-1.5">
                                                                <input id="switch-queryPassword"
                                                                       var="${queryPassword!''}" name="queryPassword"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="允许" data-handle-width="40"
                                                                       data-off-text="禁止" data-on-color="success">
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">使用注册卡密码:</label>
                                                            <div class="col-lg-1.5">
                                                                <input id="switch-useRegPassword"
                                                                       var="${useRegPassword!''}" name="useRegPassword"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="允许" data-handle-width="40"
                                                                       data-off-text="禁止" data-on-color="success">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">自定义配置:</label>
                                                            <div class="col-lg-5">
                                                                <textarea class="form-control" name="customize"
                                                                          placeholder="key1=value1;key2=value2;"
                                                                          rows="4">${config.customize!"无"}</textarea>
                                                                <span class="form-text text-muted">参数配置格式: key1=value1;key2=value2;（没有空格，英文符号）</span>
                                                            </div>
                                                            <label class="col-xl-2 col-lg-2 col-form-label">超管登入台数</label>
                                                            <div class="col-xl-1 col-lg-1">
                                                                <input id="superLoginNumber" name="superLoginNumber"
                                                                       type="number" class="form-control" autocomplete="off"
                                                                       value="<#if loginNumber??>${loginNumber}</#if>">
                                                            </div>
                                                            <div class="col-xl-1 col-lg-1">
                                                                <input id="superLoginPassword" name="superLoginPassword"
                                                                       type="hidden" class="form-control" autocomplete="off"
                                                                       value="<#if loginPassword??>${loginPassword}</#if>">
                                                            </div>
                                                            <div class="col-xl-1 col-lg-1">
                                                                <input id="superLoginMinutes" name="superLoginMinutes"
                                                                       type="hidden" class="form-control" autocomplete="off"
                                                                       value="<#if loginMinutes??>${loginMinutes}</#if>">
                                                            </div>
                                                        </div>

                                                        <#if (profile.type == 1)>
                                                            <div class="form-group row">
                                                                <label class="col-xl-3 col-lg-3 col-form-label">随机码登入台数</label>
                                                                <div class="col-xl-2 col-lg-2">
                                                                    <input id="randomCodeLoginNumber"
                                                                           name="randomCodeLoginNumber" type="number"
                                                                           class="form-control" autocomplete="off"
                                                                           value="<#if config??>${config.randomCodeLoginNumber}</#if>">
                                                                </div>
                                                            </div>
                                                        </#if>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">是否开启独立版本:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-aloneVersion"
                                                                       var="${config.aloneVersion}" name="aloneVersion"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>

                                                            <label class="col-xl-2 col-lg-2 col-form-label">客户端登录方式:</label>
                                                            <div class="col-xl-3 col-lg-3 ">
                                                                <div class="kt-checkbox-inline">
                                                                    <select class="form-control" id="clientLoginMethod" name="clientLoginMethod">

                                                                    <option value="0"<#if clientLoginMethod=='0'>selected</#if> >密码登录</option>
                                                                    <option value="1"<#if clientLoginMethod=='1'>selected</#if> >扫码登录</option>
                                                                    <option value="1,0"<#if clientLoginMethod=='1,0'>selected</#if> >扫码登录+密码登录</option>
                                                                    <option value="0,1"<#if clientLoginMethod=='0,1'>selected</#if> >密码登录+扫码登录</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">防沉迷配置:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-antiAddiction"
                                                                       var="${antiAddictionConfig!0}"
                                                                       name="antiAddiction" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">是否开启星云:</label>
                                                            <div class="col-lg-2">
                                                                <input id="switch-nebulaOpenFlag"
                                                                       var="${config.nebulaOpenFlag}"
                                                                       name="nebulaOpenFlag" data-switch="true"
                                                                       type="checkbox" data-on-text="开启"
                                                                       data-handle-width="40" data-off-text="关闭"
                                                                       data-on-color="success">
                                                            </div>

                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">人脸认证方式:</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <select class="form-control" id="authWay"
                                                                        name="authWay">
                                                                    <option value="1"
                                                                            <#if (config.authWay)??><#if (config.authWay == 1)>selected</#if></#if>>
                                                                        拍照
                                                                    </option>
                                                                    <option value="2"
                                                                            <#if (config.authWay)??><#if (config.authWay == 2)>selected</#if></#if>>
                                                                        视频
                                                                    </option>
                                                                </select>
                                                            </div>

                                                            <label class="col-xl-2 col-lg-2 col-form-label">客户端二维码不可重复使用：</label>
                                                            <div class="col-xl-3 col-lg-3 ">
                                                                <input id="switch-repeatScanCode"
                                                                       var="${config.repeatScanCode}" name="repeatScanCode"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">加载 CultureBox：</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-cultureBoxFlag"
                                                                       var="${placeBizConfigBO.cultureBoxFlag}" name="cultureBoxFlag"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>

                                                            <label class="col-xl-2 col-lg-2 col-form-label">绑定的第三方账号：</label>
                                                            <div class="col-xl-3 col-lg-3">
                                                                <select class="form-control" id="thirdAccountId" name="thirdAccountId">
                                                                    <option value="" <#if !(placeBizConfigBO.thirdAccountId)??>selected</#if> >无</option>
                                                                    <#if thirdAccountList?exists>
                                                                        <#list thirdAccountList as thirdAccount>
                                                                            <option value="${thirdAccount.thirdAccountId}" <#if ((placeBizConfigBO.thirdAccountId!'null') == thirdAccount.thirdAccountId) >selected</#if> >${thirdAccount.name}（${thirdAccount.thirdAccountId}）</option>
                                                                        </#list>
                                                                    </#if>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">快捷激活(白吧)：</label>
                                                            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-3">
                                                                <input id="switch-quickActivation"
                                                                       var="${config.quickActivation}" name="quickActivation"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>

                                                            <label class="col-xl-2 col-lg-2 col-form-label">实名是否检验证件号：</label>
                                                            <div class="col-xl-2 col-lg-2">
                                                                <input id="switch-realnameCheckIdNumber"
                                                                       var="${config.realnameCheckIdNumber}" name="realnameCheckIdNumber"
                                                                       data-switch="true" type="checkbox"
                                                                       data-on-text="开启" data-handle-width="40"
                                                                       data-off-text="关闭" data-on-color="success">
                                                            </div>
                                                        </div>

                                                        <div id="antiAddictionDetail" hidden>
                                                            <div class="form-group row">
                                                                <label class="col-lg-3 col-form-label"></label>

                                                                <label class="col-lg-1 col-md-2 col-sm-3 kt-checkbox"
                                                                       style="text-align: right;padding-top: calc(0.65rem + 1px);">
                                                                    <input type="checkbox" name="popupNoticeTimeConfig"
                                                                           data-type="popupNoticeTime"
                                                                           class="form-control">连续上机<span
                                                                            style="margin-top: 10px; text-align: right;padding-top: calc(0.65rem + 1px);"></span>
                                                                </label>

                                                                <#--																	<label class="col-lg-3 col-form-label">连续上机</label>-->
                                                                <div class="col-lg-1 col-md-2 col-sm-2">
                                                                    <input type="text" name="popupNoticeTime"
                                                                           class="antiAddiction form-control"
                                                                           value="${antiAddictionBO.popupNoticeTime!''}"
                                                                           disabled>

                                                                </div>
                                                                <div style="font-weight: 400; padding-top: calc(0.65rem + 1px);">
                                                                    小时，客户端弹出防沉迷提示
                                                                </div>
                                                            </div>

                                                            <div class="form-group row">
                                                                <label class="col-lg-3 col-form-label"></label>
                                                                <label class="col-lg-1 col-md-2 col-sm-3 kt-checkbox"
                                                                       style="text-align: right;padding-top: calc(0.65rem + 1px);">
                                                                    <input type="checkbox" name="intervalConfig"
                                                                           data-type="interval">每间隔<span
                                                                            style="margin-top: 10px; text-align: right;padding-top: calc(0.65rem + 1px);"></span>
                                                                </label>
                                                                <#--																	<label class="col-lg-1 col-md-1 col-sm-1 col-form-label">每间隔</label>-->

                                                                <div class="col-lg-1 col-md-2 col-sm-2">
                                                                    <input type="text" name="interval"
                                                                           class="antiAddiction form-control"
                                                                           value="${antiAddictionBO.interval!''}"
                                                                           disabled>
                                                                </div>
                                                                <div style="font-weight: 400; padding-top: calc(0.65rem + 1px);">
                                                                    分钟，客户端再次弹出防沉迷提示
                                                                </div>
                                                            </div>

                                                            <div class="form-group row">
                                                                <label class="col-lg-3 col-form-label"></label>
                                                                <label class="col-lg-1 col-md-2 col-sm-3 kt-checkbox"
                                                                       style="text-align: right;padding-top: calc(0.65rem + 1px);">
                                                                    <input type="checkbox"
                                                                           name="forcefulShutdownTimeConfig"
                                                                           data-type="forcefulShutdownTime">连续上机<span
                                                                            style="margin-top: 10px; text-align: right;padding-top: calc(0.65rem + 1px);"></span>
                                                                </label>

                                                                <#--																	<label class="col-lg-1 col-md-1 col-sm-1  col-form-label">连续上机</label>-->
                                                                <div class="col-lg-1 col-md-2 col-sm-2">
                                                                    <input type="text" name="forcefulShutdownTime"
                                                                           class="antiAddiction form-control"
                                                                           value="${antiAddictionBO.forcefulShutdownTime!''}"
                                                                           disabled>
                                                                </div>

                                                                <div style="font-weight: 400; padding-top: calc(0.65rem + 1px);">
                                                                    小时，强制结账重启，回到锁屏
                                                                </div>
                                                            </div>

                                                            <div class="form-group row">
                                                                <label class="col-lg-3 col-form-label"></label>
                                                                <label class="col-lg-1 col-md-2 col-sm-3 kt-checkbox"
                                                                       style="text-align: right;padding-top: calc(0.65rem + 1px);">
                                                                    <input type="checkbox" name="forbidLoginTimeConfig"
                                                                           data-type="forbidLoginTime">强制结账后<span
                                                                            style="margin-top: 10px; text-align: right;padding-top: calc(0.65rem + 1px);"></span>
                                                                </label>

                                                                <#--																	<label class="col-lg-1 col-md-1 col-sm-1  col-form-label">强制结账后</label>-->
                                                                <div class="col-lg-1 col-md-2 col-sm-2">
                                                                    <input type="text" name="forbidLoginTime"
                                                                           class="antiAddiction form-control"
                                                                           value="${antiAddictionBO.forbidLoginTime!''}"
                                                                           disabled>
                                                                </div>
                                                                <div style="font-weight: 400; padding-top: calc(0.65rem + 1px);">
                                                                    分钟内，禁止再次登录上机
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <div class="col-xl-1 col-lg-1"></div>
                                                            <label class="col-xl-3 col-lg-3 col-md-2 col-sm-2 col-form-label">附加费触发方式:</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6">
                                                                <div class="kt-checkbox-inline">
                                                                    <#list activeTypes? keys as key>
                                                                    <label class="kt-checkbox">
                                                                        <input type="checkbox" name="activeTypes" value="${activeTypes[key]}" <#if surchargeConfigBO??><#list surchargeConfigBO.activeTypes?split("_") as type><#if (activeTypes[key]==type)>checked</#if></#list></#if> >${key}
                                                                        <span></span>
                                                                    </label>
                                                                    </#list>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="kt-portlet__foot">
                                                            <div class="kt-form__actions">
                                                                <div class="row">
                                                                    <div class="col-lg-4"></div>
                                                                    <div class="col-lg-6">
                                                                        <button type="button" id="kt_submit_config"
                                                                                class="btn btn-success btn-wide">确定
                                                                        </button>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <!--																			<button type="reset" class="btn btn-secondary btn-wide">取消</button>-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                                <div class="tab-pane" id="kt_tabs_profile" role="tabpanel">
                                                    <!--begin::Form-->
                                                    <form class="kt-form kt-form--label-right">
                                                        <input type="hidden" id="id" name="id" value="${profile.id?c}">
                                                        <!--																<input type="hidden" id="billingType" name="billingType" value="${profile.billingType?c}" >-->
                                                        <!--																<input type="hidden" id="type" name="type" value="${profile.type?c}" >-->
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">识别码:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="identifier" name="identifier"
                                                                       class="form-control" placeholder="系统自动生成"
                                                                       value="${profile.identifier}" readonly>
                                                            </div>

                                                            <label class="col-lg-1 col-form-label">场所ID:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="placeId" name="placeId"
                                                                       class="form-control" placeholder="请输入场所编码"
                                                                       value="${profile.placeId}" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">场所名称:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="displayName" name="displayName"
                                                                       class="form-control" placeholder="请输入场所名称"
                                                                       value="${profile.displayName}">
                                                            </div>

                                                            <label class="col-lg-1 col-form-label">收银台IP地址:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="cashierLocalServer"
                                                                       name="cashierLocalServer" class="form-control"
                                                                       placeholder="请输入收银台IP地址"
                                                                       value="<#if (config.id)??>${config.cashierLocalServer}</#if>">
                                                            </div>
                                                        </div>
                                                        <div class="kt-separator kt-separator--border-dashed kt-separator--space-lg"></div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">注册卡名称:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="name" name="name"
                                                                       class="form-control"
                                                                       placeholder="请输入注册卡场所名称"
                                                                       value="${profile.name}">
                                                            </div>
                                                            <label class="col-lg-1  col-form-label">注册卡服务器:</label>
                                                            <div class="col-lg-3 ">
                                                                <select class="form-control" id="regcardServerGroupName"
                                                                        name="regcardServerGroupName">
                                                                    <option value=""
                                                                            <#if regcardServerGroupName=='error'>selected</#if> >
                                                                        无
                                                                    </option>
                                                                    <option value="quanguo"
                                                                            <#if regcardServerGroupName=='quanguo'>selected</#if> >
                                                                        全国
                                                                    </option>
                                                                    <option value="shenzhen"
                                                                            <#if regcardServerGroupName=='shenzhen'>selected</#if> >
                                                                        网租
                                                                    </option>
                                                                    <option value="hubei"
                                                                            <#if regcardServerGroupName=='hubei'>selected</#if> >
                                                                        湖北
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">场所类型:</label>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" name="type" id="placeType">
                                                                    <option value="0"
                                                                            <#if profile.type==0>selected</#if> >网吧
                                                                    </option>
                                                                    <option value="1"
                                                                            <#if profile.type==1>selected</#if> >酒店
                                                                    </option>
                                                                    <option value="2"
                                                                            <#if profile.type==2>selected</#if> >网租
                                                                    </option>
                                                                    <option value="3"
                                                                            <#if profile.type==3>selected</#if> >普通场所
                                                                    </option>
                                                                    <option value="4"
                                                                            <#if profile.type==4>selected</#if> >营销大师
                                                                    </option>
                                                                    <option value="5"
                                                                            <#if profile.type==5>selected</#if> >九威
                                                                    </option>
                                                                    <option value="6"
                                                                            <#if profile.type==6>selected</#if> >大巴掌
                                                                    </option>
                                                                    <option value="7"
                                                                            <#if profile.type==7>selected</#if> >PMS
                                                                    </option>
                                                                    <option value="8"
                                                                            <#if profile.type==8>selected</#if> >龙管家
                                                                    </option>
                                                                    <option value="301"
                                                                            <#if profile.type==301>selected</#if> >万象
                                                                    </option>
                                                                    <option value="302"
                                                                            <#if profile.type==302>selected</#if> >嘟嘟牛
                                                                    </option>
                                                                    <option value="303"
                                                                            <#if profile.type==303>selected</#if> >轻网联盟
                                                                    </option>
                                                                    <option value="304"
                                                                            <#if profile.type==304>selected</#if> >佳星
                                                                    </option>
                                                                    <option value="305"
                                                                            <#if profile.type==305>selected</#if> >百果树
                                                                    </option>
                                                                    <option value="306"
                                                                            <#if profile.type==306>selected</#if> >奥比特
                                                                    </option>
                                                                    <option value="307"
                                                                            <#if profile.type==307>selected</#if> >丕微
                                                                    </option>
                                                                </select>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" name="billingType"
                                                                        id="placeBillingType">
                                                                    <option value="1"
                                                                            <#if profile.billingType==1>selected</#if>>
                                                                        计费场所
                                                                    </option>
                                                                    <option value="0"
                                                                            <#if profile.billingType==0>selected</#if>>
                                                                        非计费场所
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">区域ID:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="areaId" class="form-control"
                                                                       placeholder="注册卡功能必须"
                                                                       value="${profile.areaId!}" hidden>
                                                                <select class="form-control" id="regCardAreaIdSelect"
                                                                        name="areaId">
                                                                </select>
                                                            </div>
                                                            <label class="col-lg-1 col-form-label">审计ID:</label>
                                                            <div class="col-lg-3">
                                                                <input type="text" id="auditId" name="auditId"
                                                                       class="form-control" placeholder="注册卡功能必须"
                                                                       value="${profile.auditId!}">
                                                            </div>
                                                        </div>
                                                        <div class="kt-separator kt-separator--border-dashed kt-separator--space-lg"></div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">所在地区:</label>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" id="provinceSelect">
                                                                    <option value="">请选择</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" id="citySelect">
                                                                    <option value="">请选择</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" id="countrySelect">
                                                                    <option value="">请选择</option>
                                                                </select>
                                                            </div>
                                                            <!--
                                                            <div class="col-lg-2" id="townDiv">
                                                                <select class="form-control" id="townSelect">
                                                                    <option value="">请选择</option>
                                                                </select>
                                                            </div>
                                                             -->
                                                            <input type="hidden" id="code" name="regionCode">
                                                            <input type="hidden" id="regionCode"
                                                                   value="<#if (profile.id)??>${profile.regionCode}</#if>">
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">详细地址:</label>
                                                            <div class="col-lg-5">
                                                                <input type="text" id="address" name="address"
                                                                       class="form-control"
                                                                       placeholder="准确详细的地址可获得LBS服务"
                                                                       value="<#if (profile.id)??>${profile.address}</#if>">
                                                                <span class="form-text text-muted">地址规则: 省份、城市、区县、城镇、乡村、街道、门牌号码、屋邨、大厦。<br/>例如: 北京市朝阳区阜通东大街6号</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">法人:</label>
                                                            <div class="col-lg-2">
                                                                <input type="text" name="frequentContactor"
                                                                       class="form-control"
                                                                       placeholder="请输入常用联系人"
                                                                       value="<#if (profile.id)??>${profile.frequentContactor}</#if>">
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">法人手机:</label>
                                                            <div class="col-lg-2">
                                                                <input type="text" name="frequentContactorMobile"
                                                                       class="form-control"
                                                                       placeholder="请输入联系人手机"
                                                                       value="<#if (profile.id)??>${profile.frequentContactorMobile}</#if>">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">注册终端:</label>
                                                            <div class="col-lg-2">
                                                                <input id="kt_touchspin" name="clientNum" type="text"
                                                                       class="form-control"
                                                                       value="<#if (profile.id)??>${profile.clientNum}<#else>0</#if>">
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">注册收银台:</label>
                                                            <div class="col-lg-2">
                                                                <div class="input-group date">
                                                                    <input type="text" id="kt_touchspin_cashier"
                                                                           name="cashierNum" class="form-control"
                                                                           value="<#if (profile.id)??>${profile.cashierNum}<#else>0</#if>"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">服务终止时间:</label>
                                                            <div class="col-lg-2">
                                                                <div class="input-group date">
                                                                    <#if profile.expired??>
                                                                        <#if profile.expired?length lt 19>
                                                                            <input type="text" name="expiredStr"
                                                                                   class="form-control kt_datepicker"
                                                                                   value="<#if (profile.expired)??>${profile.expired?replace("T"," ") + ":00"}</#if>"
                                                                                   placeholder="请指定服务终止时间"/>
                                                                        <#else>
                                                                            <input type="text" name="expiredStr"
                                                                                   class="form-control kt_datepicker"
                                                                                   value="<#if (profile.expired)??>${profile.expired?replace("T"," ")?substring(0,19)}</#if>"
                                                                                   placeholder="请指定服务终止时间"/>
                                                                        </#if>
                                                                    </#if>

                                                                </div>
                                                            </div>

                                                            <label class="col-lg-2 col-form-label">龙兜账号:</label>
                                                            <div class="col-lg-2">
                                                                <div class="input-group date">
                                                                    <input type="text" id="merUsername"
                                                                           name="merUsername" class="form-control"
                                                                           value="<#if (profile.id)??>${profile.merUsername!''}</#if>"/>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    <#if (profile.versionFlag!=9)>
                                                        <div class="form-group row">
                                                            <label class="col-lg-3 col-form-label">版本标志:</label>
                                                            <div class="col-lg-2">
                                                                <select class="form-control" name="versionFlag"  id="versionFlagStr">

                                                                    <option value="0"
                                                                    <#if profile.versionFlag==0>selected</#if> >基础版
                                                                    </option>

                                                                    <option value="1"
                                                                    <#if profile.versionFlag==1>selected</#if> >标准版
                                                                    </option>

                                                                    <option value="2"
                                                                    <#if profile.versionFlag==2>selected</#if> >旗舰版
                                                                    </option>

                                                                <#if (profile.versionFlag==10)>
                                                                    <option value="9"
                                                                    <#if profile.versionFlag==9>selected</#if> >
                                                                    </option>
                                                                </#if>

                                                                 </select>
                                                            </div>
                                                        </div>
                                                    </#if>

                                                    <div class="form-group row">

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除会员数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanMemberLimit"
                                                                   var="0" name="cleanMemberLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除收银台会员数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanCashierMemberLimit"
                                                                   var="0" name="cleanCashierMemberLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>
                                                    </div>

                                                    <div class="form-group row">

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除网吧会员详情数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanPlaceCardIdMemberLimit"
                                                                   var="0" name="cleanPlaceCardIdMemberLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除收银台上机记录数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanCashierLogLoginLimit"
                                                                   var="0" name="cleanCashierLogLoginLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>
                                                    </div>

                                                    <div class="form-group row">

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除网吧后台上机记录数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanPlaceLogLoginLimit"
                                                                   var="0" name="cleanPlaceLogLoginLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>

                                                        <label class="col-xl-3 col-lg-3 col-form-label">解除网吧后台上机记录详情数据请求限制：</label>
                                                        <div class="col-xl-2 col-lg-2">
                                                            <input id="switch-cleanPlaceLogLoginDetailsLimit"
                                                                   var="0" name="cleanPlaceLogLoginDetailsLimit"
                                                                   data-switch="true" type="checkbox"
                                                                   data-on-text="解除" data-handle-width="40"
                                                                   data-off-text="限制" data-on-color="success">
                                                        </div>
                                                    </div>


                                                        <div class="form-group row" id="cloudVersionExpirationTimeDiv">
                                                            <label class="col-lg-3 col-form-label">云版本到期时间:</label>
                                                            <div class="col-lg-2">
                                                                <div class="input-group date">
                                                                    <#if profile.cloudVersionExpired??>
                                                                        <#if profile.cloudVersionExpired?length lt 19>
                                                                            <input type="text"
                                                                                   name="cloudVersionExpiredStr"
                                                                                   class="form-control kt_datepicker"
                                                                                   value="<#if (profile.cloudVersionExpired)??>${profile.cloudVersionExpired?replace("T"," ") + ":00"}</#if>"
                                                                                   placeholder="请指定云版本到期时间"/>
                                                                        <#else>
                                                                            <input type="text"
                                                                                   name="cloudVersionExpiredStr"
                                                                                   class="form-control kt_datepicker"
                                                                                   value="<#if (profile.cloudVersionExpired)??>${profile.cloudVersionExpired?replace("T"," ")?substring(0,19)}</#if>"
                                                                                   placeholder="请指定云版本到期时间"/>

                                                                        </#if>
                                                                    <#else>
                                                                        <input type="text" name="cloudVersionExpiredStr"
                                                                               class="form-control kt_datepicker"
                                                                               value="<#if (profile.cloudVersionExpired)??>${profile.cloudVersionExpired?replace("T"," ")}</#if>"
                                                                               placeholder="请指定云版本到期时间"/>
                                                                    </#if>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="kt-portlet__foot">
                                                            <div class="kt-form__actions">
                                                                <div class="row">
                                                                    <div class="col-lg-4"></div>
                                                                    <div class="col-lg-6">
                                                                        <button type="button" id="kt_submit_profile"
                                                                                class="btn btn-success btn-wide">确定
                                                                        </button>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <!--																			<button type="reset" class="btn btn-secondary btn-wide">取消</button>-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                    <!--end::Form-->
                                                </div>
                                                <div class="tab-pane" id="kt_tabs_account" role="tabpanel">
                                                    <!--begin: Datatable -->
                                                    <table class="table table-striped- table-bordered table-hover table-checkable"
                                                           id="kt_table_account">
                                                        <thead>
                                                        <tr>
                                                            <th>序号</th>
                                                            <th>账号ID</th>
                                                            <th>登录名</th>
                                                            <th>账号名</th>
                                                            <th>手机</th>
                                                            <th>类型</th>
                                                            <th>操作</th>
                                                        </tr>
                                                        </thead>
                                                    </table>
                                                    <!--end: Datatable -->
                                                    <#--															<div class="kt-portlet__foot">-->
                                                    <#--																<div class="row">-->
                                                    <#--																	<div class="col-lg-10"></div>-->
                                                    <#--																	<div class="col-lg-2">-->
                                                    <#--																		<a href="/place/account/edit/${profile.placeId}/0"  class="btn btn-label-success btn-pill">添加账号</a>-->
                                                    <#--																	</div>-->
                                                    <#--																</div>-->
                                                    <#--															</div>-->
                                                </div>
                                                <div class="tab-pane" id="kt_tabs_area" role="tabpanel">
                                                    <!--begin: Datatable -->
                                                    <table class="table table-striped- table-bordered table-hover table-checkable"
                                                           id="kt_table_area">
                                                        <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>区域ID</th>
                                                            <th>区域名称</th>
                                                            <th>默认区域</th>
<#--                                                            <th>操作</th>-->
                                                        </tr>
                                                        </thead>
                                                    </table>
                                                    <!--end: Datatable -->
                                                    <div class="kt-portlet__foot">
<#--                                                        <div class="row">-->
<#--                                                            <div class="col-lg-10"></div>-->
<#--                                                            <div class="col-lg-2">-->
<#--                                                                <a href="/place/area/edit/${profile.placeId}/0"-->
<#--                                                                   class="btn btn-label-success btn-pill">添加区域</a>-->
<#--                                                            </div>-->
<#--                                                        </div>-->
                                                    </div>
                                                </div>
                                                <div class="tab-pane" id="kt_tabs_card_type" role="tabpanel">
                                                    <!--begin: Datatable -->
                                                    <table class="table table-striped- table-bordered table-hover table-checkable"
                                                           id="kt_table_cardtype">
                                                        <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>类型ID</th>
                                                            <th>类型名称</th>
                                                            <th>级别</th>
                                                            <th>操作</th>
                                                        </tr>
                                                        </thead>
                                                    </table>
                                                    <!--end: Datatable -->
                                                    <div class="kt-portlet__foot">
                                                        <div class="row">
                                                            <div class="col-lg-10"></div>
                                                            <div class="col-lg-2">
                                                                <a href="/place/cardtype/edit/${profile.placeId}/0"
                                                                   class="btn btn-label-success btn-pill">添加卡类型</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--														<div class="tab-pane" id="kt_tabs_billing_rule" role="tabpanel">-->
                                                <!--															&lt;!&ndash;begin: Datatable &ndash;&gt;-->
                                                <!--															<table class="table table-striped- table-bordered table-hover table-checkable" id="kt_table_billing_rule">-->
                                                <!--																<thead>-->
                                                <!--																	<tr>-->
                                                <!--																		<th>ID</th>-->
                                                <!--																		<th>规则ID</th>-->
                                                <!--																		<th>区域名称</th>-->
                                                <!--																		<th>卡类型</th>-->
                                                <!--																		<th>价格</th>-->
                                                <!--																		<th>是否包时</th>-->
                                                <!--																		<th>开始时间</th>-->
                                                <!--																		<th>结束时间</th>-->
                                                <!--																		<th>操作</th>-->
                                                <!--																	</tr>-->
                                                <!--																</thead>-->
                                                <!--															</table>-->
                                                <!--															&lt;!&ndash;end: Datatable &ndash;&gt;-->
                                                <!--															<div class="kt-portlet__foot">-->
                                                <!--																<div class="row">-->
                                                <!--																	<div class="col-lg-10"></div>-->
                                                <!--																	<div class="col-lg-2">-->
                                                <!--																		<a href="/place/billingRule/edit/${profile.placeId}/0"  class="btn btn-label-success btn-pill">添加计费规则</a>-->
                                                <!--																	</div>-->
                                                <!--																</div>-->
                                                <!--															</div>-->
                                                <!--														</div>-->
                                                <div class="tab-pane" id="kt_tabs_topup_rule" role="tabpanel">
                                                    <!--begin: Datatable -->
                                                    <table class="table table-striped- table-bordered table-hover table-checkable"
                                                           id="kt_table_topup_rule">
                                                        <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>规则ID</th>
                                                            <th>卡类型</th>
                                                            <th>充值金额</th>
                                                            <th>赠送金额</th>
<#--                                                            <th>操作</th>-->
                                                        </tr>
                                                        </thead>
                                                    </table>
                                                    <!--end: Datatable -->
                                                    <div class="kt-portlet__foot">
<#--                                                        <div class="row">-->
<#--                                                            <div class="col-lg-10"></div>-->
<#--                                                            <div class="col-lg-2">-->
<#--                                                                <a href="/place/topupRule/edit/${profile.placeId}/0"-->
<#--                                                                   class="btn btn-label-success btn-pill">添加充值规则</a>-->
<#--                                                            </div>-->
<#--                                                        </div>-->
                                                    </div>
                                                </div>

                                                <!-- 客户端 -->
                                                <div class="tab-pane" id="kt_tabs_area1" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20" id="clientForm">
                                                        <div class="row kt-margin-b-20">
                                                            <div class="col-lg-2 col-md-3 col-sm-3 kt-margin-b-10-tablet-and-mobile">
                                                                <label>区域名称:</label>
                                                                <select class="form-control kt-input" data-col-index="2"
                                                                        id="areaSelect" name="areaSelect"
                                                                        style="width: auto"></select>
                                                            </div>
                                                            <div class="ol-lg-1 col-md-1 col-sm-1" style="margin-top: 25px;">
                                                                <button id="exportClientList" class="btn btn-brand btn-elevate btn-icon-sm" >
                                                                    <i class="la la-plus"></i> 导出
                                                                </button>
                                                            </div>
                                                        </div>

                                                    </form>

                                                    <table class="table table-striped- table-bordered table-hover table-checkable"
                                                           id="kt_table_client">
                                                        <thead>
                                                        <tr>
                                                            <th>区域id</th>
                                                            <th>区域名称</th>
                                                            <th>机器名</th>
                                                            <th>MAC地址</th>
                                                            <th>IP地址</th>
                                                            <th>客户端版本</th>
                                                        </tr>
                                                        </thead>
                                                    </table>
                                                </div>

                                                <!-- 会员导入 -->
                                                <div class="tab-pane" id="kt_tabs_member" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20"
                                                          id="memberImportForm">
                                                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <!--begin::Portlet-->
                                                                    <div class="kt-portlet">
                                                                        <!--begin::Form-->
                                                                        <div class="kt-portlet__body">
                                                                            <div class="kt-section kt-section--first">
                                                                                <h3 class="kt-section__title">
                                                                                    上传会员数据Excel:</h3>
                                                                                <div class="kt-section__body">
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">场所Id:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="placeId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="${profile.placeId}"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">文件:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="file"
                                                                                                   name="file"
                                                                                                   class="form-control">
                                                                                            <span class="form-text text-muted">请上传一个包含会员列表的xlsx文件</span>

                                                                                        </div>
                                                                                    </div>

                                                                                    <div class="form-group row">
                                                                                        <h3 class="kt-section__title">
                                                                                            导入模板(表头与模板不一致则会导入不成功!):<a
                                                                                                    href="https://4wgj.topfreeweb.net/importTemplate/%E4%BC%9A%E5%91%98%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx">点击下载</a>
                                                                                        </h3>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="kt-portlet__foot">
                                                                            <div class="kt-form__actions">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5 col-md-5 col-sm-5"></div>
                                                                                    <div class="col-lg-6 col-md-6 col-sm-6">
                                                                                        <button type="button"
                                                                                                id="kt_submit"
                                                                                                class="btn btn-success">
                                                                                            确定
                                                                                        </button>
                                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                        <button type="reset"
                                                                                                id="history"
                                                                                                class="btn btn-secondary">
                                                                                            重置
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end::Form-->
                                                                    </div>
                                                                    <!--end::Portlet-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>

                                                <!-- 注册卡导入 -->
                                                <div class="tab-pane" id="kt_tabs_regcard" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20"
                                                          id="regcardImportForm">
                                                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <!--begin::Portlet-->
                                                                    <div class="kt-portlet">
                                                                        <!--begin::Form-->
                                                                        <div class="kt-portlet__body">
                                                                            <div class="kt-section kt-section--first">
                                                                                <h3 class="kt-section__title">
                                                                                    上传注册卡数据Excel:</h3>
                                                                                <div class="kt-section__body">
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">审计Id:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="auditId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="<#if (profile.auditId)??>${profile.auditId}</#if>"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">区域Id:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="areaId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="<#if (profile.auditId)??><#if (profile.areaId)??>${profile.areaId}</#if></#if>"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">文件:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="file"
                                                                                                   name="file"
                                                                                                   class="form-control">
                                                                                            <span class="form-text text-muted">请上传一个包含注册卡列表的xlsx文件</span>

                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <h3 class="kt-section__title">
                                                                                            导入模板(表头与模板不一致则会导入不成功!):<a
                                                                                                    href="https://4wgj.topfreeweb.net/importTemplate/%E6%B3%A8%E5%86%8C%E5%8D%A1%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx">点击下载</a>
                                                                                        </h3>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="kt-portlet__foot">
                                                                            <div class="kt-form__actions">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5 col-md-5 col-sm-5"></div>
                                                                                    <div class="col-lg-6 col-md-6 col-sm-6">
                                                                                        <button type="button"
                                                                                                id="kt_submit_1"
                                                                                                class="btn btn-success">
                                                                                            确定
                                                                                        </button>
                                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                        <button type="reset"
                                                                                                class="btn btn-secondary">
                                                                                            重置
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end::Form-->
                                                                    </div>
                                                                    <!--end::Portlet-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>

                                                <!-- pw会员更新导入(临时功能) -->
                                                <div class="tab-pane" id="kt_tabs_wanxiangUpdate" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20"
                                                          id="wanxiangUpdateImportForm">
                                                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <!--begin::Portlet-->
                                                                    <div class="kt-portlet">
                                                                        <!--begin::Form-->
                                                                        <div class="kt-portlet__body">
                                                                            <div class="kt-section kt-section--first">
                                                                                <h3 class="kt-section__title">
                                                                                    上传pubw数据Excel:</h3>
                                                                                <div class="kt-section__body">
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">placeId:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="placeId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="<#if (profile.placeId)??>${profile.placeId}</#if>"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">文件:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="file"
                                                                                                   name="file"
                                                                                                   class="form-control">
                                                                                            <span class="form-text text-muted">请上传一个包含pubw数据列表的xlsx文件</span>

                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="kt-portlet__foot">
                                                                            <div class="kt-form__actions">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5 col-md-5 col-sm-5"></div>
                                                                                    <div class="col-lg-6 col-md-6 col-sm-6">
                                                                                        <button type="button"
                                                                                                id="kt_submit_2"
                                                                                                class="btn btn-success">
                                                                                            确定
                                                                                        </button>
                                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                        <button type="reset"
                                                                                                class="btn btn-secondary">
                                                                                            重置
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end::Form-->
                                                                    </div>
                                                                    <!--end::Portlet-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>

                                                <!-- pw会员新增导入(临时功能) -->
                                                <div class="tab-pane" id="kt_tabs_wanxiangAdd" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20"
                                                          id="wanxiangAddImportForm">
                                                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <!--begin::Portlet-->
                                                                    <div class="kt-portlet">
                                                                        <!--begin::Form-->
                                                                        <div class="kt-portlet__body">
                                                                            <div class="kt-section kt-section--first">
                                                                                <h3 class="kt-section__title">
                                                                                    上传pubw新增数据Excel:</h3>
                                                                                <div class="kt-section__body">
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">placeId:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="placeId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="<#if (profile.placeId)??>${profile.placeId}</#if>"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">文件:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="file"
                                                                                                   name="file"
                                                                                                   class="form-control">
                                                                                            <span class="form-text text-muted">请上传一个包含注册卡列表的xlsx文件</span>

                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="kt-portlet__foot">
                                                                            <div class="kt-form__actions">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5 col-md-5 col-sm-5"></div>
                                                                                    <div class="col-lg-6 col-md-6 col-sm-6">
                                                                                        <button type="button"
                                                                                                id="kt_submit_3"
                                                                                                class="btn btn-success">
                                                                                            确定
                                                                                        </button>
                                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                        <button type="reset"
                                                                                                class="btn btn-secondary">
                                                                                            重置
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end::Form-->
                                                                    </div>
                                                                    <!--end::Portlet-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>


                                                <!-- 后台确认交接班 -->
                                                <div class="tab-pane" id="kt_tabs_submit_place_shift" role="tabpanel">
                                                    <form class="kt-form kt-form--fit kt-margin-b-20"
                                                          id="submitPlaceShiftForm">
                                                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <!--begin::Portlet-->
                                                                    <div class="kt-portlet">
                                                                        <!--begin::Form-->
                                                                        <div class="kt-portlet__body">
                                                                            <div class="kt-section kt-section--first">
                                                                                <h3 class="kt-section__title">
                                                                                    后台确认交接班: (<span style="color:red">当前端收银台交班页面提示长时间未交班无法继续交班的，可在此操作交班</span>)</h3>
                                                                                <div class="kt-section__body">
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">placeId:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="placeId"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="<#if (profile.placeId)??>${profile.placeId}</#if>"
                                                                                                   readonly>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">收银台:</label>
                                                                                        <div class="col-lg-2">
                                                                                            <select class="form-control" id="cashierId" name="cashierId" >
                                                                                                <#if (placeCashierBOs)??>
                                                                                                <#list placeCashierBOs as placeCashierBO>
                                                                                                <option value="${placeCashierBO.cashierId}">
                                                                                                    <#if (placeCashierBO.cashierName)??>
                                                                                                    ${placeCashierBO.cashierName}
                                                                                                </#if>
                                                                                                （${placeCashierBO.cashierId}）
                                                                                                </option>
                                                                                            </#list>
                                                                                        </#if>
                                                                                        </select>
                                                                                        </div>
                                                                                    </div>

                                                                                    <!--交班人账号名称Id-->
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">交班人账号名称:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="successorAccountName"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="">
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">交班人密码:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="password"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="">
                                                                                        </div>
                                                                                    </div>
                                                                                    <!--预留下班金额-->
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">预留下班金额(元):</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="nextShiftHandoverCashStr"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="">
                                                                                        </div>
                                                                                    </div>
                                                                                    <!--备注-->
                                                                                    <div class="form-group row">
                                                                                        <label class="col-lg-3 col-form-label">备注:</label>
                                                                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xl-3">
                                                                                            <input type="text"
                                                                                                   name="remark"
                                                                                                   class="form-control"
                                                                                                   autocomplete="off"
                                                                                                   value="通过管理后台确认交接班"
                                                                                                   readonly
                                                                                            >
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="kt-portlet__foot">
                                                                            <div class="kt-form__actions">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5 col-md-5 col-sm-5"></div>
                                                                                    <div class="col-lg-6 col-md-6 col-sm-6">
                                                                                        <button type="button"
                                                                                                id="kt_submit_place_shift"
                                                                                                class="btn btn-success">
                                                                                            确定
                                                                                        </button>
                                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                        <button type="reset"
                                                                                                class="btn btn-secondary">
                                                                                            重置
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end::Form-->
                                                                    </div>
                                                                    <!--end::Portlet-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Portlet-->
                                </div>
                            </div>
                        </div>
                        <!-- end:: Content -->
                    </div>
                </div>
            </div>

            <!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
        </div>
    </div>
</div>
<!-- end:: Page -->

<#include "/base/script.html">

<!--begin::Page Vendors(used by this page) -->
<script src="https://4wgj.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.js"
        type="text/javascript"></script>
<script src="/assets/js/pages/place/profile/edit.js" type="text/javascript"></script>
<script src="/assets/js/datetimepicker.zh-CN.js" type="text/javascript"></script>
<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
