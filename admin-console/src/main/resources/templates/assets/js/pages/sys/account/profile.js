"use strict";

// Class definition
var KTUserEdit = function () {
	// Base elements
	var avatar;
	var initUserForm = function() {
		avatar = new KTAvatar('kt_user_edit_avatar');
	}	
	
	var showErrorMsg = function(form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">'+msg+'</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

	var handleProfileFormSubmit = function() {
        $('#kt_edit_profile_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            form.validate({
                rules: {
                	nickname: {
                        required: true
                    },
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/sys/account/profile',
                type: 'POST',
                success: function(response) {
                	console.log(JSON.stringify(response))
                	if(response.result){
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'info', response.message);
                	}else{
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'warning', response.message);
                	}
                },
                error: function(response, status){
                	btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                	showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }
	
	var handlePasswordFormSubmit = function() {
        $('#kt_edit_password_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            form.validate({
                rules: {
                	password: {
                        required: true,
                        minlength: 6
                    },
                    newPassword: {
                        required: true,
                        minlength: 6
                    },
                    reNewPassword: {
                    	required: true,
                    	equalTo: "#newPassword"
                    }
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/sys/account/password',
                type: 'POST',
                success: function(response) {
                	scrollTo(0,0);
                	if(response.result){
                		showErrorMsg(form, 'info', response.message);
                		window.location.href=response.data.result;//登录后跳转
                	}else{
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'warning', response.message);
                	}
                },
                error: function(response, status){
                	btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                	showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }
	
	var handleMobileFormSubmit = function() {
        $('#kt_edit_mobile_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            form.validate({
                rules: {
                	mobile: {
                        required: true,
                        mobile: true,
                    },
                    code: {
                        required: true,
                        minlength: 6,
                        maxlength: 6,
                    },
                }
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/sys/account/mobile',
                type: 'POST',
                success: function(response) {
                	if(response.result){
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'success', response.message);
                	}else{
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'warning', response.message);
                	}
                },
                error: function(response, status){
                	btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                	showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }
	
	var sendCode = function (){
		$("#kt_send_validcode_sumit").click(function(e) {
			e.preventDefault();
	        var btn = $(this);
	        var form = $(this).closest('form');
	        var mobileReg = /^1[3|5|6|7|8|9]{1}[0-9]{9}$/;
	        var mobileValue = $("#mobile").val();
	        var countdown = 60;
	        if (mobileValue.length == 11 && mobileReg.test(mobileValue)) {
	        	$.ajax({
						url: "sendcode?mobile=" + mobileValue,
			        	success: function (sendCodeResult) {
			        		if ("ok" == sendCodeResult) {
			        			var timer = setInterval(function() {
		 							countdown--;
		 							if (countdown == 0) {
		 								btn.attr('disabled', false);
		 								btn.html("获取验证码");
		 								clearInterval(timer);
		 							} else {
		 								btn.attr('disabled', true);
	 		 						btn.html("重新发送(" + countdown + "s)");
		 							}
		 						}, 1000);
		 					} else {
			        			showErrorMsg(form, 'danger', "验证码发送失败，请稍后再试");
			        		}
			        	}
					});
	        } else {
	        	showErrorMsg(form, 'warning', "请输入手机号码");
	        }
		});
	};
	
	var initWechatLogin = function (){
		var wechatLoginState = $('#wechatLoginState').val()
		if (typeof(wechatLoginState) != "undefined"){
			var obj = new WxLogin({
				self_redirect : false,
				id : "wechat_bind",
				appid : "wxa468a7f8cae10889",
				scope : "snsapi_login",
				redirect_uri : "http://admin.4wgj.com/sys/account/profile/wechat/bind",
				state : wechatLoginState,
				style : "white",
				href : "http://localhost:8000/assets/css/qrcode.css"
			});
		}
	};
	
	return {
		// public functions
		init: function() {
			initUserForm();
			handleProfileFormSubmit();
			handlePasswordFormSubmit();
			handleMobileFormSubmit();
			sendCode();
			initWechatLogin();
		}
	};
	
}();

jQuery(document).ready(function() {	
	
	jQuery.validator.addMethod("mobile", function(value, element) {
		var length = value.length;
		var mobile = /^1[3|5|6|7|8|9]{1}[0-9]{9}$/;
		return this.optional(element) || (length == 11 && mobile.test(value));
	}, "请填写手机号码");	
	
	jQuery.extend(jQuery.validator.messages, {
		remote : "验证码不正确",
	});
	
	KTUserEdit.init();
		
});