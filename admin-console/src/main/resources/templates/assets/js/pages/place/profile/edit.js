// Class definition
var areaId;
var KTFormControls = function () {
    // Private functions

    var initSwitch = function () {
        $('[data-switch=true]').bootstrapSwitch();

        if ($("#switch-realname").attr("var") == 0) {
            $("#switch-realname").bootstrapSwitch('state', false)
        } else {
            $("#switch-realname").bootstrapSwitch('state', true)
        }

        if ($("#switch-modifyPassword").attr("var") == 0) {
            $("#switch-modifyPassword").bootstrapSwitch('state', false)
        } else {
            $("#switch-modifyPassword").bootstrapSwitch('state', true)
        }

        //是否允许查询密码
        if ($("#switch-queryPassword").attr("var") == 0) {
            $("#switch-queryPassword").bootstrapSwitch('state', false)
        } else {
            $("#switch-queryPassword").bootstrapSwitch('state', true)
        }

        //是否允许查询密码
        if ($("#switch-useRegPassword").attr("var") == 0) {
            $("#switch-useRegPassword").bootstrapSwitch('state', true)
        } else {
            $("#switch-useRegPassword").bootstrapSwitch('state', false)
        }

        if ($("#switch-regcard").attr("var") == 0) {
            $("#switch-regcard").bootstrapSwitch('state', false)
        } else {
            $("#switch-regcard").bootstrapSwitch('state', true)
        }
        if ($("#switch-check").attr("var") == 0) {
            $("#switch-check").bootstrapSwitch('state', false)
            // 
            $('.dropdown').hide();
        } else {
            $("#switch-check").bootstrapSwitch('state', true)
            $('.dropdown').show();
        }
        $("#switch-check").bootstrapSwitch().on('switchChange.bootstrapSwitch', function(event, state) {  
           if(state){
	          $('.dropdown').show();
              clientTypesEventMonitor();
           }else{
	          $('.dropdown').hide();
           }
        });

        if ($("#switch-onlineTopup").attr("var") == 0) {
            $("#switch-onlineTopup").bootstrapSwitch('state', false)
        } else {
            $("#switch-onlineTopup").bootstrapSwitch('state', true)
        }

        if ($("#switch-renew").attr("var") == 0) {
            $("#switch-renew").bootstrapSwitch('state', false)
        } else {
            $("#switch-renew").bootstrapSwitch('state', true)
        }

        if ($("#switch-lockAndLoginOut").attr("var") == 0) {
            $("#switch-lockAndLoginOut").bootstrapSwitch('state', false)
        } else {
            $("#switch-lockAndLoginOut").bootstrapSwitch('state', true)
        }

        if ($("#switch-wechatQRCodeLogin").attr("var") == 0) {
            $("#switch-wechatQRCodeLogin").bootstrapSwitch('state', false)
        } else {
            $("#switch-wechatQRCodeLogin").bootstrapSwitch('state', true)
        }

        if ($("#switch-cashierRealnameQrcode").attr("var") == 0) {
            $("#switch-cashierRealnameQrcode").bootstrapSwitch('state', false)
        } else {
            $("#switch-cashierRealnameQrcode").bootstrapSwitch('state', true)
        }

        if ($("#switch-forbiddenClientActiveDirectly").attr("var") == 0) {
            $("#switch-forbiddenClientActiveDirectly").bootstrapSwitch('state', false)
        } else {
            $("#switch-forbiddenClientActiveDirectly").bootstrapSwitch('state', true)
        }

        if ($("#switch-cashierGetAuthImageFlag").attr("var") == 0) {
            $("#switch-cashierGetAuthImageFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-cashierGetAuthImageFlag").bootstrapSwitch('state', true)
        }

        if ($("#switch-alipayAppScancodeFlag").attr("var") == 0) {
            $("#switch-alipayAppScancodeFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-alipayAppScancodeFlag").bootstrapSwitch('state', true)
        }
        if ($("#switch-alipayAppFaceFlag").attr("var") == 0) {
            $("#switch-alipayAppFaceFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-alipayAppFaceFlag").bootstrapSwitch('state', true)
        }
        if ($("#switch-alipayAppActiveFlag").attr("var") == 0) {
            $("#switch-alipayAppActiveFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-alipayAppActiveFlag").bootstrapSwitch('state', true)
        }

        if ($("#switch-nebulaOpenFlag").attr("var") == 0) {
            $("#switch-nebulaOpenFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-nebulaOpenFlag").bootstrapSwitch('state', true)
        }

        if ($("#switch-aloneVersion").attr("var") == 0) {
            $("#switch-aloneVersion").bootstrapSwitch('state', false)
        } else {
            $("#switch-aloneVersion").bootstrapSwitch('state', true)
        }

        // if ($("#switch-shop").attr("var") == 0) {
        //     $("#switch-shop").bootstrapSwitch('state', false)
        // } else {
        //     $("#switch-shop").bootstrapSwitch('state', true)
        // }

        if ($("#switch-repeatScanCode").attr("var") == 0) {
            $("#switch-repeatScanCode").bootstrapSwitch('state', false)
        } else {
            $("#switch-repeatScanCode").bootstrapSwitch('state', true)
        }

        let antiAddictionStateShow = false
        if ($switchAntiAddiction.attr("var") == 0) {
            $switchAntiAddiction.bootstrapSwitch('state', false)
        } else {
            $switchAntiAddiction.bootstrapSwitch('state', true)
            antiAddictionStateShow = true
        }
        initAntiAddictionDetail(antiAddictionStateShow)


        if ($("#switch-cultureBoxFlag").attr("var") == 0) {
            $("#switch-cultureBoxFlag").bootstrapSwitch('state', false)
        } else {
            $("#switch-cultureBoxFlag").bootstrapSwitch('state', true)
        }
        // 解除获取会员数据限制
        if ($("#switch-cleanMemberLimit").attr("var") == 0) {
            $("#switch-cleanMemberLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanMemberLimit").bootstrapSwitch('state', true)
        }

        // 解除收银台获取会员数据限制
        if ($("#switch-cleanCashierMemberLimit").attr("var") == 0) {
            $("#switch-cleanCashierMemberLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanCashierMemberLimit").bootstrapSwitch('state', true)
        }

        // 解除网吧会员详情获取会员数据限制
        if ($("#switch-cleanPlaceCardIdMemberLimit").attr("var") == 0) {
            $("#switch-cleanPlaceCardIdMemberLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanPlaceCardIdMemberLimit").bootstrapSwitch('state', true)
        }
        // 是否允许快捷激活(白吧),0 不允许(默认)，1 允许
        if ($("#switch-quickActivation").attr("var") == 0) {
            $("#switch-quickActivation").bootstrapSwitch('state', false)
        } else {
            $("#switch-quickActivation").bootstrapSwitch('state', true)
        }

        // 解除网吧上机记录获取数据限制
        if ($("#switch-cleanPlaceLogLoginLimit").attr("var") == 0) {
            $("#switch-cleanPlaceLogLoginLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanPlaceLogLoginLimit").bootstrapSwitch('state', true)
        }

        // 解除网吧上机记录详情获取数据限制
        if ($("#switch-cleanPlaceLogLoginDetailsLimit").attr("var") == 0) {
            $("#switch-cleanPlaceLogLoginDetailsLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanPlaceLogLoginDetailsLimit").bootstrapSwitch('state', true)
        }

        if ($("#switch-realnameCheckIdNumber").attr("var") == 0) {
            $("#switch-realnameCheckIdNumber").bootstrapSwitch('state', false)
        } else {
            $("#switch-realnameCheckIdNumber").bootstrapSwitch('state', true)
        }

        // 解除收银台上机记录获取数据限制
        if ($("#switch-cleanCashierMemberLimit").attr("var") == 0) {
            $("#switch-cleanCashierMemberLimit").bootstrapSwitch('state', false)
        } else {
            $("#switch-cleanCashierMemberLimit").bootstrapSwitch('state', true)
        }
        if ($("#switch-aliappSkipAd").attr("var") == 0) {
            $("#switch-aliappSkipAd").bootstrapSwitch('state', false)
        } else {
            $("#switch-aliappSkipAd").bootstrapSwitch('state', true)
        }
        if ($("#switch-aliappFaceType").attr("var") == 0) {
            $("#switch-aliappFaceType").bootstrapSwitch('state', false)
        } else {
            $("#switch-aliappFaceType").bootstrapSwitch('state', true)
        }

        // 广告
        // if ($("#switch-advertisement").attr("var") == 0) {
        //     $("#switch-advertisement").bootstrapSwitch('state', false)
        // } else {
        //     $("#switch-advertisement").bootstrapSwitch('state', true)
        // }

    }

	function clientTypesEventMonitor(){
		var faceclientTypes = $('#clientTypeSelect').find(':checkbox');
		faceclientTypes.prop('checked', false);// 清空
		
		var regcardCheckedClients = $('#regcardCheckedClients').val();
		if(regcardCheckedClients != undefined && regcardCheckedClients!=''){
			var clienttypes = regcardCheckedClients.split(",");
			    for(var i=0; i<clienttypes.length; i++){
			    	faceclientTypes.each(function() {
                        if($(this).val() === clienttypes[i]){
	                       $(this).prop('checked', true);
	                       return;
                        }
                    });
			    }
		}
		
		faceclientTypes.click(function(){
			var checkedSize=$('#clientTypeSelect').find(':checkbox:checked').length;
			if (checkedSize == 4) {
				$('#faceclienttype4All').prop('disabled', true);
            } else if(checkedSize == 0) {
	            $('#faceclienttype4All').prop('disabled', false);
            }
		});
		
	    $('#clientType4All').change(function() {  
            if ($(this).is(':checked')) {  
                faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', true);
                     $(this).prop('checked', false);
                  }
                });
            } else {
                faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', false);
                  }
                });
            }
        });
        if($('#clientType4All').is(':checked')){
			faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', true);
                  }
            });
		}else{
			faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', false);
                  }
            });
		}
		
	}


    /* 防沉迷配置 start*/
    let $antiAddictionDetail = $("#antiAddictionDetail")
    let $switchAntiAddiction = $("#switch-antiAddiction")
    let antiAddictionChange = function () {
        initCheckBox()
        $switchAntiAddiction.change(function () {
        })
        $switchAntiAddiction.on('switchChange.bootstrapSwitch', function (e, state) {
            updateAntiAddictionDetailHidden(state)
        });
    }

    let initAntiAddictionDetail = function (antiAddictionStateShow) {
        updateAntiAddictionDetailHidden(antiAddictionStateShow)
    }

    let updateAntiAddictionDetailHidden = function (show) {
        if (show) {
            $antiAddictionDetail.prop("hidden", false)
        } else {
            $antiAddictionDetail.prop("hidden", true)
        }
    }

    $("input[type = checkbox]").change(function (e) {
        let currentTarget = e.currentTarget
        let name = currentTarget.name
        let $inputEle = $("input[name='" + name + "']")
        let checked = $inputEle.prop('checked')
        let type = $inputEle.data("type")
        if (!checked) {
            // 取消勾选，删除后面的内容，同时不可编辑
            updateDetailConfig(type, false)
        } else {
            updateDetailConfig(type, true)
        }
    })

    let updateDetailConfig = function (name, use) {
        let $input = $("input[name='" + name + "']")
        if (use) {
            $input.prop("disabled", false)
        } else {
            $input.val("").prop("disabled", true)
        }
    }

    let initCheckBox = function () {
        // let $popupNoticeTime = $("input[name='antiAddiction.popupNoticeTime']")
        // let $interval = $("input[name='antiAddiction.interval']")
        // let $forcefulShutdownTime = $("input[name='antiAddiction.forcefulShutdownTime']")
        // let $forbidLoginTime = $("input[name='antiAddiction.forbidLoginTime']")

        let $popupNoticeTime = $("input[name='popupNoticeTime']")
        let $interval = $("input[name='interval']")
        let $forcefulShutdownTime = $("input[name='forcefulShutdownTime']")
        let $forbidLoginTime = $("input[name='forbidLoginTime']")

        let popupNoticeTimeVal = $popupNoticeTime.val()
        let intervalVal = $interval.val()
        let forcefulShutdownTimeVal = $forcefulShutdownTime.val()
        let forbidLoginTimeVal = $forbidLoginTime.val()

        if (popupNoticeTimeVal > 0) {
            $("input[name='popupNoticeTimeConfig']").prop("checked", true)
            $popupNoticeTime.prop("disabled", false)
        }
        if (intervalVal > 0) {
            $("input[name='intervalConfig']").prop("checked", true)
            $interval.prop("disabled", false)
        }
        if (forcefulShutdownTimeVal > 0) {
            $("input[name='forcefulShutdownTimeConfig']").prop("checked", true)
            $forcefulShutdownTime.prop("disabled", false)
        }
        if (forbidLoginTimeVal > 0) {
            $("input[name='forbidLoginTimeConfig']").prop("checked", true)
            $forbidLoginTime.prop("disabled", false)
        }
    }
    /* 防沉迷配置 end*/

    var datepicker = function () {
        var arrows;
        if (KTUtil.isRTL()) {
            arrows = {
                leftArrow: '<i class="la la-angle-right"></i>',
                rightArrow: '<i class="la la-angle-left"></i>'
            }
        } else {
            arrows = {
                leftArrow: '<i class="la la-angle-left"></i>',
                rightArrow: '<i class="la la-angle-right"></i>'
            }
        }

        $('.kt_datepicker').datetimepicker({
            rtl: KTUtil.isRTL(),
            format: 'yyyy-mm-dd hh:ii:ss',
            language: "zh-CN",
            todayHighlight: true,
            orientation: "bottom left",
            templates: arrows,
        });
    }

    var initTouchspin = function () {
        $('#kt_touchspin').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 1,
            max: 1000,
            step: 1,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });

        $('#wechatAgeConfigMin').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 99,
            step: 1,
            decimals: 0,
            boostat: 1,
            maxboostedstep: 1,
        });

        $('#wechatAgeConfigMax').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 99,
            step: 1,
            decimals: 0,
            boostat: 1,
            maxboostedstep: 1,
        });

        $('#kt_faceEffectiveTime').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 999999,
            step: 1,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });

        $('#kt_qrcodeRefreshTime').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 86400,
            step: 5,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });

        $('#kt_qrcodeRefreshTimeCashier').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 86400,
            step: 5,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });

         $('#kt_nonIdNumber').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 0,
            max: 9999,
            step: 1,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });

    }

    var initTouchspinCashier = function () {
        $('#kt_touchspin_cashier').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',
            min: 1,
            max: 1000,
            step: 1,
            decimals: 0,
            boostat: 10,
            maxboostedstep: 50,
        });
    }

    var showErrorMsg = function (form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">' + msg + '</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

    var initCascadeSelect = function () {
        initProvince();
        $("#provinceSelect").change(function () {
            var provinceId = $("#provinceSelect").val();
            if (provinceId != null && provinceId != '' && provinceId != 'undefined') {
                initCity(provinceId);
            } else {
                $("#citySelect").empty();
                $("#citySelect").append("<option value=''>请选择</option>");
                $("#countrySelect").empty();
                $("#countrySelect").append("<option value=''>请选择</option>");
                //$("#townSelect").empty();
                //$("#townSelect").append("<option value=''>请选择</option>");
                //$('#townDiv').attr("readonly", false);
            }
        });

        $("#citySelect").change(function () {
            var cityId = $("#citySelect").val();
            if (cityId != null && cityId != '' && cityId != 'undefined') {
                initCountry(cityId);
            } else {
                $("#countrySelect").empty();
                $("#countrySelect").append("<option value=''>请选择</option>");
                //$("#townSelect").empty();
                //$("#townSelect").append("<option value=''>请选择</option>");
                //$('#townDiv').attr("readonly", false);
            }
        });

        $("#countrySelect").change(function () {
            var countryId = $("#countrySelect").val();
            $('#code').val(countryId);
            //if (countryId != null && countryId != '' && countryId != 'undefined') {
                //initTown(countryId);
            //} else {
                //$("#townSelect").empty();
                //$("#townSelect").append("<option value=''>请选择</option>");
                //$('#townDiv').attr("readonly", false);
            //}
            $("#address").val($("#provinceSelect option:selected").text() + $("#citySelect option:selected").text() + $("#countrySelect option:selected").text());
        });
        
        /** 
        $("#townSelect").change(function () {
            var cityId = $("#citySelect").val();
            if (cityId == 441900000000 || cityId == 442000000000) {
                $("#address").val($("#provinceSelect option:selected").text() + $("#citySelect option:selected").text() + $("#townSelect option:selected").text());
            } else {
                $("#address").val($("#provinceSelect option:selected").text() + $("#citySelect option:selected").text() + $("#countrySelect option:selected").text() + $("#townSelect option:selected").text());
                $('#code').val($("#townSelect").val());
            }
        });*/

        $("#areaSelect").change(function () {
            areaId = $("#areaSelect").val();
            if (areaId != null) {
                initTableClient();
            }
        });
    }
    var handleProfileFormSubmit = function () {
        $('#kt_submit_profile').click(function (e) {
            // 接口反扒限制开关
            var cleanMemberLimitSwitch = $('#switch-cleanMemberLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanMemberLimit').val(cleanMemberLimitSwitch);
            // 解除收银台请求会员数量开关
            var cleanCashierMemberLimitSwitch = $('#switch-cleanCashierMemberLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanCashierMemberLimit').val(cleanCashierMemberLimitSwitch);

            // 解除网吧会员详情请求会员数量开关
            var cleanPlaceCardIdMemberLimit = $('#switch-cleanPlaceCardIdMemberLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanPlaceCardIdMemberLimit').val(cleanPlaceCardIdMemberLimit);

            // 解除网吧上机记录请求数量开关
            var cleanPlaceLogLoginLimit = $('#switch-cleanPlaceLogLoginLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanPlaceLogLoginLimit').val(cleanPlaceLogLoginLimit);

            // 解除网吧上机记录详情请求数量开关
            var cleanPlaceLogLoginDetailsLimit = $('#switch-cleanPlaceLogLoginDetailsLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanPlaceLogLoginDetailsLimit').val(cleanPlaceLogLoginDetailsLimit);

            // 解除收银台上机记录请求数量开关
            var cleanCashierLogLoginLimit = $('#switch-cleanCashierLogLoginLimit').val() === 'on' ? 1 : 0;
            $('#switch-cleanCashierLogLoginLimit').val(cleanCashierLogLoginLimit);


            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            form.validate({
                // define validation rules
                rules: {
                    type: {
                        required: true
                    },
                    billingType: {
                        required: true
                    },
                    displayName: {
                        required: true,
                        nameVerification: true,
                        minlength: 2
                    },
                    name: {
                        nameVerification: true
                    },
                    placeId: {
                        required: true,
                        minlength: 14,
                        maxlength: 14,
                    },
                    regionCode: {
                        required: true,
                    },
                    address: {
                        required: true,
                        minlength: 5,
                        maxlength: 50
                    },
                    frequentContactor: {
                        required: true,
                        minlength: 2,
                        maxlength: 10,
                    },
                    frequentContactorMobile: {
                        required: true,
                        mobile: true,
                        maxlength: 11,
                    },
                    clientNum: {
                        required: true,
                        digits: true,
                        min: 1
                    },
                    cashierNum: {
                        required: true,
                        digits: true,
                        min: 1
                    },
                    expiredStr: {
                        required: true,
                    },
                    randomCodeLoginNumber: {
                        required: true,
                        number: true
                    },
                    merUsername: {
                        required: false,
                        maxlength: 20
                    }
                },
            });

            if (!form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/place/profile/edit',
                type: 'POST',
                data: function (params) {
                    console.log('params1', params);
                },
                success: function (response) {
                    if (response.result) {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        $("#identifier").val(response.data.obj.identifier);
                        $("#placeId").val(response.data.obj.placeId);
                        $("#lngAndLat").val(response.data.obj.lngAndLat);
                        $("#id").val(response.data.obj.id);
                        // $("#").val(response.data.obj.id);
                        showErrorMsg(form, 'success', response.message);
                        window.scrollTo(0, 0)
                        setTimeout(function(){
                            window.location.reload();
                            },
                            2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'warning', response.message);
                    }

                },
                error: function (response, status) {
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

    var exportClientList = function (){
        $("#exportClientList").click(function (e){
            e.preventDefault();
            console.info("$exportBtn is click ")

            swal.fire({
                title: "正在导出......",
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });

            // use axios
            axios({
                method: "post",
                url: "/place/client/export?placeId="+$('#placeId').val(),
                headers: {"Content-Type": "application/json;charset=utf-8"},
                responseType: "blob"
            }).then(function (response) {
                swal.close();
                let data = response.data
                // var data = res.data;
                let blob = new Blob([data], {type: 'application/octet-stream'});
                let url = URL.createObjectURL(blob);
                let exportLink = document.createElement('a');
                exportLink.setAttribute("download", "客户端信息列表.xlsx");
                exportLink.href = url;
                document.body.appendChild(exportLink);
                exportLink.click();
            }).catch(function (error) {
                swal.close();
                console.log(error);
                let data = error.data
                swal.fire({
                    "title": "导出失败", "text": data.message, "type": "error"
                })
            })
        });
    }

    var handleConfigFormSubmit = function () {
        $('#kt_submit_config').click(function (e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            form.validate({
                // define validation rules
                rules: {
                    popupNoticeTime: {
                        number: true,
                        min: 0
                    },
                    interval: {
                        digits: true
                    },
                    forcefulShutdownTime: {
                        number: true,
                        min: 0
                    },
                    forbidLoginTime: {
                        digits: true
                    },
                    superLoginPassword: {
                        required: true
                    },
                    superLoginNumber: {
                        required: true,
                        number: true
                    },
                    superLoginMinutes: {
                        required: true,
                        number: true
                    },
                },
            });

            var activeTypes = "";
            if (!form.valid()) {
                return;
            }else{
                $('input[name="activeTypes"]').each(function(i,v){
                	if(this.checked===true){
                		activeTypes = activeTypes + v.value + "_";
                	}
                });
                activeTypes = activeTypes.toString().substring(0,activeTypes.length - 1);
            }

            var wechatAgeConfigMin = $('#wechatAgeConfigMin').val();
            var wechatAgeConfigMax = $('#wechatAgeConfigMax').val();
            if (Number(wechatAgeConfigMax) < Number(wechatAgeConfigMin)) {
                showErrorMsg(form, 'warning', "年龄上限不能小于下限");
                return;
            }

            $("#wechatAgeConfig").val(wechatAgeConfigMin + "-" + wechatAgeConfigMax);

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            // 自定义formData，防沉迷对象创建 antiAddictionBO
            let formData = new FormData(form[0]);
            let antiAddictionStatus;
            let useRegPassword = 1;
            for (let [key, value] of formData.entries()) {
                // console.log(key, value);
                // for form[0] to get type=checkbox value
                for (let i = 0; i < form[0].length; i++) {
                    if (form[0][i].type === "checkbox" && form[0][i].name === key) {
                        // value = form[0][i].value;
                        console.log("checkbox is ", key, value);
                        formData.set(key, 1);
                        if (form[0][i].name === "antiAddiction") {
                            antiAddictionStatus = 1;
                        }
                        if (form[0][i].name === "useRegPassword") {
                            useRegPassword = 0;
                        }
                    }
                }
            }

            if (antiAddictionStatus === 1) {
                if (formData.get('popupNoticeTime') !== null) {
                    formData.set('antiAddictionBO.popupNoticeTime', formData.get('popupNoticeTime'));
                }
                if (formData.get('interval') !== null) {
                    formData.set('antiAddictionBO.interval', formData.get('interval'));
                }
                if (formData.get('forcefulShutdownTime') !== null) {
                    formData.set('antiAddictionBO.forcefulShutdownTime', formData.get('forcefulShutdownTime'));
                }
                if (formData.get('forbidLoginTime') !== null) {
                    formData.set('antiAddictionBO.forbidLoginTime', formData.get('forbidLoginTime'));
                }
            }
            formData.set("activeTypes",activeTypes);
            formData.set("useRegPassword",useRegPassword);

            // 
            if($("#switch-check").attr("var") == 1){
			   var clientTypesChecked = $('#clientTypeSelect').find(':checkbox:checked');
               var	clientTypeSelect4cheched="";
               clientTypesChecked.each(function() {  
                   // 你可以在这里处理每个被勾选的checkbox  
                   // 例如，打印出它们的值  
                   clientTypeSelect4cheched += $(this).val() + ",";  
                   console.log($(this).val());
               });
               if(clientTypeSelect4cheched == undefined || clientTypeSelect4cheched.trim() === ''){
	              // alert("请选择移动端扫码跳过注册卡绑定的扫脸终端类型");
                  btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                  showErrorMsg(form, 'warning', "请选择移动端扫码跳过注册卡绑定的扫脸终端类型");
                  return;
               }
               clientTypeSelect4cheched = clientTypeSelect4cheched.substring(0, clientTypeSelect4cheched.length-1);
               console.log(clientTypeSelect4cheched);
               formData.set("regcardCheckedClients",clientTypeSelect4cheched);
            }

            $.ajax({
                url: '/place/config/edit1',
                type: 'POST',
                processData : false, //告诉jQuery不要去处理发送的数据
                contentType: false,//告诉jQuery不要去设置Content-Type请求头
                data: formData,
                success: function (response) {
                    if (response.result) {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        $("#identifier").val(response.data.obj.identifier);
                        $("#placeId").val(response.data.obj.placeId);
                        $("#lngAndLat").val(response.data.obj.lngAndLat);
                        $("#id").val(response.data.obj.id);
                        showErrorMsg(form, 'success', response.message);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'warning', response.message);
                    }

                },
                error: function (response, status) {
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                },
                complete: function (res) {
                    window.scrollTo(0, 0)
                }
            });
        });
    }

    let $kt_table_accountDT
    var initTableAccount = function () {
        // var table = $('#kt_table_account');
        var placeId = $('#placeId').val();

        // begin first table
        $kt_table_accountDT = $kt_table_account.DataTable({
            language: {
                "url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            ordering: false, // 字段排序
            paging: false,
            ajax: '/place/account/accounts/' + placeId,
            columns: [
                {data: 'id'},
                {data: 'accountId'},
                {data: 'loginName'},
                {data: 'accountName'},
                {data: 'mobile'},
                {data: 'type'},
                {data: '操作', responsivePriority: -1},
            ],
            columnDefs: [
                {
                    targets: -1,
                    title: '操作',
                    render: function (data, type, full, meta) {
                        return '<a href="/place/account/edit/' + placeId + '/' + full.accountId + '" class="btn btn-sm btn-clean btn-icon btn-icon-md" title="编辑">' +
                            '<i class="la la-edit"></i>' +
                            '</a>' +
                            '<a href="javascript:void(0)" class="btn btn-sm btn-clean btn-icon btn-icon-md"' +
                            ' data-place-id="' + full.placeId +
                            '" data-account-id="' + full.accountId + '" id="resetPassword" title="重置密码">' +
                            '<i class="la flaticon2-reload-1"></i>' +
                            '</a>';
                    },
                },
                {
                    targets: -2,
                    render: function (data, type, full, meta) {
                        if (data == 0) {
                            return '<span class="kt-badge kt-badge--inline kt-badge--danger">管理员</span>';
                        } else if (data == 1) {
                            return '<span class="kt-badge kt-badge--inline kt-badge--warning">场所管理</span>';
                        } else {
                            return '<span class="kt-badge kt-badge--inline kt-badge--info">收银员</span>';
                        }
                    },
                },

            ],
        });
    }

    var initTableArea = function () {
        var table = $('#kt_table_area');
        var placeId = $('#placeId').val();
        table.DataTable({
            language: {
                "url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            ordering: false, // 字段排序
            paging: false,
            ajax: {
                url: '/place/area/areas/' + placeId,
                type: 'GET',
            },
            columns: [
                {data: 'id'},
                {data: 'areaId'},
                {data: 'areaName'},
                {data: 'isDefault'},
                // {data: '操作', responsivePriority: -1},
            ],
            columnDefs: [
                // {
                //     targets: -1,
                //     title: '操作',
                //     render: function (data, type, full, meta) {
                //         return '<a href="/place/area/edit/' + full.placeId + '/' + full.areaId + '" class="btn btn-sm btn-clean btn-icon btn-icon-md" title="编辑">' +
                //             '<i class="la la-edit"></i>' +
                //             '</a>';
                //     },
                // },
                {
                    targets: 3,
                    render: function (data, type, full, meta) {
                        if (data == 1) {
                            return '<span class="kt-badge kt-badge--inline kt-badge--danger">是</span>';
                        } else {
                            return '<span class="kt-badge kt-badge--inline kt-badge--info">否</span>';
                        }
                    },
                },
            ],
        });
    }

    var initTableCardType = function () {
        var table = $('#kt_table_cardtype');
        var placeId = $('#placeId').val();
        table.DataTable({
            language: {
                "url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            ordering: false, // 字段排序
            paging: false,
            ajax: {
                url: '/place/cardtype/cardtypes/' + placeId,
                type: 'GET',
            },
            columns: [
                {data: 'id'},
                {data: 'cardTypeId'},
                {data: 'typeName'},
                {data: 'level'},
                {data: '操作', responsivePriority: -1},
            ],
            columnDefs: [
                {
                    targets: -1,
                    title: '操作',
                    render: function (data, type, full, meta) {
                        return '<a href="/place/cardtype/edit/' + full.placeId + '/' + full.cardTypeId + '" class="btn btn-sm btn-clean btn-icon btn-icon-md" title="编辑">' +
                            '<i class="la la-edit"></i>' +
                            '</a>';
                    },
                },
            ],
        });
    }

    var initTableTopupRule = function () {
        var table = $('#kt_table_topup_rule');
        var placeId = $('#placeId').val();
        table.DataTable({
            language: {
                "url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            ordering: false, // 字段排序
            paging: false,
            ajax: {
                url: '/place/topupRule/topupRules/' + placeId,
                type: 'GET',
            },
            columns: [
                {data: 'id'},
                {data: 'topupRuleId'},
                {data: 'cardTypeName'},

                {data: 'amount'},
                {data: 'presentAmount'},
                // {data: '操作', responsivePriority: -1},
            ],
            columnDefs: [
                // {
                //     targets: -1,
                //     title: '操作',
                //     render: function (data, type, full, meta) {
                //         return '<a href="/place/topupRule/edit/' + full.placeId + '/' + full.topupRuleId + '" class="btn btn-sm btn-clean btn-icon btn-icon-md" title="编辑">' +
                //             '<i class="la la-edit"></i>' +
                //             '</a>';
                //     },
                // },
                {
                    targets: 4,
                    render: function (data, type, full, meta) {
                        return (data / 100.00) + '元';
                    },
                },
                {
                    targets: 3,
                    render: function (data, type, full, meta) {
                        return (data / 100.00) + '元';
                    },
                },
            ],
        });
    }

    // 初始化 场所-客户端 界面
    function initTableClient() {

        var table = $('#kt_table_client');
        var placeId = $('#placeId').val();
        table.DataTable({
            language: {
                "url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            "fnDrawCallback": function (oSettings) {
                $('.dataTables_filter input').attr({'name': 'search', 'placeholder': '机器名、ip地址'});//提示
            },
            lengthChange: false, // 开启每页设置显示数量
            pageLength: 10, // 设置每页默认显示数量
            searchDelay: 1000, // 搜索框change时的延迟
            ordering: false, // 字段排序
            paging: true,
            destroy: true,
            ajax: {
                url: '/place/client/clients',
                type: 'GET',
                data: {
                    "placeId": placeId,
                    "areaId": areaId,
                }
            },
            columns: [
                {data: 'areaId'},
                {data: 'areaName'},
                {data: 'hostName'},
                {data: 'macAddr'},
                {data: 'ipAddr'},
                {data: 'clientVersion'},
            ],
            columnDefs: [
                {
                    targets: 1,
                    render: function (data, type, row) {
                        var rowAreaId = row.areaId;
                        // 区域id 为空 -> 显示空白
                        if (row.areaId == "") {
                            return '<div>' + "" + '</div>'
                        } else {
                            var returnText = "";
                            // 获得 areaSelect opetion 的所有选项(网吧区域 接口的返回值)
                            var $option = $("#areaSelect option");
                            // 如果 客户端.区域id 与 网吧区域.区域id 相等，则使用 区域id 的名称
                            $option.each(function () {
                                var initAreaId = $(this).val();
                                var initAreaName = $(this).attr("label");
                                if (initAreaId !== "" && initAreaId === rowAreaId) {
                                    returnText = initAreaName;
                                }
                            });
                            return '<div>' + returnText + '</div>'
                        }
                    }
                },
            ],
        });

    }

    // 初始化注册卡区域id
    function initRegCardAreaId() {
        var regcardServerGroupName = $('#regcardServerGroupName').val();
        initRegCardSelect(regcardServerGroupName);
        var currAreaId = $("#areaId").val();
        if (currAreaId !== '' || currAreaId !== null || currAreaId !== undefined) {
            $("#regCardAreaIdSelect").val(currAreaId);
        } else {
            $("#regCardAreaIdSelect").val('');
        }
    }

    // 注册卡服务器下拉框
    $("#regcardServerGroupName").change(function () {
        var regcardServerGroupName = $('#regcardServerGroupName').val();
        initRegCardSelect(regcardServerGroupName);
    });

    //初始化弹框打开的时候，只有场所类型变更为"网租"时才可见“云版本到期时间”选项,其他类型不可见
    var intiCloudVersionExpirationTimeShow = function () {
        var placeType = $("#placeType").val();
        if (placeType == '2') {
            $('#cloudVersionExpirationTimeDiv').attr('hidden', false);
        } else {
            $('#cloudVersionExpirationTimeDiv').attr('hidden', true);
        }
    }

    function initRegCardSelect(regcardServerGroupName) {
        var areaId = $("#regCardAreaIdSelect");
        areaId.empty();
        if (regcardServerGroupName === '') {
            areaId.append("<option value=''>" + "无" + "</option>");
        } else if (regcardServerGroupName === 'quanguo') {
            areaId.append("<option value=''>" + "无" + "</option>");
            // areaId.append("<option value='440300'>" + "深圳(440300)" + "</option>");
            // areaId.append("<option value='410800'>" + "焦作(410800)" + "</option>");
            // areaId.append("<option value='441400'>" + "梅州(441400)" + "</option>");
            // areaId.append("<option value='445100'>" + "潮州(445100)" + "</option>");
            // areaId.append("<option value='520100'>" + "贵阳(520100)" + "</option>");
            // areaId.append("<option value='520200'>" + "六盘水(520200)" + "</option>");
            // areaId.append("<option value='520300'>" + "遵义(520300)" + "</option>");
            // areaId.append("<option value='522400'>" + "毕节(522400)" + "</option>");
            // areaId.append("<option value='341100'>" + "滁州(341100)" + "</option>");
        } else if (regcardServerGroupName === 'shenzhen') {
            areaId.append("<option value=''>" + "请选择" + "</option>");
            areaId.append("<option value=''>" + "仙桃(无地区编码)" + "</option>");
            areaId.append("<option value='440301000001'>" + "深圳龙华(440301000001)" + "</option>");
            areaId.append("<option value='440306000001'>" + "深圳光明(440306000001)" + "</option>");
            areaId.append("<option value='440307000001'>" + "深圳龙岗(440307000001)" + "</option>");
            areaId.append("<option value='440305000001'>" + "深圳宝安(440305000001)" + "</option>");
        } else {
            areaId.append("<option value=''>" + "无" + "</option>");
            // areaId.append("<option value='420200'>" + "黄石(420200)" + "</option>");
            // areaId.append("<option value='420700'>" + "鄂州(420700)" + "</option>");
            // areaId.append("<option value='421100'>" + "黄冈(421100)" + "</option>");
            // areaId.append("<option value='429005'>" + "潜江(429005)" + "</option>");
            // areaId.append("<option value='420300'>" + "十堰(420300)" + "</option>");
            // areaId.append("<option value='421200'>" + "咸宁(421200)" + "</option>");
            // areaId.append("<option value='422800'>" + "恩施(422800)" + "</option>");
            // areaId.append("<option value='420000'>" + "江汉石油(420000)" + "</option>");
            // areaId.append("<option value='429006'>" + "天门(429006)" + "</option>");
            // areaId.append("<option value='421000'>" + "荆州(421000)" + "</option>");
            // areaId.append("<option value='422200'>" + "孝感(422200)" + "</option>");
            // areaId.append("<option value='420500'>" + "宜昌(420500)" + "</option>");
            // areaId.append("<option value='420800'>" + "荆门(420800)" + "</option>");
            // areaId.append("<option value='421300'>" + "随州(421300)" + "</option>");
            // areaId.append("<option value='420600'>" + "襄樊(420600)" + "</option>");
            // areaId.append("<option value='429021'>" + "神农架(429021)" + "</option>");
        }
    }

    // 初始化区域
    function initArea() {
        var placeId = $('#placeId').val();
        $.ajax({
            url: '/place/area/areas/' + placeId,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                var area = $("#areaSelect");
                area.empty();
                area.append("<option value=''>" + "全部" + "</option>");
                $.each(response.data, function (index, item) {
                    var areaName = response.data[index].areaName;
                    var areaId = response.data[index].areaId;
                    $("#areaSelect").append("<option value='" + areaId + "' label='" + areaName + "'></option>");
                });

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {

            }
        });
    }

    // 会员导入
    var handleImportSubmit = function () {

        var importForm = $('#memberImportForm');

        importForm.validate({
            // define validation rules
            rules: {
                placeId: {
                    required: true,
                },
                file: {
                    required: true,
                    extensionempty: "xlsx|xls",
                    fileMaxSize: 15
                }
            },
        });

        $('#kt_submit').click(function (e) {
            e.preventDefault();
            var btn = $(this);

            if (!importForm.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);
            scrollTo(0, 0);

            swal.fire({
                title: "正在导入......",
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });

            importForm.ajaxSubmit({
                url: '/place/import/memberImport',
                type: 'POST',
                success: function (response) {
                    swal.close();
                    $("input[name='file']")[0].value = '';
                    if (response == 'BAD_PARAM') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '参数错误');
                    } else if (response == 'DUPLICATION_DATA_IN_EXCEL') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '表格中数据重复');
                    } else if (response == 'DUPLICATION_DATA_WITH_DB') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '和已有数据重复');
                    } else if (response == 'EMPTY_DATA') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '导入数据为空');
                    } else if (response == 'SUCCESS') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'success', '操作成功');
                        setTimeout(function () {
                            history.back(-1);
                        }, 2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', response);
                    }
                },
                error: function (response, status) {
                    swal.close();
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(importForm, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

    // 注册卡导入
    var handleRegcardImportSubmit = function () {

        var importForm = $('#regcardImportForm');

        importForm.validate({
            // define validation rules
            rules: {
                auditId: {
                    required: true,
                },
                file: {
                    required: true,
                    extensionempty: "xlsx|xls",
                    fileMaxSize: 5
                }
            },
        });

        $('#kt_submit_1').click(function (e) {
            e.preventDefault();
            var btn = $(this);

            if (!importForm.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);
            scrollTo(0, 0);

            swal.fire({
                title: "正在导入......",
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });

            importForm.ajaxSubmit({
                url: '/place/import/regcardImport',
                type: 'POST',
                success: function (response) {
                    swal.close();
                    $("input[name='file']")[0].value = '';
                    if (response == 'BAD_PARAM') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '参数错误');
                    } else if (response == 'EMPTY_DATA') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '导入数据为空');
                    } else if (response == 'SUCCESS') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'success', '操作成功');
                        setTimeout(function () {
                            history.back(-1);
                        }, 2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', response);
                    }
                },
                error: function (response, status) {
                    swal.close();
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(importForm, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

    // pubw会员更新导入
    var handleWanXiangImportSubmit = function () {

        var importForm = $('#wanxiangUpdateImportForm');

        importForm.validate({
            // define validation rules
            rules: {
                placeId: {
                    required: true,
                },
                file: {
                    required: true,
                    extensionempty: "xlsx|xls",
                    fileMaxSize: 15
                }
            },
        });

        $('#kt_submit_2').click(function (e) {
            e.preventDefault();
            var btn = $(this);

            if (!importForm.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);
            scrollTo(0, 0);

            swal.fire({
                title: "正在导入......",
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });

            importForm.ajaxSubmit({
                url: '/place/import/importMemberUpdateAccount',
                type: 'POST',
                success: function (response) {
                    swal.close();
                    $("input[name='file']")[0].value = '';
                    if (response == 'BAD_PARAM') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '参数错误');
                    } else if (response == 'DUPLICATION_DATA_IN_EXCEL') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '表格中数据重复');
                    } else if (response == 'DUPLICATION_DATA_WITH_DB') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '和已有数据重复');
                    } else if (response == 'EMPTY_DATA') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '导入数据为空');
                    } else if (response == 'SUCCESS') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'success', '操作成功');
                        setTimeout(function () {
                            history.back(-1);
                        }, 2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', response);
                    }
                },
                error: function (response, status) {
                    swal.close();
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(importForm, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

    // pubw会员新增导入
    var handleWanXiangAddImportSubmit = function () {

        var importForm = $('#wanxiangAddImportForm');

        importForm.validate({
            // define validation rules
            rules: {
                placeId: {
                    required: true,
                },
                file: {
                    required: true,
                    extensionempty: "xlsx|xls",
                    fileMaxSize: 15
                }
            },
        });

        $('#kt_submit_3').click(function (e) {
            e.preventDefault();
            var btn = $(this);

            if (!importForm.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);
            scrollTo(0, 0);

            swal.fire({
                title: "正在导入......",
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });

            importForm.ajaxSubmit({
                url: '/place/import/importWanXiangAdd',
                type: 'POST',
                success: function (response) {
                    swal.close();
                    $("input[name='file']")[0].value = '';
                    if (response == 'BAD_PARAM') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '参数错误');
                    } else if (response == 'DUPLICATION_DATA_IN_EXCEL') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '表格中数据重复');
                    } else if (response == 'DUPLICATION_DATA_WITH_DB') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '和已有数据重复');
                    } else if (response == 'EMPTY_DATA') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', '导入数据为空');
                    } else if (response == 'SUCCESS') {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'success', '操作成功');
                        setTimeout(function () {
                            history.back(-1);
                        }, 2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'warning', response);
                    }
                },
                error: function (response, status) {
                    swal.close();
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(importForm, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

        // 后台确认交接班
        var handleSubmitPlaceShift = function () {

            var importForm = $('#submitPlaceShiftForm');

            importForm.validate({
                // define validation rules
                rules: {
                    placeId: {
                        required: true,
                    },
                    cashierId: {
                        required: true,
                    },
                    successorAccountName: {
                        required: true,
                    },
                    password: {
                        required: true,
                    },
                },
            });

            $('#kt_submit_place_shift').click(function (e) {
                e.preventDefault();
                var btn = $(this);

                if (!importForm.valid()) {
                    return;
                }

                btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);
                scrollTo(0, 0);

                importForm.ajaxSubmit({
                    url: '/place/shift/submit',
                    type: 'POST',
                    beforeSubmit: function(arr, $form, options) {
                            // 打印表单字段
                            console.log(arr);

                            // 修改某个字段的值
                            for (let i = 0; i < arr.length; i++) {
                                if (arr[i].name === 'nextShiftHandoverCashStr' && arr[i].value != null && arr[i].value.trim() !== "") {
                                    // 修改值，将元换算成分。防止精度丢失，保留一位小数是为了防止用户位数输入过多
                                    arr[i].value=parseFloat((arr[i].value*100).toFixed(1));
                                    //如果还存在小数部分，阻止提交表单
                                    if (!Number.isInteger(arr[i].value)) {
                                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                                        showErrorMsg(importForm, 'warning', '金额不正确');
                                        return false;
                                    }
                                }
                            }

                            // 返回 false 可以阻止表单提交
                            return true;
                        },
                    success: function (response) {
                        if (response == 'BAD_PARAM') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', '参数错误');
                        } else if (response == 'WORKING_SHIFT_NOT_FOUND') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', '班次不存在');
                        } else if (response == 'GET_REQUEST_BODY_ERROR') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', '参数存在问题');
                        } else if (response == 'ACCOUNT_NOT_FOUND') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', '当班人不存在');
                        } else if (response == 'SUCCESSOR_ACCOUNT_NOT_FOUND') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', '交班人不存在');
                        }else if (response == 'SUCCESS') {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'success', '操作成功');
                            setTimeout(function () {
                                history.back(-1);
                            }, 2000);
                        } else {
                            btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                            showErrorMsg(importForm, 'warning', response);
                        }
                    },
                    error: function (response, status) {
                        swal.close();
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(importForm, 'danger', '系统繁忙，请稍后再试');
                    }
                });
            });
        }

    let $kt_tabs_account = $('#kt_tabs_account');
    let $kt_table_account = $('#kt_table_account');
    let resetPasswordFun = function () {
        $kt_tabs_account.on('click', '#resetPassword', function (e) {
            e.preventDefault();
            // console.log('resetPassword');

            // console.log(e.currentTarget)

            let accountId = $(e.currentTarget).data('account-id')
            let placeId = $(e.currentTarget).data('place-id')
            console.log(accountId)
            console.log(placeId)

            Swal.fire({
                title: '确定要重置密码吗?',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                reverseButtons: true
            }).then((result) => {
                console.log('result', result.value)
                if (result.value) {
                    resetPasswordReq(placeId, accountId)
                }
            })

        })
    }

    let resetPasswordReq = function (placeId, accountId) {

        axios({
            method: 'post',
            url: '/place/account/resetPwd',
            params: {
                placeId: placeId,
                accountId: accountId
            }
        })
            .then(function (response) {
                // console.log(response)
                if (response.status === 200) {
                    let data = response.data;
                    if (data.result) {
                        swal.fire({
                            title: '操作成功',
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            allowEnterKey: false,
                        })
                        setTimeout(function () {
                            // $kt_table_accountDT.draw();
                            swal.close();
                        }, 1000);
                    } else {
                        swal.fire({
                            title: data.message,
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            allowEnterKey: false,
                        })
                        setTimeout(function () {
                            // $kt_table_accountDT.draw();
                            swal.close();
                        }, 2000);
                    }

                } else {
                    console.warn(response)
                }
            })
            .catch(function (error) {
                console.warn(error)
            })
    }

    return {
        // public functions
        init: function () {
            datepicker();
            initTouchspin();
            initTouchspinCashier();
            initCascadeSelect();
            initSwitch();

            antiAddictionChange()

            handleProfileFormSubmit();
            handleConfigFormSubmit();
            exportClientList();
            initTableAccount();
            initTableArea();
            initTableCardType();
            //	initTableBillingRule();
            initTableTopupRule();
            initArea();
            initTableClient();
            handleImportSubmit();
            handleRegcardImportSubmit();
            initRegCardAreaId();
            intiCloudVersionExpirationTimeShow();
            resetPasswordFun();

            handleWanXiangAddImportSubmit();
            handleWanXiangImportSubmit();
            
            clientTypesEventMonitor();

            handleSubmitPlaceShift();
        }
    }
}();

function initProvince() {
    $.ajax({
        url: '/region/provinces',
        type: 'GET',
        dataType: 'json',
        success: function (response) {
            $("#provinceSelect").empty();
            $("#provinceSelect").append("<option value=''>请选择</option>");
            $.each(response, function (index, item) {
                var name = response[index].name;
                var provinceId = response[index].provinceId;
                $("#provinceSelect").append("<option value='" + provinceId + "'>" + name + "</option");
            });
            if ($("#id").val() != 0) {
                $("#provinceSelect").val($("#regionCode").val().substring(0, 2) + "0000000000");
                initCity($("#provinceSelect").val());
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error(errorThrown);
            console.error(XMLHttpRequest);
            console.error(textStatus);
        }
    });
}

function initCity(provinceId) {
    $('#code').val(provinceId);
    $.ajax({
        url: '/region/cities/' + provinceId,
        type: 'GET',
        dataType: 'json',
        success: function (response) {
            $("#citySelect").empty();
            $("#citySelect").append("<option value=''>请选择</option>");
            $.each(response, function (index, item) {
                var name = response[index].name;
                var cityId = response[index].cityId;
                $("#citySelect").append("<option value='" + cityId + "'>" + name + "</option");
            });
            if ($("#id").val() != 0) {
                $("#citySelect").val($("#regionCode").val().substring(0, 4) + "00000000");
                initCountry($("#citySelect").val())
            } else {
                $("#address").val($("#provinceSelect option:selected").text());
            }
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            //$("#townSelect").empty();
            //$("#townSelect").append("<option value=''>请选择</option>");

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error(errorThrown);
            console.error(XMLHttpRequest);
            console.error(textStatus);
        }
    });
}

function initCountry(cityId) {
    $('#code').val(cityId);
    $.ajax({
        url: '/region/countries/' + cityId,
        type: 'GET',
        dataType: 'json',
        success: function (response) {
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            $.each(response, function (index, item) {
                var name = response[index].name;
                var countryId = response[index].countryId;
                $("#countrySelect").append("<option value='" + countryId + "'>" + name + "</option");
            });
            if ($("#id").val() != 0) {
                var str = $("#regionCode").val();
                $("#countrySelect").val(str);
                $('#code').val(str);
                //initTown($("#countrySelect").val());
            } else {
                $("#address").val($("#provinceSelect option:selected").text() + $("#citySelect option:selected").text());
                //$("#townSelect").empty();
                //$("#townSelect").append("<option value=''>请选择</option>");
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.error(errorThrown);
            console.error(XMLHttpRequest);
            console.error(textStatus);
        }
    });
}


jQuery(document).ready(function () {
    $("#placeType").on('change', function (e) {
        let profileType = e.delegateTarget.value;
        if (profileType === '2') {
            $('#cloudVersionExpirationTimeDiv').attr('hidden', false);
        } else {
            $('#cloudVersionExpirationTimeDiv').attr('hidden', true);
        }
    });

    jQuery.validator.addMethod("mobile", function (value, element) {
        var length = value.length;
        var mobile = /^1[3|4|5|6|7|8|9]{1}[0-9]{9}$/;
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请输入手机号码");

    jQuery.validator.addMethod("nameVerification", function (value, element) {
        var name = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/;
        return this.optional(element) || (name.test(value));
    }, "请输入正确的姓名格式!由中文、英文、数字组成");

    $.validator.addMethod("extensionempty", function (value, element, param) {
        param = typeof param === "string" ? param.replace(/,/g, "|") : "png|jpe?g|gif";
        return this.optional(element) || value.match(new RegExp("\\.(" + param + ")$", "i")) || value.indexOf('.') == -1;
    }, $.validator.format("文件类型不符合要求"));

    $.validator.addMethod("fileMaxSize", function (value, element, param) {
        var file = element.files[0];
        var fileMaxSize = param * 1024 * 1024;
        return this.optional(element) || !(file.size > fileMaxSize);
    }, $.validator.format("文件最大不超过15M"));

    KTFormControls.init();
    $('.dropdown-menu').on('click', function(e) {
        if($(this).hasClass('dropdown-menu')) {
            e.stopPropagation();
        }
    });

});