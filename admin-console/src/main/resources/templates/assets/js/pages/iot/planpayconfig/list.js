"use strict";

var KTDatatablesDataSourceAjaxServer = function() {

	var showErrorMsg = function(form, type, msg) {
		var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">'+msg+'</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

		form.find('.alert').remove();
		alert.prependTo(form);
		//alert.animateClass('fadeIn animated');
		KTUtil.animateClass(alert[0], 'fadeIn animated');
		alert.find('span').html(msg);
	}


	var initTable1 = function() {
		var table = $('#kt_table_1');

		var planName = $('#planName').val();
		var faceclientType = $('#faceclientTypeSelect').val();
		var conftype = $('#conftypeSelect').val();
		var planFeeType = $('#planFeeTypeSelect').val();
		var placeType = $('#placeTypeSelect').val();
		var enable = $('#enableSelect').val();
		var planFeeConvertFlag = $('#planFeeConvertFlagSelect').val();

		// begin first table
		table.DataTable({
			language: {
				"url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
			},
			serverSide: true, // 开启服务器模式，必须
			responsive: true, // 启用和配置响应式
			processing: true, // 是否显示加载中提示
			searching: false, // 开启搜索框
			// "fnDrawCallback": function(oSettings) {
			// 	$('.dataTables_filter input').attr({ 'name': 'search', 'placeholder': '场所编码' });// 搜索提示
			// },
			lengthChange: false, // 开启每页设置显示数量
			ordering: false, // 字段排序
			paging: true,
			destroy: true,
			ajax: {
				url: '/iot/planpayconfig/getPlanPayConfigs',
				type:'GET',
				data: {
					"planName": planName,
					"faceclientType": faceclientType,
					"conftype": conftype,
					"planFeeType": planFeeType,
					"placeType": placeType,
					"enable": enable,
					"planFeeConvertFlag": planFeeConvertFlag
				},
			},
			columns: [
				{data: 'id'},
				{data: 'planName'},
				{data: 'placeType'},
				{data: 'faceclientTypesStr'}, // 
				{data: 'conftype' },
				{data: 'planFeeType' },//
				{data: 'planFee' },
				{data: 'feeOfGift' },
				{data: 'effectiveMinutes' },
				{data: 'updated' },
				{data: 'enable' },
				{data: 'operateUser' },
				{data: 'tipsDesc', "visible": false},
				{data: '操作', responsivePriority: -1 }
			],
			columnDefs: [
				{
					targets: 3, // 
					render: function(data, type, full, meta) {
						return convertFaceclientTypes(data);
					}
				},
				{
					targets: 4, // 
					render: function(data, type, full, meta) {
						if(data === 'PROV'){
							return "省级";
						}else if(data === 'CITY'){
							return "市级";
						}else if(data === 'COUNTRY'){
							return "区级";
						}else if(data === 'BAR'){
							return "单店 ";
						}
					}
				},
				{
					targets: 5,
					render: function(data, type, full, meta) {
						if (data === 'EveryTime') {
							return '次卡';
						} else if (data === 'ByDay') {
							return '日卡';
						} else if (data === 'ByWeek') {
							return '周卡';
						} else if (data === 'ByMonth') {
							return '月卡';
						} else if (data === 'ByQuarter') {
							return '季卡';
						} else if (data === 'ByYear') {
							return '年卡';
						}
					}
				},
				{
					targets: 6,
					render: function(data, type, full, meta) {
						return (data / 100.00) + '元';
					}
				},
				{
					targets: 7,
					render: function(data, type, full, meta) {
						return (data / 100.00) + '元';
					}
				},
				{
					targets: -1,
					title: '操作',
					render: function(data, type, full, meta) {
						var connectModalId; 
						if(full.conftype === 'BAR'){
							connectModalId = 'connectionBarModal';
						}else{
							connectModalId = 'connectionAreaModal';
						}
						var rowData = encodeURIComponent(JSON.stringify(full));
						return '<button type="button" class="btn btn-sm btn-primary" ' +
							'data-toggle="modal" data-target="#'+ connectModalId +'" data-value="' + rowData + '" data-description="' +full.conftype+ '"  title="关联场所">' +
							'关联场所</button>' +
							'<span>  </span>' +
							'<button type="button" class="btn btn-sm btn-primary" ' +
							'data-toggle="modal" data-target="#editConfModal" data-value="' + rowData + '" data-description="' +full.conftype+ '"  title="编辑套餐">' +
							'编辑套餐</button>';
					}
				},
				{
					targets: -4,
					render: function(data, type, full, meta) {
						if (data === 0) {
							return '<span class="kt-badge kt-badge--inline kt-badge--success">启用</span>';
						} else {
							return '<span class="kt-badge kt-badge--inline kt-badge--danger">停用</span>';
						}
					}
				},
				{
					targets: -5,
					render: function(data, type, full, meta) {
						var datetime;
						if (data.length < 6) {
							datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], 0]);
						} else {
							datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], data[5]]);
						}
						return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
					}

				},
				{
					targets: -6,
					render: function(data, type, full, meta) {
						var days = 0;
						if (data >= 1440) {
							if (data >= 1440) {
								days = data / 1440;
							}
							var mins = data % 1440;
							var hours = 0;
							if (mins > 0) {
								if (mins >= 60) {
									hours = mins / 60;
								}
								mins = mins % 60;
							}
							return days.toFixed() + '天' + hours.toFixed() + '小时' + mins.toFixed() + '分钟';
						} else {
							var hours = 0;
							var mins = data % 1440;
							if (mins > 0) {
								if (mins >= 60) {
									hours = mins / 60;
								}
								mins = mins % 60;
							}
							return hours.toFixed() + '小时' + mins.toFixed() + '分钟';
						}
					}

				},
				{
					targets: 2,
					render: function(data, type, full, meta) {
						if (data === 0) {
							return '<span class="kt-font-brand kt-font-bold">' + "网吧" + '</span>';
						} else if (data === 1) {
							return '<span class="kt-font-brand kt-font-bold">' + "电竞酒店" + '</span>';
						} else if (data === 2) {
							return '<span class="kt-font-brand kt-font-bold">' + "网租" + '</span>';
						} else if (data === 3) {
							return '<span class="kt-font-brand kt-font-bold">' + "普通场所" + '</span>';
						} else if (data === 4) {
							return '<span class="kt-font-brand kt-font-bold">' + "营销大师" + '</span>';
						} else if (data === 5) {
							return '<span class="kt-font-brand kt-font-bold">' + "九威" + '</span>';
						} else if (data === 6) {
							return '<span class="kt-font-brand kt-font-bold">' + "大巴掌" + '</span>';
						} else if (data === 301) {
							return '<span class="kt-font-brand kt-font-bold">' + "万象" + '</span>';
						} else if (data === 302) {
							return '<span class="kt-font-brand kt-font-bold">' + "嘟嘟牛" + '</span>';
						} else if (data === 303) {
							return '<span class="kt-font-brand kt-font-bold">' + "轻网联盟" + '</span>';
						} else if (data === 7) {
							return '<span class="kt-font-brand kt-font-bold">' + "PMS" + '</span>';
						} else if (data === 8) {
							return '<span class="kt-font-brand kt-font-bold">' + "龙管家" + '</span>';
						} else if (data === -1) {
							return '<span class="kt-font-brand kt-font-bold">' + "全部" + '</span>';
						} else if (data === 304) {
							return '<span class="kt-font-brand kt-font-bold">' + "佳星" + '</span>';
						} else if (data === 305) {
							return '<span class="kt-font-brand kt-font-bold">' + "百果树" + '</span>';
						} else if (data === 306) {
							return '<span class="kt-font-brand kt-font-bold">' + "奥比特" + '</span>';
						} else if (data === 307) {
							return '<span class="kt-font-brand kt-font-bold">' + "丕微" + '</span>';
						}else {
							return "<span>未知</span>";
						}
					}
				}
			]
		});
	};

    function convertFaceclientTypes(data){
	    var cltypes = data.split(",");
		var showValues="";
		var singelValue="";
		var csize = cltypes.length;
		for(var i=0; i < csize; i++){
		    var cltype = parseInt(cltypes[i]);
		    if(cltype == 1){
		    	singelValue = "四维新版公众号";
		    }else if(cltype == 2){
		    	singelValue = "四维小程序";
		    }else if(cltype == 3){
		    	singelValue = "V8小程序";
		    }else if(cltype == 4){
		    	singelValue = "IOT自助机";
		    }else if(cltype == 5){
		    	singelValue = "V8 公众号";
		    }else if(cltype == 6){
		    	singelValue = "支付宝小程序";
		    }else{
		    	singelValue = "全部";
		    }
		    if(i<csize-1){
		        showValues += singelValue + "/";
		    }else{
		        showValues += singelValue;
		    } 
		}
		return showValues;
    }

	function connectionBars_table(planconfigId) {
		var table = $('#connectionBars_table');

		// begin first table
		table.DataTable({
			language: {
				"url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
			},
			serverSide: true, // 开启服务器模式，必须
			responsive: true, // 启用和配置响应式
			processing: true, // 是否显示加载中提示
			searching: false, // 开启搜索框
			// "fnDrawCallback": function(oSettings) {
			// 	$('.dataTables_filter input').attr({ 'name': 'search', 'placeholder': '场所编码' });// 搜索提示
			// },
			lengthChange: false, // 开启每页设置显示数量
			ordering: false, // 字段排序
			paging: true,
			destroy: true,
			autoWidth: true,
			pageLength: 5,
			ajax: {
				url: '/iot/planpayconfigconection/getPlanPayConfigConections4Page',
				type:'GET',
				data: {
					"planconfigId": planconfigId,
					"conftype": "BAR"
				},
			},
			columns: [
				{data: 'connectionId'},
				{data: 'barName'},
				{data: 'areaName'},
				{data: 'created'}, // 
				{data: '操作', responsivePriority: -1 }
			],
			columnDefs: [
				{
					targets: -1,
					title: '操作',
					render: function(data, type, full, meta) {
						var rowData = JSON.stringify(full);
						return '<button type="button" class="btn btn-sm btn-primary" ' +
							'data-toggle="modal" data-target="#deleteModalCenter" data-value=' + rowData + ' data-description=' +full.conftype+ '  title="删除关联">' +
							'删除</button>';
					}
				},
				{
					targets: -2,
					render: function(data, type, full, meta) {
						var datetime;
						if (data.length < 6) {
							datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], 0]);
						} else {
							datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], data[5]]);
						}
						return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
					}

				}
			]
		});
		
	};

	// 页面查询按钮
	$('#search').on('click', function(e) {
		e.preventDefault();
		initTable1();
	});

	// 页面重置按钮
	$('#reset').on('click', function(e) {
		e.preventDefault();
		$('#planName').val("");
		$('#faceclientTypeSelect').val("");
		$('#conftypeSelect').val("");
		$('#planFeeTypeSelect').val("");
		$('#placeTypeSelect').val("");
		$('#enableSelect').val("");
		$('#planFeeConvertFlagSelect').val("");
	});

	// edit套餐modal 
	function initEditConfModal() {
		$('#editConfModal').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowDataValue = button.data('value');
			var faceclientTypes = $('#faceclientTypeSelect4Add').find(':checkbox');
			faceclientTypes.prop('checked', false);
			
			if(rowDataValue != undefined){
			    // 初始化人脸终端类型多选控件
                var rowData = JSON.parse(decodeURIComponent(rowDataValue));
			    var clienttypes = rowData.faceclientTypesStr.split(",");
			    for(var i=0; i<clienttypes.length; i++){
			    	faceclientTypes.each(function() {
                        if($(this).val() === clienttypes[i]){
	                       $(this).prop('checked', true);
	                       return;
                        }
                    });
			    }
			    // 监听多选控件
                faceclientTypesEventMonitor();

				// 修改
                $('#planConfigId4Add').val(rowData.id);
                $('#planName4Add').val(rowData.planName);

		        // $('#faceclientTypeSelect4Add').val(rowData.faceclientType);
		        $('#conftypeSelect4Add').val(rowData.conftype);
                $('#conftypeSelect4Add').prop('disabled', true);

		        $('#planFeeTypeSelect4Add').val(rowData.planFeeType);
		        $('#placeTypeSelect4Add').val(rowData.placeType);
		        $('#enableSelect4Add').val(rowData.enable);
		        $('#effectiveMinutes4Add').val(rowData.effectiveMinutes);
			    $('#planFee4Add').data('actual-value', rowData.planFee);
                $('#feeOfGift4Add').data('actual-value', rowData.feeOfGift);
                $('#planFee4Add').val(rowData.planFee/100.00);
                $('#feeOfGift4Add').val(rowData.feeOfGift/100.00);
                $('#tipsDesc4Add').val(rowData.tipsDesc);
                $("#addConfigForm").valid();

			}else{
				faceclientTypesEventMonitor();
				
				// add
				$('#conftypeSelect4Add').prop('disabled', false);
				$('#enableSelect4Add').val("0");
				$('#planConfigId4Add').val("");
                $('#planName4Add').val("");
		        //$('#faceclientTypeSelect4Add').val("0");

		        $('#conftypeSelect4Add').val("BAR");
		        $('#planFeeTypeSelect4Add').val("EveryTime");
		        $('#placeTypeSelect4Add').val("0");
		        $('#effectiveMinutes4Add').val("");
                $('#planFee4Add').val("");
                $('#feeOfGift4Add').val("");
                $('#tipsDesc4Add').val("");
			}
		});
	}
	
	function faceclientTypesEventMonitor(){
		var faceclientTypes = $('#faceclientTypeSelect4Add').find(':checkbox');
		faceclientTypes.click(function(){
			var checkedSize=$('#faceclientTypeSelect4Add').find(':checkbox:checked').length;
			if (checkedSize == 6) {
				$('#faceclienttype4All').prop('disabled', true);
            } else if(checkedSize == 0) {
	            $('#faceclienttype4All').prop('disabled', false);
            }
		});
		
	    $('#faceclienttype4All').change(function() {  
            if ($(this).is(':checked')) {  
                faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', true);
                     $(this).prop('checked', false);
                  }
                });
            } else {
                faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', false);
                  }
                });
            }
        });
        if($('#faceclienttype4All').is(':checked')){
			faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', true);
                  }
            });
		}else{
			faceclientTypes.each(function() {
                  if($(this).val() != 0){
	                 $(this).prop('disabled', false);
                  }
            });
		}
	}
	
	function addConfig(){
		$('#addConfigForm').validate({
	        	// define validation rules
	            rules: {
	            	planName4Add: {
		                required: true
	                },
	                planFee4Add: {
	                	required: true,
	                	number:true,
	                	min: 0.01,
                        max: 9999,
                        maxlength: 4
	                },
	                effectiveMinutes4Add: {
	                	required: true,
	                	digits: true,
	                	min: 1,
                        max: 999999
	                },
	                feeOfGift4Add: {
	                	required: true,
	                	number: true,
	                	min: 0,
                        max: 9999,
                        maxlength: 4
	                },
                    tipsDesc4Add: {
		                required: false,
                        maxlength: 200
	                },
	            }
	    });

        $("#planFee4Add").on("input", function(){
	        $('#planFee4Add').data('value-modify-flag',1);
        });
        $("#feeOfGift4Add").on("input", function(){
	        $('#feeOfGift4Add').data('value-modify-flag',1);
        });
        $("#planFee4Add").blur(function() {
	        var modifyFlag = $('#planFee4Add').data('value-modify-flag');
            if(modifyFlag!=1){
	           return;
            }
	        var srcValue = $('#planFee4Add').val();
	        $('#planFee4Add').data('actual-value', parseInt(srcValue*100));
			$('#planFee4Add').data('value-modify-flag',0);
		});
		$("#feeOfGift4Add").blur(function() {
			var modifyFlag = $('#feeOfGift4Add').data('value-modify-flag');
            if(modifyFlag!=1){
	           return;
            }
	        var srcValue = $('#feeOfGift4Add').val();
			$('#feeOfGift4Add').data('actual-value', parseInt(srcValue*100));
			$('#feeOfGift4Add').data('value-modify-flag',0);
		});
		
		
		$("#doAddConf").click(function () {
			var btn = $(this);
			var form = $('#authConfigForm');
			
			var addConfigForm = $('#addConfigForm');
			var id4Add = $('#planConfigId4Add').val();
			var	enableSelect4Add = $('#enableSelect4Add').val();
			var	planName4Add = $('#planName4Add').val();
			var faceclientTypesChecked = $('#faceclientTypeSelect4Add').find(':checkbox:checked');
            var	faceclientTypeSelect4Add="";
            faceclientTypesChecked.each(function() {  
                // 你可以在这里处理每个被勾选的checkbox  
                // 例如，打印出它们的值  
                faceclientTypeSelect4Add += $(this).val() + ",";  
                console.log($(this).val());  
            });
            if(faceclientTypeSelect4Add == undefined || faceclientTypeSelect4Add.trim() === ''){
	           alert("请选择扫脸终端类型");
               return;
            }
            faceclientTypeSelect4Add = faceclientTypeSelect4Add.substring(0, faceclientTypeSelect4Add.length-1);
            console.log(faceclientTypeSelect4Add);
			//var	faceclientTypeSelect4Add = $('#faceclientTypeSelect4Add').val();
			
			var	conftypeSelect4Add = $('#conftypeSelect4Add').val();
			var	planFeeTypeSelect4Add = $('#planFeeTypeSelect4Add').val();
			var	effectiveMinutes4Add = $('#effectiveMinutes4Add').val();
			//var	planFee4Add = $('#planFee4Add').val();
			//var	feeOfGift4Add = $('#feeOfGift4Add').val();
			var	planFee4Add = $('#planFee4Add').data('actual-value');
			var	feeOfGift4Add = $('#feeOfGift4Add').data('actual-value');
			var	placeTypeSelect4Add = $('#placeTypeSelect4Add').val();
			var tipsDesc4Add = $('#tipsDesc4Add').val();

	        if (!$("#addConfigForm").valid()) {
                return;
            }

			addConfigForm.ajaxSubmit({
                url: '/iot/planpayconfig/edit',
                type: 'POST',
				data: {
					"id": id4Add,
					"enable": enableSelect4Add,
					"planName": planName4Add,
					"faceclientTypesStr": faceclientTypeSelect4Add,
					"conftype": conftypeSelect4Add,
					"planFeeType": planFeeTypeSelect4Add,
					"effectiveMinutes": effectiveMinutes4Add,
					"planFee": planFee4Add,
					"feeOfGift": feeOfGift4Add,
					"placeType": placeTypeSelect4Add,
					"tipsDesc": tipsDesc4Add
				},
                success: function(response) {
	                $('#editConfModal').modal("hide");
                	scrollTo(0,0);
                	if(response.result){
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'success', response.message);
                        initTable1();// 刷新页面
                	}else{
                		btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                		showErrorMsg(form, 'warning', response.message);
                	}
                	
                },
                error: function(response, status){
                	btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                	showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
            
			
		});
	}
	
    $('#addConfigBtn').on('click', function(e) {
		e.preventDefault();
		$('#editConfModal').modal("show");
	});
	
	$('#querySingleBarPlaBtn').on('click', function(e) {
		e.preventDefault();
		$('#barConnectPlanConfsModal').modal("show");
	});
	
	$('#confSysParamBtn').on('click', function(e) {
		e.preventDefault();
		$('#sysParamsConfModal').modal("show");
	});
	
	// 关联网吧
	function initConnectionBarModal() {
		var planConfId; 
		$('#connectionBarModal').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowDataValue = button.data('value');
			if(rowDataValue != undefined){
				//
				var rowData = JSON.parse(decodeURIComponent(rowDataValue));
				planConfId=rowData.id;
				$('#baridsText').val("");
                // init connectionBars_table  
                $('#connectionBarForm').find('.alert').remove();
                connectionBars_table(planConfId);
			}
		});
		
	    // 批量关联网吧
	    $('#connectBar4Batch').on('click', function(e) {
	    	e.preventDefault();
            var baridsText=$('#baridsText').val().trim();
            if(baridsText===''){
	            alert("请先输入网吧编码");
                return; 
            }
            
            var barIds=baridsText.replaceAll("\n",",");
            var btn = $(this);
			var form = $('#connectionBarForm');
			$.ajax({
				url: '/iot/planpayconfigconection/addList',
				type: 'POST',
				data: {"barIds": barIds,"id": planConfId},
				success: function(response) {
					console.log(response);
					if(response.result){
						$('#baridsText').val("");
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
						setTimeout(function(){ connectionBars_table(planConfId); }, 2000);
						// var table = $('#kt_table_1').DataTable();
						// table.draw( false );
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });
	    // 批量删除网吧
	    $('#delBar4Batch').on('click', function(e) {
	    	e.preventDefault();
	    	var baridsText=$('#baridsText').val().trim();
            if(baridsText===''){
	            alert("请先输入网吧编码");
                return; 
            }
            
            var barIds=baridsText.replaceAll("\n",",");
            var btn = $(this);
			var form = $('#connectionBarForm');
			$.ajax({
				url: '/iot/planpayconfigconection/delete',
				type: 'POST',
				data: {"barIds": barIds,"id": planConfId},
				success: function(response) {
					console.log(response);
					if(response.result){
						$('#baridsText').val("");
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
						setTimeout(function(){ connectionBars_table(planConfId); }, 2000);
						// var table = $('#kt_table_1').DataTable();
						// table.draw( false );
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });

        var barId;
		$('#deleteModalCenter').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowData = button.data('value');
			if(rowData != undefined){
				barId=rowData.connectionId;
			}
		});
	    $('#doDelete4Bar').on('click', function(e) {
	    	e.preventDefault();
            var btn = $(this);
			var form = $('#connectionBarForm');
			$.ajax({
				url: '/iot/planpayconfigconection/delete',
				type: 'POST',
				data: {"barIds": barId,"id": planConfId},
				success: function(response) {
					console.log(response);
					if(response.result){
						$('#baridsText').val("");
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
						setTimeout(function(){ connectionBars_table(planConfId); }, 2000);
						// var table = $('#kt_table_1').DataTable();
						// table.draw( false );
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });
	}	

    var barid4mz; 
	function connectionConfs_table() {
		var table = $('#connectionConfs_table');

        barid4mz = $('#barid4mz').val();
        var faceclientType = $('#faceclientTypeSelect4mz').val();
		// begin first table
		table.DataTable({
			language: {
				"url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
			},
			serverSide: true, // 开启服务器模式，必须
			responsive: true, // 启用和配置响应式
			processing: true, // 是否显示加载中提示
			searching: false, // 开启搜索框
			// "fnDrawCallback": function(oSettings) {
			// 	$('.dataTables_filter input').attr({ 'name': 'search', 'placeholder': '场所编码' });// 搜索提示
			// },
			lengthChange: false, // 开启每页设置显示数量
			ordering: false, // 字段排序
			paging: true,
			destroy: true,
			pageLength: 5,
			ajax: {
				url: '/iot/planpayconfig/getActiveConfigsBySingelBar',
				type:'GET',
				data: {
					"barid": barid4mz,
					"faceclientType": faceclientType
				},
			},
			columns: [
				{data: 'id'},
				{data: 'planName'},
				{data: 'placeType'},
				{data: 'faceclientTypesStr'}, // 
				{data: 'conftype' },
				{data: 'planFeeType' },//
				{data: 'planFee' },
				{data: 'feeOfGift' },
				{data: 'effectiveMinutes' },
				{data: '操作', responsivePriority: -1 }
			],
			columnDefs: [
				{
					targets: 2,
					render: function(data, type, full, meta) {
						if (data === 0) {
							return '<span class="kt-font-brand kt-font-bold">' + "网吧" + '</span>';
						} else if (data === 1) {
							return '<span class="kt-font-brand kt-font-bold">' + "电竞酒店" + '</span>';
						} else if (data === 2) {
							return '<span class="kt-font-brand kt-font-bold">' + "网租" + '</span>';
						} else if (data === 3) {
							return '<span class="kt-font-brand kt-font-bold">' + "普通场所" + '</span>';
						} else if (data === 4) {
							return '<span class="kt-font-brand kt-font-bold">' + "营销大师" + '</span>';
						} else if (data === 5) {
							return '<span class="kt-font-brand kt-font-bold">' + "九威" + '</span>';
						} else if (data === 6) {
							return '<span class="kt-font-brand kt-font-bold">' + "大巴掌" + '</span>';
						} else if (data === 301) {
							return '<span class="kt-font-brand kt-font-bold">' + "万象" + '</span>';
						} else if (data === 302) {
							return '<span class="kt-font-brand kt-font-bold">' + "嘟嘟牛" + '</span>';
						} else if (data === 303) {
							return '<span class="kt-font-brand kt-font-bold">' + "轻网联盟" + '</span>';
						} else if (data === 7) {
							return '<span class="kt-font-brand kt-font-bold">' + "PMS" + '</span>';
						} else if (data === 8) {
							return '<span class="kt-font-brand kt-font-bold">' + "龙管家" + '</span>';
						} else if (data === -1) {
							return '<span class="kt-font-brand kt-font-bold">' + "全部" + '</span>';
						} else if (data === 304) {
							return '<span class="kt-font-brand kt-font-bold">' + "佳星" + '</span>';
						} else if (data === 305) {
							return '<span class="kt-font-brand kt-font-bold">' + "百果树" + '</span>';
						} else if (data === 306) {
							return '<span class="kt-font-brand kt-font-bold">' + "奥比特" + '</span>';
						} else if (data === 307) {
							return '<span class="kt-font-brand kt-font-bold">' + "丕微" + '</span>';
						}else {
							return "<span>未知</span>";
						}
					}
				},
				{
					targets: 3, // 
					render: function(data, type, full, meta) {
						return convertFaceclientTypes(data);
					}
				},
				{
					targets: 4, // 
					render: function(data, type, full, meta) {
						if(data === 'PROV'){
							return "省级";
						}else if(data === 'CITY'){
							return "市级";
						}else if(data === 'COUNTRY'){
							return "区级";
						}else if(data === 'BAR'){
							return "单店 ";
						}
					}
				},
				{
					targets: 5,
					render: function(data, type, full, meta) {
						if (data === 'EveryTime') {
							return '次卡';
						} else if (data === 'ByDay') {
							return '日卡';
						} else if (data === 'ByWeek') {
							return '周卡';
						} else if (data === 'ByMonth') {
							return '月卡';
						} else if (data === 'ByQuarter') {
							return '季卡';
						} else if (data === 'ByYear') {
							return '年卡';
						}
					}
				},
				{
					targets: 6,
					render: function(data, type, full, meta) {
						return (data / 100.00) + '元';
					}
				},
				{
					targets: 7,
					render: function(data, type, full, meta) {
						return (data / 100.00) + '元';
					}
				},
				{
					targets: -1,
					title: '操作',
					render: function(data, type, full, meta) {
						 var rowData = JSON.stringify(full);
					    if(full.conftype==='BAR'){
						   return '<button type="button" class="btn btn-sm btn-primary" ' +
							'data-toggle="modal" data-target="#cancelConnectionModal" data-value=' + rowData + ' data-description=""  title="取消关联">' +
							'取消关联</button>'
					    }else{
						   return '...';
					    }
					}
				},
				{
					targets: -2,
					render: function(data, type, full, meta) {
						var days = 0;
						if (data >= 1440) {
							if (data >= 1440) {
								days = data / 1440;
							}
							var mins = data % 1440;
							var hours = 0;
							if (mins > 0) {
								if (mins >= 60) {
									hours = mins / 60;
								}
								mins = mins % 60;
							}
							return days.toFixed() + '天' + hours.toFixed() + '小时' + mins.toFixed() + '分钟';
						} else {
							var hours = 0;
							var mins = data % 1440;
							if (mins > 0) {
								if (mins >= 60) {
									hours = mins / 60;
								}
								mins = mins % 60;
							}
							return hours.toFixed() + '小时' + mins.toFixed() + '分钟';
						}
					}

				}
			]
		});
		
		
	};

    // 查询单个门店生效套餐
	function initBarConnectPlanConfsModal() {
		$('#barConnectPlanConfsModal').on('show.bs.modal', function (event) {
            $('#barConnectionConfsForm').find('.alert').remove(); 
            // init table
            connectionConfs_table();
		});
		
	    // 查询单个门店生效套餐
	    $('#queryBtn4mz').on('click', function(e) {
	    	e.preventDefault();
            connectionConfs_table();
	    });

       var planConfId;
	   $('#cancelConnectionModal').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowData = button.data('value');
			if(rowData != undefined){
				planConfId=rowData.id;
			}
		});
	    $('#doCancel4One').on('click', function(e) {
	    	e.preventDefault();
            var btn = $(this);
			var form = $('#barConnectionConfsForm');
			$.ajax({
				url: '/iot/planpayconfigconection/delete',
				type: 'POST',
				data: {"barIds": barid4mz,"id": planConfId},
				success: function(response) {
					console.log(response);
					if(response.result){
						$('#baridsText').val("");
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
						setTimeout(function(){ connectionConfs_table(); }, 2000);
						// var table = $('#kt_table_1').DataTable();
						// table.draw( false );
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });
	}
  
function initSysParamsConfModal(){
	$('#sysParamsConfModal').on('show.bs.modal', function (event) {
            $('#sysParamsConfForm').find('.alert').remove(); 
            var btn = $(this);
            var form = $('#sysParamsConfForm');
            $.ajax({
				url: '/iot/planpayconfig/getIotSysParams',
				type: 'GET',
				//data: {},
				success: function(response) {
					console.log(response);
					if(response.result){
						$("#appPaySyncFlagSelect").val(response.data.obj.appPaySyncFlag + '');
						
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	});
	
	$('#doSysParamsSubmit').on('click', function(e) {
	    	e.preventDefault();
            var btn = $(this);
            var form = $('#sysParamsConfForm');

            var appPaySyncFlag = $("#appPaySyncFlagSelect").val();
            console.log('appPaySyncFlag:' + appPaySyncFlag); // 输出：true 或 false
			$.ajax({
				url: '/iot/planpayconfig/confIotSysParams',
				type: 'POST',
				data: {"appPaySyncFlag": appPaySyncFlag},
				success: function(response) {
					console.log(response);
					if(response.result){
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						showErrorMsg(form, 'success', response.message);
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });
}

function setConnectionCheck4Area(connectionsArr, treeContainer){
	// var $myTree=$('ul.tree');
	 var $myTree=$("#" + treeContainer).find('ul.tree');
     $myTree.find('.checkbox').removeClass('checked half_checked').siblings(':checkbox').prop('checked', false);
     $myTree.find('.checktree.arrow').removeClass('collapsed').addClass('expanded');
     //$('ul.tree').find('input[value="000000000000"]').parents("li:first").find("ul").css("display","none");
     $myTree.find('input[value="000000000000"]').parents("li:first").find("ul").css("display","none");

	 $.each(connectionsArr, function() {
		var checkedId=$(this).attr("connectionId");
		//console.log(checkedId);
        //var checknode=$('ul.tree').find('input[value="' + checkedId +'"]').parents("li:first").find(".checktree.checkbox");
        var checknode=$myTree.find('input[value="' + checkedId +'"]').parents("li:first").find(".checktree.checkbox"); 
        var $this = jQuery(checknode);
        $this.toggleClass('checked').removeClass('half_checked').siblings(':checkbox:first').prop('checked', $this.hasClass('checked'));

        $this.filter('.checked').siblings('ul:first').find('.checkbox:not(.checked)').removeClass('half_checked').addClass('checked').siblings(':checkbox').prop('checked', true);
        $this.filter(':not(.checked)').siblings('ul:first').find('.checkbox.checked').removeClass('checked half_checked').siblings(':checkbox').prop('checked', false);
        $this.parents("ul:first").siblings(":checkbox:first").trigger('refresh');
    });
    $myTree.siblings(":checkbox:first").trigger('refresh');
}

	function initConnectionAreaModal() {
		var planConfId;
		var conftype; 
		$('#connectionAreaModal').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowData = JSON.parse(decodeURIComponent(button.data('value')));
			if(rowData != undefined){
				planConfId=rowData.id;
				conftype = rowData.conftype;
				if(conftype==='PROV'){
					initCheckTree(regionJsonArr4prov, 'areaTreeContainer');
					$("#areaTreeContainer").show();
					$("#areaTreeContainer4country").hide();
				}else if(conftype==='CITY'){
					initCheckTree(regionJsonArr4city, 'areaTreeContainer');
					$("#areaTreeContainer").show();
					$("#areaTreeContainer4country").hide();
				}else{
					$("#areaTreeContainer").hide();
					$("#areaTreeContainer4country").show();
				}
		   
            // 设置勾选状态
            $.ajax({
				url: '/iot/planpayconfigconection/getPlanPayConfigConections',
				type: 'GET',
				data: {"conftype": conftype,"planconfigId": planConfId},
				success: function(response) {
					// console.log(response);
					if(response.result){
						var connectionsArr=response.data.list;
						if(conftype == 'COUNTRY'){
							setConnectionCheck4Area(connectionsArr, "areaTreeContainer4country");
						}else{
							setConnectionCheck4Area(connectionsArr, "areaTreeContainer");
						}
					} else {
						alert(response.message);
					}
				},
				error: function(response, status){
					alert('系统繁忙，请稍后再试');
				}
			})

			}
		});
		
		$('#doConfirm4Area').on('click', function(e) {
	    	e.preventDefault();
            var btn = $(this);
            var connectionIds = "";
            var ulAreaTree = conftype === 'COUNTRY' ? $("#areaTreeContainer4country").find('ul.tree') : $("#areaTreeContainer").find('ul.tree');
            ulAreaTree.find('input[type="checkbox"]:checked').each(function(){
	             var code=$(this).val();
                 if(conftype === 'PROV'){
	                 if(code.endsWith("0000000000") && code!="000000000000"){
		                connectionIds += code + ",";
	                 }
                 }else if(conftype === 'CITY'){
	                 if(code!="000000000000" && !code.endsWith("0000000000") && code.endsWith("00000000")){
		                connectionIds += code + ",";
	                 }
                 }else{
					 // code.endsWith("000000") || code.endsWith("000") 正常的区，或特殊的区（如：中山/东莞下一级的，442000004000）
					 if (code != "000000000000" && !code.endsWith("0000000000") && !code.endsWith("00000000") && (code.endsWith("000000") || code.endsWith("000"))) {
						 connectionIds += code + ",";
					 }
                 }
            });
            if(connectionIds!=""){
	            connectionIds = connectionIds.substring(0,connectionIds.length-1);
            }

			var form = $('#authConfigForm');
			$.ajax({
				url: '/iot/planpayconfigconection/update4Area',
				type: 'POST',
				data: {"conftype": conftype,"connectionIds": connectionIds,"id": planConfId},
				success: function(response) {
					console.log(response);
					if(response.result){
						btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
						
						showErrorMsg(form, 'success', response.message);
						setTimeout(function(){ connectionConfs_table(); }, 2000);
						// var table = $('#kt_table_1').DataTable();
						// table.draw( false );
					} else {
						showErrorMsg(form, 'danger', response.message);
					}
				},
				error: function(response, status){
					btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
					showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
				}
			})
	    });
	}	

	var getEffectiveMinutes = function() {
		$("#planFeeTypeSelect4Add").change(function(){
			$("#planFee4Add").val('');
    		var type = $("#planFeeTypeSelect4Add").val();
			if (type === 'Free') {
				$("#effectiveMinutes4Add").val(0);
				$("#planFee4Add").val(0);
			} else if (type === 'ByDay') {
				$("#effectiveMinutes4Add").val(1440);
			} else if (type === 'ByWeek') {
				$("#effectiveMinutes4Add").val(10080);
			} else if (type === 'ByMonth') {
				$("#effectiveMinutes4Add").val(43200);
			} else if (type === 'ByQuarter') {
				$("#effectiveMinutes4Add").val(129600);
			} else if (type === 'ByYear') {
				$("#effectiveMinutes4Add").val(525600);
			} else {
				$("#effectiveMinutes4Add").val('');
			}
        });
	}

	return {
		// main function to initiate the module
		init: function() {
			// $('#kt_daterangepicker_1').val('');
			initTable1();
			initEditConfModal();
			addConfig();
			
			initConnectionBarModal();
			initBarConnectPlanConfsModal();
			initConnectionAreaModal();
			initSysParamsConfModal();
            getEffectiveMinutes();
		},
	};

}();

function initCheckTree(jsonData, treeContainer){
	$('#' + treeContainer)[0].innerHTML='';
    var myTreeDiv = $('<ul class="tree" style="margin-left: 15px;"></ul>');
    $.each(jsonData, function() {
        var $node = $('<li><input type="checkbox" value="'+ this.code +'"/><label>'+ this.name +'</label></li>');  
        myTreeDiv.append($node);
        if (this.children && this.children.length > 0) {
	       var $ulnode = $('<ul></ul>');
           $node.append($ulnode);
	       appendChildren($ulnode, this.children);
        }
        
    });  
    $('#' + treeContainer).append(myTreeDiv);
    $("#" + treeContainer).find('ul.tree').checkTree();
    //$('ul.tree').checkTree();
  }

function appendChildren($parent, children) {  
    $.each(children, function() {  
        var $node = $('<li><input type="checkbox" value="'+ this.code +'"/><label>'+ this.name +'</label></li>');  
        $parent.append($node);
        if (this.children && this.children.length > 0) {
	        var $ulnode = $('<ul></ul>');
            $node.append($ulnode);
            appendChildren($ulnode, this.children);
        }  
    });
}	

var regionJsonArr4prov;
var regionJsonArr4city;
var regionJsonArr4country;
jQuery(document).ready(function() {
	KTDatatablesDataSourceAjaxServer.init();
	$.getJSON('/assets/json/region_json_prov.json', function(data) {  
       regionJsonArr4prov = data;
    });
    $.getJSON('/assets/json/region_json_city.json', function(data) {  
       regionJsonArr4city = data;
    });
	$.getJSON('/assets/json/region_json_country.json', function(data) {
       regionJsonArr4country = data;
       initCheckTree(regionJsonArr4country, 'areaTreeContainer4country');
    });

    $('.dropdown-menu').on('click', function(e) {
        if($(this).hasClass('dropdown-menu')) {
            e.stopPropagation();
        }
    });
});
