"use strict";
var KTDatatablesDataSourceAjaxServer = function() {

    function daysBetweenDates(date1, date2) {  
       var startDate = new Date(date1);  
       var endDate = new Date(date2);  
       var timeDiff = endDate - startDate;  
       return Math.abs(timeDiff / (1000 * 60 * 60 * 24));  
    }
	var startTime = moment().subtract(6, "days").format("YYYY-MM-DD") + " 00:00:00";
	var endTime = moment().startOf('day').format("YYYY-MM-DD") + " 23:59:59";
	var defaultStartTime = moment().subtract(6, "days").format("YYYY-MM-DD");
	var defaultEndTime = moment().startOf('day').format("YYYY-MM-DD");
	var regionCode = "";
	var dateRangePicker = function () {
		console.info("loading......dateRangePicker");
		$("#kt_daterangepicker_1").daterangepicker({
				language:  'zh-CN',
				buttonClasses: 'btn',
				applyClass: 'btn-primary',
				cancelClass: 'btn-secondary',
				startDate: moment().subtract('days', 6),
				endDate: moment().startOf('day'),
				ranges : {
					'今日': [moment().startOf('day'), moment()],
					'昨日': [moment().subtract('days', 1).startOf('day'), moment().subtract('days', 1).endOf('day')],
					'最近7日': [moment().subtract('days', 6), moment()],
					'最近30日': [moment().subtract('days', 29), moment()],
					'本月': [moment().startOf("month"),moment().endOf("month")],
					'上个月': [moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]
				},
				locale: {
					format: "YYYY-MM-DD",
					applyLabel: '确认',
					cancelLabel: '取消',
					fromLabel: '从',
					toLabel: '到',
					customRangeLabel: '选择时间',
					daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
					monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
				}},
			function (start, end, lable) {
				console.info("A new date selection was made: " + start.format("YYYY-MM-DD") + " to " + end.format("YYYY-MM-DD"));
				var datePicker11=$("#kt_daterangepicker_1");
				var daysSize = daysBetweenDates(start.format("YYYY-MM-DD"), end.format("YYYY-MM-DD"));
				if(daysSize>60){
					alert("选择日期时间段不能超过60天");
					startTime = defaultStartTime + " 00:00:00";
				    endTime = defaultEndTime + " 23:59:59";
                    datePicker11.data('daterangepicker').setStartDate(moment().subtract('days', 6));  
                    datePicker11.data('daterangepicker').setEndDate(moment().startOf('day'));
					return;
				}else{
					datePicker11.data('daterangepicker').setStartDate(start);  
                    datePicker11.data('daterangepicker').setEndDate(end);
				}
				startTime = start.format("YYYY-MM-DD") + " 00:00:00";
				endTime = end.format("YYYY-MM-DD") + " 23:59:59";
			});
	};

	function initProvince(){
		console.log("initProvince()");
		$.ajax({
			url: '/region/provinces',
			type: 'GET',
			dataType: 'json',
			success: function(response){
				$("#provinceSelect").empty();
				$("#provinceSelect").append("<option value=''>请选择</option>");
				$.each(response, function(index, item){
					var name = response[index].name;
					var provinceId = response[index].provinceId;
					$("#provinceSelect").append("<option value='" + provinceId + "'>" + name + "</option");
				});
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				console.log(errorThrown);
			}
		});
	}

	function initCity(provinceId){
		console.log("initCity("+provinceId+")");
		regionCode = provinceId;
		$.ajax({
			url: '/region/cities/'+provinceId,
			type: 'GET',
			dataType: 'json',
			success: function(response){
				$("#citySelect").empty();
				$("#citySelect").append("<option value=''>请选择</option>");
				$.each(response, function(index, item){
					var name = response[index].name;
					var cityId = response[index].cityId;
					$("#citySelect").append("<option value='"+cityId+"'>"+name+"</option");
				});
				$("#countrySelect").empty();
				$("#countrySelect").append("<option value=''>请选择</option>");

			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				console.log(errorThrown);
			}
		});
	}

	function initCountry(cityId){
		console.log("initCountry("+cityId+")");
		regionCode = cityId;
		$.ajax({
			url: '/region/countries/'+cityId,
			type: 'GET',
			dataType: 'json',
			success: function(response){
				$("#countrySelect").empty();
				$("#countrySelect").append("<option value=''>请选择</option>");
				$.each(response, function(index, item){
					var name = response[index].name;
					var countryId = response[index].countryId;
					$("#countrySelect").append("<option value='"+countryId+"'>"+name+"</option");
				});
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				console.log(errorThrown);
			}
		});
	}


	var initCascadeSelect = function(){

		initProvince();

		$("#provinceSelect").change(function(){
			var provinceId = $("#provinceSelect").val();
			if (provinceId != null && provinceId !='' && provinceId !='undefined') {
				initCity(provinceId);
			} else {
				regionCode = provinceId;
				$("#citySelect").empty();
				$("#citySelect").append("<option value=''>请选择</option>");
				$("#countrySelect").empty();
				$("#countrySelect").append("<option value=''>请选择</option>");
				$("#townSelect").empty();
				$("#townSelect").append("<option value=''>请选择</option>");
				$('#townDiv').attr("readonly",false);
			}
		});

		$("#citySelect").change(function(){
			var cityId = $("#citySelect").val();
			if (cityId != null && cityId !='' && cityId !='undefined') {
				initCountry(cityId);
			} else {
				regionCode = regionCode.substring(0,2) + "0000000000";
				$("#countrySelect").empty();
				$("#countrySelect").append("<option value=''>请选择</option>");
				$("#townSelect").empty();
				$("#townSelect").append("<option value=''>请选择</option>");
				$('#townDiv').attr("readonly",false);
			}
		});

		$("#countrySelect").change(function(){
			var countryId = $("#countrySelect").val();
			if (countryId != null && countryId !='' && countryId !='undefined') {
				regionCode = countryId;
			} else {
				regionCode = regionCode.substring(0,4) + "00000000";
				$("#townSelect").empty();
				$("#townSelect").append("<option value=''>请选择</option>");
				$('#townDiv').attr("readonly",false);
			}
		});
	}

	var initTable1 = function() {
		var table = $('#kt_table_1');
		var placeId = $('#placeId').val();
		var orderId = $('#orderId').val();
		var type = $('#type').val();
		var effectived = $('#effectived').val();
		var source = $('#deviceSelect').val();
		var placeAuthFeeType = $('#placeAuthFeeType').val();
		var idNumber = $('#idNumber').val();
		var idName = $('#idName').val();
		// begin first table
		table.DataTable({
			language: {
				"url": "https://4wgj.topfreeweb.net/assets/vendors/custom/Chinese.json"
		    },
		    serverSide: true, // 开启服务器模式，必须
			responsive: true, // 启用和配置响应式
			processing: true, // 是否显示加载中提示
			searching: false, // 开启搜索框
			// "fnDrawCallback": function( oSettings ) {
			// 	$('.dataTables_filter input').attr({'name':'search','placeholder': '场所编码'});// 搜索提示
			// },
			lengthChange: false, // 开启每页设置显示数量
			ordering: false, // 字段排序
			paging: true,
			destroy: true,
			ajax: {
				url: '/iot/faceAuth/faceAuths',
				type:'GET',
				data: {
					"type": type,
					"placeId": placeId,
					"orderId": orderId,
					"idName": idName,
					"effectived": effectived,
					"source": source,
					"placeAuthFeeType": placeAuthFeeType,
					"idNumber": idNumber,
					"startTime": startTime,
					"endTime": endTime,
					"searchRegionCode": regionCode
				},
			},
			columns: [
				{data: 'created'},
				{data: 'idNumber'},
				{data: 'idName'},
				{data: 'placeId'},
				{data: 'placeName'}, // 4
				{data: 'address'},
				{data: 'type'},
				{data: 'deviceId'},
				{data: 'faceId'},
			],
			columnDefs: [
				{
					targets: '_all', // 全选
					className: "text-center font-weight-normal", //居中
				},
				{
					targets: 5,
					width : "80px",
				},
				{
					targets: 7,
					render: function(data, type, full, meta) {
						if (full.deviceId !=='' && full.deviceId !== null && full.deviceId == "wechatMiniByWyf") {
							return '<span class="">微信小程序</span>';
						}else if (full.deviceId !=='' && full.deviceId !== null && full.deviceId == "wechatMp") {
							return '<span class="">微信公众号</span>';
						}else if (full.deviceId !=='' && full.deviceId !== null && full.deviceId == "iot_minapp"){
							return '<span class="">支付宝IOT</span>';
						}else if (full.deviceId !=='' && full.deviceId !== null && full.deviceId == "alipay_minapp"){
							return '<span class="">支付宝小程序</span>';
						}
					},
				},
				{
					targets: 0,
					width : "80px",
					render: function(data, type, full, meta) {
						if (data !== '' && data !== null && data !== undefined) {
							var datetime;
							if (data.length < 6) {
								datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], 0]);
							} else {
								datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], data[5]]);
							}
							return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
						}
						return '';
					},				
					
				},
				{
					targets: 8,
					render: function(data, type, full, meta) {
						var rowData = JSON.stringify(full);
						if(data !== '' && data !== null && data !== undefined){
							return '<button type="button" class="btn btn-sm btn-primary" ' +
							'data-toggle="modal" data-target="#viewAuthFeeDetailModal" data-value=' + rowData + ' data-description=' +full.placeId+ ' title="付费详情">付费详情' +
							'</button>';
						}else{
							return '';
						}
					},
				},
				{
					targets: 6,
					width : "40px",
					render: function (data, type, row) {
						if (data === 0) {
							return '<span class="kt-font-brand kt-font-bold">' + "网吧" + '</span>';
						} else if (data === 1) {
							return '<span class="kt-font-brand kt-font-bold">' + "酒店" + '</span>';
						} else if (data === 2) {
							return '<span class="kt-font-brand kt-font-bold">' + "网租" + '</span>';
						} else if (data === 3) {
							return '<span class="kt-font-brand kt-font-bold">' + "普通场所" + '</span>';
						} else if (data === 4) {
							return '<span class="kt-font-brand kt-font-bold">' + "营销大师" + '</span>';
						} else if (data === 5) {
							return '<span class="kt-font-brand kt-font-bold">' + "九威" + '</span>';
						} else if (data === 6) {
							return '<span class="kt-font-brand kt-font-bold">' + "大巴掌" + '</span>';
						} else if (data === 7) {
							return '<span class="kt-font-brand kt-font-bold">' + "PMS" + '</span>';
						}  else if (data === 8) {
							return '<span class="kt-font-brand kt-font-bold">' + "龙管家" + '</span>';
						} else if (data === 301) {
							return '<span class="kt-font-brand kt-font-bold">' + "万象" + '</span>';
						} else if (data === 302) {
							return '<span class="kt-font-brand kt-font-bold">' + "嘟嘟牛" + '</span>';
						} else if (data === 303) {
							return '<span class="kt-font-brand kt-font-bold">' + "轻网联盟" + '</span>';
						} else if (data === 304) {
							return '<span class="kt-font-brand kt-font-bold">' + "佳星" + '</span>';
						} else if (data === 305) {
							return '<span class="kt-font-brand kt-font-bold">' + "百果树" + '</span>';
						} else if (data === 306) {
							return '<span class="kt-font-brand kt-font-bold">' + "奥比特" + '</span>';
						} else if (data === 307) {
							return '<span class="kt-font-brand kt-font-bold">' + "丕微" + '</span>';
						} else {
							return "<span>未知</span>";
						}
					}
				},
				
			],
		});
	};

	var initStatistics = function() {
        queryFaceStat("authProfileNum");
        queryFaceStat("authPeopleNum");
        queryFaceStat("authNum");
	}
	
	function queryFaceStat(statName){
		var placeId = $('#placeId').val();
		var orderId = $('#orderId').val();
		var type = $('#type').val();
		var effectived = $('#effectived').val();
		var placeAuthFeeType = $('#placeAuthFeeType').val();
		var idNumber = $('#idNumber').val();
		var idName = $('#idName').val();
		var source = $('#deviceSelect').val();
		$.ajax({
			url: '/iot/faceAuth/queryFaceStatistics',
			type: 'GET',
			data: {
				"type": type,
				"placeId": placeId,
				"orderId": orderId,
				"idName": idName,
				"effectived": effectived,
				"placeAuthFeeType": placeAuthFeeType,
				"idNumber": idNumber,
				"startTime": startTime,
				"endTime": endTime,
				"searchRegionCode": regionCode,
				"source": source,
				"statName": statName
			},
			success: function(response){
				if(statName === "authProfileNum"){
					$("#authProfileNum").text(response.authProfileNum+"个");
				}else if(statName === "authPeopleNum"){
					$("#authPeopleNum").text(response.authPeopleNum+"人");
				}else{
					$("#authNum").text(response.authNum+"次");
				} 
			},
			error: function(XMLHttpRequest, textStatus, errorThrown){
				console.log(errorThrown);
			}
		});
	}

	// 页面查询按钮
	$('#search').on('click', function(e) {
		e.preventDefault();
		initTable1();
		initStatistics();
	});

	// 页面重置按钮
	$('#reset').on('click', function(e) {
		e.preventDefault();
		$('#placeId').val("");
		$('#orderId').val("");
		$('#provinceSelect').val("");
		$('#citySelect').val("");
		$('#countrySelect').val("");
		$('#type').val("");
		$('#idName').val("");
		$('#idNumber').val("");
		$('#effectived').val("");
		$('#placeAuthFeeType').val();
		$('#deviceSelect').val("");
		regionCode = "";
		$('#kt_daterangepicker_1').val(defaultStartTime + " - " + defaultEndTime);
		//var datePicker11=$("#kt_daterangepicker_1");
        //datePicker11.data('daterangepicker').setStartDate(moment().subtract('days', 6));  
        //datePicker11.data('daterangepicker').setEndDate(moment().startOf('day'));
		startTime = defaultStartTime + " 00:00:00";
		endTime = defaultEndTime + " 23:59:59";
	});

    function getAuthFeeTypeName(data){
		if (data === 'EveryTime') {
			return '次卡';
		} else if (data === 'ByDay') {
			return '日卡';
		} else if (data === 'ByWeek') {
			return '周卡';
		} else if (data === 'ByMonth') {
			return '月卡';
		} else if (data === 'ByQuarter') {
			return '季卡';
		} else if (data === 'ByYear') {
			return '年卡';
		} else {
			return '';
		}	 			
    }

    function getDateTimeStr(data){
	    if (data !== '' && data !== null && data !== undefined) {
			var datetime;
			if (data.length < 6) {
				datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], 0]);
			} else {
				datetime = moment([data[0], data[1] - 1, data[2], data[3], data[4], data[5]]);
			}
			    return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
			}
		return '';
    }
    // 查看详情
    function initViewAuthFeeDetailModal(){
	   	$('#viewAuthFeeDetailModal').on('show.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var rowData = button.data('value');
			if(rowData != undefined){
				// 获取对应付费信息TODO
				$('#placeId4view').val(rowData.placeId);
				$('#idName4view').val(rowData.idName);
				$('#idNumber4view').val(rowData.idNumber);
				
				var createdStr = getDateTimeStr(rowData.created);
			    $.ajax({
			    	url: '/iot/authOrder/queryPlaceAuthFeeByFaceId',
			    	type: 'GET',
			    	data: {"placeId": rowData.placeId,"faceId": rowData.faceId,"startTime": createdStr},
			    	success: function(response) {
			    		console.log(response);
			    		if(response.result){
				            var dataObj = response.data.obj;
                            if(dataObj==null){
					            $('#isPay4view').val('否');
				                $('#authFee4view').val('');
				                $('#authFeeType4view').val('');
				                $('#effectiveTime4view').val('');
				                $('#effectived4view').val('');
				                $('#payTime4view').val('');
				                $('#orderId4view').val('');
	                           return;
                            }
				            $('#isPay4view').val((dataObj.authFee!=undefined && dataObj.authFee > 0)? '是' : '否');
				            $('#authFee4view').val(dataObj.authFee/100.00 + '元');
				            $('#authFeeType4view').val(getAuthFeeTypeName(dataObj.authFeeType));
				
				            $('#effectiveTime4view').val(getDateTimeStr(dataObj.effectiveTime));
				            $('#effectived4view').val(dataObj.effectived == 1 ? "已支付" : "未支付");
				            $('#payTime4view').val(dataObj.effectived == 0 ? '' : getDateTimeStr(dataObj.updated));
				
				            $('#orderId4view').val(dataObj.orderId);
			    			//btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
			    			//showErrorMsg(form, 'success', response.message);
			    			//setTimeout(function(){ connectionBars_table(planConfId); }, 2000);
			    		} else {
			    			showErrorMsg(form, 'danger', response.message);
			    		}
			    	},
			    	error: function(response, status){
			    		//btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
			    		showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
			    	}
			    });
			}
		});
    }

	// 导出
	var isHasData = 0;
	var exportUrl;
	$('#export').on('click', function(e) {
		e.preventDefault();

		var placeId = $('#placeId').val();
		var orderId = $('#orderId').val();
		var type = $('#type').val();
		var effectived = $('#effectived').val();
		var source = $('#deviceSelect').val();
		var placeAuthFeeType = $('#placeAuthFeeType').val();
		var idNumber = $('#idNumber').val();
		var idName = $('#idName').val();

		swal.fire({
			title: "正在导出......",
			showConfirmButton: false,
			allowOutsideClick: false,
			allowEscapeKey: false,
			allowEnterKey: false,
		});

		exportUrl = '/iot/faceAuth/export?type=' + type + '&placeId=' + placeId
			+ '&orderId=' + orderId + '&idName=' + idName + '&effectived=' + effectived + '&source=' + source
			+ '&placeAuthFeeType=' + placeAuthFeeType + '&idNumber=' + idNumber
			+ '&startTime=' + startTime + '&endTime=' + endTime + '&searchRegionCode=' + regionCode;//请求url
		
		isHasData = 0;
		reqExportData(exportUrl, 0);
	});

    function reqExportData(url, dataPage){
	    url += '&dataPage=' + dataPage;
	    var method = 'post';//请求方法
		var xhr = new XMLHttpRequest();//定义一个XMLHttpRequest对象
		xhr.open(method, url, true);
		xhr.send(null);
		xhr.responseType = 'blob';//设置ajax的响应类型为blob
		var maxPage = 30;
		xhr.onload = function ()//当请求完成，响应就绪进入
		{
			swal.close();
			if (this.status === 200)//当响应状态码为200时进入
			{
				var blob = this.response;//获取响应返回的blob对象
				//这一段用来判断是否是IE浏览器，因为下面有些代码不支持IE
				if (typeof window.navigator.msSaveBlob !== 'undefined') {
					window.navigator.msSaveBlob(blob, "人脸记录导出列表.xlsx");
					return;
				}
				var a = document.createElement('a');//在dom树上创建一个a标签
				var url = window.URL.createObjectURL(blob);//生成一个相对于浏览器的虚拟url，用于指向传入的blob对象，让浏览器可以通过这个url找到这个blob对象
				a.href = url;//将url赋值给a标签的href属性
				a.download = '人脸记录导出列表_' + dataPage + '.xlsx';//设置设置下载文件的名称
				a.click();//主动触发a标签点击事件
				
				isHasData = parseInt(this.getResponseHeader("isHasData"));
				if(isHasData > 0){
					dataPage += 1;
					if(dataPage > maxPage-1){
						alert("一次最多只能连续导出10个文件,请不要频繁操作。");
						return;
					}
					reqExportData(exportUrl, dataPage);
				}
			}
		};
    } 

	return {

		// main function to initiate the module
		init: function() {
			dateRangePicker();
			initCascadeSelect();
			initTable1();
			initStatistics();
			initViewAuthFeeDetailModal();
		},
	};

}();


jQuery(document).ready(function() {
	KTDatatablesDataSourceAjaxServer.init();
});