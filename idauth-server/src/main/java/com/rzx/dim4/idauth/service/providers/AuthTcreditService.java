package com.rzx.dim4.idauth.service.providers;

import com.alibaba.excel.util.StringUtils;
import com.google.gson.Gson;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.idauth.AuthProvider;
import com.rzx.dim4.base.utils.CryptUtils;
import com.rzx.dim4.idauth.entity.IdCard;
import com.rzx.dim4.idauth.service.IdCardService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * 天创信用
 *
 * <AUTHOR>
 * {@code @Date} 2023年8月22日 上午11:50:14
 */
@Slf4j
@Service
public class AuthTcreditService {

    private final String appId = "cbfba9a02953434cb638671ade53eef2";

    private final String tokenId = "bb62afaa48ad4b0aadf960f4dce1544f";

    @Autowired
    IdCardService idCardService;


    /**
     * 2要素
     *
     * @param name     姓名
     * @param idNumber 身份证号
     * @return ServiceCodes ServiceCodes
     */
    public ServiceCodes auth2e(String name, String idNumber) {

        ServiceCodes result = ServiceCodes.INTERNAL_EXCEPTION;

        String url = "https://api.tcredit.com/identity/verifyIdcardC";

        // 封装参数
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("appId", appId);
        params.add("tokenKey", getSign(url, idNumber, name, null));
        params.add("idcard", idNumber);
        params.add("name", name);

        try {
            // 发送请求
            TcreditResponse response = sendRequest(url, params);
            if (response.getStatus() == 0) { // 请求成功
                if (response.getData() != null && "1".equals(response.getData().getResult())) {
                    IdCard idCard = new IdCard(idNumber, name, AuthProvider.TCREDIT_2E, 1);
                    idCardService.saveNew(idCard);
                    result = ServiceCodes.AUTH_SUCC;
                } else if (response.getData() != null && "3".equals(response.getData().getResult())) {
                    result = ServiceCodes.NO_AUTH_INFO;
                } else {
                    result = ServiceCodes.INCONSISTENT;
                }
            } else {
                result = ServiceCodes.TCREDIT_SERVER_ERROR;
            }
        } catch (Exception e) {
            log.error("Tcredit二要素调用异常");
            e.printStackTrace();
            result = ServiceCodes.TCREDIT_SERVER_ERROR;
        }
        return result;
    }

    public ServiceCodes auth3e(String name, String idNumber, String facePhotoBase64, float threshold) {
        ServiceCodes result = ServiceCodes.INTERNAL_EXCEPTION;
        threshold = threshold * 1000; // 天创的反馈的规则是小于600，系统断为不同人，600-700 不能确定是否为同一人，大于 700：系统判断为同一人

        if (facePhotoBase64.startsWith("data:image")) { // 天创的接口base64不能传 data:image/jpg;base64,
            facePhotoBase64 = facePhotoBase64.split(",")[1];
        }

        String url = "https://api.tcredit.com/identity/authentication";

        // 封装参数
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("appId", appId);
        params.add("tokenKey", getSign(url, idNumber, name, facePhotoBase64));
        params.add("idcard", idNumber);
        params.add("name", name);
        params.add("photo", facePhotoBase64);

        try {
            // 发送请求
            TcreditResponse response = sendRequest(url, params);
            if (response.getStatus() == 0) { // 请求成功
                if (response.getData() != null && ("00".equals(response.getData().getResult()) || "02".equals(response.getData().getResult()))) {
                    IdCard idCard = new IdCard(idNumber, name, AuthProvider.TCREDIT_3E, 1);
                    idCardService.saveNew(idCard);
                    Float confidence = response.getData().getScore();
                    if (confidence >= threshold) {
                        result = ServiceCodes.AUTH_SUCC;
                    } else {
                        result = ServiceCodes.BAD_PHOTO_OR_VIDEO;
                    }
                } else if (response.getData() != null && "01".equals(response.getData().getResult())) {
                    result = ServiceCodes.BAD_PHOTO_OR_VIDEO;
                } else if (response.getData() != null && "5X".equals(response.getData().getResult())) {
                    result = ServiceCodes.INCONSISTENT;
                } else if (response.getData() != null && "6X".equals(response.getData().getResult())) {
                    result = ServiceCodes.INCONSISTENT;
                } else if (response.getData() != null && "0I".equals(response.getData().getResult())) {
                    result = ServiceCodes.NO_FACE;
                } else if (response.getData() != null && "0D".equals(response.getData().getResult())) {
                    result = ServiceCodes.NO_AUTH_INFO;
                } else if (response.getData() != null && "XY".equals(response.getData().getResult())) {
                    result = ServiceCodes.PHOTO_TOO_LARGE;
                } else {
                    result = ServiceCodes.TCREDIT_SERVER_ERROR;
                }
            } else {
                result = ServiceCodes.TCREDIT_SERVER_ERROR;
            }
        } catch (Exception e) {
            log.error("Tcredit三要素调用异常");
            e.printStackTrace();
            result = ServiceCodes.TCREDIT_SERVER_ERROR;
        }
        return result;

    }

    /**
     * 发送tcredit请求
     *
     * @param url
     * @param map
     * @return
     * @throws Exception
     */
    private TcreditResponse sendRequest(String url, MultiValueMap<String, Object> map) throws Exception {
        long now = System.currentTimeMillis();
        // 设置header
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        // 设置entity
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(map, headers);
        // 发送请求并接受结果
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, entity, String.class);
        TcreditResponse responseResult = new Gson().fromJson(responseEntity.getBody(), TcreditResponse.class);
        log.info("[" + String.valueOf(map.get("idcard")) + "]天创请求:::" + url + "，耗时:::"
                + String.valueOf(System.currentTimeMillis() - now));
        log.info("[" + String.valueOf(map.get("idcard")) + "]返回内容:::" + responseEntity.getBody());
        return responseResult;
    }

    private String getSign(String url, String idNumber, String name, String photo) {
        String toSign = url + tokenId + "idcard=" + idNumber + ",name=" + name;
        if (!StringUtils.isEmpty(photo)) {
            toSign += ",photo=" + photo;
        }
        String sign = CryptUtils.generateDigestWithMD5NoSalt(toSign.getBytes());
        return sign.toUpperCase();
    }

    @Data
    class TcreditResponse {
        private TcreditData data;
        private String seqNum;
        private String message;
        private int status;
    }

    @Data
    class TcreditData {
        private String result;
        private float score;
        private String resultMsg;
    }

}
