package com.rzx.dim4.marketing.repository;

import com.rzx.dim4.marketing.entity.GoodsRecommend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月04日 16:00
 */
public interface GoodsRecommendRepository extends JpaRepository<GoodsRecommend,Long>, JpaSpecificationExecutor<GoodsRecommend> {

    List<GoodsRecommend> findByPlaceIdAndIdInAndDeleted(String placeId,List<Long> ids,int deleted);

    List<GoodsRecommend> findByPlaceIdAndDeleted(String placeId,int deleted);

    // 根据place_id逻辑删除
    @Modifying
    @Transactional
    @Query(value = "UPDATE goods_recommend SET deleted = 1,updated = now() WHERE place_id = :placeId and deleted = 0",nativeQuery = true)
    void logicDeleteByPlaceId(@Param("placeId") String placeId);
}
