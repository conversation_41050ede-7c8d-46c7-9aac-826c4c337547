package com.rzx.dim4.marketing.entity;

import com.rzx.dim4.base.bo.marketing.LogDeviceLoginBO;
import com.rzx.dim4.base.bo.marketing.MeituanTokenBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 第三方设备（机位图、大屏等）登录记录
 * <AUTHOR>
 * @date 2025年05月14日 14:38
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "log_device_login", indexes = { @Index(name = "idx_goods_receipt_place_id", columnList = "place_id"), })
public class LogDeviceLogin extends BaseEntity {

    @Column(name = "place_id", length = 14, nullable = false, updatable = false)
    private String placeId; // 场所ID

    @Column(name = "account_id", nullable = false)
    private String accountId; // 登录人id

    @Column(name = "account_name", nullable = false)
    private String accountName; // 登录人名称

    @Column(name = "uuid", length = 100)
    private String uuid;

    public LogDeviceLoginBO toBO() {
        LogDeviceLoginBO bo = new LogDeviceLoginBO();
        BeanUtils.copyProperties(this, bo);
        return bo;
    }

}
