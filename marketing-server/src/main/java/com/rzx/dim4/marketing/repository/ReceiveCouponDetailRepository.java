package com.rzx.dim4.marketing.repository;

import com.rzx.dim4.marketing.entity.ReceiveCouponDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024年08月13日 18:23
 */
public interface ReceiveCouponDetailRepository extends JpaRepository<ReceiveCouponDetail, Long>, JpaSpecificationExecutor<ReceiveCouponDetail> {


    Optional<ReceiveCouponDetail> findTop1ByOrderByIdDesc();

    Optional<ReceiveCouponDetail> findByPlaceIdAndCouponDetailIdAndDeleted(String placeId, String couponDetailId, int deleted);

    Optional<ReceiveCouponDetail> findByPlaceIdAndIdNumberAndCouponCodeAndDeleted(String placeId, String couponDetailId,String couponCode, int deleted);

    List<ReceiveCouponDetail> findByPlaceIdAndIdNumberAndDeleted(String placeId, String idNumber, int deleted);

    List<ReceiveCouponDetail> findByPlaceIdAndIdIn(String placeId, List<Long> ids);

    @Modifying(clearAutomatically=true, flushAutomatically = true)
    @Transactional
    @Query(value = "UPDATE log_coupon SET deleted =1 ,updated = now() WHERE place_id = :placeId and id in (:ids)", nativeQuery = true)
    int batchRemove(@Param("placeId") String placeId, @Param("ids") List<Long> ids);

    List<ReceiveCouponDetail> findByDeletedAndStatusAndEndTimeLessThan(int deleted, int status, LocalDateTime endTime);

    List<ReceiveCouponDetail> findByPlaceIdAndCouponIdInAndStatusAndDeleted(String placeId,List<String> couponIds,int status,int deleted);

    List<ReceiveCouponDetail> findByPlaceIdAndIdNumberAndStatusAndDeleted(String placeId, String idNumber,int status, int deleted);

    // 查询三天后过期的优惠券
    List<ReceiveCouponDetail> findByDeletedAndStatusAndEndTimeBetween(int deleted, int status, LocalDateTime start, LocalDateTime end);
}
