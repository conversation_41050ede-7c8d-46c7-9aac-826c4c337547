package com.rzx.dim4.marketing.web.controller.web.shop;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.rzx.dim4.base.bo.marketing.GoodsPurchaseOrderBO;
import com.rzx.dim4.base.bo.marketing.GoodsPurchaseOrderListBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.marketing.entity.GoodsPurchaseOrder;
import com.rzx.dim4.marketing.entity.GoodsPurchaseOrderList;
import com.rzx.dim4.marketing.entity.GoodsSuppliers;
import com.rzx.dim4.marketing.service.shop.GoodsPurchaseOrderListService;
import com.rzx.dim4.marketing.service.shop.GoodsPurchaseOrderService;
import com.rzx.dim4.marketing.service.shop.GoodsSuppliersService;
import com.rzx.dim4.marketing.util.TokenUtil;
import com.rzx.dim4.marketing.util.market.ExcelUtils;
import com.rzx.dim4.marketing.web.controller.web.vo.ExportGoodsPurchaseOrderListCellVO;
import com.rzx.dim4.marketing.web.controller.web.vo.ExportGoodsPurchaseOrderListVO;
import com.rzx.dim4.marketing.web.controller.web.vo.ExportGoodsPurchaseOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月08日 14:46
 *
 * 查询、详情、新增、导出采购订单
 */
@Slf4j
@RestController
@RequestMapping("/marketing/goodsPurchaseOrder")
public class GoodsPurchaseOrderController {

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private GoodsSuppliersService goodsSuppliersService;

    @Autowired
    private GoodsPurchaseOrderService goodsPurchaseOrderService;

    @Autowired
    private GoodsPurchaseOrderListService goodsPurchaseOrderListService;


    /**
     * 保存采购订单
     * @param request
     * @param goodsPurchaseOrderBO
     * @return
     */
    @PostMapping("/save")
    public GenericResponse<ListDTO<GoodsPurchaseOrderBO>> saveGoodsPurchaseOrder(HttpServletRequest request,
                                                                                 @RequestBody @Validated GoodsPurchaseOrderBO goodsPurchaseOrderBO) {

        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);
        if (StringUtils.isEmpty(goodsPurchaseOrderBO.getPlaceId()) && webLoginAccount.getType() != 6) {
            goodsPurchaseOrderBO.setPlaceId(webLoginAccount.getPlaceId());
        }
        log.info("场所:::{} 用户:::{}，保存采购订单数据为:::{}",goodsPurchaseOrderBO.getPlaceId(),webLoginAccount.getAccountId(), JSONObject.toJSONString(goodsPurchaseOrderBO));

        if (StringUtils.isEmpty(goodsPurchaseOrderBO.getPlaceId()) ||  StringUtils.isEmpty(goodsPurchaseOrderBO.getSupplierId()) ||  StringUtils.isEmpty(goodsPurchaseOrderBO.getPlaceId()) ) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        Optional<GoodsSuppliers> byPlaceIdAndSupplierId = goodsSuppliersService.findByPlaceIdAndSupplierId(goodsPurchaseOrderBO.getPlaceId(), goodsPurchaseOrderBO.getSupplierId());
        if(!byPlaceIdAndSupplierId.isPresent()){
            return new GenericResponse<>(ServiceCodes.SHOP_SUPPLIER_NOT_EXIST);
        }
        goodsPurchaseOrderBO.setCreater(Long.parseLong(webLoginAccount.getAccountId()));
        goodsPurchaseOrderService.save(goodsPurchaseOrderBO);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 查询采购订单详情
     * @param placeId 场所id，必填
     * @param purchaseOrderNum 采购订单编号，必填
     * @return
     */
    @GetMapping("/findDetails")
    public GenericResponse<ObjDTO<GoodsPurchaseOrderBO>> findGoodsPurchaseOrderDetails(@RequestParam String placeId, @RequestParam String purchaseOrderNum) {
        if(StringUtils.isEmpty(placeId) || (StringUtils.isEmpty(purchaseOrderNum) ) ){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        Optional<GoodsPurchaseOrder> goodsPurchaseOrderListOpt = goodsPurchaseOrderService.findByPlaceIdAndPurchaseOrderNum(placeId,purchaseOrderNum);
        if (!goodsPurchaseOrderListOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.MARKET_GOODS_PURCHASE_ORDER_NOT_FOUND);
        }
        GoodsPurchaseOrder goodsPurchaseOrder = goodsPurchaseOrderListOpt.get();
        GoodsPurchaseOrderBO goodsPurchaseOrderBO = goodsPurchaseOrder.toBO();
        // 查询采购订单的商品列表并赋值
        List<GoodsPurchaseOrderList> goodsPurchaseOrderLists = goodsPurchaseOrderListService.findByPlaceIdAndPurchaseOrderNum(placeId,purchaseOrderNum);
        List<GoodsPurchaseOrderListBO> goodsPurchaseOrderListBOS = goodsPurchaseOrderLists.stream().map(GoodsPurchaseOrderList::toBO).collect(Collectors.toList());
        goodsPurchaseOrderBO.setGoodsPurchaseOrderListBOS(goodsPurchaseOrderListBOS);

        Optional<GoodsSuppliers> byPlaceIdAndSupplierId = goodsSuppliersService.findByPlaceIdAndSupplierId(goodsPurchaseOrder.getPlaceId(), goodsPurchaseOrder.getSupplierId());
        if(byPlaceIdAndSupplierId.isPresent()){
            goodsPurchaseOrderBO.setSupplierName(byPlaceIdAndSupplierId.get().getSupplierName());
        }
        return new GenericResponse<>(new ObjDTO<>(goodsPurchaseOrderBO));

    }


    /**
     * 分页查询采购订单列表
     * @param placeId
     * @param startDate
     * @param endDate
     * @param status
     * @param size
     * @param start
     * @param orderColumns
     * @param order
     * @return
     */
    @GetMapping("/findPageList")
    public GenericResponse<PagerDTO<GoodsPurchaseOrderBO>> pageGo(
            @RequestParam(name = "placeId") String placeId,
            @RequestParam(value = "startDate", defaultValue = "") String startDate,
            @RequestParam(name = "endDate", defaultValue = "") String endDate,
            @RequestParam(name = "status", defaultValue = "") String status,
            @RequestParam(name = "start", defaultValue = "0") int start,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order) {

        int page = (start == 0 ? start : (start / size));
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.fromString(order), orderColumns);
        Map<String, Object> map = new HashMap<>();
        map.put("placeId", placeId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("status", status);

        Page<GoodsPurchaseOrder> pager = goodsPurchaseOrderService.findAll(map, pageable);
        List<GoodsPurchaseOrderBO> bos = pager.getContent().stream().map(GoodsPurchaseOrder::toBO).collect(Collectors.toList());
        // 获取供应商名称
        bos.forEach(e->{
            Optional<GoodsSuppliers> optionalGoodsSuppliers = goodsSuppliersService.findBySupplierIdAndPlaceId(e.getSupplierId(),placeId);
            // 查询供应商信息
            if (!optionalGoodsSuppliers.isPresent()) {
                e.setSupplierName("");
            } else {
                GoodsSuppliers goodsSuppliers = optionalGoodsSuppliers.get();
                e.setSupplierName(goodsSuppliers.getSupplierName());
            }
            GenericResponse<ObjDTO<PlaceAccountBO>> placeAccountByPlaceIdAndAccountId = placeServerService.findPlaceAccountByPlaceIdAndAccountId(placeId, e.getCreater() + "");
            if(placeAccountByPlaceIdAndAccountId.isResult()){
                e.setCreaterName(placeAccountByPlaceIdAndAccountId.getData().getObj().getAccountName());
            }
        });
        return new GenericResponse<>(new PagerDTO<>((int) pager.getTotalElements(), bos));

    }


    @RequestMapping(value = "/exportDetails", method = RequestMethod.GET)
    public void exportGoodsPurchaseOrder(HttpServletRequest request,
                                         @RequestParam(name = "placeId") String placeId,
                                         @RequestParam(name = "purchaseOrderNum") String purchaseOrderNum,
                                   HttpServletResponse response) {

//        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);

        List<ExportGoodsPurchaseOrderVO> vos = new ArrayList<>();

        List<ExportGoodsPurchaseOrderListCellVO> vos2 = new ArrayList<>();
        List<ExportGoodsPurchaseOrderListVO> vos3 = new ArrayList<>();

        Optional<GoodsPurchaseOrder> byPlaceIdAndPurchaseOrderNum = goodsPurchaseOrderService.findByPlaceIdAndPurchaseOrderNum(placeId, purchaseOrderNum);
        if(!byPlaceIdAndPurchaseOrderNum.isPresent()){
            return;
        }
        GoodsPurchaseOrder goodsPurchaseOrder = byPlaceIdAndPurchaseOrderNum.get();
        GoodsPurchaseOrderBO goodsPurchaseOrderBO = goodsPurchaseOrder.toBO();

        Optional<GoodsSuppliers> byPlaceIdAndSupplierId = goodsSuppliersService.findByPlaceIdAndSupplierId(goodsPurchaseOrder.getPlaceId(), goodsPurchaseOrder.getSupplierId());
        String supplierName = "";
        if(byPlaceIdAndSupplierId.isPresent()){
            supplierName = byPlaceIdAndSupplierId.get().getSupplierName();
        }

        GenericResponse<ObjDTO<PlaceAccountBO>> placeAccountByPlaceIdAndAccountId = placeServerService.findPlaceAccountByPlaceIdAndAccountId(placeId, goodsPurchaseOrderBO.getCreater() + "");
        String createrName = "";
        if(placeAccountByPlaceIdAndAccountId.isResult()){
            createrName = placeAccountByPlaceIdAndAccountId.getData().getObj().getAccountName();
        }
        vos.add(new ExportGoodsPurchaseOrderVO(createrName, supplierName,goodsPurchaseOrderBO.getPayTypeName() , Double.valueOf(goodsPurchaseOrderBO.getPurchaseAmount()) /100, goodsPurchaseOrderBO.getPayAmount() /100, ""));

        vos2.add(new ExportGoodsPurchaseOrderListCellVO("",  "", ""));
        vos2.add(new ExportGoodsPurchaseOrderListCellVO(" ",  " ", " "));
        vos2.add(new ExportGoodsPurchaseOrderListCellVO("商品名称",  "发生数量", "发生金额"));

//        vos3.add(new ExportGoodsPurchaseOrderListVO("可乐",  100, 200));

        List<GoodsPurchaseOrderList> goodsPurchaseOrderLists = goodsPurchaseOrderListService.findByPlaceIdAndPurchaseOrderNum(placeId, purchaseOrderNum);
        goodsPurchaseOrderLists.forEach(e-> {
            ExportGoodsPurchaseOrderListVO vo = new ExportGoodsPurchaseOrderListVO();
            vo.setGoodsName(e.getGoodsName());
            vo.setPurchaseNumber(e.getPurchaseNumber());
            vo.setPurchasePrice(Double.valueOf(e.getPurchasePrice()*e.getPurchaseNumber()) / 100);
            vos3.add(vo);
        });

        try {
//            ExcelUtils.writeExcel(response, vos, "采购订单详情", "采购订单详情", ExportGoodsPurchaseOrderVO.class, false, null);
            ExcelUtils.writeExcel2(response, vos,vos2,vos3, "采购订单详情", "采购订单详情", ExportGoodsPurchaseOrderVO.class, false, null);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }


}
