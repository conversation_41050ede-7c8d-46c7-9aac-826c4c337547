package com.rzx.dim4.marketing.service.statistics;

import com.rzx.dim4.base.bo.marketing.statistics.StatisticsGoodsSaleByDayBO;
import com.rzx.dim4.base.bo.marketing.statistics.StatisticsGoodsSaleByDayQueryBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.MoneyCountUtil;
import com.rzx.dim4.base.vo.marketing.StatisticsGoodsSaleByDayVO;
import com.rzx.dim4.base.vo.marketing.StatisticsSlowMovingGoodsExportVO;
import com.rzx.dim4.base.vo.marketing.StatisticsSlowMovingGoodsVO;
import com.rzx.dim4.marketing.entity.StorageGoods;
import com.rzx.dim4.marketing.service.shop.StorageGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品滞销汇总服务
 *
 * <AUTHOR> hwx
 * @since 2025/2/10 9:05
 */
@Slf4j
@Service
public class StatisticsSlowMovingGoodsService {

    @Autowired
    private StatisticsGoodsSaleByDayService statisticsGoodsSaleByDayService;
    @Autowired
    private StorageGoodsService storageGoodsService;

    /**
     * 查询商品滞销数据
     *
     * @param queryBo 查询条件
     * @param pageRequest 分页参数
     * @return 分页数据
     */
    public GenericResponse<PagerDTO<?>> querySlowMovingGoods(StatisticsGoodsSaleByDayQueryBO queryBo, PageRequest pageRequest) {
        //根据商品分类统计商品销售情况
        List<StatisticsGoodsSaleByDayBO> bos = getStatisticsGoodsSaleByDayBOS(queryBo, pageRequest);
        if (CollectionUtils.isEmpty(bos)) {
            return new GenericResponse<>(new PagerDTO<>(0, new ArrayList<>()));
        }

        List<StatisticsGoodsSaleByDayVO> vos = statisticsGoodsSaleByDayService.toVO(bos);

        int count = statisticsGoodsSaleByDayService.countStatisticsGoodsSaleByDay(queryBo);

        return new GenericResponse<>(new PagerDTO<>(count, vos));
    }

    private List<StatisticsGoodsSaleByDayBO> getStatisticsGoodsSaleByDayBOS(StatisticsGoodsSaleByDayQueryBO queryBo, Pageable pageRequest) {
        List<StatisticsGoodsSaleByDayBO> statisticsGoodsSaleByDayBOS = statisticsGoodsSaleByDayService.queryStatisticsGoodsSaleByDayGroupByGoodsId(queryBo, pageRequest);
        for (StatisticsGoodsSaleByDayBO bo : statisticsGoodsSaleByDayBOS) {
            if (bo.getGoodsCategory() != 1) {  //虚拟商品算净销量和收入时不算上退货
                bo.setCountSale(bo.getCountSale() - bo.getSumRefundTotal());
                bo.setSumSaleTotal(bo.getSumSaleTotal() - bo.getSumRefundTotal());
            }
        }
        return statisticsGoodsSaleByDayBOS;
    }

    /**
     * 统计商品滞销汇总数据
     *
     * @param queryBo 查询条件
     * @return 汇总数据
     */
    public GenericResponse<?> totalSlowMovingGoods(StatisticsGoodsSaleByDayQueryBO queryBo) {
        List<StatisticsGoodsSaleByDayBO> bos = getStatisticsGoodsSaleByDayBOS(queryBo, Pageable.unpaged());
        if (CollectionUtils.isEmpty(bos)) {
            return new GenericResponse<>();
        }

        StatisticsSlowMovingGoodsVO vo = new StatisticsSlowMovingGoodsVO();
        int totalCountSale = 0; //累计销售数
        int totalGrossProfit = 0; //累计毛利
        for (StatisticsGoodsSaleByDayBO bo : bos) {
            totalCountSale += bo.getCountSale();
            totalGrossProfit += bo.calculateGrossProfit();
        }
        vo.setTotalCountSale(totalCountSale);
        vo.setTotalGrossProfit(totalGrossProfit);

        List<String> goodsIds = statisticsGoodsSaleByDayService.queryStatisticsGoodsId(queryBo);
        if (CollectionUtils.isNotEmpty(goodsIds)) {
            vo.setSlowGoodsNum(goodsIds.size());
            List<StorageGoods> storageGoods = storageGoodsService.findByPlaceIdAndGoodsIdIn(queryBo.getPlaceId(), goodsIds);
            if (CollectionUtils.isNotEmpty(storageGoods)) {
                //库存成品总计
                int sumGoodsStocksNum = storageGoods.stream().mapToInt(StorageGoods::getGoodsStocksNum).sum();
                vo.setGoodsStocksNum(sumGoodsStocksNum);
            }
        }

        return new GenericResponse<>(new ObjDTO<>(vo));
    }

    /**
     * 导出商品滞销数据
     *
     * @param queryBo 查询条件
     * @param pageRequest 分页参数
     * @return 导出数据列表
     */
    public List<StatisticsSlowMovingGoodsExportVO> exportSlowMovingGoods(StatisticsGoodsSaleByDayQueryBO queryBo, PageRequest pageRequest) {
        GenericResponse<PagerDTO<?>> response = querySlowMovingGoods(queryBo, pageRequest);
        List<StatisticsGoodsSaleByDayVO> list = (List<StatisticsGoodsSaleByDayVO>) response.getData().getList();
        return list.stream()
                .map(v -> {
                    StatisticsSlowMovingGoodsExportVO exportVo = new StatisticsSlowMovingGoodsExportVO();
                    BeanUtils.copyProperties(v, exportVo);
                    exportVo.setUnitPrice(MoneyCountUtil.price(v.getUnitPrice()));
                    exportVo.setAvgSalePrice(MoneyCountUtil.price(v.getAvgSalePrice()));
                    exportVo.setSumSaleTotal(MoneyCountUtil.price(v.getSumSaleTotal()));
                    exportVo.setGrossProfit(MoneyCountUtil.price(v.getGrossProfit()));
                    return exportVo;
                })
                .collect(Collectors.toList());
    }
}