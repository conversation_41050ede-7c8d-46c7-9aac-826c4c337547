package com.rzx.dim4.marketing.service.coupon;

import com.alibaba.fastjson.JSONObject;
import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.billing.third.OnlineBO;
import com.rzx.dim4.base.bo.marketing.GoodsBO;
import com.rzx.dim4.base.bo.notify.polling.CouponBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.TopupAndDeductionBusinessBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.CouponOperationType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.billing.*;
import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import com.rzx.dim4.base.utils.BeanCoverUtil;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.SpringUtils;
import com.rzx.dim4.base.vo.marketing.CouponAmountVO;
import com.rzx.dim4.marketing.entity.*;
import com.rzx.dim4.marketing.event.GoodsOrderEvent;
import com.rzx.dim4.marketing.repository.DiscountCouponRepository;
import com.rzx.dim4.marketing.service.douyin.MarketDouyinStoreService;
import com.rzx.dim4.marketing.service.meituan.MarketMeituanStoreService;
import com.rzx.dim4.marketing.service.shop.*;
import com.rzx.dim4.marketing.util.meituan.MeituanRequest;
import com.rzx.dim4.marketing.util.shop.GoodsUtil;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzx.dim4.base.utils.DateTimeUtils.isInDateTimeRange;

/**
 * <AUTHOR>
 * @date 2024年08月12日 14:35
 */
@Service
@Slf4j
public class DiscountCouponService {

    @Autowired
    private DiscountCouponRepository discountCouponRepository;

    @Autowired
    private BillingRulePackageTimeApi billingRulePackageTimeApi;

    @Autowired
    private MarketDouyinStoreService marketDouyinStoreService;

    @Autowired
    private MeituanRequest meituanRequest;

    @Autowired
    private ReceiveCouponDetailService receiveCouponDetailService;

    @Autowired
    private LogCouponOperationService logCouponOperationService;

    @Autowired
    private BillingTopupRuleApi billingTopupRuleApi;
    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    BillingServerService billingServerService;

    @Autowired
    BillingOnlineApi billingOnlineApi;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    private BuyGiftsService buyGiftsService;

    @Autowired
    private BuyGiftsGoodsService buyGiftsGoodsService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private ShopConfigService shopConfigService;

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private OrderGoodsService orderGoodsService;

    @Autowired
    private StorageGoodsService storageGoodsService;

    @Autowired
    private BillingCardApi billingCardApi;

    @Autowired
    private WechatMessageApi wechatMessageApi;

    @Autowired
    private LogBuyGiftsVerifyRecordService logBuyGiftsVerifyRecordService;

    @Autowired
    private LogRoomApi logRoomApi;

    @Autowired
    private InviteApi inviteApi;

    @Autowired
    private NotifyServerService notifyServerService;

    public DiscountCoupon save(DiscountCoupon discountCoupon){
        if(StringUtils.isEmpty(discountCoupon.getCouponId())){
            discountCoupon.setCreated(LocalDateTime.now());
            discountCoupon.setCouponId(buildCouponId(discountCoupon.getPlaceId()));
        }else{
            discountCoupon.setUpdated(LocalDateTime.now());
        }
        return discountCouponRepository.save(discountCoupon);
    }

    @Synchronized
    private String buildCouponId(String placeId) {
        Optional<DiscountCoupon> lastDate = findTop1ByPlaceId(placeId);
        int indexInt = 3001;
        if (lastDate.isPresent()) {
            String lastCouponId = lastDate.get().getCouponId();
            indexInt = Integer.parseInt(lastCouponId) + 1;
        }
        return String.valueOf(indexInt);
    }

    public Optional<DiscountCoupon> findTop1ByPlaceId(String placeId){
        return discountCouponRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
    }

    public Optional<DiscountCoupon> findByPlaceIdAndCouponIdAndDeleted(String placeId,String couponId){
        return discountCouponRepository.findByPlaceIdAndCouponIdAndDeleted(placeId,couponId,0);
    }

    public Optional<DiscountCoupon> findByPlaceIdAndCouponId(String placeId,String couponId){
        return discountCouponRepository.findByPlaceIdAndCouponId(placeId,couponId);
    }

    public List<DiscountCoupon> findByPlaceIdAndCouponIdIn(String placeId,List<String> couponIds){
        return discountCouponRepository.findByPlaceIdAndCouponIdIn(placeId,couponIds);
    }

    public int batchRemove(String placeId,List<Long> ids){
        return discountCouponRepository.batchRemove(placeId,ids);
    }

    public int updateStatus(String placeId,String couponId,int status){
        return discountCouponRepository.updateStatus(placeId,couponId,status);
    }

    public List<DiscountCoupon> findByPlaceIdAndCouponName(String placeId,String couponName){
        return discountCouponRepository.findByPlaceIdAndCouponNameLikeAndDeleted(placeId,couponName,0);
    }

    public List<DiscountCoupon> findByPlaceIdAndIdIn(String placeId,List<Long> ids){
        return discountCouponRepository.findByPlaceIdAndIdInAndDeleted(placeId,ids,0);
    }


    public List<DiscountCoupon> findByPlaceIdAndDeletedAndStatus(String placeId,int status){
        return discountCouponRepository.findByPlaceIdAndDeletedAndStatus(placeId,0,status);
    }


    public Page<DiscountCoupon> findAll(Map<String, String> map, Pageable pageable) {
        return discountCouponRepository.findAll(new Specification<DiscountCoupon>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<DiscountCoupon> root, CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                List<Predicate> predicateList = new ArrayList<>();
                predicateList.add(cb.equal(root.get("deleted"), 0));
                // 场所
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    predicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
                }
                // 优惠券id
                if (map.containsKey("couponTypeId") && !StringUtils.isEmpty(map.get("couponTypeId"))) {
                    predicateList.add(cb.equal(root.get("couponTypeId").as(String.class), map.get("couponTypeId")));
                }
                // 优惠券名称
                if (map.containsKey("couponName") && !StringUtils.isEmpty(map.get("couponName"))) {
                    predicateList.add(cb.like(root.get("couponName").as(String.class),"%"+ map.get("couponName")+"%"));
                }
                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return cb.and(predicateList.toArray(predicateArr));
            }
        }, pageable);
    }

    @Transactional
    public GenericResponse<?> certificateVerify(String encryptedCode,String placeId,String idName,String idNumber,String buyGiftsId,SourceType sourceType,
                                                Long creater,String createrName,String shiftId,SourceType operatorSourceType,String clientId,String remark,
                                                String cashierId){

        //5秒防止重复提交
        String requestRepetitionVerify = "market:"+placeId+"_"+idNumber+"_"+encryptedCode;
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(requestRepetitionVerify))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(requestRepetitionVerify, requestRepetitionVerify, 5, TimeUnit.SECONDS);

        if(shiftId == null && operatorSourceType != SourceType.CLIENT){
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(placeId, idNumber);
        if(!billingCardResponse.isResult() || null == billingCardResponse.getData() || null == billingCardResponse.getData().getObj()){
            return new GenericResponse<>(ServiceCodes.MARKET_MEMBER_EVENT_TYPE_ERROR);
        }
        BillingCardBO billingCardBO = billingCardResponse.getData().getObj();
        LocalDateTime now = LocalDateTime.now();

        //查询买赠数据详情
        Optional<BuyGifts> buyGiftsOptional = buyGiftsService.findByPlaceIdAndBuyGiftsId(placeId, buyGiftsId);
        if(!buyGiftsOptional.isPresent()){
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BuyGifts buyGifts = buyGiftsOptional.get();
        //校验买赠是否失效
        boolean b = GoodsUtil.verifyBuyGifts(buyGifts, now);
        if(buyGifts.getStatus() == 1 || !b){
            return new GenericResponse<>(ServiceCodes.MARKET_BUYGIFTS_STATUS_ERROR);
        }
        if(buyGifts.getEventGoods().equals("-1")){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_ERROR);
        }
        if(!buyGifts.getCardTypeIds().contains(billingCardBO.getCardTypeId())){
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
        }
        if(!GoodsUtil.verifyBuyGifts(buyGifts,now)){
            return new GenericResponse<>(ServiceCodes.MARKET_BUYGIFTS_STATUS_ERROR);
        }
        Optional<ShopConfig> shopConfigOptional = shopConfigService.findByPlaceId(placeId);
        ShopConfig shopConfig = shopConfigOptional.get();
        String rackId = null;
        if(shopConfig.getStoreType() == 0){
            //多仓库
            rackId = "100000"; //现阶段默认100000
        }else if(shopConfig.getStoreType() == 1){
            //单仓库
            rackId = "000000";
        }
        //查询买赠赠送的商品列表
        List<BuyGiftsGoods> giftsGoodsList = buyGiftsGoodsService.findByPlaceIdAndBuyGiftsId(placeId, buyGiftsId);

        List<GoodsBO> goodsBOList = new ArrayList<>(); //这里存储本次核销所有需要消费的商品，goodsNum为消耗的数量
        List<String> goodsIds = new ArrayList<>();
        List<Goods> goodsList = new ArrayList<>();
        //查询赠品商品信息
        if(giftsGoodsList.size() > 0){
            goodsIds = giftsGoodsList.stream().map(BuyGiftsGoods::getGoodsId).collect(Collectors.toList());
            goodsList = goodsService.findByPlaceIdAndGoodsIdIn(placeId, goodsIds);

            goodsBOList = goodsList.stream().map(Goods::toBO).collect(Collectors.toList());
            //计算商品数量
            for (GoodsBO goodsBO : goodsBOList) {
                for (BuyGiftsGoods buyGiftsGoods : giftsGoodsList) {
                    if(goodsBO.getGoodsId().equals(buyGiftsGoods.getGoodsId())){
                        goodsBO.setGoodsNum(goodsBO.getGoodsNum() + buyGiftsGoods.getGoodsNum());
                        goodsBO.setBuyGiftPrice(buyGiftsGoods.getGoodsPrice());
                        goodsBO.setPresent(1);
                    }
                }
            }
        }
        //查询主商品信息
        goodsIds = Arrays.asList(buyGifts.getEventGoods().split(","));
        if(goodsIds.size() > 0){
            goodsList = goodsService.findByPlaceIdAndGoodsIdIn(placeId, goodsIds);
            for (Goods goods : goodsList) {
                GoodsBO goodsBO = goods.toBO();
                goodsBO.setGoodsNum(1);
                goodsBO.setBuyGiftPrice(0);
                goodsBO.setPresent(0);
                goodsBOList.add(goodsBO);
            }
        }

        //用户存储优惠券商品，后续不需要再次查询
        Map<String,DiscountCoupon> couponMap = new HashMap<>();
        List<DiscountCoupon> couponList = new ArrayList<>();
        String ruleId = null;
        //校验商品售卖时间，售卖数量，库存
        for (GoodsBO goodsBO : goodsBOList) {
            if(goodsBO.getGoodsCategory() == 3 || goodsBO.getGoodsCategory() == 4){
                if(StringUtils.isEmpty(goodsBO.getCouponId()) && StringUtils.isEmpty(goodsBO.getPackageRuleId())){
                    return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_ERROR);
                }
                if(goodsBO.getGoodsCategory() == 4){
                    ruleId = goodsBO.getPackageRuleId();
                }else{
                    //优惠券商品
                    String couponId = goodsBO.getCouponId();
                    Optional<DiscountCoupon> byPlaceIdAndCouponId = findByPlaceIdAndCouponIdAndDeleted(placeId, couponId);
                    if(!byPlaceIdAndCouponId.isPresent()){
                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_NOT_FOUND);
                    }
                    DiscountCoupon discountCoupon = byPlaceIdAndCouponId.get();
                    if(discountCoupon.getStatus() == 1){
                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_DISABLED);
                    }
                    int week = now.getDayOfWeek().getValue();
                    if(!discountCoupon.getEffectiveWeekDays().contains(week+"")){ //校验是否符合使用时间
                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_INVALID);
                    }
                    if(discountCoupon.getFailureTimeType() == 2){
                        if(now.compareTo(discountCoupon.getStartTime()) < 0){
                            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_INVALID);
                        }
                        if(now.compareTo(discountCoupon.getEndTime()) > 0){
                            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_INVALID);
                        }
                    }
                    ruleId = discountCoupon.getRuleId();

                    if(0 != discountCoupon.getLimitNum()){
                        //查询使用数量是否超过 limitNum
                        //默认日
                        LocalDateTime verStartTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
                        LocalDateTime verEndTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
                        if(2 == discountCoupon.getLimitType()){
                            //周
                            verStartTime = now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
                            verEndTime = now.with(DayOfWeek.SUNDAY).toLocalDate().atTime(23, 59, 59);
                        }else if(3 == discountCoupon.getLimitType()){
                            //月
                            verStartTime = now.with(TemporalAdjusters.firstDayOfMonth()).toLocalDate().atStartOfDay();
                            verEndTime = now.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().atTime(23, 59, 59);
                        }else if(4 == discountCoupon.getLimitType()){
                            //永久（年）
                            verStartTime = now.minus(365, ChronoUnit.DAYS);
                            verEndTime = now;
                        }
                        List<LogCouponOperation> toDayAllUseCoupon = logCouponOperationService.findByPlaceIdAndCouponIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, couponId,
                                CouponOperationType.USE_COUPON, verStartTime,verEndTime,idNumber);
                        if(toDayAllUseCoupon.size() >= discountCoupon.getLimitNum()){
                            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_EXCEED_THE_LIMIT);
                        }
                        int residueNum = discountCoupon.getLimitNum() - toDayAllUseCoupon.size();//剩余次数
                        if( residueNum < goodsBO.getGoodsNum()){
                            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_EXCEED_THE_LIMIT);
                        }
                    }
                    couponMap.put(couponId,discountCoupon);
                    couponList.add(discountCoupon);
                }

                if(!StringUtils.isEmpty(ruleId)){

                    GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime = billingServerService.queryBillingRulePackageTime(placeId, ruleId);
                    if(!queryBillingRulePackageTime.isResult()){
                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_RULE_NOT_FOUND);
                    }
                    BillingRulePackageTimeBO obj = queryBillingRulePackageTime.getData().getObj();

                    checkPackageTimeAvailable(obj,billingCardBO);
//                    LocalTime localTime = LocalTime.now();
//                    if(DateTimeUtils.timeFormatFloat(Time.valueOf(localTime)) - obj.getStartTime() < 0){
//                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_TIME_VERIFY);
//                    }
//                    if(DateTimeUtils.timeFormatFloat(Time.valueOf(localTime)) - obj.getEndTime() > 0){
//                        return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_TIME_VERIFY);
//                    }
                    GenericResponse<ObjDTO<InviteOnlineBO>> byPlaceIdAndCardId = inviteApi.findByPlaceIdAndCardId(placeId, billingCardBO.getCardId());
                    if(byPlaceIdAndCardId.isResult()){
                        return new GenericResponse<>(ServiceCodes.BILLING_INVITE_PACKAGE_NOT_SUPPORT);
                    }

//                    //校验是否正在包时中上机
                    GenericResponse<ObjDTO<OnlineBO>> objDTOGenericResponse = billingOnlineApi.queryOnlineInfo(placeId, idNumber);
                    if(objDTOGenericResponse.isResult() ){
                        String areaIds = obj.getAreaIds();
                        OnlineBO onlineBO = objDTOGenericResponse.getData().getObj();
                        String onlineAreaId = onlineBO.getAreaId();

                        if (!areaIds.contains(onlineAreaId)) {
                            log.info("checkPackageArea 包时区域不匹配;areaIds{},当前所在区域,onlineAreaId{}",  areaIds, onlineAreaId);
                            return new GenericResponse<>(ServiceCodes.BILLING_PT_AREA_CONFLICT);
                        }

                        // 查询是否在包间上机
                        GenericResponse<ObjDTO<LogRoomBO>> logRoomByPlaceIdAndCardId = logRoomApi.findLogRoomByPlaceIdAndCardId(placeId,onlineBO.getCardId());
                        if (logRoomByPlaceIdAndCardId.isResult()) {
                            LogRoomBO logRoomBO = logRoomByPlaceIdAndCardId.getData().getObj();
                            if (logRoomBO.getIsMaster() == 0) {
                                // 副卡在包间上机 不允许操作包时
                                log.info("checkPackageArea 包间副卡不允许操作包时;areaIds{},当前所在区域,onlineAreaId{}",  areaIds, onlineAreaId);
                                return new GenericResponse<>(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
                            }
                        }
                    }
                }

            }else{
                //固装或者虚拟商品
                boolean b1 = GoodsUtil.verifyGoodsCycle(goodsBO, now);
                if(!b1){
                    return new GenericResponse<>(ServiceCodes.SHOP_GOODS_NOT_IN_SALES_TIME);
                }
            }
        }
        long count = couponList.stream().filter(it -> !StringUtils.isEmpty(it.getRuleId())).count();
        if(count > 1){
            log.info("用户:::{} 单次核销优惠券时包时券数目大于1",idNumber);
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_ERROR);
        }


        //核销第三方优惠券
        Map<String,String> result = new HashMap<>();
        if(SourceType.DOUYIN.equals(sourceType)){
            GenericResponse<SimpleObjDTO> simpleObjDTOGenericResponse = marketDouyinStoreService.certificateVerify(encryptedCode, Dim4StringUtils.getUUIDWithoutHyphen(), placeId, idNumber);
            if(!simpleObjDTOGenericResponse.isResult()){
                return simpleObjDTOGenericResponse;
            }
            //核销成功，生成订单记录，并且直接完成订单发放商品
            result = simpleObjDTOGenericResponse.getData().getResult();
        }else if(SourceType.MEITUAN.equals(sourceType)){
            result = meituanRequest.certificateVerify(placeId,encryptedCode,1,creater+"",createrName);
            if(!result.get("code").equals("0")){
                return new GenericResponse<>("核销失败！"+result.get("msg"));
            }
        }else{
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_NOT_BIND);
        }

        CouponAmountVO couponAmountVO = new CouponAmountVO();
        try {
            String amountJson = stringRedisTemplate.opsForValue().get(encryptedCode);
            couponAmountVO = JSONObject.parseObject(amountJson, CouponAmountVO.class);
        }catch (Exception e){
            log.info("缓存中读取优惠券实际金额失败!"+e.getMessage());
        }

        //生成订单
        String orderId = "SHO" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        List<OrderGoods> orderGoodsList = new ArrayList<>(); //存储本次订单的消费商品
        int totalMoney = 0; //订单总金额
        int realMoney = 0; //实际总金额
        for (GoodsBO goodsBO : goodsBOList) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setCreated(now);
            orderGoods.setPlaceId(goodsBO.getPlaceId());
            orderGoods.setOrderId(orderId);
            orderGoods.setGoodsId(goodsBO.getGoodsId());
            orderGoods.setGoodsName(goodsBO.getGoodsName());
            orderGoods.setQuantity(goodsBO.getGoodsNum());
            orderGoods.setPresent(goodsBO.getPresent());
            orderGoods.setGoodsCategory(goodsBO.getGoodsCategory());
            if(goodsBO.getPresent() == 0){
                if(couponAmountVO.getPayAmount() > 0){
                    orderGoods.setUnitPrice(couponAmountVO.getPayAmount());
                    orderGoods.setDiscounts(couponAmountVO.getPaymentDiscountAmount()); //主商品折扣价格为0
                    realMoney += couponAmountVO.getPayAmount(); //计算实际总金额
                    totalMoney += couponAmountVO.getPayAmount();//计算订单总金额
                }else{
                    orderGoods.setUnitPrice(goodsBO.getUnitPrice());
                    orderGoods.setDiscounts(0); //主商品折扣价格为0
                    realMoney += goodsBO.getUnitPrice(); //计算实际总金额
                    totalMoney += goodsBO.getUnitPrice();//计算订单总金额
                }

            }else{
                orderGoods.setUnitPrice(0); //赠品销售价格为0
                orderGoods.setDiscounts(goodsBO.getBuyGiftPrice());
                totalMoney += goodsBO.getBuyGiftPrice();//计算订单总金额
            }
            orderGoods.setGoodsTypeId(goodsBO.getGoodsTypeId());
            orderGoods.setGoodsTypeName(goodsBO.getGoodsTypeName());
            if(goodsBO.getGoodsCategory() == 1 && goodsBO.getVirtualGoodsType() == 0){
                orderGoods.setInternetFeeId(buyGiftsId);
            }
            orderGoods.setMealsId("");
            if(goodsBO.getGoodsCategory() == 3){
                orderGoods.setGoodsQuota(goodsBO.getUnitPrice());
            }else{
                orderGoods.setGoodsQuota(goodsBO.getGoodsQuota());
            }
            orderGoods.setStatus(1);
            orderGoodsList.add(orderGoods);
        }
        //生成本次订单信息
        Orders orders = new Orders();
        orders.setCreated(now);
        orders.setPlaceId(placeId);
        orders.setOrderId(orderId);
        orders.setSourceType(operatorSourceType);
        orders.setClientId(clientId);
        orders.setTotalMoney(totalMoney);
        orders.setRealMoney(realMoney);
        orders.setRuleId(ruleId);
        if(SourceType.DOUYIN.equals(sourceType)){
            orders.setPayType(PayType.DOUYIN);
        }else{
            orders.setPayType(PayType.MEITUAN);
        }

        orders.setShiftId(shiftId);
        orders.setRemark(remark);
        orders.setCashierId(cashierId);
        orders.setIdNumber(billingCardBO.getIdNumber());
        orders.setIdName(billingCardBO.getIdName());
        orders.setStatus(1); //已付款
        orders.setCardId(billingCardBO.getCardId());
        orders.setCardTypeId(billingCardBO.getCardTypeId());
        orders.setStorageRackId(rackId);
        orders.setCreater(creater);
        orders.setCreaterName(createrName);
        orders.setIsMeals(0);
        orders.setOrderType(2);
        orders.setPayTime(now);
        orders.setBuyGiftsId(buyGiftsId);
        Orders saveOrder = ordersService.save(orders);
        orders.setRemark(SourceType.getDesc(operatorSourceType)+"自助核销-"+SourceType.getDesc(sourceType)+"优惠券-"+buyGifts.getBuyGiftsName());
        orderGoodsService.saveAll(orderGoodsList);

        //优惠券商品
        List<GoodsBO> couponGoodsBOs = goodsBOList.stream().filter(it-> it.getGoodsCategory() == 3).collect(Collectors.toList());
        //虚拟商品
        List<GoodsBO> virtualGoodsBOs = goodsBOList.stream().filter(it-> it.getGoodsCategory() == 1).collect(Collectors.toList());
        //固装商品
        List<GoodsBO> entityGoodsBO = goodsBOList.stream().filter(it-> it.getGoodsCategory() == 0).collect(Collectors.toList());
        //包时商品
        List<GoodsBO> packageGoodsBO = goodsBOList.stream().filter(it-> it.getGoodsCategory() == 4).collect(Collectors.toList());

        if(entityGoodsBO.size() > 0){
            //固装商品订单商品
            List<OrderGoods> entityOrderGoods = orderGoodsList.stream().filter(it -> it.getGoodsCategory() == 0).collect(Collectors.toList());
            //处理固装商品-扣库存
            Map<String,Integer> maps = new HashMap<>();
            List<Goods> entityGoods = BeanCoverUtil.converList(entityGoodsBO, Goods.class);
            for (OrderGoods orderGoods : entityOrderGoods) {
                if(maps.containsKey(orderGoods.getGoodsId())){
                    maps.put(orderGoods.getGoodsId(),maps.get(orderGoods.getGoodsId()) + orderGoods.getQuantity());
                }else{
                    maps.put(orderGoods.getGoodsId(),orderGoods.getQuantity());
                }
                //调整一下商品价格，改库存写日志时需要写入
                for (Goods goods : entityGoods) {
                    if(goods.getGoodsId().equals(orderGoods.getGoodsId())){
                        goods.setUnitPrice(orderGoods.getUnitPrice());
                        goods.setCostPrice(goods.getCostPrice());
                    }
                }
            }
            //货架不为空时扣库存
            if(!StringUtils.isEmpty(rackId)){
                log.info("用户:::{} {} 核销优惠券---使用固装商品扣除库存:::{} {} ",idNumber,placeId,rackId, JSONObject.toJSONString(maps));
//                ordersService.verifyOrderStorage(); //商品销售校验
                int returnCode = storageGoodsService.updateGoodsStocks(placeId, maps, saveOrder.getOrderId(), entityGoods,7, false, rackId,createrName,operatorSourceType);
                if (returnCode < 0) {
                    log.info("用户:::{} 固装商品:::{} 核销成功，但固装商品发放时发生异常",idNumber,JSONObject.toJSONString(entityGoodsBO));
                    //撤销核销
                    if(SourceType.DOUYIN.equals(sourceType)){
                        marketDouyinStoreService.cancelVerifyCoupon(result.get("certificateId"),result.get("verifyId"),placeId);
                    }else{
                        meituanRequest.syncCancelCertificateVerify(placeId,encryptedCode,result.get("verifyId"),creater+"",createrName);
                    }
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
                }
            }
        }

        if(virtualGoodsBOs.size() > 0){
            //处理虚拟商品-充值网费
            int topupCost = 0;//本金
            for (GoodsBO virtualGoodsBO : virtualGoodsBOs) {
                topupCost += (virtualGoodsBO.getGoodsQuota() * virtualGoodsBO.getGoodsNum());
            }
            //给用户充钱
            if(topupCost>0){
                log.info("用户:::{} {} 核销优惠券---使用虚拟商品充值网费:::{} {} ",idNumber,placeId,topupCost, JSONObject.toJSONString(virtualGoodsBOs));
                //计费卡加钱
                String requestTicket1 = Dim4StringUtils.getUUIDWithoutHyphen();
                stringRedisTemplate.opsForValue().set(requestTicket1, requestTicket1, 1, TimeUnit.MINUTES);
                GenericResponse<?> response = billingServerService.billingCardUpdateAccount(requestTicket1, billingCardBO.getPlaceId(), billingCardBO.getCardId(),
                        0, -topupCost, 3, operatorSourceType,null,orderId); //这里是奖励
                if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
                    log.info("用户:::{} 虚拟商品:::{} 核销成功，但虚拟商品充值时发生异常:::{}",idNumber,JSONObject.toJSONString(virtualGoodsBOs),response.getMessage());
                    //撤销核销
                    if(SourceType.DOUYIN.equals(sourceType)){
                        marketDouyinStoreService.cancelVerifyCoupon(result.get("certificateId"),result.get("verifyId"),placeId);
                    }else{
                        meituanRequest.syncCancelCertificateVerify(placeId,encryptedCode,result.get("verifyId"),creater+"",createrName);
                    }
                    throw new ServiceException(response.getMessage());
                }
            }
        }

        if(packageGoodsBO.size() > 0){
            //处理包时商品
            for (GoodsBO goodsBO : packageGoodsBO) {
                if(StringUtils.isEmpty(goodsBO.getPackageRuleId())){
                    return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_RULE_NOT_FOUND);
                }
                //包时商品
                String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
                stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
                GenericResponse<?> objDTOGenericResponse = billingRulePackageTimeApi.couponPackageTime(requestTicket2, placeId, billingCardBO.getCardId(), goodsBO.getPackageRuleId(), operatorSourceType.name(), shiftId, goodsBO.getGoodsName(),sourceType.name());
                if(!objDTOGenericResponse.isResult()){
                    log.info("用户:::{} 包时商品:::{} 核销成功，但生成包时时发生异常:::{}",idNumber,JSONObject.toJSONString(packageGoodsBO),objDTOGenericResponse.getMessage());
                    //撤销核销
                    if(SourceType.DOUYIN.equals(sourceType)){
                        marketDouyinStoreService.cancelVerifyCoupon(result.get("certificateId"),result.get("verifyId"),placeId);
                    }else{
                        meituanRequest.syncCancelCertificateVerify(placeId,encryptedCode,result.get("verifyId"),creater+"",createrName);
                    }
                    throw new ServiceException(objDTOGenericResponse.getMessage());
                }
            }
        }

        if(couponGoodsBOs.size() > 0){
            //处理优惠券商品-开通包时或充值网费并写入优惠券操作记录
            for (GoodsBO couponGoodsBO : couponGoodsBOs) {
                DiscountCoupon discountCoupon = couponMap.get(couponGoodsBO.getCouponId());
                log.info("用户:::{} {} 核销优惠券---使用券:::{} {} ",idNumber,placeId,discountCoupon.getCouponId(),discountCoupon.getCouponName());
                //给用户写入已使用的优惠券
                ReceiveCouponDetail receiveCouponDetail = new ReceiveCouponDetail();
                receiveCouponDetail.setPlaceId(placeId);
                receiveCouponDetail.setCouponId(discountCoupon.getCouponId());
                receiveCouponDetail.setCouponName(discountCoupon.getCouponName());
                receiveCouponDetail.setCouponTypeId(discountCoupon.getCouponTypeId());
                receiveCouponDetail.setCouponTypeName(discountCoupon.getCouponTypeName());
                receiveCouponDetail.setRuleId(discountCoupon.getRuleId());
                receiveCouponDetail.setAmount(discountCoupon.getAmount());
                receiveCouponDetail.setStartTime(discountCoupon.getStartTime()); //这里应该是立即生效的，因为直接使用掉了
                receiveCouponDetail.setEndTime(discountCoupon.getEndTime());
                receiveCouponDetail.setUseTime(now);
                receiveCouponDetail.setEffectiveWeekDays(discountCoupon.getEffectiveWeekDays());
                receiveCouponDetail.setStatus(1);
                receiveCouponDetail.setShiftId(shiftId);
                receiveCouponDetail.setOperatorName(createrName);
                receiveCouponDetail.setIdName(idName);
                receiveCouponDetail.setIdNumber(idNumber);
                receiveCouponDetail.setCouponCode(encryptedCode);
                receiveCouponDetail.setCouponSourceType(sourceType);
                receiveCouponDetail.setCreated(now);
                receiveCouponDetail.setCreater(Long.valueOf(creater));
                ReceiveCouponDetail save = receiveCouponDetailService.save(receiveCouponDetail);

                //写入日志
                LogCouponOperation logCouponOperation = new LogCouponOperation();
                logCouponOperation.setPlaceId(placeId);
                logCouponOperation.setCouponId(discountCoupon.getCouponId());
                logCouponOperation.setCouponName(discountCoupon.getCouponName());
                logCouponOperation.setAmount(discountCoupon.getAmount());
                logCouponOperation.setCouponDetailId(save.getCouponDetailId());
                logCouponOperation.setOperationType(CouponOperationType.USE_COUPON);
                logCouponOperation.setSourceType(operatorSourceType);
                logCouponOperation.setCouponCode(encryptedCode);
                logCouponOperation.setIdName(idName);
                logCouponOperation.setIdNumber(idNumber);
                logCouponOperation.setRemark(remark);
                logCouponOperation.setClientId(clientId);
                logCouponOperation.setCertificateId(result.get("certificateId"));
                logCouponOperation.setVerifyId(result.get("verifyId"));
                logCouponOperation.setCreater(Long.valueOf(creater));
                logCouponOperation.setCreated(now);
                logCouponOperation.setCreaterName(createrName);
                logCouponOperation.setShiftId(shiftId);
                logCouponOperationService.save(logCouponOperation);


                //根据优惠券类型进行充网费或者包时操作
                if(StringUtils.isEmpty(discountCoupon.getRuleId())){
                     //本金
                    int topupCost = (discountCoupon.getAmount() * couponGoodsBO.getGoodsNum());

                    //充网费
                    GenericResponse<?> objDTOGenericResponse = billingTopupRuleApi.couponTopup(placeId, idNumber, 0, topupCost, operatorSourceType, shiftId, discountCoupon.getCouponName());
                    if(!objDTOGenericResponse.isResult()){
                        log.info("用户:::{} 优惠券:::{} 核销成功，但充值时发生异常:::{}",idNumber,save.getCouponDetailId(),objDTOGenericResponse.getMessage());
                        //撤销核销
                        if(SourceType.DOUYIN.equals(sourceType)){
                            marketDouyinStoreService.cancelVerifyCoupon(result.get("certificateId"),result.get("verifyId"),placeId);
                        }else{
                            meituanRequest.syncCancelCertificateVerify(placeId,encryptedCode,result.get("verifyId"),creater+"",createrName);
                        }
                        throw new ServiceException(objDTOGenericResponse.getMessage());
                    }
                }else{
                    //包时
                    String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
                    stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
                    GenericResponse<?> objDTOGenericResponse = billingRulePackageTimeApi.couponPackageTime(requestTicket2, placeId, billingCardBO.getCardId(), discountCoupon.getRuleId(), operatorSourceType.name(), shiftId, discountCoupon.getCouponName(),sourceType.name());
                    if(!objDTOGenericResponse.isResult()){
                        log.info("用户:::{} 优惠券:::{} 核销成功，但生成包时时发生异常:::{}",idNumber,save.getCouponDetailId(),objDTOGenericResponse.getMessage());
                        //撤销核销
                        if(SourceType.DOUYIN.equals(sourceType)){
                            marketDouyinStoreService.cancelVerifyCoupon(result.get("certificateId"),result.get("verifyId"),placeId);
                        }else{
                            meituanRequest.syncCancelCertificateVerify(placeId,encryptedCode,result.get("verifyId"),creater+"",createrName);
                        }
                        throw new ServiceException(objDTOGenericResponse.getMessage());
                    }
                }
            }
        }
        log.info("团购券核销成功！ {}" ,JSONObject.toJSONString(saveOrder));
        saveOrder.setUpdated(LocalDateTime.now());
        saveOrder.setStatus(3);
        saveOrder.setFinishedTime(LocalDateTime.now());
        ordersService.save(saveOrder);
        orderGoodsService.updateStatusByPlaceIdAndOrderId(placeId,orderId,3,null);

        if(!StringUtils.isEmpty(buyGifts.getUpCardTypeId())){
            log.info("团购券核销成功调整卡类型 {}" ,JSONObject.toJSONString(buyGifts.getBuyGiftsId()));
            //修改会员卡类型
            billingCardApi.modifyBillingCardType(billingCardBO.getPlaceId(),billingCardBO.getCardId(),buyGifts.getUpCardTypeId());
        }
        try {
            //写入核销记录
            LogBuyGiftsVerifyRecord logBuyGiftsVerifyRecord = new LogBuyGiftsVerifyRecord();
            logBuyGiftsVerifyRecord.setPlaceId(placeId);
            logBuyGiftsVerifyRecord.setBuyGiftsId(buyGiftsId);
            logBuyGiftsVerifyRecord.setBuyGiftsName(buyGifts.getBuyGiftsName());
            logBuyGiftsVerifyRecord.setCouponCode(encryptedCode);
            logBuyGiftsVerifyRecord.setCertificateId(result.get("certificateId"));
            logBuyGiftsVerifyRecord.setVerifyId(result.get("verifyId"));
            logBuyGiftsVerifyRecord.setSourceType(sourceType);
            logBuyGiftsVerifyRecord.setOrderId(orderId);
            logBuyGiftsVerifyRecord.setCreated(now);
            logBuyGiftsVerifyRecord.setCreater(creater);
            logBuyGiftsVerifyRecordService.save(logBuyGiftsVerifyRecord);
        }catch (Exception e){
            e.printStackTrace();
        }


        GenericResponse<ObjDTO<PlaceProfileBO>> profileResponse = placeServerService.findByPlaceId(placeId);
        if(profileResponse.isResult()){
            //发送核销成功通知
            wechatMessageApi.sendCouponVerificationSuccessMessage(placeId,idNumber,profileResponse.getData().getObj().getDisplayName(),buyGifts.getBuyGiftsName(),encryptedCode,now);
        }
        List<String> goodsIdList=new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, saveOrder,goodsIdList));

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }


    @Transactional
    public GenericResponse<?> localCertificateVerify(String encryptedCode,String placeId,String idName,String idNumber, Long creater,
                                                     String createrName,String shiftId,SourceType operatorSourceType,String clientId,String remark){

        //5秒防止重复提交
        String requestRepetitionVerify = "market:local_"+placeId+"_"+idNumber+"_"+encryptedCode;
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(requestRepetitionVerify))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(requestRepetitionVerify, requestRepetitionVerify, 5, TimeUnit.SECONDS);

        GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(placeId, idNumber);
        if(!billingCardResponse.isResult() || null == billingCardResponse.getData() || null == billingCardResponse.getData().getObj()){
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        Optional<ReceiveCouponDetail> byPlaceIdAndCouponId = receiveCouponDetailService.findByPlaceIdAndIdNumberAndCouponCode(placeId, idNumber, encryptedCode);
        //查询出优惠券详情
        if(!byPlaceIdAndCouponId.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_ERROR);
        }
        ReceiveCouponDetail couponDetail = byPlaceIdAndCouponId.get();
        if(couponDetail.getStatus() != 0){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_LOSE_EFFICACY);
        }

//        if(!StringUtils.isEmpty(couponDetail.getRuleId()) && !verifyPackageTime(placeId,couponDetail.getRuleId())){
            //校验包时是否到可使用时间
//            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_TIME_VERIFY);
//        }
        String couponId = couponDetail.getCouponId();

        BillingCardBO billingCardBO = billingCardResponse.getData().getObj();
        //如果是包时券需要判断是否使用中或者未使用的包时，如果有 提示需要先结束当前包时
        if(!StringUtils.isEmpty(couponDetail.getRuleId())){
//            //校验是否有未使用的预包时
//            GenericResponse<ObjDTO<PackageTimeReserveBO>> placeIdAndCardId = packageTimeReserveApi.queryByPlaceIdAndCardId(placeId, billingCardBO.getCardId());
//            if (placeIdAndCardId.isResult() && !ObjectUtils.isEmpty(placeIdAndCardId.getData().getObj())) {
//                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_EXIST_FUTURE_PACKAGE_RULE_HINT);
//            }
//            //校验是否正在包时中上机
//            GenericResponse<ObjDTO<OnlineBO>> objDTOGenericResponse = billingOnlineApi.queryOnlineInfo(placeId, idNumber);
//            if(objDTOGenericResponse.isResult() && objDTOGenericResponse.getData().getObj().getPackageFlag() > 0){
//                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_RULE_HINT);
//            }
            GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime = billingServerService.queryBillingRulePackageTime(placeId, couponDetail.getRuleId());
            if(!queryBillingRulePackageTime.isResult()){
                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_RULE_NOT_FOUND);
            }
            BillingRulePackageTimeBO obj = queryBillingRulePackageTime.getData().getObj();
            LocalTime now = LocalTime.now();
//            if(DateTimeUtils.timeFormatFloat(Time.valueOf(now)) - obj.getStartTime() < 0){
//                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_TIME_VERIFY);
//            }
//            if(DateTimeUtils.timeFormatFloat(Time.valueOf(now)) - obj.getEndTime() > 0){
//                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_PACKAGE_TIME_VERIFY);
//            }
            GenericResponse<ObjDTO<InviteOnlineBO>> byPlaceIdAndCardId = inviteApi.findByPlaceIdAndCardId(placeId, billingCardBO.getCardId());
            if(byPlaceIdAndCardId.isResult()){
                return new GenericResponse<>(ServiceCodes.BILLING_INVITE_PACKAGE_NOT_SUPPORT);
            }

        }

        //需要校验有效期，是否禁用，使用数量，是否生效日
        LocalDateTime now = LocalDateTime.now();
        if(couponDetail.getStatus() == 1){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_DISABLED);
        }
        if(!couponDetail.getEffectiveWeekDays().contains(String.valueOf(now.getDayOfWeek().getValue()))){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_INVALID);
        }
        if(now.isBefore(couponDetail.getStartTime())){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_INVALID);
        }
        if(now.isAfter(couponDetail.getEndTime())){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_LOSE_EFFICACY);
        }

        Optional<DiscountCoupon> byPlaceIdAndCouponIdAndDeleted = discountCouponRepository.findByPlaceIdAndCouponId(placeId, couponDetail.getCouponId());
        if(!byPlaceIdAndCouponIdAndDeleted.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_DATA_NOT_FOUND);
        }
        DiscountCoupon discountCoupon = byPlaceIdAndCouponIdAndDeleted.get();
        //查询原始优惠券
        if(0 != discountCoupon.getLimitNum()){
            //查询使用数量是否超过 limitNum
            //默认日
            LocalDateTime verStartTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            LocalDateTime verEndTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            if(2 == discountCoupon.getLimitType()){
                //周
                verStartTime = now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
                verEndTime = now.with(DayOfWeek.SUNDAY).toLocalDate().atTime(23, 59, 59);
            }else if(3 == discountCoupon.getLimitType()){
                //月
                verStartTime = now.with(TemporalAdjusters.firstDayOfMonth()).toLocalDate().atStartOfDay();
                verEndTime = now.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().atTime(23, 59, 59);
            }else if(4 == discountCoupon.getLimitType()){
                //月
                verStartTime = now.minus(365, ChronoUnit.DAYS);
                verEndTime = now;
            }
            List<LogCouponOperation> toDayAllUseCoupon = logCouponOperationService.findByPlaceIdAndCouponIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, couponId,
                    CouponOperationType.USE_COUPON, verStartTime,verEndTime,idNumber);
            if(toDayAllUseCoupon.size() >= discountCoupon.getLimitNum()){
                return new GenericResponse<>(ServiceCodes.MARKET_COUPON_EXCEED_THE_LIMIT);
            }
        }
        //获取班次
        if(StringUtils.isEmpty(shiftId)){
            GenericResponse<ObjDTO<PlaceShiftBO>> objDTOGenericResponse = placeServerService.queryDefaultWorkShift(placeId);
            if(objDTOGenericResponse.isResult()){
                shiftId = objDTOGenericResponse.getData().getObj().getShiftId();
            }
        }

        couponDetail.setStatus(1);
        couponDetail.setUpdated(now);
        couponDetail.setUseTime(now);
        receiveCouponDetailService.save(couponDetail);
        //写入日志
        LogCouponOperation logCouponOperation = new LogCouponOperation();
        logCouponOperation.setPlaceId(placeId);
        logCouponOperation.setCouponId(couponDetail.getCouponId());
        logCouponOperation.setCouponName(couponDetail.getCouponName());
        logCouponOperation.setAmount(couponDetail.getAmount());
        logCouponOperation.setCouponDetailId(couponDetail.getCouponDetailId());
        logCouponOperation.setOperationType(CouponOperationType.USE_COUPON);
        logCouponOperation.setSourceType(operatorSourceType);
        logCouponOperation.setCouponCode(encryptedCode);
        logCouponOperation.setIdName(idName);
        logCouponOperation.setIdNumber(idNumber);
        logCouponOperation.setRemark(remark);
        logCouponOperation.setClientId(clientId);
//        logCouponOperation.setCertificateId(); //本地优惠券没有这个
        logCouponOperation.setCreater(creater);
        logCouponOperation.setCreated(now);
        logCouponOperation.setShiftId(shiftId);
        logCouponOperation.setCreaterName(createrName);
        logCouponOperationService.save(logCouponOperation);

        //根据优惠券类型进行充网费或者包时操作
        if(StringUtils.isEmpty(couponDetail.getRuleId())){
            //充网费
            GenericResponse<?> objDTOGenericResponse = billingTopupRuleApi.couponTopup(placeId, idNumber, 0, couponDetail.getAmount(), operatorSourceType, shiftId, couponDetail.getCouponName());
            if(!objDTOGenericResponse.isResult()){
                log.info("用户:::{} 优惠券:::{} 核销成功，但充值时发生异常:::{}",idNumber,couponDetail.getCouponDetailId(),objDTOGenericResponse.getMessage());
                throw new ServiceException(objDTOGenericResponse.getMessage());
            }
        }else{
            //包时
            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
            GenericResponse<?> objDTOGenericResponse = billingRulePackageTimeApi.couponPackageTime(requestTicket2, placeId, billingCardBO.getCardId(), couponDetail.getRuleId(), operatorSourceType.name(), shiftId, couponDetail.getCouponName(),couponDetail.getCouponSourceType().name());
            if(!objDTOGenericResponse.isResult()){
                log.info("用户:::{} 优惠券:::{} 核销成功，但生成包时时发生异常:::{}",idNumber,couponDetail.getCouponDetailId(),objDTOGenericResponse.getMessage());
                throw new ServiceException(objDTOGenericResponse.getMessage());
            }
        }
        //使用成功，添加轮询
        List<ReceiveCouponDetail> list = receiveCouponDetailService.findByPlaceIdAndIdNumberAndStatusAndDeleted(placeId, idNumber, 0);
        sendTopupBusiness(placeId,list.size(),idNumber,idNumber,operatorSourceType);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    public GenericResponse<ListDTO<BillingCardBO>> findCardInfo(String idNumber, String placeId) {
        return billingCardApi.findCardByPlaceIdAndLikeIdNumber(placeId, idNumber);
    }

    private boolean verifyPackageTime(String placeId,String ruleId){

        GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime = billingServerService.queryBillingRulePackageTime(placeId, ruleId);
        if(!queryBillingRulePackageTime.isResult()){
            throw new ServiceException(ServiceCodes.MARKET_COUPON_PACKAGE_RULE_NOT_FOUND);
        }

        BillingRulePackageTimeBO billingRulePackageTime = queryBillingRulePackageTime.getData().getObj();
        float start = billingRulePackageTime.getStartTime(); //包时开始时间
        float end = billingRulePackageTime.getEndTime(); //包时结束时间

        LocalDateTime nowDateTime = LocalDateTime.now();
        int nowMinute = LocalDateTime.now().getMinute();
        float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
        float currHours = nowDateTime.getHour() + minute; //当前时间
        if (end - start > 0) { // 同一天
            if (currHours >= start && currHours < end) {
                return true;
            }
        } else { // 跨0点包时
            if (currHours >= start || currHours < end) {
                return true;
            }
        }
        return false;
    }

    public GenericResponse<?> cancelVerifyCoupon( String couponCode, String verifyId, String placeId, SourceType sourceType){
        if(SourceType.DOUYIN.equals(sourceType)){
            return marketDouyinStoreService.cancelVerifyCoupon(couponCode,verifyId,placeId);
        }else if(SourceType.MEITUAN.equals(sourceType)){
            Map<String, String> map = meituanRequest.cancelCertificateVerify(placeId,couponCode,verifyId,"1","手动取消核销");
            if(!"0".equals(map.get("code"))){
                return new GenericResponse<>(map.get("msg"));
            }else{
                return new GenericResponse<>(ServiceCodes.NO_ERROR);
            }
        }else{
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
    }

    public void checkPackageTimeAvailable (BillingRulePackageTimeBO billingRulePackageTime,
                                           BillingCardBO billingCardBO) {
        //校验是否禁用
        if(billingRulePackageTime.getForbidden() == 1){
            throw new ServiceException(ServiceCodes.MARKET_COUPON_DATA_DISABLED);
        }
        // 校验卡类型
        if (!billingRulePackageTime.getCardTypeIds().contains(billingCardBO.getCardTypeId())) {
            log.info("checkPackageTimeAvailable 包时规则不支持该卡类型;{}",  billingCardBO.getCardId());
            throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
        }
        //校验购买次数
        GenericResponse<SimpleDTO> packageTimeBuyCount = billingRulePackageTimeApi.findPackageTimeBuyCount(billingCardBO.getPlaceId(), billingCardBO.getCardId(),billingRulePackageTime.getRuleId());
        if(!packageTimeBuyCount.isResult()){
            throw new ServiceException(ServiceCodes.MARKET_LIMIT_SALES_TYPE_ERROR);
        }
        // 校验销售时间
        int salesTimeType = billingRulePackageTime.getSalesTimeType();
        Time salesStartTime = Time.valueOf(billingRulePackageTime.getSalesStartTime());
        Time salesEndTime = Time.valueOf(billingRulePackageTime.getSalesEndTime());
        String customDate = billingRulePackageTime.getCustomDate();

        if(0 == salesTimeType ) {
            return;
        }else if (1 == salesTimeType) {
            // 不限制或者为每日,只需判断时间段是否包含
            if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内;start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
        }  else if (2 == salesTimeType) {
            // 每周
            LocalDate currentDate = LocalDate.now();
            // 获取星期数（1-7，1 表示星期一）
            DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
            int weekdayNumber = dayOfWeek.getValue();
            if (StringUtils.isEmpty(customDate) || !customDate.contains(String.valueOf(weekdayNumber))) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在周日期内);customDate{},weekdayNumber{}",  customDate, weekdayNumber);
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
            if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(周);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
        } else if (3 == salesTimeType) {
            // 每月
            LocalDate currentDate = LocalDate.now();
            // 获取当前日期是本月的第几天
            int dayOfMonth = currentDate.getDayOfMonth();
            if (StringUtils.isEmpty(customDate) || !customDate.contains(String.valueOf(dayOfMonth))) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在月日期内);customDate{}, dayOfMonth{}",  customDate, dayOfMonth);
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
            if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(月);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
        } else {
            // 自定义时间段
            if (StringUtils.isEmpty(customDate) || !customDate.contains("_")) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在自定义日期内);customDate{}",  customDate);
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
            if (!checkCustomDateIsInDateTimeRange(customDate)) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在自定义日期内);customDate{}",  customDate);
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
            if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
                log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(周);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
                throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
            }
        }

        // 校验包时类型,非包时段、包时长就报错
        if (billingRulePackageTime.getPackageFlag() != 1 && billingRulePackageTime.getPackageFlag() != 2) {
            log.info("checkPackageTimeAvailable 包时类型错误;packageFlag{}",  billingRulePackageTime.getPackageFlag());
            throw new ServiceException(ServiceCodes.MARKET_PACKAGE_RULE_TYPE_ERROR);
        }
    }

    /**
     * 当前时间是否在某个时间段内
     * @param salesStartTime
     * @param salesEndTime
     * @return
     */
    private boolean checkIsInDateTimeRange (Time salesStartTime, Time salesEndTime) {
        LocalTime start = salesStartTime.toLocalTime();
        LocalTime end = salesEndTime.toLocalTime();
        // 获取当前时间
        LocalTime current = LocalTime.now();
        return isInDateTimeRange(current, start, end);
    }

    /**
     * 校验当前日期是否在自定义日期内
     * @param customDate
     * @return
     */
    private boolean checkCustomDateIsInDateTimeRange (String customDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 分割字符串获取起始日期和结束日期
        String[] parts = customDate.split("_");
        LocalDate startDate = LocalDate.parse(parts[0], formatter);
        LocalDate endDate = LocalDate.parse(parts[1], formatter);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 判断当前日期是否在起始日期和结束日期之间
        return!currentDate.isBefore(startDate) &&!currentDate.isAfter(endDate);
    }

    private void sendTopupBusiness(String placeId,int counponCount,String idNumber,String clientId,SourceType sourceType){
        // 保存轮询数据
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId,clientId,idNumber, BusinessType.COUPON);

        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();

            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {

                CouponBusinessBO couponBusinessBO = new CouponBusinessBO();
                couponBusinessBO.setPlaceId(placeId);
                couponBusinessBO.setIdNumber(idNumber);
                couponBusinessBO.setCreated(LocalDateTime.now().toString());
                couponBusinessBO.setSourceType(sourceType);
                couponBusinessBO.setBusinessType(BusinessType.COUPON);
                couponBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                couponBusinessBO.setClientId(clientId);
                couponBusinessBO.setCounponCount(counponCount);
                // 保存收银台业务数据
                notifyServerService.pushCouponBusinessData(couponBusinessBO);
            }
        }
    }
}
