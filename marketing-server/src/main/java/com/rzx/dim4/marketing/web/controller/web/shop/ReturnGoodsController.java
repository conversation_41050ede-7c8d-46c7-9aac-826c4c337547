package com.rzx.dim4.marketing.web.controller.web.shop;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.rzx.dim4.base.bo.marketing.ReturnGoodsBO;
import com.rzx.dim4.base.bo.marketing.ReturnGoodsDetailedBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.marketing.entity.GoodsSuppliers;
import com.rzx.dim4.marketing.entity.ReturnGoods;
import com.rzx.dim4.marketing.entity.ReturnGoodsDetailed;
import com.rzx.dim4.marketing.service.shop.GoodsSuppliersService;
import com.rzx.dim4.marketing.service.shop.ReturnGoodsDetailedService;
import com.rzx.dim4.marketing.service.shop.ReturnGoodsService;
import com.rzx.dim4.marketing.util.TokenUtil;
import com.rzx.dim4.marketing.util.market.ExcelUtils;
import com.rzx.dim4.marketing.web.controller.web.vo.ExportReturnGoodsDetailedVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月16日
 * 退货单
 * 查询、详情、新增、导出商品退货单
 */
@Slf4j
@RestController
@RequestMapping("/marketing/returnGoods")
public class ReturnGoodsController {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    private ReturnGoodsService returnGoodsService;

    @Autowired
    private ReturnGoodsDetailedService returnGoodsDetailedService;

    @Autowired
    private GoodsSuppliersService goodsSuppliersService;

    /**
     * 保存退货单
     * @param request
     * @param returnGoodsBO
     * @return
     */
    @PostMapping("/save")
    public GenericResponse<ListDTO<ReturnGoodsBO>> saveReturnGoods(HttpServletRequest request, @RequestBody ReturnGoodsBO returnGoodsBO) {

        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);
        if (StringUtils.isEmpty(returnGoodsBO.getPlaceId()) && webLoginAccount.getType() != 6) {
            returnGoodsBO.setPlaceId(webLoginAccount.getPlaceId());
        }
        log.info("场所:::{} 用户:::{}，保存退货单数据为:::{}",returnGoodsBO.getPlaceId(),webLoginAccount.getAccountId(), JSONObject.toJSONString(returnGoodsBO));

        if (StringUtils.isEmpty(returnGoodsBO.getPlaceId()) || StringUtils.isEmpty(returnGoodsBO.getSupplierId())
                || StringUtils.isEmpty(returnGoodsBO.getProfitLossPrice()) || CollectionUtils.isEmpty(returnGoodsBO.getReturnGoodsDetailedBOS())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        String sskey = "MARKETING_RETURN_GOODS" + "_" + returnGoodsBO.getPlaceId() +  "_" +  returnGoodsBO.getSupplierId();
        boolean lock = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(sskey, "1", 2000, TimeUnit.MILLISECONDS));
        // 存在返回 false，不存在返回 true
        if (!lock) {
            log.info("商品退货单:::::::::::::::重复请求:::placeId:::" + returnGoodsBO.getPlaceId() +  "::::supplierId:::" +  returnGoodsBO.getSupplierId());
            return new GenericResponse<>(ServiceCodes.MARKET_RETURN_GOODS_REPEAT);
        }
        returnGoodsBO.setCreater(Long.valueOf(webLoginAccount.getAccountId()));
        returnGoodsBO.setId(null);
        returnGoodsService.save(returnGoodsBO);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 查询退货单详情
     * @param placeId 场所id，必填
     * @param returnGoodsNum 退货单编号，必填
     * @return
     */
    @GetMapping("/findDetails")
    public GenericResponse<ObjDTO<ReturnGoodsBO>> findReturnGoodsDetails(@RequestParam String placeId, @RequestParam String returnGoodsNum) {
        if(StringUtils.isEmpty(placeId) || (StringUtils.isEmpty(returnGoodsNum))){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        Optional<ReturnGoods> returnGoodsListOpt = returnGoodsService.findByPlaceIdAndReturnGoodsNum(placeId,returnGoodsNum);
        if (!returnGoodsListOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.MARKET_RETURN_GOODS_NOT_FOUND);
        }
        ReturnGoods returnGoods = returnGoodsListOpt.get();
        ReturnGoodsBO returnGoodsBO = returnGoods.toBO();
        // 查询退货单的商品列表并赋值
        List<ReturnGoodsDetailed> returnGoodsLists = returnGoodsDetailedService.findByPlaceIdAndReturnGoodsNum(placeId,returnGoodsNum);
        List<ReturnGoodsDetailedBO> returnGoodsListBOS = returnGoodsLists.stream().map(ReturnGoodsDetailed::toBO).collect(Collectors.toList());
        returnGoodsBO.setReturnGoodsDetailedBOS(returnGoodsListBOS);

        return new GenericResponse<>(new ObjDTO<>(returnGoodsBO));

    }



    /**
     * 分页查询退货单列表
     * @param placeId
     * @param startDate
     * @param endDate
     * @param type 商品类型
     * @param size
     * @param start
     * @param orderColumns  排序字段
     * @param order  排序属性:升序还是降序
     * @return
     */
    @GetMapping("/findPageList")
    @Validated
    public GenericResponse<PagerDTO<ReturnGoodsBO>> findPageList(
            @RequestParam(name = "placeId") String placeId,
            @RequestParam(value = "startDate", defaultValue = "") String startDate,
            @RequestParam(name = "endDate", defaultValue = "") String endDate,
            @RequestParam(name = "type", defaultValue = "0") @Min(value = 0, message = "参数必须是非负整数") @Max(value = 1, message = "参数必须是非负整数")  int type,
            @RequestParam(name = "start", defaultValue = "0") int start,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order) {
        if(StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        int page = (start == 0 ? start : (start / size));
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.fromString(order), orderColumns);
        Map<String, Object> map = new HashMap<>();
        map.put("placeId", placeId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("type", type);

        Page<ReturnGoods> pager = returnGoodsService.findAll(map, pageable);
        List<ReturnGoodsBO> bos = pager.getContent().stream().map(ReturnGoods::toBO).collect(Collectors.toList());
        for (ReturnGoodsBO bo : bos) {
            Optional<GoodsSuppliers> byPlaceIdAndSupplierId = goodsSuppliersService.findByPlaceIdAndSupplierId(bo.getPlaceId(), bo.getSupplierId());
            if(byPlaceIdAndSupplierId.isPresent()){
                bo.setSupplierName(byPlaceIdAndSupplierId.get().getSupplierName());
            }
        }
        return new GenericResponse<>(new PagerDTO<>((int) pager.getTotalElements(), bos));

    }

    /**
     * 导出-退货单详情
     * @param placeId 场所id
     * @param returnGoodsNum 退货单单号
     * @param response
     */
    @RequestMapping(value = "/exportDetails", method = RequestMethod.GET)
    public void exportReturnGoodsDetails(
            @RequestParam(name = "placeId") String placeId,
            @RequestParam(name = "returnGoodsNum") String returnGoodsNum,
            HttpServletResponse response) {
        if(StringUtils.isEmpty(placeId) || (StringUtils.isEmpty(returnGoodsNum))){
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
        List<ExportReturnGoodsDetailedVO> vos = new ArrayList<>();
        List<ReturnGoodsDetailed> returnGoodsDetaileds = returnGoodsDetailedService.findByPlaceIdAndReturnGoodsNum(placeId, returnGoodsNum);
        returnGoodsDetaileds.forEach(e -> {
            ExportReturnGoodsDetailedVO vo = new ExportReturnGoodsDetailedVO();
            BeanUtils.copyProperties(e, vo);

            BigDecimal price = new BigDecimal(e.getPrice());
            BigDecimal profitLossPrice = new BigDecimal(e.getProfitLossPrice());

            BigDecimal formattedPrice = price.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal formattedProfitLossPrice = profitLossPrice.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);

            vo.setPrice(formattedPrice);
            vo.setProfitLossPrice(formattedProfitLossPrice);

            // 商品类型处理
            if (e.getIsGift() == 0) {
                vo.setIsGift("否");
            } else {
                vo.setIsGift("是");
            }
            vos.add(vo);
        });
        try {
            ExcelUtils.writeExcel(response, vos, "退货单详情", "退货单详情", ExportReturnGoodsDetailedVO.class, false, null);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }


}
