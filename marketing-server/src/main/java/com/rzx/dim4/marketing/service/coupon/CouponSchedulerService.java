package com.rzx.dim4.marketing.service.coupon;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.CouponOperationType;
import com.rzx.dim4.marketing.entity.LogCouponOperation;
import com.rzx.dim4.marketing.entity.ReceiveCouponDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年10月08日 14:39
 */
@Service
@Slf4j
public class CouponSchedulerService {

    @Autowired
    private ReceiveCouponDetailService detailService;

    @Autowired
    private LogCouponOperationService logCouponOperationService;

    @Async
    public void schedulerCheckValid(){
        // 1.查询出未失效但是已过期的优惠券
        LocalDateTime now = LocalDateTime.now();
        List<ReceiveCouponDetail> allCoupon = detailService.findByDeletedAndStatusAndEndTimeLessThan(0, 0, now);
        if(allCoupon.size() == 0){
            return;
        }
        log.info("定时器检测过期优惠券数量：{}",allCoupon.size());
        // 2.批量修改状态
        List<LogCouponOperation> logCouponOperations = new ArrayList<>();

        for (ReceiveCouponDetail detail : allCoupon) {
            log.info("优惠券id：{} 已过期",detail.getCouponDetailId());
            detail.setUpdated(now);
            detail.setStatus(2);

            //生成操作记录
            LogCouponOperation operation = new LogCouponOperation();
            operation.setPlaceId(detail.getPlaceId());
            operation.setCouponId(detail.getCouponId());
            operation.setCouponDetailId(detail.getCouponDetailId());
            operation.setCreater(1l);
            operation.setCreaterName("系统");
            operation.setCreated(now);
            operation.setOperationType(CouponOperationType.EXPIRED_COUPON);
            operation.setSourceType(SourceType.SYSTEM);
            operation.setIdName(detail.getIdName());
            operation.setIdNumber(detail.getIdNumber());
            operation.setCouponName(detail.getCouponName());
            operation.setAmount(detail.getAmount());
            logCouponOperations.add(operation);
        }
        // 3.写入日志
        try {
            detailService.saveAll(allCoupon);
        }catch (Exception e){
            log.info("保存失效优惠券状态时异常！");
            e.printStackTrace();
        }
        try {
            logCouponOperationService.saveAll(logCouponOperations);
        }catch (Exception e){
            log.info("保存失效优惠券操作记录时异常！");
            e.printStackTrace();
        }

    }

    @Async
    public void schedulerCheckExpiringSoon() {
        // 1.查询出3天后即将过期的优惠券
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threeDaysLaterStart = now.plusDays(3).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime threeDaysLaterEnd = now.plusDays(3).withHour(23).withMinute(59).withSecond(59);

        List<ReceiveCouponDetail> threeDaysExpiringCoupons = detailService.findByDeletedAndStatusAndEndTimeBetween(
                0, 0, threeDaysLaterStart, threeDaysLaterEnd);

        if (threeDaysExpiringCoupons.isEmpty()) {
            return;
        }

        log.info("定时器检测3天后过期优惠券数量：{}", threeDaysExpiringCoupons.size());

        // 2.发送微信公众号通知
        for (ReceiveCouponDetail coupon : threeDaysExpiringCoupons) {
            log.info("优惠券id：{} 将在3天后过期，发送微信公众号通知", coupon.getCouponDetailId());

            // TODO: 发送微信公众号通知
            // 这里需要调用微信公众号通知接口
            // 示例代码：
            /*
            try {
                wechatMessageApi.sendCouponExpiringMessage(
                    coupon.getIdNumber(),           // 用户身份证号
                    coupon.getPlaceId(),            // 场所ID
                    coupon.getCouponName(),         // 优惠券名称
                    coupon.getAmount(),             // 优惠券面额
                    coupon.getEndTime(),            // 过期时间
                    now                             // 当前时间
                );
            } catch (Exception e) {
                log.error("发送微信公众号通知失败，优惠券ID: {}", coupon.getCouponDetailId(), e);
            }
            */
        }
    }
}