package com.rzx.dim4.marketing.event;

import com.rzx.dim4.base.event.BaseEvent;
import com.rzx.dim4.marketing.entity.OrderGoods;
import com.rzx.dim4.marketing.entity.Orders;
import lombok.Getter;

import java.util.List;


/**
 * 订单变化事件
 * <AUTHOR> hwx
 * @since 2025/3/4 14:38
 */
@Getter
public class GoodsOrderEvent extends BaseEvent {
    private static final long serialVersionUID = -410463731952113872L;

    private Orders orders;

    private List<String> goodsIds;


    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public GoodsOrderEvent(Object source) {
        super(source);
    }

    public GoodsOrderEvent(Object source, Orders orders,List<String> goodsIds) {
        super(source);
        this.orders = orders;
        this.goodsIds = goodsIds;
    }
}
