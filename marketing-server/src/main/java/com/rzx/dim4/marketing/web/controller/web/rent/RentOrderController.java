
package com.rzx.dim4.marketing.web.controller.web.rent;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.marketing.OrderGoodsBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.bo.marketing.RentConfigBO;
import com.rzx.dim4.base.bo.marketing.rent.RentOrderQueryBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingCardTypeApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.marketing.service.rent.RentConfigService;
import com.rzx.dim4.marketing.service.shop.OrdersService;
import com.rzx.dim4.marketing.util.TokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 租赁设置controller
 */
@Api(tags = "门店后台-租赁订单")
@Slf4j
@RestController
@RequestMapping("/rent/rentOrder")
public class RentOrderController {

    @Autowired
    private OrdersService ordersService;


    @ApiOperationSupport(order = 1, author = "hgs")
    @ApiOperation(value = "分页查询租赁订单列表")
    @PostMapping("/findPageList")
    public GenericResponse<PagerDTO<OrderGoodsBO>> findPageList(HttpServletRequest request, @RequestBody RentOrderQueryBO rentOrderQueryBO) {
        log.info("分页查询租赁订单列表 CashierOrdersController.findPageList:::Params=({})", new Gson().toJson(rentOrderQueryBO));
        if (StringUtils.isEmpty(rentOrderQueryBO.getPlaceId())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if (rentOrderQueryBO.getSize() > 200 || rentOrderQueryBO.getSize()  < 1 || rentOrderQueryBO.getPage()  < 0) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_QUERY);
        }
        return ordersService.findRentOrderPageList(request, rentOrderQueryBO);
    }

}
