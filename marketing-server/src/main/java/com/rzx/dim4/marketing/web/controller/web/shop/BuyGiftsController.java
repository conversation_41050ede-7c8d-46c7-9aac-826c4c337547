package com.rzx.dim4.marketing.web.controller.web.shop;

import com.alibaba.fastjson.JSONObject;
import com.rzx.dim4.base.bo.marketing.BuyGiftsBO;
import com.rzx.dim4.base.bo.marketing.BuyGiftsGoodsBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.marketing.entity.BuyGifts;
import com.rzx.dim4.marketing.entity.BuyGiftsGoods;
import com.rzx.dim4.marketing.entity.Goods;
import com.rzx.dim4.marketing.service.coupon.DiscountCouponService;
import com.rzx.dim4.marketing.service.douyin.MarketDouyinStoreService;
import com.rzx.dim4.marketing.service.meituan.MarketMeituanStoreService;
import com.rzx.dim4.marketing.service.shop.BuyGiftsGoodsService;
import com.rzx.dim4.marketing.service.shop.BuyGiftsService;
import com.rzx.dim4.marketing.service.shop.GoodsService;
import com.rzx.dim4.marketing.service.shop.StorageGoodsService;
import com.rzx.dim4.marketing.util.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年12月21日
 */
@Slf4j
@RestController
@RequestMapping("/marketing/buyGifts")
public class BuyGiftsController {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BuyGiftsService buyGiftsService;

    @Autowired
    private BuyGiftsGoodsService buyGiftsGoodsService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private MarketMeituanStoreService marketMeituanStoreService;

    @Autowired
    private MarketDouyinStoreService marketDouyinStoreService;

    @Autowired
    private DiscountCouponService discountCouponService;


    /**
     * 新增买赠
     * @param request
     * @param buyGiftsBO
     * @return
     */
    @PostMapping("/save")
    public GenericResponse<ObjDTO<BuyGiftsBO>> saveBuyGifts(HttpServletRequest request, @RequestBody BuyGiftsBO buyGiftsBO) {

        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);
        if (StringUtils.isEmpty(buyGiftsBO.getPlaceId()) && webLoginAccount.getType() != 6) {
            buyGiftsBO.setPlaceId(webLoginAccount.getPlaceId());
        }

        log.info("场所:::{} 用户:::{}，新增买赠为:::{}",buyGiftsBO.getPlaceId(),webLoginAccount.getAccountId(), JSONObject.toJSONString(buyGiftsBO));

        if (StringUtils.isEmpty(buyGiftsBO.getEventGoods()) || StringUtils.isEmpty(buyGiftsBO.getBuyGiftsName())
                || (StringUtils.isEmpty(buyGiftsBO.getNeedBuyNum()) || buyGiftsBO.getNeedBuyNum()<=0) || StringUtils.isEmpty(buyGiftsBO.getEventDate())
                || (buyGiftsBO.getEventDate() == 0 && (StringUtils.isEmpty(buyGiftsBO.getStartDate()) || StringUtils.isEmpty(buyGiftsBO.getEndDate())))
                || StringUtils.isEmpty(buyGiftsBO.getStartTime()) || StringUtils.isEmpty(buyGiftsBO.getEndTime()) || StringUtils.isEmpty(buyGiftsBO.getBuyGiftsGoodsBOS().size() < 0)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        String sskey = "MARKETING_BUY_GIFTS" + "_" + buyGiftsBO.getPlaceId() +  "_" +  buyGiftsBO.getBuyGiftsName();
        boolean lock = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(sskey, "1", 2000, TimeUnit.MILLISECONDS));
        // 存在返回 false，不存在返回 true
        if (!lock) {
            log.info("买赠信息新增:::::::::::::::重复请求:::placeId:::" + buyGiftsBO.getPlaceId() +  "::::buyGiftsName:::" +  buyGiftsBO.getBuyGiftsName());
            return new GenericResponse<>(ServiceCodes.MARKET_BUY_GIFTS_REPEAT);
        }
        //校验ruleId不为空的商品存在时就不能添加包时券了
        List<String> goodIds = buyGiftsBO.getBuyGiftsGoodsBOS().stream().map(BuyGiftsGoodsBO::getGoodsId).collect(Collectors.toList());
        if(buyGiftsBO.getEventGoodsType() == 0){
            goodIds.addAll(Arrays.asList(buyGiftsBO.getEventGoods().split(",")));
        }
        List<Goods> goodsList = goodsService.findByPlaceIdAndGoodsIdInAndDeleted(buyGiftsBO.getPlaceId(),goodIds,0);
        if(goodsList.stream().filter(it->it.getGoodsCategory() == 4).count() > 0){
            List<String> couponIds = goodsList.stream().filter(it -> !StringUtils.isEmpty(it.getCouponId())).map(Goods::getCouponId).collect(Collectors.toList());
            if(couponIds.size() > 0){
                discountCouponService.findByPlaceIdAndCouponIdIn(buyGiftsBO.getPlaceId(), couponIds).forEach(e -> {
                    if (e.getRuleId() != null) {
                        throw new ServiceException(ServiceCodes.MARKET_BUY_GIFTS_PACKAGE_RULE_NOT_ALLOW);
                    }
                });
            }
        }




        if (StringUtils.isEmpty(buyGiftsBO.getBuyGiftsId())) {
            buyGiftsBO.setCreated(LocalDateTime.now());
            buyGiftsBO.setEventType(0);
            buyGiftsBO.setStatus(0);
        }
        buyGiftsBO.setCreater(Long.parseLong(webLoginAccount.getAccountId()));
        return new GenericResponse<>(new ObjDTO<>(buyGiftsService.saveBuyGifts(buyGiftsBO)));
    }

    /**
     * 分页查询买赠信息
     * @param request
     * @param placeId 场所id
     * @param bugGiftsName 活动名称
     * @param schemeRemarks 备注
     * @param size
     * @param start
     * @return
     */
    @GetMapping("/findPageList")
    public GenericResponse<PagerDTO<BuyGiftsBO>> findPageList(HttpServletRequest request,
                                                           @RequestParam(name = "placeId") String placeId,
                                                           @RequestParam(name = "buyGiftsName",required = false) String buyGiftsName,
                                                           @RequestParam(name = "schemeRemarks",required = false) String schemeRemarks,
                                                           @RequestParam(name = "marketingPlatform",required = false) String marketingPlatform,
                                                           @RequestParam(name = "size", defaultValue = "10") int size,
                                                           @RequestParam(name = "start", defaultValue = "1") int start){

        if(StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        int page = start / size;
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("placeId", placeId);
        if(!StringUtils.isEmpty(buyGiftsName)){
            queryMap.put("buyGiftsName",buyGiftsName);
        }
        if(!StringUtils.isEmpty(schemeRemarks)){
            queryMap.put("schemeRemarks",schemeRemarks);
        }
        if(!StringUtils.isEmpty(marketingPlatform)){
            queryMap.put("marketingPlatform",marketingPlatform);
        }

        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "id");

        Page<BuyGifts> all = buyGiftsService.findAll(queryMap, pageable);

        List<BuyGiftsBO> bos = all.getContent().stream().map(e -> {
            return e.toBO();
        }).collect(Collectors.toList());

        bos.forEach(e-> {
            List<BuyGiftsGoods> buyGiftsGoodsList = buyGiftsGoodsService.findByPlaceIdAndBuyGiftsIdIn(placeId, Arrays.asList(e.getBuyGiftsId()));
            List<BuyGiftsGoodsBO> buyGiftsGoodsBOList = buyGiftsGoodsList.stream().map(BuyGiftsGoods::toBO).collect(Collectors.toList());
            buyGiftsGoodsBOList.forEach(it->{
                Optional<Goods> goodsCodeObj = goodsService.findByPlaceIdAndGoodsIdAndDeleted(placeId, it.getGoodsId());
                if(goodsCodeObj.isPresent()) {
                    Goods goods = goodsCodeObj.get();
                    it.setGoodsPic(goods.getGoodsPic());
                    it.setGoodsPrice(goods.getUnitPrice());
                }
            });
            e.setBuyGiftsGoodsBOS(buyGiftsGoodsBOList);
        });

        return new GenericResponse<>(new PagerDTO<>((int) all.getTotalElements(), bos));
    }

    /**
     * 删除买赠
     * @param placeId 场所id，必填
     * @param buyGiftsId 买赠id，必填
     * @return
     */
    @GetMapping("delete")
    public  GenericResponse<?> deleteBuyGifts(@RequestParam String  placeId, @RequestParam String buyGiftsId) {
        if(com.alibaba.excel.util.StringUtils.isEmpty(placeId) || (com.alibaba.excel.util.StringUtils.isEmpty(buyGiftsId) ) ){
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        Optional<BuyGifts> optionalBuyGifts = buyGiftsService.findByPlaceIdAndBuyGiftsId(placeId,buyGiftsId);
        if (!optionalBuyGifts.isPresent()) {
            throw new ServiceException(ServiceCodes.MARKET_BUY_GIFTS_NOT_FOUND);
        }
        BuyGifts buyGifts = optionalBuyGifts.get();
        buyGifts.setDeleted(1);
        buyGifts.setUpdated(LocalDateTime.now());
        buyGiftsService.save(buyGifts);
        // 删除买赠的商品列表
        buyGiftsGoodsService.deleteByBuyGiftsId(placeId,buyGiftsId);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }



    /**
     * 批量修改商品信息
     * @param request
     * @param buyGiftsBO   商品信息  buyGiftsBO.buyGiftsIds   需要修改的买赠信息ID
     * @return
     */
    @PostMapping("/batchEditGoods")
    public GenericResponse<?> batchEditGoods(HttpServletRequest request,
                                             @RequestBody BuyGiftsBO buyGiftsBO){
        if(CollectionUtils.isEmpty(buyGiftsBO.getBuyGiftsIds()) || StringUtils.isEmpty(buyGiftsBO.getPlaceId())){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);

        log.info("场所:::{} 用户:::{}，批量修改买赠信息:::{}",buyGiftsBO.getPlaceId(),webLoginAccount.getAccountId(), JSONObject.toJSONString(buyGiftsBO));

        buyGiftsService.batchEditBuyGifts(buyGiftsBO);

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }


    /**
     * 批量删除买赠信息
     * @param request
     * @param placeId   场所id
     * @param buyGiftsIds 买赠信息id列表
     * @return
     */
    @PostMapping("/batchDelete")
    public GenericResponse<?> batchDeleteGoods(HttpServletRequest request,
                                               @RequestParam(name = "placeId") String placeId,
                                               @RequestParam(name = "buyGiftsIds") List<String> buyGiftsIds){
        if(CollectionUtils.isEmpty(buyGiftsIds) || StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);

        log.info("场所:::{} 用户:::{}，批量删除买赠:::{} ",placeId,webLoginAccount.getAccountId(),buyGiftsIds);

        buyGiftsService.batchDeleteBuyGifts(placeId,buyGiftsIds);

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }



    /**
     * 根据商品id列表查询买赠商品信息
     * @param request
     * @param placeId
     * @param goodsIdList
     * @return
     */
    @GetMapping("/findBuyGiftsGoods")
    public GenericResponse<ListDTO<BuyGiftsGoodsBO>> findByGoodsIds(HttpServletRequest request,
                                                            @RequestParam String placeId,
                                                            @RequestParam String goodsIdList){
        if(StringUtils.isEmpty(goodsIdList) || StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

//        PlaceAccountBO webLoginAccount = TokenUtil.getWebLoginAccount(request);
//
//        log.info("场所:::{} 用户:::{}，查询买赠商品列表:::{} ",webLoginAccount.getPlaceId(),webLoginAccount.getAccountId(),goodsIdList);

        List<String> goodsIds = Arrays.asList(goodsIdList.split(","));
        List<BuyGiftsGoods> buyGiftsGoodsList =  buyGiftsGoodsService.findByPlaceIdAndGoodsIdIn(placeId,goodsIds);
        List<Goods> byPlaceIdAndGoodsIdIn =  goodsService.findByPlaceIdAndGoodsIdIn(placeId,goodsIds);

        //校验是否存在错误商品id
        if(goodsIds.size() != byPlaceIdAndGoodsIdIn.size()){
            throw new ServiceException(ServiceCodes.MARKET_SHOP_GOODS_NOT_EXIST);
        }
        List<BuyGiftsGoodsBO> buyGiftsGoodsBOS = buyGiftsGoodsList.stream().map(BuyGiftsGoods::toBO).collect(Collectors.toList());

        for (BuyGiftsGoodsBO buyGiftsGoodsBO:buyGiftsGoodsBOS) {
            Optional<Goods> goodsCodeObj = goodsService.findByPlaceIdAndGoodsCodeAndDeleted(placeId, buyGiftsGoodsBO.getGoodsId());
            if(goodsCodeObj.isPresent()){
                Goods goods = goodsCodeObj.get();
                //实时获取买赠赠送商品的价格和图片
                if(!StringUtils.isEmpty(goods.getGoodsPic())){
                    buyGiftsGoodsBO.setGoodsPic(goods.getGoodsPic());
                }
                if(!StringUtils.isEmpty(goods.getCostPrice())){
                    buyGiftsGoodsBO.setGoodsPrice(goods.getCostPrice());
                }
            }
        }
        return new GenericResponse<>(new ListDTO<>(buyGiftsGoodsBOS));
    }

    @GetMapping("/syncDouyinBuyGifts")
    public GenericResponse<?> findByGoodsIds(@RequestParam String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        GenericResponse<SimpleObjDTO> simpleObjDTOGenericResponse = marketDouyinStoreService.queryOnlineGoods(placeId);

        return simpleObjDTOGenericResponse;
    }

    @GetMapping("/syncMeituanBuyGifts")
    public GenericResponse<?> syncMeituanBugGifts(@RequestParam String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        GenericResponse<SimpleObjDTO> simpleObjDTOGenericResponse = marketMeituanStoreService.queryOnlineGoods(placeId);

        return simpleObjDTOGenericResponse;
    }
}
