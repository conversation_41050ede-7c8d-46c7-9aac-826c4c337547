package com.rzx.dim4.marketing.web.controller.api.impl;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBriefBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingCardTypeApi;
import com.rzx.dim4.base.service.feign.marketing.RentApi;
import com.rzx.dim4.base.service.feign.place.PlaceAreaApi;
import com.rzx.dim4.marketing.entity.*;
import com.rzx.dim4.marketing.service.rent.RentAreaDiscountService;
import com.rzx.dim4.marketing.service.rent.RentConfigService;
import com.rzx.dim4.marketing.service.rent.RentGoodsService;
import com.rzx.dim4.marketing.service.rent.RentMemberDiscountService;
import com.rzx.dim4.marketing.service.shop.OrdersService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 设备租赁api实现类
 *
 * <AUTHOR> hwx
 * @since 2025/3/4 13:34
 */
@Slf4j
@RestController
@RequestMapping("/feign/marketing/rent")
public class RentApiImpl implements RentApi {
    @Autowired
    protected RentConfigService rentConfigService;
    @Autowired
    protected RentMemberDiscountService rentMemberDiscountService;

    @Autowired
    protected RentGoodsService rentGoodsService;

    @Autowired
    protected RentAreaDiscountService rentAreaDiscountService;

    @Autowired
    protected OrdersService ordersService;

    @Autowired
    protected PlaceAreaApi placeAreaApi;

    @Autowired
    protected BillingCardTypeApi billingCardTypeApi;


    @Autowired
    protected StringRedisTemplate stringRedisTemplate;


    /**
     * 保存(初始化)设备租赁
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @PostMapping("/initRentConfig")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public GenericResponse<ObjDTO<RentConfigBO>> initRentConfig(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId) {
        log.info("开始初始化保存租赁配置，请求的场所编码：{}", placeId);
        checkoutRequestTicket(requestTicket);
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        try {
            RentConfig addRentConfig = new RentConfig();
            addRentConfig.setPlaceId(placeId);
            addRentConfig.setRentSwitch(0);
            addRentConfig.setCycleMinutes(30);
            addRentConfig.setIgnoreMinutes(0);
            addRentConfig.setRentNotLogout(0);
            // 获取区域列表
            GenericResponse<ListDTO<PlaceAreaBriefBO>> areaResponse = placeAreaApi.findByPlaceId(placeId);
            if (!areaResponse.isResult() || areaResponse.getData() == null || CollectionUtils.isEmpty(areaResponse.getData().getList())) {
                return new GenericResponse<>(new ObjDTO<>());
            }
            List<PlaceAreaBriefBO> placeAreaList = areaResponse.getData().getList();
            List<PlaceAreaBriefBO> distinctPlaceAreaList = placeAreaList.stream()
                    .collect(Collectors.toMap(
                            PlaceAreaBriefBO::getAreaId,  // Key: areaId
                            bo -> bo,                     // Value: 对象本身
                            (existing, replacement) -> existing  // 如果重复，保留已存在的
                    ))
                    .values()                        // 获取所有唯一的 PlaceAreaBriefBO
                    .stream()
                    .collect(Collectors.toList());   // 转回 List<PlaceAreaBriefBO>
            List<String> areaIds = distinctPlaceAreaList.stream().map(PlaceAreaBriefBO::getAreaId).collect(Collectors.toList());
            addRentConfig.setAreaIds(String.join(",", areaIds));
            distinctPlaceAreaList.forEach(item -> {
                RentAreaDiscount rentAreaDiscount = new RentAreaDiscount();
                rentAreaDiscount.setPlaceId(placeId);
                rentAreaDiscount.setAreaId(item.getAreaId());
                rentAreaDiscount.setAreaName(item.getAreaName());
                rentAreaDiscount.setRentDiscount(100);
                rentAreaDiscount.setDepositDiscount(100);
                rentAreaDiscountService.save(rentAreaDiscount);
            });
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<ListDTO<BillingCardTypeBO>> billingCardTypeResponse = billingCardTypeApi.list(requestTicket, placeId);
            if (!areaResponse.isResult() || areaResponse.getData() == null || CollectionUtils.isEmpty(areaResponse.getData().getList())) {
                return new GenericResponse<>(new ObjDTO<>());
            }
            List<BillingCardTypeBO> billingCardTypeList = billingCardTypeResponse.getData().getList();

            List<BillingCardTypeBO> distinctBillingCardTypeList = billingCardTypeList.stream()
                    .collect(Collectors.toMap(
                            BillingCardTypeBO::getCardTypeId,  // Key: cardTypeId
                            bo -> bo,                     // Value: 对象本身
                            (existing, replacement) -> existing  // 如果重复，保留已存在的
                    ))
                    .values()                        // 获取所有唯一的 PlaceAreaBriefBO
                    .stream()
                    .collect(Collectors.toList());   // 转回 List<PlaceAreaBriefBO>

            distinctBillingCardTypeList.forEach(item -> {
                RentMemberDiscount rentMemberDiscount = new RentMemberDiscount();
                rentMemberDiscount.setPlaceId(placeId);
                rentMemberDiscount.setCardTypeId(item.getCardTypeId());
                rentMemberDiscount.setCardType(item.getTypeName());
                rentMemberDiscount.setRentDiscount(100);
                rentMemberDiscount.setDepositDiscount(100);
                rentMemberDiscountService.save(rentMemberDiscount);
            });
            addRentConfig.setDeleted(0);

            List<String> cardTypeIds = distinctBillingCardTypeList.stream().map(BillingCardTypeBO::getCardTypeId).collect(Collectors.toList());
            addRentConfig.setCardIds(String.join(",", cardTypeIds));
            RentConfig saveRentConfig = rentConfigService.save(addRentConfig);
            log.info("设备租赁配置初始化保存成功");
            return new GenericResponse<>(new ObjDTO<>(saveRentConfig.toBO()));
        } catch (Exception e) {
            log.error("设备租赁配置初始化失败", e);
            throw new ServiceException("设备租赁配置初始化失败", e);
        }
    }


    @GetMapping("/findConfigByPlaceId")
    @Override
    public GenericResponse<ObjDTO<RentConfigBO>> findConfigByPlaceId(String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        RentConfigBO configBO = new RentConfigBO();
        Optional<RentConfig> rentConfigOptional = rentConfigService.findOneByPlaceId(placeId);
        if (!rentConfigOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        RentConfig rentConfig = rentConfigOptional.get();
        BeanUtils.copyProperties(rentConfig, configBO);
        List<RentMemberDiscount> rentMemberDiscountList = rentMemberDiscountService.findListByPlaceId(placeId);
        if (CollectionUtils.isNotEmpty(rentMemberDiscountList)) {
            List<RentMemberDiscountBO> rentMemberDiscountBOList = rentMemberDiscountList.stream().map(RentMemberDiscount::toBO).collect(Collectors.toList());
            configBO.setRentMemberDiscountBOList(rentMemberDiscountBOList);
        }
        List<RentAreaDiscount> rentAreaDiscountList = rentAreaDiscountService.findListByPlaceId(placeId);
        if (CollectionUtils.isNotEmpty(rentAreaDiscountList)) {
            List<RentAreaDiscountBO> rentAreaDiscountBOList = rentAreaDiscountList.stream().map(RentAreaDiscount::toBO).collect(Collectors.toList());
            configBO.setRentAreaDiscountBOList(rentAreaDiscountBOList);
        }
        return new GenericResponse<>(new ObjDTO<>(configBO));
    }

    @GetMapping("/findRentOrderBySomeCondition")
    @Override
    public GenericResponse<ListDTO<OrdersBO>> findRentOrderBySomeCondition(String placeId, String cardId) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        Map<String, String> params = new HashMap<>();
        params.put("placeId", placeId);
        params.put("deleted", "0");
        params.put("cardId", cardId);
        params.put("orderType", "10");
        params.put("rentStaus", "1");
        List<Orders> ordersList = ordersService.findAll(params);
        List<OrdersBO> ordersBOListDTO = ordersList.stream().map(Orders::toBO).collect(Collectors.toList());
        return new GenericResponse<>(new ListDTO<>(ordersBOListDTO));
    }

    @GetMapping("/saveOrUpdateRentMemberDiscount")
    @Override
    public GenericResponse<ObjDTO<RentMemberDiscountBO>> saveOrUpdateRentMemberDiscount(String requestTicket, RentMemberDiscountBO bo) {
        log.info("开始保存/编辑租赁-会员卡类型折扣，请求的数据：{}", new Gson().toJson(bo));
        checkoutRequestTicket(requestTicket);
        String placeId = bo.getPlaceId();
        String cardTypeId = bo.getCardTypeId();
        RentMemberDiscount rentMemberDiscount = new RentMemberDiscount();
        rentMemberDiscount.setPlaceId(placeId);
        rentMemberDiscount.setCardTypeId(cardTypeId);
        rentMemberDiscount.setCardType(bo.getCardType());
        rentMemberDiscount.setRentDiscount(bo.getRentDiscount());
        rentMemberDiscount.setDepositDiscount(bo.getDepositDiscount());
        RentMemberDiscount save = rentMemberDiscountService.save(rentMemberDiscount);
        return new GenericResponse<>(new ObjDTO<>(save.toBO()));
    }

    @GetMapping("/saveOrUpdateRentAreaDiscount")
    @Override
    public GenericResponse<ObjDTO<RentAreaDiscountBO>> saveOrUpdateRentAreaDiscount(String requestTicket, RentAreaDiscountBO bo) {
        log.info("开始保存/编辑租赁-区域折扣，请求的数据：{}", new Gson().toJson(bo));
        checkoutRequestTicket(requestTicket);
        String placeId = bo.getPlaceId();
        String areaId = bo.getAreaId();
        Optional<RentAreaDiscount> rentAreaDiscountOpt = rentAreaDiscountService.findByPlaceIdAndAreaId(placeId, areaId);
        if (!rentAreaDiscountOpt.isPresent()) {
            RentAreaDiscount rentAreaDiscount = new RentAreaDiscount();
            rentAreaDiscount.setPlaceId(placeId);
            rentAreaDiscount.setAreaId(bo.getAreaId());
            rentAreaDiscount.setAreaName(bo.getAreaName());
            rentAreaDiscount.setRentDiscount(100);
            rentAreaDiscount.setDepositDiscount(100);
            RentAreaDiscount save = rentAreaDiscountService.save(rentAreaDiscount);
            return new GenericResponse<>(new ObjDTO<>(save.toBO()));
        }
        return new GenericResponse<>(new ObjDTO<>(new RentAreaDiscountBO()));
    }


    @PostMapping("/updateRentAreaDiscountByAreas")
    @Override
    public GenericResponse<ObjDTO<RentAreaDiscountBO>> updateRentAreaDiscountByAreas(String requestTicket,  String placeId, List<PlaceAreaBO> placeAreaBOList)
    {
        log.info("开始保存/编辑租赁-区域折扣，请求的数据：placeId:{},areaIds:{}",placeId, new Gson().toJson(placeAreaBOList));
        checkoutRequestTicket(requestTicket);
        RentAreaDiscount save= null;
        for (PlaceAreaBO item : placeAreaBOList) {
            RentAreaDiscount rentAreaDiscount = new RentAreaDiscount();
            rentAreaDiscount.setPlaceId(placeId);
            rentAreaDiscount.setAreaId(item.getAreaId());
            rentAreaDiscount.setAreaName(item.getAreaName());
            rentAreaDiscount.setRentDiscount(100);
            rentAreaDiscount.setDepositDiscount(100);
            save = rentAreaDiscountService.save(rentAreaDiscount);
        }
        List<String> areaIds=placeAreaBOList.stream().map(PlaceAreaBO::getAreaId).collect(Collectors.toList());
        Optional<RentConfig> rentConfigOptional=rentConfigService.findOneByPlaceId(placeId);
        if (!rentConfigOptional.isPresent()) {
            throw new ServiceException(ServiceCodes.MARKET_RENT_CONFIG_NOT_FOUND);
        }
        RentConfig existRentConfig=rentConfigOptional.get();
        String existAreaIdStr=existRentConfig.getAreaIds();
      // 将 existAreaIdStr 按逗号分割并转换为 List<String>
        List<String> existAreaIds = Arrays.asList(existAreaIdStr.split(","));
        // 合并两个列表（去重可选）
        existAreaIds.addAll(areaIds);
        // 将 mergedList 转换成 "a,b,c" 格式的字符串
        String mergedAreaIdStr = String.join(",", existAreaIds);
        existRentConfig.setAreaIds(mergedAreaIdStr);
        rentConfigService.save(existRentConfig);
        return new GenericResponse<>(new ObjDTO<>(save.toBO()));
    }

    @Override
    public GenericResponse<ObjDTO<RentMemberDiscountBO>> updateRentMemberDiscountByCardTypes(String requestTicket, String placeId, List<BillingCardTypeBO> billingCardTypeBOList) {
        log.info("开始保存/编辑租赁-会员折扣，请求的数据：placeId:{},areaIds:{}",placeId, new Gson().toJson(billingCardTypeBOList));
        checkoutRequestTicket(requestTicket);
        RentMemberDiscount save = null;
       for (BillingCardTypeBO item : billingCardTypeBOList) {
           RentMemberDiscount rentMemberDiscount = new RentMemberDiscount();
           rentMemberDiscount.setPlaceId(placeId);
           rentMemberDiscount.setCardTypeId(item.getCardTypeId());
           rentMemberDiscount.setCardType(item.getTypeName());
           rentMemberDiscount.setRentDiscount(100);
           rentMemberDiscount.setDepositDiscount(100);
           save = rentMemberDiscountService.save(rentMemberDiscount);
       }
        List<String> cardTypeIds=billingCardTypeBOList.stream().map(BillingCardTypeBO::getCardTypeId).collect(Collectors.toList());
        Optional<RentConfig> rentConfigOptional=rentConfigService.findOneByPlaceId(placeId);
        if (!rentConfigOptional.isPresent()) {
            throw new ServiceException(ServiceCodes.MARKET_RENT_CONFIG_NOT_FOUND);
        }
        RentConfig existRentConfig=rentConfigOptional.get();
        String existCardTypeIdStr=existRentConfig.getCardIds();
        // 将 existAreaIdStr 按逗号分割并转换为 List<String>
        List<String> existCardTypeIdS = Arrays.asList(existCardTypeIdStr.split(","));
        // 合并两个列表（去重可选）
        existCardTypeIdS.addAll(cardTypeIds);
        // 将 mergedList 转换成 "a,b,c" 格式的字符串
        String mergedCardIdStr = String.join(",", existCardTypeIdS);
        existRentConfig.setCardIds(mergedCardIdStr);
        rentConfigService.save(existRentConfig);
        return new GenericResponse<>(new ObjDTO<>(save.toBO()));
    }


    public static <S, T> List<T> copyList(List<S> sourceList, Class<T> targetClass) {
        if (sourceList == null) {
            return null;
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            try {
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target); // 字段名相同即可拷贝
                targetList.add(target);
            } catch (Exception e) {
                throw new RuntimeException("Failed to copy list", e);
            }
        }
        return targetList;
    }

    private void checkoutRequestTicket(String requestTicket) {
        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
            log.warn("防重复请求拦截：redis 没有相应的 requestTicket={}，请求被拦截", requestTicket);
            throw new ServiceException(ServiceCodes.NO_TICKET);
        }
    }
}
