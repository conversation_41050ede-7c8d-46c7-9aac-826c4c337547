package com.rzx.dim4.marketing.service.shop;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.billing.third.OnlineBO;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.marketing.rent.RentOrderQueryBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.TopupAndDeductionBusinessBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentRequestBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.bo.user.MiniApp.*;
import com.rzx.dim4.base.cons.BaseConstants;
import com.rzx.dim4.base.dto.*;
import com.rzx.dim4.base.enums.BizServer;
import com.rzx.dim4.base.enums.ClientBusinessIds;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.OrderPayStatus;
import com.rzx.dim4.base.enums.billing.PackageTimeReserveStatus;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.OrderType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.billing.*;
import com.rzx.dim4.base.service.feign.place.PlaceClientApi;
import com.rzx.dim4.base.service.feign.place.PlaceShiftApi;
import com.rzx.dim4.base.service.feign.user.Dim4UserApi;
import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.PayTypeUtil;
import com.rzx.dim4.base.utils.ResultHandleUtil;
import com.rzx.dim4.base.utils.SpringUtils;
import com.rzx.dim4.base.vo.marketing.OrdersTipsVO;
import com.rzx.dim4.marketing.entity.*;
import com.rzx.dim4.marketing.event.GoodsOrderEvent;
import com.rzx.dim4.marketing.repository.LogBuyGiftsVerifyRecordRepository;
import com.rzx.dim4.marketing.repository.OrderRefundRepository;
import com.rzx.dim4.marketing.repository.OrdersRepository;
import com.rzx.dim4.marketing.service.rent.*;
import com.rzx.dim4.marketing.util.RegionChnCodeUtil;
import com.rzx.dim4.marketing.util.shop.GoodsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025年01月14日 15:16
 */
@Slf4j
@Service
public class OrdersService {

    @Autowired
    private RegionChnCodeUtil regionChnCodeUtil;
    @Autowired
    private OrdersRepository ordersRepository;

    @Autowired
    private OrderGoodsService orderGoodsService;

    @Autowired
    private ShopConfigService shopConfigService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private StorageGoodsService storageGoodsService;

    @Autowired
    private BillingServerService billingServerService;

    @Autowired
    private PaymentServerService paymentServerService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private InternetFeeService internetFeeService;

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    @Autowired
    private WechatMessageApi wechatMessageApi;
    @Autowired
    protected PlaceShiftApi placeShiftApi;

    @Autowired
    private LogTopupApi logTopupApi;

    @Autowired
    private AsyncOrderService asyncOrderService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private Dim4UserApi dim4UserApi;

    @Autowired
    private BillingOnlineApi billingOnlineApi;

    @Autowired
    private PlaceClientApi placeClientApi;

    @Autowired
    private PackageTimeReserveApi packageTimeReserveApi;

    @Autowired
    private BillingRulePackageTimeApi billingRulePackageTimeApi;

    @Autowired
    protected OrderRefundGoodsService orderRefundGoodsService;

    @Autowired
    private OrderRefundRepository orderRefundRepository;

    @Autowired
    private LogBuyGiftsVerifyRecordRepository logBuyGiftsVerifyRecordRepository;

    @Autowired
    private BillingCardApi billingCardApi;

    @Autowired
    private BalanceDetailsApi balanceDetailsApi;

    @Autowired
    private RentGoodsService rentGoodsService;

    @Autowired
    private RentConfigService rentConfigService;

    @Autowired
    private RentBlackListService rentBlackListService;


    @Autowired
    private RentMemberDiscountService rentMemberDiscountService;

    @Autowired
    private RentAreaDiscountService rentAreaDiscountService;


    private final static String shopOrderKey = "market_order_key_";

    private final static String MINIAPP_ORDER_KEY = "miniapp_order_key_";

    private final static String shopRentOrderKey = "market_rent_order_key_";

    private static final Gson gson = new Gson();

    @Transactional(rollbackFor = Exception.class)
    public Orders save(Orders order) {
        return ordersRepository.save(order);
    }

    public Optional<Orders> findByPlaceIdAndOrderId(String placeId, String orderId) {
        return ordersRepository.findByPlaceIdAndOrderId(placeId, orderId);
    }

    public Optional<Orders> findByOrderId(String orderId) {
        return ordersRepository.findByOrderId(orderId);
    }

    /**
     * 创建充值订单
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResponse<ObjDTO<PaymentResultBO>> createGoodsOrderForMiniApp(MiniAppTopUpOrdersBO ordersBO) {
        log.info("createGoodsOrderForMiniApp={}", JSONObject.toJSONString(ordersBO));

        if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getIdNumber())) {
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        //校验重复提交订单，2秒防止重复
        String key = MINIAPP_ORDER_KEY + ordersBO.getPlaceId() + "_topup_" + ordersBO.getRealMoney() + "_" + ordersBO.getGoodsId();
        if (stringRedisTemplate.delete(key)) {
            throw new ServiceException(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 10, TimeUnit.SECONDS);


        //校验场所是否开通商超，和场所是否支持当前支付方式
        Optional<ShopConfig> shopConfigOptional = shopConfigService.findByPlaceId(ordersBO.getPlaceId());
        if (!shopConfigOptional.isPresent()) {
            throw new ServiceException(ServiceCodes.SHOP_CONFIG_ERROR);
        }
        ShopConfig shopConfig = shopConfigOptional.get();
        if (shopConfig.getOrderSwitch() == 1) {
            throw new ServiceException(ServiceCodes.MARKET_STOP_TAKING_ORDERS);
        }

        //公共校验库存的方法(目前只有两种库存方式，1 单仓库 校验000000库存，多仓库校验100000库存，)
        String rackId = null;
        if (shopConfig.getStoreType() == 1) {
            rackId = "000000";
        } else if (shopConfig.getStoreType() == 0) {
            //后续如果支持多货架需要查询货架id
            rackId = "100000";
        }


        // 查询场所配置信息: 查询场所是否开通在线支付
        PlaceConfigBO placeConfigBO = ResultHandleUtil.getFromResponse(placeServerService.findPlaceConfigByPlaceId(ordersBO.getPlaceId()), ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        //获取班次信息
        String cashierId = placeConfigBO.getShiftOnlineIncome();
        PlaceShiftBO placeShiftBO = ResultHandleUtil.getFromResponse(placeShiftApi.findWorkingShiftByCashierId(ordersBO.getPlaceId(), cashierId), ServiceCodes.PLACE_SHIFT_NOT_FOUND);

        //查询用户会员卡
        BillingCardBO billingCardBo = ResultHandleUtil.getFromResponse(billingServerService.findBillingCard(ordersBO.getPlaceId(), ordersBO.getIdNumber()), ServiceCodes.BILLING_CARD_NOT_FOUND);


        //查询商品信息
        Optional<Goods> goodsOptional = goodsService.findByPlaceIdAndGoodsId(ordersBO.getPlaceId(), ordersBO.getGoodsId());
        if (!goodsOptional.isPresent()) {
            throw new ServiceException(ServiceCodes.SHOP_GOODS_NOT_EXIST);
        }
        Goods goods = goodsOptional.get();
        if (1 == goods.getSellStatus()) {
            throw new ServiceException(ServiceCodes.MARKET_GOODS_STOP_SELLING);
        }
        if (1 == goods.getOnlinePaySwitch()) {
            throw new ServiceException(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_ONLINE_PAY);
        }

        //校验商品售卖周期
        if (!GoodsUtil.verifyGoodsCycle(goods.toBO(), LocalDateTime.now())) {
            throw new ServiceException(ServiceCodes.MARKET_GOODS_NOT_IN_SALES_TIME);
        }

        //校验库存
        if (goods.getIsCalculateInventory() == 0 && !StringUtils.isEmpty(rackId)) {
            Optional<StorageGoods> storageGoodsOptional = storageGoodsService.findByPlaceIdAndGoodsIdAndStorageRackId(ordersBO.getPlaceId(), goods.getGoodsId(), rackId);
            if (!storageGoodsOptional.isPresent()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
            }
            StorageGoods storageGoods = storageGoodsOptional.get();
            if (ordersBO.getQuantity() > storageGoods.getGoodsStocksNum()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
            }
        }

//        if (!goods.getCardTypeIds().contains(ordersBO.getCardTypeId())) {
//            throw new ServiceException(ordersBO.getGoodsName() + ServiceCodes.MARKET_CARD_TYPE_NONSUPPORT.getMessage());
//        }

        //校验商品限购
        if (goods.getExtCount() > 0) {
            LocalDateTime startTime = null;
            LocalDateTime endTime = LocalDateTime.now().minusDays(0).with(LocalTime.MAX); //查询结束时间
            if (goods.getExtType() == 0) {
                //每日限购
                startTime = LocalDateTime.now().minusDays(0).with(LocalTime.MIN); //查询开始时间
            } else if (goods.getExtType() == 1) {
                //每周限购（每七天）
                startTime = LocalDateTime.now().minusDays(7);
            } else if (goods.getExtType() == 2) {
                //每月限购（每三十天）
                startTime = LocalDateTime.now().minusDays(30);
            } else if (goods.getExtType() == 3) {
                //永久限购
                startTime = LocalDateTime.now().minusDays(365);
            }
            //查询购买次数
            int countNum = orderGoodsService.extSumByOrderIdAndGoodsId(ordersBO.getPlaceId(), ordersBO.getIdNumber(), goods.getGoodsId(), startTime, endTime);
            if ((countNum + ordersBO.getQuantity()) > goods.getExtCount()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_GOODS_NUMBER_MAX_ERROR);
            }
        }

        //付款码
        String payCode = ordersBO.getPayCode();
        PayType payType = PayTypeUtil.checkPayType(ordersBO.getPayCode(), PayType.AGGREGATE_PAY);

        //生成订单
        String orderId = "SHO" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();

        Orders orders = new Orders();
        orders.setCreated(LocalDateTime.now());
        orders.setPlaceId(ordersBO.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(ordersBO.getSourceType());
        orders.setTotalMoney(ordersBO.getTotalMoney());
        orders.setRealMoney(ordersBO.getRealMoney());
        orders.setPayType(payType);
        orders.setShiftId(null != placeShiftBO ? placeShiftBO.getShiftId() : "");
        orders.setRemark(ordersBO.getRemark());
        orders.setIdNumber(ordersBO.getIdNumber());
        orders.setIdName(ordersBO.getIdName());
        orders.setCashierId(placeShiftBO != null ? placeShiftBO.getCashierId() : "");
        orders.setCashierName(placeShiftBO != null ? placeShiftBO.getCashierName() : "");
        orders.setClientId(SpecialPlaceClients.MINIAPP.getClientId());
        orders.setClientName(SpecialPlaceClients.MINIAPP.getClientName());
        orders.setStatus(OrderPayStatus.CREATED.getCode());
        orders.setPayCode(payCode);
        orders.setCardId(billingCardBo.getCardId());
        orders.setCardTypeId(billingCardBo.getCardTypeId());
        orders.setStorageRackId(rackId);
        orders.setCreater(ordersBO.getCreater());
        orders.setCreaterName(ordersBO.getCreaterName());
        orders.setIsMeals(ordersBO.getIsMeals());
        orders.setOrderType(OrderType.TOP_UP.getCode());

        OrderGoods orderGoods = new OrderGoods();
        orderGoods.setCreated(LocalDateTime.now());
        orderGoods.setPlaceId(ordersBO.getPlaceId());
        orderGoods.setOrderId(orderId);
        orderGoods.setStatus(OrderPayStatus.CREATED.getCode());

        orderGoods.setGoodsId(ordersBO.getGoodsId());
        orderGoods.setQuantity(ordersBO.getQuantity());
        orderGoods.setDiscounts(ordersBO.getDiscounts());

        orderGoods.setGoodsName(goods.getGoodsName());
        // 赠送金额
        orderGoods.setUnitPrice(goods.getUnitPrice());
        orderGoods.setSpecs(goods.getSpecs());
        if (!StringUtils.isEmpty(goods.getGoodsTypeId())) {
            orderGoods.setGoodsTypeId(goods.getGoodsTypeId());
            orderGoods.setGoodsTypeName(goods.getGoodsTypeName());
        } else {
            orderGoods.setGoodsTypeId(" ");
            orderGoods.setGoodsTypeName("默认分类");
        }

        // 活动ID
        orderGoods.setInternetFeeId(ordersBO.getInternetFeeId());
        if (StringUtils.isEmpty(ordersBO.getInternetFeeId())) {
            orderGoods.setGoodsPresentAmount(0);
        } else {
            internetFeeService.findByPlaceIdAndInternetFeeId(ordersBO.getPlaceId(), ordersBO.getInternetFeeId()).ifPresent(item -> {
                orderGoods.setGoodsPresentAmount(item.getPresentAmount() * Math.max(ordersBO.getQuantity(), 1));
            });
        }

        orderGoods.setGoodsQuota(goods.getGoodsQuota());
        orderGoods.setGoodsCategory(goods.getGoodsCategory());
        // 是否是赠送，0否，1是
        orderGoods.setPresent(0);
        orderGoods.setMealsId(ordersBO.getMealsId());

        //保存订单
        Orders order = ordersRepository.save(orders);
        orderGoodsService.save(orderGoods);
        log.info("==================小程序充值订单{}生成成功================", order.getOrderId());

        List<String> goodsIdList = new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, order, goodsIdList));

        //生成付款订单
        String payAmountYuan = String.valueOf(BigDecimal.valueOf(orders.getRealMoney()).divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(orders.getRealMoney());
        requestBO.setOrderDesc("小程序充值，合计" + payAmountYuan + "元");
        requestBO.setStoreNo(orders.getPlaceId());
        requestBO.setBizOrderId(orderId);
        requestBO.setPayType(orders.getPayType().name());
        requestBO.setBizServer(BizServer.MARKETING.name());
        requestBO.setBizType("shopping");
        requestBO.setIdNumber(orders.getIdNumber());
        requestBO.setPlaceId(orders.getPlaceId());
        requestBO.setPayCode(orders.getPayCode());
        requestBO.setBusinessId(ClientBusinessIds.WECHAT_MINI_APP.getCode());
        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

        log.info("小程序充值订单请求参数:::::PaymentRequestBO={}", new Gson().toJson(requestBO));

        GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService.createPaymentOrder(requestTicket, requestBO);
        ResultHandleUtil.handleObjectResponse(paymentServerResponse, paymentResultBO -> {
            log.info("小程序充值订单返回结果:::::PayResponse={}", new Gson().toJson(paymentResultBO));

            order.setLdOrderId(paymentResultBO.getLdOrderId());
            order.setUpdated(LocalDateTime.now());
            ordersRepository.save(order);

            addLogTopUp(paymentResultBO, order, ordersBO.getCreater(), ordersBO.getCreaterName(), ordersBO.getGoodsTypeName());

            // 如果是扫码枪支付成功，立即调用接口推送
            asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, orders);
        });

        return paymentServerResponse;
    }

    private void addLogTopUp(PaymentResultBO paymentResultBO, Orders orders, Long accountId, String accountName, String cardTypeName) {
        log.info("小程序充值订单:::新增LogTopUp充值记录:::::OrderId={}", orders.getOrderId());

        GenericResponse<ObjDTO<OnlineBO>> onlineResponse = billingOnlineApi.findUserOnlineInfo(orders.getPlaceId(), orders.getIdNumber());


        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        LogTopupBO logTopupBO = new LogTopupBO();
        logTopupBO.setPlaceId(orders.getPlaceId());
        logTopupBO.setCardId(orders.getCardId());
        logTopupBO.setClientId(orders.getClientId());
        logTopupBO.setCardTypeId(orders.getCardTypeId());
        logTopupBO.setCardTypeName(cardTypeName);
        logTopupBO.setIdNumber(orders.getIdNumber());
        logTopupBO.setIdName(orders.getIdName());
        logTopupBO.setCashAmount(orders.getRealMoney());
        logTopupBO.setPresentAmount(0);
        logTopupBO.setPayType(orders.getPayType());
        logTopupBO.setSourceType(orders.getSourceType());
        logTopupBO.setShiftId(orders.getShiftId());
        logTopupBO.setOperator(accountId + "");
        logTopupBO.setOperatorName(accountName);
        logTopupBO.setCreated(LocalDateTime.now());
        logTopupBO.setOrderId(paymentResultBO.getOrderId());
        logTopupBO.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopupBO.setQrcodeUrl(paymentResultBO.getQrcodeUrl());
        logTopupBO.setStatus(1);

        // 如果用户正在上机，clientId就修改为正在上机的clientId，目的是方便收银台根据clientId刷新余额
        ResultHandleUtil.handleObjectResponse(onlineResponse, item -> logTopupBO.setClientId(item.getClientId()));

        log.info("充值订单LogTopUpBO={}", new Gson().toJson(logTopupBO));

        logTopupApi.createLogTopup(requestTicket, logTopupBO);
    }

    /**
     * 创建订单
     *
     * @param ordersBO 商品订单对象
     * @return
     */
    @Transactional
    public GenericResponse<ObjDTO<PaymentResultBO>> createGoodsOrder(OrdersBO ordersBO) {

        log.info("商品点购订单:{}", JSONObject.toJSONString(ordersBO));

        if (ordersBO.getPayType().name().equals(PayType.CASH.name())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getPayType()) || CollectionUtils.isEmpty(ordersBO.getOrderGoodsList())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        //校验重复提交订单，2秒防止重复
        String key = shopOrderKey + ordersBO.getPlaceId() + "_" + ordersBO.getSourceType().name() + "_" + ordersBO.getRealMoney() + "_" + ordersBO.getOrderGoodsList().get(0).getGoodsId();
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(key))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 2, TimeUnit.SECONDS);

        //校验场所是否开通商超，和场所是否支持当前支付方式
        Optional<ShopConfig> byPlaceId = shopConfigService.findByPlaceId(ordersBO.getPlaceId());
        if (!byPlaceId.isPresent()) {
            return new GenericResponse<>(ServiceCodes.SHOP_CONFIG_ERROR);
        }
        ShopConfig shopConfig = byPlaceId.get();

        if (shopConfig.getOrderSwitch() == 1 && SourceType.CASHIER != ordersBO.getSourceType() && ordersBO.getOrderType() < 2) {
            return new GenericResponse<>(ServiceCodes.MARKET_STOP_TAKING_ORDERS);
        }

        if (SourceType.CASHIER.name().equals(ordersBO.getSourceType().name())) {
            //收银台(目前收银台下单校验id、名称、班次必填)
            if (StringUtils.isEmpty(ordersBO.getCashierId()) || StringUtils.isEmpty(ordersBO.getCashierName())) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }

        } else if (SourceType.CLIENT.name().equals(ordersBO.getSourceType().name())) {
            //客户端
            if (StringUtils.isEmpty(ordersBO.getClientId()) || StringUtils.isEmpty(ordersBO.getClientName()) || StringUtils.isEmpty(ordersBO.getCardId())
                    || StringUtils.isEmpty(ordersBO.getIdNumber()) || StringUtils.isEmpty(ordersBO.getIdName())) {
                return new GenericResponse<>(ServiceCodes.NULL_PARAM);
            }
        } else if (SourceType.WECHAT.name().equals(ordersBO.getSourceType().name())) {

            if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getIdName()) || StringUtils.isEmpty(ordersBO.getIdNumber())
                    || StringUtils.isEmpty(ordersBO.getOpenId()) || StringUtils.isEmpty(ordersBO.getPayType()) || StringUtils.isEmpty(ordersBO.getReturnUrl())) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }

        } else { // source的值只能是client 或 cashier， 其他后续在支持
            return new GenericResponse<>(ServiceCodes.SHOP_GOODS_SOURCE_ERROR);
        }

        //查询场所是否开通在线支付
        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(ordersBO.getPlaceId());
        if (!placeConfigResponse.isResult()) {
            return new GenericResponse<>(ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        }
        PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        String payCode = ordersBO.getPayCode(); //付款码
        int payCodeNum = 0;
        PayType payType = ordersBO.getPayType();
        try {
            if (payCode != null) {
                log.info("::::::::::::::::::::::订单请求payCode:::::::::::::::::::::::::::::::::" + payCode);
                payCodeNum = StringUtils.isEmpty(payCode) ? 0 : Integer.parseInt(payCode.substring(0, 2));
                if (payCodeNum >= 10 && payCodeNum <= 15) {
                    // 前缀以10、11、12、13、14、15 开头，则是微信付款码
                    payType = PayType.WECHAT_SCAN;
                } else if (payCodeNum >= 25 && payCodeNum <= 30) {
                    // 前缀以25、26、27、28、29、30 开头，则是支付宝付款码
                    payType = PayType.ALIPAY_SCAN;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        //校验是否有虚拟商品，如果有虚拟商品，则需要校验idnumber必填和已存在会员卡

        List<String> goodsIds = ordersBO.getOrderGoodsList().stream().map(OrderGoodsBO::getGoodsId).distinct().collect(Collectors.toList());

        List<Goods> goodsList = goodsService.findByPlaceIdAndGoodsIdIn(ordersBO.getPlaceId(), goodsIds);

        //查询用户会员卡
        BillingCardBO cardBO = null;
        if (!StringUtils.isEmpty(ordersBO.getIdNumber())) {
            GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(ordersBO.getPlaceId(), ordersBO.getIdNumber());
            if (SourceType.CLIENT.name().equals(ordersBO.getSourceType().name()) && !billingCardResponse.isResult()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            if (billingCardResponse.isResult()) {
                cardBO = billingCardResponse.getData().getObj();
            }
        }

        //获取虚拟商品数量
        long xlCount = goodsList.stream().filter(it -> it.getGoodsCategory() == 1).count();
        if (xlCount > 0 && payType == PayType.BILLING_CARD) {
            return new GenericResponse<>(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
        }
        if (xlCount > 0 && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }
        if (payType == PayType.BILLING_CARD && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }

        // 先从SpecialPlaceClients获取特殊的几个客户端信息
        SpecialPlaceClients specialPlaceClient = SpecialPlaceClients.findByClientId(ordersBO.getClientId());
        if (specialPlaceClient != null) {
            ordersBO.setClientName(specialPlaceClient.getClientName());
        } else {
            // 如果从SpecialPlaceClients未获取到特殊的几个客户端信息，则从数据库查询
            if (!StringUtils.isEmpty(ordersBO.getClientId()) && StringUtils.isEmpty(ordersBO.getClientName())) {
                GenericResponse<ObjDTO<PlaceClientBO>> placeClientResponse = placeServerService.findClientByPlaceIdAndClientId(ordersBO.getPlaceId(), ordersBO.getClientId());
                if (!placeClientResponse.isResult()) {
                    return new GenericResponse<>(placeClientResponse.getMessage());
                }
                PlaceClientBO placeClientBo = placeClientResponse.getData().getObj();
                ordersBO.setClientName(placeClientBo.getHostName());
            }
        }

        //公共校验库存的方法(目前只有两种库存方式，1 单仓库 校验000000库存，多仓库校验100000库存，)
        String rackId = null;
        if (shopConfig.getStoreType() == 1) {
            rackId = "000000";
        } else if (shopConfig.getStoreType() == 0) {
            //后续如果支持多货架需要查询货架id
            rackId = "100000";
        }
        LocalDateTime now = LocalDateTime.now();

        //校验商品信息
        verifyOrderStorage(ordersBO, rackId, payType, now, cardBO);

        //获取班次信息
        PlaceShiftBO shiftBO = null;
        String cashierId = ordersBO.getCashierId();
        if (StringUtils.isEmpty(cashierId)) {
            cashierId = placeConfigBO.getShiftOnlineIncome();
        }
        GenericResponse<ObjDTO<PlaceShiftBO>> workingShiftByCashierId = placeShiftApi.findWorkingShiftByCashierId(ordersBO.getPlaceId(), cashierId);
        if (workingShiftByCashierId.isResult()) {
            shiftBO = workingShiftByCashierId.getData().getObj();
        }

        //生成订单
        String orderId = "SHO" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        Orders orders = new Orders();
        orders.setCreated(now);
        orders.setPlaceId(ordersBO.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(ordersBO.getSourceType());
        orders.setClientId(ordersBO.getClientId());
        orders.setClientName(ordersBO.getClientName());
        orders.setTotalMoney(ordersBO.getTotalMoney());
        orders.setRealMoney(ordersBO.getRealMoney());
        orders.setPayType(payType);
        orders.setShiftId(null != shiftBO ? shiftBO.getShiftId() : "");
        orders.setRemark(ordersBO.getRemark());
        orders.setCashierId(ordersBO.getCashierId());
        orders.setCashierName(ordersBO.getCashierName());
        orders.setIdNumber(ordersBO.getIdNumber());
        orders.setIdName(ordersBO.getIdName());
        orders.setStatus(0);
        orders.setPayCode(payCode);
        if (cardBO != null) {
            orders.setCardTypeId(cardBO.getCardTypeId());
            orders.setCardId(cardBO.getCardId());
        }
        orders.setStorageRackId(rackId);
        orders.setCreater(ordersBO.getCreater());
        orders.setCreaterName(ordersBO.getCreaterName());
        orders.setIsMeals(ordersBO.getIsMeals());
        orders.setOrderType(1);
        //网费订单
        if (ordersBO.getOrderType() == 3) {
            orders.setOrderType(3);
        }

        List<OrderGoods> orderGoodsList = new ArrayList<>();
        for (OrderGoodsBO orderGoodsBO : ordersBO.getOrderGoodsList()) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setCreated(now);
            orderGoods.setPlaceId(ordersBO.getPlaceId());
            orderGoods.setOrderId(orderId);
            orderGoods.setGoodsId(orderGoodsBO.getGoodsId());
            orderGoods.setGoodsName(orderGoodsBO.getGoodsName());
            orderGoods.setQuantity(orderGoodsBO.getQuantity());
            orderGoods.setUnitPrice(orderGoodsBO.getUnitPrice());
            orderGoods.setSpecs(orderGoodsBO.getSpecs());
            orderGoods.setDiscounts(orderGoodsBO.getDiscounts());
            orderGoods.setGoodsTypeId(orderGoodsBO.getGoodsTypeId());
            orderGoods.setGoodsTypeName(orderGoodsBO.getGoodsTypeName());
            if (StringUtils.isEmpty(orderGoodsBO.getGoodsTypeId())) {
                orderGoods.setGoodsTypeId(" ");
                orderGoods.setGoodsTypeName("默认分类");
            }
            orderGoods.setGoodsPresentAmount(orderGoodsBO.getGoodsPresentAmount());
            orderGoods.setInternetFeeId(orderGoodsBO.getInternetFeeId());
            List<Goods> collect = goodsList.stream().filter(it -> it.getGoodsId().equals(orderGoodsBO.getGoodsId())).collect(Collectors.toList());
            if (collect.size() != 1) {
                return new GenericResponse<>(ServiceCodes.MARKET_SHOP_RACK_STORAGE_DATA_ERROR);
            } else {
                orderGoods.setGoodsQuota(collect.get(0).getGoodsQuota());
                orderGoods.setGoodsCategory(collect.get(0).getGoodsCategory());
            }

//            orderGoods.setStatus();
            orderGoods.setPresent(orderGoodsBO.getPresent());
            orderGoods.setMealsId(orderGoodsBO.getMealsId());
            orderGoods.setStatus(0);
            orderGoodsList.add(orderGoods);
        }
        //保存订单
        Orders order = ordersRepository.save(orders);
        orderGoodsService.saveAll(orderGoodsList);

        List<String> goodsIdList1 = new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, order, goodsIdList1));

        if (orders.getPayType() == PayType.BILLING_CARD) {//卡扣支付

            //初始化公共修改库存方法参数
            Map<String, Integer> maps = new HashMap<>();
//            orderGoodsList = orderGoodsList.stream().filter(it-> it.getGoodsCategory() == 0).collect(Collectors.toList());
            for (OrderGoods orderGoods : orderGoodsList) {
                if (maps.containsKey(orderGoods.getGoodsId())) {
                    maps.put(orderGoods.getGoodsId(), maps.get(orderGoods.getGoodsId()) + orderGoods.getQuantity());
                } else {
                    maps.put(orderGoods.getGoodsId(), orderGoods.getQuantity());
                }
                //调整一下商品价格，改库存写日志时需要写入
                for (Goods goods : goodsList) {
                    if (goods.getGoodsId().equals(orderGoods.getGoodsId())) {
                        goods.setUnitPrice(orderGoods.getUnitPrice());
                    }
                }
            }
            //扣除库存
            int returnCode = storageGoodsService.updateGoodsStocks(ordersBO.getPlaceId(), maps, order.getOrderId(), goodsList, 7,
                    false, order.getStorageRackId(), order.getCreaterName(), order.getSourceType());
            if (returnCode < 0) {
                return new GenericResponse<>(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
            }
            int deductionCash = 0;
            int deductionPresent = 0;
            if (order.getBuckleType() == 0) {
                deductionCash = order.getRealMoney();
            } else {
                deductionPresent = order.getRealMoney();
            }

            // 扣费
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<?> respDeduction = billingServerService.billingCardUpdateAccount(requestTicket, ordersBO.getPlaceId(),
                    cardBO.getCardId(), deductionCash, deductionPresent, 1, order.getSourceType(), null, orderId);
            if (!respDeduction.isResult()) {
                // 扣费失败，还回库存
                storageGoodsService.updateGoodsStocks(ordersBO.getPlaceId(), maps, order.getOrderId(), goodsList, 7,
                        true, order.getStorageRackId(), order.getCreaterName(), order.getSourceType());
                return new GenericResponse<>(respDeduction.getMessage());
            }

            order.setStatus(1);
            order.setPayTime(now);
            save(order);
            orderGoodsService.updateStatusByPlaceIdAndOrderId(order.getPlaceId(), order.getOrderId(), 1, null);

            List<String> goodsIdList2 = new ArrayList<>();
            //发布事件
            SpringUtils.publishEvent(new GoodsOrderEvent(this, order, goodsIdList2));

            return new GenericResponse<>(ServiceCodes.NO_ERROR);
        } else if (orders.getPayType() == PayType.AGGREGATE_PAY || orders.getPayType() == PayType.ALIPAY_SCAN || orders.getPayType() == PayType.WECHAT_SCAN) { //聚合扫码支付
            //生成付款订单
            String payAmountYuan = String.valueOf(BigDecimal.valueOf(orders.getRealMoney())
                    .divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
            String orderDes = "购买商品，合计" + payAmountYuan + "元";
            String bizType = "shopping";
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(orders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(orders.getPlaceId());
            requestBO.setBizOrderId(orderId);
            requestBO.setPayType(orders.getPayType().name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(orders.getIdNumber());
            requestBO.setPlaceId(orders.getPlaceId());
            requestBO.setPayCode(orders.getPayCode());
            // 业绩自动化需求新增字段
            requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

            if (SourceType.WECHAT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());
            } else if (SourceType.CLIENT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CLIENT.getCode());
            } else if (SourceType.CASHIER == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CASHIER.getCode());
                if (StringUtils.isEmpty(ordersBO.getClientIp())) {
                    requestBO.setClientIp(ordersBO.getClientIp());
                }
            }

            GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService
                    .createPaymentOrder(requestTicket, requestBO);
            if (paymentServerResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PaymentResultBO paymentResultBO = paymentServerResponse.getData().getObj();
                log.info("创建支付订单后查看返回结果::::::::::payRes={}", new Gson().toJson(paymentServerResponse.getData().getObj()));
                // 如果是扫码枪支付成功，立即调用接口推送
//                if ("0000".equals(paymentResultBO.getPayState()) && (orders.getPayType().equals(PayType.ALIPAY_SCAN)
//                        || orders.getPayType().equals(PayType.WECHAT_SCAN))) {
//                    int poundage = StringUtils.isEmpty(paymentResultBO.getPoundage()) ? 0 : paymentResultBO.getPoundage();
//                    paymentServerService.notifyPaymentOrderSync(paymentResultBO.getOrderId(),poundage);
//                }

                order.setLdOrderId(paymentResultBO.getLdOrderId());
                order.setUpdated(now);
                save(order);
                // 如果是扫码枪支付成功，立即调用接口推送
                asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, orders);
            }
            return paymentServerResponse;
        } else if (orders.getPayType() == PayType.WECHAT_MP) { //公众号支付
            //生成付款订单
            String orderDes = "公众号购买商品，合计" + orders.getRealMoney() / 100 + "元";
            String bizType = "shopping";
            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(orders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(orders.getPlaceId());
            requestBO.setBizOrderId(orders.getOrderId());
            requestBO.setPayType(ordersBO.getPayType().name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(ordersBO.getIdNumber());
            requestBO.setPlaceId(orders.getPlaceId());
            requestBO.setReturnUrl(ordersBO.getReturnUrl());
            requestBO.setOpenId(ordersBO.getOpenId());

            GenericResponse<ObjDTO<PaymentResultBO>> paymentOrder = paymentServerService.createPaymentOrder(requestTicket2, requestBO);
            if (paymentOrder.isResult()) {
                order.setLdOrderId(paymentOrder.getData().getObj().getLdOrderId());
                order.setUpdated(now);
                save(order);
            }


            return paymentOrder;
        }

        return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
    }

    /**
     * 校验订单商品库存、销售状态、销售时间段（不生成订单），校验
     * ordersBO 订单信息
     * rackId 仓库id
     * PayType 支付方式
     * now 当前时间
     *
     * @return 订单需要购买的商品
     */
    public void verifyOrderStorage(OrdersBO ordersBO, String rackId, PayType payType, LocalDateTime now, BillingCardBO cardBO) {

        if (null == cardBO && ordersBO.getOrderType() == 3) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }

        String placeId = ordersBO.getPlaceId();

        List<OrderGoodsBO> orderGoodsList = ordersBO.getOrderGoodsList(); //里面可能有重复的商品.需要去重

        String areaId = null;
        if (!StringUtils.isEmpty(ordersBO.getClientId())) {
            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(placeId, ordersBO.getClientId());
            if (clientByPlaceIdAndClientId.isResult()) {
                areaId = clientByPlaceIdAndClientId.getData().getObj().getAreaId();
            }
        }

        //key = goods ，val = quantity 销售数量
        Map<String, Integer> maps = new HashMap<>();
        for (OrderGoodsBO orderGoodsBO : orderGoodsList) {
            if (maps.containsKey(orderGoodsBO.getGoodsId())) {
                maps.put(orderGoodsBO.getGoodsId(), maps.get(orderGoodsBO.getGoodsId()) + orderGoodsBO.getQuantity());
            } else {
                maps.put(orderGoodsBO.getGoodsId(), orderGoodsBO.getQuantity());
            }
        }
        List<OrderGoodsBO> uniqueList = new ArrayList<>(); //去除重复商品id的List
        List<String> goodIds = new ArrayList<>();
        for (OrderGoodsBO data : orderGoodsList) {
            if (!goodIds.contains(data.getGoodsId())) {
                goodIds.add(data.getGoodsId());
                uniqueList.add(data);
            }
        }

        for (OrderGoodsBO orderGoodsBO : uniqueList) {
//            if(ordersBO.getOrderType() == 3){
//                Optional<InternetFee> byPlaceIdAndGoodsId = internetFeeService.findByPlaceIdAndGoodsId(placeId, orderGoodsBO.getGoodsId());
//                if(byPlaceIdAndGoodsId.isPresent()){
//                    InternetFee internetFee = byPlaceIdAndGoodsId.get();
//                    //卡类型是否有权限
//                    if(!internetFee.getCardTypeIds().contains(cardBO.getCardTypeId())){
//                        throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
//                    }
//                }
//            }
            //查询商品信息
            Optional<Goods> byPlaceIdAndGoodsId = goodsService.findByPlaceIdAndGoodsId(placeId, orderGoodsBO.getGoodsId());
            if (!byPlaceIdAndGoodsId.isPresent()) {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_NOT_EXIST);
            }
            Goods goods = byPlaceIdAndGoodsId.get();
            if (1 == goods.getSellStatus()) {
                throw new ServiceException(ServiceCodes.MARKET_GOODS_STOP_SELLING);
            }
            if (1 == goods.getOnlinePaySwitch()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_ONLINE_PAY);
            }
            if (payType.name().equals(PayType.BILLING_CARD.name())) {
                //校验商品是否支持卡扣
//                ordersBO.getBuckleType() == 0 &&
                if (goods.getSupportCashSwitch() == 1 && goods.getSupportPresentSwitch() == 1) {
                    //商品是否支持余额购买
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
                }
            }
            if (!StringUtils.isEmpty(areaId) && !StringUtils.isEmpty(goods.getAreaIds()) && !goods.getAreaIds().contains(areaId)) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_AREA_ERROR);
            }

            //校验商品售卖周期
            boolean b = GoodsUtil.verifyGoodsCycle(goods.toBO(), now);
            if (!b) {
                throw new ServiceException(ServiceCodes.MARKET_GOODS_NOT_IN_SALES_TIME);
            }
            //卡扣,后续需要区分奖金卡扣和本金卡扣
            if (payType.name().equals(PayType.BILLING_CARD.name())) {
                if (goods.getSupportCashSwitch() == 1) {
                    throw new ServiceException(ServiceCodes.SHOP_GOODS_PROHIBITED_CASH);
                }
            }
            //校验库存
            if (goods.getIsCalculateInventory() == 0 && !StringUtils.isEmpty(rackId)) {
                Optional<StorageGoods> byPlaceIdAndGoodsIdAndStorageRackId = storageGoodsService.findByPlaceIdAndGoodsIdAndStorageRackId(placeId, goods.getGoodsId(), rackId);
                if (!byPlaceIdAndGoodsIdAndStorageRackId.isPresent()) {
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
                }
                StorageGoods storageGoods = byPlaceIdAndGoodsIdAndStorageRackId.get();
                Integer storageNum = maps.get(goods.getGoodsId());
                if (storageNum > storageGoods.getGoodsStocksNum()) {
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
                }
            }
            //校验卡类型权限
            if (!ObjectUtils.isEmpty(cardBO)) {
                if (!goods.getCardTypeIds().contains(cardBO.getCardTypeId())) {
                    throw new ServiceException(orderGoodsBO.getGoodsName() + ServiceCodes.MARKET_CARD_TYPE_NONSUPPORT.getMessage());
                }
            }

            //校验商品限购
            if (goods.getExtCount() > 0) {
                LocalDateTime startTime = null;
                LocalDateTime endTime = LocalDateTime.now().minusDays(0).with(LocalTime.MAX); //查询结束时间
                if (goods.getExtType() == 0) {
                    //每日限购
                    startTime = LocalDateTime.now().minusDays(0).with(LocalTime.MIN); //查询开始时间
                } else if (goods.getExtType() == 1) {
                    //每周限购（每七天）
                    startTime = LocalDateTime.now().minus(7, ChronoUnit.DAYS);
                } else if (goods.getExtType() == 2) {
                    //每月限购（每三十天）
                    startTime = LocalDateTime.now().minus(30, ChronoUnit.DAYS);
                } else if (goods.getExtType() == 3) {
                    //永久限购
                    startTime = LocalDateTime.now().minus(365, ChronoUnit.DAYS);
                }
                //查询购买次数
                if (null != cardBO) {
                    int i = orderGoodsService.extSumByOrderIdAndGoodsId(placeId, cardBO.getIdNumber(), goods.getGoodsId(), startTime, endTime);
                    if ((i + orderGoodsBO.getQuantity()) > goods.getExtCount()) {
                        throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_GOODS_NUMBER_MAX_ERROR);
                    }
                }
            }

        }
        if (ordersBO.getIsMeals() == 0) { //非套餐订单时
            //订单总金额
            int sum = orderGoodsList.stream().mapToInt(it -> (it.getUnitPrice()) * it.getQuantity()).sum();
            if (sum != ordersBO.getRealMoney()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_GOODS_ORDER_TOTAL_ERROR);
            }
        }
        if (payType.name().equals(PayType.BILLING_CARD.name())) {
            // 临时卡不支持卡扣
            if ("1000".equals(cardBO.getCardTypeId())) {
                throw new ServiceException(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
            }
            if (ordersBO.getBuckleType() == 0 && cardBO.getCashAccount() < ordersBO.getRealMoney()) {
                //本金余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            } else if (ordersBO.getBuckleType() == 0 && cardBO.getPresentAccount() < ordersBO.getRealMoney()) {
                //奖励余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }
        }


    }


    /**
     * 回调时需要确认订单信息，校验并扣除库存
     *
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int paymentNotifyOrder(String orderId) {
        log.info("订单::{} marketing回调", orderId);
        Optional<Orders> optOrder = ordersRepository.findByOrderId(orderId);
        if (!optOrder.isPresent()) {
            return -1;
        }
        Orders order = optOrder.get();
        Optional<ShopConfig> byPlaceId = shopConfigService.findByPlaceId(order.getPlaceId());
        ShopConfig shopConfig = byPlaceId.get();

        List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(order.getPlaceId(), orderId);

        List<String> goodsIds = orderGoodsList.stream().map(OrderGoods::getGoodsId).distinct().collect(Collectors.toList());

        List<Goods> goodsList = goodsService.findByPlaceIdAndGoodsIdIn(order.getPlaceId(), goodsIds);

        int topupCost = 0;//本金（钱包）
        int topupPresentAmount = 0;//网费
        String cardTypeId = null;//将等级调整至

        Map<String, Integer> maps = new HashMap<>();
        for (OrderGoods orderGoods : orderGoodsList) {
            if (maps.containsKey(orderGoods.getGoodsId())) {
                maps.put(orderGoods.getGoodsId(), maps.get(orderGoods.getGoodsId()) + orderGoods.getQuantity());
            } else {
                maps.put(orderGoods.getGoodsId(), orderGoods.getQuantity());
            }
            //调整一下商品价格，改库存写日志时需要写入。赠品不需要调整价格
//            for (Goods goods : goodsList) {
//                if (goods.getGoodsId().equals(orderGoods.getGoodsId())) {
//                    goods.setUnitPrice(orderGoods.getUnitPrice());
//                }
//            }
            // 如果是赠品，那么虚拟商品网费归为赠送奖励
            if (orderGoods.getPresent() != 1) {
                topupCost += (orderGoods.getGoodsQuota() * orderGoods.getQuantity());
            } else {
                topupPresentAmount += (orderGoods.getGoodsQuota() * orderGoods.getQuantity());
            }
            // 如果有网费充送且有配置“将等级调整至”功能。网费充值功能一次只有一个商品。补充逻辑：该订单商品不为赠品的时候才有赠送
            if (!StringUtils.isEmpty(orderGoods.getInternetFeeId()) && orderGoods.getPresent() != 1) {
                // 如果别人买商品时赠送的商品是虚拟商品并且绑定了网费充送活动，该如何？网费充送只会在充值的时候生效，购买商品的时候不符合场景
                Optional<InternetFee> internetFeeOptional = internetFeeService.findByPlaceIdAndInternetFeeId(orderGoods.getPlaceId(), orderGoods.getInternetFeeId());
                if (internetFeeOptional.isPresent()) {
                    InternetFee internetFee = internetFeeOptional.get();
                    if (internetFeeOptional.get().getCardTypeIds().contains(order.getCardTypeId())) {
                        topupPresentAmount = internetFee.getPresentAmount();
                        cardTypeId = internetFee.getLevelTo();
                    }
                }
            }
        }

        log.info("订单::{} 查询是否充值网费,topupCost={},topupPresent={}", orderId, topupCost, topupPresentAmount);
        //判断是否有虚拟商品需要充值网费
        if (topupCost > 0 || topupPresentAmount > 0) {

            //计费卡加钱（如果是网费充值会有卡等级调整逻辑更改卡类型）
            String requestTicket1 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket1, requestTicket1, 1, TimeUnit.MINUTES);
            GenericResponse<?> response = billingServerService.billingCardUpdateAccount(requestTicket1, order.getPlaceId(), order.getCardId(), -topupCost, -topupPresentAmount,
                    3, order.getSourceType(), cardTypeId, orderId);
            if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
                return -1;
            } else {
                List<String> collect = orderGoodsList.stream().filter(it -> it.getGoodsCategory() == 1).map(OrderGoods::getGoodsId).collect(Collectors.toList());
                //修改状态
                orderGoodsService.updateStatusByPlaceIdAndOrderIdAndGoodsId(order.getPlaceId(), orderId, order.getStatus(), collect, null);
            }
            if (order.getOrderType() == 3) {
                if (!StringUtils.isEmpty(order.getIdNumber())) {
                    GenericResponse<ObjDTO<BillingCardBO>> serviceBillingCard = billingServerService.findBillingCard(order.getPlaceId(), order.getIdNumber());
                    if (serviceBillingCard.isResult()) {
                        return 3;
                    }
                    BillingCardBO cardBO = serviceBillingCard.getData().getObj();
                    GenericResponse<ObjDTO<PlaceProfileBO>> placeIdProfile = placeServerService.findByPlaceId(order.getPlaceId());
                    //发送充值成功模板推送
                    if (placeIdProfile.isResult()) {
                        wechatMessageApi.sendTopupSuccessMessage(order.getPlaceId(), order.getIdNumber(),
                                placeIdProfile.getData().getObj().getDisplayName(),
                                order.getIdName() + order.getCardId(),
                                (topupCost + topupPresentAmount), cardBO.getTotalAccount(), LocalDateTime.now());
                    }
                    //通知轮询
                    if (order.getSourceType().equals(SourceType.WECHAT)) {
                        sendTopupBusiness(order, cardBO, topupCost, topupPresentAmount);
                    }
                }
                return 3;
            }
        }

        if (shopConfig.getStoreType() == 2) {
            return 2;
        }
        log.info("订单::{} 准备扣除库存", orderId);
        //校验并扣除库存
        int result = storageGoodsService.updateGoodsStocks(order.getPlaceId(), maps, order.getOrderId(), goodsList, 7, false
                , order.getStorageRackId(), order.getCreaterName(), order.getSourceType());
        return result;
    }


    /**
     * 设备租赁回调校验
     *
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int paymentNotifyRentOrder(String orderId) {
        log.info("租赁订单::{}marketing回调", orderId);
        Optional<Orders> optOrder = ordersRepository.findByOrderId(orderId);
        if (!optOrder.isPresent()) {
            return -1;
        }
        Orders order = optOrder.get();


        List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(order.getPlaceId(), orderId);

        List<String> goodsIds = orderGoodsList.stream().map(OrderGoods::getGoodsId).distinct().collect(Collectors.toList());

        List<RentGoods> rentGoodsList = rentGoodsService.findByPlaceIdAndGoodsIdIn(order.getPlaceId(), goodsIds);

        Map<String, Integer> maps = new HashMap<>();
        for (OrderGoods orderGoods : orderGoodsList) {
            if (maps.containsKey(orderGoods.getGoodsId())) {
                maps.put(orderGoods.getGoodsId(), maps.get(orderGoods.getGoodsId()) + orderGoods.getQuantity());
            } else {
                maps.put(orderGoods.getGoodsId(), orderGoods.getQuantity());
            }
        }

        log.info("租赁订单::{}准备扣除库存", orderId);
        //校验并扣除库存
        return rentGoodsService.updateRentGoodsStocks(order.getPlaceId(), maps, rentGoodsList, order.getCreaterName(), order.getSourceType(), false);
    }


    /**
     * 小程序创建订单
     *
     * @param ordersBO 商品订单对象
     * @return
     */
    @Transactional
    public GenericResponse<ObjDTO<PaymentResultBO>> MiniCreateGoodsOrder(OrdersBO ordersBO) {
        log.info("小程序端商品点购订单:{}", JSONObject.toJSONString(ordersBO));
        if (StringUtils.isEmpty(ordersBO.getPlaceId()) || CollectionUtils.isEmpty(ordersBO.getOrderGoodsList())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        //校验重复提交订单，2秒防止重复
        String key = MINIAPP_ORDER_KEY + ordersBO.getPlaceId() + "_" + ordersBO.getSourceType().name() + "_" + ordersBO.getRealMoney() + "_" + ordersBO.getOrderGoodsList().get(0).getGoodsId();
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(key))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 2, TimeUnit.SECONDS);

        //校验场所是否开通商超，和场所是否支持当前支付方式
        Optional<ShopConfig> byPlaceId = shopConfigService.findByPlaceId(ordersBO.getPlaceId());
        if (!byPlaceId.isPresent()) {
            return new GenericResponse<>(ServiceCodes.SHOP_CONFIG_ERROR);
        }
        ShopConfig shopConfig = byPlaceId.get();
        if (shopConfig.getOrderSwitch() == 1) {
            return new GenericResponse<>(ServiceCodes.MARKET_STOP_TAKING_ORDERS);
        }

        //查询场所是否开通在线支付
        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> genericResponse = placeServerService.findPlaceConfigByPlaceId(ordersBO.getPlaceId());
        if (!genericResponse.isResult()) {
            return new GenericResponse<>(ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        }
        PlaceConfigBO placeConfigBO = genericResponse.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }
        //付款码
        String payCode = ordersBO.getPayCode();

        PayType payType = PayTypeUtil.checkPayType(payCode, PayType.AGGREGATE_PAY);

        //校验是否有虚拟商品，如果有虚拟商品，则需要校验idnumber必填和已存在会员卡

        List<String> goodsIds = ordersBO.getOrderGoodsList().stream().map(OrderGoodsBO::getGoodsId).distinct().collect(Collectors.toList());

        List<Goods> goodsList = goodsService.findByPlaceIdAndGoodsIdIn(ordersBO.getPlaceId(), goodsIds);

        //查询用户会员卡
        BillingCardBO cardBO = null;
        if (!StringUtils.isEmpty(ordersBO.getIdNumber())) {
            GenericResponse<ObjDTO<BillingCardBO>> billingCard = billingServerService.findBillingCard(ordersBO.getPlaceId(), ordersBO.getIdNumber());
            if (!billingCard.isResult()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            if (billingCard.isResult()) {
                cardBO = billingCard.getData().getObj();
            }
        }

        //获取虚拟商品数量
        long xlCount = goodsList.stream().filter(it -> it.getGoodsCategory() == 1).count();
        if (xlCount > 0 && payType == PayType.BILLING_CARD) {
            return new GenericResponse<>(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
        }
        if (xlCount > 0 && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }
        if (payType == PayType.BILLING_CARD && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }

        if (!StringUtils.isEmpty(ordersBO.getClientId()) && StringUtils.isEmpty(ordersBO.getClientName())) {
            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(ordersBO.getPlaceId(), ordersBO.getClientId());
            if (!clientByPlaceIdAndClientId.isResult()) {
                return new GenericResponse<>(clientByPlaceIdAndClientId.getMessage());
            }
            PlaceClientBO obj = clientByPlaceIdAndClientId.getData().getObj();
            ordersBO.setClientName(obj.getHostName());
        }
        //公共校验库存的方法(目前只有两种库存方式，1 单仓库 校验000000库存，多仓库校验100000库存，)
        String rackId = null;
        if (shopConfig.getStoreType() == 1) {
            rackId = "000000";
        } else if (shopConfig.getStoreType() == 0) {
            //后续如果支持多货架需要查询货架id
            rackId = "100000";
        }
        LocalDateTime now = LocalDateTime.now();

        //校验商品信息
        verifyOrderStorage(ordersBO, rackId, payType, now, cardBO);

        //获取班次信息
        PlaceShiftBO shiftBO = null;
        String cashierId = ordersBO.getCashierId();
        if (StringUtils.isEmpty(cashierId)) {
            cashierId = placeConfigBO.getShiftOnlineIncome();
        }
        GenericResponse<ObjDTO<PlaceShiftBO>> workingShiftByCashierId = placeShiftApi.findWorkingShiftByCashierId(ordersBO.getPlaceId(), cashierId);
        if (workingShiftByCashierId.isResult()) {
            shiftBO = workingShiftByCashierId.getData().getObj();
        }

        //生成订单
        String orderId = "SHO" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        Orders orders = new Orders();
        orders.setCreated(now);
        orders.setPlaceId(ordersBO.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(ordersBO.getSourceType());
        orders.setClientId(ordersBO.getClientId());
        orders.setClientName(ordersBO.getClientName());
        orders.setTotalMoney(ordersBO.getTotalMoney());
        orders.setRealMoney(ordersBO.getRealMoney());
        orders.setPayType(payType);
        orders.setShiftId(null != shiftBO ? shiftBO.getShiftId() : "");
        orders.setRemark(ordersBO.getRemark());
        orders.setIdNumber(ordersBO.getIdNumber());
        orders.setIdName(ordersBO.getIdName());
        orders.setStatus(0);
        orders.setPayCode(payCode);
        orders.setIsAutoDelivery(ordersBO.getIsAutoDelivery());
        if (cardBO != null) {
            orders.setCardTypeId(cardBO.getCardTypeId());
            orders.setCardId(cardBO.getCardId());
        }
        orders.setStorageRackId(rackId);
        orders.setCreater(ordersBO.getCreater());
        orders.setCreaterName(ordersBO.getCreaterName());
        orders.setIsMeals(ordersBO.getIsMeals());
        orders.setOrderType(1);
        orders.setCashierId(null);

        List<OrderGoods> orderGoodsList = new ArrayList<>();
        for (OrderGoodsBO orderGoodsBO : ordersBO.getOrderGoodsList()) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setCreated(now);
            orderGoods.setPlaceId(ordersBO.getPlaceId());
            orderGoods.setOrderId(orderId);
            orderGoods.setGoodsId(orderGoodsBO.getGoodsId());
            orderGoods.setGoodsName(orderGoodsBO.getGoodsName());
            orderGoods.setQuantity(orderGoodsBO.getQuantity());
            orderGoods.setUnitPrice(orderGoodsBO.getUnitPrice());
            orderGoods.setSpecs(orderGoodsBO.getSpecs());
            orderGoods.setDiscounts(orderGoodsBO.getDiscounts());
            orderGoods.setGoodsTypeId(orderGoodsBO.getGoodsTypeId());
            orderGoods.setGoodsTypeName(orderGoodsBO.getGoodsTypeName());
            if (StringUtils.isEmpty(orderGoodsBO.getGoodsTypeId())) {
                orderGoods.setGoodsTypeId(" ");
                orderGoods.setGoodsTypeName("默认分类");
            }
            orderGoods.setGoodsPresentAmount(orderGoodsBO.getGoodsPresentAmount());
            orderGoods.setInternetFeeId(orderGoodsBO.getInternetFeeId());
            List<Goods> collect = goodsList.stream().filter(it -> it.getGoodsId().equals(orderGoodsBO.getGoodsId())).collect(Collectors.toList());
            if (collect.size() != 1) {
                return new GenericResponse<>(ServiceCodes.MARKET_SHOP_RACK_STORAGE_DATA_ERROR);
            } else {
                orderGoods.setGoodsQuota(collect.get(0).getGoodsQuota());
                orderGoods.setGoodsCategory(collect.get(0).getGoodsCategory());
            }

//            orderGoods.setStatus();
            orderGoods.setPresent(orderGoodsBO.getPresent());
            orderGoods.setMealsId(orderGoodsBO.getMealsId());
            orderGoods.setStatus(0);
            orderGoodsList.add(orderGoods);
        }
        //保存订单
        Orders order = ordersRepository.save(orders);
        orderGoodsService.saveAll(orderGoodsList);

        List<String> goodsIdList = new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, order, goodsIdList));

        //生成付款订单
        String payAmountYuan = String.valueOf(BigDecimal.valueOf(orders.getRealMoney())
                .divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
        String orderDes = "购买商品，合计" + payAmountYuan + "元";
        String bizType = "shopping";
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(orders.getRealMoney());
        requestBO.setOrderDesc(orderDes);
        requestBO.setStoreNo(orders.getPlaceId());
        requestBO.setBizOrderId(orderId);
        requestBO.setPayType(orders.getPayType().name());
        requestBO.setBizServer(BizServer.MARKETING.name());
        requestBO.setBizType(bizType);
        requestBO.setIdNumber(orders.getIdNumber());
        requestBO.setPlaceId(orders.getPlaceId());
        requestBO.setPayCode(orders.getPayCode());
        requestBO.setReturnUrl(ordersBO.getReturnUrl());
        requestBO.setOpenId(ordersBO.getOpenId());
        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

        if (SourceType.MINIAPP == order.getSourceType()) {
            requestBO.setBusinessId(ClientBusinessIds.WECHAT_MINI_APP.getCode());
        }
        GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService
                .createPaymentOrder(requestTicket, requestBO);
        if (paymentServerResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            PaymentResultBO paymentResultBO = paymentServerResponse.getData().getObj();
            log.info("创建支付订单后查看返回结果::::::::::payRes={}", new Gson().toJson(paymentServerResponse.getData().getObj()));
            order.setLdOrderId(paymentResultBO.getLdOrderId());
            order.setUpdated(now);
            save(order);
            // 如果是扫码枪支付成功，立即调用接口推送
            asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, orders);
        }
        return paymentServerResponse;
    }


    //分页查询
    public Page<Orders> findAll(Map<String, String> map, Pageable pageable) {
        return ordersRepository.findAll(getSpecification(map), pageable);
    }

    //小程序订单列表分页查询
    public Page<Orders> miniAppfindAll(Map<String, String> map, Pageable pageable) {
        return ordersRepository.findAll(getMiniAppSpecification(map), pageable);
    }
    //小程序订单列表查询
    public List<Orders> miniAppfindOrderList(Map<String, String> map) {
        return ordersRepository.findAll(getMiniAppSpecification(map));
    }

    public List<Orders> findAll(Map<String, String> map) {
        return ordersRepository.findAll(getSpecification(map));
    }

    public List<Orders> findAll(OrderQueryBo queryBo) {
        return ordersRepository.findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            if (queryBo.getStartDate() != null) {
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), queryBo.getStartDate()));
            }
            if (queryBo.getEndDate() != null) {
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), queryBo.getEndDate()));
            }
            if (queryBo.getOrderType() != null) {
                andPredicateList.add(cb.equal(root.get("orderType"), queryBo.getOrderType()));
            }
            if (queryBo.getNotOrderType() != null) {
                andPredicateList.add(cb.not(cb.equal(root.get("orderType"), queryBo.getNotOrderType())));
            }
            if (queryBo.getNotOrderTypes() != null && !queryBo.getNotOrderTypes().isEmpty()) {
                // 获取 "orderType" 路径
                Path<Integer> orderTypePath = root.get("orderType");

                // 构建 NOT IN 条件
                CriteriaBuilder.In<Integer> notInClause = cb.in(orderTypePath);
                for (Integer type : queryBo.getNotOrderTypes()) {
                    notInClause.value(type);
                }
                // 添加 NOT 条件
                andPredicateList.add(cb.not(notInClause));
            }

            if (!CollectionUtils.isEmpty(queryBo.getStatus())) {
                andPredicateList.add(root.get("status").in(queryBo.getStatus()));
            }

            if (queryBo.getNotStatus() != null) {
                andPredicateList.add(cb.not(cb.equal(root.get("status"), queryBo.getNotStatus())));
            }
            if (!CollectionUtils.isEmpty(queryBo.getRentStatus())) {
                andPredicateList.add(root.get("rentStatus").in(queryBo.getRentStatus()));
            }

            if (!StringUtils.isEmpty(queryBo.getPlaceId())) {
                andPredicateList.add(cb.equal(root.get("placeId"), queryBo.getPlaceId()));
            }
            if (queryBo.getDeleted() != null) {
                andPredicateList.add(cb.equal(root.get("deleted"), queryBo.getDeleted()));
            }
            if (!CollectionUtils.isEmpty(queryBo.getPayType())) {
                andPredicateList.add(root.get("payType").as(PayType.class).in(queryBo.getPayType()));
            }
            if (!CollectionUtils.isEmpty(queryBo.getSourceTypes())) {
                andPredicateList.add(root.get("sourceType").as(SourceType.class).in(queryBo.getSourceTypes()));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        });
    }

    public List<String> findAllOrderIds(OrderQueryBo queryBo) {
        return findAll(queryBo).stream()
                .map(Orders::getOrderId)
                .collect(Collectors.toList());
    }

    private static Specification<Orders> getSpecification(Map<String, String> map) {
        return (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所id
                andPredicateList.add(cb.equal(root.get("placeId"), map.get("placeId")));
            }
            if (map.containsKey("orderId") && !StringUtils.isEmpty(map.get("orderId"))) {// 订单号
                andPredicateList.add(cb.like(root.get("orderId"), "%" + map.get("orderId") + "%"));
            }
            if (map.containsKey("orderIds") && !StringUtils.isEmpty(map.get("orderIds"))) {// 订单id
                Path<Object> path = root.get("orderId");
                String[] orderTypeArr = map.get("orderIds").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> orderTypeList = new ArrayList<>();
                for (String s : orderTypeArr) {
                    orderTypeList.add(s);
                }
                orderTypeList.forEach(in::value);
                andPredicateList.add(in);
            }
            if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 场所id
                andPredicateList.add(cb.like(root.get("idNumber"), "%" + map.get("idNumber") + "%"));
            }

            if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {// 是否删除
                andPredicateList.add(cb.equal(root.get("deleted"), Integer.parseInt(map.get("deleted"))));
            }

            if (map.containsKey("queryDateType") && !StringUtils.isEmpty(map.get("queryDateType"))) {// 查询时间类型
                if ("1".equals(map.get("queryDateType"))) {
                    // 下单开始时间
                    if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                        LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                        andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                    }
                    // 下单结束时间
                    if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                        LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                        andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                    }
                } else if ("2".equals(map.get("queryDateType"))) {
                    // 支付开始时间
                    if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                        LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                        andPredicateList.add(cb.greaterThanOrEqualTo(root.get("payTime").as(LocalDateTime.class), startTime));
                    }
                    // 支付结束时间
                    if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                        LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                        andPredicateList.add(cb.lessThanOrEqualTo(root.get("payTime").as(LocalDateTime.class), endTime));
                    }
                } else if ("3".equals(map.get("queryDateType"))) {
                    // 租赁开始时间
                    if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                        LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                        andPredicateList.add(cb.greaterThanOrEqualTo(root.get("rentStart").as(LocalDateTime.class), startTime));
                    }
                    // 租赁结束时间
                    if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                        String endDateStr = map.get("endDate").toString().trim();
                        if (!endDateStr.isEmpty()) {
                            LocalDateTime endTime = LocalDateTime.parse(endDateStr, fmt);
                            // 如果 rentEnd 为 null，则不限制
                            Predicate rentEndCondition = cb.or(
                                    cb.isNull(root.get("rentEnd")),
                                    cb.lessThanOrEqualTo(root.get("rentEnd"), endTime)
                            );
                            andPredicateList.add(rentEndCondition);

                        }
                    }
                }
            }

            if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {// 来源
                andPredicateList.add(cb.equal(root.get("sourceType").as(SourceType.class), SourceType.valueOf(map.get("sourceType").toString())));
            }

            if (map.containsKey("sourceTypes") && !StringUtils.isEmpty(map.get("sourceTypes"))) {// 来源列表
                CriteriaBuilder.In<SourceType> in = cb.in(root.get("sourceType").as(SourceType.class));
                List<SourceType> sourceTypes = Arrays.stream(map.get("sourceTypes").split(",")).map(SourceType::valueOf).collect(Collectors.toList());
                sourceTypes.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("payType") && !StringUtils.isEmpty(map.get("payType"))) {// 支付方式
                if (map.get("payType").equals(PayType.WECHAT_PAY.name())) {
                    Path<Object> path = root.get("payType");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<PayType> payTypes = new ArrayList<>();
                    payTypes.add(PayType.WECHAT_PAY);
                    payTypes.add(PayType.AGGREGATE_PAY_WECHAT);
                    payTypes.forEach(in::value);
                    andPredicateList.add(in);
                } else if (map.get("payType").equals(PayType.ALIPAY_PAY.name())) {
                    Path<Object> path = root.get("payType");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<PayType> payTypes = new ArrayList<>();
                    payTypes.add(PayType.ALIPAY_PAY);
                    payTypes.add(PayType.AGGREGATE_PAY_ALI);
                    payTypes.forEach(in::value);
                    andPredicateList.add(in);
                } else {
                    andPredicateList.add(cb.equal(root.get("payType").as(PayType.class), PayType.valueOf(map.get("payType").toString())));
                }
            }
            if (map.containsKey("status") && !StringUtils.isEmpty(map.get("status"))) {// 订单状态
                Path<Object> path = root.get("status");
                String[] statuses = map.get("status").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<Integer> statusList = new ArrayList<>();
                for (String s : statuses) {
                    statusList.add(Integer.valueOf(s));
                }
                statusList.forEach(in::value);
                andPredicateList.add(in);
//                andPredicateList.add(cb.equal(root.get("status").as(Integer.class),map.get("status")));
            }
            if (map.containsKey("orderType") && !StringUtils.isEmpty(map.get("orderType"))) {// 订单类型
                Path<Object> path = root.get("orderType");
                String[] orderTypeArr = map.get("orderType").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<Integer> orderTypeList = new ArrayList<>();
                for (String s : orderTypeArr) {
                    orderTypeList.add(Integer.valueOf(s));
                }
                orderTypeList.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("payTypeNotIn") && !StringUtils.isEmpty(map.get("payTypeNotIn"))) {// 不在订单类型
                String[] orderTypeArr = map.get("payTypeNotIn").split(",");
                CriteriaBuilder.In<PayType> in = cb.in(root.get("payType").as(PayType.class));
                List<PayType> list = new ArrayList<>();
                for (String s : orderTypeArr) {
                    list.add(PayType.valueOf(s));
                }
                list.forEach(in::value);
                andPredicateList.add(in.not());
            }

            if (map.containsKey("clientId") && !StringUtils.isEmpty(map.get("clientId"))) {// 客户端id
                andPredicateList.add(cb.equal(root.get("clientId").as(String.class), map.get("clientId")));
            }
            if (map.containsKey("clientName") && !StringUtils.isEmpty(map.get("clientName"))) {// 机器号
                andPredicateList.add(cb.equal(root.get("clientName").as(String.class), map.get("clientName")));
            }
            if (map.containsKey("phoneNumber") && !StringUtils.isEmpty(map.get("phoneNumber"))) {// 联系电话
                andPredicateList.add(cb.like(root.get("phoneNumber"), "%" + map.get("phoneNumber") + "%"));
            }
            if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {// 会员卡id
                andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
            }
            if (map.containsKey("createrName") && !StringUtils.isEmpty(map.get("createrName"))) {// 操作人名称
                andPredicateList.add(cb.like(root.get("createrName").as(String.class), "%" + map.get("createrName") + "%"));
            }
            if (map.containsKey("rentStatus") && !StringUtils.isEmpty(map.get("rentStatus"))) {// 租赁订单状态
                Path<Object> path = root.get("rentStatus");
                String[] statuses = map.get("rentStatus").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<Integer> rentStatusList = new ArrayList<>();
                for (String s : statuses) {
                    rentStatusList.add(Integer.valueOf(s));
                }
                rentStatusList.forEach(in::value);
                andPredicateList.add(in);
            }
            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        };
    }

    private static Specification<Orders> getMiniAppSpecification(Map<String, String> map) {
        return (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所id
                andPredicateList.add(cb.equal(root.get("placeId"), map.get("placeId")));
            }
            if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 班次id
                andPredicateList.add(cb.equal(root.get("shiftId"), map.get("shiftId")));
            }
            if (map.containsKey("buckleType") && !StringUtils.isEmpty(map.get("buckleType"))) {// 卡扣方式
                andPredicateList.add(cb.equal(root.get("buckleType"), map.get("buckleType")));
            }

            if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {// 是否删除
                andPredicateList.add(cb.equal(root.get("deleted"), Integer.parseInt(map.get("deleted"))));
            }

            if (map.containsKey("queryDateType") && !StringUtils.isEmpty(map.get("queryDateType"))) {// 查询时间类型
                if ("1".equals(map.get("queryDateType"))) {
                    // 下单开始时间
                    if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                        LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                        andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                    }
                    // 下单结束时间
                    if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                        LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                        andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                    }
                } else if ("2".equals(map.get("queryDateType"))) {
                    // 支付开始时间
                    if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                        LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                        andPredicateList.add(cb.greaterThanOrEqualTo(root.get("payTime").as(LocalDateTime.class), startTime));
                    }
                    // 支付结束时间
                    if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                        LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                        andPredicateList.add(cb.lessThanOrEqualTo(root.get("payTime").as(LocalDateTime.class), endTime));
                    }
                }
            }

            if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {// 来源
                andPredicateList.add(cb.equal(root.get("sourceType").as(SourceType.class), SourceType.valueOf(map.get("sourceType").toString())));
            }

            if (map.containsKey("sourceTypes") && !StringUtils.isEmpty(map.get("sourceTypes"))) {// 来源列表
                CriteriaBuilder.In<SourceType> in = cb.in(root.get("sourceType").as(SourceType.class));
                List<SourceType> sourceTypes = Arrays.stream(map.get("sourceTypes").split(",")).map(SourceType::valueOf).collect(Collectors.toList());
                sourceTypes.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("payType") && !StringUtils.isEmpty(map.get("payType"))) {// 支付方式
                if (map.get("payType").equals(PayType.WECHAT_PAY.name())) {
                    Path<Object> path = root.get("payType");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<PayType> payTypes = new ArrayList<>();
                    payTypes.add(PayType.WECHAT_PAY);
                    payTypes.add(PayType.WECHAT_MP);
                    payTypes.add(PayType.AGGREGATE_PAY_WECHAT);
                    payTypes.forEach(in::value);
                    andPredicateList.add(in);
                } else if (map.get("payType").equals(PayType.ALIPAY_PAY.name())) {
                    Path<Object> path = root.get("payType");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<PayType> payTypes = new ArrayList<>();
                    payTypes.add(PayType.ALIPAY_PAY);
                    payTypes.add(PayType.AGGREGATE_PAY_ALI);
                    payTypes.add(PayType.ALIPAY_MP);
                    payTypes.forEach(in::value);
                    andPredicateList.add(in);
                } else {
                    andPredicateList.add(cb.equal(root.get("payType").as(PayType.class), PayType.valueOf(map.get("payType").toString())));
                }
            }

            if (map.containsKey("payTypes") && !StringUtils.isEmpty(map.get("payTypes"))) {// 来源列表
                CriteriaBuilder.In<PayType> in = cb.in(root.get("payType").as(PayType.class));
                List<PayType> payTypes = Arrays.stream(map.get("payTypes").split(",")).map(PayType::valueOf).collect(Collectors.toList());
                payTypes.forEach(in::value);
                andPredicateList.add(in);
            }
            if (map.containsKey("status") && !StringUtils.isEmpty(map.get("status"))) {// 订单状态
                Path<Object> path = root.get("status");
                String[] statuses = map.get("status").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<Integer> statusList = new ArrayList<>();
                for (String s : statuses) {
                    statusList.add(Integer.valueOf(s));
                }
                statusList.forEach(in::value);
                andPredicateList.add(in);
//                andPredicateList.add(cb.equal(root.get("status").as(Integer.class), map.get("status")));
            }
            if (map.containsKey("orderType") && !StringUtils.isEmpty(map.get("orderType"))) {// 订单类型
                Path<Object> path = root.get("orderType");
                String[] orderTypeArr = map.get("orderType").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<Integer> orderTypeList = new ArrayList<>();
                for (String s : orderTypeArr) {
                    orderTypeList.add(Integer.valueOf(s));
                }
                orderTypeList.forEach(in::value);
                andPredicateList.add(in);
            }
            if (map.containsKey("clientId") && !StringUtils.isEmpty(map.get("clientId"))) {// 客户端id
                andPredicateList.add(cb.equal(root.get("clientId").as(String.class), map.get("clientId")));
            }
            if (map.containsKey("clientName") && !StringUtils.isEmpty(map.get("clientName"))) {// 机器号
                andPredicateList.add(cb.equal(root.get("clientName").as(String.class), map.get("clientName")));
            }
            if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {// 会员卡id
                andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
            }
            if (map.containsKey("createrName") && !StringUtils.isEmpty(map.get("createrName"))) {// 操作人名称
                andPredicateList.add(cb.like(root.get("createrName").as(String.class), "%" + map.get("createrName") + "%"));
            }
            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        };
    }


    public List<Orders> findByPlaceIdAndStatusInAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, LocalDateTime startTime, LocalDateTime endTime, List<Integer> status) {
        return ordersRepository.findByPlaceIdAndStatusInAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndDeleted(placeId, status, startTime, endTime, 0);
    }

    public GenericResponse<ObjDTO<OrdersBO>> getOrder(String orderId, String placeId) {
        Optional<Orders> byPlaceIdAndOrderId = findByPlaceIdAndOrderId(placeId, orderId);
        if (!byPlaceIdAndOrderId.isPresent()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        Orders orders = byPlaceIdAndOrderId.get();

        return new GenericResponse<>(new ObjDTO<>(orders.toBO()));
    }

    public GenericResponse<PagerDTO<OrdersBO>> findPageList(HttpServletRequest request, String queryDateType, String startDate, String endDate, String sourceType, String status, String payType,
                                                            String createrName, String clientId, String placeId, int size, int start) {

        int page = start / size;

        Map<String, String> params = new HashMap<>();
        params.put("queryDateType", queryDateType);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("sourceType", sourceType);
        params.put("status", status);
        params.put("payType", payType);
        params.put("createrName", createrName);
        params.put("clientId", clientId);
        params.put("placeId", placeId);
        params.put("orderType", "1,2,8");

        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "id");
        Page<Orders> all = findAll(params, pageable);
        if (all.getTotalElements() == 0) {
            return new GenericResponse<>(new PagerDTO<>(0, new ArrayList<>()));
        }

        List<OrdersBO> bos = all.getContent().stream().map(Orders::toBO).collect(Collectors.toList());

        for (OrdersBO bo : bos) {
            List<OrderGoods> byPlaceIdAndOrderId = orderGoodsService.findByPlaceIdAndOrderId(bo.getPlaceId(), bo.getOrderId());
            //判断是否图片为空，为空就补上
            List<String> goodsIds = byPlaceIdAndOrderId.stream().map(OrderGoods::getGoodsId).collect(Collectors.toList());
            List<Goods> allGoods = goodsService.findByPlaceIdAndGoodsIdIn(placeId, goodsIds);

            if (byPlaceIdAndOrderId.size() > 0) {
                List<OrderGoodsBO> orderGoodsBOS = byPlaceIdAndOrderId.stream().map(OrderGoods::toBO).collect(Collectors.toList());
                if (allGoods.size() > 0) {
                    for (OrderGoodsBO orderGoodsBO : orderGoodsBOS) {
                        for (Goods goods : allGoods) {
                            if (goods.getGoodsId().equals(orderGoodsBO.getGoodsId())) {
                                orderGoodsBO.setGoodsPic(goods.getGoodsPic());
                                break;
                            }
                        }
                    }
                }
                bo.setOrderGoodsList(orderGoodsBOS);
            }
            //idNumber 获取会员信息(会员卡类型名称)
            if (!StringUtils.isEmpty(bo.getIdNumber())) {
                GenericResponse<ObjDTO<BillingCardBO>> billingCard = billingServerService.findBillingCard(bo.getPlaceId(), bo.getIdNumber());
                if (billingCard.isResult()) {
                    BillingCardBO obj = billingCard.getData().getObj();
                    bo.setCardTypeId(obj.getCardTypeId());
                    bo.setCardTypeName(obj.getCardTypeName());
                    bo.setTotalAccount(obj.getTotalAccount());
                }
            }
            if (!StringUtils.isEmpty(bo.getCourierId())) {
                GenericResponse<ObjDTO<PlaceAccountBO>> placeAccountByPlaceIdAndAccountId = placeServerService.findPlaceAccountByPlaceIdAndAccountId(bo.getPlaceId(), bo.getCourierId());
                if (placeAccountByPlaceIdAndAccountId.isResult()) {
                    bo.setCourierName(placeAccountByPlaceIdAndAccountId.getData().getObj().getAccountName());
                }
            }
        }

        Collections.sort(bos, new Comparator<OrdersBO>() {
            @Override
            public int compare(OrdersBO o1, OrdersBO o2) {
                // 1. status=3 的优先级最高
                boolean isO1Priority = (o1.getStatus() == 1);
                boolean isO2Priority = (o2.getStatus() == 1);

                if (isO1Priority && !isO2Priority) {
                    return -1; // o1 排在前面
                } else if (!isO1Priority && isO2Priority) {
                    return 1;  // o2 排在前面
                } else {
                    // 2. status 相同的情况下，按 id 降序排序
                    return Long.compare(o2.getId(), o1.getId());
                }
            }
        });

        return new GenericResponse<>(new PagerDTO<>((int) all.getTotalElements(), bos));
    }

    public GenericResponse<ObjDTO<OrdersDetailBO>> queryOrderDetail(String placeId, String orderId) {
        Optional<Orders> orderDetailOptional = ordersRepository.findByPlaceIdAndOrderId(placeId, orderId);
        if (orderDetailOptional.isPresent()) {
            OrdersDetailBO orderDetailBo = orderDetailOptional.get().toOrderDetailBO();
            if (!StringUtils.isEmpty(orderDetailBo.getRuleId())) {
                GenericResponse<ObjDTO<BillingRulePackageTimeBO>> BillingRulePackageTimeResponse = billingRulePackageTimeApi.findByPlaceIdAndRuleId(placeId, orderDetailBo.getRuleId());
                ResultHandleUtil.handleObjectResponse(BillingRulePackageTimeResponse, orderDetailBo::setRulePackageTimeBo);
            }

            List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(placeId, orderId);
            ResultHandleUtil.handleList(orderGoodsList, () -> {
                List<OrderGoodsDetailBO> orderGoodsDetailList = orderGoodsList.stream().map(OrderGoods::toDetailBO)
                        .peek(item -> {
                            if (!StringUtils.isEmpty(item.getInternetFeeId())) {
                                Optional<InternetFee> internetFeeOptional = internetFeeService.findByPlaceIdAndInternetFeeId(placeId, item.getInternetFeeId());
                                internetFeeOptional.ifPresent(internetFee -> item.setInternetFeeBo(internetFee.toBO()));
                            }
                        }).collect(Collectors.toList());

                orderDetailBo.setOrderGoodsList(orderGoodsDetailList);
            });

            return new GenericResponse<>(new ObjDTO<>(orderDetailBo));
        }
        return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_ORDERID);
    }

    public GenericResponse<ObjDTO<OrdersStatusMiniAppBO>> queryOrderStatus(String placeId, String orderId) {
        return ordersRepository.findByPlaceIdAndOrderId(placeId, orderId)
                .map(orders -> GenericResponse.toSuccessResponse(orders.toBriefMiniAppBO()))
                .orElseGet(() -> GenericResponse.toFailureResponse(ServiceCodes.PAYMENT_BAD_ORDERID));

    }

    private List<Integer> checkStatus(Integer status) {
        return OrderPayStatus.isShowStatus(status) ? Lists.newArrayList(status) : OrderPayStatus.getShowStatus();
    }

    public GenericResponse<ObjDTO<InternetFeePackageOrderBO>> findPackageOrderPageListForMiniApp(InternetFeeSearchPackageTimeRecordBO paramsBo) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<Integer> statusList = checkStatus(paramsBo.getStatus());

        String placeId = paramsBo.getPlaceId();
        LocalDateTime startTime = LocalDateTime.parse(paramsBo.getStartTime(), fmt);
        LocalDateTime endTime = LocalDateTime.parse(paramsBo.getEndTime(), fmt);
        String searchKey = paramsBo.getSearchKey();
        Long operator = paramsBo.getOperator();
        String sourceType = paramsBo.getSourceType();

        InternetFeePackageOrderBO packageOrderBo = new InternetFeePackageOrderBO();
        packageOrderBo.setPlaceId(placeId);

        Page<Orders> pageResult = ordersRepository.findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("placeId"), placeId));
            predicateList.add(cb.equal(root.get("deleted"), BaseEntity.NO));
            // 如果订单类型是包时订单，查询包时订单和网费支付的包时订单（4和6都表示包时订单）
            predicateList.add(cb.in(root.get("orderType").as(int.class)).value(OrderType.PACKAGE_TIME_BILLING_CARD.getCode()).value(OrderType.PACKAGE_TIME.getCode()));
            predicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            predicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));

            CriteriaBuilder.In<Object> statusCriteriaBuilderIn = cb.in(root.get("status"));
            statusList.forEach(statusCriteriaBuilderIn::value);
            predicateList.add(statusCriteriaBuilderIn);

            if (!StringUtils.isEmpty(sourceType)) {
                predicateList.add(cb.equal(root.get("sourceType").as(String.class), sourceType));
            }

            if (operator != null) {
                predicateList.add(cb.equal(root.get("creater").as(Long.class), operator));
            }

            if (!StringUtils.isEmpty(searchKey)) {
                predicateList.add(cb.or(
                        cb.like(root.get("idNumber").as(String.class), "%" + searchKey + "%"),
                        cb.like(root.get("cardId").as(String.class), "%" + searchKey + "%")
                ));
            }

            Predicate[] andPredicateArr = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(andPredicateArr));
        }, PageRequest.of(paramsBo.getPage(), paramsBo.getSize(), Sort.Direction.DESC, "id"));


        if (pageResult.getTotalElements() == 0L) {
            return new GenericResponse<>(new ObjDTO<>(packageOrderBo));
        }

        Map<String, String> hostNameMap = new HashMap<>();
        Map<String, String> headImgMap = new HashMap<>();

        GenericResponse<ListDTO<BillingOnlineBO>> billingOnlineResponse = billingOnlineApi.queryBillingOnlineByPlaceId(paramsBo.getPlaceId());
        ResultHandleUtil.handleListResponse(billingOnlineResponse, billingOnlineList -> {
            Set<String> clientIds = new HashSet<>();
            Set<String> idNumbers = new HashSet<>();
            Map<String, String> clientIdAndIdNumberMap = billingOnlineList.stream().peek(item -> {
                clientIds.add(item.getClientId());
                idNumbers.add(item.getIdNumber());
            }).collect(Collectors.toMap(BillingOnlineBO::getClientId, BillingOnlineBO::getIdNumber));

            // 根据placeId和clientIds获取机器信息
            GenericResponse<ListDTO<PlaceClientBO>> placeClientResponse = placeClientApi.findByPlaceIdAndClientIds(paramsBo.getPlaceId(), new ArrayList<>(clientIds));
            ResultHandleUtil.handleListResponse(placeClientResponse, placeClients -> {
                // 组装成Map，方便下面查询
                placeClients.forEach(placeClient -> {
                    String idNumber = clientIdAndIdNumberMap.get(placeClient.getClientId());
                    if (StringUtils.isEmpty(idNumber)) {
                        hostNameMap.put(idNumber, placeClient.getHostName());
                    }
                });
            });

            GenericResponse<ListDTO<Dim4UserBO>> dim4UserInfoListResponse = dim4UserApi.getDim4UserInfoList(new ArrayList<>(idNumbers));
            ResultHandleUtil.handleListResponse(dim4UserInfoListResponse, dim4UserInfoList -> {
                dim4UserInfoList.forEach(dim4UserInfo -> headImgMap.put(dim4UserInfo.getIdNumber(), dim4UserInfo.getAuthImageUrl()));
            });
        });

        PackageTimeReserveStatusBO packageTimeReserveStatusBo = new PackageTimeReserveStatusBO();

        List<OrdersMiniAppBO> ordersBoList = pageResult.getContent().stream().map(Orders::toMiniAppBO).collect(Collectors.toList());
        for (OrdersMiniAppBO ordersMiniAppBo : ordersBoList) {
            PackageTimeReserveStatusBO.RequestItem requestItem = new PackageTimeReserveStatusBO.RequestItem();
            requestItem.setOrderId(ordersMiniAppBo.getOrderId());
            requestItem.setPlaceId(ordersMiniAppBo.getPlaceId());
            requestItem.setCardId(ordersMiniAppBo.getCardId());
            packageTimeReserveStatusBo.getRequestItemList().add(requestItem);

            // 判断是否图片为空，为空就补上
            List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(ordersMiniAppBo.getPlaceId(), ordersMiniAppBo.getOrderId());
            ResultHandleUtil.handleList(orderGoodsList, () -> orderGoodsList.forEach(item -> {
                if (!StringUtils.isEmpty(item.getInternetFeeId())) {
                    internetFeeService.findByPlaceIdAndInternetFeeId(placeId, item.getInternetFeeId())
                            .ifPresent(internetFee -> {
                                InternetFeeMiniAppBO internetFeeMiniAppBO = internetFee.toMiniAppBO();
                                goodsService.findByPlaceIdAndGoodsId(placeId, internetFeeMiniAppBO.getGoodsId()).ifPresent(goods -> {
                                    internetFeeMiniAppBO.setGoodsPic(goods.getGoodsPic());
                                });
                                ordersMiniAppBo.getInternetGifts().add(internetFeeMiniAppBO);
                            });
                }
            }));

            GenericResponse<ObjDTO<BillingRulePackageTimeBO>> response = billingRulePackageTimeApi.findByPlaceIdAndRuleId(placeId, ordersMiniAppBo.getRuleId());
            ResultHandleUtil.handleObjectResponse(response, a -> ordersMiniAppBo.setPackageName(a.getRuleName()));


            ordersMiniAppBo.setHeadImageUrl(headImgMap.get(ordersMiniAppBo.getIdNumber()));
            ordersMiniAppBo.setHostName(hostNameMap.get(ordersMiniAppBo.getIdNumber()));

            //idNumber 获取会员信息(会员卡类型名称)
            if (!StringUtils.isEmpty(ordersMiniAppBo.getIdNumber())) {
                GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(ordersMiniAppBo.getPlaceId(), ordersMiniAppBo.getIdNumber());
                ResultHandleUtil.handleObjectResponse(billingCardResponse, billingCardBO -> {
                    ordersMiniAppBo.setCardTypeId(billingCardBO.getCardTypeId());
                    ordersMiniAppBo.setCardTypeName(billingCardBO.getCardTypeName());
                    ordersMiniAppBo.setTotalAccount(billingCardBO.getTotalAccount());
                });
            }
        }

        // 获取包时订单的状态
        GenericResponse<ObjDTO<PackageTimeReserveStatusBO>> packageTimeStatusResponse = packageTimeReserveApi.queryPackageTimeStatus(packageTimeReserveStatusBo);
        ResultHandleUtil.handleObjectResponse(packageTimeStatusResponse, packageTimeReserveStatus -> {
            Map<String, Integer> packageTimeReserveStatusMap = new HashMap<>();
            packageTimeReserveStatus.getResponseItemList().forEach(item -> packageTimeReserveStatusMap.put(item.getOrderId(), item.getStatus()));
            ordersBoList.forEach(a -> a.setPackageTimeStatus(packageTimeReserveStatusMap.getOrDefault(a.getOrderId(), PackageTimeReserveStatus.USED.getCode())));
        });

        ordersBoList.sort((o1, o2) -> {
            // 1. status=3 的优先级最高
            boolean isO1Priority = (o1.getStatus() == 1);
            boolean isO2Priority = (o2.getStatus() == 1);

            if (isO1Priority && !isO2Priority) {
                return -1; // o1 排在前面
            } else if (!isO1Priority && isO2Priority) {
                return 1;  // o2 排在前面
            } else {
                // 2. status 相同的情况下，按 id 降序排序
                return Long.compare(o2.getId(), o1.getId());
            }
        });

        String searchContent = StringUtils.isEmpty(searchKey) ? null : "%" + searchKey + "%";
        if (OrderPayStatus.isNotShowStatus(paramsBo.getStatus())) {
            // 在线支付包时
            int onlineSum = doStatisticsSumForPackage(placeId, sourceType, startTime, endTime, operator, searchContent, OrderPayStatus.getSuccessStatus());
            packageOrderBo.setOnlineSum(onlineSum);

            int onlineCount = getStatisticsCountForPackage(placeId, sourceType, startTime, endTime, operator, searchContent, OrderPayStatus.getSuccessStatus());
            packageOrderBo.setOrderNormalCount(onlineCount);
        } else {
            if (OrderPayStatus.isRefundStatus(paramsBo.getStatus())) {
                packageOrderBo.setOnlineSum(0);
                packageOrderBo.setOrderNormalCount(0);
            } else {
                // 在线支付包时
                int onlineSum = doStatisticsSumForPackage(placeId, sourceType, startTime, endTime, operator, searchContent, statusList);
                packageOrderBo.setOnlineSum(onlineSum);

                int onlineCount = getStatisticsCountForPackage(placeId, sourceType, startTime, endTime, operator, searchContent, statusList);
                packageOrderBo.setOrderNormalCount(onlineCount);
            }
        }

        if (OrderPayStatus.isNotShowStatus(paramsBo.getStatus())) {
            // 在线包时退款
            int onlineRefundSum = doStatisticsSumForPackage(placeId, sourceType, startTime, endTime, operator, searchKey, OrderPayStatus.getRefundStatus());
            packageOrderBo.setOnlineRefundSum(onlineRefundSum);

            int onlineRefundCount = getStatisticsCountForPackage(placeId, sourceType, startTime, endTime, operator, searchKey, OrderPayStatus.getRefundStatus());
            packageOrderBo.setOrderRefundCount(onlineRefundCount);
        } else {
            if (OrderPayStatus.isSuccessStatus(paramsBo.getStatus())) {
                packageOrderBo.setOnlineRefundSum(0);
                packageOrderBo.setOrderRefundCount(0);
            } else {
                // 在线包时退款
                int onlineRefundSum = doStatisticsSumForPackage(placeId, sourceType, startTime, endTime, operator, searchKey, statusList);
                packageOrderBo.setOnlineRefundSum(onlineRefundSum);

                int onlineRefundCount = getStatisticsCountForPackage(placeId, sourceType, startTime, endTime, operator, searchKey, statusList);
                packageOrderBo.setOrderRefundCount(onlineRefundCount);
            }
        }

        packageOrderBo.setPager(new PagerDTO<>((int) pageResult.getTotalElements(), ordersBoList));

        return GenericResponse.toSuccessResponse(packageOrderBo);
    }

    // 统计金额
    private int doStatisticsSumForPackage(String placeId, String sourceType, LocalDateTime startTime, LocalDateTime endTime,
                                          Long creator, String searchKey, List<Integer> statusList) {
        Integer sum = ordersRepository.queryOrderBalanceSum(placeId, OrderType.PACKAGE_TIME.getCode(), startTime, endTime, creator, sourceType, searchKey, statusList);
        return sum == null ? 0 : sum;
    }

    // 统计数量
    private int getStatisticsCountForPackage(String placeId, String sourceType, LocalDateTime startTime, LocalDateTime endTime,
                                             Long creator, String searchKey, List<Integer> statusList) {
        Integer count = ordersRepository.queryOrderBalanceCount(placeId, OrderType.PACKAGE_TIME.getCode(), startTime, endTime, creator, sourceType, searchKey, statusList);
        return count == null ? 0 : count;
    }


    public GenericResponse<ObjDTO<InternetFeeTopUpOrderBO>> findTopUpOrderPageListForMiniApp(InternetFeeSearchTopUpRecordBO paramsBo) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 订单状态 0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款
        List<Integer> statusList = checkStatus(paramsBo.getStatus());

        String placeId = paramsBo.getPlaceId();
        LocalDateTime startTime = LocalDateTime.parse(paramsBo.getStartTime(), fmt);
        LocalDateTime endTime = LocalDateTime.parse(paramsBo.getEndTime(), fmt);
        String searchKey = paramsBo.getSearchKey();
        Long operator = paramsBo.getOperator();
        String sourceType = paramsBo.getSourceType();

        InternetFeeTopUpOrderBO topUpOrderBo = new InternetFeeTopUpOrderBO();
        topUpOrderBo.setPlaceId(placeId);

        Page<Orders> pageResult = ordersRepository.findAll((Specification<Orders>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("placeId"), placeId));
            predicateList.add(cb.equal(root.get("deleted"), 0));
            predicateList.add(cb.equal(root.get("orderType").as(int.class), OrderType.TOP_UP.getCode()));
            predicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            predicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));

            CriteriaBuilder.In<Object> statusCriteriaBuilderIn = cb.in(root.get("status"));
            statusList.forEach(statusCriteriaBuilderIn::value);
            predicateList.add(statusCriteriaBuilderIn);

            if (!StringUtils.isEmpty(sourceType)) {
                predicateList.add(cb.equal(root.get("sourceType").as(String.class), sourceType));
            }

            if (operator != null) {
                predicateList.add(cb.equal(root.get("creater").as(Long.class), operator));
            }

            if (!StringUtils.isEmpty(searchKey)) {
                predicateList.add(cb.or(
                        cb.like(root.get("idNumber").as(String.class), "%" + searchKey + "%"),
                        cb.like(root.get("cardId").as(String.class), "%" + searchKey + "%")
                ));
            }

            Predicate[] andPredicateArr = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(andPredicateArr));
        }, PageRequest.of(paramsBo.getPage(), paramsBo.getSize(), Sort.Direction.DESC, "id"));


        if (pageResult.getTotalElements() == 0L) {
            return new GenericResponse<>(new ObjDTO<>(topUpOrderBo));
        }

        Map<String, String> hostNameMap = new HashMap<>();
        Map<String, String> headImgMap = new HashMap<>();

        GenericResponse<ListDTO<BillingOnlineBO>> billingOnlineResponse = billingOnlineApi.queryBillingOnlineByPlaceId(paramsBo.getPlaceId());
        ResultHandleUtil.handleListResponse(billingOnlineResponse, billingOnlineList -> {
            Set<String> clientIds = new HashSet<>();
            Set<String> idNumbers = new HashSet<>();
            Map<String, String> clientIdAndIdNumberMap = billingOnlineList.stream().peek(item -> {
                clientIds.add(item.getClientId());
                idNumbers.add(item.getIdNumber());
            }).collect(Collectors.toMap(BillingOnlineBO::getClientId, BillingOnlineBO::getIdNumber));

            // 根据placeId和clientIds获取机器信息
            GenericResponse<ListDTO<PlaceClientBO>> placeClientResponse = placeClientApi.findByPlaceIdAndClientIds(paramsBo.getPlaceId(), new ArrayList<>(clientIds));
            ResultHandleUtil.handleListResponse(placeClientResponse, placeClients -> {
                // 组装成Map，方便下面查询
                placeClients.forEach(placeClient -> {
                    String idNumber = clientIdAndIdNumberMap.get(placeClient.getClientId());
                    if (StringUtils.isEmpty(idNumber)) {
                        hostNameMap.put(idNumber, placeClient.getHostName());
                    }
                });
            });

            GenericResponse<ListDTO<Dim4UserBO>> dim4UserInfoListResponse = dim4UserApi.getDim4UserInfoList(new ArrayList<>(idNumbers));
            ResultHandleUtil.handleListResponse(dim4UserInfoListResponse, dim4UserInfoList -> {
                dim4UserInfoList.forEach(dim4UserInfo -> headImgMap.put(dim4UserInfo.getIdNumber(), dim4UserInfo.getAuthImageUrl()));
            });
        });

        //PackageTimeReserveStatusBO packageTimeReserveStatusBo = new PackageTimeReserveStatusBO();

        List<OrdersMiniAppBO> ordersBoList = pageResult.getContent().stream().map(Orders::toMiniAppBO).collect(Collectors.toList());
        for (OrdersMiniAppBO ordersMiniAppBo : ordersBoList) {


            // 判断是否图片为空，为空就补上
            List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(ordersMiniAppBo.getPlaceId(), ordersMiniAppBo.getOrderId());
            ResultHandleUtil.handleList(orderGoodsList, () -> orderGoodsList.forEach(item -> {
                if (!StringUtils.isEmpty(item.getInternetFeeId())) {
                    internetFeeService.findByPlaceIdAndInternetFeeId(placeId, item.getInternetFeeId())
                            .ifPresent(internetFee -> {
                                InternetFeeMiniAppBO internetFeeMiniAppBO = internetFee.toMiniAppBO();
                                goodsService.findByPlaceIdAndGoodsId(placeId, internetFeeMiniAppBO.getGoodsId()).ifPresent(goods -> {
                                    internetFeeMiniAppBO.setGoodsPic(goods.getGoodsPic());
                                });
                                ordersMiniAppBo.getInternetGifts().add(internetFeeMiniAppBO);
                            });
                }
            }));

            ordersMiniAppBo.setHeadImageUrl(headImgMap.get(ordersMiniAppBo.getIdNumber()));
            ordersMiniAppBo.setHostName(hostNameMap.get(ordersMiniAppBo.getIdNumber()));

            //idNumber 获取会员信息(会员卡类型名称)
            if (!StringUtils.isEmpty(ordersMiniAppBo.getIdNumber())) {
                GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(ordersMiniAppBo.getPlaceId(), ordersMiniAppBo.getIdNumber());
                ResultHandleUtil.handleObjectResponse(billingCardResponse, billingCardBO -> {
                    ordersMiniAppBo.setCardTypeId(billingCardBO.getCardTypeId());
                    ordersMiniAppBo.setCardTypeName(billingCardBO.getCardTypeName());
                    ordersMiniAppBo.setTotalAccount(billingCardBO.getTotalAccount());
                });
            }
        }

        ordersBoList.sort((o1, o2) -> {
            // 1. status=3 的优先级最高
            boolean isO1Priority = (o1.getStatus() == 1);
            boolean isO2Priority = (o2.getStatus() == 1);

            if (isO1Priority && !isO2Priority) {
                return -1; // o1 排在前面
            } else if (!isO1Priority && isO2Priority) {
                return 1;  // o2 排在前面
            } else {
                // 2. status 相同的情况下，按 id 降序排序
                return Long.compare(o2.getId(), o1.getId());
            }
        });

        String searchContent = StringUtils.isEmpty(searchKey) ? null : "%" + searchKey + "%";
        // 充值赠送金额
        int giftSum = queryOrderGiftBalanceSumForTopUp(placeId, startTime, endTime, sourceType, operator, searchContent, statusList);
        // 退款金额
        int refundSum = getOrderRefundSumForTopUp(placeId, startTime, endTime, sourceType, operator, searchContent, paramsBo.getStatus());

        // 收银台充值金额
        int cashierSum = getOrderSumForTopUp(placeId, startTime, endTime, sourceType, SourceType.CASHIER, operator, searchContent, statusList);
        // 小程序充值金额
        int miniAppSum = getOrderSumForTopUp(placeId, startTime, endTime, sourceType, SourceType.MINIAPP, operator, searchContent, statusList);
        // 客户端充值金额
        int clientSum = getOrderSumForTopUp(placeId, startTime, endTime, sourceType, SourceType.CLIENT, operator, searchContent, statusList);
        // 公众号充值金额
        int mpSum = getOrderSumForTopUp(placeId, startTime, endTime, sourceType, SourceType.WECHAT, operator, searchContent, statusList);

        topUpOrderBo.setCashierAmount(cashierSum);
        topUpOrderBo.setClientAmount(clientSum);
        topUpOrderBo.setWxmpAmount(mpSum);
        topUpOrderBo.setMiniAppAmount(miniAppSum);
        topUpOrderBo.setTotalAmount(cashierSum + clientSum + mpSum + miniAppSum - refundSum);

        topUpOrderBo.setPresentAmount(giftSum);
        topUpOrderBo.setRefundAmount(refundSum);

        topUpOrderBo.setPager(new PagerDTO<>((int) pageResult.getTotalElements(), ordersBoList));

        return new GenericResponse<>(new ObjDTO<>(topUpOrderBo));
    }

    // 统计退款金额
    private int getOrderRefundSumForTopUp(String placeId, LocalDateTime startTime, LocalDateTime endTime, String sourceType,
                                          Long creator, String searchKey, Integer status) {
        List<Integer> statusList = new ArrayList<>();
        if (status == null) {
            statusList.addAll(OrderPayStatus.getRefundStatus());
        } else if (OrderPayStatus.isRefundStatus(status)) {
            statusList.add(status);
        } else {
            return 0;
        }
        Integer sum = ordersRepository.queryOrderBalanceSum(placeId,  OrderType.TOP_UP.getCode(), startTime, endTime, creator, sourceType, searchKey , statusList);
        return sum == null ? 0 : sum;
    }

    // 统计金额
    private int getOrderSumForTopUp(String placeId, LocalDateTime startTime, LocalDateTime endTime, String sourceTypeName, SourceType sourceType,
                                    Long creator, String searchKey, List<Integer> statusList) {
        SourceType sourceType1 = SourceType.getByName(sourceTypeName);
        if (sourceType1 != null && sourceType1 != sourceType) {
            return 0;
        }
        Integer sum = ordersRepository.queryOrderBalanceSum(placeId, OrderType.TOP_UP.getCode(), startTime, endTime, creator, sourceType.name(),  searchKey , statusList);
        return sum == null ? 0 : sum;
    }

    // 查询赠送金额
    private int queryOrderGiftBalanceSumForTopUp(String placeId, LocalDateTime startTime, LocalDateTime endTime, String sourceType, Long creator,
                                                 String searchKey, List<Integer> statusList) {
        Integer sum = ordersRepository.queryOrderGiftBalanceSum(placeId,  OrderType.TOP_UP.getCode(), startTime, endTime, sourceType, creator,  searchKey , statusList);
        return sum == null ? 0 : sum;
    }

    public GenericResponse<PagerDTO<OrdersBriefBO>> findOrderPage(OrderQueryRequestBO paramsBo) {

        if (StringUtils.isEmpty(paramsBo.getPlaceId())) {
            return GenericResponse.toFailureResponse("场所ID不能为空");
        }

        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<Integer> statusList = new ArrayList<>();
        if (OrderPayStatus.isNotShowStatus(paramsBo.getStatus())) {
            statusList.addAll(OrderPayStatus.getShowStatus());
        } else {
            statusList.add(paramsBo.getStatus());
        }

        Page<Orders> pageResult = ordersRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("placeId"), paramsBo.getPlaceId()));
            predicateList.add(cb.equal(root.get("deleted"), 0));

            CriteriaBuilder.In<Integer> statusInCriteriaBuilder = cb.in(root.get("status"));
            statusList.forEach(statusInCriteriaBuilder::value);
            predicateList.add(statusInCriteriaBuilder);

            if (paramsBo.getOrderType() != null) {
                // 如果订单类型是包时订单，查询包时订单和网费支付的包时订单（4和6都表示包时订单）
                if (OrderType.PACKAGE_TIME.getCode() == paramsBo.getOrderType() || OrderType.PACKAGE_TIME_BILLING_CARD.getCode() == paramsBo.getOrderType()) {
                    predicateList.add(cb.in(root.get("orderType").as(int.class)).value(OrderType.PACKAGE_TIME_BILLING_CARD.getCode()).value(OrderType.PACKAGE_TIME.getCode()));
                } else {
                    predicateList.add(cb.equal(root.get("orderType").as(int.class), paramsBo.getOrderType()));
                }
            }

            if (!StringUtils.isEmpty(paramsBo.getStartTime())) {
                LocalDateTime startTime = LocalDateTime.parse(paramsBo.getStartTime(), fmt);
                predicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            }

            if (!StringUtils.isEmpty(paramsBo.getEndTime())) {
                LocalDateTime endTime = LocalDateTime.parse(paramsBo.getEndTime(), fmt);
                predicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
            }

            if (!StringUtils.isEmpty(paramsBo.getCardId())) {
                predicateList.add(cb.equal(root.get("cardId").as(String.class), paramsBo.getCardId()));
            }

            if (!StringUtils.isEmpty(paramsBo.getOrderId())) {
                predicateList.add(cb.equal(root.get("orderId").as(String.class), paramsBo.getOrderId()));
            }

            if (!StringUtils.isEmpty(paramsBo.getIdName())) {
                predicateList.add(cb.like(root.get("idName").as(String.class), "%" + paramsBo.getIdName() + "%"));
            }

            // 身份证号用这个字段查询，请同时加上placeId才会走索引
            if (!StringUtils.isEmpty(paramsBo.getIdNumber())) {
                if (paramsBo.getIdNumber().length() == 18) {
                    predicateList.add(cb.equal(root.get("idNumber").as(String.class), paramsBo.getIdNumber()));
                } else {
                    predicateList.add(cb.like(root.get("idNumber").as(String.class), "%" + paramsBo.getIdNumber() + "%"));
                }
            }

            if (!StringUtils.isEmpty(paramsBo.getPayType())) {
                predicateList.add(cb.equal(root.get("payType").as(String.class), paramsBo.getPayType()));
            }

            if (!StringUtils.isEmpty(paramsBo.getSourceType())) {
                predicateList.add(cb.equal(root.get("sourceType").as(String.class), paramsBo.getSourceType()));
            }

            if (paramsBo.getMoneyMoreThan() != null) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("realMoney").as(int.class), paramsBo.getMoneyMoreThan()));
            }

            Predicate[] andPredicateArr = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(andPredicateArr));
        }, PageRequest.of(paramsBo.getPage(), paramsBo.getSize(), Sort.Direction.DESC, "id"));

        return ResultHandleUtil.toPagerDTOResponse(pageResult, orders -> {
            List<OrdersBriefBO> ordersBriefBoList = orders.stream().map(Orders::toBriefBO).collect(Collectors.toList());

            // 如果是团购订单，需要返回 核销套餐名称、券码 和 团购来源
            if (OrderType.GROUP_PURCHASE.getType().equals(String.valueOf(paramsBo.getOrderType()))) {
                List<String> orderIds = orders.stream().map(Orders::getOrderId).filter(item -> !StringUtils.isEmpty(item)).distinct().collect(Collectors.toList());
                ResultHandleUtil.handleList(orderIds, () -> {
                    List<LogBuyGiftsVerifyRecord> recordList = logBuyGiftsVerifyRecordRepository.findByPlaceIdAndOrderIdIn(paramsBo.getPlaceId(), orderIds);
                    ResultHandleUtil.handleList(recordList, () -> {
                        Map<String, LogBuyGiftsVerifyRecord> recordMap = new HashMap<>();
                        for (LogBuyGiftsVerifyRecord logBuyGiftsVerifyRecord : recordList) {
                            recordMap.put(logBuyGiftsVerifyRecord.getOrderId(), logBuyGiftsVerifyRecord);
                        }
                        ordersBriefBoList.forEach(item -> {
                            LogBuyGiftsVerifyRecord logBuyGiftsVerifyRecord = recordMap.get(item.getOrderId());
                            if (logBuyGiftsVerifyRecord != null) {
                                item.setBuyGiftsName(logBuyGiftsVerifyRecord.getBuyGiftsName());
                                item.setCouponCode(logBuyGiftsVerifyRecord.getCouponCode());
                                item.setSourcePurchase(SourceType.getDescription(logBuyGiftsVerifyRecord.getSourceType()));
                            }
                        });
                    });
                });
            }
            return ordersBriefBoList;
        });
    }

    /**
     * 退款：网费支付的包时订单6
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResponse<SimpleDTO> refundOrderForInternetFeePackageTime(InternetFeeOrderRefundBO paramsBo) {
        log.info("::::::::::::网费支付的包时订单退款: OrdersService.refundOrder orderId={}, placeId={}, specialPlaceClient={}", paramsBo.getOrderId(), paramsBo.getPlaceId(), paramsBo.getSpecialPlaceClient());

        if (StringUtils.isEmpty(paramsBo.getPlaceId()) || StringUtils.isEmpty(paramsBo.getOrderId())) {
            log.info("::::::::::::网费支付的包时订单退款: 退款参数不能为空 OrdersService.refundOrder placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 1.先查询本服务中的订单信息
        Optional<Orders> ordersOptional = ordersRepository.findByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId());
        if (!ordersOptional.isPresent()) {
            log.info("::::::::::::网费支付的包时订单退款: Orders未查到订单, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_BAD_ORDERID);
        }
        Orders orders = ordersOptional.get();

        if (OrderType.PACKAGE_TIME_BILLING_CARD.getCode() != orders.getOrderType()) {
            log.info("::::::::::::网费支付的包时订单退款: 订单类型错误, placeId={}, orderId={}, orderType={}", paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getOrderType());
            throw new ServiceException("订单类型错误");
        }
        if (OrderPayStatus.isRefundStatus(orders.getStatus())) {
            log.info("::::::::::::网费支付的包时订单退款: 订单已退款,请勿重复退款A, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_REFUND_REPEAT);
        }
        if (!OrderPayStatus.checkOrderStatusAlreadyPayed(orders.getStatus())) {
            log.info("::::::::::::网费支付的包时订单退款: 订单支付状态异常,不能退款, placeId={}, orderId={}, orderStatus={}", paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getStatus());
            throw new ServiceException("订单支付状态异常");
        }
        if (orders.getRealMoney() <= 0) {
            log.info("::::::::::::网费支付的包时订单退款: 订单金额为0,不能退款, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException("退款失败，订单金额不能为0");
        }

        boolean isOrderRefundExists = orderRefundRepository.existsByOrderIdAndPlaceId(paramsBo.getOrderId(), paramsBo.getPlaceId());
        if (isOrderRefundExists) {
            log.info("::::::::::::网费支付的包时订单退款: 订单已退款,请勿重复退款B, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_REFUND_REPEAT);
        }

        // 2.OpenFeign检查场所配置
        PlaceConfigBO placeConfigBo = ResultHandleUtil.getFromResponse(placeServerService.findPlaceConfigByPlaceId(paramsBo.getPlaceId()), ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        if (placeConfigBo.getOnlineTopup() == 0) {
            log.info("::::::::::::网费支付的包时订单退款: 场所配置不支持在线支付, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        // 3.OpenFeign检查正在使用或已使用的情况，不能退款
        GenericResponse<ObjDTO<PackageTimeReserveBO>> reserveResponse = packageTimeReserveApi.queryByPlaceIdAndCardIdAndOrderId(paramsBo.getPlaceId(), orders.getCardId(), paramsBo.getOrderId());
        log.info("网费支付的包时订单退款: orderId={}, 检查用户是否在用包时上机的返回结果: {}", paramsBo.getOrderId(), new Gson().toJson(reserveResponse));

        PackageTimeReserveBO packageTimeReserveBo = ResultHandleUtil.getFromResponse(reserveResponse, "系统异常,请稍后重试");
        if (packageTimeReserveBo.getStatus() == PackageTimeReserveStatus.USING.getCode()) {
            log.info("网费支付的包时订单退款: 包时{}正在使用，无法退款, placeId={}, orderId={}, idNumber={}", packageTimeReserveBo.getRuleId(), paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getIdNumber());
            throw new ServiceException("用户正在用这个包时上机，无法退款");
        }
        if (packageTimeReserveBo.getStatus() == PackageTimeReserveStatus.USED.getCode()) {
            log.info("网费支付的包时订单退款: 包时{}已被使用，无法退款, placeId={}, orderId={}, idNumber={}", packageTimeReserveBo.getRuleId(), paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getIdNumber());
            throw new ServiceException("包时已被使用，无法退款");
        }

        // 4.OpenFeign获取班次信息
        PlaceShiftBO placeShiftBo;
        GenericResponse<ObjDTO<PlaceShiftBO>> placeShiftResponse = placeShiftApi.findWorkingShiftByCashierId(paramsBo.getPlaceId(), placeConfigBo.getShiftOnlineIncome());
        if (placeShiftResponse.isResult()) {
            placeShiftBo = placeShiftResponse.getData().getObj();
        } else {
            placeShiftBo = new PlaceShiftBO();
        }

        orders.setStatus(OrderPayStatus.REFUND.getCode());
        orders.setFinishedTime(LocalDateTime.now());
        orders.setRefundTime(LocalDateTime.now());
        orders.setUpdated(LocalDateTime.now());
        orders.setRemark(paramsBo.getRemark());

        String refundId = "REF" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        //组装参数
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setPlaceId(paramsBo.getPlaceId());
        orderRefund.setOrderId(orders.getOrderId());
        orderRefund.setRefundId(refundId);
        // 哪个收银台点的退款就记在这个收银台的班次上
        orderRefund.setShiftId(placeShiftBo != null ? placeShiftBo.getShiftId() : "");
        orderRefund.setCashierId(placeShiftBo != null ? placeShiftBo.getCashierId() : "");
        orderRefund.setCashierName(orders.getCashierName());
        orderRefund.setRefundAmount(orders.getRealMoney());
        orderRefund.setRealMoney(orders.getRealMoney());
        // 退款方式，0只退款，1退货退款  2取消订单 ,3 系统退单
        orderRefund.setRefundType(1);
        orderRefund.setRefundTime(LocalDateTime.now());
        orderRefund.setRemark(paramsBo.getRemark());
        orderRefund.setCreated(LocalDateTime.now());
        orderRefund.setCreaterName(paramsBo.getAccountName());
        orderRefund.setCreater(paramsBo.getAccountId());
        orderRefund.setSourceType(orders.getSourceType());
        orderRefund.setPayRefundId("");

        // 本金账户的退款金额
        int refundCashAmount = packageTimeReserveBo.getCostCashAccount();
        // 奖励账户的退款金额
        int refundPresentAmount = packageTimeReserveBo.getCostPresentAccount();
        // 临时账户的退款金额
        int refundTemporaryAmount = packageTimeReserveBo.getCostTemporaryOnlineAccount();

        log.info("::::::::::::网费支付的包时订单退款: placeId={}, orderId={}, refundCashAmount={}, refundPresentAmount={}, refundTemporaryAmount={}::::::::::::",
                paramsBo.getPlaceId(), paramsBo.getOrderId(), refundCashAmount, refundPresentAmount, refundTemporaryAmount);

        // 组装更新BillingCard余额的请求参数对象
        BillingCardBalanceUpdateRequestBO billingCardBalanceUpdateRequestBo = new BillingCardBalanceUpdateRequestBO();
        billingCardBalanceUpdateRequestBo.setPlaceId(orders.getPlaceId());
        billingCardBalanceUpdateRequestBo.setCardId(orders.getCardId());
        billingCardBalanceUpdateRequestBo.setOrderId(orders.getOrderId());
        billingCardBalanceUpdateRequestBo.setCashAccount(refundCashAmount);
        billingCardBalanceUpdateRequestBo.setTemporaryOnlineAccount(refundTemporaryAmount);
        billingCardBalanceUpdateRequestBo.setPresentAccount(refundPresentAmount);
        billingCardBalanceUpdateRequestBo.setSourceType(orders.getSourceType());
        billingCardBalanceUpdateRequestBo.setAccountId(paramsBo.getAccountId());
        billingCardBalanceUpdateRequestBo.setSpecialPlaceClient(paramsBo.getSpecialPlaceClient());
        billingCardBalanceUpdateRequestBo.setRemark(paramsBo.getRemark());
        billingCardBalanceUpdateRequestBo.setOrderType(OrderType.PACKAGE_TIME_BILLING_CARD.getCode());

        String requestTicket1 = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket1, requestTicket1, 15, TimeUnit.SECONDS);
        GenericResponse<SimpleDTO> accountResponse = billingServerService.refundForInternetFeePackageTimeOrder(requestTicket1, billingCardBalanceUpdateRequestBo);
        log.info("::::::::::::网费支付的包时订单退款: 订单{}退款,调用billing-server的返回结果：{}", orders.getOrderId(), new Gson().toJson(accountResponse));

        if (ResultHandleUtil.isFailure(accountResponse)) {
            log.info("::::::::::::网费支付的包时订单退款: 订单{}退款,调用billing-server失败: {}==============", orders.getOrderId(), accountResponse.getMessage());
            throw new ServiceException("退款失败");
        }

        orderRefundRepository.save(orderRefund);

        int updateResult = ordersRepository.updateOrderToRefundStatus(orders.getPlaceId(), orders.getOrderId(), paramsBo.getRemark());
        log.info("网费支付的包时订单{}的更新后结果={}", orders.getOrderId(), updateResult);

        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, orders, new ArrayList<>()));

        log.info("::::::::::::网费支付的包时订单退款: 订单{}退款成功:::::::::::::::", orders.getOrderId());
        return GenericResponse.toSimpleResponse("退款成功");
    }

    /**
     * 退款：充值3、包时订单4
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResponse<SimpleDTO> refundOrderForTopUpAndPackageTime(InternetFeeOrderRefundBO paramsBo) {
        log.info("::::::::::::订单退款 OrdersService.refundOrder placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());

        if (StringUtils.isEmpty(paramsBo.getPlaceId()) || StringUtils.isEmpty(paramsBo.getOrderId())) {
            log.info("::::::::::::订单退款参数不能为空 OrdersService.refundOrder placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 1.先查询本服务中的订单信息
        Optional<Orders> ordersOptional = ordersRepository.findByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId());
        if (!ordersOptional.isPresent()) {
            log.info("::::::::::::Orders未查到订单, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_BAD_ORDERID);
        }
        Orders orders = ordersOptional.get();

        if (OrderType.PACKAGE_TIME.getCode() != orders.getOrderType() && OrderType.TOP_UP.getCode() != orders.getOrderType()) {
            log.info("::::::::::::订单退款: 订单类型错误, placeId={}, orderId={}, orderType={}", paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getOrderType());
            throw new ServiceException("订单类型错误");
        }
        if (OrderPayStatus.isRefundStatus(orders.getStatus())) {
            log.info("::::::::::::订单退款: 订单已退款,请勿重复退款A, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_REFUND_REPEAT);
        }
        if (!OrderPayStatus.checkOrderStatusAlreadyPayed(orders.getStatus())) {
            log.info("::::::::::::订单退款: 订单支付状态异常,不能退款, placeId={}, orderId={}, orderStatus={}", paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getStatus());
            throw new ServiceException("订单支付状态异常");
        }
        if (orders.getRealMoney() <= 0) {
            log.info("::::::::::::订单退款: 订单金额为0,不能退款, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException("退款失败，订单金额不能为0");
        }

        // 2.OpenFeign检查场所配置
        PlaceConfigBO placeConfigBo = ResultHandleUtil.getFromResponse(placeServerService.findPlaceConfigByPlaceId(paramsBo.getPlaceId()), ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        if (placeConfigBo.getOnlineTopup() == 0) {
            log.info("::::::::::::订单退款: 场所配置不支持在线支付, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        boolean isOrderRefundGoodsExists = orderRefundGoodsService.existsByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId());
        if (isOrderRefundGoodsExists) {
            log.info("::::::::::::订单退款: 订单已退款,请勿重复退款B, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_REFUND_REPEAT);
        }
        boolean isOrderRefundExists = orderRefundRepository.existsByOrderIdAndPlaceId(paramsBo.getOrderId(), paramsBo.getPlaceId());
        if (isOrderRefundExists) {
            log.info("::::::::::::订单退款: 订单已退款,请勿重复退款C, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException(ServiceCodes.PAYMENT_REFUND_REPEAT);
        }

        // 3.OpenFeign查询付款订单
        GenericResponse<ObjDTO<PaymentOrderBO>> paymentOrderResponse = paymentServerService.queryPaymentOrder(paramsBo.getOrderId());
        if (!paymentOrderResponse.isResult()) {
            log.info("::::::::::::PaymentOrder未查到付款订单, placeId={}, orderId={}", paramsBo.getPlaceId(), paramsBo.getOrderId());
            throw new ServiceException("未查到付款订单");
        }

        // 如果是包时订单，正在使用或已使用的情况，不能退款
        if (orders.getOrderType() == OrderType.PACKAGE_TIME.getCode()) {
            GenericResponse<ObjDTO<PackageTimeReserveBO>> reserveResponse = packageTimeReserveApi.queryByPlaceIdAndCardIdAndOrderId(paramsBo.getPlaceId(), orders.getCardId(), paramsBo.getOrderId());
            log.info("包时订单退款:placeId={}, orderId={}, idNumber={}, 检查用户是否在用包时上机的返回结果: {}", paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getIdNumber(), new Gson().toJson(reserveResponse));

            PackageTimeReserveBO packageTimeReserveBo = ResultHandleUtil.getFromResponse(reserveResponse, "系统异常,请稍后重试");

            if (packageTimeReserveBo.getStatus() == PackageTimeReserveStatus.USING.getCode()) {
                log.info("包时订单退款: 包时{}正在使用，无法退款, placeId={}, orderId={}, idNumber={}", packageTimeReserveBo.getRuleId(), paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getIdNumber());
                throw new ServiceException("用户正在用这个包时上机，无法退款");
            }
            if (packageTimeReserveBo.getStatus() == PackageTimeReserveStatus.USED.getCode()) {
                log.info("包时订单退款: 包时{}已被使用，无法退款, placeId={}, orderId={}, idNumber={}", packageTimeReserveBo.getRuleId(), paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getIdNumber());
                throw new ServiceException("包时已被使用，无法退款");
            }
        }

        //获取班次信息
        PlaceShiftBO placeShiftBo;
        GenericResponse<ObjDTO<PlaceShiftBO>> placeShiftResponse = placeShiftApi.findWorkingShiftByCashierId(paramsBo.getPlaceId(), placeConfigBo.getShiftOnlineIncome());
        if (placeShiftResponse.isResult()) {
            placeShiftBo = placeShiftResponse.getData().getObj();
        } else {
            placeShiftBo = new PlaceShiftBO();
        }

        orders.setStatus(OrderPayStatus.REFUND.getCode());
        orders.setFinishedTime(LocalDateTime.now());
        orders.setRefundTime(LocalDateTime.now());
        orders.setUpdated(LocalDateTime.now());
        orders.setRemark(paramsBo.getRemark());

        String refundId = "REF" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        //组装参数
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setPlaceId(paramsBo.getPlaceId());
        orderRefund.setOrderId(orders.getOrderId());
        orderRefund.setRefundId(refundId);
        // 哪个收银台点的退款就记在这个收银台的班次上
        orderRefund.setShiftId(placeShiftBo != null ? placeShiftBo.getShiftId() : "");
        orderRefund.setCashierId(placeShiftBo != null ? placeShiftBo.getCashierId() : "");
        orderRefund.setCashierName(orders.getCashierName());
        orderRefund.setRefundAmount(orders.getRealMoney());
        orderRefund.setRealMoney(orders.getRealMoney());
        // 退款方式，0只退款，1退货退款  2取消订单 ,3 系统退单
        orderRefund.setRefundType(1);
        orderRefund.setRefundTime(LocalDateTime.now());
        orderRefund.setRemark(paramsBo.getRemark());
        orderRefund.setCreated(LocalDateTime.now());
        orderRefund.setCreaterName(paramsBo.getAccountName());
        orderRefund.setCreater(paramsBo.getAccountId());
        orderRefund.setSourceType(orders.getSourceType());
        orderRefund.setPayRefundId("");


        GenericResponse<ObjDTO<BillingCardBriefBO>> placeCardResponse = billingCardApi.findByPlaceIdAndCardId(paramsBo.getPlaceId(), orders.getCardId());
        BillingCardBriefBO billingCardBo = ResultHandleUtil.getFromResponse(placeCardResponse, ServiceCodes.BILLING_CARD_NOT_FOUND);

        // 本金账户余额
        int cashAmount = billingCardBo.getCashAccount();
        // 临时账户余额
        int temporaryOnlineAmount = billingCardBo.getTemporaryOnlineAccount();
        // 奖励账户金额
        int presentAmount = billingCardBo.getPresentAccount();

        // 临时账户 + 本金账户的总和 小于 退款金额，就拒绝退款（在线包时除外）
        if (orders.getOrderType() != OrderType.PACKAGE_TIME.getCode()) {
            if (orders.getRealMoney() > cashAmount + temporaryOnlineAmount) {
                log.info("退款失败：账户余额不足, orderId={},placeId={},本金账户余额={},临时账户余额={},订单退款金额={}", paramsBo.getOrderId(), paramsBo.getPlaceId(), cashAmount, temporaryOnlineAmount, orders.getRealMoney());
                throw new ServiceException("退款失败，账户余额不足");
            }
        }
        // 本金账户的退款金额
        int refundCashAmount = 0;
        // 临时账户的退款金额
        int refundTemporaryOnlineAmount = 0;
        // 奖励账户的退款金额
        AtomicInteger refundPresentAmount = new AtomicInteger(0);

        // 优先扣临时账户的钱，不够再从本金账户扣
        if (temporaryOnlineAmount > 0) {
            if (temporaryOnlineAmount >= orders.getRealMoney()) {
                refundTemporaryOnlineAmount = orders.getRealMoney();
            } else {
                refundTemporaryOnlineAmount = temporaryOnlineAmount;
                refundCashAmount = orders.getRealMoney() - refundTemporaryOnlineAmount;
            }
        } else {
            refundCashAmount = orders.getRealMoney();
        }


        // 订单退款商品关联信息
        List<OrderRefundGoods> orderRefundGoodsList = new ArrayList<>();
        // 商品ID集合
        List<String> goodsIds = new ArrayList<>();

        List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId());
        ResultHandleUtil.handleList(orderGoodsList, () -> {
            // 商品ID集合去重
            Set<String> goodsIdSet = new HashSet<>();

            for (OrderGoods orderGood : orderGoodsList) {
                refundPresentAmount.addAndGet(orderGood.getGoodsPresentAmount());

                // 把商品ID添加到Set集合中
                goodsIdSet.add(orderGood.getGoodsId());

                // 组装订单退款商品关联信息
                OrderRefundGoods orderRefundGoods = new OrderRefundGoods();
                orderRefundGoods.setPlaceId(paramsBo.getPlaceId());
                orderRefundGoods.setOrderId(paramsBo.getOrderId());
                orderRefundGoods.setRefundId(refundId);
                orderRefundGoods.setGoodsId(orderGood.getGoodsId());
                orderRefundGoods.setGoodsName(orderGood.getGoodsName());
                orderRefundGoods.setGoodsTypeId(orderGood.getGoodsTypeId());
                orderRefundGoods.setGoodsTypeName(orderGood.getGoodsTypeName());
                orderRefundGoods.setGoodsCategory(orderGood.getGoodsCategory());
                orderRefundGoods.setQuantity(orderGood.getQuantity());
                orderRefundGoods.setUnitPrice(orderGood.getUnitPrice());
                orderRefundGoods.setDiscounts(orderGood.getDiscounts());
                orderRefundGoods.setPresent(orderGood.getPresent());
                orderRefundGoods.setCreated(LocalDateTime.now());
                orderRefundGoods.setCreater(paramsBo.getAccountId());

                orderRefundGoodsList.add(orderRefundGoods);
            }

            // 将Set转换为List，请勿改变原有顺序
            goodsIds.addAll(goodsIdSet);

            //查询商品信息列表
            List<Goods> goodsList = goodsService.findByPlaceIdAndGoodsIdIn(paramsBo.getPlaceId(), goodsIds);
            for (OrderRefundGoods orderRefundGoods : orderRefundGoodsList) {
                //调整一下商品价格，改库存写日志时需要写入
                for (Goods goods : goodsList) {
                    if (goods.getGoodsId().equals(orderRefundGoods.getGoodsId())) {
                        orderRefundGoods.setUnitPrice(goods.getUnitPrice());
                    }
                }
            }
        });

        // 在线支付包时不需要判断本金
        if (orders.getOrderType() != OrderType.PACKAGE_TIME.getCode()) {
            if (presentAmount < refundPresentAmount.get()) {
                log.info("退款失败：奖励账户余额不足, orderId={},placeId={},奖励账户余额={},订单的奖励退款金额={}", paramsBo.getOrderId(), paramsBo.getPlaceId(), presentAmount, refundPresentAmount.get());
                throw new ServiceException("退款失败，账户中的奖励余额不足");
            }
        }

        log.info("::::::::::::订单退款: placeId={}, orderId={}, cardTypeId={}, refundCashAmount={}, refundPresentAmount={}, refundTemporaryOnlineAmount={}::::::::::::",
                paramsBo.getPlaceId(), paramsBo.getOrderId(), orders.getCardTypeId(), refundCashAmount, refundPresentAmount.get(), refundTemporaryOnlineAmount);

        // 组装更新BillingCard余额的请求参数对象
        BillingCardBalanceUpdateRequestBO billingCardBalanceUpdateRequestBo = new BillingCardBalanceUpdateRequestBO();
        billingCardBalanceUpdateRequestBo.setPlaceId(orders.getPlaceId());
        billingCardBalanceUpdateRequestBo.setCardId(orders.getCardId());
        billingCardBalanceUpdateRequestBo.setOrderId(orders.getOrderId());
        billingCardBalanceUpdateRequestBo.setCashAccount(refundCashAmount);
        billingCardBalanceUpdateRequestBo.setPresentAccount(refundPresentAmount.get());
        billingCardBalanceUpdateRequestBo.setTemporaryOnlineAccount(refundTemporaryOnlineAmount);
        billingCardBalanceUpdateRequestBo.setSourceType(orders.getSourceType());
        billingCardBalanceUpdateRequestBo.setAccountId(paramsBo.getAccountId());
        billingCardBalanceUpdateRequestBo.setSpecialPlaceClient(paramsBo.getSpecialPlaceClient());
        billingCardBalanceUpdateRequestBo.setRemark(paramsBo.getRemark());
        billingCardBalanceUpdateRequestBo.setOrderType(orders.getOrderType());

        // 退款，计费卡加钱
        String requestTicket1 = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket1, requestTicket1, 15, TimeUnit.SECONDS);

        log.info("::::::::::::订单退款: 订单{}退款, 本金：{}, 奖励：{}, 临时卡金额: {}", orders.getOrderId(), refundCashAmount, refundPresentAmount.get(), refundTemporaryOnlineAmount);

        //只有充值或者网费包时的才需要更新

        GenericResponse<SimpleDTO> accountResponse = billingServerService.updateBillingCardAccountForRefund(requestTicket1, billingCardBalanceUpdateRequestBo);
        log.info("::::::::::::订单退款: 订单{}退款,调用billing-server的返回结果：{}", orders.getOrderId(), new Gson().toJson(accountResponse));

        // 如果更新BillingCard失败，一定抛异常回滚
        if (ResultHandleUtil.isFailure(accountResponse)) {
            log.info("::::::::::::订单退款: 订单{}退款,调用billing-server失败: {}==============", orders.getOrderId(), accountResponse.getMessage());
            throw new ServiceException(accountResponse.getMessage());
        }

        if (!PayType.BILLING_CARD.equals(orders.getPayType())) {
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 15, TimeUnit.SECONDS);
            GenericResponse<ObjDTO<PaymentRefundOrderBO>> refundResponse = paymentServerService.refundPaymentOrder(requestTicket, orders.getPlaceId(), paramsBo.getOrderId(), orders.getRealMoney());
            log.info("::::::::::::订单退款: 订单{}退款,调用payment-server的返回结果：{}", orders.getOrderId(), new Gson().toJson(refundResponse));

            // 如果退款失败，一定抛异常回滚
            if (ResultHandleUtil.isFailure(refundResponse)) {
                log.info("::::::::::::订单退款: 订单{}退款,调用payment-server失败: {}==============", orders.getOrderId(), refundResponse.getMessage());
                throw new ServiceException(ServiceCodes.PAYMENT_REFUND_ERROR);
            }
            PaymentRefundOrderBO paymentRefundOrderBO = refundResponse.getData().getObj();
            orderRefund.setPayRefundId(paymentRefundOrderBO.getRefundOrderId());
        }

        orderRefundRepository.save(orderRefund);
        orderRefundGoodsService.saveAll(orderRefundGoodsList);

        int updateResult = ordersRepository.updateOrderToRefundStatus(orders.getPlaceId(), orders.getOrderId(), paramsBo.getRemark());
        log.info("退款订单{}的更新后结果={}", orders.getOrderId(), updateResult);

        orderGoodsService.updateStatusByPlaceIdAndOrderIdAndGoodsId(paramsBo.getPlaceId(), paramsBo.getOrderId(), OrderPayStatus.REFUND.getCode(), goodsIds, null);

        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, orders, goodsIds));

        log.info("::::::::::::订单退款: 订单{}退款成功:::::::::::::::", orders.getOrderId());

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 获取当前门店订单tips
     *
     * @param placeId
     * @return
     */
    public List<OrdersTipsVO> findTips(String placeId) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        String key = BaseConstants.TIPS_GOODS_ORDER + placeId;
        List<OrdersTipsVO> vos = new ArrayList<>();

        //遍历redis数据
        Map<String, String> entries = hashOperations.entries(key);
        if (MapUtils.isEmpty(entries)) {
            return vos;
        }
        List<String> delOrderIds = new ArrayList<>();
        entries.forEach((k, v) -> {
            if (StringUtils.isEmpty(v)) {
                return;
            }
            OrdersTipsVO vo = null;
            try {
                vo = new Gson().fromJson(v, OrdersTipsVO.class);
            } catch (JsonSyntaxException e) {
                log.error("[订单tips]redis数据异常,order:{}", v);
            }
            if (vo != null) {
                if (LocalDateTime.now().isAfter(vo.getCreated().plusMinutes(15))) {
                    //如果超时15分钟，则删除tips
                    delOrderIds.add(k);
                } else {
                    vos.add(vo);

                }
            }
        });
        //支付时间倒序,null放到最后面
        vos.sort(Comparator.comparing(OrdersTipsVO::getPayTime, Comparator.nullsLast(Comparator.naturalOrder())));

        //删除超时tips
        if (CollectionUtils.isNotEmpty(delOrderIds)) {
            hashOperations.delete(key, delOrderIds.toArray());
        }
        return vos;
    }


//    /**
//     * 获取当前门店订单(小程序)
//     *
//     * @param placeId
//     * @return
//     */
//    public List<OrdersTipsVO> findMiniApp(String placeId) {
//        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
//        String key = BaseConstants.MINI_APP_GOODS_ORDER + placeId;
//        List<OrdersTipsVO> vos = new ArrayList<>();
//        // 获取Redis的连接对象
//        Long ttlSeconds = redisTemplate.getExpire(key, TimeUnit.SECONDS);
//        if (ttlSeconds == null || ttlSeconds <= 0) {
//            // key不存在或已过期
//            return vos;
//        }
//        //遍历redis数据
//        Map<String, String> entries = hashOperations.entries(key);
//        if (MapUtils.isEmpty(entries)) {
//            return vos;
//        }
//        List<OrdersTipsVO> finalVos = vos;
//        entries.forEach((k, v) -> {
//            if (StringUtils.isEmpty(v)) {
//                return;
//            }
//            OrdersTipsVO vo = null;
//            try {
//                vo = new Gson().fromJson(v, OrdersTipsVO.class);
//            } catch (JsonSyntaxException e) {
//                log.error("[订单tips]redis数据异常,order:{}", v);
//            }
//            if (vo != null) {
//                finalVos.add(vo);
//            }
//        });
//        //支付时间倒序,null放到最后面
//        finalVos.sort(Comparator.comparing(OrdersTipsVO::getPayTime, Comparator.nullsLast(Comparator.naturalOrder())));
//        // 只查询前100条
//        if (finalVos.size() > 100) {
//            vos = finalVos.subList(0, 100);
//        }
//        return vos;
//    }


   /** 获取当前门店订单(小程序)
     * @param placeId
     * @return
     */
   public List<OrdersTipsVO> findMiniApp(String placeId)
   {
       List<OrdersTipsVO> vos = new ArrayList<>();
       String pattern = BaseConstants.MINI_APP_GOODS_ORDER + placeId + "_*";

       // 先扫描匹配的所有订单key
       Set<String> orderKeys = scanKeys(pattern);
       if (CollectionUtils.isEmpty(orderKeys)) {
           return vos;
       }

       // 获取ValueOperations
       ValueOperations<String, String> valueOps = redisTemplate.opsForValue();

       List<OrdersTipsVO> tempVos = new ArrayList<>();
       for (String orderKey : orderKeys) {
           String v = valueOps.get(orderKey);
           if (StringUtils.isEmpty(v)) {
               continue;
           }
           try {
               OrdersTipsVO vo = gson.fromJson(v, OrdersTipsVO.class);
               if (vo != null) {
                   tempVos.add(vo);
               }
           }
           catch
           (JsonSyntaxException e) {
               log.error("[订单小程序]redis数据异常,order:{}", v);
           }
       }

       // 按支付时间倒序排序
       tempVos.sort(Comparator.comparing(OrdersTipsVO::getPayTime, Comparator.nullsLast(Comparator.naturalOrder())));

       // 只返回前100
       return tempVos.stream().limit(100).collect(Collectors.toList());
   }

    // 扫描匹配的所有订单key
    private Set<String> scanKeys(String pattern) {
        Set<String> keys = new HashSet<>();
        redisTemplate.execute((RedisCallback<Void>) connection -> {
            ScanOptions options = ScanOptions.scanOptions().match(pattern).count(200).build();
            try (Cursor<byte[]> cursor = connection.scan(options)) {
                while (cursor.hasNext()) {
                    String key = new String(cursor.next(), StandardCharsets.UTF_8);
                    keys.add(key);
                }
            }
            catch (Exception e) {
                log.error("扫描Redis Keys时发生异常", e);
            }
            return null;
        });
        return keys;
    }





    public List<Orders> findByPlaceIdAndOrderIdIn(String placeId, List<String> orderIds) {
        return ordersRepository.findByPlaceIdAndOrderIdIn(placeId, orderIds);
    }

    @Transactional
    public GenericResponse<?> createCustomOrder(OrdersBO ordersBO) {
        log.info("自定义收款:{}", JSONObject.toJSONString(ordersBO));

        if (ordersBO.getPayType().name().equals(PayType.CASH.name())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getPayType())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        //校验重复提交订单，2秒防止重复
        String key = shopOrderKey + ordersBO.getPlaceId() + "_" + ordersBO.getSourceType().name() + "_" + ordersBO.getRealMoney();
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(key))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 2, TimeUnit.SECONDS);
        //校验场所是否开通商超，和场所是否支持当前支付方式
        Optional<ShopConfig> byPlaceId = shopConfigService.findByPlaceId(ordersBO.getPlaceId());
        if (!byPlaceId.isPresent()) {
            return new GenericResponse<>(ServiceCodes.SHOP_CONFIG_ERROR);
        }
        ShopConfig shopConfig = byPlaceId.get();
        if (shopConfig.getCustomizedPayLimit() > 0 && shopConfig.getCustomizedPayLimit() < ordersBO.getRealMoney()) {
            return new GenericResponse<>(ServiceCodes.SHOP_CONFIG_CUSTOMIZED_PAY_LIMIT);
        }
        String payCode = ordersBO.getPayCode(); //付款码
        int payCodeNum = 0;
        PayType payType = ordersBO.getPayType();
        try {
            if (payCode != null) {
                log.info("::::::::::::::::::::::订单请求payCode:::::::::::::::::::::::::::::::::" + payCode);
                payCodeNum = StringUtils.isEmpty(payCode) ? 0 : Integer.parseInt(payCode.substring(0, 2));
                if (payCodeNum >= 10 && payCodeNum <= 15) {
                    // 前缀以10、11、12、13、14、15 开头，则是微信付款码
                    payType = PayType.WECHAT_SCAN;
                } else if (payCodeNum >= 25 && payCodeNum <= 30) {
                    // 前缀以25、26、27、28、29、30 开头，则是支付宝付款码
                    payType = PayType.ALIPAY_SCAN;
                } else {
                    return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        GenericResponse<ObjDTO<PlaceConfigBO>> genericResponse = placeServerService.findPlaceConfigByPlaceId(ordersBO.getPlaceId());
        if (!genericResponse.isResult()) {
            return new GenericResponse<>(ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        }
        PlaceConfigBO placeConfigBO = genericResponse.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }
        //获取班次信息
        PlaceShiftBO shiftBO = null;
        String cashierId = ordersBO.getCashierId();
        if (StringUtils.isEmpty(cashierId)) {
            cashierId = placeConfigBO.getShiftOnlineIncome();
        }
        GenericResponse<ObjDTO<PlaceShiftBO>> workingShiftByCashierId = placeServerService.findWorkingShiftByCashierId(ordersBO.getPlaceId(), cashierId);
        if (workingShiftByCashierId.isResult()) {
            shiftBO = workingShiftByCashierId.getData().getObj();
        }
        LocalDateTime now = LocalDateTime.now();
        //生成订单
        String orderId = "OTH" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        Orders orders = new Orders();
        orders.setCreated(now);
        orders.setPlaceId(ordersBO.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(ordersBO.getSourceType());
        orders.setClientId(ordersBO.getClientId());
        orders.setClientName(ordersBO.getClientName());
        orders.setTotalMoney(ordersBO.getTotalMoney());
        orders.setRealMoney(ordersBO.getRealMoney());
        orders.setPayType(payType);
        orders.setShiftId(null != shiftBO ? shiftBO.getShiftId() : "");
        orders.setRemark(ordersBO.getRemark());
        orders.setCashierId(ordersBO.getCashierId());
        orders.setCashierName(ordersBO.getCashierName());
        orders.setIdNumber(ordersBO.getIdNumber());
        orders.setIdName(ordersBO.getIdName());
        orders.setStatus(0);
        orders.setPayCode(payCode);
        orders.setCreater(ordersBO.getCreater());
        orders.setCreaterName(ordersBO.getCreaterName());
        orders.setIsMeals(ordersBO.getIsMeals());
        orders.setOrderType(ordersBO.getOrderType());

        Orders order = ordersRepository.save(orders);

        if (orders.getPayType() == PayType.AGGREGATE_PAY || orders.getPayType() == PayType.ALIPAY_SCAN || orders.getPayType() == PayType.WECHAT_SCAN) { //聚合扫码支付
            //生成付款订单
            String payAmountYuan = String.valueOf(BigDecimal.valueOf(orders.getRealMoney())
                    .divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
            String orderDes = "自定义收款" + payAmountYuan + "元";
            String bizType = "shopping";
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(orders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(orders.getPlaceId());
            requestBO.setBizOrderId(orderId);
            requestBO.setPayType(orders.getPayType().name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(orders.getIdNumber());
            requestBO.setPlaceId(orders.getPlaceId());
            requestBO.setPayCode(orders.getPayCode());
            // 业绩自动化需求新增字段
            requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

            if (SourceType.WECHAT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());
            } else if (SourceType.CLIENT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CLIENT.getCode());
            } else if (SourceType.CASHIER == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CASHIER.getCode());
                if (StringUtils.isEmpty(ordersBO.getClientIp())) {
                    requestBO.setClientIp(ordersBO.getClientIp());
                }
            }

            GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService
                    .createPaymentOrder(requestTicket, requestBO);
            if (paymentServerResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PaymentResultBO paymentResultBO = paymentServerResponse.getData().getObj();
                // 如果是扫码枪支付成功，立即调用接口推送
//                if ("0000".equals(paymentResultBO.getPayState()) && (orders.getPayType().equals(PayType.ALIPAY_SCAN)
//                        || orders.getPayType().equals(PayType.WECHAT_SCAN))) {
//                    int poundage = StringUtils.isEmpty(paymentResultBO.getPoundage()) ? 0 : paymentResultBO.getPoundage();
//                    paymentServerService.notifyPaymentOrderSync(paymentResultBO.getOrderId(),poundage);
//                }

                order.setLdOrderId(paymentResultBO.getLdOrderId());
                order.setUpdated(now);
                save(order);

                // 如果是扫码枪支付成功，立即调用接口推送
                asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, orders);
            }
            return paymentServerResponse;
        }
        return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
    }

    /**
     * 统计自定义收款订单数量和金额
     *
     * @param placeId 场所ID
     * @param start   开始时间
     * @param end     结束时间
     * @return Map包含count(订单数)和amount(总金额)
     */
    public Map<String, Object> sumCustomOrderCountAndAmount(String placeId, LocalDateTime start,
                                                            LocalDateTime end) {
        return ordersRepository.sumCustomOrderCountAndAmount(placeId, start, end);
    }

    /**
     * 根据规则ID统计包时订单数量和金额
     *
     * @param placeId 场所ID
     * @param start   开始时间
     * @param end     结束时间
     * @return 包含ruleId、count和amount的Map列表
     */
    public List<Map<String, Object>> sumPackageOrderCountAndAmountGroupByRuleId(
            String placeId, LocalDateTime start, LocalDateTime end) {
        return ordersRepository.sumPackageOrderCountAndAmountGroupByRuleId(placeId, start, end);
    }

    public List<Orders> findByPlaceIdAndOrderIdInAndShiftIdNot(String placeId, List<String> orderIds, String shiftId) {
        return ordersRepository.findByPlaceIdAndOrderIdInAndShiftIdNot(placeId, orderIds, shiftId);
    }

    public List<Orders> findByPlaceIdAndOrderIdInAndBetweenTimes(String placeId, List<String> orderIds, LocalDateTime start) {
        return ordersRepository.findByPlaceIdAndOrderIdInAndCreatedLessThanAndOrderTypeNot(placeId, orderIds, start,10);
    }


    public GenericResponse<ObjDTO<PaymentResultBO>> createTopupOrder(OrdersBO ordersBO) {
        ordersBO.setCreater(1L);
        ordersBO.setCreaterName("用户");
        ordersBO.setOrderType(3);
        GenericResponse<ObjDTO<PaymentResultBO>> paymentResultBo = createGoodsOrder(ordersBO);
        if (paymentResultBo.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            PaymentResultBO paymentResultBO = paymentResultBo.getData().getObj();
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            LogTopupBO logTopupBO = new LogTopupBO();
            LocalDateTime now = LocalDateTime.now();
            logTopupBO.setPlaceId(ordersBO.getPlaceId());
            logTopupBO.setCardId(ordersBO.getCardId());
            logTopupBO.setClientId(null == ordersBO.getClientId() ? "" : ordersBO.getClientId());
            logTopupBO.setCardTypeId(ordersBO.getCardTypeId());
            logTopupBO.setCardTypeName(ordersBO.getCardTypeName());
            logTopupBO.setIdNumber(ordersBO.getIdNumber());
            logTopupBO.setIdName(ordersBO.getIdName());
            logTopupBO.setCashAmount(ordersBO.getRealMoney());
            logTopupBO.setPresentAmount(0);
            logTopupBO.setPayType(ordersBO.getPayType());
            logTopupBO.setSourceType(ordersBO.getSourceType());
            logTopupBO.setShiftId(ordersBO.getShiftId() == null ? "" : ordersBO.getShiftId());
            logTopupBO.setOperator(ordersBO.getCardId());
            logTopupBO.setOperatorName(ordersBO.getIdName());
            logTopupBO.setCreated(now);
            logTopupBO.setOrderId(paymentResultBO.getOrderId());
            logTopupBO.setLdOrderId(paymentResultBO.getLdOrderId());
            logTopupBO.setQrcodeUrl(paymentResultBO.getQrcodeUrl());
            logTopupBO.setStatus(1);
            logTopupApi.createLogTopup(requestTicket, logTopupBO);
        }
        return paymentResultBo;
    }

    // 根据场所和班次id查询所有订单
    public List<Orders> findByPlaceIdAndShiftIdInAndStatusIn(String placeId, List<String> shiftIds, List<Integer> status) {
        return ordersRepository.findByPlaceIdAndShiftIdInAndStatusInAndOrderTypeNot(placeId, shiftIds, status, 2);
    }


    /**
     * 网吧根据班次的开始时间和结束时间，计算相应订单类型的所有订单
     *
     * @param placeId
     * @param startTime
     * @param endTime
     * @param status
     * @param orderTypeList
     * @return
     */
    public List<Orders> findByPlaceIdAndStatusInAndCreatedGreaterThanEqualAndCreatedLessThanAndOrderTypeIn(String placeId, LocalDateTime startTime, LocalDateTime endTime, List<Integer> status, List<Integer> orderTypeList) {
        return ordersRepository.findByPlaceIdAndStatusInAndCreatedGreaterThanEqualAndCreatedLessThanAndDeletedAndOrderTypeIn(placeId, status, startTime, endTime, 0, orderTypeList);
    }

    private void sendTopupBusiness(Orders orders, BillingCardBO billingCardBO, int cost, int present) {
        // 保存轮询数据
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(orders.getPlaceId(), orders.getClientId(), orders.getIdNumber(), BusinessType.TOPUP);

        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();

            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {

                TopupAndDeductionBusinessBO businessTopupAndDeductionBO = new TopupAndDeductionBusinessBO();
                businessTopupAndDeductionBO.setPlaceId(orders.getPlaceId());
                businessTopupAndDeductionBO.setIdNumber(billingCardBO.getIdNumber());
                businessTopupAndDeductionBO.setCashAmount(cost);
                businessTopupAndDeductionBO.setPresentAmount(present);
                businessTopupAndDeductionBO.setTotalAccount(billingCardBO.getTotalAccount());
                businessTopupAndDeductionBO.setCreated(LocalDateTime.now().toString());
                businessTopupAndDeductionBO.setSourceType(orders.getSourceType());
                businessTopupAndDeductionBO.setBusinessType(BusinessType.TOPUP);
                businessTopupAndDeductionBO.setBusinessId(pollingBO.getCashierBusinessId());
                businessTopupAndDeductionBO.setClientId(orders.getClientId());
                businessTopupAndDeductionBO.setType(1);
                // 保存收银台业务数据
                notifyServerService.pushTopupAndDeductionBusinessData(businessTopupAndDeductionBO);
            }
        }
    }

    public GenericResponse<PagerDTO<OrdersBO>> findBuyGiftsPageList(String placeId,String sourceType, String startDate, String endDate, int size,int start) {

        int page = start / size;

        Map<String, String> params = new HashMap<>();
        params.put("queryDateType", "1");
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("sourceType", sourceType);
        params.put("placeId", placeId);
        params.put("orderType", "2");

        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "id");
        Page<Orders> all = findAll(params, pageable);
        if (all.getTotalElements() == 0) {
            return new GenericResponse<>(new PagerDTO<>(0, new ArrayList<>()));
        }

        List<OrdersBO> bos = all.getContent().stream().map(Orders::toBO).collect(Collectors.toList());

        //暂时不考虑子商品
//        for (OrdersBO bo : bos) {
//            List<OrderGoods> byPlaceIdAndOrderId = orderGoodsService.findByPlaceIdAndOrderId(bo.getPlaceId(), bo.getOrderId());
//            //判断是否图片为空，为空就补上
//            List<String> goodsIds = byPlaceIdAndOrderId.stream().map(OrderGoods::getGoodsId).collect(Collectors.toList());
//            List<Goods> allGoods = goodsService.findByPlaceIdAndGoodsIdIn(placeId, goodsIds);
//
//            if (byPlaceIdAndOrderId.size() > 0) {
//                List<OrderGoodsBO> orderGoodsBOS = byPlaceIdAndOrderId.stream().map(OrderGoods::toBO).collect(Collectors.toList());
//                if (allGoods.size() > 0) {
//                    for (OrderGoodsBO orderGoodsBO : orderGoodsBOS) {
//                        for (Goods goods : allGoods) {
//                            if (goods.getGoodsId().equals(orderGoodsBO.getGoodsId())) {
//                                orderGoodsBO.setGoodsPic(goods.getGoodsPic());
//                                break;
//                            }
//                        }
//                    }
//                }
//                bo.setOrderGoodsList(orderGoodsBOS);
//            }
//        }
        return new GenericResponse<>(new PagerDTO<>((int) all.getTotalElements(), bos));
    }

    public int updateOrderFee(String placeId,String orderId,int fee){
        return ordersRepository.updateOrderFee(placeId,orderId,fee);
    }

    public Integer sumMoneyByPlaceAndIdNumberAndTime(String placeId,String idNumber,LocalDateTime startDateTime, LocalDateTime endDateTime){
        List<Integer> statusList = Arrays.asList(1, 2, 3, 4);
        return ordersRepository.sumRealMoneyByConditions(placeId,idNumber,startDateTime,endDateTime,statusList);
    }

    /**
     * 校验设备租赁订单商品库存、销售状态，校验
     * ordersBO 订单信息
     * rackId 仓库id
     * PayType 支付方式
     * now 当前时间
     *
     * @return 订单需要购买的商品
     */
    public void verifyRentOrderStorage(OrdersBO ordersBO, PayType payType, BillingCardBO cardBO, RentConfig rentConfig, String areaId) {

        String placeId = ordersBO.getPlaceId();

        List<OrderGoodsBO> orderGoodsList = ordersBO.getOrderGoodsList(); //里面可能有重复的商品.需要去重

//        String areaId = null;
//        if (!StringUtils.isEmpty(ordersBO.getClientId())) {
//            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(placeId, ordersBO.getClientId());
//            if (clientByPlaceIdAndClientId.isResult()) {
//                areaId = clientByPlaceIdAndClientId.getData().getObj().getAreaId();
//            }
//        }

        //key = goods ，val = quantity 销售数量
        Map<String, Integer> maps = new HashMap<>();
        for (OrderGoodsBO orderGoodsBO : orderGoodsList) {
            if (maps.containsKey(orderGoodsBO.getGoodsId())) {
                maps.put(orderGoodsBO.getGoodsId(), maps.get(orderGoodsBO.getGoodsId()) + orderGoodsBO.getQuantity());
            } else {
                maps.put(orderGoodsBO.getGoodsId(), orderGoodsBO.getQuantity());
            }
        }
        List<OrderGoodsBO> uniqueList = new ArrayList<>(); //去除重复商品id的List
        List<String> goodIds = new ArrayList<>();
        for (OrderGoodsBO data : orderGoodsList) {
            if (!goodIds.contains(data.getGoodsId())) {
                goodIds.add(data.getGoodsId());
                uniqueList.add(data);
            }
        }

        for (OrderGoodsBO orderGoodsBO : uniqueList) {
            //查询商品信息
            Optional<RentGoods> byPlaceIdAndGoodsId = rentGoodsService.findByPlaceIdAndGoodsId(placeId, orderGoodsBO.getGoodsId());
            if (!byPlaceIdAndGoodsId.isPresent()) {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_NOT_EXIST);
            }
            RentGoods rentGoods = byPlaceIdAndGoodsId.get();
            if (1 == rentGoods.getSellStatus()) {
                throw new ServiceException(ServiceCodes.MARKET_GOODS_STOP_SELLING);
            }
            //校验库存
            Integer goodsBuyNum = maps.get(rentGoods.getGoodsId()); //下单购买数量
            if (goodsBuyNum > rentGoods.getGoodsStocksNum()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
            }

            //校验商品限购
            if (rentGoods.getExtCount() > 0 && goodsBuyNum > rentGoods.getExtCount()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_GOODS_NUMBER_MAX_ERROR);
            }

            // 校验可租赁区域
            if (!StringUtils.isEmpty(areaId) && !StringUtils.isEmpty(rentConfig.getAreaIds()) && !rentConfig.getAreaIds().contains(areaId)) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_AREA_ERROR);
            }

            //校验卡类型权限
            if (!ObjectUtils.isEmpty(cardBO)) {
                if (!rentConfig.getCardIds().contains(cardBO.getCardTypeId())) {
                    throw new ServiceException(orderGoodsBO.getGoodsName() + ServiceCodes.MARKET_CARD_TYPE_NONSUPPORT.getMessage());
                }
            }

        }
//        if (ordersBO.getIsMeals() == 0) { //非套餐订单时
//            //订单总金额
//            int sum = orderGoodsList.stream().mapToInt(it -> (it.getDeposit()) * it.getQuantity()).sum();
//            if (sum != ordersBO.getRealMoney()) {
//                throw new ServiceException(ServiceCodes.MARKET_SHOP_GOODS_ORDER_TOTAL_ERROR);
//            }
//        }
        if (payType.name().equals(PayType.BILLING_CARD.name())) {
            // 临时卡不支持卡扣
            if ("1000".equals(cardBO.getCardTypeId())) {
                throw new ServiceException(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
            }
            if (ordersBO.getBuckleType() == 0 && cardBO.getCashAccount() < ordersBO.getRealMoney()) {
                //本金余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            } else if (ordersBO.getBuckleType() == 0 && cardBO.getPresentAccount() < ordersBO.getRealMoney()) {
                //奖励余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }
        }
    }


    /**
     * 创建设备租赁订单
     *
     * @param ordersBO 商品订单对象
     * @return
     */
    @Transactional
    public GenericResponse<ObjDTO<PaymentResultBO>> createRentOrder(OrdersBO ordersBO) {

//        log.info("租赁订单:{}", JSONObject.toJSONString(ordersBO));
        if (ordersBO.getPayType().name().equals(PayType.CASH.name())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getPayType()) || CollectionUtils.isEmpty(ordersBO.getOrderGoodsList())) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 查询设备租赁管理配置
        Optional<RentConfig> rentConfigOptional = rentConfigService.findOneByPlaceId(ordersBO.getPlaceId());
        if (!rentConfigOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.MARKET_RENT_CONFIG_NOT_FOUND);
        }
        RentConfig rentConfig = rentConfigOptional.get();

        if (rentConfig.getRentSwitch() == 1) {
            return new GenericResponse<>(ServiceCodes.MARKET_STOP_RENT_ORDERS);
        }

        //查询用户是否被拉入黑名单
        Optional<RentBlackList> rentBlackListOptional = rentBlackListService.findByPlaceIdAndIdNumber(ordersBO.getPlaceId(), ordersBO.getIdNumber());
        if (rentBlackListOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.MARKET_RENT_BLACK_LIST);
        }

        //校验重复提交订单，2秒防止重复
        String key = shopRentOrderKey + ordersBO.getPlaceId() + "_" + ordersBO.getSourceType().name() + "_" + ordersBO.getRealMoney() + "_" + ordersBO.getOrderGoodsList().get(0).getGoodsId();
        if (stringRedisTemplate.delete(key)) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 2, TimeUnit.SECONDS);

        if (SourceType.CASHIER.name().equals(ordersBO.getSourceType().name())) {
            //收银台(目前收银台下单校验id、名称、班次必填)
            if (StringUtils.isEmpty(ordersBO.getCashierId()) || StringUtils.isEmpty(ordersBO.getCashierName())) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }

        } else if (SourceType.CLIENT.name().equals(ordersBO.getSourceType().name())) {
            //客户端
            if (StringUtils.isEmpty(ordersBO.getClientId()) || StringUtils.isEmpty(ordersBO.getClientName()) || StringUtils.isEmpty(ordersBO.getCardId())
                    || StringUtils.isEmpty(ordersBO.getIdNumber()) || StringUtils.isEmpty(ordersBO.getIdName())) {
                return new GenericResponse<>(ServiceCodes.NULL_PARAM);
            }
        } else if (SourceType.WECHAT.name().equals(ordersBO.getSourceType().name())) {

            if (StringUtils.isEmpty(ordersBO.getPlaceId()) || StringUtils.isEmpty(ordersBO.getIdName()) || StringUtils.isEmpty(ordersBO.getIdNumber())
                    || StringUtils.isEmpty(ordersBO.getOpenId()) || StringUtils.isEmpty(ordersBO.getPayType()) || StringUtils.isEmpty(ordersBO.getReturnUrl())) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }

        } else { // source的值只能是client 或 cashier，WECHAT  其他后续在支持
            return new GenericResponse<>(ServiceCodes.SHOP_GOODS_SOURCE_ERROR);
        }

        //查询场所是否开通在线支付
        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(ordersBO.getPlaceId());
        if (!placeConfigResponse.isResult()) {
            return new GenericResponse<>(ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        }
        PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        String payCode = ordersBO.getPayCode(); //付款码
        int payCodeNum = 0;
        PayType payType = ordersBO.getPayType();
        try {
            if (payCode != null) {
                log.info("::::::::::::::::::::::租赁订单请求payCode:::::::::::::::::::::::::" + payCode);
                payCodeNum = StringUtils.isEmpty(payCode) ? 0 : Integer.parseInt(payCode.substring(0, 2));
                if (payCodeNum >= 10 && payCodeNum <= 15) {
                    // 前缀以10、11、12、13、14、15 开头，则是微信付款码
                    payType = PayType.WECHAT_SCAN;
                } else if (payCodeNum >= 25 && payCodeNum <= 30) {
                    // 前缀以25、26、27、28、29、30 开头，则是支付宝付款码
                    payType = PayType.ALIPAY_SCAN;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }

        //校验是否有虚拟商品，如果有虚拟商品，则需要校验idnumber必填和已存在会员卡

        List<String> goodsIds = ordersBO.getOrderGoodsList().stream().map(OrderGoodsBO::getGoodsId).distinct().collect(Collectors.toList());

        // 这里查询设备租赁商品，用来判断前端是否任意传
        List<RentGoods> rentGoodsList = rentGoodsService.findByPlaceIdAndGoodsIdIn(ordersBO.getPlaceId(), goodsIds);

        //查询用户会员卡
        BillingCardBO cardBO = null;
        if (!StringUtils.isEmpty(ordersBO.getIdNumber())) {
            GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(ordersBO.getPlaceId(), ordersBO.getIdNumber());
            if (SourceType.CLIENT.name().equals(ordersBO.getSourceType().name()) && !billingCardResponse.isResult()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            if (billingCardResponse.isResult()) {
                cardBO = billingCardResponse.getData().getObj();
            }
        }

        if (payType == PayType.BILLING_CARD) {
            return new GenericResponse<>(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
        }
        if (null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }
        if (payType == PayType.BILLING_CARD && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }

        // 先从SpecialPlaceClients获取特殊的几个客户端信息
        SpecialPlaceClients specialPlaceClient = SpecialPlaceClients.findByClientId(ordersBO.getClientId());
        if (specialPlaceClient != null) {
            ordersBO.setClientName(specialPlaceClient.getClientName());
        } else {
            // 如果从SpecialPlaceClients未获取到特殊的几个客户端信息，则从数据库查询
            if (!StringUtils.isEmpty(ordersBO.getClientId()) && StringUtils.isEmpty(ordersBO.getClientName())) {
                GenericResponse<ObjDTO<PlaceClientBO>> placeClientResponse = placeServerService.findClientByPlaceIdAndClientId(ordersBO.getPlaceId(), ordersBO.getClientId());
                if (!placeClientResponse.isResult()) {
                    return new GenericResponse<>(placeClientResponse.getMessage());
                }
                PlaceClientBO placeClientBo = placeClientResponse.getData().getObj();
                ordersBO.setClientName(placeClientBo.getHostName());
            }
        }

        String areaId = null;
        if (!StringUtils.isEmpty(ordersBO.getClientId())) {
            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(ordersBO.getPlaceId(), ordersBO.getClientId());
            if (clientByPlaceIdAndClientId.isResult()) {
                areaId = clientByPlaceIdAndClientId.getData().getObj().getAreaId();
            }
        }

        LocalDateTime now = LocalDateTime.now();

        //校验租赁商品信息
        verifyRentOrderStorage(ordersBO, payType, cardBO, rentConfig, areaId);

        //获取班次信息
        PlaceShiftBO shiftBO = null;
        String cashierId = ordersBO.getCashierId();
        if (StringUtils.isEmpty(cashierId)) {
            cashierId = placeConfigBO.getShiftOnlineIncome();
        }
        GenericResponse<ObjDTO<PlaceShiftBO>> workingShiftByCashierId = placeShiftApi.findWorkingShiftByCashierId(ordersBO.getPlaceId(), cashierId);
        if (workingShiftByCashierId.isResult()) {
            shiftBO = workingShiftByCashierId.getData().getObj();
        }

//        int areaDiscount = 0;
//        int memberDiscount = 0;
        int depositDiscount = 0;
        int rentDiscount = 0;

        Optional<RentAreaDiscount> rentAreaDiscountOpt = rentAreaDiscountService.findByPlaceIdAndAreaId(ordersBO.getPlaceId(), areaId);
        if (rentAreaDiscountOpt.isPresent()) {
            RentAreaDiscount rentAreaDiscount = rentAreaDiscountOpt.get();
            depositDiscount = rentAreaDiscount.getDepositDiscount();
            rentDiscount = rentAreaDiscount.getRentDiscount();
        }

        Optional<RentMemberDiscount> rentMemberDiscountOpt = rentMemberDiscountService.findByPlaceIdAndCardTypeId(ordersBO.getPlaceId(), ordersBO.getCardTypeId());
        if (rentMemberDiscountOpt.isPresent()) {
            RentMemberDiscount rentMemberDiscount = rentMemberDiscountOpt.get();
            if (rentMemberDiscount.getDepositDiscount() < depositDiscount) {
                depositDiscount = rentMemberDiscount.getDepositDiscount();
            }
            if (rentMemberDiscount.getRentDiscount() < rentDiscount) {
                rentDiscount = rentMemberDiscount.getRentDiscount();
            }
        }
        int realMoney = ordersBO.getTotalMoney() * depositDiscount;// 可以做金额比较判断

        //生成押金订单，押金
        String orderId = "DEP" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
        Orders orders = new Orders();
        orders.setCreated(now);
        orders.setPlaceId(ordersBO.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(ordersBO.getSourceType());
        orders.setClientId(ordersBO.getClientId());
        orders.setClientName(ordersBO.getClientName());
        orders.setTotalMoney(ordersBO.getTotalMoney());//未折扣前
        orders.setRealMoney(ordersBO.getRealMoney());
        orders.setPayType(payType);
        orders.setShiftId(null != shiftBO ? shiftBO.getShiftId() : "");
        orders.setRemark(ordersBO.getRemark());
        orders.setCashierId(ordersBO.getCashierId());
        orders.setCashierName(ordersBO.getCashierName());
        orders.setIdNumber(ordersBO.getIdNumber());
        orders.setIdName(ordersBO.getIdName());
        orders.setStatus(0);
        orders.setPayCode(payCode);
        if (cardBO != null) {
            orders.setCardTypeId(cardBO.getCardTypeId());
            orders.setCardId(cardBO.getCardId());
        }
        orders.setCreater(ordersBO.getCreater());
        orders.setCreaterName(ordersBO.getCreaterName());
        orders.setIsMeals(ordersBO.getIsMeals());
        orders.setPhoneNumber(ordersBO.getPhoneNumber());
        orders.setOrderType(OrderType.DEPOSIT.getCode());
        List<OrderGoods> orderGoodsList = new ArrayList<>();
        for (OrderGoodsBO orderGoodsBO : ordersBO.getOrderGoodsList()) {
            OrderGoods orderGoods = new OrderGoods();
            orderGoods.setCreated(now);
            orderGoods.setPlaceId(ordersBO.getPlaceId());
            orderGoods.setOrderId(orderId);
            orderGoods.setGoodsId(orderGoodsBO.getGoodsId());
            orderGoods.setGoodsName(orderGoodsBO.getGoodsName());
            orderGoods.setQuantity(orderGoodsBO.getQuantity());
            orderGoods.setUnitPrice(orderGoodsBO.getUnitPrice());
            orderGoods.setSpecs(orderGoodsBO.getSpecs());
            orderGoods.setDiscounts(orderGoodsBO.getDiscounts());
            orderGoods.setGoodsTypeId(orderGoodsBO.getGoodsTypeId());
            orderGoods.setGoodsTypeName(orderGoodsBO.getGoodsTypeName());
            orderGoods.setDeposit(orderGoodsBO.getDeposit());
            orderGoods.setRentBillingType(orderGoodsBO.getRentBillingType());
            if (StringUtils.isEmpty(orderGoodsBO.getGoodsTypeId())) {
                orderGoods.setGoodsTypeId(" ");
                orderGoods.setGoodsTypeName("设备租赁");
            }
            orderGoods.setGoodsPresentAmount(0);
            List<RentGoods> collect = rentGoodsList.stream().filter(it -> it.getGoodsId().equals(orderGoodsBO.getGoodsId())).collect(Collectors.toList());
            if (collect.size() != 1) {
                return new GenericResponse<>(ServiceCodes.MARKET_SHOP_RACK_STORAGE_DATA_ERROR);
            } else {
                orderGoods.setGoodsQuota(0);
                orderGoods.setGoodsCategory(0);
            }

//            orderGoods.setStatus();
            orderGoods.setPresent(orderGoodsBO.getPresent());
            orderGoods.setMealsId(orderGoodsBO.getMealsId());
            orderGoods.setStatus(0);
            orderGoodsList.add(orderGoods);
        }
        //保存订单
        Orders order = ordersRepository.save(orders);
        orderGoodsService.saveAll(orderGoodsList);

        List<String> goodsIdList1 = new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, order, goodsIdList1));

        if (orders.getPayType() == PayType.AGGREGATE_PAY || orders.getPayType() == PayType.ALIPAY_SCAN || orders.getPayType() == PayType.WECHAT_SCAN) { //聚合扫码支付
            //生成付款订单
            String payAmountYuan = String.valueOf(BigDecimal.valueOf(orders.getRealMoney())
                    .divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
            String orderDes = "租赁商品押金，合计" + payAmountYuan + "元";
            String bizType = "shopping";
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(orders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(orders.getPlaceId());
            requestBO.setBizOrderId(orderId);
            requestBO.setPayType(payType.name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(orders.getIdNumber());
            requestBO.setPlaceId(orders.getPlaceId());
            requestBO.setPayCode(orders.getPayCode());
            // 业绩自动化需求新增字段
            requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

            if (SourceType.WECHAT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());
            } else if (SourceType.CLIENT == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CLIENT.getCode());
            } else if (SourceType.CASHIER == order.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CASHIER.getCode());
                if (StringUtils.isEmpty(ordersBO.getClientIp())) {
                    requestBO.setClientIp(ordersBO.getClientIp());
                }
            }

            GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService
                    .createPaymentOrder(requestTicket, requestBO);
            if (paymentServerResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PaymentResultBO paymentResultBO = paymentServerResponse.getData().getObj();
                log.info("租赁押金创建支付订单后查看返回结果::::::::::payRes={}", new Gson().toJson(paymentServerResponse.getData().getObj()));
                order.setLdOrderId(paymentResultBO.getLdOrderId());
                order.setUpdated(now);
                save(order);
                // 如果是扫码枪支付成功，立即调用接口推送
                asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, orders);
            }
            return paymentServerResponse;
        } else if (orders.getPayType() == PayType.WECHAT_MP) { //公众号支付
            //生成付款订单
            String orderDes = "公众号租赁商品押金，合计" + orders.getRealMoney() / 100 + "元";
            String bizType = "shopping";
            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(orders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(orders.getPlaceId());
            requestBO.setBizOrderId(orders.getOrderId());
            requestBO.setPayType(payType.name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(ordersBO.getIdNumber());
            requestBO.setPlaceId(orders.getPlaceId());
            requestBO.setReturnUrl(ordersBO.getReturnUrl());
            requestBO.setOpenId(ordersBO.getOpenId());

            GenericResponse<ObjDTO<PaymentResultBO>> paymentOrder = paymentServerService.createPaymentOrder(requestTicket2, requestBO);
            if (paymentOrder.isResult()) {
                order.setLdOrderId(paymentOrder.getData().getObj().getLdOrderId());
                order.setUpdated(now);
                save(order);
            }
            return paymentOrder;
        }

        return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
    }


    /**
     * 校验订单商品库存、销售状态、销售时间段（不生成订单），校验
     * ordersBO 订单信息
     * rackId 仓库id
     * PayType 支付方式
     * now 当前时间
     *
     * @return 订单需要购买的商品
     */
    public void verifyRentOrder(OrdersBO ordersBO, String rackId, PayType payType, LocalDateTime now, BillingCardBO cardBO) {

        if (null == cardBO && ordersBO.getOrderType() == 3) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }

        String placeId = ordersBO.getPlaceId();

        List<OrderGoodsBO> orderGoodsList = ordersBO.getOrderGoodsList(); //里面可能有重复的商品.需要去重

        String areaId = null;
        if (!StringUtils.isEmpty(ordersBO.getClientId())) {
            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(placeId, ordersBO.getClientId());
            if (clientByPlaceIdAndClientId.isResult()) {
                areaId = clientByPlaceIdAndClientId.getData().getObj().getAreaId();
            }
        }

        //key = goods ，val = quantity 销售数量
        Map<String, Integer> maps = new HashMap<>();
        for (OrderGoodsBO orderGoodsBO : orderGoodsList) {
            if (maps.containsKey(orderGoodsBO.getGoodsId())) {
                maps.put(orderGoodsBO.getGoodsId(), maps.get(orderGoodsBO.getGoodsId()) + orderGoodsBO.getQuantity());
            } else {
                maps.put(orderGoodsBO.getGoodsId(), orderGoodsBO.getQuantity());
            }
        }
        List<OrderGoodsBO> uniqueList = new ArrayList<>(); //去除重复商品id的List
        List<String> goodIds = new ArrayList<>();
        for (OrderGoodsBO data : orderGoodsList) {
            if (!goodIds.contains(data.getGoodsId())) {
                goodIds.add(data.getGoodsId());
                uniqueList.add(data);
            }
        }

        for (OrderGoodsBO orderGoodsBO : uniqueList) {
//            if(ordersBO.getOrderType() == 3){
//                Optional<InternetFee> byPlaceIdAndGoodsId = internetFeeService.findByPlaceIdAndGoodsId(placeId, orderGoodsBO.getGoodsId());
//                if(byPlaceIdAndGoodsId.isPresent()){
//                    InternetFee internetFee = byPlaceIdAndGoodsId.get();
//                    //卡类型是否有权限
//                    if(!internetFee.getCardTypeIds().contains(cardBO.getCardTypeId())){
//                        throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
//                    }
//                }
//            }
            //查询商品信息
            Optional<Goods> byPlaceIdAndGoodsId = goodsService.findByPlaceIdAndGoodsId(placeId, orderGoodsBO.getGoodsId());
            if (!byPlaceIdAndGoodsId.isPresent()) {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_NOT_EXIST);
            }
            Goods goods = byPlaceIdAndGoodsId.get();
            if (1 == goods.getSellStatus()) {
                throw new ServiceException(ServiceCodes.MARKET_GOODS_STOP_SELLING);
            }
            if (1 == goods.getOnlinePaySwitch()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_ONLINE_PAY);
            }
            if (payType.name().equals(PayType.BILLING_CARD.name())) {
                //校验商品是否支持卡扣
//                ordersBO.getBuckleType() == 0 &&
                if (goods.getSupportCashSwitch() == 1 && goods.getSupportPresentSwitch() == 1) {
                    //商品是否支持余额购买
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
                }
            }
            if (!StringUtils.isEmpty(areaId) && !StringUtils.isEmpty(goods.getAreaIds()) && !goods.getAreaIds().contains(areaId)) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_AREA_ERROR);
            }

            //校验商品售卖周期
            boolean b = GoodsUtil.verifyGoodsCycle(goods.toBO(), now);
            if (!b) {
                throw new ServiceException(ServiceCodes.MARKET_GOODS_NOT_IN_SALES_TIME);
            }
            //卡扣,后续需要区分奖金卡扣和本金卡扣
            if (payType.name().equals(PayType.BILLING_CARD.name())) {
                if (goods.getSupportCashSwitch() == 1) {
                    throw new ServiceException(ServiceCodes.SHOP_GOODS_PROHIBITED_CASH);
                }
            }
            //校验库存
            if (goods.getIsCalculateInventory() == 0 && !StringUtils.isEmpty(rackId)) {
                Optional<StorageGoods> byPlaceIdAndGoodsIdAndStorageRackId = storageGoodsService.findByPlaceIdAndGoodsIdAndStorageRackId(placeId, goods.getGoodsId(), rackId);
                if (!byPlaceIdAndGoodsIdAndStorageRackId.isPresent()) {
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
                }
                StorageGoods storageGoods = byPlaceIdAndGoodsIdAndStorageRackId.get();
                Integer storageNum = maps.get(goods.getGoodsId());
                if (storageNum > storageGoods.getGoodsStocksNum()) {
                    throw new ServiceException(ServiceCodes.MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY);
                }
            }
            //校验卡类型权限
            if (!ObjectUtils.isEmpty(cardBO)) {
                if (!goods.getCardTypeIds().contains(cardBO.getCardTypeId())) {
                    throw new ServiceException(orderGoodsBO.getGoodsName() + ServiceCodes.MARKET_CARD_TYPE_NONSUPPORT.getMessage());
                }
            }

            //校验商品限购
            if (goods.getExtCount() > 0) {
                LocalDateTime startTime = null;
                LocalDateTime endTime = LocalDateTime.now().minusDays(0).with(LocalTime.MAX); //查询结束时间
                if (goods.getExtType() == 0) {
                    //每日限购
                    startTime = LocalDateTime.now().minusDays(0).with(LocalTime.MIN); //查询开始时间
                } else if (goods.getExtType() == 1) {
                    //每周限购（每七天）
                    startTime = LocalDateTime.now().minus(7, ChronoUnit.DAYS);
                } else if (goods.getExtType() == 2) {
                    //每月限购（每三十天）
                    startTime = LocalDateTime.now().minus(30, ChronoUnit.DAYS);
                } else if (goods.getExtType() == 3) {
                    //永久限购
                    startTime = LocalDateTime.now().minus(365, ChronoUnit.DAYS);
                }
                //查询购买次数
                if (null != cardBO) {
                    int i = orderGoodsService.extSumByOrderIdAndGoodsId(placeId, cardBO.getIdNumber(), goods.getGoodsId(), startTime, endTime);
                    if ((i + orderGoodsBO.getQuantity()) > goods.getExtCount()) {
                        throw new ServiceException(ServiceCodes.MARKET_SHOP_ORDER_GOODS_NUMBER_MAX_ERROR);
                    }
                }
            }

        }
        if (ordersBO.getIsMeals() == 0) { //非套餐订单时
            //订单总金额
            int sum = orderGoodsList.stream().mapToInt(it -> (it.getUnitPrice()) * it.getQuantity()).sum();
            if (sum != ordersBO.getRealMoney()) {
                throw new ServiceException(ServiceCodes.MARKET_SHOP_GOODS_ORDER_TOTAL_ERROR);
            }
        }
        if (payType.name().equals(PayType.BILLING_CARD.name())) {
            // 临时卡不支持卡扣
            if ("1000".equals(cardBO.getCardTypeId())) {
                throw new ServiceException(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
            }
            if (ordersBO.getBuckleType() == 0 && cardBO.getCashAccount() < ordersBO.getRealMoney()) {
                //本金余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            } else if (ordersBO.getBuckleType() == 0 && cardBO.getPresentAccount() < ordersBO.getRealMoney()) {
                //奖励余额是否足够
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }
        }


    }

    // 统计未派送的订单数量
    public int countNonDeliveryOrders(String placeId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Integer> orderTypeList = Arrays.asList(1);
        Integer sum = ordersRepository.countNonDeliveryOrders(placeId, startTime, endTime,orderTypeList);
        return sum == null ? 0 : sum;
    }

    // 统计未派送的订单数量
    public List<OrdersBO> queryRentingOrderByPlaceId(String placeId,String idNumber) {
       List<Orders> list=ordersRepository.queryOrderByPlaceIdAndIdNumberAndOrderTypeAndRentStatusAndDeleted(placeId,idNumber,10,2,0);
       List<OrdersBO> resultList= list.stream().map(Orders::toBO).collect(Collectors.toList());
       return resultList;
    }


    @Transactional
    public GenericResponse<ObjDTO<PaymentResultBO>> cancelRentOrder() {
        return new GenericResponse<>();
    }

    /**
     * 结算设备租赁订单
     *
     * @param settlementRequestBO
     * @return 备注：还需要加结算金额比对，前端和后端计算的是否一致
     */
    @Transactional
    public GenericResponse<ObjDTO<PaymentResultBO>> settlementRentOrder(RentGoodsSettlementRequestBO settlementRequestBO) {
        //校验重复提交订单，2秒防止重复
        String key = shopRentOrderKey + settlementRequestBO.getPlaceId() + "_" + settlementRequestBO.getOrderId() + "_" + settlementRequestBO.getRealMoney();
        if (stringRedisTemplate.delete(key)) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(key, key, 2, TimeUnit.SECONDS);

        //第一步，先查询设备租赁押金订单
        Optional<Orders> depositOrdersOpt = findByPlaceIdAndOrderId(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId());
        if (!depositOrdersOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        Orders depositOrders = depositOrdersOpt.get();
        log.info("结算租赁押金订单:{}", JSONObject.toJSONString(depositOrders));

        PayType payType = settlementRequestBO.getPayType();

//        if (depositOrders.getRentStatus() != 2) {
//            return new GenericResponse<>(ServiceCodes.MARKET_ORDER_STATUS_NOT_SUPPORT_SETTLEMENT);
//        }

        if (depositOrders.getPayType().name().equals(PayType.CASH.name())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }
        //查询用户是否被拉入黑名单
        Optional<RentBlackList> rentBlackListOptional = rentBlackListService.findByPlaceIdAndIdNumber(settlementRequestBO.getPlaceId(), depositOrders.getIdNumber());
        if (rentBlackListOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.MARKET_RENT_BLACK_LIST);
        }

        // 开始计算订单商品的结算金额====================>
        // 查询设备租赁管理配置，看是否在免费时长内
        Optional<RentConfig> rentConfigOptional = rentConfigService.findOneByPlaceId(settlementRequestBO.getPlaceId());
        if (!rentConfigOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.MARKET_RENT_CONFIG_NOT_FOUND);
        }
        RentConfig rentConfig = rentConfigOptional.get();

        int deductionTime = rentConfig.getCycleMinutes(); // 计费周期
        int ignoreMinutes = rentConfig.getIgnoreMinutes(); // 免费时长
        String areaId = null == depositOrders.getAreaId() ? "1" : depositOrders.getAreaId();
        int totalRealMoney = 0;//总租金

        Orders rentOrders = new Orders();
        String rentOrderId = null;

        /** 如果是对单个商品结算*/
        if (!StringUtils.isEmpty(settlementRequestBO.getRentGoodsId())) {

            // 查询当前押金订单商品
            Optional<OrderGoods> depositOrderGoodsOpt = orderGoodsService.findByPlaceIdAndOrderIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), settlementRequestBO.getRentGoodsId());
            if (!depositOrderGoodsOpt.isPresent()) {
                return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_GOODS_NOT_FOUND);
            }
            OrderGoods depositOrderGoods = depositOrderGoodsOpt.get();

            if (depositOrderGoods.getRentStatus() != 2) {
                return new GenericResponse<>(ServiceCodes.MARKET_ORDER_STATUS_NOT_SUPPORT_SETTLEMENT);
            }
            long rentMinutes = ChronoUnit.MINUTES.between(depositOrderGoods.getRentStart(), LocalDateTime.now());
            log.info("结算查询:::{}租赁订单{}商品租赁时长为{}分钟，当前免费时长为{}分钟", settlementRequestBO.getOrderId(), settlementRequestBO.getRentGoodsId(), rentMinutes, ignoreMinutes);
            // 获取该租赁商品数据
            Optional<RentGoods> rentGoodsOptional = rentGoodsService.findByPlaceIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getRentGoodsId());
            if (!rentGoodsOptional.isPresent()) {
                return new GenericResponse<>(ServiceCodes.NOT_FOUND);
            }
            RentGoods rentGoods = rentGoodsOptional.get();


            // 如果还在免费时长范围内，租金为0
            if (rentMinutes < ignoreMinutes) {
                LocalDateTime now = LocalDateTime.now();
                rentOrderId = "REN" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
//                Orders rentOrders = new Orders();
                rentOrders.setCreated(now);
                rentOrders.setPlaceId(depositOrders.getPlaceId());
                rentOrders.setOrderId(rentOrderId);
                rentOrders.setSourceType(depositOrders.getSourceType());
                rentOrders.setClientId(depositOrders.getClientId());
                rentOrders.setClientName(depositOrders.getClientName());
                rentOrders.setTotalMoney(0);// 原本租金
                rentOrders.setRealMoney(0);// 折扣后的租金
                rentOrders.setPayType(payType);//0元的时候支付类型跟随押金
                rentOrders.setShiftId(depositOrders.getShiftId());
                rentOrders.setRemark(depositOrders.getRemark());
                rentOrders.setCashierId(depositOrders.getCashierId());
                rentOrders.setCashierName(depositOrders.getCashierName());
                rentOrders.setIdNumber(depositOrders.getIdNumber());
                rentOrders.setIdName(depositOrders.getIdName());
                rentOrders.setStatus(3);
                rentOrders.setPayCode(settlementRequestBO.getPayCode());
                rentOrders.setCardTypeId(depositOrders.getCardTypeId());
                rentOrders.setCardId(depositOrders.getCardId());
                rentOrders.setCreater(Long.valueOf(settlementRequestBO.getAccountId()));//结算人
                rentOrders.setCreaterName(settlementRequestBO.getAccountName());//结算人名称
                rentOrders.setIsMeals(depositOrders.getIsMeals());
                rentOrders.setRentStart(depositOrders.getRentStart());
                rentOrders.setRentEnd(LocalDateTime.now());
                rentOrders.setRentStatus(3);//该笔租金订单已经结束
                rentOrders.setOrderType(OrderType.RENT.getCode());
                rentOrders.setDepositOrderId(depositOrders.getOrderId());//租金订单记录关联押金订单
                save(rentOrders);

                // 保存租金订单商品
                OrderGoods rentOrderGoods = new OrderGoods();
                rentOrderGoods.setCreated(now);
                rentOrderGoods.setPlaceId(depositOrderGoods.getPlaceId());
                rentOrderGoods.setOrderId(rentOrderId);
                rentOrderGoods.setGoodsId(depositOrderGoods.getGoodsId());
                rentOrderGoods.setGoodsName(depositOrderGoods.getGoodsName());
                rentOrderGoods.setQuantity(depositOrderGoods.getQuantity());
                rentOrderGoods.setUnitPrice(depositOrderGoods.getUnitPrice());
                rentOrderGoods.setSpecs(depositOrderGoods.getSpecs());
                rentOrderGoods.setDiscounts(depositOrderGoods.getDiscounts());
                rentOrderGoods.setGoodsTypeId(depositOrderGoods.getGoodsTypeId());
                rentOrderGoods.setGoodsTypeName(depositOrderGoods.getGoodsTypeName());
                rentOrderGoods.setDeposit(depositOrderGoods.getDeposit());
                if (StringUtils.isEmpty(depositOrderGoods.getGoodsTypeId())) {
                    rentOrderGoods.setGoodsTypeId("");
                    rentOrderGoods.setGoodsTypeName("设备租赁租金");
                }
                rentOrderGoods.setGoodsPresentAmount(0);
                rentOrderGoods.setGoodsQuota(0);
                rentOrderGoods.setGoodsCategory(10);
                rentOrderGoods.setPresent(depositOrderGoods.getPresent());
                rentOrderGoods.setMealsId(depositOrderGoods.getMealsId());
                rentOrderGoods.setStatus(3);
                rentOrderGoods.setRentStatus(3);
                rentOrderGoods.setRentAmount(0);
                orderGoodsService.save(rentOrderGoods);
                // 再把押金订单状态改为已经3已结束/已退款

                //单个商品免费时长内结算租金订单后，押金的订单状态和订单商品状态同步更改。
                depositOrders.setStatus(3); // 押金订单下所有的商品都结算完才能改为完成
                depositOrders.setRentStatus(3);
                depositOrders.setRentEnd(LocalDateTime.now());
                depositOrders.setFinishedTime(now);
                depositOrders.setUpdated(LocalDateTime.now());
                save(depositOrders);
                // 修改押金订单商品列表的已结算状态，结束时间，这里变更为退款押金正常后再更改
//                orderGoodsService.settlementRentByPlaceIdAndOrderId(depositOrders.getPlaceId(), depositOrders.getOrderId());
                return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_IN_FREE_TIME);
            }
            else {
                // 结算单个商品，非免费时长内
                int rentDiscount = 0;//租金折扣
                Optional<RentMemberDiscount> memberOpt = rentMemberDiscountService.findByPlaceIdAndCardTypeId(settlementRequestBO.getPlaceId(), depositOrders.getCardTypeId());
                Optional<RentAreaDiscount> areaOpt = rentAreaDiscountService.findByPlaceIdAndAreaId(settlementRequestBO.getPlaceId(), areaId);

                if (memberOpt.isPresent()) {
                    rentDiscount = memberOpt.get().getRentDiscount();
                }
                if (areaOpt.isPresent()) {
                    int areaDiscount = areaOpt.get().getRentDiscount();
                    if (areaDiscount < rentDiscount) {
                        rentDiscount = areaDiscount;
                    }
                }


                int rentBillingType = rentGoods.getRentBillingType(); // 租赁商品计费类型
                int rentPrice = rentGoods.getPrice(); //租金价格，按时计费的时候是每小时价格，按次计费是每次租赁的价格
                int maxPrice = rentGoods.getMaxPrice();//封顶价格


                // 如果是计时
                if (rentBillingType == 1) {
                    if (deductionTime == 15 || deductionTime == 30 || deductionTime == 60) {
                        int totalRent = (int) calculateTotalRent(rentMinutes, deductionTime, rentPrice);

                        int totalDiscountRent = calculateRentAfterDiscount(totalRent, rentDiscount);
                        totalRealMoney = Math.min(totalDiscountRent, maxPrice);
                    }
                } else {
                    // 按次计费
                    totalRealMoney = calculateRentAfterDiscount(rentPrice, rentDiscount);
                }
            }

            LocalDateTime now = LocalDateTime.now();
            rentOrderId = "REN" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
            rentOrders.setCreated(now);
            rentOrders.setPlaceId(depositOrders.getPlaceId());
            rentOrders.setOrderId(rentOrderId);
            rentOrders.setSourceType(depositOrders.getSourceType());
            rentOrders.setClientId(depositOrders.getClientId());
            rentOrders.setClientName(depositOrders.getClientName());
            rentOrders.setTotalMoney(settlementRequestBO.getTotalMoney());// 原本租金
            rentOrders.setRealMoney(settlementRequestBO.getRealMoney());// 折扣后的租金
            rentOrders.setPayType(payType);//0元的时候支付类型跟随押金
            rentOrders.setShiftId(depositOrders.getShiftId());
            rentOrders.setRemark(depositOrders.getRemark());
            rentOrders.setCashierId(depositOrders.getCashierId());
            rentOrders.setCashierName(depositOrders.getCashierName());
            rentOrders.setIdNumber(depositOrders.getIdNumber());
            rentOrders.setIdName(depositOrders.getIdName());
            rentOrders.setStatus(0);
            rentOrders.setPayCode(settlementRequestBO.getPayCode());
            rentOrders.setCardTypeId(depositOrders.getCardTypeId());
            rentOrders.setCardId(depositOrders.getCardId());
//                rentOrders.setRentStatus(depositOrders.getRentStatus());
            rentOrders.setCreater(Long.valueOf(settlementRequestBO.getAccountId()));//结算人
            rentOrders.setCreaterName(settlementRequestBO.getAccountName());//结算人名称
            rentOrders.setIsMeals(depositOrders.getIsMeals());
            rentOrders.setRentStart(depositOrders.getRentStart());
            rentOrders.setRentEnd(LocalDateTime.now());
            rentOrders.setRentStatus(2);
            rentOrders.setOrderType(OrderType.RENT.getCode());
            rentOrders.setDepositOrderId(depositOrders.getOrderId());//租金订单记录关联押金订单
            save(rentOrders);

            // 保存租金订单商品
            OrderGoods rentOrderGoods = new OrderGoods();
            rentOrderGoods.setCreated(now);
            rentOrderGoods.setPlaceId(depositOrderGoods.getPlaceId());
            rentOrderGoods.setOrderId(rentOrderId);
            rentOrderGoods.setGoodsId(depositOrderGoods.getGoodsId());
            rentOrderGoods.setGoodsName(depositOrderGoods.getGoodsName());
            rentOrderGoods.setQuantity(depositOrderGoods.getQuantity());
            rentOrderGoods.setUnitPrice(depositOrderGoods.getUnitPrice());
            rentOrderGoods.setSpecs(depositOrderGoods.getSpecs());
            rentOrderGoods.setDiscounts(depositOrderGoods.getDiscounts());
            rentOrderGoods.setGoodsTypeId(depositOrderGoods.getGoodsTypeId());
            rentOrderGoods.setGoodsTypeName(depositOrderGoods.getGoodsTypeName());
            rentOrderGoods.setDeposit(depositOrderGoods.getDeposit());
            if (StringUtils.isEmpty(depositOrderGoods.getGoodsTypeId())) {
                rentOrderGoods.setGoodsTypeId("");
                rentOrderGoods.setGoodsTypeName("设备租赁租金");
            }
            rentOrderGoods.setGoodsPresentAmount(0);
            rentOrderGoods.setGoodsQuota(0);
            rentOrderGoods.setGoodsCategory(10);
            rentOrderGoods.setPresent(depositOrderGoods.getPresent());
            rentOrderGoods.setMealsId(depositOrderGoods.getMealsId());
            rentOrderGoods.setStatus(0);
            rentOrderGoods.setRentAmount(settlementRequestBO.getRealMoney());
            rentOrderGoods.setRentStatus(2);// 结算的商品肯定是在租赁中
            orderGoodsService.save(rentOrderGoods);
        } else {
            /** 对多个商品结算*/
            // 直接先查询当前押金订单的租赁开始时间，用订单的租赁开始时间去判断是否在免费时长内

            // 查询当前押金订单商品
//            Optional<OrderGoods> depositOrderGoodsOption = orderGoodsService.findByPlaceIdAndOrderIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), settlementRequestBO.getRentGoodsId());
//            if (!depositOrderGoodsOption.isPresent()) {
//                return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_GOODS_NOT_FOUND);
//            }
//            OrderGoods depositOrderGoods = depositOrderGoodsOption.get();
            long rentMinutes = ChronoUnit.MINUTES.between(depositOrders.getRentStart(), LocalDateTime.now());
            log.info("全部结算查询:::{}租赁订单租赁时长为{}分钟，当前免费时长为{}分钟", settlementRequestBO.getOrderId(), rentMinutes, ignoreMinutes);
            // 获取该租赁商品数据
//            Optional<RentGoods> rentGoodsOptional = rentGoodsService.findByPlaceIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getRentGoodsId());
//            if (!rentGoodsOptional.isPresent()) {
//                return new GenericResponse<>(ServiceCodes.NOT_FOUND);
//            }
//            RentGoods rentGoods = rentGoodsOptional.get();

            // 全部结算如果还在免费时长范围内，租金为0
            if (rentMinutes < ignoreMinutes) {
                LocalDateTime now = LocalDateTime.now();
                rentOrderId = "REN" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
                rentOrders.setCreated(now);
                rentOrders.setPlaceId(depositOrders.getPlaceId());
                rentOrders.setOrderId(rentOrderId);
                rentOrders.setSourceType(depositOrders.getSourceType());
                rentOrders.setClientId(depositOrders.getClientId());
                rentOrders.setClientName(depositOrders.getClientName());
                rentOrders.setTotalMoney(0);// 原本租金
                rentOrders.setRealMoney(0);// 折扣后的租金
                rentOrders.setPayType(payType);//0元的时候支付类型跟随押金
                rentOrders.setShiftId(depositOrders.getShiftId());
                rentOrders.setRemark(depositOrders.getRemark());
                rentOrders.setCashierId(depositOrders.getCashierId());
                rentOrders.setCashierName(depositOrders.getCashierName());
                rentOrders.setIdNumber(depositOrders.getIdNumber());
                rentOrders.setIdName(depositOrders.getIdName());
                rentOrders.setStatus(3);
                rentOrders.setPayCode(settlementRequestBO.getPayCode());
                rentOrders.setCardTypeId(depositOrders.getCardTypeId());
                rentOrders.setCardId(depositOrders.getCardId());
                rentOrders.setCreater(Long.valueOf(settlementRequestBO.getAccountId()));//结算人
                rentOrders.setCreaterName(settlementRequestBO.getAccountName());//结算人名称
                rentOrders.setIsMeals(depositOrders.getIsMeals());
                rentOrders.setRentStart(depositOrders.getRentStart());
                rentOrders.setRentEnd(LocalDateTime.now());
                rentOrders.setRentStatus(3);//该笔租金订单已经结束
                rentOrders.setOrderType(OrderType.RENT.getCode());
                rentOrders.setDepositOrderId(depositOrders.getOrderId());//租金订单记录关联押金订单
                save(rentOrders);

                // 保存租金订单商品
                List<OrderGoods> settlementFreeTimeOrderGoodsList = orderGoodsService.findByPlaceIdAndOrderIdAndRentStatus(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), 2);
                log.info("全部结算:::免费时长内查询待结算租金商品列表:::::::::{}",new Gson().toJson(settlementFreeTimeOrderGoodsList));
                List<OrderGoods> settlementFreeTimeOrderGoodsAddList = new ArrayList<>();
//                for (OrderGoods freeTimeOrderGoodsAdd : settlementFreeTimeOrderGoodsList) {
//                    // 获取基础租赁商品数据
//                    OrderGoods  freeTimeRentOrderGoods = getOrderGoods(freeTimeOrderGoodsAdd, rentOrderId);
//
//                    // 查询当前押金订单商品
//                    Optional<OrderGoods> depositOrderGoodsOpt = orderGoodsService.findByPlaceIdAndOrderIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), rentOrderGoods.getGoodsId());
//                    if (!depositOrderGoodsOpt.isPresent()) {
//                        return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_GOODS_NOT_FOUND);
//                    }
//
//                }
                for (OrderGoods freeTimeOrderGoodsAdd : settlementFreeTimeOrderGoodsList) {
                    // 获取基础租赁商品数据
                    OrderGoods rentOrderGoods = getOrderGoods(freeTimeOrderGoodsAdd, rentOrderId);
                    rentOrderGoods.setStatus(3);
                    rentOrderGoods.setRentStatus(3);
                    rentOrderGoods.setRentAmount(0);
                    settlementFreeTimeOrderGoodsAddList.add(rentOrderGoods);
                }
                log.info("全部结算:::免费时长租金商品列表:::::::::{}",new Gson().toJson(settlementFreeTimeOrderGoodsAddList));
                orderGoodsService.saveAll(settlementFreeTimeOrderGoodsAddList);

                // 再把押金订单状态改为已经3已结束/已退款
                //免费时长内租金订单，押金的订单状态和订单商品状态同步更改。
                depositOrders.setStatus(3); // 押金订单下所有的商品都结算完才能改为完成
                depositOrders.setRentStatus(3);
                depositOrders.setRentEnd(LocalDateTime.now());
                depositOrders.setFinishedTime(now);
                depositOrders.setUpdated(LocalDateTime.now());
                save(depositOrders);
                // 修改押金订单商品列表的已结算状态，结束时间
//                orderGoodsService.settlementRentByPlaceIdAndOrderId(depositOrders.getPlaceId(), depositOrders.getOrderId());
                return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_IN_FREE_TIME);
            } else {
                // 全部结算非免费时长范围内
                LocalDateTime now = LocalDateTime.now();
                rentOrderId = "REN" + Dim4StringUtils.generateCode(3) + System.currentTimeMillis();
                rentOrders.setCreated(now);
                rentOrders.setPlaceId(depositOrders.getPlaceId());
                rentOrders.setOrderId(rentOrderId);
                rentOrders.setSourceType(depositOrders.getSourceType());
                rentOrders.setClientId(depositOrders.getClientId());
                rentOrders.setClientName(depositOrders.getClientName());
                rentOrders.setTotalMoney(settlementRequestBO.getTotalMoney());// 原本租金
                rentOrders.setRealMoney(settlementRequestBO.getRealMoney());// 折扣后的租金
                rentOrders.setPayType(payType);
                rentOrders.setShiftId(depositOrders.getShiftId());

                rentOrders.setRemark(depositOrders.getRemark());
                rentOrders.setCashierId(depositOrders.getCashierId());
                rentOrders.setCashierName(depositOrders.getCashierName());
                rentOrders.setIdNumber(depositOrders.getIdNumber());
                rentOrders.setIdName(depositOrders.getIdName());
                rentOrders.setStatus(0);
                rentOrders.setPayCode(settlementRequestBO.getPayCode());
                rentOrders.setCardTypeId(depositOrders.getCardTypeId());
                rentOrders.setCardId(depositOrders.getCardId());
                rentOrders.setRentStatus(depositOrders.getRentStatus());
                rentOrders.setCreater(Long.valueOf(settlementRequestBO.getAccountId()));//结算人
                rentOrders.setCreaterName(settlementRequestBO.getAccountName());//结算人名称
                rentOrders.setIsMeals(depositOrders.getIsMeals());
                rentOrders.setRentStart(depositOrders.getRentStart());
                rentOrders.setOrderType(OrderType.RENT.getCode());
                rentOrders.setDepositOrderId(depositOrders.getOrderId());//租金订单记录关联押金订单
                save(rentOrders);
                // 这里查询押金订单商品中，租赁状态还是租赁中商品列表
                // 2. 查询结算的租金订单的商品列表
                List<OrderGoods> settlementOrderGoods = orderGoodsService.findByPlaceIdAndOrderIdAndRentStatus(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), 2);
                log.info("全部结算查询待结算租金商品列表:::::::::{}",new Gson().toJson(settlementOrderGoods));
                List<OrderGoods> settlementOrderGoodsList = new ArrayList<>();
                for (OrderGoods orderGoodsAdd : settlementOrderGoods) {
                    // 获取基础租赁商品数据
                    OrderGoods rentOrderGoods = getOrderGoods(orderGoodsAdd, rentOrderId);

                    // 查询当前押金订单商品
                    Optional<OrderGoods> depositOrderGoodsOpt = orderGoodsService.findByPlaceIdAndOrderIdAndGoodsId(settlementRequestBO.getPlaceId(), settlementRequestBO.getOrderId(), rentOrderGoods.getGoodsId());
                    if (!depositOrderGoodsOpt.isPresent()) {
                        return new GenericResponse<>(ServiceCodes.MARKET_DEPOSIT_GOODS_NOT_FOUND);
                    }
                    OrderGoods depositOrderGoods = depositOrderGoodsOpt.get();
                    long rentMinutesAll = ChronoUnit.MINUTES.between(depositOrderGoods.getRentStart(), LocalDateTime.now());
                    log.info("全部结算查询:::{}租赁订单{}商品租赁时长为{}分钟，当前免费时长为{}分钟", settlementRequestBO.getOrderId(), rentOrderGoods.getGoodsId(), rentMinutesAll, ignoreMinutes);

                    // 获取该租赁商品数据
                    Optional<RentGoods> rentGoodsOptional = rentGoodsService.findByPlaceIdAndGoodsId(settlementRequestBO.getPlaceId(), rentOrderGoods.getGoodsId());
                    if (!rentGoodsOptional.isPresent()) {
                        return new GenericResponse<>(ServiceCodes.NOT_FOUND);
                    }
                    RentGoods rentGoodsAll = rentGoodsOptional.get();

                    // 计算租金
                    int rentDiscount = 0;//租金折扣
                    Optional<RentMemberDiscount> memberOpt = rentMemberDiscountService.findByPlaceIdAndCardTypeId(settlementRequestBO.getPlaceId(), depositOrders.getCardTypeId());
                    Optional<RentAreaDiscount> areaOpt = rentAreaDiscountService.findByPlaceIdAndAreaId(settlementRequestBO.getPlaceId(), areaId);

                    if (memberOpt.isPresent()) {
                        rentDiscount = memberOpt.get().getRentDiscount();
                    }
                    if (areaOpt.isPresent()) {
                        int areaDiscount = areaOpt.get().getRentDiscount();
                        if (areaDiscount < rentDiscount) {
                            rentDiscount = areaDiscount;
                        }
                    }

                    int rentBillingType = rentGoodsAll.getRentBillingType(); // 租赁商品计费类型
                    int rentPrice = rentGoodsAll.getPrice(); //租金价格，按时计费的时候是每小时价格，按次计费是每次租赁的价格
                    int maxPrice = rentGoodsAll.getMaxPrice();//封顶价格

                    // 如果是计时
                    if (rentBillingType == 1) {
                        if (deductionTime == 15 || deductionTime == 30 || deductionTime == 60) {
                            int totalRent = (int) calculateTotalRent(rentMinutesAll, deductionTime, rentPrice);

                            int totalDiscountRent = calculateRentAfterDiscount(totalRent, rentDiscount);
                            totalRealMoney = Math.min(totalDiscountRent, maxPrice);
                        }
                    } else {
                        // 按次计费
                        totalRealMoney = calculateRentAfterDiscount(rentPrice, rentDiscount);
                    }
                    //保存租赁商品的实际租金
                    rentOrderGoods.setRentAmount(totalRealMoney);
                    settlementOrderGoodsList.add(rentOrderGoods);
                }
                log.info("全部结算租金商品列表:::::::::{}",new Gson().toJson(settlementOrderGoodsList));
                orderGoodsService.saveAll(settlementOrderGoodsList);
            }

        }

//        if (SourceType.CASHIER.name().equals(depositOrders.getSourceType().name())) {
//            //收银台(目前收银台下单校验id、名称、班次必填)
//            if (StringUtils.isEmpty(depositOrders.getCashierId()) || StringUtils.isEmpty(depositOrders.getCashierName())) {
//                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
//            }
//
//        } else { // source的值只能是client 或 cashier，WECHAT  其他后续在支持
//            return new GenericResponse<>(ServiceCodes.SHOP_GOODS_SOURCE_ERROR);
//        }

        //查询场所是否开通在线支付
        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(settlementRequestBO.getPlaceId());
        if (!placeConfigResponse.isResult()) {
            return new GenericResponse<>(ServiceCodes.REGCARD_SERVER_CONFIG_ERROR);
        }
        PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_CONFIG_ONLINE_PAY_NONSUPPORT);
        }

        String payCode = settlementRequestBO.getPayCode(); //付款码
        int payCodeNum = 0;

        try {
            if (payCode != null) {
                log.info("::::::::::::::::::::::结算租赁订单请求payCode:::::::::::::::::::::::::" + payCode);
                payCodeNum = StringUtils.isEmpty(payCode) ? 0 : Integer.parseInt(payCode.substring(0, 2));
                if (payCodeNum >= 10 && payCodeNum <= 15) {
                    // 前缀以10、11、12、13、14、15 开头，则是微信付款码
                    payType = PayType.WECHAT_SCAN;
                } else if (payCodeNum >= 25 && payCodeNum <= 30) {
                    // 前缀以25、26、27、28、29、30 开头，则是支付宝付款码
                    payType = PayType.ALIPAY_SCAN;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }


        //查询用户会员卡
        BillingCardBO cardBO = null;
        if (!StringUtils.isEmpty(depositOrders.getIdNumber())) {
            GenericResponse<ObjDTO<BillingCardBO>> billingCardResponse = billingServerService.findBillingCard(depositOrders.getPlaceId(), depositOrders.getIdNumber());
            if (SourceType.CLIENT.name().equals(depositOrders.getSourceType().name()) && !billingCardResponse.isResult()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            if (billingCardResponse.isResult()) {
                cardBO = billingCardResponse.getData().getObj();
            }
        }

        if (payType == PayType.BILLING_CARD) {
            return new GenericResponse<>(ServiceCodes.MARKET_SHOP_NOT_SUPPORT_PAY);
        }
        if (null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }
        if (payType == PayType.BILLING_CARD && null == cardBO) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_USER);
        }


//        if (!StringUtils.isEmpty(depositOrders.getClientId())) {
//            GenericResponse<ObjDTO<PlaceClientBO>> clientByPlaceIdAndClientId = placeServerService.findClientByPlaceIdAndClientId(depositOrders.getPlaceId(), depositOrders.getClientId());
//            if (clientByPlaceIdAndClientId.isResult()) {
//                areaId = clientByPlaceIdAndClientId.getData().getObj().getAreaId();
//            }
//        }


        List<String> goodsIdList1 = new ArrayList<>();
        //发布事件
        SpringUtils.publishEvent(new GoodsOrderEvent(this, depositOrders, goodsIdList1));
        if (payType == PayType.AGGREGATE_PAY || payType == PayType.ALIPAY_SCAN || payType == PayType.WECHAT_SCAN) { //聚合扫码支付
            //生成付款订单
            String payAmountYuan = String.valueOf(BigDecimal.valueOf(rentOrders.getRealMoney())
                    .divide(BigDecimal.valueOf(100)).setScale(2).doubleValue());
            String orderDes = "租赁商品租金结算，合计" + payAmountYuan + "元";
            String bizType = "shopping";
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            PaymentRequestBO requestBO = new PaymentRequestBO();
            requestBO.setOrderAmount(rentOrders.getRealMoney());
            requestBO.setOrderDesc(orderDes);
            requestBO.setStoreNo(rentOrders.getPlaceId());
            requestBO.setBizOrderId(rentOrderId);
            requestBO.setPayType(payType.name());
            requestBO.setBizServer(BizServer.MARKETING.name());
            requestBO.setBizType(bizType);
            requestBO.setIdNumber(rentOrders.getIdNumber());
            requestBO.setPlaceId(rentOrders.getPlaceId());
            requestBO.setPayCode(payCode);
            // 业绩自动化需求新增字段
            requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));

            if (SourceType.WECHAT == depositOrders.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());
            } else if (SourceType.CLIENT == depositOrders.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CLIENT.getCode());
            } else if (SourceType.CASHIER == depositOrders.getSourceType()) {
                requestBO.setBusinessId(ClientBusinessIds.CASHIER.getCode());
//                if (StringUtils.isEmpty(depositOrdersBO.getClientIp())) {
//                    requestBO.setClientIp(depositOrdersBO.getClientIp());
//                }
            }

            GenericResponse<ObjDTO<PaymentResultBO>> paymentServerResponse = paymentServerService
                    .createPaymentOrder(requestTicket, requestBO);
            if (paymentServerResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PaymentResultBO paymentResultBO = paymentServerResponse.getData().getObj();
                log.info("租赁租金创建支付订单后查看返回结果::::::::::payRes={}", new Gson().toJson(paymentServerResponse.getData().getObj()));
                rentOrders.setLdOrderId(paymentResultBO.getLdOrderId());
                rentOrders.setUpdated(LocalDateTime.now());
                save(rentOrders);
                // 如果是扫码枪支付成功，立即调用接口推送
                asyncOrderService.asycnNotifyPaymentOrder(paymentResultBO, rentOrders);
            }
            return paymentServerResponse;
        }

        return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_PAYTYPE);
    }

    private static OrderGoods getOrderGoods(OrderGoods orderGoodsAdd, String rentOrderId) {
        OrderGoods orderGoods = new OrderGoods();
        orderGoods.setCreated(LocalDateTime.now());
        orderGoods.setPlaceId(orderGoodsAdd.getPlaceId());
        orderGoods.setOrderId(rentOrderId);
        orderGoods.setGoodsId(orderGoodsAdd.getGoodsId());
        orderGoods.setGoodsName(orderGoodsAdd.getGoodsName());
        orderGoods.setQuantity(orderGoodsAdd.getQuantity());
        orderGoods.setUnitPrice(orderGoodsAdd.getUnitPrice());
        orderGoods.setSpecs(orderGoodsAdd.getSpecs());
        orderGoods.setDiscounts(orderGoodsAdd.getDiscounts());
        orderGoods.setGoodsTypeId(orderGoodsAdd.getGoodsTypeId());
        orderGoods.setGoodsTypeName(orderGoodsAdd.getGoodsTypeName());
        orderGoods.setGoodsPresentAmount(0);
        orderGoods.setGoodsQuota(0);
        orderGoods.setGoodsCategory(10);
        orderGoods.setPresent(orderGoodsAdd.getPresent());
        orderGoods.setMealsId("");
        orderGoods.setStatus(0);
        orderGoods.setDeposit(orderGoodsAdd.getDeposit());
        return orderGoods;
    }

    //租赁订单查询(门店后台，客户端)
    public GenericResponse<PagerDTO<OrderGoodsBO>> findRentOrderPageList(HttpServletRequest request, RentOrderQueryBO queryBO) {
        // 构建基础查询参数
        Map<String, String> params = new HashMap<>();
        params = buildBaseQueryParams(queryBO,params);

        //先查询orders
        int findPage = queryBO.getPage() >= 0 ? queryBO.getPage() : 0;
        int size = queryBO.getSize() > 0 ? queryBO.getSize() : 10;

        // 构建排序规则（先按 rentStart 倒序,其次,rent_status=1(待派送的) 优先）
        Sort sort = Sort.by(Sort.Direction.DESC, "rentStart")
                .and(JpaSort.unsafe(Sort.Direction.ASC, "rentStatus"));

        Pageable pageable = PageRequest.of(findPage, size, sort);

        Map<String, String> orderQueryMap = new HashMap<>();
        if (!StringUtils.isEmpty(queryBO.getIdNumber())) {
            orderQueryMap.put("idNumber", queryBO.getIdNumber());
        }
        if (!StringUtils.isEmpty(queryBO.getClientName())) {
            orderQueryMap.put("clientName", queryBO.getClientName());
        }
        orderQueryMap.put("placeId", queryBO.getPlaceId());
        orderQueryMap.put("deleted", "0");
        orderQueryMap.put("orderType", "10");
        orderQueryMap.put("status", "1,2,3,4,5");
        List<Orders> ordersList = findAll(orderQueryMap);
        if (CollectionUtils.isNotEmpty(ordersList)) {
            List<String> orderIdList = ordersList.stream().map(Orders::getOrderId).distinct().collect(Collectors.toList());
            String orderIdsStr = String.join(",", orderIdList);
            params.put("orderIds", orderIdsStr);
        }

        // 分页查询
        Page<OrderGoods> page = orderGoodsService.findAllForRentOrder(params, pageable);
        if (page == null || CollectionUtils.isEmpty(page.getContent())) {
            return new GenericResponse<>(new PagerDTO<>(0, Collections.emptyList()));
        }

        // 转换并排序BO对象
        List<OrderGoodsBO> bos = convertAndSortOrderGoods(page.getContent());

        // 批量获取关联数据
        List<OrderGoodsBO> changeList = enrichOrdersWithRelatedData(bos, queryBO);

        // 返回结果
        return new GenericResponse<>(new PagerDTO<>((int) page.getTotalElements(), changeList));
    }

    //租赁订单查询(收银台)
    public GenericResponse<PagerDTO<OrdersBO>> findCashierRentOrderPageList(HttpServletRequest request, RentOrderQueryBO queryBO) {
        Map<String, String> params = new HashMap<>();
        // 处理商品名称模糊查询
        if (!StringUtils.isEmpty(queryBO.getGoodsName())) {
            Optional<Set<String>> orderIdsOpt = handleGoodsNameQuery(queryBO, params);
            if (!orderIdsOpt.isPresent()) {
                return new GenericResponse<>(new PagerDTO<>(0, Collections.emptyList()));
            }
        }

        // 构建基础查询参数
        params = buildBaseQueryParams(queryBO,params);
        // 分页查询
        Page<Orders> page = findOrdersPage(queryBO, params);
        if (page == null || CollectionUtils.isEmpty(page.getContent())) {
            return new GenericResponse<>(new PagerDTO<>(0, Collections.emptyList()));
        }

        // 转换并排序BO对象
        List<OrdersBO> bos = convertAndSortOrders(page.getContent());

        // 批量获取关联数据
        enrichOrdersWithRelatedDataForCashierRent(bos, queryBO.getPlaceId(), queryBO.getGoodsName());

        // 返回结果
        return new GenericResponse<>(new PagerDTO<>((int) page.getTotalElements(), bos));
    }


    // 构建基础查询参数
    private Map<String, String> buildBaseQueryParams(RentOrderQueryBO queryBO, Map<String, String> params) {
        params.put("placeId", queryBO.getPlaceId());
        params.put("deleted", "0");
        params.put("orderType", "10");
        params.put("status", "1,2,3,4,5");

        Optional.ofNullable(queryBO.getQueryDateType()).ifPresent(v -> params.put("queryDateType", String.valueOf(v)));
        Optional.ofNullable(queryBO.getOrderId()).ifPresent(v -> params.put("orderId", v));
        Optional.ofNullable(queryBO.getStartDate()).ifPresent(v -> params.put("startDate", v));
        Optional.ofNullable(queryBO.getEndDate()).ifPresent(v -> params.put("endDate", v));
        Optional.ofNullable(queryBO.getRentStatus()).ifPresent(v -> params.put("rentStatus", v));
        Optional.ofNullable(queryBO.getIdNumber()).ifPresent(v -> params.put("idNumber", v));
        Optional.ofNullable(queryBO.getPhoneNumber()).ifPresent(v -> params.put("phoneNumber", v));
        Optional.ofNullable(queryBO.getClientName()).ifPresent(v -> params.put("clientName", v));
        Optional.ofNullable(queryBO.getGoodsName()).ifPresent(v -> params.put("goodsName", v));

        return params;
    }

    // 处理商品名称模糊查询
    private Optional<Set<String>> handleGoodsNameQuery(RentOrderQueryBO queryBO, Map<String, String> params) {
        if (StringUtils.isEmpty(queryBO.getGoodsName())) {
            return Optional.empty();
        }

        List<OrderGoods> orderGoodsList = orderGoodsService.findByPlaceIdAndGoodsNameLikeAndDeleted(queryBO.getPlaceId(), queryBO.getGoodsName());

        if (CollectionUtils.isEmpty(orderGoodsList)) {
            return Optional.of(Collections.emptySet());
        }

        Set<String> orderIdSet = orderGoodsList.stream()
                .map(OrderGoods::getOrderId)
                .collect(Collectors.toSet());
        String orderIds = String.join(",", orderIdSet);

        params.put("orderIds", orderIds);
        return Optional.of(orderIdSet);
    }

    // 执行分页查询
//    private Page<Orders> findOrdersPage(RentOrderQueryBO queryBO, Map<String, String> params) {
//        Pageable pageable = PageRequest.of(
//                queryBO.getPage(),
//                queryBO.getSize(),
//                Sort.Direction.DESC,
//                "rentStart",
//                "rentStatus"
//        );
//        return findAll(params, pageable);
//    }
    private Page<Orders> findOrdersPage(RentOrderQueryBO queryBO, Map<String, String> params) {
        // 1. 构建分页参数
        int page = queryBO.getPage() >= 0 ? queryBO.getPage() : 0;
        int size = queryBO.getSize() > 0 ? queryBO.getSize() : 10;

        // 构建排序规则（先按rent_status=1(待派送的) 优先,其次按rentStart 倒序排序）
        Sort sort = Sort.by(Sort.Direction.ASC, "rentStatus")
                .and(JpaSort.unsafe(Sort.Direction.DESC, "rentStart"));

        Pageable pageable = PageRequest.of(page, size, sort);
        return findAll(params, pageable);
    }

    // 转换并排序订单
    private List<OrdersBO> convertAndSortOrders(List<Orders> ordersList) {
        return ordersList.stream()
                .map(Orders::toBO)
                .sorted((o1, o2) -> {
                    // 状态优先排序
                    boolean isO1Priority = (o1.getStatus() == 1);
                    boolean isO2Priority = (o2.getStatus() == 1);
                    if (isO1Priority != isO2Priority) {
                        return Boolean.compare(isO2Priority, isO1Priority);
                    }
                    return Long.compare(o2.getId(), o1.getId());
                })
                .collect(Collectors.toList());
    }

    // 转换并排序订单
    private List<OrderGoodsBO> convertAndSortOrderGoods(List<OrderGoods> orderGoodsList) {
        return orderGoodsList.stream()
                .map(OrderGoods::toBO)
                .sorted((o1, o2) -> {
                    // 状态优先排序
                    boolean isO1Priority = (o1.getStatus() == 1);
                    boolean isO2Priority = (o2.getStatus() == 1);
                    if (isO1Priority != isO2Priority) {
                        return Boolean.compare(isO2Priority, isO1Priority);
                    }
                    return Long.compare(o2.getId(), o1.getId());
                })
                .collect(Collectors.toList());

    }

    // 批量获取关联数据（收银台租赁订单用）
    private void enrichOrdersWithRelatedDataForCashierRent(List<OrdersBO> bos, String placeId, String goodsName) {
        if (CollectionUtils.isEmpty(bos)) {
            return;
        }
        List<String> orderIds = bos.stream().map(OrdersBO::getOrderId).distinct().collect(Collectors.toList());
        List<OrderGoods> byPlaceIdAndOrderIdInAndDeleted = orderGoodsService.findByPlaceIdAndOrderIdInAndDeleted(placeId, orderIds);
        // 转换为 Map<orderId, List<OrderGoods>>
        Map<String, List<OrderGoods>> orderGoodsMap = byPlaceIdAndOrderIdInAndDeleted.stream()
                .collect(Collectors.groupingBy(OrderGoods::getOrderId));

        bos.forEach(item -> {
            // 获取订单商品列表，默认为空列表
            List<OrderGoods> orderGoodsList = orderGoodsMap.getOrDefault(item.getOrderId(), Collections.emptyList());

            // 构建流并应用过滤（如果有）
            Stream<OrderGoods> goodsStream = orderGoodsList.stream();
            if (!StringUtils.isEmpty(goodsName)) {
                goodsStream = goodsStream.filter(each -> each.getGoodsName().contains(goodsName));
            }
            // 转换为 BO 并处理业务逻辑
            List<OrderGoodsBO> orderGoodsBOList = goodsStream
                    .map(OrderGoods::toBO) // 实例方法引用
                    .collect(Collectors.toList());
            // 设置最终结果
            item.setOrderGoodsList(orderGoodsBOList);
        });


        // 使用并行流处理批量数据获取
        boolean useParallel = bos.size() > 50;
        Stream<OrdersBO> stream = useParallel ? bos.parallelStream() : bos.stream();

        // 批量获取商品图片信息
        Map<String, String> goodsPicMap = getGoodsPicMapForOrder(bos, placeId);

        // 批量获取会员信息
        Map<String, BillingCardBO> billingCardMap = getBillingCardMapForOrder(bos, placeId);

        // 批量获取派送人员信息
        Map<String, String> courierNameMap = getCourierNameMapForOrder(bos, placeId);

        // 批量获取区域信息
        Map<String, String> areaIdMap = getClientAreaMapForOrder(bos, placeId);

        // 4. 设置所有关联数据
        stream.forEach(bo -> {
            // 设置商品图片
            bo.getOrderGoodsList().forEach(og -> {
                if (goodsPicMap.containsKey(og.getGoodsId())) {
                    og.setGoodsPic(goodsPicMap.get(og.getGoodsId()));
                }
            });

            // 设置会员信息
            if (org.apache.commons.lang.StringUtils.isNotEmpty(bo.getIdNumber()) && billingCardMap.containsKey(bo.getIdNumber())) {
                BillingCardBO card = billingCardMap.get(bo.getIdNumber());
                bo.setCardTypeId(card.getCardTypeId());
                bo.setCardTypeName(card.getCardTypeName());
                bo.setTotalAccount(card.getTotalAccount());
                bo.setIdName(card.getIdName());
            }

            // 设置快递员信息
            if (org.apache.commons.lang.StringUtils.isNotEmpty(bo.getCourierId()) && courierNameMap.containsKey(bo.getCourierId())) {
                bo.setCourierName(courierNameMap.get(bo.getCourierId()));
            }
            //设置租赁时长
            if (Objects.nonNull(bo.getRentStart()) && Objects.nonNull(bo.getRentEnd())) {
                bo.setRentDuration(calculateTimeDifference(bo.getRentStart(), bo.getRentEnd()));
            }
            // 设置区域信息
            if (!StringUtils.isEmpty(bo.getClientId()) && areaIdMap.containsKey(bo.getClientId())) {
                bo.setAreaId(areaIdMap.get(bo.getClientId()));
            }
        });
    }

    // 批量获取关联数据（门店/客户端 租赁订单用）
    private List<OrderGoodsBO> enrichOrdersWithRelatedData(List<OrderGoodsBO> bos, RentOrderQueryBO queryBO) {
        if (CollectionUtils.isEmpty(bos)) {
            return new ArrayList<>();
        }
        List<OrderGoodsBO> changeList = bos;
        String placeId = queryBO.getPlaceId();
        List<String> orderIds = bos.stream().map(OrderGoodsBO::getOrderId).distinct().collect(Collectors.toList());
        List<Orders> ordersList = this.findByPlaceIdAndOrderIdIn(placeId, orderIds);
        // 转换为 Map<orderId, Orders>
        Map<String, Orders> ordersMap = ordersList.stream().filter(item -> OrderType.DEPOSIT.getCode() == item.getOrderType())
                .collect(Collectors.toMap(Orders::getOrderId, order -> order));
        changeList.forEach(item -> {
            if (ordersMap.containsKey(item.getOrderId())) {
                item.setIdNumber(ordersMap.get(item.getOrderId()).getIdNumber());
                item.setCardTypeId(ordersMap.get(item.getOrderId()).getCardTypeId());
                item.setClientId(ordersMap.get(item.getOrderId()).getClientId());
                item.setClientName(ordersMap.get(item.getOrderId()).getClientName());
                item.setCourierId(ordersMap.get(item.getOrderId()).getCourierId());
                item.setPayType(ordersMap.get(item.getOrderId()).getPayType());
                item.setPayTime(ordersMap.get(item.getOrderId()).getPayTime());
                item.setCreater(ordersMap.get(item.getOrderId()).getCreater());
                item.setCreaterName(ordersMap.get(item.getOrderId()).getCreaterName());
            }
        });
        if (!StringUtils.isEmpty(queryBO.getIdNumber())) {
            changeList = bos.stream().filter(item -> !StringUtils.isEmpty(item.getIdNumber()) && item.getIdNumber().contains(queryBO.getIdNumber())).collect(Collectors.toList());
        }
        // 使用并行流处理批量数据获取
        boolean useParallel = changeList.size() > 50;
        Stream<OrderGoodsBO> stream = useParallel ? changeList.parallelStream() : changeList.stream();

        // 批量获取商品图片信息
        Map<String, String> goodsPicMap = getGoodsPicMapForOrderGoods(changeList, placeId);


        // 批量获取会员信息
        Map<String, BillingCardBO> billingCardMap = getBillingCardMapForOrderGoods(changeList, placeId);

        // 批量获取派送人员信息
        Map<String, String> courierNameMap = getCourierNameMapForOrderGoods(changeList, placeId);

        // 4. 设置所有关联数据
        stream.forEach(bo -> {
            // 设置商品图片
            if (goodsPicMap.containsKey(bo.getGoodsId())) {
                bo.setGoodsPic(goodsPicMap.get(bo.getGoodsId()));
            }


            // 设置会员信息
            if (org.apache.commons.lang.StringUtils.isNotEmpty(bo.getIdNumber()) && billingCardMap.containsKey(bo.getIdNumber())) {
                BillingCardBO card = billingCardMap.get(bo.getIdNumber());
                bo.setCardTypeId(card.getCardTypeId());
                bo.setCardTypeName(card.getCardTypeName());
                bo.setIdName(card.getIdName());
            }

            // 设置快递员信息
            if (org.apache.commons.lang.StringUtils.isNotEmpty(bo.getCourierId()) && courierNameMap.containsKey(bo.getCourierId())) {
                bo.setCourierName(courierNameMap.get(bo.getCourierId()));
            }
            //设置租赁时长
            if (Objects.nonNull(bo.getRentStart()) && Objects.nonNull(bo.getRentEnd())) {
                bo.setRentDuration(calculateTimeDifference(bo.getRentStart(), bo.getRentEnd()));
            }
        });
        return changeList;
    }


    private Map<String, String> getGoodsPicMapForOrder(List<OrdersBO> bos, String placeId) {
        Set<String> goodsIds = bos.stream()
                .flatMap(bo -> bo.getOrderGoodsList().stream().map(OrderGoodsBO::getGoodsId))
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyMap();
        }

        return rentGoodsService.findByPlaceIdAndGoodsIdIn(placeId, new ArrayList<>(goodsIds))
                .stream()
                .collect(Collectors.toMap(RentGoods::getGoodsId, RentGoods::getGoodsPic));
    }


    private Map<String, String> getGoodsPicMapForOrderGoods(List<OrderGoodsBO> ogs, String placeId) {
        Set<String> goodsIds = ogs.stream().map(OrderGoodsBO::getGoodsId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyMap();
        }

        return rentGoodsService.findByPlaceIdAndGoodsIdIn(placeId, new ArrayList<>(goodsIds))
                .stream()
                .collect(Collectors.toMap(RentGoods::getGoodsId, RentGoods::getGoodsPic));
    }


    private Map<String, String> getClientMapForOrderGoods(List<OrderGoodsBO> bos, String placeId) {
        Set<String> clientIds = bos.stream()
                .map(OrderGoodsBO::getClientId)               // 提取 clientId
                .filter(clientId -> !StringUtils.isEmpty(clientId))  // 过滤掉 null,过滤掉空字符串 ""
                .collect(Collectors.toSet());             // 收集到 Set

        if (CollectionUtils.isEmpty(clientIds)) {
            return Collections.emptyMap();
        }

        GenericResponse<ListDTO<PlaceClientBO>> response = placeClientApi.findByPlaceIdAndClientIds(placeId, new ArrayList<>(clientIds));

        return response != null && CollectionUtils.isNotEmpty(response.getData().getList())
                ? response.getData().getList().stream().collect(Collectors.toMap(PlaceClientBO::getClientId, PlaceClientBO::getHostName))
                : Collections.emptyMap();
    }


    private Map<String, BillingCardBO> getBillingCardMapForOrder(List<OrdersBO> bos, String placeId) {
        List<String> idNumbers = bos.stream()
                .map(OrdersBO::getIdNumber)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(idNumbers)) {
            return Collections.emptyMap();
        }

        GenericResponse<ListDTO<BillingCardBO>> response = billingCardApi.findByPlaceIdAndIdNumbers(placeId, idNumbers);
        return response != null && CollectionUtils.isNotEmpty(response.getData().getList())
                ? response.getData().getList().stream().collect(Collectors.toMap(BillingCardBO::getIdNumber, Function.identity()))
                : Collections.emptyMap();
    }


    private Map<String, BillingCardBO> getBillingCardMapForOrderGoods(List<OrderGoodsBO> bos, String placeId) {
        List<String> idNumbers = bos.stream()
                .map(OrderGoodsBO::getIdNumber)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(idNumbers)) {
            return Collections.emptyMap();
        }

        GenericResponse<ListDTO<BillingCardBO>> response = billingCardApi.findByPlaceIdAndIdNumbers(placeId, idNumbers);
        return response != null && CollectionUtils.isNotEmpty(response.getData().getList())
                ? response.getData().getList().stream().collect(Collectors.toMap(BillingCardBO::getIdNumber, Function.identity()))
                : Collections.emptyMap();
    }


    private Map<String, String> getCourierNameMapForOrder(List<OrdersBO> bos, String placeId) {
        Set<String> courierIds = bos.stream()
                .map(OrdersBO::getCourierId)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(courierIds)) {
            return Collections.emptyMap();
        }
        Map<String, String> courierNameMap = new HashMap<>();
        GenericResponse<ListDTO<PlaceAccountBO>> response = placeServerService.findAccountByPlaceId(placeId);

        if (response.isResult() && CollectionUtils.isNotEmpty(response.getData().getList())) {
            List<PlaceAccountBO> accountBOList = response.getData().getList();
            accountBOList.forEach(item -> {
                if (courierIds.contains(item.getAccountId())) {
                    courierNameMap.put(item.getAccountId(), item.getAccountName());
                }
            });
        }
        return courierNameMap;
    }

    private Map<String, String> getClientAreaMapForOrder(List<OrdersBO> bos, String placeId) {
        Set<String> clientIds = bos.stream()
                .map(OrdersBO::getClientId)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(clientIds)) {
            return Collections.emptyMap();
        }
        Map<String, String> areaIdMap = new HashMap<>();
        GenericResponse<ListDTO<PlaceClientBO>>  response = placeClientApi.findByPlaceIdAndClientIds(placeId,new ArrayList<>(clientIds));

        if (response.isResult() && CollectionUtils.isNotEmpty(response.getData().getList())) {
            List<PlaceClientBO> placeClientBOS = response.getData().getList();
            placeClientBOS.forEach(item -> {
                if (clientIds.contains(item.getClientId())) {
                    areaIdMap.put(item.getClientId(), item.getAreaId());
                }
            });
        }
        return areaIdMap;
    }



    private Map<String, String> getCourierNameMapForOrderGoods(List<OrderGoodsBO> bos, String placeId) {
        Set<String> courierIds = bos.stream()
                .map(OrderGoodsBO::getCourierId)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(courierIds)) {
            return Collections.emptyMap();
        }
        Map<String, String> courierNameMap = new HashMap<>();
        GenericResponse<ListDTO<PlaceAccountBO>> response = placeServerService.findAccountByPlaceId(placeId);

        if (response.isResult() && CollectionUtils.isNotEmpty(response.getData().getList())) {
            List<PlaceAccountBO> accountBOList = response.getData().getList();
            accountBOList.forEach(item -> {
                if (courierIds.contains(item.getAccountId())) {
                    courierNameMap.put(item.getAccountId(), item.getAccountName());
                }
            });
        }
        return courierNameMap;
    }
    /**
     * 根据租赁时长、扣费周期和每小时单价，计算总租金。
     *
     * @param rentMinutes   租赁总时长（分钟）
     * @param deductionTime 扣费周期（分钟）
     * @param rentPrice     每小时单价（元/小时）
     * @return 总租金（分）
     */
    public static double calculateTotalRent(long rentMinutes, int deductionTime, double rentPrice) {
        // 1. 计算扣费次数（向上取整）
        int feeCount = (int) Math.ceil((double) rentMinutes / deductionTime);

        // 2. 计算每个周期的费用
        double feePerCycle = (deductionTime / 60.0) * rentPrice;

        // 3. 计算总租金
        return feeCount * feePerCycle;
    }

    /**
     * 计算租金折扣后的金额
     *
     * @param rentPrice    租金
     * @param rentDiscount 租金折扣率
     * @return
     */
    public static int calculateRentAfterDiscount(int rentPrice, int rentDiscount) {
        if (rentDiscount == 100) {
            return rentPrice;
        }
        BigDecimal discount = new BigDecimal(rentDiscount)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return new BigDecimal(rentPrice)
                .multiply(discount)
                .setScale(0, RoundingMode.HALF_UP)
                .intValue();
    }


    /**
     * 计算租赁时长
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return
     */
    public static String calculateTimeDifference(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (startDateTime == null || endDateTime == null) {
            throw new IllegalArgumentException("开始时间和结束时间都不能为null");
        }

        if (endDateTime.isBefore(startDateTime)) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        Duration duration = Duration.between(startDateTime, endDateTime);
        long totalMinutes = duration.toMinutes();

        if (totalMinutes < 60) {
            return totalMinutes + "分钟";
        } else {
            long hours = duration.toHours();
            long minutes = duration.toMinutes() % 60;
            return hours + "小时 " + minutes + "分钟";
        }
    }

}
