package com.rzx.dim4.marketing.service.shop;

import com.rzx.dim4.marketing.entity.InternetFee;
import com.rzx.dim4.marketing.repository.InternetFeeRepository;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024年12月04日 16:08
 */
@Service
@Slf4j
public class InternetFeeService {

    @Autowired
    private InternetFeeRepository internetFeeRepository;

    public InternetFee save(InternetFee internetFee) {
        if (StringUtils.isEmpty(internetFee.getInternetFeeId())) {
            internetFee.setInternetFeeId(getNextId(internetFee.getPlaceId()));
        }
        return internetFeeRepository.save(internetFee);
    }

    @Synchronized
    private synchronized String getNextId(String placeId) {
        int internetFeeId = 4000;
        Optional<InternetFee> lastTags = internetFeeRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
        if (lastTags.isPresent()) {
            internetFeeId = Integer.parseInt(lastTags.get().getInternetFeeId()) + 1;
        }
        return String.valueOf(internetFeeId);
    }

    public List<InternetFee> findByPlaceId(String placeId){
        return internetFeeRepository.findByPlaceIdAndDeleted(placeId,0);
    }
    public Optional<InternetFee> findById(Long id){
        return internetFeeRepository.findById(id);
    }

    public Optional<InternetFee> findByPlaceIdAndInternetFeeId(String placeId,String internetFeeId){
        return internetFeeRepository.findByPlaceIdAndInternetFeeIdAndDeleted(placeId,internetFeeId,0);
    }

    // 根据场所号和名字查询网费充送
    public Optional<InternetFee> findByPlaceIdAndName(String placeId,String name){
        return internetFeeRepository.findByPlaceIdAndNameAndDeleted(placeId,name,0);
    }

    // 查询上架的网费充送活动
    public Optional<InternetFee> findByPlaceIdAndGoodsId(String placeId,String goodsId){
        return internetFeeRepository.findByPlaceIdAndGoodsIdAndStatusAndDeleted(placeId,goodsId,0,0);
    }

    // 查询上架的网费充送活动
//    public Optional<InternetFee> findByPlaceIdAndGoodsIdAndCardTypeId(String placeId,String goodsId, String  cardTypeId){
//        return internetFeeRepository.findByPlaceIdAndGoodsIdAndCardTypeIdsLikeAndStatusAndDeleted(placeId,goodsId, "%" + cardTypeId + "%",0,0);
//    }

    public List<InternetFee> findByPlaceIdAndGoodsId(String placeId, String goodsId, String cardTypeId) {
        return internetFeeRepository.findByPlaceIdAndGoodsIdAndCardTypeIdsLikeAndStatusAndDeleted(placeId, goodsId, "%" + cardTypeId + "%", 0, 0);
    }


    public Page<InternetFee> findAll(Map<String, Object> map, Pageable pageable) {
        return internetFeeRepository.findAll((Specification<InternetFee>) (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();

            if (map.containsKey("name") && !StringUtils.isEmpty(map.get("name"))) {// 活动名称
                andPredicateList.add(cb.like(root.get("name").as(String.class),"%"+ map.get("name")+"%"));
            }

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所Id
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class),map.get("placeId")));
            }

            if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {
                andPredicateList.add(cb.equal(root.get("deleted"),Integer.parseInt(map.get("deleted")+"")));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        }, pageable);
    }


    public int updateInternetFeeCardTypeIds(String placeId,  String cardTypeIds){
        return internetFeeRepository.updateGoodsCardTypeIds(placeId);
    }

    // 逻辑删除记录
    @Transactional
    public void  logicdeleteByPlaceId (String placeId) {
        internetFeeRepository.logicDeleteByPlaceId(placeId);
    }
}
