package com.rzx.dim4.marketing.service.douyin;

import com.alibaba.fastjson.JSONObject;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBriefBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.LogInterfaceUrl;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingServerServiceHystrix;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.billing.BillingCardApi;
import com.rzx.dim4.base.service.feign.place.PlaceAreaApi;
import com.rzx.dim4.base.vo.marketing.CouponAmountVO;
import com.rzx.dim4.marketing.entity.*;
import com.rzx.dim4.marketing.repository.MarketDouyinStoreRepository;
import com.rzx.dim4.marketing.service.coupon.DiscountCouponService;
import com.rzx.dim4.marketing.service.shop.*;
import com.rzx.dim4.marketing.util.douyin.DouyinRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.naming.Name;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzx.dim4.base.enums.marketing.LogInterfaceUrl.*;

/**
 * 抖音门店管理
 * <AUTHOR>
 * @date 2024年08月05日 16:04
 */
@Slf4j
@Service
public class MarketDouyinStoreService {

    @Autowired
    private MarketDouyinStoreRepository marketDouyinStoreRepository;

    @Autowired
    private DouyinTokenService douyinTokenService;

    @Autowired
    private DouyinRequest douyinRequestUrl;

    @Autowired
    private LogMarketRequestService logMarketRequestService;

    @Autowired
    private BuyGiftsService buyGiftsService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private ShopConfigService shopConfigService;

    @Autowired
    private StorageGoodsService storageGoodsService;

    @Autowired
    private BuyGiftsGoodsService buyGiftsGoodsService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BillingServerService billingServerService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private GoodsTypeService goodsTypeService;


    /**
     * 查询门店匹配关系 (业务暂时用不到这个接口)
     * @param placeId
     * @return
     */
    public GenericResponse<ObjDTO<MarketDouyinStoreBO>> queryMatchStore(String placeId){

        Map<String,Object> param = new HashMap<>();
        param.put("ext_ids",placeId);

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.getRequest(DOUYIN_QUERY_MATCH.getUrl(), accessToken, param);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,param,now,DOUYIN_QUERY_MATCH);

        return new GenericResponse<>();
    }

    /**
     * 查询门店匹配关系 (业务暂时用不到这个接口)
     * @return
     */
    public GenericResponse<ObjDTO<MarketDouyinStoreBO>> queryGoodsTypes(){

        Map<String,Object> param = new HashMap<>();
        param.put("category_id","4000000");//4000000 = 休闲娱乐 主类目
        param.put("query_category_type","1");//

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.getRequest(DOUYIN_GOODS_TYPES_QUERY.getUrl(), accessToken, param);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,null,param,now,DOUYIN_GOODS_TYPES_QUERY);

        return new GenericResponse<>();
    }

    /**
     * 更新门店匹配任务状态
     * @param placeId
     * @return
     */
    public GenericResponse<ObjDTO<MarketDouyinStoreBO>> queryMatchStoreTask(String placeId){

        Optional<MarketDouyinStore> optionalMarketDouyinStore = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(!optionalMarketDouyinStore.isPresent()){
            return new GenericResponse<>(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }
        MarketDouyinStore marketDouyinStore = optionalMarketDouyinStore.get();
        if(marketDouyinStore.getStatus() == 0){
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_STATUS_ERROR);
        }

        Map<String,Object> param = new HashMap<>();
        param.put("task_id",marketDouyinStore.getTaskId());

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.getRequest(DOUYIN_QUERY_MATCH_TASK.getUrl(), accessToken, param);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,param,now,DOUYIN_QUERY_MATCH_TASK);

        if(map != null && map.containsKey("data")){

            Map data = (Map) map.get("data");
            if(data.containsKey("error_code") && Double.valueOf(data.get("error_code")+"") == 0){
                List<Map<String,Object>> results = (List)data.get("results");
                for (Map<String, Object> result : results) {

                    if(String.valueOf(result.get("ext_id")).equals(placeId)){
                        int matchStatus = Integer.parseInt(String.valueOf(result.getOrDefault("match_status", "1"))); //1 - 等待执行 2 - 匹配进行中 5 - 匹配完成
                        int matchResult = Integer.parseInt(String.valueOf(result.getOrDefault("match_result", "0")));
                        marketDouyinStore.setStatus(matchStatus);
                        marketDouyinStore.setMatchResult(matchResult);
                        marketDouyinStore.setMatchMessage(String.valueOf(result.get("match_message")));
                        marketDouyinStoreRepository.save(marketDouyinStore);
                    }
                }

            }else{
                return new GenericResponse<>(String.valueOf(data.get("description")));
            }
        }

        return new GenericResponse<>(new ObjDTO<>(marketDouyinStore.toBO()));
    }

    /**
     * 提交门店匹配任务
     * @param placeId
     * @return
     */
    public GenericResponse<?> matchStorePlace(String placeId){
        if(StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Optional<MarketDouyinStore> optionalMarketDouyinStore = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(!optionalMarketDouyinStore.isPresent()){
            return new GenericResponse<>(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }
        MarketDouyinStore marketDouyinStore = optionalMarketDouyinStore.get();

        Map<String,Object> param = new HashMap<>();
        param.put("ext_id",placeId);
        param.put("poi_id",marketDouyinStore.getPoiId()); //第三方场所id
        param.put("poi_name",marketDouyinStore.getPoiName());
        param.put("address",marketDouyinStore.getAddress());       //详细地址
        param.put("longitude",String.valueOf(marketDouyinStore.getLongitude()));       //经度
        param.put("latitude",String.valueOf(marketDouyinStore.getLatitude()));       //纬度

        Map<String,Object> datas = new HashMap<>();
        List<Map<String,Object>> list = new ArrayList<>();
        list.add(param);
        datas.put("datas",list);  //注意！！  必须是这个格式 {"datas":[{"key":"value"}]}

        String accessToken = douyinTokenService.getAccessToken();

        String responseString = douyinRequestUrl.postRequest(DOUYIN_MATCH_TASK.getUrl(), accessToken, datas);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,datas,now,DOUYIN_MATCH_TASK);

        if(map != null && map.containsKey("data")){

            Map data = (Map) map.get("data");
            if(data.containsKey("error_code") && Double.valueOf(data.get("error_code")+"") == 0){

                marketDouyinStore.setUpdated(now);
                marketDouyinStore.setStatus(1);
                marketDouyinStore.setTaskId(data.get("task_id")+"");
                marketDouyinStoreRepository.save(marketDouyinStore);
            }else{
                log.info("matchStorePlace 失败！，请求结果："+data);
                return new GenericResponse<>(String.valueOf(data.get("description")));
            }
        }

        return new GenericResponse<>(ServiceCodes.OPT_ERROR);
    }

    /**
     * 查询门店信息
     * account_id和poi_id，二者必填其一，若都填写，account_id优先
     */
    public List<MarketDouyinStoreBO> goodLifeQuery(String poiId, String accountId,String placeId){

        if(StringUtils.isEmpty(poiId) && StringUtils.isEmpty(accountId)){
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(byPlaceId.isPresent()){
            MarketDouyinStore marketDouyinStore = byPlaceId.get();
            if(!poiId.equals(marketDouyinStore.getPoiId())){
                //这里是否可以修改为删掉之前的场所
                throw new ServiceException(ServiceCodes.MARKET_STORE_REPETITIVE_BINDING);
            }
        }

        Map<String,Object> param = new HashMap<>();
        param.put("page",1);
        param.put("size",10);
        if(!StringUtils.isEmpty(poiId)){
            param.put("poi_id",poiId);
        }
        if(!StringUtils.isEmpty(accountId)){
            param.put("account_id",accountId);
        }

        String accessToken = douyinTokenService.getAccessToken();

        String responseString = douyinRequestUrl.getRequest(DOUYIN_QUERY_POI.getUrl(), accessToken, param);
        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,param,now,DOUYIN_QUERY_POI);

        List<MarketDouyinStore> datas = new ArrayList<>();
        if(map.containsKey("data")) {
            Map data = (Map) map.get("data");
            if (data.containsKey("error_code") && Double.valueOf(data.get("error_code") + "") == 0) {
                List<Map<String,Map<String,Object>>> pois = (List)data.get("pois");
                for (Map<String, Map<String, Object>> dateMap : pois) {
                    MarketDouyinStore marketDouyinStore = new MarketDouyinStore();
                    //查询是否已存在数据，如果存在则更新，不存在就保存

                    Map<String, Object> accountMap = dateMap.get("account");
                    Map<String,String> poiAccountMap = (Map) accountMap.get("poi_account");
                    Map<String, String> rootAccountMap = (Map)dateMap.get("root_account");

//                    Optional<MarketDouyinStore> byAccountId = marketDouyinStoreRepository.findByAccountId(poiAccountMap.get("account_id"));
//                    if(byAccountId.isPresent()){
//                        throw  new ServiceException();
//                    }else{
////                        marketDouyinStore = byAccountId.get();
////                        marketDouyinStore.setUpdated(now);
//                        marketDouyinStore.setCreated(now);
//                    }
                    marketDouyinStore.setPlaceId(placeId);

                    marketDouyinStore.setPoiId(String.valueOf(dateMap.get("poi").get("poi_id")));
                    marketDouyinStore.setPoiName(String.valueOf(dateMap.get("poi").get("poi_name")));
                    marketDouyinStore.setAddress(String.valueOf(dateMap.get("poi").get("address")));
                    marketDouyinStore.setLongitude(Double.parseDouble(String.valueOf(dateMap.get("poi").get("longitude"))));
                    marketDouyinStore.setLatitude(Double.parseDouble(String.valueOf(dateMap.get("poi").get("latitude"))));


                    marketDouyinStore.setAccountId(poiAccountMap.get("account_id"));
                    marketDouyinStore.setAccountName(poiAccountMap.get("account_name"));

                    marketDouyinStore.setRootAccountId(rootAccountMap.get("account_id"));
                    marketDouyinStore.setRootAccountName(rootAccountMap.get("account_name"));
                    marketDouyinStore.setStatus(1);

                    datas.add(marketDouyinStore);
                }
            }else{
                throw new ServiceException(String.valueOf(data.get("description")));
            }
        }
//        marketDouyinStoreRepository.saveAll(datas);

        List<MarketDouyinStoreBO> bos = datas.stream().map(MarketDouyinStore::toBO).collect(Collectors.toList());
        return bos;
    }

    /**
     * 保存请求日志
     * @param map 返回内容
     * @param placeId 场所id
     * @param param 请求参数
     * @param now 时间长
     */
    private void addLogMarketRequest(Map map, String placeId, Map<String,Object> param, LocalDateTime now, LogInterfaceUrl logInterfaceUrl){
        try {
            //请求日志
            LogMarketRequest logMarketRequest = new LogMarketRequest();
            logMarketRequest.setPlaceId(placeId);
            logMarketRequest.setInterfaceUrl(logInterfaceUrl);
            logMarketRequest.setInterfaceSource(logInterfaceUrl.getSource());
            logMarketRequest.setRequestParam(JSONObject.toJSONString(param));
            logMarketRequest.setCreated(now);
            if(map != null && map.containsKey("data")){
                Map data = (Map) map.get("data");
                logMarketRequest.setReturnCode(data.get("error_code")+"");
                logMarketRequest.setReturnMessage(data.get("description")+"");
                logMarketRequest.setTaskId(data.get("task_id")+"");
            }else{
                logMarketRequest.setReturnCode("-1");
                logMarketRequest.setReturnMessage("请求失败");
            }
            logMarketRequestService.save(logMarketRequest);
        }catch (Exception e){
            e.printStackTrace();;
        }
    }

    /**
     * 创建/更新商品接口 (待实现，需要在页面上新增很多字段)
     * @param discountCoupon 四维管家的商品信息
     * @return
     */
//    public GenericResponse<?> productSave(DiscountCoupon discountCoupon){
    //根据场所id查询第三方商户信息
//        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPlaceId(discountCoupon.getPlaceId());
//        if(!byPlaceId.isPresent()){
//            return new GenericResponse<>(ServiceCodes.MARKET_STORE_NOT_BIND);
//        }
//        MarketDouyinStore marketDouyinStore = byPlaceId.get();
//
//        Map<String,Object> param = new HashMap<>();
//        param.put("account_id",marketDouyinStore.getAccountId());
//
//        Map<String,Object> productMap = new HashMap<>();
//        productMap.put("out_id",discountCoupon.getCouponId());//外部商品 ID
//        productMap.put("product_name",discountCoupon.getCouponName());//商品名
//        productMap.put("category_full_name","网吧/网咖");//品类全名-传统娱乐
//        productMap.put("category_id",4007001);//三级品类id
//        productMap.put("product_type",1);//团购类型：（更新商品时不可修改）1：团购套餐
//        productMap.put("biz_line",1);//业务线。（更新商品时不可修改）
//        productMap.put("account_name",marketDouyinStore.getPoiName());//商家名
//
//        Map<String,String> attrKeyMap = new HashMap<>(); //一下attrKeyMap字段全部为必填（但是能传空） 值必须为json格式
//        attrKeyMap.put("appointment","{\"need_appointment\":\"false\"}");//预约信息 做展示使用
//        attrKeyMap.put("auto_renew","false");//自动延期 true/false
//        attrKeyMap.put("can_no_use_date","");//不可使用日期 - 传空在自己系统做校验
//        attrKeyMap.put("Description","");//商品描述
//        if(!StringUtils.isEmpty(discountCoupon.getUrls())){
//            String[] split = discountCoupon.getUrls().split(",");
//            List<Map<String,String>> urlMap = new ArrayList<>();
//            for (String s : split) {
//                urlMap.add(new HashMap<String,String>(){{
//                    put("url",s);
//                }});
//            }
//            attrKeyMap.put("image_list",JSONObject.toJSONString(urlMap));//封面图 传url
//        }else{
//            attrKeyMap.put("image_list","[]");//封面图 传url
//        }
//
//        // todo
//        attrKeyMap.put("Notification","");//使用规则
//        attrKeyMap.put("RefundPolicy","");//退款政策
//        attrKeyMap.put("refund_need_merchant_confirm","");//退款是否需商家审核
//        attrKeyMap.put("show_channel","");//投放渠道
//        attrKeyMap.put("use_date","");//使用日期
//        attrKeyMap.put("use_time","");//使用时间
//        attrKeyMap.put("code_source_type","");//券码生成方式
//        attrKeyMap.put("settle_type","");//收款方式
//        attrKeyMap.put("use_type","");//团购使用方式
//        attrKeyMap.put("limit_rule","");//限制购买
//
//        productMap.put("attr_key_value_map",marketDouyinStore.getPoiName());//商品属性 KV，
//        return null;
//    }

    /**
     * 验券准备（根据优惠券二维码查询优惠券信息）
     * @param url 二维码中的url，需要根据url获取encrypted_data参数
     * @param code 用户手输的code
     *         注意！，encrypted_data 和 code 两个参数不可以同时提交
     * @return
     */
    public GenericResponse<ObjDTO<BuyGiftsBO>> certificatePrepare(String url, String code, String placeId){

        //5秒防止重复提交
        String requestRepetitionVerify = "market:"+placeId+"_"+url+"_"+code;
        if (Boolean.TRUE.equals(stringRedisTemplate.delete(requestRepetitionVerify))) {
            return new GenericResponse<>(ServiceCodes.FREQUENT_REQUESTS);
        }
        stringRedisTemplate.opsForValue().set(requestRepetitionVerify, requestRepetitionVerify, 5, TimeUnit.SECONDS);

        log.info("准备验券:::{} {} {}",placeId,url,code);

        Optional<ShopConfig> shopConfigOptional = shopConfigService.findByPlaceId(placeId);
        ShopConfig shopConfig = shopConfigOptional.get();
        String rackId = null;
        if(shopConfig.getStoreType() == 0){
            //多仓库
            rackId = "100000"; //现阶段默认100000
        }else if(shopConfig.getStoreType() == 1){
            //单仓库
            rackId = "000000";
        }

        if(StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if(StringUtils.isEmpty(url) && StringUtils.isEmpty(code)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if((!StringUtils.isEmpty(url) && !url.contains("v.douyin.com"))){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_NOT_FOUND);
        }

        //获取第三方场所信息
        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(!byPlaceId.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_NOT_BIND);
        }
        MarketDouyinStore marketDouyinStore = byPlaceId.get();

        Map<String,Object> params = new HashMap<>();

        if(StringUtils.isEmpty(code)){
            try {
                HttpURLConnection con = (HttpURLConnection) new URL(url).openConnection();
                con.setInstanceFollowRedirects(false);
                con.connect();
                int responseCode = con.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_MOVED_PERM || responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {
                    String location = con.getHeaderField("Location");
                    log.info("场所：：：{} 识别优惠券二维码url：：：{}获取优惠券接口参数成功，重定向：：：{}",placeId,url,location);
                    params.put("encrypted_data",getParamByUrl(location,"object_id"));
                }
            }catch (Exception e){
                log.error("场所：：：{} 识别优惠券二维码url：：：{}获取优惠券接口参数失败！",placeId,url);
                return new GenericResponse<>(ServiceCodes.OPT_ERROR);
            }
        }else{
            params.put("code",code);
        }

        //如果未成功获取参数，直接返回错误
        if(StringUtils.isEmpty(params.get("code")) && StringUtils.isEmpty(params.get("encrypted_data"))){
            return new GenericResponse<>(ServiceCodes.MARKET_COUPON_NOT_FOUND);
        }

        params.put("poi_id",marketDouyinStore.getPoiId());

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.getRequest(DOUYIN_COUPON_FULFILMENT_PREPARE.getUrl(), accessToken, params);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,params,now,DOUYIN_COUPON_FULFILMENT_PREPARE);

        if(map != null && map.containsKey("data")){

            Map data = (Map) map.get("data");
            if(data.containsKey("error_code") && Double.valueOf(data.get("error_code")+"") == 0){
                List<Map<String,Object>> results = (List)data.get("certificates");
                for (Map<String, Object> result : results) {
                    String encryptedCode = String.valueOf(result.get("encrypted_code"));
                    Map<String,Object> skuMap = (Map)result.get("sku");
                    String skuId = String.valueOf(skuMap.get("sku_id")); //商品skuId
                    String buyGiftsName = String.valueOf(skuMap.get("title")); //商品名称
//                    String productId = String.valueOf(skuMap.get("product_id")); //商品id ?? 文档说是这个id，但是取的suk_id能匹配上之前获取的product_id
                    Long soldStartTime = Long.valueOf(String.valueOf(skuMap.get("sold_start_time"))); //商品开始时间（毫秒时间戳）

                    Map<String,Object> amountMap = (Map)result.get("amount");
                    Integer pay_amount = Integer.valueOf(amountMap.getOrDefault("pay_amount","0")+""); //用户实付金额
                    Integer coupon_pay_amount = Integer.valueOf(amountMap.getOrDefault("coupon_pay_amount","0")+""); //优惠券应付金额

                    CouponAmountVO vo = new CouponAmountVO(pay_amount,coupon_pay_amount,coupon_pay_amount-pay_amount);
                    stringRedisTemplate.opsForValue().set(encryptedCode,JSONObject.toJSONString(vo),15,TimeUnit.MINUTES);
                    Optional<BuyGifts> byPlaceIdAndBuyGiftsName = null;
                    if(!StringUtils.isEmpty(skuId)){
                        byPlaceIdAndBuyGiftsName = buyGiftsService.findByPlaceIdAndPlatformId(placeId, skuId);
                        log.info("根据抖音 productId：{} 查询买赠信息结果:::{}",skuId,byPlaceIdAndBuyGiftsName.isPresent());
                    }
                    if(null == byPlaceIdAndBuyGiftsName || !byPlaceIdAndBuyGiftsName.isPresent()){
                        byPlaceIdAndBuyGiftsName = buyGiftsService.findByPlaceIdAndBuyGiftsName(placeId, buyGiftsName);
                    }
                    if(!byPlaceIdAndBuyGiftsName.isPresent()){
                        return new GenericResponse<>(ServiceCodes.MARKET_BUY_GIFTS_NOT_FOUND);
                    }
                    BuyGifts buyGifts = byPlaceIdAndBuyGiftsName.get();
                    if(buyGifts.getStatus() == 1){
                        return new GenericResponse<>(ServiceCodes.MARKET_BUYGIFTS_STATUS_ERROR);
                    }
                    BuyGiftsBO buyGiftsBO = buyGifts.toBO();
                    buyGiftsBO.setEncryptedCode(encryptedCode);
                    buyGiftsBO.setSkuId(skuId);
                    buyGiftsBO.setSoldStartTime(soldStartTime);
                    buyGiftsBO.setSourceType(SourceType.DOUYIN);

                    //查询赠送的商品列表
                    List<BuyGiftsGoods> byPlaceIdAndBuyGiftsId = buyGiftsGoodsService.findByPlaceIdAndBuyGiftsId(buyGiftsBO.getPlaceId(), buyGiftsBO.getBuyGiftsId());
                    List<BuyGiftsGoodsBO> giftsGoodsBOS = byPlaceIdAndBuyGiftsId.stream().map(BuyGiftsGoods::toBO).collect(Collectors.toList());
                    for (BuyGiftsGoodsBO giftsGoodsBO : giftsGoodsBOS) {
                        Optional<Goods> byPlaceIdAndGoodsId = goodsService.findByPlaceIdAndGoodsId(giftsGoodsBO.getPlaceId(), giftsGoodsBO.getGoodsId());
                        if(byPlaceIdAndGoodsId.isPresent()){
                            giftsGoodsBO.setGoodsPic(byPlaceIdAndGoodsId.get().getGoodsPic());
                        }

                        if(!StringUtils.isEmpty(rackId)){
                            //查询库存
                            Optional<StorageGoods> byPlaceIdAndGoodsIdAndStorageRackId = storageGoodsService.findByPlaceIdAndGoodsIdAndStorageRackId(giftsGoodsBO.getPlaceId(), giftsGoodsBO.getGoodsId(), rackId);
                            if(byPlaceIdAndGoodsIdAndStorageRackId.isPresent()){
                                giftsGoodsBO.setGoodsStocksNum(byPlaceIdAndGoodsIdAndStorageRackId.get().getGoodsStocksNum());
                            }
                        }
                    }

                    buyGiftsBO.setBuyGiftsGoodsBOS(giftsGoodsBOS);

                    return new GenericResponse<>(new ObjDTO<>(buyGiftsBO));
                }

            }else{
                return new GenericResponse<>(String.valueOf(data.get("description")));
            }
        }

        return new GenericResponse<>(ServiceCodes.NOT_FOUND);
    }

    /**
     * 核销抖音优惠券
     * @param placeId 场所id
     * @param encryptedCode 优惠券码
     * @param idNumber 证件号，用来打日志
     * @param verifyToken 一次验券的标识 (用于短时间内的幂等)、主要用于三方码订单防止重复提交，要保证单个操作结果一致
     * @return
     */
    public GenericResponse<SimpleObjDTO> certificateVerify(String encryptedCode, String verifyToken, String placeId, String idNumber){

        if (StringUtils.isEmpty(encryptedCode) || StringUtils.isEmpty(verifyToken) || StringUtils.isEmpty(placeId)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(!byPlaceId.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_NOT_BIND);
        }
        MarketDouyinStore marketDouyinStore = byPlaceId.get();

        List list = new ArrayList();
        list.add(encryptedCode);
        //参数
        Map<String,Object> params = new HashMap<>();
        params.put("verify_token",verifyToken);
        params.put("encrypted_codes",list);
        params.put("poi_id",marketDouyinStore.getPoiId());

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.postRequest(DOUYIN_COUPON_FULFILMENT_VERIFY.getUrl(), accessToken, params);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,placeId,params,now,DOUYIN_COUPON_FULFILMENT_VERIFY);

        if(map != null && map.containsKey("data")) {
            Map data = (Map) map.get("data");
            if (data.containsKey("error_code") && Double.valueOf(data.get("error_code") + "") == 0) {

                List<Map<String,Object>> results = (List)data.get("verify_results"); //一次只核销一张券，目前默认按一张券结果处理
                for (Map<String, Object> resultMap : results) {
                    String result = String.valueOf(resultMap.get("result"));
                    String msg = String.valueOf(resultMap.get("msg"));
                    if(!"0".equals(result)){
                        log.info("用户:::{} 在场所:::{} 核销:::{} 失败！ 原因为:::{}--{}",idNumber,placeId,encryptedCode,result,msg);
                        return new GenericResponse<>(msg);
                    }

                    String certificateId = String.valueOf(resultMap.get("certificate_id")); //这个id 用于后续退款和撤销核销
                    String verifyId = String.valueOf(resultMap.get("verify_id")); //这个id 用于后续退款和撤销核销

                    Map<String,String> resultMapDate = new HashMap<>();
                    resultMapDate.put("verifyId",verifyId);
                    resultMapDate.put("certificateId",certificateId);
                    return new GenericResponse<>(new SimpleObjDTO(resultMapDate));
                }
            }else{
                return new GenericResponse<>(String.valueOf(data.get("description")));
            }
        }

        return new GenericResponse<>(ServiceCodes.OPT_ERROR);
    }


    /**
     * 取消核销优惠券
     * 订单状态由“已使用”回改为“待使用”，用于验券错误需要撤回验券等场景， 有时间限制，验券超过一个小时就不可再撤销。
     * @param certificateId 验券成功时返回的 certificateId
     * @param verifyId 验券成功时返回的 verifyId
     * @return 接口调用结果
     */
    public GenericResponse<?> cancelVerifyCoupon(String certificateId,String verifyId,String placeId) {
        if (StringUtils.isEmpty(certificateId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("verify_id", verifyId);
        params.put("certificate_id", certificateId);

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.postRequest(DOUYIN_GOODS_CANCEL_VERIFY.getUrl(), accessToken, params);


        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);
        log.info("取消核销抖音优惠券:::{]:::{}:::{}",certificateId,verifyId,placeId);

        addLogMarketRequest(map, placeId, params, now,DOUYIN_GOODS_CANCEL_VERIFY);

        if (map != null && map.containsKey("data")) {
            Map data = (Map) map.get("data");
            if (data.containsKey("error_code") && Double.valueOf(data.get("error_code") + "") == 0 && "success".equals(data.get("description")+"")) {
                return new GenericResponse<>(ServiceCodes.NO_ERROR);
            } else {
                return new GenericResponse<>(String.valueOf(data.get("description")));
            }
        }

        return new GenericResponse<>(ServiceCodes.OPT_ERROR);
    }


    /**
     * 获取请求url的指定key 的value值
     * @param url 请求地址
     * @param paramKey 获取对应key的value
     * @return
     */
    public String getParamByUrl(String url,String paramKey){
        Map<String, String> paramMap = new HashMap<>();
        String[] params = url.split("&"); // 分割查询参数
        for (String param : params) {
            String[] keyValue = param.split("=");
            if (keyValue.length > 1) {
                String key = keyValue[0];
                String value = keyValue.length > 2 ? String.join("=", keyValue):keyValue[1];
                paramMap.put(key, value);
            }
        }
        // 获取特定参数
        return paramMap.get(paramKey);
    }

    private Map<String,Object> queryOnlineGoods(String accountId,String cursor,String count,String poiId){


        Map<String,Object> param = new HashMap<>();
        param.put("account_id",accountId);  //场所门店id
        param.put("status","1");//1-在线 2-下线 3-封禁
        param.put("count",count); //就查询50条
        param.put("goods_creator_type","1");
        if(!StringUtils.isEmpty(poiId)){
            List<String> poiIds = new ArrayList<>();
            poiIds.add(poiId);
            param.put("poi_ids",poiIds);
        }

//        if(!StringUtils.isEmpty(cursor)){
            param.put("cursor",cursor);
//        }

        String accessToken = douyinTokenService.getAccessToken();
        String responseString = douyinRequestUrl.getRequest(DOUYIN_GOODS_ONLINE_QUERY.getUrl(), accessToken, param);

        LocalDateTime now = LocalDateTime.now();
        Map map = JSONObject.parseObject(responseString, Map.class);

        addLogMarketRequest(map,null,param,now,DOUYIN_GOODS_ONLINE_QUERY);

        return map;
    }

    public GenericResponse<SimpleObjDTO> queryOnlineGoods(String placeId){

        //获取商户信息
        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPlaceId(placeId);
        if(!byPlaceId.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_NOT_BIND);
        }
        //查询会员卡类型
        GenericResponse<ListDTO<BillingCardTypeBO>> cardTypeByPlaceId = billingServerService.findCardTypeByPlaceId(placeId);
        String cardTypeIds = "1000,1001";
        if(cardTypeByPlaceId.isResult()){
            List<String> cardTypeIdList = cardTypeByPlaceId.getData().getList().stream().map(BillingCardTypeBO::getCardTypeId).collect(Collectors.toList());
            cardTypeIds = String.join(",", cardTypeIdList);
        }
        //查询全部区域
        String areaIds ="2001";
        GenericResponse<ListDTO<PlaceAreaBO>> areaApiByPlaceId = placeServerService.findPlaceAreaByPlaceId(placeId);
        if(cardTypeByPlaceId.isResult()){
            List<String> areaIdList = areaApiByPlaceId.getData().getList().stream().map(PlaceAreaBO::getAreaId).collect(Collectors.toList());
            areaIds = String.join(",", areaIdList);
        }
        //查询商品类型
        String goodsTypeId = "";
        String goodsTypeName = "默认分类";
        Optional<GoodsType> goodsTypeOptional = goodsTypeService.findByPlaceIdAndGoodsTypeName(placeId, "美团");
        if(goodsTypeOptional.isPresent()){
            goodsTypeId = goodsTypeOptional.get().getGoodsTypeId();
            goodsTypeName = goodsTypeOptional.get().getGoodsTypeName();
        }
        MarketDouyinStore marketDouyinStore = byPlaceId.get();
        //获取商品列表
        String accountId = marketDouyinStore.getRootAccountId();
        String nextCursor = null; //分页参数，传null不分页
        String count = "50"; //查询条数（一次最多50）
        Map<String, Object> map = queryOnlineGoods(accountId, nextCursor, count,marketDouyinStore.getPoiId());
        //处理商品列表数据
        if (map != null && map.containsKey("data")) {
            Map data = (Map) map.get("data");
            if (data.containsKey("error_code") && Double.valueOf(data.get("error_code") + "") == 0) {
                //查询成功,获取products 商品列表
                List<Map<String,Object>> products = (List)data.get("products");
                if(products.size() > 0){
                    int total = products.size();
                    int successCount = 0;
                    int errorCount = 0;
                    int repetitionCount = 0;
                    for (Map<String, Object> productMap : products) {
                       try {
                           Map<String,Object> product = (Map) productMap.get("product"); //商品信息
                           Map<String,Object> sku = (Map) productMap.get("sku"); //价格信息
                           //校验名称是否已存在，如果已存在则跳过
                           String productName = product.get("product_name") + ""; //商品名称
                           Integer actualAmount = Integer.valueOf(sku.getOrDefault("actual_amount","0")+""); //实际销售价格
                           Optional<BuyGifts> byName = buyGiftsService.findByPlaceIdAndBuyGiftsName(placeId, productName);
                           if(byName.isPresent()){
                               repetitionCount += 1;
                               continue;
                           }
                           byName = buyGiftsService.findByPlaceIdAndPlatformId(placeId, product.get("product_id") + ""); //查询是否有重id
                           if(byName.isPresent()){
                               repetitionCount += 1;
                               continue;
                           }
                           List<Goods> goodsByName = goodsService.findByPlaceIdAndGoodsNameAndGoodsCategory(placeId, productName, 0);
                           Goods goods = null;
                           if(goodsByName.size() != 0){
                               goods = goodsByName.get(0);
                               goods.setUnitPrice(actualAmount);
                               goods.setUpdated(LocalDateTime.now());
                               goodsService.save(goods);
                           }else{
                               goods = new Goods();
                               //新增一个商品
                               goods.setPlaceId(placeId);
                               goods.setGoodsName(productName);
                               goods.setGoodsTypeId(goodsTypeId);
                               goods.setGoodsTypeName(goodsTypeName);
                               goods.setUnitPrice(actualAmount);
                               goods.setUnit(16);//单位
                               goods.setGoodsCategory(0);//固装商品
                               goods.setSellStatus(0);//停售
                               goods.setCycle(0);//售卖周期默认每日
                               goods.setStartTime1(0);
                               goods.setEndTime1(24);
                               goods.setShowMobileSwitch(1);
                               goods.setShowClientSwitch(1);
                               goods.setShowCashierSwitch(1);
                               goods.setOnlinePaySwitch(1);
                               goods.setSupportPresentSwitch(1);
                               goods.setSupportCashSwitch(1);
                               goods.setSellStatus(0); //停售
                               goods.setAreaIds(areaIds);
                               goods.setGoodsPic("http://assets.topfreeweb.net/southwest/shop/douyin.png");
                               goods.setCardTypeIds(cardTypeIds);

                               goods.setShowRank(1);
                               goods.setIsCalculateInventory(1);
                               goods.setCreated(LocalDateTime.now());
                               goods.setCreater(1l);
                               goods.setWeeks("1,2,3,4,5,6,7");
                               goods.setDays("1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31");
                               goods.setStartDate(LocalDate.of(2024,12,01));
                               goods.setEndDate(LocalDate.of(2046,12,01));
                               goods.setEndTime2(24);
                               goods.setEndTime3(24);
                               goodsService.save(goods);
                           }
                           BuyGifts buyGifts = new BuyGifts();
                           buyGifts.setPlaceId(placeId);
                           buyGifts.setBuyGiftsName(productName);
                           buyGifts.setSchemeRemarks("系统自动匹配第三方团购套餐");
                           buyGifts.setWeeks("1,2,3,4,5,6,7");
                           buyGifts.setDays("1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31");
                           buyGifts.setStartDate(LocalDate.of(2024,12,01));
                           buyGifts.setEndDate(LocalDate.of(2046,12,01));
                           buyGifts.setHideStartDate(LocalDate.of(2024,12,01));
                           buyGifts.setHideEndDate(LocalDate.of(2046,12,01));
                           buyGifts.setStartTime(0);
                           buyGifts.setEndTime(23.98f);
                           buyGifts.setBalancePay(0);
                           buyGifts.setEventGoodsType(0);
                           buyGifts.setEventGoods(goods.getGoodsId());
                           buyGifts.setCardTypeIds(cardTypeIds);
                           buyGifts.setStatus(0);
                           buyGifts.setMarketingPlatform("DOUYIN");
                           buyGifts.setPlatformId(product.get("product_id") + "");
                           buyGifts.setCreated(LocalDateTime.now());
                           buyGifts.setCreater(1l);
                           buyGiftsService.save(buyGifts);
                           successCount +=1 ;
                       }catch (Exception e){
                           errorCount +=1;
                           log.info("同步第三方买赠数据异常:{}",e.getMessage());
                           e.printStackTrace();
                       }
                    }

                    Map<String,Integer> result = new HashMap<>();
                    result.put("total",total);
                    result.put("successCount",successCount);
                    result.put("errorCount",errorCount);
                    result.put("repetitionCount",repetitionCount);

                    return new GenericResponse<>(new SimpleObjDTO(result));
                }else{
                    return new GenericResponse<>(ServiceCodes.NOT_FOUND);
                }
            } else {
                return new GenericResponse<>(String.valueOf(data.get("description"))); //具体异常描述
            }
        }else{
            return new GenericResponse<>(ServiceCodes.OPT_ERROR);
        }
    }

    /**
     * 保存抖音商户门店信息
     * MarketDouyinStoreBO   前端根据poiId和placeId查询出来的数据
     * 说明：
     * 前端选择确定后，点击保存按钮，后端根据placeId+poiId去market_douyin_store表查询，如果有值的话，报错提示返回前端“商户已绑定第三方账户！请取消绑定后再重新绑定”；如果没有值则新增数据。
     */
    public GenericResponse<SimpleDTO> saveMarketDouyinStore(MarketDouyinStoreBO marketDouyinStoreBO) {
        if((StringUtils.isEmpty(marketDouyinStoreBO.getPoiId()) && StringUtils.isEmpty(marketDouyinStoreBO.getAccountId())) || StringUtils.isEmpty(marketDouyinStoreBO.getPlaceId())){
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        // 保存前先根据placeId和poiId查询marketDouyinStore是否存在，如果存在应该报错提示前端
        Optional<MarketDouyinStore> byPlaceId = marketDouyinStoreRepository.findByPoiId(marketDouyinStoreBO.getPoiId());
        if(byPlaceId.isPresent()){
            return new GenericResponse<>(ServiceCodes.MARKET_STORE_IS_BIND);
        }
        LocalDateTime now = LocalDateTime.now();
        MarketDouyinStore marketDouyinStore = new MarketDouyinStore();
        marketDouyinStore.setCreated(now);
        marketDouyinStore.setPlaceId(marketDouyinStoreBO.getPlaceId());
        marketDouyinStore.setPoiId(marketDouyinStoreBO.getPoiId());
        marketDouyinStore.setPoiName(marketDouyinStoreBO.getPoiName());
        marketDouyinStore.setAddress(marketDouyinStoreBO.getAddress());
        marketDouyinStore.setLongitude(marketDouyinStoreBO.getLongitude());
        marketDouyinStore.setLatitude(marketDouyinStoreBO.getLatitude());
        marketDouyinStore.setAccountId(marketDouyinStoreBO.getAccountId());
        marketDouyinStore.setAccountName(marketDouyinStoreBO.getAccountName());
        marketDouyinStore.setRootAccountId(marketDouyinStoreBO.getRootAccountId());
        marketDouyinStore.setRootAccountName(marketDouyinStoreBO.getRootAccountName());
        marketDouyinStore.setStatus(1);
        marketDouyinStore.setCreater(marketDouyinStoreBO.getCreater());
        marketDouyinStoreRepository.save(marketDouyinStore);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }


    public Optional<MarketDouyinStore> findByPlaceId(String placeId){
        return marketDouyinStoreRepository.findByPlaceId(placeId);
    }

    public MarketDouyinStore save(MarketDouyinStore marketDouyinStore){
        return marketDouyinStoreRepository.save(marketDouyinStore);
    }
}
