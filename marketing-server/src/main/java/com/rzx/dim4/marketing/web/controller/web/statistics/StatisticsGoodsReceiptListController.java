package com.rzx.dim4.marketing.web.controller.web.statistics;

import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.ExcelUtil;
import com.rzx.dim4.base.vo.marketing.StatisticsGoodsReceiptListExportVO;
import com.rzx.dim4.marketing.service.statistics.StatisticsGoodsReceiptListService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 采购成本分析
 */
@RestController
@RequestMapping("/marketing/admin/statistics/goodsReceiptList")
public class StatisticsGoodsReceiptListController {

    @Autowired
    protected StatisticsGoodsReceiptListService statisticsGoodsReceiptListService;

    /**
     * 查询
     * @param request
     * @param placeId
     * @param size
     * @param page
     * @param goodType 类型
     * @param startDate
     * @param endDate
     * @param supplierName 供应商名称
     * @return
     */
    @GetMapping("/query")
    public GenericResponse<PagerDTO<?>> query(HttpServletRequest request,
                                              @RequestParam String placeId,
                                              @RequestParam(name = "size", defaultValue = "10") int size,
                                              @RequestParam(name = "page", defaultValue = "0") int page,
                                              @RequestParam(required = false, defaultValue = "") String goodType,
                                              @RequestParam String startDate,
                                              @RequestParam String endDate,
                                              @RequestParam(required = false, defaultValue = "") String supplierName) {
        if (StringUtils.isBlank(placeId) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        PageRequest pageable = PageRequest.of(page, size);
        return statisticsGoodsReceiptListService.query(placeId, goodType, startDate, endDate, supplierName, pageable);
    }

    /**
     * 导出
     *
     * @param request
     * @param placeId
     * @param size
     * @param page
     * @param goodType 类型
     * @param startDate
     * @param endDate
     * @param supplierName 供应商名称
     */
    @GetMapping("/export")
    public void export(HttpServletRequest request,
                       HttpServletResponse response,
                       @RequestParam String placeId,
                       @RequestParam(name = "size", defaultValue = "10") int size,
                       @RequestParam(name = "page", defaultValue = "0") int page,
                       @RequestParam(required = false, defaultValue = "") String goodType,
                       @RequestParam String startDate,
                       @RequestParam String endDate,
                       @RequestParam(required = false, defaultValue = "") String supplierName) {
        if (StringUtils.isBlank(placeId) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        PageRequest pageable = PageRequest.of(page, size);
        List<StatisticsGoodsReceiptListExportVO> vos = statisticsGoodsReceiptListService.export(placeId, goodType, startDate, endDate, supplierName, pageable);
        try {
            ExcelUtil.writeExcel(response, vos, "采购成本分析", "采购成本分析", StatisticsGoodsReceiptListExportVO.class);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
