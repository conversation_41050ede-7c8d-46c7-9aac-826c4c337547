package com.rzx.dim4.marketing.service.shop;

import com.alibaba.excel.util.CollectionUtils;
import com.rzx.dim4.base.bo.marketing.GoodsPurchaseOrderBO;
import com.rzx.dim4.base.bo.marketing.GoodsPurchaseOrderListBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.marketing.entity.Goods;
import com.rzx.dim4.marketing.entity.GoodsPurchaseOrder;
import com.rzx.dim4.marketing.entity.GoodsPurchaseOrderList;
import com.rzx.dim4.marketing.repository.GoodsPurchaseOrderListRepository;
import com.rzx.dim4.marketing.repository.GoodsPurchaseOrderRepository;
import com.rzx.dim4.marketing.util.market.OrderNumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024年12月08日 16:05
 * 采购订单
 */
@Service
@Slf4j
public class GoodsPurchaseOrderService {

    @Autowired
    private GoodsPurchaseOrderRepository   goodsPurchaseOrderRepository;

    @Autowired
    private GoodsPurchaseOrderListRepository goodsPurchaseOrderListRepository;

    @Autowired
    private GoodsService goodsService;


    @Transactional
    public GoodsPurchaseOrder save(GoodsPurchaseOrderBO goodsPurchaseOrderBO) {
        String orderNum =  OrderNumUtil.getOrderNum();
        if (StringUtils.isEmpty(goodsPurchaseOrderBO.getPurchaseOrderNum())) {
            goodsPurchaseOrderBO.setPurchaseOrderNum(orderNum);
        } else {
            goodsPurchaseOrderBO.setUpdated(LocalDateTime.now());
        }

        // 计算采购商品种类数目
        int goodsKindTotal = 0;
        // 计算采购商品种类数目
        int goodsTotal = 0;
        // 计算采购金额
        int purchasePrice = 0;


        if (!CollectionUtils.isEmpty(goodsPurchaseOrderBO.getGoodsPurchaseOrderListBOS()))  {
            goodsKindTotal =  goodsPurchaseOrderBO.getGoodsPurchaseOrderListBOS().size();
        } else {
            throw new ServiceException(ServiceCodes.MARKET_NO_CHOOSE_GOODS);
        }
        GoodsPurchaseOrder goodsPurchaseOrder = new GoodsPurchaseOrder();
        BeanUtils.copyProperties(goodsPurchaseOrderBO,goodsPurchaseOrder);
        goodsPurchaseOrder.setGoodsKindTotal(goodsKindTotal);
        goodsPurchaseOrder.setStatus(0);
        goodsPurchaseOrder.setCreated(LocalDateTime.now());
        // 保存采购订单商品列表
        for (GoodsPurchaseOrderListBO goodsPurchaseOrderListBO : goodsPurchaseOrderBO.getGoodsPurchaseOrderListBOS()) {
            Optional<Goods> goodsCodeObj = goodsService.findByPlaceIdAndGoodsIdAndDeleted(goodsPurchaseOrderListBO.getPlaceId(), goodsPurchaseOrderListBO.getGoodsId());
            if(!goodsCodeObj.isPresent()) {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_NOT_EXIST);
            }
            goodsTotal += goodsPurchaseOrderListBO.getPurchaseNumber();
            purchasePrice += (goodsPurchaseOrderListBO.getPurchasePrice() * goodsPurchaseOrderListBO.getPurchaseNumber());
            GoodsPurchaseOrderList goodsPurchaseOrderList = new GoodsPurchaseOrderList();
            BeanUtils.copyProperties(goodsPurchaseOrderListBO,goodsPurchaseOrderList);
            goodsPurchaseOrderList.setPurchaseOrderNum(orderNum);
            goodsPurchaseOrderList.setCreated(LocalDateTime.now());
            goodsPurchaseOrderListRepository.save(goodsPurchaseOrderList);
        }
        goodsPurchaseOrder.setPurchaseAmount(purchasePrice);
        goodsPurchaseOrder.setGoodsTotal(goodsTotal);
        goodsPurchaseOrderRepository.save(goodsPurchaseOrder);
        return goodsPurchaseOrder;
    }

    public Optional<GoodsPurchaseOrder> findByPlaceIdAndPurchaseOrderNum(String placeId, String purchaseOrderNum) {
        return goodsPurchaseOrderRepository.findByPlaceIdAndPurchaseOrderNumAndDeleted(placeId, purchaseOrderNum, 0);
    }

    public Page<GoodsPurchaseOrder> findAll(Map<String, Object> map, Pageable pageable) {
        return goodsPurchaseOrderRepository.findAll(new Specification<GoodsPurchaseOrder>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<GoodsPurchaseOrder> root, CriteriaQuery<?> query,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = criteriaBuilder.equal(root.get("deleted"), 0);
                predicateList.add(p1);
                // 场所ID
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("placeId").as(String.class), map.get("placeId"));
                    predicateList.add(predicate);
                }

                //采购订单状态
                if (map.containsKey("status") && !StringUtils.isEmpty(map.get("status"))) {// 类型名称
                    predicateList.add(criteriaBuilder.equal(root.get("status").as(String.class),map.get("status")));
                }

                // 开始时间
                if (map.containsKey("startDate") && !org.springframework.util.StringUtils.isEmpty(map.get("startDate"))) {
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(map.get("startDate")), fmt);
                    predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                // 结束时间
                if (map.containsKey("endDate") && !org.springframework.util.StringUtils.isEmpty(map.get("endDate"))) {
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(map.get("endDate")), fmt);
                    predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }


                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicateArr));
            }
        }, pageable);
    }

    public List<GoodsPurchaseOrder> findAllByPlaceIdAndSupplierIdIn(String placeId, List<String> supplierIds) {
        return goodsPurchaseOrderRepository.findAllByPlaceIdAndSupplierIdInAndDeleted(placeId, supplierIds,0);
    }

    // 逻辑删除记录
    @Transactional
    public void logicdeleteByPlaceId(String placeId) {
        goodsPurchaseOrderRepository.logicDeleteByPlaceId(placeId);
    }


}
