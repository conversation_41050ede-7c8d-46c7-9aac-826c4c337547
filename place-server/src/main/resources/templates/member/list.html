<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<!-- begin::Head -->
<head>
	<meta charset="utf-8" />
	<#include "/base/titleHeader.html">
	<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
	<link href="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
	<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
<!-- begin:: Page -->

<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->
<div class="kt-grid kt-grid--hor kt-grid--root">
	<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

			<!-- begin:: Header -->
			<#include "/base/header.html">
			<!-- end:: Header -->

			<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
				<div class="kt-container  kt-container--fluid ">

					<!-- begin:: Aside -->
					<#include "/base/aside.html">
					<!-- end:: Aside -->

					<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

						<!-- begin:: Subheader -->
						<div class="kt-subheader   kt-grid__item" id="kt_subheader">
							<div class="kt-container  kt-container--fluid ">
								<div class="kt-subheader__main">
									<h3 class="kt-subheader__title">会员管理</h3>
									<span class="kt-subheader__separator kt-subheader__separator--v"></span>
									<div class="kt-subheader__group" id="kt_subheader_search">
										<span class="kt-subheader__desc" id="kt_subheader_total">会员列表</span>
									</div>
								</div>
								<div class="kt-subheader__toolbar">
									<a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
								</div>
							</div>
						</div>
						<!-- end:: Subheader -->

						<!-- begin:: Content -->
						<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
							<div class="kt-portlet kt-portlet--mobile">
<!--								<div class="kt-portlet__head kt-portlet__head&#45;&#45;lg">-->
<!--                                    <div class="kt-portlet__head-label">-->

<!--                                    </div>-->
<!--                                    <div class="kt-portlet__head-toolbar">-->
<!--                                        <div class="kt-portlet__head-wrapper">-->
<!--                                            <div class="kt-portlet__head-actions">-->
<!--                                                <button id="importMember" class="btn btn-brand btn-elevate btn-icon-sm">-->
<!--                                                    <i class="la la-plus"></i> 导入用户-->
<!--                                                </button>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
								
								<div class="kt-portlet__body">
									<form id="memberForm">
										<div class="row kt-margin-b-20">

											<div class="ol-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;margin-left: 0px;">
												<select class="form-control kt-input" data-col-index="2" id="cardTypeSelect" style="width: auto">
												</select>
											</div>

											<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-left: 3%;margin-bottom: 0px">
												<input id="search" type="text" class="form-control" placeholder="请输入卡号查询">
											</div>

											<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-left: 3%;margin-bottom: 0px">
												<input id="idName" type="text" class="form-control" placeholder="请输入姓名查询">
											</div>
											<!--                        <div class="ol-lg-1 col-md-1 col-sm-1">-->
											<!--                        </div>-->

											<div class="ol-lg-1 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile" style="margin-left: 2%;margin-bottom: 0px">
												<button type="button" class="btn btn-success btn-wide" id="searchSubmit">查询</button>
											</div>

											<div class="ol-lg-1 col-md-1 col-sm-1">

											</div>

											<div class="ol-lg-2 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile" style="margin-left: 2%;margin-bottom: 0px">
												<button type="button" class="btn btn-success btn-wide" id="changeCardType">更改卡类型</button>
											</div>

											<input type="hidden" id="placeId" value="<#if (PLACE_SERVER_ACCOUNT)??>${PLACE_SERVER_ACCOUNT.placeId}<#else>0</#if>" >
										</div>
									</form>

									<!--begin: Search Form -->
<!--									<form class="kt-form kt-form&#45;&#45;fit kt-margin-b-20" id="cardTypeForm">-->
<!--										<div class="row kt-margin-b-20" hidden>-->
<!--											<div class="col-xs-2 col-lg-2 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile">-->
<!--												<label>门店名称:</label>-->
<!--												<input type="hidden" id="placeId" value="<#if (PLACE_SESSION)??>${PLACE_SESSION}<#else>0</#if>" >-->
<!--												<select class="form-control kt-input" data-col-index="2" id="placeSelect" name="placeSelect">-->

<!--												</select>-->
<!--											</div>-->
<!--										</div>-->

<!--									</form>-->
									<!--begin: Datatable -->
									<!--begin: Datatable -->
									<table class="table table-striped table-bordered table-hover table-checkable" id="kt_table_1">
										<thead>
										<tr bgcolor="#005699">
											<th style="color: #FFFFFF"><input class="checkAllchild" type="checkbox" id="checkAllBox"/></th>
											<th style="color: #FFFFFF">会员卡号</th>
											<th style="color: #FFFFFF">姓名</th>
											<th style="color: #FFFFFF">会员类型</th>
<!--											<th style="color: #FFFFFF">网费余额</th>-->
											<th style="color: #FFFFFF">开卡时间</th>
											<th style="color: #FFFFFF">上次结账时间</th>
											<th style="color: #FFFFFF">查看详情</th>
										</tr>
										</thead>
									</table>
									<!--end: Datatable -->
								</div>
							</div>
						</div>
						<!-- end:: Content -->
					</div>
				</div>
			</div>
			<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
		</div>
	</div>
</div>

<div class="modal fade cashier-model" id="kt_modal_1" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-xl" role="document">
		<div class="modal-content" id="one">
			<div class="modal-header" style="padding-top: 0px;padding-bottom: 0px;">
				<label class="col-form-label" style="font-size: 1.5rem;"><b>会员详情:</b></label>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body" style="padding-top: 0px;">
				<div class="form-group" style="margin-bottom: 5px;">
					<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
						<table class="table table-striped table-bordered table-hover table-checkable" id="cardInfoTable" style="margin-bottom: 0px;">
							<tr style="text-align:center">
								<th rowspan="2" style="vertical-align: middle;color: #000000"><b>账户信息</b></th>
								<th style="color: #000000"><b>会员卡号</b></th>
								<th style="color: #000000"><b>姓名</b></th>
								<th style="color: #000000"><b>会员类型</b></th>
								<th style="color: #000000"><b>可用余额</b></th>
								<th style="color: #000000"><b>本金</b></th>
								<th style="color: #000000"><b>奖励</b></th>
								<th style="color: #000000"><b>开卡时间</b></th>
								<th style="color: #000000"><b>经办人</b></th>
								<th style="color: #000000"><b>上次结账时间</b></th>
								<th style="color: #000000"><b>会员状态</b></th>
							</tr>
							<tr style="text-align:center;">
								<th id="idNumber" style="font-weight: 500"></th>
								<th id="cardId" style="font-weight: 500" hidden></th>
								<th id="name" style="font-weight: 500"></th>
								<th id="cardTypeName" style="font-weight: 500"></th>
								<th id="totalAccount" style="font-weight: 500"></th>
								<th id="cashAccount" style="font-weight: 500"></th>
								<th id="presentAccount" style="font-weight: 500"></th>
								<th id="created" style="font-weight: 500"></th>
								<th id="createName" style="font-weight: 500"></th>
								<th id="lastLogOutTime" style="font-weight: 500"></th>
								<th id="status" style="font-weight: 500"></th>
							</tr>
						</table>
					</div>
				</div>
				<div class="form-group" style="margin-bottom: 20px;">
					<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
						<table class="table table-striped table-bordered table-hover table-checkable" id="realnameInfoTable">
							<tr align="center">
								<th rowspan="2" style="vertical-align: middle;color: #000000"><b>实名信息</b></th>
								<th style="color: #000000;"><b>性别</b></th>
								<th  style="color: #000000"><b>民族</b></th>
								<th style="color: #000000"><b>手机号码</b></th>
								<th style="color: #000000"><b>开卡证件</b></th>
								<th  style="color: #000000"><b>证件号码</b></th>
								<th style="color: #000000"><b>发证机关</b></th>
								<th style="color: #000000"><b>证件有效期</b></th>
								<th style="color: #000000"><b>证件地址</b></th>
							</tr>
							<tr style="text-align:center;">
								<th id="sex" style="font-weight: 500"></th>
								<th id="nation" style="font-weight: 500"></th>
								<th id="phoneNumber" style="font-weight: 500"></th>
								<th id="idNumberType" style="font-weight: 500"></th>
								<th id="realname-idNumber" style="font-weight: 500"></th>
								<th id="issuingAuthority" style="font-weight: 500"></th>
								<th id="validPeriod" style="font-weight: 500"></th>
								<th id="address" style="font-weight: 500"></th>
							</tr>
						</table>
					</div>
				</div>
				<div class="form-group">
					<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 form-group row" style="margin-bottom: 0px;">
							<label class="col-form-label" style="font-size: 1.5rem;padding-bottom: 0px;"><b>消费明细:</b></label>
							<div class="ol-lg-6 col-md-6 col-sm-6 form-group row">
								<div class="col-xl-3 col-lg-3 col-md-3 col-sm-3 form-group row" style="margin-bottom: 0px;margin-left: 20px;margin-top: 5px;">
									<label class="col-form-label" style="font-size: 1rem;" id="sumTopup"></label>
								</div>

								<div class="col-xl-3 col-lg-3 col-md-3 col-sm-3 form-group row" style="margin-bottom: 0px;margin-top: 5px;">
									<label class="col-form-label" style="font-size: 1rem;" id="sumConsumption"></label>
								</div>

								<div class="col-xl-3 col-lg-3 col-md-3 col-sm-3 form-group row" style="margin-bottom: 0px;margin-top: 5px;">
									<label class="col-form-label" style="font-size: 1rem;" id="sumReversal"></label>
								</div>
							</div>

							<div class="col-xl-3 col-lg-3 col-md-3 col-sm-3 form-group row" style="margin-bottom: 0px;">
								<label for="kt_daterangepicker_1" style="margin-top: 10px;"><b>时间：</b></label>
								<input type='text' class="form-control" id="kt_daterangepicker_1" readonly placeholder="选择时间段" style="width: auto"/>
							</div>

							<select class="col-xl-1 col-lg-1 col-md-1 col-sm-1  form-control kt-input" id="operationType"  style="width: auto">
							</select>

							<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1" style="margin-left: 3%">
								<button type="button" class="btn btn-success btn-wide" style="width: 110.5px;" id="onlineRecordSearchSubmit">查询</button>
							</div>
						</div>

						<table class="table table-striped table-bordered table-hover table-checkable" id="consumptionInfoTable" align="center">
							<thead>
							<tr bgcolor="#005699">
								<th style="color: #FFFFFF">时间</th>
								<th style="color: #FFFFFF">事件类型</th>
								<th style="color: #FFFFFF">金额</th>
								<th style="color: #FFFFFF">详情</th>
								<th style="color: #FFFFFF">当前余额</th>
								<th style="color: #FFFFFF">操作人</th>
								<th style="color: #FFFFFF">区域-终端</th>
								<th style="color: #FFFFFF">来源</th>
								<th style="color: #FFFFFF">备注</th>
							</tr>
							</thead>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="modal-dialog modal-xxl modal-dialog-centered " id="two" hidden>
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" id="myGetMemberModalLabel" style="margin: auto">
						请完成验证码
					</h4>
				</div>
				<div class="modal-body">
					<form id="getMemberVerificationCodeForm">
						<div class="form-group row">
							<div class="col-xl-5 col-lg-5 col-md-5 col-sm-5" style="padding-left: 0px;">
								<input class="form-control" type="text" placeholder="输入图片中的字符" name="verifyCode"
									   autocomplete="off"
									   style="background: white;width:230px;margin-top: 8%;height: 80%;margin-left: 10%">
							</div>
							<div class="col-xl-5 col-lg-5 col-md-5 col-sm-5" style="padding-left: 0px;" ;>
								<img alt="单击图片刷新！" class="pointer" id="get_security_kaptcha"
									 onclick="this.src='/place-server/common/interface/kaptcha?d='+new Date()*1"
									 style="margin-top: 16px;padding-left: 6px;width: 90%;margin-left: 30%">
							</div>
							<span class="kt-subheader__desc" id="update_kaptcha1"
								  style=" margin-top: 1%;margin-left: 60%">看不清？换一张</span>
						</div>
					</form>

				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭
					</button>
					<button type="button" name="getMemberVerificationSubmit" id="getMemberVerificationSubmit"
							class="btn btn-primary">
						提交
					</button>
				</div>
			</div>
		</div><!-- /.modal-content -->
	</div>
</div>

<div class="modal fade cashier-model" id="kt_modal_2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header" style="padding-top: 0px;padding-bottom: 0px;">
				<label class="col-form-label" style="font-size: 1.5rem;"><b>更改卡类型:</b></label>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body" style="padding-top: 0px;">
				<div class="form-group" style="margin-bottom: 5px;">
					<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 form-group row" style="margin-bottom: 0px;margin-top: 18px;">
						<div class="ol-lg-5 col-md-5 col-sm-7" style="margin: auto">
							<select class="form-control kt-input" data-col-index="2" id="modalCardTypeSelect">
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" id="cancel">取消</button>
				<button type="button" class="btn btn-primary" data-dismiss="modal" id="doChange">确定</button>
			</div>
		</div>
	</div>
</div>

<!--验证码安全验证功能弹框-->
<div class="modal fade" id="verificationCodeModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true" >
	<div class="modal-dialog modal-xxl modal-dialog-centered" >
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title" id="myModalLabel" style="margin: auto">
					请完成验证码
				</h4>
			</div>
			<div class="modal-body">
				<form id="verificationCodeForm">
					<div class="form-group row">
						<div class="col-xl-5 col-lg-5 col-md-5 col-sm-5" style="padding-left: 0px;">
							<input class="form-control" type="text" placeholder="输入图片中的字符" name="verifyCode"
								   autocomplete="off"
								   style="background: white;width:230px;margin-top: 8%;height: 80%;margin-left: 10%">
						</div>
						<div class="col-xl-5 col-lg-5 col-md-5 col-sm-5" style="padding-left: 0px;" ;>
							<img alt="单击图片刷新！" class="pointer" id="security_kaptcha"
								 onclick="this.src='/place-server/common/interface/kaptcha?d='+new Date()*1"
								 style="margin-top: 16px;padding-left: 6px;width: 90%;margin-left: 30%">
						</div>
						<span class="kt-subheader__desc" id="update_kaptcha2" style=" margin-top: 1%;margin-left: 60%">看不清？换一张</span>
					</div>
				</form>

			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭
				</button>
				<button type="button" name="verificationSubmit" id="verificationSubmit" class="btn btn-primary">
					提交
				</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal -->
</div>
<#include "/base/script.html">

<!--begin::Page Vendors(used by this page) -->

<script src="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.js" type="text/javascript"></script>
<script src="/place-server/assets/js/pages/member/list.js" type="text/javascript"></script>
<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
