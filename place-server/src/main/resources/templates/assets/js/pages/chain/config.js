"use strict";
var regionCode = "";
var KTDatatablesDataSourceAjaxServer = function () {

    var showErrorMsg = function (form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">' + msg + '</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

    let setColumnSearchField = function (param, fieldName, fieldValue) {
        let arrLength = param.columns.length;
        for (let i = 0; i < arrLength; i++) {
            if (fieldName != null && param.columns[i].data == fieldName) {
                param.columns[i].search.value = fieldValue;
                break;
            }
        }
    };


    let $deleteChainCardTypeModal = $("#deleteChainCardTypeModal")
    let initModal = function () {
        $deleteChainCardTypeModal.on('show.bs.modal', function (event) {
            let button = $(event.relatedTarget);

            let typename = button.data('typename');
            let id = button.data('id');
            let cardtypeid = button.data('cardtypeid');

            $("#deletedId").data("id", id)
            $("#deletedId").data("cardtypeid", cardtypeid)
            $("#deletedId").data("typename", typename)
            let model = $(this);

            model.find('.modal-body').text("确定删除卡类型 名称 为 " + typename + " 的数据吗？");
        });
    }

    let $deleteSubmit = $("#deleteSubmit")
    let chainCardTypeDeleteSubmit = function () {
        // $deleteChainCardTypeArr click event
        $deleteSubmit.click(function (e) {
            e.preventDefault()
            console.log("deleteChainCardType click")
            // get chainId and id
            let id = $("#deletedId").data("id")
            let cardTypeId = $("#deletedId").data("cardtypeid")
            let typename = $("#deletedId").data("typename")

            console.log("chainId: " + currentChainIdValue)
            console.log("id: " + id)

            // ajax post to delete chain card type
            $.ajax({
                url: "/place-server/chain/cardType/delete?chainId=" + currentChainIdValue + "&cardTypeId=" + cardTypeId,
                type: "POST",
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    console.log("delete chain card type success")
                    console.log(JSON.stringify(data))

                    if (data.result) {
                        swal.fire({
                            "title": "删除成功",
                            "type": "success",
                            "confirmButtonClass": "btn btn-secondary"
                        })

                        $deleteChainCardTypeModal.modal("hide")
                        // reload chain card type table
                        $chainCardType.DataTable().ajax.reload()
                    } else {
                        let msg = ''
                        if (data.code === 830102) {
                            msg = "卡类型删除失败！" + typename + "卡类型下还绑定有会员，请先将会员转移再重试"
                        } else {
                            msg = data.message
                        }
                        swal.fire({
                            "title": msg,
                            "type": "error",
                            "confirmButtonClass": "btn btn-secondary"
                        })
                    }

                },
                error: function (data) {
                    console.log("delete chain card type error")
                    console.log(JSON.stringify(data))
                    swal.fire({
                        "title": data.message,
                        "type": "error",
                        "confirmButtonClass": "btn btn-secondary"
                    })
                }
            })
        })
    }

    // input element name = "typeName"
    let $typeName = $("#typeName")
    let $createSubmit = $("#createSubmit")
    let $createChainCardTypeModal = $("#createChainCardType")
    let $minCreateCardAmountCreate = $("#minCreateCardAmountCreate")
    let $minPointsRequirementCreate = $("#minPointsRequirementCreate")


    // 编辑按钮功能
    let $editSubmit = $("#editSubmit") //编辑按钮
    let $editChainCardTypeModal = $("#editChainCardType")// modal框

    let chainCardTypeCreateSubmit = function () {
        var form = $('#cardTypeAddForm');
        // $createChainCardTypeModal modal show evnet
        $createChainCardTypeModal.on('show.bs.modal', function (event) {
            console.log("createChainCardTypeModal show.bs.modal")
            $typeName.val("")
            form.validate({
                // define validation rules
                rules: {
                    minCreateCardAmountCreate: {
                        required: true,
                        digits: true,
                        min: 0,
                        max: 9999
                    },
                    minPointsRequirementCreate: {
                        required: true,
                        digits: true,
                        min: 0,
                        max: 99999999
                    }
                },
            });
            // 初始化值0
            $('#minCreateCardAmountCreate').val(0);
            $('#minPointsRequirementCreate').val(0);
        })

        /// hide
        $createChainCardTypeModal.on('hide.bs.modal', function (event) {
            console.log("createChainCardTypeModal hide.bs.modal")
            $typeName.val("")
            form.validate().resetForm();
        })


        // $createSubmit click event
        $createSubmit.click(function () {
            console.log("createSubmit click")

            // if $typeName val is empty, swal
            let typeNameVal = $typeName.val()
            console.log("typeNameVal: " + typeNameVal)
            let minCreateCardAmountCreateVal = $minCreateCardAmountCreate.val()
            let minPointsRequirementCreateVal = $minPointsRequirementCreate.val()

            // 表单校验过了才允许往下走
            if (!form.valid()) {
                return;
            }
            if (typeNameVal === "") {
                swal.fire({
                    "title": "类型名称不能为空",
                    "type": "error",
                    "confirmButtonClass": "btn btn-secondary"
                })
                return
            }

            let placeChainCardTypeBO = {}
            placeChainCardTypeBO.chainId = currentChainIdValue
            placeChainCardTypeBO.typeName = typeNameVal
            placeChainCardTypeBO.minPointsRequirement = minPointsRequirementCreateVal
            placeChainCardTypeBO.minCreateCardAmount = minCreateCardAmountCreateVal

            // ajax post to create chain card type
            $.ajax({
                url: "/place-server/chain/cardType/add",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(placeChainCardTypeBO),
                success: function (data) {
                    console.log("create chain card type success")
                    console.log(JSON.stringify(data))

                    if (data.result) {
                        Swal.fire({
                            title: "创建成功",
                            type: "success",
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            // hide the modal
                            // close the modal $createChainCardTypeModal
                            $createChainCardTypeModal.modal("hide")
                            // reload chain card type table
                            $chainCardType.DataTable().ajax.reload()
                        })
                    } else {
                        swal.fire({
                            "title": data.message,
                            "type": "error",
                            "confirmButtonClass": "btn btn-secondary"
                        })
                    }
                },
                error: function (data) {
                    console.log("create chain card type error")
                    console.log(JSON.stringify(data))
                }
            })

        })
    }

    // 初始化连锁卡类型编辑确定按钮事件
    // let $cardtypeid = $("#cardtypeid")
    let $typeId = $("#typeId")
    let $typeNameEdit = $("#typeNameEdit")
    let $minCreateCardAmount = $("#minCreateCardAmount")
    let $minPointsRequirement = $("#minPointsRequirement")

    let chainCardTypeEditSubmit = function () {
        var form = $('#cardTypeEditForm');

        $editChainCardTypeModal.on('show.bs.modal', function (event) {
            let button = $(event.relatedTarget);
            let cardTypeId = button.data('cardtypeid');
            let cardTypeName = button.data('typename');
            let minCreateCardAmount = button.data('mincreatecardamount');
            let minPointsRequirement = button.data('minpointsrequirement');

            // 加载编辑弹框的卡类型信息
            $typeId.val(cardTypeId)
            $typeNameEdit.val(cardTypeName)
            $minCreateCardAmount.val(minCreateCardAmount)
            $minPointsRequirement.val(minPointsRequirement)

            // var form = $('#cardTypeEditForm');
            form.validate({
                // define validation rules
                rules: {
                    minCreateCardAmount: {
                        required: true,
                        digits: true,
                        min: 0,
                        max: 9999
                    },
                    minPointsRequirement: {
                        required: true,
                        digits: true,
                        min: 0,
                        max: 99999999
                    }
                },
            });
        })

        /// hide
        $editChainCardTypeModal.on('hide.bs.modal', function (event) {
            console.log("editChainCardTypeModal hide.bs.modal")
            // $typeId.val("")
            // var form = $('#cardTypeEditForm');
            // form.validate().destroy();
            form.validate().resetForm();
        })


        // $createSubmit click event
        $editSubmit.click(function () {
            console.log("editSubmit click")
            // 表单校验过了才允许往下走
            if (!form.valid()) {
                return;
            }
            let typeNameEditVal = $typeNameEdit.val()
            let minCreateCardAmountVal = $minCreateCardAmount.val()
            let minPointsRequirementVal = $minPointsRequirement.val()

            let placeChainCardTypeBO = {}
            placeChainCardTypeBO.chainId = currentChainIdValue
            placeChainCardTypeBO.typeName = typeNameEditVal
            placeChainCardTypeBO.minPointsRequirement = minPointsRequirementVal
            placeChainCardTypeBO.minCreateCardAmount = minCreateCardAmountVal

            // ajax post to create chain card type
            $.ajax({
                url: "/place-server/chain/cardType/edit",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(placeChainCardTypeBO),
                success: function (data) {
                    console.log("create chain card type success")
                    console.log(JSON.stringify(data))

                    if (data.result) {
                        Swal.fire({
                            title: "编辑成功",
                            type: "success",
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            $editChainCardTypeModal.modal("hide")
                            $chainCardType.DataTable().ajax.reload()
                        })
                    } else {
                        swal.fire({
                            "title": data.message,
                            "type": "error",
                            "confirmButtonClass": "btn btn-secondary"
                        })
                    }
                },
                error: function (data) {
                    console.log("edit chain card type error")
                    console.log(JSON.stringify(data))
                }
            })

        })

    }

    let $chainPlaceAmountShareDT
    let initPlaceChainStoresFromServer = function () {
        // begin first table
        $chainPlaceAmountShareDT = $chainPlaceAmountShare.DataTable({
            language: {
                "url": "http://assets.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            searchDelay: 1000, // 搜索框change时的延迟
            ordering: false, // 字段排序
            paging: true,
            destroy: true,
            ajax: {
                url: '/place-server/chain/stores/page',
                type: 'POST',
                contentType: 'application/json; charset=utf-8',
                data: function (param) {

                    setColumnSearchField(param, "chainId", currentChainIdValue);
                    // setColumnSearchField(param, "chainName", selectChainNameVal);
                    // setColumnSearchField(param, "created", selectCreatedVal);

                    return JSON.stringify(param);
                }
            },
            columns: [
                {data: 'placeId'},
                {data: 'placeName'},
                {data: 'shareCashAccount'},
                {data: 'sharePresentAccount'},
                {data: 'shareMemberPoint'},

                {data: 'chainId'}
            ],
            columnDefs: [
                {
                    targets: '_all', // 全选
                    className: "text-center font-weight-normal", //居中
                },
                {
                    targets: 2,
                    title: '本金漫游',
                    render: function (data, type, full, meta) {
                        if (data === 1) {
                            return "<input type='checkbox' checked='checked' disabled='disabled'>"
                        } else {
                            return "<input type='checkbox'  disabled='disabled'>"
                        }
                    },
                },
                {
                    targets: 3,
                    title: "<input type='checkbox' id='sharePresentAccountColumn'>" + "&nbsp;&nbsp;奖励漫游",
                    render: function (data, type, full, meta) {
                        if (data === 1) {
                            return "<input type='checkbox' checked='checked' class='sharePresentAccountCell' data-placdid='"
                                + full.placeId + "' data-sharePresentAccount='" + full.sharePresentAccount + "'>"
                        } else {
                            return "<input type='checkbox' class='sharePresentAccountCell' data-placdid='"
                                + full.placeId + "' data-sharePresentAccount='" + full.sharePresentAccount + "'>"
                        }
                    },
                },
                {
                    targets: 4,
                    title: "<input type='checkbox'  id='shareMemberPointColumn' >" + "&nbsp;&nbsp;积分漫游",
                    render: function (data, type, full, meta) {
                        if (data === 1) {
                            return "<input type='checkbox' checked='checked' class='shareMemberPointCell' data-placdid='"
                                + full.placeId + "' data-shareMemberPoint='" + full.shareMemberPoint + "'>"
                        } else {
                            return "<input type='checkbox'  class='shareMemberPointCell' data-placdid='"
                                + full.placeId + "' data-shareMemberPoint='" + full.shareMemberPoint + "'>"
                        }
                    },
                },
                {
                    targets: [5],
                    visible: false,
                }
            ],
            initComplete: function (settings, json) {
                console.log("initComplete")

                let placeChainStoreBOList = []
                let placeChainStoreBOListOrigin = []

                // console.log("json: " + JSON.stringify(json))
                placeChainStoreBOList = json.data

                // console.log("placeChainStoreBOList: " + JSON.stringify(placeChainStoreBOList))
                placeChainStoreBOListOrigin = JSON.parse(JSON.stringify(placeChainStoreBOList))
                initCheckbox(placeChainStoreBOListOrigin, placeChainStoreBOList)
                // initCheckbox()
            },
            drawCallback: function (settings) {
                console.log("drawCallback")
                var api = this.api();
                let resJson = api.rows({ page: 'current' }).data().ajax.json()

                // Output the data for the visible rows to the browser's console
                // console.log(resJson);

                let placeChainStoreBOList = []
                let placeChainStoreBOListOrigin = []

                placeChainStoreBOList = resJson.data
                // placeChainStoreBOListOrigin copy placeChainStoreBOList
                placeChainStoreBOListOrigin = JSON.parse(JSON.stringify(placeChainStoreBOList))

                // console.log("placeChainStoreBOList: " + JSON.stringify(placeChainStoreBOList))
                // placeChainStoreBOListOrigin = JSON.parse(JSON.stringify(placeChainStoreBOList))
                //
                console.log("drawCallback Origin: placeId=" + placeChainStoreBOListOrigin[0].placeId,
                    ", sharePresentAccount=", placeChainStoreBOListOrigin[0].sharePresentAccount,
                    ", shareMemberPoint=", placeChainStoreBOListOrigin[0].shareMemberPoint)
                initCheckbox(placeChainStoreBOListOrigin, placeChainStoreBOList)
            }
        });

    }


    let doubleConfirm = function (placeChainStoreBOList) {
        Swal.fire({
            title: "您对共享漫游数据进行了修改，请问需要保存吗？",
            type: "info",
            showCancelButton: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(function (result) {
            if (!result.value) {
                console.log("cancel save chain amount share config")
                // reload page
                // window.location.reload()
            } else {
                console.log("confirm save chain amount share config")
                updateChainAmountShareConfig(placeChainStoreBOList)
            }
        })
    }

    let updateChainAmountShareConfig = function (placeChainStoreBOList) {

        // console.log("updateChainAmountShareConfig placeChainStoreBOList: " + JSON.stringify(placeChainStoreBOList))
        // return

        $.ajax({
            url: "/place-server/chain/stores/update",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(placeChainStoreBOList),
            success: function (data) {
                // console.log("create chain card type success")
                // console.log(JSON.stringify(data))

                if (data.result) {
                    swal.fire({
                        "title": "保存成功",
                        "type": "success",
                        "confirmButtonClass": "btn btn-secondary"
                    })
                    // reload chain card type table
                    $chainPlaceAmountShareDT.draw()
                } else {
                    swal.fire({
                        "title": data.message,
                        "type": "error",
                        "confirmButtonClass": "btn btn-secondary"
                    })
                }
            },
            error: function (data) {
                console.log("create chain card type error")
                console.log(JSON.stringify(data))
            }
        })

    }

    let initChainCardTypeFromServer = function () {
        // begin first table
        $chainCardType.DataTable({
            language: {
                "url": "http://assets.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            searchDelay: 1000, // 搜索框change时的延迟
            ordering: false, // 字段排序
            paging: true,
            destroy: true,
            ajax: {
                url: '/place-server/chain/cardType/page',
                type: 'POST',
                contentType: 'application/json; charset=utf-8',
                data: function (param) {

                    setColumnSearchField(param, "chainId", currentChainIdValue);
                    // setColumnSearchField(param, "chainName", selectChainNameVal);
                    // setColumnSearchField(param, "created", selectCreatedVal);

                    return JSON.stringify(param);
                },
            },
            columns: [
                {data: 'cardTypeId'},
                {data: 'typeName'},
                {data: 'minCreateCardAmount'},
                {data: 'minPointsRequirement'},
                {data: '操作'},
                {data: 'chainId'},
            ],
            columnDefs: [
                {
                    targets: '_all', // 全选
                    className: "text-center font-weight-normal", //居中
                },
                {
                    targets: [5],
                    visible: false,
                },
                {
                    targets: 4,
                    title: '操作',
                    render: function (data, type, full, meta) {
                        if (full.cardTypeId === "100000") {
                            // return ""
                            let editHtml = '<a href="" data-toggle="modal" id="aa" data-target="#editChainCardType"'
                                + ' data-chainid="' + full.chainId
                                + '" data-id="' + full.id
                                + '" data-cardtypeid="' + full.cardTypeId
                                + '" data-typename="' + full.typeName
                                + '" data-minpointsrequirement="' + full.minPointsRequirement
                                + '" data-mincreatecardamount="' + full.minCreateCardAmount
                                + '" class="kt-link kt-link--state kt-link--info editChainCardType">编辑</a><br/>'
                            return editHtml

                        } else {
                            let editHtml = '<a href="" data-toggle="modal" id="aa" data-target="#editChainCardType"'
                                + ' data-chainid="' + full.chainId
                                + '" data-id="' + full.id
                                + '" data-cardtypeid="' + full.cardTypeId
                                + '" data-typename="' + full.typeName
                                + '" data-minpointsrequirement="' + full.minPointsRequirement
                                + '" data-mincreatecardamount="' + full.minCreateCardAmount
                                + '" class="kt-link kt-link--state kt-link--info editChainCardType">编辑</a><br/>'

                            let deleteHtml = '<a href="" data-toggle="modal" data-target="#deleteChainCardTypeModal"'
                                + ' data-chainid="' + full.chainId
                                + '" data-id="' + full.id
                                + '" data-cardtypeid="' + full.cardTypeId
                                + '" data-typename="' + full.typeName
                                + '" data-cardtypeid="' + full.minPointsRequirement
                                + '" class="kt-link kt-link--state kt-link--info deleteChainCardType">删除</a><br/>'
                            return editHtml + deleteHtml
                        }
                    },
                },
            ],
        });
    };


    let initChainPlaceAmountShare = function () {
        console.log("initChainPlaceAmountShare")
        initPlaceChainStoresFromServer()
    }

    let initCheckbox = async function (placeChainStoreBOListOrigin, placeChainStoreBOList) {
        // let initCheckbox = async function () {
        console.log("initCheckbox")

        // 表头全选/全不选
        $chainPlaceAmountShare.on("click", "#sharePresentAccountColumn", function () {
            console.log("sharePresentAccountColumn click")
            let checked = $(this).prop("checked")

            let $allEle = $("input[class='sharePresentAccountCell']")
            if (checked) {
                $allEle.prop("checked", true)
                console.log("checked: " + checked)

                // foreach $allEle and  placeChainStoreBOList, when placeid is equal to data-placdid, set data-sharepresentaccount to 1
                $allEle.each(function (index, element) {
                    placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                        let $element = $(element)
                        let placeId = $element.data("placdid")
                        // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                        if (placeChainStoreBO.placeId == $element.data("placdid")) {
                            placeChainStoreBO.sharePresentAccount = 1
                        }
                    })
                })
            } else {
                $allEle.prop("checked", false)
                console.log("checked: " + checked)

                $allEle.each(function (index, element) {
                    placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                        let $element = $(element)
                        let placeId = $element.data("placdid")
                        // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                        if (placeChainStoreBO.placeId == $element.data("placdid")) {
                            placeChainStoreBO.sharePresentAccount = 0
                        }
                    })
                })
            }

            saveChainAmountShareConfig(placeChainStoreBOListOrigin, placeChainStoreBOList)
        })

        // 表头全选/全不选
        $chainPlaceAmountShare.on("click", "#shareMemberPointColumn", function () {
            console.log("shareMemberPointColumn click")
            // get sharePresentAccountColumn input element status checked or not
            let checked = $(this).prop("checked")

            let $allEle = $("input[class='shareMemberPointCell']")
            if (checked) {
                $allEle.prop("checked", true)
                console.log("checked: " + checked)

                $allEle.each(function (index, element) {
                    placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                        let $element = $(element)
                        let placeId = $element.data("placdid")
                        // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                        if (placeChainStoreBO.placeId == $element.data("placdid")) {
                            placeChainStoreBO.shareMemberPoint = 1
                        }
                    })
                })
            } else {
                $allEle.prop("checked", false)
                console.log("checked: " + checked)

                $allEle.each(function (index, element) {
                    placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                        let $element = $(element)
                        let placeId = $element.data("placdid")
                        // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                        if (placeChainStoreBO.placeId == $element.data("placdid")) {
                            placeChainStoreBO.shareMemberPoint = 0
                        }
                    })
                })
            }

            saveChainAmountShareConfig(placeChainStoreBOListOrigin, placeChainStoreBOList)
        })

        $chainPlaceAmountShare.on("click", ".sharePresentAccountCell", function () {
            console.log("sharePresentAccountCell click")
            // get sharePresentAccountColumn input element status checked or not
            let checked = $(this).prop("checked")
            console.log("checked: " + checked)

            let $htmlElement = $(this)

            placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                let $element = $htmlElement
                let placeId = $element.data("placdid")
                // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                console.log()
                if (placeChainStoreBO.placeId == $element.data("placdid")) {
                    if (checked) {
                        placeChainStoreBO.sharePresentAccount = 1
                    } else {
                        placeChainStoreBO.sharePresentAccount = 0
                    }
                }
            })

            saveChainAmountShareConfig(placeChainStoreBOListOrigin, placeChainStoreBOList)
        })

        $chainPlaceAmountShare.on("click", ".shareMemberPointCell", await async function () {
            console.log("shareMemberPointCell click")
            // get sharePresentAccountColumn input element status checked or not
            let checked = $(this).prop("checked")
            console.log("checked: " + checked)

            let $htmlElement = $(this)

            for (let i = 0; i < placeChainStoreBOList.length; i++) {
                let placeChainStoreBO = placeChainStoreBOList[i]
                let $element = $htmlElement
                let placeId = $element.data("placdid")
                // console.log("placeId: " + placeId + " placeChainStoreBO.placeId: " + placeChainStoreBO.placeId)
                if (placeChainStoreBOList[i].placeId == $element.data("placdid")) {
                    if (checked) {
                        placeChainStoreBOList[i].shareMemberPoint = 1
                    } else {
                        placeChainStoreBOList[i].shareMemberPoint = 0
                    }
                }
            }
            console.log("BO: placeId=" + placeChainStoreBOList[0].placeId,
                ", sharePresentAccount=", placeChainStoreBOList[0].sharePresentAccount,
                ", shareMemberPoint=", placeChainStoreBOList[0].shareMemberPoint)
            saveChainAmountShareConfig(placeChainStoreBOListOrigin, placeChainStoreBOList)
        })

        console.log("before saveChainAmountShareConfig, BO: placeId=" + placeChainStoreBOList[0].placeId,
            ", sharePresentAccount=", placeChainStoreBOList[0].sharePresentAccount,
            ", shareMemberPoint=", placeChainStoreBOList[0].shareMemberPoint)
        // saveChainAmountShareConfig(placeChainStoreBOListOrigin, placeChainStoreBOList)
    }

    let Temp

    let $saveChainAmountShareConfig = $("#saveChainAmountShareConfig")
    let saveChainAmountShareConfig = async function (placeChainStoreBOListOrigin, placeChainStoreBOList) {
        console.log("saveChainAmountShareConfig init")
        console.log("enter saveChainAmountShareConfig, BO: placeId=" + placeChainStoreBOList[0].placeId,
            ", sharePresentAccount=", placeChainStoreBOList[0].sharePresentAccount,
            ", shareMemberPoint=", placeChainStoreBOList[0].shareMemberPoint)

        Temp = JSON.parse(JSON.stringify(placeChainStoreBOList))

        // 重绘
        // $chainPlaceAmountShareDT.on("draw", await function () {
        //     console.log('重绘 Redraw occurred at: ' + new Date().getTime());
        //     let resJson = $chainPlaceAmountShareDT.ajax.json()
        //     // console.log("resJson: " + JSON.stringify(resJson))
        //
        //     placeChainStoreBOList = resJson.data
        //     // placeChainStoreBOListOrigin copy placeChainStoreBOList
        //     placeChainStoreBOListOrigin = JSON.parse(JSON.stringify(placeChainStoreBOList))
        //
        //     // console placeChainStoreBOList and placeChainStoreBOListOrigin first element
        //
        //     console.log("Redraw Origin: placeId=" + placeChainStoreBOListOrigin[0].placeId,
        //         ", sharePresentAccount=", placeChainStoreBOListOrigin[0].sharePresentAccount,
        //         ", shareMemberPoint=", placeChainStoreBOListOrigin[0].shareMemberPoint)
        //
        //     initCheckbox(placeChainStoreBOListOrigin, placeChainStoreBOList)
        // })

        // 两个地方出发保存提示，1  点击保存按钮；2  切换 tab。
        // 切换页面时，触发丢失提示

        $saveChainAmountShareConfig.click(function () {
            console.log("saveChainAmountShareConfig click")

            // 二次确认
            Swal.fire({
                title: "确定保存吗？",
                type: "info",
                showCancelButton: true,
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(function (result) {
                if (!result.value) {
                    console.log("cancel save chain amount share config")
                } else {
                    console.log("confirm save chain amount share config")
                    updateChainAmountShareConfig(placeChainStoreBOList)
                }
            })

            // $saveChainAmountShareConfig can click after 2 seconds
            $saveChainAmountShareConfig.prop("disabled", true)
            setTimeout(function () {
                if ($saveChainAmountShareConfig.prop("disabled")) {
                    $saveChainAmountShareConfig.prop("disabled", false)
                }
            }, 2000)

        })

        // 设置共享漫游项tab隐藏时的提示
        let $tabEle = $('a[data-toggle="tab"]')
        $tabEle.on('hide.bs.tab', async function (e) {
            console.log("hide.bs.tab 老选项卡隐藏之前")

            switch ((e.target.toString().split('#')[1])) {
                case 'tabs_chain_card_type_config':
                    // console.log("tabs_chain_card_type_config hide")
                    break;
                case 'tabs_chain_amount_share_config':
                    let inParam = Temp
                    // console.log("tabs_chain_amount_share_config hide")
                    console.log("tabs_chain_amount_share_config hide, BO: placeId=" + inParam[0].placeId,
                        ", sharePresentAccount=", inParam[0].sharePresentAccount,
                        ", shareMemberPoint=", inParam[0].shareMemberPoint)

                    let changed = await listHaveChanged(placeChainStoreBOListOrigin, inParam)
                    if (changed) {
                        doubleConfirm(inParam)
                    } else {
                        console.log("else after if , changed: " + changed)
                    }
                    break
                case 'tabs_chain_place_config':
                    // console.log("tabs_chain_place_config hide")
                    break;
                default:
                    console.log("default")
                    break;
            }
        })

        // 页面关闭/刷新事件
        window.addEventListener('beforeunload', async function (e) {
            // e.preventDefault();
            console.log("beforeunload")
            // doubleConfirm(placeChainStoreBOList)
            let tips = '确定要关闭当前页面吗？'
            let changed = await listHaveChanged(placeChainStoreBOListOrigin, Temp)
            console.log("before if , changed: " + changed)
            if (changed) {
                e.returnValue = tips
                return tips
            }
        })
    }

    // create a function named listHaveChanged,
    // to found the change between placeChainStoreBOListCopy and placeChainStoreBOList about sharePresentAccount and shareMemberPoint
    let listHaveChanged = async function (placeChainStoreBOListOrigin, placeChainStoreBOList) {
        let changed = false

        console.log("start listHaveChanged, changed=" + changed)

        console.log("Origin: placeId=" + placeChainStoreBOListOrigin[0].placeId,
            ", sharePresentAccount=", placeChainStoreBOListOrigin[0].sharePresentAccount,
            ", shareMemberPoint=", placeChainStoreBOListOrigin[0].shareMemberPoint)
        console.log("BO: placeId=" + placeChainStoreBOList[0].placeId,
            ", sharePresentAccount=", placeChainStoreBOList[0].sharePresentAccount,
            ", shareMemberPoint=", placeChainStoreBOList[0].shareMemberPoint)

        placeChainStoreBOListOrigin.forEach(function (placeChainStoreBOOrigin) {
            placeChainStoreBOList.forEach(function (placeChainStoreBO) {
                if (placeChainStoreBOOrigin.placeId == placeChainStoreBO.placeId) {
                    console.log("in forEach")

                    console.log("Origin: placeId=" + placeChainStoreBOListOrigin[0].placeId,
                        ", sharePresentAccount=", placeChainStoreBOListOrigin[0].sharePresentAccount,
                        ", shareMemberPoint=", placeChainStoreBOListOrigin[0].shareMemberPoint)
                    console.log("BO: placeId=" + placeChainStoreBOList[0].placeId,
                        ", sharePresentAccount=", placeChainStoreBOList[0].sharePresentAccount,
                        ", shareMemberPoint=", placeChainStoreBOList[0].shareMemberPoint)

                    if (placeChainStoreBOOrigin.sharePresentAccount != placeChainStoreBO.sharePresentAccount) {
                        // console placeChainStoreBOOrigin and placeChainStoreBO
                        changed = true
                    }
                    if (placeChainStoreBOOrigin.shareMemberPoint != placeChainStoreBO.shareMemberPoint) {
                        changed = true
                    }
                }
            })
        })

        console.log("end listHaveChanged, changed=" + changed)
        return changed
    }

    let initChainCardType = function () {
        initChainCardTypeFromServer()
    }

    let initValue = function () {
        // get $currentChainId value
        currentChainIdValue = $currentChainId.data("chainid")
        console.log("currentChainIdValue: " + currentChainIdValue)
        // 根据连锁门店Id查询连锁信息
        $.ajax({
            url: '/place-server/chain/getChain',
            type: 'GET',
            success: function (response) {
                if (response.code !== 100000) {
                    alert(response.message);
                    return;
                }
                var upgradeUserLevelFlag = response.data.obj.upgradeUserLevelFlag;
                var downgradeUserLevelFlag = response.data.obj.downgradeUserLevelFlag;
                if (upgradeUserLevelFlag === 1) {
                    $('#upgradeUserLevelOnPoints').prop('checked', true)
                    $('#upgradeUserLevelOnPoints').val(1)
                } else {
                    $('#upgradeUserLevelOnPoints').prop('checked', false)
                    $('#upgradeUserLevelOnPoints').val(0)
                }

                if (downgradeUserLevelFlag === 1) {
                    $('#downgradeUserLevelOnPoints').prop('checked', true)
                    $('#downgradeUserLevelOnPoints').val(1)
                } else {
                    $('#downgradeUserLevelOnPoints').prop('checked', false)
                    $('#downgradeUserLevelOnPoints').val(0)
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
            }
        });
    }


    //连锁中心根据积分自动升级用户等级复选框点击事件处理
    $("#upgradeUserLevelOnPoints").click(function () {
        if ($(this).prop("checked")) {
            $('#upgradeUserLevelOnPoints').val(1)
        } else {
            $('#upgradeUserLevelOnPoints').val(0)
        }
        $("#upgradeUserLevelOnPointsModal").modal("show")

    })

    //连锁中心在”根据积分自动升级用户等级”开关点击后，处理弹框中的内容
    function initUpgradeUserLevelOnPoints() {
        $('#upgradeUserLevelOnPointsModal').on('show.bs.modal', function (event) {
            let action = '开启';
            let append = '';
            if ($('#upgradeUserLevelOnPoints').prop("checked")) {
                action = '开启'
                append = '开启后会打开连锁下所有门店下的积分漫游！'
            } else {
                action = '关闭'
            }
            var model = $(this);
            model.find('.modal-body').text("确定要" + action + "”根据积分自动升级用户等级”吗？" + append);
        });
    }

    //连锁中心"根据积分自动降级用户等级"复选框点击事件处理
    $("#downgradeUserLevelOnPoints").click(function () {
        if ($(this).prop("checked")) {
            $('#downgradeUserLevelOnPoints').val(1)
        } else {
            $('#downgradeUserLevelOnPoints').val(0)
        }
        $("#downgradeUserLevelOnPointsModal").modal("show")

    })

    //连锁中心在”根据积分自动升级用户等级”开关点击后，处理弹框中的内容
    function initDowngradeUserLevelOnPoints() {
        $('#downgradeUserLevelOnPointsModal').on('show.bs.modal', function (event) {
            let action = '开启';
            let append = '';
            if ($('#downgradeUserLevelOnPoints').prop("checked")) {
                action = '开启'
                append = '开启后会打开连锁下所有门店下的积分漫游！'
            } else {
                action = '关闭'
            }
            var model = $(this);
            model.find('.modal-body').text("确定要" + action + "”根据积分自动降级用户等级”吗？" + append);
        });
    }

    // 根据“积分”自动升级用户等级确定按钮
    function upgradeUserLevelOnPointsConfirm() {
        $("#upgradeUserLevelOnPointsConfirm").click(function () {
            var btn = $(this);
            var form = $('#placeChainForm');

            // 调用修改场所配置接口
            var num = 0;
            if ($('#upgradeUserLevelOnPoints').prop("checked")) {
                num = 1
            } else {
                num = 0
            }

            $.ajax({
                url: '/place-server/chain/update?chainId=' + currentChainIdValue + "&type=" + 0 + "&num=" + num,
                type: 'POST',
                success: function (response) {
                    console.log(response);
                    if (response.result) {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'success', response.message);
                        // setTimeout(window.location.reload(), 2000);
                        setTimeout(function () {
                                window.location.reload();
                            },
                            2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'warning', response.message);
                    }
                },
                error: function (response, status) {
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            })
        })
    }

    // 根据“积分”自动升级用户等级弹框关闭按钮
    function upgradeUserLevelOnPointsClose() {
        $("#upgradeUserLevelOnPointsClose").click(function () {
            if ($("#upgradeUserLevelOnPoints").prop("checked")) {
                $('#upgradeUserLevelOnPoints').val(0)
                $("#upgradeUserLevelOnPoints").prop("checked", false)
            } else {
                $('#upgradeUserLevelOnPoints').val(1)
                $("#upgradeUserLevelOnPoints").prop("checked", true)
            }
        })
    }

    // 根据“积分”自动升级用户等级取消按钮
    function upgradeUserLevelOnPointsCancel() {
        $("#upgradeUserLevelOnPointsCancel").click(function () {
            if ($("#upgradeUserLevelOnPoints").prop("checked")) {
                $('#upgradeUserLevelOnPoints').val(0)
                $("#upgradeUserLevelOnPoints").prop("checked", false)
            } else {
                $('#upgradeUserLevelOnPoints').val(1)
                $("#upgradeUserLevelOnPoints").prop("checked", true)
            }
        })
    }

    // 根据“积分”自动降级用户等级弹框关闭按钮
    function downgradeUserLevelOnPointsClose() {
        $("#downgradeUserLevelOnPointsClose").click(function () {
            if ($("#downgradeUserLevelOnPoints").prop("checked")) {
                $('#downgradeUserLevelOnPoints').val(0)
                $("#downgradeUserLevelOnPoints").prop("checked", false)
            } else {
                $('#downgradeUserLevelOnPoints').val(1)
                $("#downgradeUserLevelOnPoints").prop("checked", true)
            }
        })
    }

    // 根据“积分”自动降级用户等级取消按钮
    function downgradeUserLevelOnPointsCancel() {
        $("#downgradeUserLevelOnPointsCancel").click(function () {
            if ($("#downgradeUserLevelOnPoints").prop("checked")) {
                $('#downgradeUserLevelOnPoints').val(0)
                $("#downgradeUserLevelOnPoints").prop("checked", false)
            } else {
                $('#downgradeUserLevelOnPoints').val(1)
                $("#downgradeUserLevelOnPoints").prop("checked", true)
            }
        })
    }

    // 根据“积分”自动降级用户等级确定按钮
    function downgradeUserLevelOnPointsConfirm() {
        $("#downgradeUserLevelOnPointsConfirm").click(function () {
            var btn = $(this);
            // var form = $('#cardTypeForm');
            var form = $('#placeChainForm');

            // 调用修改场所配置接口
            var num = 0;
            if ($('#downgradeUserLevelOnPoints').prop("checked")) {
                num = 1
            } else {
                num = 0
            }

            $.ajax({
                url: '/place-server/chain/update?chainId=' + currentChainIdValue + "&type=1" + "&num=" + num,
                type: 'POST',
                success: function (response) {
                    console.log(response);
                    if (response.result) {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'success', response.message);
                        // setTimeout(window.location.reload(), 2000);
                        setTimeout(function () {
                                window.location.reload();
                            },
                            2000);
                    } else {
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'warning', response.message);
                    }
                },
                error: function (response, status) {
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            })
        })
    }

    // 清除校验提示
    function removeOperationRecord() {
        // 移除警告弹出框
        var form = $("#cardTypeEditForm");
        form.find('.alert').remove();
        $('#minCreateCardAmount').val('');
        $('#minPointsRequirement').val('');
    }


    let $chainCardType = $("#kt_table_1")
    let $chainPlaceAmountShare = $("#kt_table_2")

    let $currentChainId = $("#currentChainId")
    let currentChainIdValue
    // let placeChainStoreBOList = []
    // let placeChainStoreBOListOrigin = []

    return {

        // main function to initiate the module
        init: function () {
            initValue()
            initChainCardType()
            chainCardTypeCreateSubmit()
            chainCardTypeDeleteSubmit()
            chainCardTypeEditSubmit()// 连锁门店卡类型修改功能
            // chainCardTypeCancelSubmit()
            initModal()

            initChainPlaceAmountShare()

            initUpgradeUserLevelOnPoints() // 根据积分自动升级卡类型按钮点击后的model初始化
            initDowngradeUserLevelOnPoints()

            upgradeUserLevelOnPointsConfirm() // 根据积分自动升级卡类型”确定“按钮
            downgradeUserLevelOnPointsConfirm()

            upgradeUserLevelOnPointsCancel() // 根据积分自动升级卡类型”取消“按钮
            upgradeUserLevelOnPointsClose() // 根据积分自动升级卡类型弹窗”关闭“按钮
            downgradeUserLevelOnPointsCancel()
            downgradeUserLevelOnPointsClose()
        },
    };
}
();


jQuery(document).ready(function () {
    KTDatatablesDataSourceAjaxServer.init();

});