"use strict";

var ForgetGeneral = function () {
    var showErrorMsg = function (form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">'+ msg + '</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

    var initButton = function () {
        $('#kt_login_signup_cancel').click(function (e) {
            e.preventDefault();
            //displayWechatForm();
            window.location="/place-server/admin/login";
        });
    }

    var sendCode = function (){
        $("#kt_send_validcode_sumit").click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $(this).closest('form');
            var mobileReg = /^1[3|5|6|7|8|9]{1}[0-9]{9}$/;
            var mobileValue = $("#mobile").val();
            var accountValue = $("#account").val();
            if (accountValue == "") {
                showErrorMsg(form, 'warning', "请输入登录账号");
                return;
            }
            var countdown = 60;
            if (mobileValue.length == 11 && mobileReg.test(mobileValue)) {
                $.ajax({
                    url : "/place-server/admin/base/checkMobile?mobile=" + mobileValue + "&account=" + accountValue,
                    success : function (checkMobileResult) {
                        if ("ok" == checkMobileResult){
                            $.ajax({
                                url: "/place-server/admin/base/sendCode?mobile=" + mobileValue + "&account=" + accountValue,
                                success: function (sendCodeResult) {
                                    if ("ok" == sendCodeResult) {
                                        showErrorMsg(form, 'success', "验证码发送成功，请注意查收");
                                    } else {
                                        showErrorMsg(form, 'danger', "验证码发送失败，请稍后再试");
                                    }
                                    $("#kt_login_signup_submit").attr("disabled",false);
                                    var timer = setInterval(function() {
                                        countdown--;
                                        if (countdown == 0) {
                                            btn.attr('disabled', false);
                                            btn.html("获取验证码");
                                            clearInterval(timer);
                                        } else {
                                            btn.attr('disabled', true);
                                            btn.html("重新发送(" + countdown + "s)");
                                        }
                                    }, 1000);
                                }
                            });
                        } else {
                            showErrorMsg(form, 'warning', "请输入该账号绑定的手机号码");
                        }
                    }
                });
            } else {
                showErrorMsg(form, 'warning', "请输入手机号码");
            }
        });
    };

    var handleSignUpFormSubmit = function() {
        var form = $("#forgetForm");
        form.validate({
            rules: {
                account: {
                    required: true,
                    accountVerification: true,
                    remote: {
                        url: "/place-server/admin/account/check/exist",
                        type: "get",
                        dataType: "json",
                        data: {
                            account: function () { return $("#account").val(); },
                        },
                        dataFilter: function (data, type) {
                            if (data == "exist") {
                                return true;  // 账号已经存在，返回exist
                            } else {
                                return false;
                            }
                        }
                    }
                },
                mobile: {
                    required: true,
                    mobile: true
                },
                newPassword: {
                    required: true,
                    rangelength:[6,60],
                    updatePassword: true
                },
                reNewPassword: {
                    required: true,
                    equalTo: "#newPassword"
                },
                code: {
                    required: true,
                    minlength: 6,
                    maxlength: 6,
                    remote: {
                        url: "/place-server/admin/base/checkCode",
                        type: "get",
                        dataType: "json",
                        data: {
                            account: function () { return $("#account").val(); },
                            mobile: function () { return $("#mobile").val(); },
                            code: function () { return $("#code").val(); }
                        },
                        dataFilter: function (data, type) {
                            if (data == "exist") {
                                return true;  //验证码相同，校验通过，返回exist
                            }
                            else {
                                return false;
                            }
                        }
                    }
                },
            },
            messages: {
                account: {
                    remote: '该账户不存在！'
                },
                code: {
                    remote: '验证码不正确！'
                }
            }
        });

        $('#kt_login_signup_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var form = $("#forgetForm");

            if (!form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            form.ajaxSubmit({
                url: '/place-server/admin/base/resetPassword',
                type: 'POST',
                success: function(response) {
                    if(response.result){
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'success', response.message);
                        setTimeout(function(){ history.back(-1); }, 2000);
                    }else{
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg(form, 'warning', response.message);
                    }
                },
                error: function(response, status){
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg(form, 'danger', '系统繁忙，请稍后再试');
                }
            });
        });
    }

    // Public Functions
    return {
        // public functions
        init: function() {
            console.log("111")
            initButton();
            handleSignUpFormSubmit();
            sendCode();
        }
    };
}();



// Class Initialization
jQuery(document).ready(function() {
    jQuery.validator.addMethod("mobile", function(value, element) {
        var length = value.length;
        var mobile = /^1[3|4|5|6|7|8|9]{1}[0-9]{9}$/;
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请填写手机号码");

    jQuery.validator.addMethod("accountVerification", function(value, element) {
        var account = /^\w+$/;
        return this.optional(element) || (account.test(value));
    }, "请输入正确的账号格式!(可以输入英文、数字、下划线)");

    jQuery.extend(jQuery.validator.messages, {
        remote : "验证码不正确",
    });

    jQuery.validator.addMethod("updatePassword", function (value, element) {
        // console.log('value', value)
        // 密码长度为至少6位，且必须同时包含至少一个字母和一个数字
        const regex = /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/;
        let bool = regex.test(value);
        // console.log('bool', bool)

        return bool;
    }, "密码不符合要求，请修改。密码长度为至少6位，且必须同时包含至少一个字母和一个数字，且不能为默认密码。");

    ForgetGeneral.init();
});