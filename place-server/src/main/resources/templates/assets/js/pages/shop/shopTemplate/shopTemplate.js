"use strict";

var KTDatatablesDataSourceAjaxServer = function() {

    var showErrorMsg = function(form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">'+msg+'</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    }

    var initDaterangepicker = function (){
        var curDate = new Date();
        $('#kt_daterangepicker_4').daterangepicker({
            language:  'zh-CN',
            buttonClasses: 'btn',
            applyClass: 'btn-primary',
            cancelClass: 'btn-secondary',
            maxDate: curDate,
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: '确认',
                cancelLabel: '取消',
                fromLabel: '从',
                toLabel: '到',
                customRangeLabel: '选择时间',
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
            }
        }, function(start, end, label) {
            $('#kt_daterangepicker_4 .form-control').val( start.format('YYYY-MM-DD') + ' ~ ' + end.format('YYYY-MM-DD'));
        });
    }

    var initTopupPresentTable = function() {
        // begin first table
        var goodsName = $('#selectGoodsName').val();
        var goodsTypeId = $('#goodsTypeSelect').val();

        var table = $("#kt_table_1").DataTable({
            language: {
                "url": "http://assets.topfreeweb.net/assets/vendors/custom/Chinese.json"
            },
            serverSide: true, // 开启服务器模式，必须
            responsive: true, // 启用和配置响应式
            processing: true, // 是否显示加载中提示
            searching: false, // 开启搜索框
            lengthChange: false, // 开启每页设置显示数量
            pageLength: 10, // 设置每页默认显示数量
            ordering: false, // 字段排序
            paging: true,
            destroy: true,
            ajax: {
                url: '/place-server/admin/shop/goodsInfo/listDailyGoods',
                type:'GET',
                data: {
                    "goodsName": goodsName,
                },
            },
            columns: [
                { data: 'goodsPic' },
                { data: 'goodsName' }, // -4
                {data: '操作', responsivePriority: -1},
            ],
            columnDefs: [
                {
                    targets: [-2],
                    data: 'no',
                    render: function(data, type, full, meta) {
                        if (data !==null && data !== undefined){
                            return '<span>'+data+'</span>'
                        }else {
                            return '<span>--</span>'
                        }

                    },
                },
                {
                    targets: [-3],
                    data: 'no',
                    render: function(data, type, full, meta) {
                        if (data !==null && data !== undefined){
                            return '<img style="width: 30px" src="' + data + '"/>'
                        }else {
                            return '<span>--</span>'
                        }

                    },
                },
//data-target="#kt_modal_template"
                {
                    targets: -1,
                    render: function(data, type, full, meta) {
                        return '<button type="button" class="btn btn-bold btn-label-brand btn-sm" data-toggle="modal"  data-target="#kt_modal_template"'  + //data-toggle="modal" data-target="#kt_modal_template"
                            'data-goodsName="'+full.goodsName+'"  data-goodsPic="'+full.goodsPic+'" data-goodsTypeId="'+full.goodsTypeId+'"  data-goodsTypeName="'+full.goodsTypeName+'" > 选择</button>'
                    },
                },
            ],
            initComplete: function () {
            }
        });
    };

    // 页面查询按钮
    $('#kt_search').on('click', function(e) {
        e.preventDefault();
        initTopupPresentTable();
        initGoodsType()
    });


    var initGoodsType = function () {
       var length= $('#goodsTypeSelect2 option').length

        if (length >2){
            return
        }
        $.ajax({
            async: false,
            url: '/place-server/admin/shop/goodsType/queryAllTypeByPlaceId',
            type: 'GET',
            dataType: 'json',
            success: function(response){
                $('#goodsTypeSelect2').empty();
                 $('#goodsTypeSelect2').append("<option value=''>请选择</option>");
                console.log(response.data.list);
                response.data.list.forEach(item=>$("#goodsTypeSelect2").append("<option value='" + item.goodsTypeId + "'>" + item.goodsTypeName + "</option>" ))

            },
            error: function(XMLHttpRequest, textStatus, errorThrown){

            }
        });
    };


    $('#goodsPic').on('change', function(e) {
        var imgEle = $("#goodsPic")[0].files[0];
        var formData = new FormData();
        formData.append("img", imgEle);
        var r= new FileReader();
        r.readAsDataURL(imgEle);
        r.onload=function (e) {
            $('#goodsPicImg').attr("src", this.result);
            $('#goodsPic').attr("src", this.result);
        }

    })


    $('#template_sub').on('click', function(e) {
        var form = $('#addGoodsForm');
        var goodsPic=  $('#goodsPicImg').attr("src")
        var imgEl2e = $("#goodsPic").val()
        var goodsId = $("#goodsId").val()
        if (goodsPic === "" || goodsPic === undefined ){
            //没有
            form.validate({
                rules: {
                    insertGoodsName: {
                        required: true,
                    },
                    salePrice: {
                        required: true,
                        nameVerificationPrice: true
                    },
                    goodsTypeId: {
                        required: true,
                        nameVerification: true
                    },
                    goodsPic: {
                        required: true,
                        fileMaxSize: true,
                        extensionempty: true,

                    },
                },
            });

        }else{
            form.validate({
                rules: {
                    insertGoodsName: {
                        required: true,
                    },
                    salePrice: {
                        required: true,
                        nameVerificationPrice: true
                    },
                    goodsTypeId: {
                        required: true,
                        nameVerification: true
                    },
                },
            });

        }

        if (!form.valid()) {
            return;
        }
        $("#template_sub").attr('disabled',true);
        var goodsTypeId=  $('#goodsTypeSelect2').val();
        var goodsTypeName=  $('#goodsTypeSelect2').find("option:selected").text();
        var salePrice=  $('#salePrice').val();
        var useCashAccount=  $('#useCashAccount').val();
        var goodsBarcode=  $('#goodsBarcode').val();
        var goodsName=  $('#insertGoodsName').val();

        var price= Number(salePrice) * 100

        if (goodsPic !== "" && goodsPic !== undefined){
            if (goodsPic.indexOf("http") === -1){
                //没有-新增操作

                var imgEle = $("#goodsPic")[0].files[0];
                var formdata = new FormData();
                formdata.append('file',imgEle);

                $.ajax({
                    url: '/place-server/admin/shop/storageGoods/uploadingflie',
                    type:'post',
                    data:formdata,
                    cache: false,
                    processData: false,
                    contentType: false,
                    success: function(response){

                        setTimeout(function() {

                        }, 2000);

                        console.log("文件",response)
                        var goodsInfos = {
                            "goodsPic":response,
                            "goodsTypeName":goodsTypeName,
                            "goodsTypeId":goodsTypeId,
                            "unitPrice":price,
                            "goodsId":goodsId,
                            "goodsBarcode":goodsBarcode,
                            "useCashAccount":useCashAccount,
                            "goodsName":goodsName,

                        };
                        $.ajax({
                            url: '/place-server/admin/shop/storageGoods/save',
                            type: 'POST',
                            dataType: 'json',
                            contentType : "application/json",
                            data: JSON.stringify(goodsInfos),
                            success: function(response){
                                if(response.result){
                                    showErrorMsg(form, 'success', response.message);
                                    $("#template_sub").attr('disabled',true);
                                    setTimeout(function() { window.location = "/place-server/admin/shop/goodsInfo/list"; }, 2000);
                                } else {
                                    showErrorMsg(form, 'warning', response.message);
                                }
                            },
                            error: function(XMLHttpRequest, textStatus, errorThrown){
                            }
                        });
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown){
                    }
                });

            }else{
                var goodsInfos = {
                    "goodsPic":goodsPic,
                    "goodsTypeName":goodsTypeName,
                    "goodsTypeId":goodsTypeId,
                    "unitPrice":price,
                    "goodsBarcode":goodsBarcode,
                    "useCashAccount":useCashAccount,
                    "goodsName":goodsName,
                    "goodsId":goodsId,
                };
                $.ajax({
                    url: '/place-server/admin/shop/storageGoods/save',
                    type: 'POST',
                    dataType: 'json',
                    contentType : "application/json",
                    data: JSON.stringify(goodsInfos),
                    success: function(response){
                        if(response.result){
                            showErrorMsg(form, 'success', response.message);
                            $("#template_sub").attr('disabled',true);
                            setTimeout(function() { window.location = "/place-server/admin/shop/goodsInfo/list"; }, 2000);
                        } else {
                            showErrorMsg(form, 'warning', response.message);
                        }
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown){
                    }
                });


            }
        }

    });



    $("#kt_modal_template").on('show.bs.modal', function (event) {

        var button = $(event.relatedTarget);
        var goodsName = button.data('goodsname');
        var goodsTypeId = button.data('goodstypeid');
        var goodsTypeName = button.data('goodstypename');
        var goodsPic = button.data('goodspic');
        $('#goodsPic').attr("src",goodsPic)
        $('#titleName').text("新增商品");
        $('#template_sub').text("确定");

        var goodsTypeId = button.data('goodstypeid');
        var goodsTypeName = button.data('goodstypename');
        $('#goodsPicImg').attr("src",goodsPic)
        $('#goodsTypeName').val(goodsTypeName);
        $('#goodsTypeSelect1').val(goodsTypeId);

        $('#goodsTypeId').val(goodsTypeId);
        $('#insertGoodsName').val(goodsName);
        $('#goodsPic').attr("aria-invalid",false);
        $("#template_sub").attr('disabled',false);
        initGoodsType();

    })







    return {
        // main function to initiate the module
        init: function() {
            initDaterangepicker();
            initTopupPresentTable();
            initGoodsType()
        },
    };

}();

jQuery(document).ready(function() {

    jQuery.validator.addMethod("nameVerification", function(value, element) {
        var name = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/;
        return this.optional(element) || (name.test(value));
    }, "请输入正确的名称格式!由中文、英文、数字组成");

    $.validator.addMethod("fileMaxSize", function(value, element, param) {
        var file = element.files[0];
        var fileMaxSize = param * 1024 * 1024;
        return this.optional(element) || !(file.size > fileMaxSize);
    }, $.validator.format("文件最大不超过3M"));

    jQuery.validator.addMethod("nameVerificationPrice", function(value, element) {
        var name = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
        return this.optional(element) || (name.test(value));
    }, "请输入正数，最多保留二位小数");

    $.validator.addMethod("extensionempty", function(value, element, param) {
        param = typeof param === "string" ? param.replace(/,/g, "|") : "png|jpe?g|gif";
        console.log(value, param);
        return this.optional(element) || value.match(new RegExp("\\.(" + param + ")$", "i")) || value.indexOf('.') == -1;
    }, $.validator.format("文件类型不符合要求"));

    jQuery.validator.addMethod("unitPrice", function (value, element) {
        return this.optional(element) || (/^([1-9]\d*|[0])(\.\d{1})?$/.test(value));
    }, "请输入正数，最多保留一位小数");

    let startValue = moment().startOf('month').format('YYYY-MM-DD');
    let endValue = moment(new Date()).format('YYYY-MM-DD');
    $('#kt_daterangepicker_4 .form-control').val( startValue + ' ~ ' + endValue);
    KTDatatablesDataSourceAjaxServer.init();
});