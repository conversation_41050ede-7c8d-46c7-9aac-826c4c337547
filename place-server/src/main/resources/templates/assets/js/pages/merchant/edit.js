// var idcardFront, idcardBack, handIdcard, businessLicense, cashier, venue, facade, authorizationLetter;

var pics = {
    idcardFront: "",
    idcardBack: "",
    handIdcard: "",
    businessLicense: "",
    cashier: "",
    venue: "",
    facade: "",
    authorizationLetter: "",
    bankCard: "",
    bankCardBack: ""
}

var AjaxServer = function(){
    var showErrorMsg = function(form, type, msg) {
        var alert = $('<div class="alert alert-' + type + ' alert-dismissible" role="alert">\
			<div class="alert-text">'+msg+'</div>\
			<div class="alert-close">\
                <i class="flaticon2-cross kt-icon-sm" data-dismiss="alert"></i>\
            </div>\
		</div>');

        form.find('.alert').remove();
        alert.prependTo(form);
        //alert.animateClass('fadeIn animated');
        KTUtil.animateClass(alert[0], 'fadeIn animated');
        alert.find('span').html(msg);
    };

    /**
     * 自动将form表单封装成json对象
     */
    $.fn.serializeObject = function() {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function() {
            if (o[this.name]) {
                if (!o[this.name].push) {
                    o[this.name] = [ o[this.name] ];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    var initBank = function () {
        initBankList();
    };

    var initCascadeSelect = function(){
        // fullAddress 地区编码只取第三层的前六位，特例：中山/东莞取第四层的值的前六位
        initProvince();

        $("#provinceSelect").change(function(){
            var provinceId = $("#provinceSelect").val();
            initCity(provinceId);
        });

        $("#citySelect").change(function(){
            var cityId = $("#citySelect").val();
            initCountry(cityId);
        });

        $("#countrySelect").change(function(){
            var countryId = $("#countrySelect").val();
            initTown(countryId);
        });

        $("#townSelect").change(function(){
            var cityId = $("#citySelect").val();
            if (cityId == ************ || cityId == ************) {
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text()+$("#townSelect option:selected").text());
            } else {
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text()+$("#countrySelect option:selected").text()+$("#townSelect option:selected").text());
            }
        });
    };


    var initBankCascadeSelect = function(){
        // fullAddress 地区编码只取第三层的前六位，特例：中山/东莞取第四层的值的前六位
        initBankProvince();

        $("#bankProvinceSelect").change(function(){
            var provinceId = $("#bankProvinceSelect").val();
            initBankCity(provinceId);
        });

        $("#bankCitySelect").change(function(){
            var cityId = $("#bankCitySelect").val();
            initBankCountry(cityId);
        });

        $("#bankCountrySelect").change(function(){
            var countryId = $("#bankCountrySelect").val();

            if (countryId != "") {
                var bankDistrict = countryId.substring(0, 6);
                var bankCode = $("#bankCode option:selected").val();
                // getBankBranchList(bankDistrict, bankCode);
            }
            initBankTown(countryId);
        });

        $("#bankTownSelect").change(function(){
            var cityId = $("#bankTownSelect").find("option:selected").val();
            if (cityId != "") {
                var bankDistrict = cityId.substring(0, 6);
                var bankCode = $("#bankCode option:selected").val();
                getBankBranchList(bankDistrict, bankCode);
            }
        });
    };

    var initIsCorporate = function () {
        var $isCorporate = $("#isCorporate");
        $isCorporate.change(function(){
            var isCorporate = $("#isCorporate option:selected").val();
            if (isCorporate == 2) {
                $("#isCorporateRow").removeAttr("hidden");
                $("#authorizationLetterRow").removeAttr("hidden");
            } else {
                $("#isCorporateRow").attr("hidden", true);
                $("#authorizationLetterRow").attr("hidden", true);
            }
        });

        if ($("#pageType").val() == 1) {
            var value;
            if ($("#isCorporateHidden").val() == "否") {
                value = 2;
            } else if ($("#isCorporateHidden").val() == "是") {
                value = 1;
            }
            $isCorporate.val(value);
            if ($isCorporate.val() == 2) {
                $("#isCorporateRow").removeAttr("hidden");
                $("#authorizationLetterRow").removeAttr("hidden");
            } else {
                $("#isCorporateRow").attr("hidden", true);
                $("#authorizationLetterRow").attr("hidden", true);
            }
        }
    };

    function initUpload() {
        $(".placeUploadImg").on("change", function () {
            var $inputELe = this;
            var idOfImg = $inputELe.id;
            var idOfUrl = idOfImg.substring(0, idOfImg.length -3);

            var file =this.files[0];
            if (file == null) {
                return;
            }
            if (!/.(jpg|jpeg|png|JPG)$/.test(file.name)) {
                showErrorMsg($("#form-merchant-1"), "warning", "图片类型必须是 .jpg,.jpeg,.png,.JPG 中的一种！");
                scrollTo(0,0);
                return;
            }
            var imgMaxSize = 1 * 1024 * 1024;
            if (file.size > imgMaxSize){
                showErrorMsg($("#form-merchant-1"), "warning", "图片最大不超过1M！");
                scrollTo(0,0);
                return;
            }

            var data = new FormData();
            data.append("multipartFile", file);
            data.append("placeId", $("#barId").val());

            $.ajax({
                url: "/place-server/admin/merchant/uploadPic",
                type: "POST",
                data: data,
                contentType: false,
                processData: false,
                success: function (res) {
                    if (res != "error") {
                        //返回图片url
                        switch (idOfUrl) {
                            case "idcardFront":
                                pics.idcardFront = res;
                                break;
                            case "idcardBack":
                                pics.idcardBack = res;
                                break;
                            case "handIdcard":
                                pics.handIdcard = res;
                                break;
                            case "businessLicense":
                                pics.businessLicense = res;
                                break;
                            case "cashier":
                                pics.cashier = res;
                                break;
                            case "venue":
                                pics.venue = res;
                                break;
                            case "facade":
                                pics.facade = res;
                                break;
                            case "authorizationLetter":
                                pics.authorizationLetter = res;
                                break;
                            case "bankCard":
                                pics.bankCard = res;
                                break;
                            case "bankCardBack":
                                pics.bankCardBack = res;
                                break;
                            default:
                                break;
                        }
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThreown) {

                },
            });
        });
    }

    var handleFormSubmit = function() {
        var form = $("#form-merchant-1");
        form.validate({
            // define validation rules
            ignore:":hidden",
            rules: {
                // 网吧编号
                barId: {
                    required: true
                },
                // 手机号
                phone: {
                    required: true,
                    mobile: true
                },
                // 身份证号
                idCard: {
                    required: true,
                    idCardVali: true,
                },
                // 银行卡号
                bankCardNo: {
                    required: true,
                    bankCardNoVali: true,
                },
                // 营业执照号
                businessLicenseNo: {
                    required: true,
                    businessLicenseNoVali: true
                },
                // 真实姓名
                realName: {
                    required: true,
                    rangelength: [2,10],
                    realNameVali: true
                },
                // 邮箱地址
                email: {
                    required: true,
                    email: true,
                    emailVali: true
                },
                // 地区编码
                district: {
                    // 6位，地区表获取
                    required: true,
                },
                // 详细地址
                fullAddress: {
                    required: true,
                    maxlength: 200,
                },
                // 身份证有效期起始日
                idCardTimeStart: {
                    required: true,
                    date: true,
                },
                // 身份证有效期截止日
                idCardTimeEnd: {
                    required: true,
                    date: true,
                    dateRangeVali: true,
                },
                // 营业执照有效期起始日
                businessLicenseTimeStart: {
                    required: true,
                    date: true,
                },
                // 营业执照有效期截止日
                businessLicenseTimeEnd: {
                    required: true,
                    date: true,
                    dateRangeVali: true,
                },
                // 银行编码
                bankCode: {
                    required: true,
                },
                // 银行地区编码
                bankDistrict: {
                    required: true,
                    preBankCodeVali: true,
                },
                // 银行支行编码
                branchBankId: {
                    required: true,
                },
                // 是否法人收款
                isCorporate: {
                    required: true,
                },
                // 法人姓名
                juridicalPersonName: {
                    realNameVali: true,
                    isCorporateVali: true,
                },
                // 法人身份证号
                juridicalPersonIdCard: {
                    idCardVali: true,
                    isCorporateVali: true,
                },
                // 身份证有效期起始日
                juridicalPersonIdCardTimeStart: {
                    date: true,
                },
                // 身份证有效期截止日
                juridicalPersonIdCardTimeEnd: {
                    date: true,
                    dateRangeVali: true,
                },
                // 身份真正面照
                idcardFrontImg: {
                    imgRequiredVali: true,
                },
                // 身份证反面照
                idcardBackImg: {
                    imgRequiredVali: true,
                },
                // 手持身份证照
                handIdcardImg: {
                    imgRequiredVali: true,
                },
                // 营业执照照片
                businessLicenseImg: {
                    imgRequiredVali: true,
                },
                // 收银台照片
                cashierImg: {
                    imgRequiredVali: true,
                },
                // 经营场所照片
                venueImg: {
                    imgRequiredVali: true,
                },
                // 店面门头照片
                facadeImg: {
                    imgRequiredVali: true,
                },
                // 收款划付函照片
                authorizationLetterImg: {
                    isCorporateVali: true,
                },
                bankCardImg: {
                    imgRequiredVali: true,
                },
                bankCardBackImg: {
                    imgRequiredVali: true,
                }
            },
        });

        // 自定义注册提交
        $('#kt_submit').click(function(e) {
            e.preventDefault();
            var btn = $(this);
            var $form = $("#form-merchant-1");

            var fields = $form.serializeArray();
            var obj = {};
            $.each(fields, function (index, field) {
                obj[field.name] = field.value;
            });

            obj["pics"] = pics;
            obj["barId"] = $("#barId").val();
            obj["branchBankId"] = $("#branchBankId").val();

            if (!$form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            $.ajax({
                url: '/place-server/admin/merchant/register',
                type: 'POST',
                contentType: "application/json",
                dataType: "JSON",
                data: JSON.stringify(obj),
                success: function(response) {
                    if(response.result){
                    }else{
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg($form, 'warning', response.msg);
                        scrollTo(0,0);
                    }
                },
                error: function(response, status){
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg($form, 'danger', '系统繁忙，请稍后再试');
                    scrollTo(0,0);
                }
            });
        });

        // 更新提交
        $('#kt_submit_edit').click(function(e) {
            e.preventDefault();

            var btn = $(this);
            var $form = $("#form-merchant-1");

            var fields = $form.serializeArray();
            var obj = {};
            $.each(fields, function (index, field) {
                obj[field.name] = field.value;
            });

            obj["pics"] = pics;
            obj["barId"] = $("#barId").val();
            obj["branchBankId"] = $("#branchBankId").val();

            if (!$form.valid()) {
                return;
            }

            btn.addClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', true);

            $.ajax({
                url: '/place-server/admin/merchant/edit',
                type: 'POST',
                contentType: "application/json",
                dataType: "JSON",
                data: JSON.stringify(obj),
                success: function(response) {
                    if(response.result){
                    }else{
                        btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                        showErrorMsg($form, 'warning', response.msg);
                        scrollTo(0,0);
                    }
                },
                error: function(response, status){
                    btn.removeClass('kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light').attr('disabled', false);
                    showErrorMsg($form, 'danger', '系统繁忙，请稍后再试');
                    scrollTo(0,0);
                }
            });
        });
    };

    return {
        init: function () {

            // 初始化网吧地区下拉框
            initCascadeSelect();
            // 初始化银行所在地下拉框
            initBankCascadeSelect();
            // 初始化是否法人收款下拉框
            initIsCorporate();
            // 初始化图片上传
            initUpload();
            // 初始化银行名称列表
            initBank();
            // 提交
            handleFormSubmit();

        },
    }
}();

function getBankBranchList(bankDistrict, bankCode) {
    $.ajax({
        url: "/place-server/admin/merchant/getBankBranchList?bankDistrict=" + bankDistrict + "&bankCode=" + bankCode,
        type: "GET",
        dataType: "JSON",
        success: function (response) {
            if (response.code == 20000) {
                $("#branchBankId").empty();
                $("#branchBankId").append("<option value=''>请选择</option>");
                $.each(response.data.list, function (key, value) {
                    $("#branchBankId").append("<option value='" + key + "'>" + value + "</option>");
                })

                if ($("#pageType").val() == 1) {
                    $("#branchBankId").val($("#branchBankIdHidden").val());
                }
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initBankTown(countryId){
    $.ajax({
        url: '/place-server/admin/region/towns/'+countryId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#bankTownSelect").empty();
            $("#bankTownSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var townId = response[index].townId;
                $("#bankTownSelect").append("<option value='"+townId+"'>"+name+"</option");
            });

            if($("#pageType").val() != 0){
                $("#bankTownSelect").val($("#bankDistrict").val());
            }else{
                // $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text()+$("#countrySelect option:selected").text());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initBankCountry(cityId){
    $.ajax({
        url: '/place-server/admin/region/countries/'+cityId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#bankCountrySelect").empty();
            $("#bankCountrySelect").append("<option value=''>请选择</option>");
            // 对东莞市、中山市单独处理
            if (cityId == ************) {
                $("#bankCountrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else if (cityId == ************) {
                $("#bankCountrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else {
                $.each(response, function(index, item){
                    var name = response[index].name;
                    var countryId = response[index].countryId;
                    $("#bankCountrySelect").append("<option value='"+countryId+"'>"+name+"</option");
                });
            }

            if($("#pageType").val() != 0){
                var str = $("#bankDistrict").val().substring(0, 6) + "000000";
                $("#bankCountrySelect").val(str);
                initBankTown($("#bankCountrySelect").val());

                if (str != "") {
                    var bankDistrict = str.substring(0, 6);
                    var bankCode = $("#bankCode option:selected").val();
                    getBankBranchList(bankDistrict, bankCode);
                }
            }else{
            }
            $("#bankTownSelect").empty();
            $("#bankTownSelect").append("<option value=''>请选择</option>");
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initBankCity(provinceId){
    $.ajax({
        url: '/place-server/admin/region/cities/'+provinceId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#bankCitySelect").empty();
            $("#bankCitySelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var cityId = response[index].cityId;
                $("#bankCitySelect").append("<option value='"+cityId+"'>"+name+"</option");
            });

            if($("#pageType").val() != 0){
                $("#bankCitySelect").val($("#district").val().substring(0, 4) + "********");
                initBankCountry($("#bankCitySelect").val())
            }else{
                // $("#fullAddress").val($("#provinceSelect option:selected").text());
            }
            $("#bankCountrySelect").empty();
            $("#bankCountrySelect").append("<option value=''>请选择</option>");
            $("#bankTownSelect").empty();
            $("#bankTownSelect").append("<option value=''>请选择</option>");

        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initBankProvince(){
    $.ajax({
        url: '/place-server/admin/region/provinces',
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#bankProvinceSelect").empty();
            $("#bankProvinceSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var provinceId = response[index].provinceId;
                $("#bankProvinceSelect").append("<option value='" + provinceId + "'>" + name + "</option");
            });
            // type 为 0 则注册，为 1 则编辑
            if($("#pageType").val() != 0){
                $("#bankProvinceSelect").val($("#bankDistrict").val().substring(0, 2) + "**********");
                initBankCity($("#bankProvinceSelect").val());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){
        }
    });
}

function initBankList() {
    $.ajax({
        url: "/place-server/admin/merchant/getBankList",
        type: "GET",
        dataType: "json",
        success: function (response) {

            var $bankCode = $("#bankCode");
            $bankCode.empty();
            $bankCode.append("<option value=''>请选择</option>");

            if (response.code == 20000) {
                $.each(response.data.list, function (key, value) {
                    $bankCode.append("<option value='" + key + "'>" + value + "</option>");
                })
            }

            if ($("#pageType").val() == 1) {
                $bankCode.val($("#bankCodeHidden").val());
                // $("#branchBankId").val($("#branchBankIdHidden").val());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initTown(countryId){
    $.ajax({
        url: '/place-server/admin/region/towns/'+countryId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var townId = response[index].townId;
                $("#townSelect").append("<option value='"+townId+"'>"+name+"</option");
            });

            if($("#pageType").val() != 0){
                $("#townSelect").val($("#district").val());
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text()+$("#countrySelect option:selected").text());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initCountry(cityId){
    $.ajax({
        url: '/place-server/admin/region/countries/'+cityId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            // 对东莞市、中山市单独处理
            if (cityId == ************) {
                $("#countrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else if (cityId == ************) {
                $("#countrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else {
                $.each(response, function(index, item){
                    var name = response[index].name;
                    var countryId = response[index].countryId;
                    $("#countrySelect").append("<option value='"+countryId+"'>"+name+"</option");
                });
            }

            if($("#pageType").val() != 0){
                var str = $("#district").val().substring(0, 6) + "000000";
                $("#countrySelect").val(str);
                initTown($("#countrySelect").val());
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text());
            }
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initCity(provinceId){
    $.ajax({
        url: '/place-server/admin/region/cities/'+provinceId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#citySelect").empty();
            $("#citySelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var cityId = response[index].cityId;
                $("#citySelect").append("<option value='"+cityId+"'>"+name+"</option");
            });

            if($("#pageType").val() != 0){
                $("#citySelect").val($("#district").val().substring(0, 4) + "********");
                initCountry($("#citySelect").val())
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text());
            }
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");

        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initProvince(){
    $.ajax({
        url: '/place-server/admin/region/provinces',
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#provinceSelect").empty();
            $("#provinceSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var provinceId = response[index].provinceId;
                $("#provinceSelect").append("<option value='" + provinceId + "'>" + name + "</option");
            });
            // type 为 0 则注册，为 1 则编辑
            if($("#pageType").val() != 0){
                $("#provinceSelect").val($("#district").val().substring(0, 2) + "**********");
                initCity($("#provinceSelect").val());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){
        }
    });
}

function initBankList() {
    $.ajax({
        url: "/place-server/admin/merchant/getBankList",
        type: "GET",
        dataType: "json",
        success: function (response) {

            var $bankCode = $("#bankCode");
            $bankCode.empty();
            $bankCode.append("<option value=''>请选择</option>");

            if (response.code == 20000) {
                $.each(response.data.list, function (i, d) {
                    $bankCode.append("<option value='" + i + "'>" + d + "</option>");
                })
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initTown(countryId){
    $.ajax({
        url: '/place-server/admin/region/towns/'+countryId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var townId = response[index].townId;
                $("#townSelect").append("<option value='"+townId+"'>"+name+"</option");
            });

            if($("#type").val() != 0){
                $("#townSelect").val($("#district").val());
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text()+$("#countrySelect option:selected").text());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initCountry(cityId){
    $.ajax({
        url: '/place-server/admin/region/countries/'+cityId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            // 对东莞市、中山市单独处理
            if (cityId == ************) {
                $("#countrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else if (cityId == ************) {
                $("#countrySelect").append("<option value='************'>"+'市辖区/镇/村'+"</option");
            } else {
                $.each(response, function(index, item){
                    var name = response[index].name;
                    var countryId = response[index].countryId;
                    $("#countrySelect").append("<option value='"+countryId+"'>"+name+"</option");
                });
            }

            if($("#type").val() != 0){
                var str = $("#district").val().substring(0, 6) + "000000";
                $("#countrySelect").val(str);
                initTown($("#countrySelect").val());
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text()+$("#citySelect option:selected").text());
            }
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initCity(provinceId){
    $.ajax({
        url: '/place-server/admin/region/cities/'+provinceId,
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#citySelect").empty();
            $("#citySelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var cityId = response[index].cityId;
                $("#citySelect").append("<option value='"+cityId+"'>"+name+"</option");
            });

            if($("#type").val() != 0){
                $("#citySelect").val($("#district").val().substring(0, 4) + "********");
                initCountry($("#citySelect").val())
            }else{
                $("#fullAddress").val($("#provinceSelect option:selected").text());
            }
            $("#countrySelect").empty();
            $("#countrySelect").append("<option value=''>请选择</option>");
            $("#townSelect").empty();
            $("#townSelect").append("<option value=''>请选择</option>");

        },
        error: function(XMLHttpRequest, textStatus, errorThrown){

        }
    });
}

function initProvince(){
    $.ajax({
        url: '/place-server/admin/region/provinces',
        type: 'GET',
        dataType: 'json',
        success: function(response){
            $("#provinceSelect").empty();
            $("#provinceSelect").append("<option value=''>请选择</option>");
            $.each(response, function(index, item){
                var name = response[index].name;
                var provinceId = response[index].provinceId;
                $("#provinceSelect").append("<option value='" + provinceId + "'>" + name + "</option");
            });
            // type 为 0 则注册，为 1 则编辑
            if($("#type").val() != 0){
                $("#provinceSelect").val($("#district").val().substring(0, 2) + "**********");
                initCity($("#provinceSelect").val());
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown){
        }
    });
}

jQuery(document).ready(function() {
    jQuery.validator.addMethod("mobile", function (value, element) {
        var length = value.length;
        var mobile = /^1[3|4|5|6|7|8|9]{1}[0-9]{9}$/;
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请输入正确的手机号码");

    jQuery.validator.addMethod("bankCardNoVali", function (value, element) {
        var length = value.length;
        // 15-18 位
        var bankCardNo = /^[1-9]\d{14,19}$/;
        return this.optional(element) || (length > 14 && length < 20 && bankCardNo.test(value));
    }, "请输入正确的银行卡号");

    jQuery.validator.addMethod("businessLicenseNoVali", function (value, element) {
        var length = value.length;
        // 15 或 18 位
        var bankCardNo = /(^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$)|(^\d{15}$)/;
        return this.optional(element) || ((length == 15 && bankCardNo.test(value)) | ((length == 18 && bankCardNo.test(value))));
    }, "请输入正确的营业执照号");

    jQuery.validator.addMethod("realNameVali", function(value, element) {
        var frequentContactor = /^[\u4E00-\u9FA5A-Za-z_]+·/;
        var frequent = /^[\u4E00-\u9FA5A-Za-z_]+$/;
        return this.optional(element) || (frequentContactor.test(value) || frequent.test(value));
    }, "请输入正确的姓名格式!(可以输入中文、英文)");

    jQuery.validator.addMethod("emailVali", function (value, element) {
        var mobile = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
        return this.optional(element) || mobile.test(value);
    }, "请输入正确的邮箱地址");
    jQuery.validator.addMethod("idCardVali", function (value, element) {
        var mobile = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/;
        return this.optional(element) || mobile.test(value);
    }, "请输入正确的身份证号");

    jQuery.validator.addMethod("dateRangeVali", function (value, element) {
        var id = element.id;
        if (id == "idCardTimeEnd") {
            return idCardTimeEnd.value > idCardTimeStart.value;
        } else if (id == "businessLicenseTimeEnd") {
            return businessLicenseTimeEnd.value > businessLicenseTimeStart.value;
        } else if (id == "juridicalPersonIdCardTimeEnd" && $("#isCorporate").val() == 2) {
            return juridicalPersonIdCardTimeEnd.value > juridicalPersonIdCardTimeStart.value;
        } else {
            return true;
        }
    }, "截止日大于起始日");

    jQuery.validator.addMethod("preBankCodeVali", function (value, element) {
        var bankCode = $("#bankCode").val();
        var isNull =  (bankCode != "" && bankCode != null);
        return isNull;
    }, "请先在上一下拉框选择对应银行");

    jQuery.validator.addMethod("isCorporateVali", function (value, element) {
        var $isCorporate = $("#isCorporate").val();
        if ($isCorporate == 2) {
            if (value == null || value == "") {
                return false;
            } else {
                return true;
            }
        }
    }, "当不是法人收款时不能为空");

    jQuery.validator.addMethod("imgRequiredVali", function (value, element) {
        var files = element.files;
        var imgFile = files[0];

        if (imgFile == null) {
            return false;
        } else {
            return true;
        }
    }, "图片不能为空");

    AjaxServer.init();
});