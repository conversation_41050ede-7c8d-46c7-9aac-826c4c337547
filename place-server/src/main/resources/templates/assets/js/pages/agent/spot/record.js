"use strict";
var KTDatatablesDataSourceAjaxServer = function() {

	var startTime = moment().subtract(6, "days").format("YYYY-MM-DD") + " 00:00:00";
	var endTime = moment().startOf('day').format("YYYY-MM-DD") + " 23:59:59";

	var dateRangePicker = function () {
		console.info("loading......dateRangePicker");
		$("#kt_daterangepicker_1").daterangepicker({
				language:  'zh-CN',
				buttonClasses: 'btn',
				applyClass: 'btn-primary',
				cancelClass: 'btn-secondary',
				startDate: moment().subtract('days', 6),
				endDate: moment().startOf('day'),
				ranges : {
					'今日': [moment().startOf('day'), moment()],
					'昨日': [moment().subtract('days', 1).startOf('day'), moment().subtract('days', 1).endOf('day')],
					'最近7日': [moment().subtract('days', 6), moment()],
					'最近30日': [moment().subtract('days', 29), moment()],
					'本月': [moment().startOf("month"),moment().endOf("month")],
					'上个月': [moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]
				},
				locale: {
					format: "YYYY-MM-DD",
					applyLabel: '确认',
					cancelLabel: '取消',
					fromLabel: '从',
					toLabel: '到',
					customRangeLabel: '选择时间',
					daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
					monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
				}},
			function (start, end, lable) {
				console.info("A new date selection was made: " + start.format("YYYY-MM-DD") + " to " + end.format("YYYY-MM-DD"));
				startTime = start.format("YYYY-MM-DD") + " 00:00:00";
				endTime = end.format("YYYY-MM-DD") + " 23:59:59";
				// $('#kt_daterangepicker_1').val(start.format("YYYY-MM-DD") + " to " + end.format("YYYY-MM-DD"));
			});
	};

	var initTable1 = function() {
		var type = $('#type').val();
		$('#kt_table_1').DataTable({
			language: {
				"url": "http://assets.topfreeweb.net/assets/vendors/custom/Chinese.json"
		    },
		    serverSide: true, // 开启服务器模式，必须
			responsive: true, // 启用和配置响应式
			processing: true, // 是否显示加载中提示
			searching: false, // 开启搜索框
			lengthChange: false, // 开启每页设置显示数量
			searchDelay: 1000, // 搜索框change时的延迟
			ordering: false, // 字段排序
			paging: true,
			destroy: true,
			ajax: {
				url: '/place-server/agent/spot/queryPageLogAgentBalanceBySpot',
				type:'GET',
				data: {
					"startTime": startTime,
					"endTime": endTime,
					"type": type
				},
			},
			columns: [
				{data: 'created'}, // 时间
				{data: 'type'}, // 登入账号
				{data: 'spot'}, // 金额
				{data: 'accountSpot'}, // 操作后余额
				{data: 'createName'}, // 操作人姓名
				{data: 'detail'}, // 详情
			],
			columnDefs: [
				{
					targets: '_all', // 全选
					className: "text-center", //居中
				},
				{
					targets: 0,
					render: function (data, type, row) {
						if (data != null) {

							var temp;
							if (data.length < 6) {
								temp = moment([ data[0], data[1] - 1, data[2], data[3], data[4], 0]);
							} else {
								temp = moment([ data[0], data[1] - 1, data[2], data[3], data[4], data[5] ]);
							}

							//var temp = moment([ data[0], data[1] - 1, data[2], data[3], data[4], data[5] ]);
							return '<span style="color: #000000;"> ' + moment(temp).format("YYYY-MM-DD HH:mm:ss") + '</span>';
						} else {
							return "";
						}
					}
				},
				{
					targets: 1,
					render: function (data, type, row) {
						if (data===0) {
							return '<span style="color: #000000;"> ' + "充值" + '</span>';
						} else if (data===1){
							return '<span style="color: #000000;"> ' + "买点" + '</span>';
						} else if (data===2){
							return '<span style="color: #000000;"> ' + "向下级代理分配点数" + '</span>';
						} else if (data===3){
							return '<span style="color: #000000;"> ' + "上级代理授予点数" + '</span>';
						} else {
							return '<span style="color: #000000;"> ' + "延期扣点" + '</span>';
						}
					}
				},
			],
		});
	};

	// 页面查询按钮
	$('#balanceSearch').on('click', function(e) {
		e.preventDefault();
		initTable1();

	});

	// 代理商余额变更记录导出
	$('#balanceChangeExport').on('click', function(e) {
		e.preventDefault();

		var type = $('#type').val();

		swal.fire({
			title: "正在导出......",
			showConfirmButton: false,
			allowOutsideClick: false,
			allowEscapeKey: false,
			allowEnterKey: false,
		});

		var method = 'get';//请求方法
		var url = '/place-server/agent/spot/export?startTime=' + startTime + '&endTime=' + endTime + '&type=' + type;//请求url
		var xhr = new XMLHttpRequest();//定义一个XMLHttpRequest对象
		xhr.open(method, url, true);
		xhr.send(null);
		xhr.responseType = 'blob';//设置ajax的响应类型为blob
		xhr.onload = function ()//当请求完成，响应就绪进入
		{
			swal.close();
			if (this.status === 200)//当响应状态码为200时进入
			{
				var blob = this.response;//获取响应返回的blob对象
				//这一段用来判断是否是IE浏览器，因为下面有些代码不支持IE
				if (typeof window.navigator.msSaveBlob !== 'undefined') {
					window.navigator.msSaveBlob(blob, "代理商点数变更记录.xlsx");
					return;
				}
				var a = document.createElement('a');//在dom树上创建一个a标签
				var url = window.URL.createObjectURL(blob);//我的理解是生成一个相对于浏览器的虚拟url，用于指向传入的blob对象，让浏览器可以通过这个url找到这个blob对象
				a.href = url;//将url赋值给a标签的href属性
				a.download = '代理商点数变更记录.xlsx';//设置设置下载文件的名称

				a.click();//主动触发a标签点击事件
			}
		};
	});

	return {
		// main function to initiate the module
		init: function() {
			initTable1();
			dateRangePicker();
		},
	};

}();

jQuery(document).ready(function() {
	KTDatatablesDataSourceAjaxServer.init();
});