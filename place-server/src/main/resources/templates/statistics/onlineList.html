<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<!-- begin::Head -->
<head>
	<meta charset="utf-8" />
	<#include "/base/titleHeader.html">
	<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
	<link href="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
	<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
<!-- begin:: Page -->

<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->
<div class="kt-grid kt-grid--hor kt-grid--root">
	<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

			<!-- begin:: Header -->
			<#include "/base/header.html">
			<!-- end:: Header -->

			<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
				<div class="kt-container  kt-container--fluid ">

					<!-- begin:: Aside -->
					<#include "/base/aside.html">
					<!-- end:: Aside -->

					<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">

						<!-- begin:: Subheader -->
						<div class="kt-subheader   kt-grid__item" id="kt_subheader">
							<div class="kt-container  kt-container--fluid ">
								<div class="kt-subheader__main">
									<h3 class="kt-subheader__title">经营统计</h3>
									<span class="kt-subheader__separator kt-subheader__separator--v"></span>
									<div class="kt-subheader__group" id="kt_subheader_search">
										<span class="kt-subheader__desc" id="kt_subheader_total">上网统计</span>
									</div>
								</div>
								<div class="kt-subheader__toolbar">
									<a href="javascript:history.back(-1);" class="btn btn-default btn-bold">返回</a>
								</div>
							</div>
						</div>
						<!-- end:: Subheader -->

						<!-- begin:: Content -->
						<div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
							<div class="kt-portlet kt-portlet--mobile">
								<div class="kt-portlet__body">
									<ul class="nav nav-tabs  nav-tabs-line nav-tabs-bold nav-tabs-line-3x nav-tabs-line-success" role="tablist">
										<li class="nav-item">
											<a class="nav-link active" data-toggle="tab" href="#kt_tabs_byDay" role="tab"><i class="la la-info-circle"></i>日统计</a>
										</li>
										<li class="nav-item">
											<a class="nav-link" data-toggle="tab" href="#kt_tabs_byMonth" role="tab"><i class="la la-credit-card"></i>月统计</a>
										</li>
									</ul>

									<div class="tab-content">
										<div class="tab-pane active" id="kt_tabs_byDay" role="tabpanel">
											<form class="kt-form kt-form--fit kt-margin-b-20">
												<div class="row kt-margin-b-20">
													<div class="col-lg-3 kt-margin-b-10-tablet-and-mobile">
														<label>查询起止时间:</label>
														<div class='input-group pull-right' id='kt_daterangepicker_4'>
															<input type='text'  class="form-control kt-input" id="dateType" readonly/>
															<div class="input-group-append">
																<span class="input-group-text"><i class="la la-calendar-check-o"></i></span>
															</div>
														</div>
													</div>

													<div class="ol-lg-1 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile" style="margin-left: 2%;margin-bottom: 0px;margin-top: 25px;">
														<button type="button" class="btn btn-success btn-wide" id="kt_search">查询</button>
													</div>
													<div class="ol-lg-4 col-md-4 col-sm-4">

													</div>
													<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-top: 25px;">
														<button id="exportOnlineList" class="btn btn-brand btn-elevate btn-icon-sm" data-right="${haveAuthority?c}">
															<i class="la la-plus"></i> 导出
														</button>
													</div>
												</div>
											</form>
											<table style="text-align: center;" class="table table-striped table-bordered table-hover table-checkable" id="kt_table_1">
												<thead>
												<tr bgcolor="#005699">
													<th style="color: #FFFFFF">日期</th>
													<th style="color: #FFFFFF">合计消耗网费</th>
													<!--											<th style="color: #FFFFFF">消费本金</th>-->
													<!--											<th style="color: #FFFFFF">消费奖励</th>-->
													<th style="color: #FFFFFF">上机次数</th>
													<th style="color: #FFFFFF">上机人数</th>
													<th style="color: #FFFFFF">上机时长</th>
													<th style="color: #FFFFFF">开临时卡</th>
													<th style="color: #FFFFFF">开会员卡</th>
													<th style="color: #FFFFFF">包时次数</th>
													<th style="color: #FFFFFF">包时金额</th>
												</tr>
												</thead>
											</table>
										</div>
										<div class="tab-pane" id="kt_tabs_byMonth" role="tabpanel">
											<form class="kt-form kt-form--fit kt-margin-b-20">
												<div class="row kt-margin-b-20">

													<div class="col-lg-3 kt-margin-b-10-tablet-and-mobile">
														<label>查询起止时间:</label>
														<select class="form-control" id="queryTime">
															<option value="1">近1个月</option>
															<option value="3">近3个月</option>
															<option value="6"selected>近6个月</option>
														</select>
													</div>

													<div class="ol-lg-1 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile" style="margin-left: 2%;margin-bottom: 0px;margin-top: 25px;">
														<button type="button" class="btn btn-success btn-wide" id="month_kt_search">查询</button>
													</div>
													<div class="ol-lg-4 col-md-4 col-sm-4">

													</div>
													<div class="ol-lg-2 col-md-2 col-sm-2" style="margin-top: 25px;">
														<button id="exportMonthOnlineList" class="btn btn-brand btn-elevate btn-icon-sm" data-right="${haveAuthority?c}">
															<i class="la la-plus"></i> 导出
														</button>
													</div>
												</div>
											</form>
											<table style="text-align: center;" class="table table-striped table-bordered table-hover table-checkable" id="month_kt_table_1">
												<thead>
												<tr bgcolor="#005699">
													<th style="color: #FFFFFF">日期</th>
													<th style="color: #FFFFFF">合计消耗网费</th>
													<th style="color: #FFFFFF">上机次数</th>
													<th style="color: #FFFFFF">上机人数</th>
													<th style="color: #FFFFFF">上机时长</th>
													<th style="color: #FFFFFF">开临时卡</th>
													<th style="color: #FFFFFF">开会员卡</th>
													<th style="color: #FFFFFF">包时次数</th>
													<th style="color: #FFFFFF">包时金额</th>
												</tr>
												</thead>
											</table>
										</div>

									</div>

									<!--end: Datatable -->
								</div>
							</div>
						</div>
						<!-- end:: Content -->
					</div>
				</div>
			</div>
			<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
		</div>
	</div>
</div>

<!-- end:: Page -->

<#include "/base/script.html">

<!--begin::Page Vendors(used by this page) -->

<script src="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.js" type="text/javascript"></script>
<script src="/place-server/assets/js/pages/statistics/monthOnlineList.js" type="text/javascript"></script>
<script src="/place-server/assets/js/pages/statistics/onlineList.js" type="text/javascript"></script>
<!--end::Page Scripts -->

</body>
<!-- end::Body -->
</html>
