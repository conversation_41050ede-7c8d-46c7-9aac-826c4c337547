<!DOCTYPE html>
<html lang="zh-cmn-Hans" xmlns:0px>
<!-- begin::Head -->
<head>
<meta charset="utf-8" />
<title>四维管家 | 门店管理</title>
<#include "/base/css.html"> <!--begin::Page Vendors Styles(used by this page) -->
<link href="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<!--end::Page Vendors Styles -->
</head>
<!-- end::Head -->

<!-- begin::Body -->
<body class="kt-page-content-white kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--transparent kt-aside--enabled kt-aside--fixed kt-page--loading">
	<!-- begin:: Page -->

	<!-- begin:: Header Mobile --> <#include "/base/m-header.html"> <!-- end:: Header Mobile -->
	<div class="kt-grid kt-grid--hor kt-grid--root">
		<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
			<div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">

				<!-- begin:: Header --> 
				<#include "/base/header.html"> 
				<!-- end:: Header -->

				<div class="kt-body kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-grid--stretch" id="kt_body">
					<div class="kt-container  kt-container--fluid ">

						<!-- begin:: Aside --> 
						<!-- 左侧菜单 -->
						<#include "/base/agent/aside.html">
						<!-- end:: Aside -->

						<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">
							<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 form-group row" style="margin-top: 30px;margin-left: 10px;">
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-brand" style="height: 223px;">
										<div class="kt-portlet__body kt-portlet__body--fluid" style="padding-right: 0px;">
											<div class="kt-widget26">
												<div class="kt-widget26__content">
													<span class="kt-widget26__number">公告</span>
													<span class="kt-widget26__desc">测试公告一&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;01-28</span>
													<span class="kt-widget26__desc">测试公告二&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;01-28</span>
													<span class="kt-widget26__desc">测试公告三&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;01-28</span>
													<span class="kt-widget26__desc">测试公告四&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;01-28</span>
													<span class="kt-widget26__desc">测试公告五&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;01-28</span>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-success" style="height: 223px;">
										<div class="kt-portlet__body kt-portlet__body--fluid" style="padding-right: 0px;">
											<div class="kt-widget26">
												<div class="kt-widget26__content">
													<span class="kt-widget26__number">账号信息</span>
													<#if PLACE_SERVER_AGENT.model==1>
													<span class="kt-widget26__desc">代理名称:<span style="margin-left: 20px;"><#if (placeAgent.id)??>${placeAgent.agentName}</#if></span></span>
													<#else>
													<span class="kt-widget26__desc">大区名称:<span style="margin-left: 20px;"><#if (placeAgent.id)??>${placeAgent.agentName}</#if></span></span>
													</#if>
													<span class="kt-widget26__desc">登入账号:<span style="margin-left: 20px;"><#if (placeAgent.id)??>${placeAgent.loginName}</#if></span></span>
													<span class="kt-widget26__desc">真实姓名:<span style="margin-left: 20px;"><#if (placeAgent.id)??>${placeAgent.accountName}</#if></span></span>
													<span class="kt-widget26__desc">管理区域:<span style="margin-left: 20px;" id="regionName"></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" data-toggle="modal" data-target="#kt_modal_1">详情</a></span>
													<#if PLACE_SERVER_AGENT.model==1>
													<span class="kt-widget26__desc">代理级别:<span style="margin-left: 20px;" ></span><#if PLACE_SERVER_AGENT.level==4>总代<#elseif PLACE_SERVER_AGENT.level==3>次级代理(省)<#elseif PLACE_SERVER_AGENT.level==2>次级代理(市)<#else>次级代理(区)</#if></span>
													</#if>
													<input type="text" id="regionCode" value="<#if (placeAgent)??>${placeAgent.region}</#if>" hidden>
													<input type="text" id="codeName" value="<#if (placeAgent)??>${placeAgent.regionName}</#if>" hidden>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-success" style="height: 223px;">
										<div class="kt-portlet__body kt-portlet__body--fluid" style="padding-right: 0px;">
											<div class="kt-widget26">
												<div class="kt-widget26__content">
													<span class="kt-widget26__number">数据汇总</span>
													<span class="kt-widget26__desc">代理场所总数:<span style="margin-left: 5px;" id="sumPlaceProfile"></span></span>
													<span class="kt-widget26__desc">近15天快到期场所数:<span style="margin-left: 20px;" id="sumPlaceProfileByDay"></span></span>
													<span class="kt-widget26__desc">活跃门店数:<span style="margin-left: 20px;" id="sumActivePlaceProfile"></span></span>
													<span class="kt-widget26__desc">活跃终端数:<span style="margin-left: 20px;" id="sumActivePlaceClient"></span></span>
													<span class="kt-widget26__desc">已过期门店数:<span style="margin-left: 20px;" id="sumPlaceProfileByExpired"></span></span>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-sm-3 col-md-3 col-lg-3">
									<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-success" style="height: 223px;">
										<div class="kt-portlet__body kt-portlet__body--fluid" style="padding-right: 0px;padding-top: 10px;">
											<div class="kt-widget26">
												<div class="kt-widget26__content">
													<div class="form-group row" style="background-color: #A682A5;height: 50px;width: 345px;border-radius: 5px;">
														<span class="kt-widget26__number" style="margin-top: 5px;margin-left: 50px;color: white;font-size: 18px">本月新增场所(家)
															<span class="kt-widget26__number" style="margin-left: 15px;margin-top: 10px;color: yellow" id="sumNewlyPlaceProfileByMonth"></span>
														</span>
													</div>
													<#if (PLACE_SERVER_ACCOUNT.type == 3)>
													<div class="form-group row" style="background-color: #EC4F4F;height: 50px;width: 345px;border-radius: 5px;">
														<span class="kt-widget26__number" style="margin-top: 5px;margin-left: 50px;color: white;font-size: 18px">本月新增终端(台)<span class="kt-widget26__number" style="margin-left: 20px;margin-top: 10px;color: yellow" id="sumNewlyPlaceClientByMonth"></span></span>
													</div>
													<div class="form-group row" style="background-color: #5E8667;height: 50px;width: 345px;border-radius: 5px;">
														<span class="kt-widget26__number" style="margin-top: 5px;margin-left: 50px;color: white;font-size: 18px">本月新增用户(个)<span class="kt-widget26__number" style="margin-left: 20px;margin-top: 10px;color: yellow" id="sumNewlyMemberByMonth"></span></span>
													</div>
													<#elseif (PLACE_SERVER_ACCOUNT.type == 0)>
													<div class="form-group row" style="background-color: #EC4F4F;height: 50px;width: 345px;border-radius: 5px;">
														<span class="kt-widget26__number" style="margin-top: 5px;margin-left: 50px;color: white;font-size: 18px">账户余额(元)<span class="kt-widget26__number" style="margin-left: 20px;margin-top: 10px;color: yellow">${(placeAgent.accountBalance / 100) ?c}</span></span>
													</div>
													<div class="form-group row" style="background-color: #5E8667;height: 50px;width: 345px;border-radius: 5px;">
														<span class="kt-widget26__number" style="margin-top: 5px;margin-left: 50px;color: white;font-size: 18px">剩余点数(个)<span class="kt-widget26__number" style="margin-left: 20px;margin-top: 10px;color: yellow">${placeAgent.accountSpot}</span></span>
													</div>
													</#if>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row" style="margin-bottom: 0px;">
							<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4">
							</div>

							<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;margin-left: 50px;">
								<select class="form-control" id="provinceSelect" name="province">
									<option value="">请选择</option>
								</select>
							</div>
							<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
								<select class="form-control" id="citySelect" name="city">
									<option value="">请选择</option>
								</select>
							</div>
							<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 form-group row" style="margin-bottom: 0px;">
								<select class="form-control" id="countrySelect" name="country">
									<option value="">请选择</option>
								</select>
							</div>
							<div class="col-xl-2 col-lg-2 col-md-2 col-sm-2">
							</div>
							<div class="col-xl-1 col-lg-1 col-md-1 col-sm-1 input-group date">
								<input type="text" id="kt_datepicker" class="form-control" autocomplete="off" />
							</div>
							<div class="ol-lg-2 col-md-2 col-sm-2 kt-margin-b-10-tablet-and-mobile">
								<button type="button" class="btn btn-success btn-wide" id="searchSubmit" style="margin-top: 0%;">查询</button>
							</div>
						</div>
						<ul class="nav nav-tabs  nav-tabs-line nav-tabs-bold nav-tabs-line-3x nav-tabs-line-success kt-content form-group row" role="tablist">
							<li class="nav-item" style="margin-left: 30px;">
								<a class="nav-link active" data-toggle="tab" href="#kt_tabs_1_1" role="tab">
									<i class="la la-gear"></i>门店
								</a>
							</li>
							<li class="nav-item" id="li_kt_tabs_1_2">
								<a class="nav-link" data-toggle="tab" href="#kt_tabs_1_2" role="tab"><i class="la la-info-circle"></i>客户端(开发中)</a>
							</li>
							<li class="nav-item" id="li_kt_tabs_1_3">
								<a class="nav-link" data-toggle="tab" href="#kt_tabs_1_3" role="tab"><i class="la la-info-circle"></i>用户(开发中)</a>
							</li>
						</ul>
						<div class="tab-pane active" id="kt_tabs_1_1" role="tabpanel">
							<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor">
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 form-group row" style="margin-top: 30px;margin-left: 10px;">
									<div class="col-sm-3 col-md-3 col-lg-3">
										<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-brand" style="height: 460px;">
											<div id="myChart">

											</div>
										</div>
									</div>
									<div class="col-sm-9 col-md-9 col-lg-9">
										<div class="kt-portlet kt-portlet--height-fluid-half kt-portlet--border-bottom-brand" style="height: 460px;">

											<div id="myChart2">

											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- begin:: Footer --> <#include "/base/footer.html"> <!-- end:: Footer -->
			</div>
		</div>
	</div>

	<#include "/base/script.html">

	<div class="modal fade cashier-model" id="kt_modal_1" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-xl " role="document">
			<div class="modal-content">
				<form id="addProfileForm">
					<div class="modal-header" style="padding-top: 0px;padding-bottom: 0px;">
						<label class="col-form-label" style="font-size: 1.5rem;"><b>管辖区域详情:</b></label>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body" style="padding-top: 0px;">
						<div class="form-group">
							<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12" id="region">
								<div class="form-group row" style="margin-left: 350px;margin-bottom: 10px;">

								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/locales/bootstrap-datepicker.zh-CN.min.js"></script>
	<script src="/place-server/assets/js/pages/sys/echarts.min.js" type="text/javascript"></script>
	<script src="http://assets.topfreeweb.net/assets/vendors/custom/datatables/datatables.bundle.js" type="text/javascript"></script>
	<!--部署使用版本-->
	<script src="/place-server/assets/js/pages/agent/dashboard.js" type="text/javascript"></script>

</body>
<!-- end::Body -->
</html>
