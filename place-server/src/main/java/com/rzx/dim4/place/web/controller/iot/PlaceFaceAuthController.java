//package com.rzx.dim4.place.web.controller.iot;
//
//import com.google.gson.Gson;
//import com.rzx.dim4.base.bo.iot.LogFaceAuthBO;
//import com.rzx.dim4.base.dto.PagerDTO;
//import com.rzx.dim4.base.response.GenericResponse;
//import com.rzx.dim4.base.response.ResponsePage;
//import com.rzx.dim4.base.service.feign.IotServerService;
//import com.rzx.dim4.base.utils.ExcelUtil;
//import com.rzx.dim4.base.utils.SignUtils;
//import com.rzx.dim4.place.entity.PlaceAccount;
//import com.rzx.dim4.place.entity.PlaceAgent;
//import com.rzx.dim4.place.service.PlaceAccountService;
//import com.rzx.dim4.place.service.PlaceAgentService;
//import com.rzx.dim4.place.web.cons.WebConstants;
//import com.rzx.dim4.place.web.controller.admin.BaseController;
//import com.rzx.dim4.place.web.vo.ExportFaceAuthVO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import javax.servlet.http.HttpSession;
//import java.io.IOException;
//import java.util.*;
//
//@Slf4j
//@Controller
//@RequestMapping("/iot/faceAuth")
//public class PlaceFaceAuthController extends BaseController {
//
//    @Autowired
//    IotServerService iotServerService;
//
//    @Autowired
//    PlaceAgentService placeAgentService;
//
//    @Autowired
//    PlaceAccountService placeAccountService;
//
//    /**
//     * 实名付费订单列表
//     *
//     * @return String
//     */
///*    @GetMapping("/list")
//    public String list() {
//        return "iot/faceAuth/list";
//    }*/
//
//    /**
//     * 场所实名配置
//     */
//    /*@ResponseBody
//    @GetMapping("/faceAuths")
//    public ResponsePage<LogFaceAuthBO> faceAuths  (HttpSession httpSession, @RequestParam(value = "draw") int draw,
//                                                   @RequestParam(name = "length", defaultValue = "10") int length,
//                                                   @RequestParam(name = "start", defaultValue = "0") int start,
//                                                   @RequestParam(name = "type", required = false, defaultValue = "") String type,
//                                                   @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
//                                                   @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
//                                                   @RequestParam(name = "idName", required = false, defaultValue = "") String idName,
//                                                   @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
//                                                   @RequestParam(name = "source", required = false, defaultValue = "") String source,
//                                                   @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
//                                                   @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
//                                                   @RequestParam(name = "startTime", required = false) String startTime,
//                                                   @RequestParam(name = "endTime", required = false) String endTime,
//                                                   @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode) {
//        PlaceAccount loginAccount = (PlaceAccount) httpSession.getAttribute(WebConstants.PLACE_SERVER_ACCOUNT);
//        String accountId = loginAccount.getAccountId();
//        Optional<PlaceAccount> placeAccountOpt = placeAccountService.findByAccountId(accountId);
//        if(!placeAccountOpt.isPresent()) {
//        	return new ResponsePage<>(1, 0, new ArrayList<>());
//        }
//        int accountType = placeAccountOpt.get().getType();
//        if (!(accountType == 0 || accountType == 3)) {
//        	return new ResponsePage<>(1, 0, new ArrayList<>());
//        }
//
//    	int page = start / length;
//        Map<String, Object> map = new HashMap<>();
//        setAgentAccountAuths(map, accountType, accountId);
//
//        map.put("placeId", placeId);
//        map.put("type", type);
//        map.put("idName", idName);
//        map.put("orderId", orderId);
//        map.put("effectived", effectived);
//        map.put("source", source);
//        map.put("placeAuthFeeType", placeAuthFeeType);
//        map.put("idNumber", idNumber);
//        map.put("startTime", startTime);
//        map.put("endTime", endTime);
//        map.put("searchRegionCode", searchRegionCode);
//        map.put("timestamp", System.currentTimeMillis()/1000 +"");
//        map.put("sign", SignUtils.generateSign4common(map));
//
//        GenericResponse<PagerDTO<LogFaceAuthBO>> result = iotServerService.queryFaceAuths(map, length,
//                page);
//        if (result.isResult()) {
//        	List<LogFaceAuthBO> list = result.getData().getList();
//        	list.forEach(e -> {
//        		e.setIdNumber(e.getIdNumber().substring(0, 6) + "********" + e.getIdNumber().substring(14));
//                e.setIdName(StringUtils.isEmpty(e.getIdName()) ? "" : e.getIdName().substring(0, 1) + getRepeat(e.getIdName().length()-1, "*"));
//        	});
//            return new ResponsePage<>(++draw, result.getData().getTotal(), list);
//        }
//        return new ResponsePage<>(1, 0, new ArrayList<>());
//    }*/
//
//    private void setAgentAccountAuths(Map<String, Object> map, int accountType, String accountId) {
//        Optional<PlaceAgent> placeAgentOpt = placeAgentService.findByAccountId(accountId);
//        List<String> authPlaceids = new ArrayList<String>();
//        List<String> authRegioncodes = new ArrayList<String>();
//        log.info("设置  ------------------accountType：{}", accountType);
//
//        if(placeAgentOpt.isPresent()) {
//        	PlaceAgent placeAgent = placeAgentOpt.get();
//            log.info("accountType：{}, placeAgent: {}", accountType, new Gson().toJson(placeAgent));
//
//        	if(accountType == 0) {
//            	// 代理
//            	if(StringUtils.isEmpty(placeAgent.getPlaceIds())) {
//                    authPlaceids = placeAgentService.getAllAgentPlaceId(placeAgent.getCreater(), placeAgent.getRegion());
//            	}else {
//                    authPlaceids.addAll(Arrays.asList(placeAgent.getPlaceIds().split(",")));
//                }
//
//                log.info("authPlaceids: " + authPlaceids.toString());
//            	map.put("authPlaceids", authPlaceids);
//            }else if(accountType == 3) {
//            	// 大区代理
//            	if(StringUtils.isEmpty(placeAgent.getRegion())) {
//            		authRegioncodes.add("-");
//            	}else {
//            		authRegioncodes.addAll(Arrays.asList(placeAgent.getRegion().split(",")));
//            	}
//            	map.put("authRegioncodes", authRegioncodes);
//            }
//        }
//	}
//
//    /**
//     * 人脸记录列表导出
//     */
//    /*@RequestMapping(value="/export", method= RequestMethod.POST)
//    @ResponseBody
//    public void exportFaceAuths   (HttpSession httpSession, @RequestParam(name = "type", required = false, defaultValue = "") String type,
//                                   @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
//                                   @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
//                                   @RequestParam(name = "idName", required = false, defaultValue = "") String idName,
//                                   @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
//                                   @RequestParam(name = "source", required = false, defaultValue = "") String source,
//                                   @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
//                                   @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
//                                   @RequestParam(name = "startTime", required = false) String startTime,
//                                   @RequestParam(name = "endTime", required = false) String endTime,
//                                   @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode,
//                                   @RequestParam(name = "dataPage", required = false, defaultValue = "0") int dataPage,
//                                   HttpServletResponse response) {
//
//        int size = 10000;
//        int start = dataPage * size;
//
//        ResponsePage<LogFaceAuthBO> orders = faceAuths(httpSession,1,size,start,type,placeId,orderId,idName,effectived,source,placeAuthFeeType,idNumber,startTime,endTime,searchRegionCode);
//        List<LogFaceAuthBO> logFaceAuthBOS = orders.getData();
//        if(orders.getData().size() == size) {
//        	response.setIntHeader("isHasData", 1);
//        }else {
//        	response.setIntHeader("isHasData", 0);
//        }
//
//        List<ExportFaceAuthVO> exportFaceAuthVOS = new ArrayList<>();
//        logFaceAuthBOS.forEach(e->{
//            ExportFaceAuthVO exportFaceAuthVO = new ExportFaceAuthVO();
//            BeanUtils.copyProperties(e, exportFaceAuthVO);
//            if (e.getType() == 0) {
//            	exportFaceAuthVO.setType("网吧");
//            } else if (e.getType() == 1) {
//            	exportFaceAuthVO.setType("电竞酒店");
//            } else if (e.getType() == 2) {
//            	exportFaceAuthVO.setType("网租");
//            } else if (e.getType() == 3) {
//            	exportFaceAuthVO.setType("普通场所");
//            } else if (e.getType() == 4) {
//            	exportFaceAuthVO.setType("营销大师");
//            } else if (e.getType() == 5) {
//            	exportFaceAuthVO.setType("九威");
//            } else if (e.getType() == 6) {
//            	exportFaceAuthVO.setType("大巴掌");
//            } else if (e.getType() == 7) {
//            	exportFaceAuthVO.setType("PMS");
//            } else if (e.getType() == 8) {
//            	exportFaceAuthVO.setType("龙管家");
//            } else if (e.getType() == 301) {
//            	exportFaceAuthVO.setType("万象");
//            } else if (e.getType() == 302) {
//            	exportFaceAuthVO.setType("嘟嘟牛");
//            } else if (e.getType() == 303) {
//            	exportFaceAuthVO.setType("轻网联盟");
//            } else if (e.getType() == 304) {
//            	exportFaceAuthVO.setType("佳星");
//            } else if (e.getType() == 305) {
//            	exportFaceAuthVO.setType("百果树");
//            } else if (e.getType() == 306) {
//            	exportFaceAuthVO.setType("奥比特");
//            } else if (e.getType() == 307) {
//            	exportFaceAuthVO.setType("丕微");
//            }  else {
//            	exportFaceAuthVO.setType("未知类型");
//            }
//
//            // 支付时间
////            if (!StringUtils.isEmpty(e.getPayTime())) {
////                exportFaceAuthVO.setPayTime(e.getPayTime().toString().substring(0,19));
////            }
//
//            exportFaceAuthVO.setCreated(e.getCreated().toString().substring(0,19));
//
//            // 认证有效时间
////            if (!StringUtils.isEmpty(e.getEffectiveTime())) {
////                exportFaceAuthVO.setEffectiveTime(e.getEffectiveTime().toString().substring(0,19));
////            }
////
////            if (e.getEffectived() == 0) {
////                exportFaceAuthVO.setEffectived("未支付");
////            } else {
////                exportFaceAuthVO.setEffectived("已支付");
////            }
////
////            if (!StringUtils.isEmpty(e.getAuthFeeType())) {
////                exportFaceAuthVO.setPlaceAuthFeeType(e.getAuthFeeType().getName());
////                exportFaceAuthVO.setChargeFlag("是");
////            } else {
////                exportFaceAuthVO.setChargeFlag("否");
////            }
////
////            if (!StringUtils.isEmpty(e.getAuthFee())) {
////                exportFaceAuthVO.setAuthFee((float)e.getAuthFee() / 100);
////            }
//
//            exportFaceAuthVO.setDeviceId(getOrderSource(e.getDeviceId()));
//
//            exportFaceAuthVOS.add(exportFaceAuthVO);
//        });
//
//        try {
//            ExcelUtil.writeExcel(response, exportFaceAuthVOS, "人脸记录导出列表",
//                    "人脸记录导出列表", ExportFaceAuthVO.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                response.getOutputStream().close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//
//    }*/
//
//    private String getRepeat(int count, String repeatStr) {
//    	String dest = "";
//    	while(count > 0) {
//    		dest += repeatStr;
//    		count--;
//    	}
//		return dest;
//	}
//
//	private String getOrderSource(String buyerId) {
//    	switch(buyerId) {
//    	case "wechatMiniByWyf":
//    		return "微信小程序";
//    	case "wechatMp":
//    		return "微信公众号";
//    	default:
//    		return "支付宝IOT";
//    	}
//	}
//
//    /*@ResponseBody
//    @GetMapping("/queryFaceStatistics")
//    public Map<String,Integer> queryFaceStatistics (HttpSession httpSession, @RequestParam(name = "type", required = false, defaultValue = "") String type,
//                                                    @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
//                                                    @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
//                                                    @RequestParam(name = "idName", required = false, defaultValue = "") String idName,
//                                                    @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
//                                                    @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
//                                                    @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
//                                                    @RequestParam(name = "startTime", required = false, defaultValue = "") String startTime,
//                                                    @RequestParam(name = "endTime", required = false, defaultValue = "") String endTime,
//                                                    @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode,
//                                                    @RequestParam(name = "source", required = false, defaultValue = "") String source,
//                                                    @RequestParam(name = "statName", required = false, defaultValue = "") String statName) {
//        PlaceAccount loginAccount = (PlaceAccount) httpSession.getAttribute(WebConstants.PLACE_SERVER_ACCOUNT);
//        String accountId = loginAccount.getAccountId();
//        Optional<PlaceAccount> placeAccountOpt = placeAccountService.findByAccountId(accountId);
//        if(!placeAccountOpt.isPresent()) {
//        	Map<String,Integer> retMap = new HashMap<>();
//        	retMap.put("authProfileNum", 0);
//        	retMap.put("authPeopleNum", 0);
//        	retMap.put("authNum", 0);
//        	return retMap;
//        }
//
//        int accountType = placeAccountOpt.get().getType();
//        if (!(accountType == 0 || accountType == 3)) {
//        	Map<String,Integer> retMap = new HashMap<>();
//        	retMap.put("authProfileNum", 0);
//        	retMap.put("authPeopleNum", 0);
//        	retMap.put("authNum", 0);
//        	return retMap;
//        }
//
//        Map<String, Object> map = new HashMap<>();
//        setAgentAccountAuths(map, accountType, accountId);
//        map.put("type", type);
//        map.put("placeId", placeId);
//        map.put("orderId", orderId);
//        map.put("idName", idName);
//        map.put("effectived", effectived);
//        map.put("placeAuthFeeType", placeAuthFeeType);
//        map.put("idNumber", idNumber);
//        map.put("startTime", startTime);
//        map.put("endTime", endTime);
//        map.put("searchRegionCode", searchRegionCode);
//        map.put("source", source);
//        map.put("timestamp", System.currentTimeMillis()/1000 +"");
//        map.put("statName", statName);
//        map.put("sign", SignUtils.generateSign4common(map));
//
//        return iotServerService.queryFaceStatistics(map);
//    }*/
//}
