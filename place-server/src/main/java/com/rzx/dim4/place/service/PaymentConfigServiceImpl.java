package com.rzx.dim4.place.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.payment.LdopayConfigApi;
import com.rzx.dim4.base.service.feign.payment.param.BindAccountBO;
import com.rzx.dim4.base.service.feign.payment.param.LdopayInfoBO;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.place.entity.PlaceProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/24
 **/
@Slf4j
@Service
public class PaymentConfigServiceImpl implements PaymentConfigService {

    @Autowired private LdopayConfigApi ldopayConfigApi;

    @Autowired private StringRedisTemplate stringRedisTemplate;

    @Autowired private PlaceProfileService placeProfileService;

    @Override
    public LdopayInfoBO queryAccount(String placeId, String mobile) {
        if (StringUtils.isEmpty(placeId) && StringUtils.isEmpty(mobile)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (!StringUtils.isEmpty(placeId)) {
            boolean localHavePaymentInfo = placeProfileService.havePaymentInfo(placeId);
            if (localHavePaymentInfo) {
                String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
                GenericResponse<ObjDTO<LdopayInfoBO>> response = ldopayConfigApi.queryAccount(requestTicket, placeId, mobile);
                if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
                    throw new ServiceException(ServiceCodes.getByCode(response.getCode()), response.getMessage());
                }

                return response.getData().getObj();
            } else {
                return null;
            }
        } else {
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<ObjDTO<LdopayInfoBO>> response = ldopayConfigApi.queryAccount(requestTicket, placeId, mobile);
            if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
                throw new ServiceException(ServiceCodes.getByCode(response.getCode()), response.getMessage());
            }

            return response.getData().getObj();
        }
    }

    @Override
    public BindAccountBO bind(String placeId, String merUsername) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(merUsername)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<PlaceProfile> placeProfileOptional = placeProfileService.findByPlaceId(placeId);
        PlaceProfile placeProfile = placeProfileOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BAD_PARAM));

        BindAccountBO bindAccountBO = doBindToLdopay(placeId, merUsername);

        placeProfile.setIsRegistered(1);
        placeProfile.setMerUsername(merUsername);
        placeProfile.setUpdated(LocalDateTime.now());
        PlaceProfile save = placeProfileService.save(placeProfile);
        log.info("placeProfile={}", new Gson().toJson(save));

        return bindAccountBO;
    }

    @Override
    public BindAccountBO doBindToLdopay(String placeId, String merUsername) {
        log.info("placeId={},merUsername={}", placeId, merUsername);

        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<ObjDTO<BindAccountBO>> response = ldopayConfigApi.bind(requestTicket, placeId, merUsername);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()), response.getMessage());
        }
        return response.getData().getObj();
    }
}
