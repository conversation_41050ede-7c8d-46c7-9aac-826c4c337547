package com.rzx.dim4.place.web.restController.admin.account;

import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.place.entity.PlaceAccount;
import com.rzx.dim4.place.entity.PlaceProfile;
import com.rzx.dim4.place.repository.PlaceAccountRepository;
import com.rzx.dim4.place.service.PlaceProfileService;
import com.rzx.dim4.place.service.admin.account.EmployeeService;
import com.rzx.dim4.place.web.controller.admin.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzx.dim4.place.web.cons.WebConstants.PLACE_SERVER_ACCOUNT;

@Slf4j
@RestController
@RequestMapping("/rspApi/admin/employee")
public class EmployeeController extends BaseController {

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private PlaceProfileService profileService;

    @Autowired
    private PlaceAccountRepository placeAccountRepository;


    /**
     * 获取账号列表数据
     *
     * @param start
     * @param length
     * @return
     */
    @GetMapping(value = "/list")
    public GenericResponse<PagerDTO<?>> accountDataTable(@RequestParam(value = "start", defaultValue = "0") Integer start,
                                                         @RequestParam(value = "length", defaultValue = "10") Integer length,
                                                         @RequestParam(name = "placeId") String placeId,
                                                         @RequestParam(name = "accountName", required = false) String accountName,
                                                         @RequestParam(name = "accountId", required = false) String accountId,
                                                         @RequestParam(name = "roleType", required = false) String roleType,
                                                         @RequestParam(name = "employedStatus", required = false) Integer employedStatus) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }


        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("placeId", placeId);
        if (!StringUtils.isEmpty(accountName)) {
            queryMap.put("accountName", accountName);
        }
        if (!StringUtils.isEmpty(accountId)) {
            queryMap.put("accountId", accountId);
        }
        if (!StringUtils.isEmpty(roleType)) {
            queryMap.put("roleType", roleType);
        }
        if (Objects.nonNull(employedStatus) && org.apache.commons.lang.StringUtils.isNotBlank(String.valueOf(employedStatus))) {
            queryMap.put("employedStatus", String.valueOf(employedStatus));
        }

        queryMap.put("type", "2");
        queryMap.put("deleted", "0");
        Pageable pageable = PageRequest.of(start, length, Sort.Direction.DESC, "id");
        Page<PlaceAccount> pager = employeeService.findAll(queryMap, pageable);
        List<PlaceAccountBO> bos = pager.getContent().stream().map(e -> e.toBO()).collect(Collectors.toList());
        List<String> placeIdList = bos.stream().map(PlaceAccountBO::getPlaceId).collect(Collectors.toList());
        List<PlaceProfile> manyPlaceProfile = profileService.findByPlaceIds(placeIdList);
        Map<String, String> placeIdToDisplayNameMap = manyPlaceProfile.stream()
                .collect(Collectors.toMap(
                        PlaceProfile::getPlaceId,       // key：placeId
                        PlaceProfile::getDisplayName    // value：displayName
                ));
        bos.forEach(item -> {
            item.setPlaceName(placeIdToDisplayNameMap.get(item.getPlaceId()));
        });
        return new GenericResponse<>(new PagerDTO<>((int) pager.getTotalElements(), bos));
    }

    /**
     * 提交修改/新增
     *
     * @param accountBo
     * @param session
     * @return
     */
    @PostMapping(value = "/saveOrUpdate")
    public GenericResponse<ObjDTO<PlaceAccountBO>> saveOrUpdate(@RequestBody PlaceAccountBO accountBo,
                                                                HttpSession session) {
        PlaceAccount loginSysAccount = (PlaceAccount) session.getAttribute(PLACE_SERVER_ACCOUNT);
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String loginPass = accountBo.getLoginPass();
        if (null == accountBo || accountBo.getPlaceId() == null) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if (StringUtils.isEmpty(accountBo.getMobile()) || StringUtils.isEmpty(accountBo.getAccountName())) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        // 防止越权操作
//        Optional<PlaceProfile> optPlaceProfile = profileService.findByPlaceId(accountBo.getPlaceId());
//        if (!optPlaceProfile.isPresent()
//                || !String.valueOf(optPlaceProfile.get().getCreater()).equals(loginSysAccount.getAccountId())) {
//            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
//        }

        PlaceAccount account = new PlaceAccount();
        if (Objects.nonNull(accountBo.getId())) {
            // 编辑
            Optional<PlaceAccount> accountOptional = employeeService.findById(accountBo.getId());
            if (!accountOptional.isPresent()) {
                return new GenericResponse<>(ServiceCodes.BAD_ID);
            }
            account = accountOptional.get();
            if (!StringUtils.isEmpty(loginPass)) {
                Dim4StringUtils.isValidPassword(accountBo.getLoginPass());
                account.setLoginPass(passwordEncoder.encode(loginPass));
            }
        } else {
            List<PlaceAccount> accountList = placeAccountRepository.findByMobileAndPlaceIdAndDeleted(accountBo.getMobile(), accountBo.getPlaceId(), 0);
            if (!accountList.isEmpty()) {
                return new GenericResponse<>("该场所已被该手机号注册");
            }
            if (StringUtils.isEmpty(loginPass)) {
                return new GenericResponse<>(ServiceCodes.NULL_PARAM);
            }
            Dim4StringUtils.isValidPassword(accountBo.getLoginPass());
            account.setLoginPass(passwordEncoder.encode(loginPass));
            account.setPlaceId(accountBo.getPlaceId());
            account.setType(accountBo.getType());
            account.setCreated(LocalDateTime.now());
            account.setEmployedStatus(1);
        }
        if (!StringUtils.isEmpty(accountBo.getNickName())) {
            account.setNickName(accountBo.getNickName());
        }
        if (!StringUtils.isEmpty(accountBo.getSex())) {
            account.setSex(accountBo.getSex());
        }
        if (!StringUtils.isEmpty(accountBo.getHeadImg())) {
            account.setHeadImg(accountBo.getHeadImg());
        }
        if (!StringUtils.isEmpty(accountBo.getIdNumber())) {
            account.setIdNumber(accountBo.getIdNumber());
        }
        if (!StringUtils.isEmpty(accountBo.getNation())) {
            account.setNation(accountBo.getNation());
        }
        if (accountBo.getHireDate() != null) {
            account.setHireDate(accountBo.getHireDate());
        }
        if (!StringUtils.isEmpty(accountBo.getIdcardFrontUrl())) {
            account.setIdcardFrontUrl(accountBo.getIdcardFrontUrl());
        }
        if (!StringUtils.isEmpty(accountBo.getIdcardBackUrl())) {
            account.setIdcardBackUrl(accountBo.getIdcardBackUrl());
        }
        if (StringUtils.isEmpty(accountBo.getLoginName())) {
            account.setLoginName(accountBo.getAccountName());
        }
        account.setAccountName(accountBo.getAccountName());
        account.setMobile(accountBo.getMobile());
        account.setUpdated(LocalDateTime.now());
        account.setRoleType(accountBo.getRoleType());

        PlaceAccount dto = employeeService.save(account);
        if (dto != null) {
            return new GenericResponse<>(new ObjDTO<>(dto.toBO()));
        }
        return new GenericResponse<>(ServiceCodes.NOT_FOUND);
    }

    /**
     * 详情
     *
     * @param placeId
     * @param accountId
     * @return
     */
    @GetMapping(value = "/detail")
    public GenericResponse<ObjDTO<PlaceAccountBO>> detail(@RequestParam(name = "placeId") String placeId,
                                                          @RequestParam(name = "accountId") String accountId) {
        Optional<PlaceAccount> accountOptional = employeeService.findByPlaceIdAndAccountId(placeId, accountId);
        if (!accountOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.PLACE_ACCOUNT_NOT_FOUND);
        }
        PlaceAccount placeAccount = accountOptional.get();
        placeAccount.setLoginPass("");
        return new GenericResponse<>(new ObjDTO<>(placeAccount.toBO()));
    }



    /**
     * 解绑定员工小程序
     *
     * @param placeAccount
     * @return
     */
    @PostMapping("/unBindMiniProgram")
    public GenericResponse<ObjDTO<PlaceAccountBO>> unBindMiniProgram(@RequestParam(required = false,name = "placeId") String placeId,
                                                                     @RequestBody PlaceAccount placeAccount) {
        if (StringUtils.isEmpty(placeId)) {
            placeId = placeAccount.getPlaceId();
        }
        String mobile = placeAccount.getMobile();
        return employeeService.unBindMiniProgram(placeId, mobile);
    }

    /**
     * 已绑定员工小程序账号列表
     *
     * @param start
     * @param length
     * @return
     */
    @GetMapping(value = "/bindMiniProgramList")
    public GenericResponse<PagerDTO<?>> bindMiniProgramList(@RequestParam(value = "start", defaultValue = "0") int start,
                                                            @RequestParam(value = "length", defaultValue = "10") int length,
                                                            @RequestParam(name = "placeId") String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("placeId", placeId);
        queryMap.put("type", "2");
        queryMap.put("deleted", "0");
        Pageable pageable = PageRequest.of(start, length, Sort.Direction.DESC, "id");
        Page<PlaceAccount> pager = employeeService.findBindAll(queryMap, pageable);
        List<PlaceAccountBO> bos = pager.getContent().stream().map(e -> {
            return e.toBO();
        }).collect(Collectors.toList());
        return new GenericResponse<>(new PagerDTO<>((int) pager.getTotalElements(), bos));
    }

    /**
     * 判断员工账号是否绑定至少一个网吧
     *
     * @param mobile
     * @return
     */
    @GetMapping("/judgeEmployeeBindBar")
    public GenericResponse<ObjDTO<PlaceAccountBO>> judgeEmployeeBindBar(@RequestParam(name = "mobile") String mobile) {
        return employeeService.judgeAccountBindBar(mobile);
    }


}
