package com.rzx.dim4.place.service.admin.statistics.impl;

import com.rzx.dim4.base.bo.billing.StatisticsOperationByDayBO;
import com.rzx.dim4.base.bo.billing.SumStatisticsOperationByDayBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.place.entity.PlaceAccount;
import com.rzx.dim4.place.response.ResponsePage;
import com.rzx.dim4.place.service.admin.statistics.StatisticsTopupPresentService;
import com.rzx.dim4.place.util.ExcelUtils;
import com.rzx.dim4.place.web.vo.ExportTopupPresentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月11日 18:01
 */
@Service
public class StatisticsTopupPresentServiceImpl implements StatisticsTopupPresentService {

    @Autowired
    BillingServerService billingServerService;

    @Override
    public ResponsePage<ExportTopupPresentVO> initTopupPresentTable(String placeId, String dateType, int draw, int start, int length) {

        int page = start / length;
        int size = length == -1 ? 100 : length;
        String[] searchTime = dateType.split("~");
        String startTime = searchTime[0].trim() + " 00:00:00";
        String endTime = searchTime[1].trim() + " 23:59:59";
//        String startTime = searchTime[0].trim();
//        String endTime = searchTime[1].trim();

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotal(size,
                page, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);

        if (resp.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new ResponsePage<>(++draw, 0, new ArrayList<>());
        }

        // 处理数据
        List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
        List<ExportTopupPresentVO> vos = getExportTopupPresentVOS(statisticsOperationByDayBOS);
        return new ResponsePage<>(++draw, resp.getData().getTotal(), vos);
    }

    @Override
    public void exportTopupPresent(String placeId, String dateType, HttpServletResponse response) {

        int page = 0;
        int size = 10000; // 导出先给个较大值，一般网吧没有这么多的会员，这样就能共用消费排行分页查询。

        String[] searchTime = dateType.split("~");
//        String startTime = searchTime[0].trim() + " 00:00:00";
//        String endTime = searchTime[1].trim() + " 23:59:59";
        String startTime = searchTime[0].trim();
        String endTime = searchTime[1].trim();

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotal(size,
                page, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);

        // 处理数据
        List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
        List<ExportTopupPresentVO> vos = getExportTopupPresentVOS(statisticsOperationByDayBOS);


        GenericResponse<ObjDTO<ExportTopupPresentVO>> objDTOGenericResponse = sumTopupPresentTable(placeId, dateType);
        if(objDTOGenericResponse.isResult()){
            ExportTopupPresentVO obj = objDTOGenericResponse.getData().getObj();
            obj.setCountDay("合计");
            vos.add(obj);
        }

        try {
            ExcelUtils.writeExcel(response, vos, placeId+"--充赠统计",
                    "充赠统计", ExportTopupPresentVO.class,false,null);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public GenericResponse<ObjDTO<ExportTopupPresentVO>> sumTopupPresentTable(String placeId, String dateType) {
        String[] searchTime = dateType.split("~");
        String startTime = searchTime[0].trim() + " 00:00:00";
        String endTime = searchTime[1].trim() + " 23:59:59";
//        String startTime = searchTime[0].trim();
//        String endTime = searchTime[1].trim();

        GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> objDTOGenericResponse = billingServerService.sumStatisticsOperationByDay(new ArrayList<String>() {{
            add(placeId);
        }}, startTime, endTime);

        if (objDTOGenericResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(new ObjDTO<>());
        }

        // 处理数据
        SumStatisticsOperationByDayBO bo = objDTOGenericResponse.getData().getObj();
        ExportTopupPresentVO vo = new ExportTopupPresentVO();
        // 处理数据
        int sumCashIncomeTopupTotal = bo.getSumMemberTopupIncome() + bo.getSumTemporaryTopupIncome(); // 充值本金 = 会员充值 + 临时卡充值的 cost
        vo.setCountTopup(bo.getSumCountTopup()); // 充值次数
        vo.setSumPresentTupupTotal((float)(bo.getSumPresentTupupTotal() + bo.getSumPresentIncome())/ 100); // 充值奖励 = 赠送充值 + 赠送 + 老带新
        vo.setSumCashTopupIncome((float)sumCashIncomeTopupTotal / 100); // 充值本金
        vo.setSumTopupTotal(vo.getSumCashTopupIncome() + vo.getSumPresentTupupTotal()); // 合计充值
        vo.setCountReversal(bo.getSumCountReversal()); // 冲正次数
        vo.setSumTotalReversal((float)Math.abs(bo.getSumTotalReversal()) / 100); // 冲正本金
        vo.setSumPresentReversalTotal((float)Math.abs(bo.getSumPresentReversalTotal()) / 100); // 冲正奖励
        vo.setSumReversalTotal(Math.abs(vo.getSumPresentReversalTotal() + vo.getSumTotalReversal())); // 合计冲正
        vo.setCountRefund(bo.getSumCountRefund()); // 退款次数
        vo.setSumCashOutIncome((float)bo.getSumCashOutIncome() / 100); // 现金退款
        vo.setSumOnlineOutIncome((float)bo.getSumOnlineOutIncome() / 100); // 线上退款
        vo.setSumRefundTotal(vo.getSumCashOutIncome() + vo.getSumOnlineOutIncome()); // 合计退款

        return new GenericResponse<>(new ObjDTO<>(vo));
    }

    @Override
    public ResponsePage<ExportTopupPresentVO> initMonthTopupPresentTable(String placeId, String dateType, int draw, int start, int length) {

        int page = start / length;
        int size = length == -1 ? 100 : length;
        List<String> pastMonthByNum = DateTimeUtils.getPastMonthByNum(Integer.valueOf(dateType));
        String startTime = pastMonthByNum.get(0);
        String endTime = pastMonthByNum.get(1);

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotalByMonth(size,
                page, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);

        if (resp.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new ResponsePage<>(++draw, 0, new ArrayList<>());
        }

        // 处理数据
        List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
        List<ExportTopupPresentVO> vos = getExportTopupPresentVOS(statisticsOperationByDayBOS);
        return new ResponsePage<>(++draw, resp.getData().getTotal(), vos);
    }

    @Override
    public void exportMonth(String placeId, String dateType, HttpServletResponse response) {

        int page = 0;
        int size = 10000; // 导出先给个较大值，一般网吧没有这么多的会员，这样就能共用消费排行分页查询。

        List<String> pastMonthByNum = DateTimeUtils.getPastMonthByNum(Integer.valueOf(dateType));
        String startTime = pastMonthByNum.get(0);
        String endTime = pastMonthByNum.get(1);

        GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotalByMonth(size,
                page, new ArrayList<String>(){{add(placeId);}}, startTime, endTime);

        // 处理数据
        List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
        List<ExportTopupPresentVO> vos = getExportTopupPresentVOS(statisticsOperationByDayBOS);


        GenericResponse<ObjDTO<ExportTopupPresentVO>> objDTOGenericResponse = sumTopupPresentTable(placeId, startTime + "~" + endTime);
        if(objDTOGenericResponse.isResult()){
            ExportTopupPresentVO obj = objDTOGenericResponse.getData().getObj();
            obj.setCountDay("合计");
            vos.add(obj);
        }

        try {
            ExcelUtils.writeExcel(response, vos, placeId+"--充赠统计",
                    "充赠统计", ExportTopupPresentVO.class,false,null);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }


    /**
     * 组装导出数据
     * @param statisticsOperationByDayBOS
     * @return
     */
    public List<ExportTopupPresentVO> getExportTopupPresentVOS(List<StatisticsOperationByDayBO> statisticsOperationByDayBOS) {
        List<ExportTopupPresentVO> vos = new ArrayList<>();
        for (StatisticsOperationByDayBO bo : statisticsOperationByDayBOS) {
            ExportTopupPresentVO vo = new ExportTopupPresentVO();
            // 处理数据
            int sumCashIncomeTopupTotal = bo.getSumMemberTopupIncome() + bo.getSumTemporaryTopupIncome(); // 充值本金 = 会员充值 + 临时卡充值的 cost
            vo.setCountDay(bo.getCountDay());
            vo.setCountTopup(bo.getCountTopup()); // 充值次数
            vo.setSumPresentTupupTotal((float)(bo.getSumPresentTupupTotal() + bo.getSumPresentIncome())/ 100); // 充值奖励 = 赠送充值 + 赠送 + 老带新
            vo.setSumCashTopupIncome((float)sumCashIncomeTopupTotal / 100); // 充值本金
            vo.setSumTopupTotal(vo.getSumCashTopupIncome() + vo.getSumPresentTupupTotal()); // 合计充值
            vo.setCountReversal(bo.getCountReversal()); // 冲正次数
            vo.setSumTotalReversal((float)Math.abs(bo.getSumTotalReversal()) / 100); // 冲正本金
            vo.setSumPresentReversalTotal((float)Math.abs(bo.getSumPresentReversalTotal()) / 100); // 冲正奖励
            vo.setSumReversalTotal(Math.abs(vo.getSumPresentReversalTotal() + vo.getSumTotalReversal())); // 合计冲正
            vo.setCountRefund(bo.getCountRefund()); // 退款次数
            vo.setSumCashOutIncome((float)bo.getSumCashOutIncome() / 100); // 现金退款
            vo.setSumOnlineOutIncome((float)bo.getSumOnlineOutIncome() / 100); // 线上退款
            vo.setSumRefundTotal(vo.getSumCashOutIncome() + vo.getSumOnlineOutIncome()); // 合计退款
            vos.add(vo);
        }
        return vos;
    }

}
