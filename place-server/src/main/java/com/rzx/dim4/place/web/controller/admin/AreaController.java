//package com.rzx.dim4.place.web.controller.admin;
//
//import com.rzx.dim4.base.bo.billing.BillingRuleCommonBO;
//import com.rzx.dim4.base.bo.notify.polling.PollingBO;
//import com.rzx.dim4.base.bo.notify.polling.UpdateConfigBusinessBO;
//import com.rzx.dim4.base.bo.place.PlaceAreaBO;
//import com.rzx.dim4.base.dto.ObjDTO;
//import com.rzx.dim4.base.dto.SimpleDTO;
//import com.rzx.dim4.base.enums.ServiceCodes;
//import com.rzx.dim4.base.enums.notify.BusinessType;
//import com.rzx.dim4.base.enums.notify.PollingType;
//import com.rzx.dim4.base.response.GenericResponse;
//import com.rzx.dim4.base.service.feign.BillingServerService;
//import com.rzx.dim4.base.service.feign.NotifyServerService;
//import com.rzx.dim4.base.utils.Dim4StringUtils;
//import com.rzx.dim4.place.entity.PlaceAccount;
//import com.rzx.dim4.place.entity.PlaceArea;
//import com.rzx.dim4.place.entity.PlaceClient;
//import com.rzx.dim4.place.response.ResponsePage;
//import com.rzx.dim4.place.service.PlaceAreaService;
//import com.rzx.dim4.place.service.PlaceClientService;
//import com.rzx.dim4.place.service.PlaceProfileService;
//import com.rzx.dim4.place.web.cons.WebConstants;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.domain.Sort;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpSession;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
//import static com.rzx.dim4.base.enums.ServiceCodes.BAD_PARAM;
//import static com.rzx.dim4.place.web.cons.WebConstants.PLACE_SERVER_ACCOUNT;
//import static com.rzx.dim4.place.web.cons.WebConstants.PLACE_SESSION_KEY;
//
//@Slf4j
//@Controller
//@RequestMapping("/admin/area")
//public class AreaController {
//
//	@Autowired
//	PlaceAreaService areaService;
//
//	@Autowired
//	PlaceAreaService placeAreaService;
//
//	@Autowired
//	PlaceClientService placeClientService;
//
//	@Autowired
//	PlaceProfileService profileService;
//
//	@Autowired
//	StringRedisTemplate stringRedisTemplate;
//
//	@Autowired
//	BillingServerService billingServerService;
//
//	@Autowired
//	NotifyServerService notifyServerService;
//
//	private static final Pattern AREA_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$");
//
//	/**
//	 * 获取区域列表
//	 *
//	 * @return 区域列表页面
//	 */
///*	@GetMapping("/list")
//	public String list() {
//		return "/area/list";
//	}*/
//
///*	@ResponseBody
//	@GetMapping("/init")
//	public List<PlaceAreaBO> init(@RequestParam String placeId) {
//		List<PlaceArea> placeAreaList = areaService.findByPlaceId(placeId);
//		return placeAreaList.stream().map(PlaceArea::toBO).collect(Collectors.toList());
//	}*/
//
//	/**
//	 * 查询场所区域信息，只查询，不带筛选
//	 *
//	 * @param search
//	 * @param length
//	 * @param start
//	 * @param orderColumns
//	 * @param order
//	 * @return
//	 */
//	/*@ResponseBody
//	@GetMapping("/areas")
//	public ResponsePage<PlaceAreaBO> areas(@RequestParam(value = "draw") int draw,
//			@RequestParam(name = "placeId", defaultValue = "") String placeId,
//			@RequestParam(name = "search[value]", defaultValue = "") String search,
//			@RequestParam(name = "length", defaultValue = "10") int length,
//			@RequestParam(name = "start", defaultValue = "0") int start,
//			@RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
//			@RequestParam(name = "order", defaultValue = "desc") String order,
//			HttpSession httpSession) {
//		log.info("areas(size:::{},page:::{},order:::{},orderColumns:::{},queryMap:::{})", length, start, order,
//				orderColumns);
//		length = length == -1 ? 100 : length;
//		int page = (start == 0 ? start : (start / length));
//		search = StringUtils.isEmpty(search) ? null : search + "%";
//		Pageable pageable = PageRequest.of(page, length, Sort.Direction.fromString(order), orderColumns);
//		Page<PlaceArea> pager = areaService.findArea(placeId, search, pageable);
//		List<PlaceAreaBO> bos = pager.getContent().stream().map(PlaceArea::toBO).collect(Collectors.toList());
//
//		httpSession.setAttribute(PLACE_SESSION_KEY, placeId);
//		return new ResponsePage<>(++draw, (int) pager.getTotalElements(), bos);
//	}*/
//
///*	@GetMapping(value = "/getIntoAdd")
//	public String getIntoAdd(@RequestParam String placeId, @RequestParam String placeName, Model model) {
//		PlaceAreaBO bo = new PlaceAreaBO();
//		bo.setPlaceId(placeId);
//		model.addAttribute("title", placeName + "新增区域");
//		model.addAttribute("area", bo);
//		return "/area/edit";
//	}*/
//
//	/**
//	 * 批量新增 场所区域
//	 *
//	 * @param areas   区域列表
//	 * @param session 会话
//	 * @return 新增结果
//	 */
//	/*@ResponseBody
//	@PostMapping(value = "/batchSaveAreas")
//	public GenericResponse<ObjDTO<PlaceAreaBO>> batchSaveAreas(@RequestBody List<PlaceAreaBO> areas, HttpSession session) {
//
//		// 1. 校验区域是否合法
//		int errCode = validateBatchAddArea(areas);
//		if (errCode != ServiceCodes.NO_ERROR.getCode()) {
//			ServiceCodes code = ServiceCodes.getByCode(errCode);
//			return new GenericResponse<>(code);
//		}
//
//		// 2. 批量新增区域
//		PlaceAccount loginedSysAccount = (PlaceAccount) session.getAttribute(WebConstants.PLACE_SERVER_ACCOUNT);
//		List<PlaceArea> placeAreas = new ArrayList<>(areas.size());
//		areas.forEach(area -> {
//			area.setAreaName(area.getAreaName().trim());
//			area.setPlaceId(area.getPlaceId().trim());
//			PlaceArea placeArea = new PlaceArea(area);
//			placeArea.setCreater(loginedSysAccount.getId());
//			placeArea.setCreated(LocalDateTime.now());
//			placeArea.setUpdated(LocalDateTime.now());
//			placeAreas.add(placeArea);
//		});
//		areaService.batchSave(placeAreas);
//
//		// 3. 新增区域增加工作卡费率信息
//		List<BillingRuleCommonBO> billingRuleCommonBOS = profileService.getBillingRuleCommonBOS(placeAreas.get(0).getPlaceId(),placeAreas);
//
//		String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
//		stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
//		billingServerService.batchCreate(requestTicket,billingRuleCommonBOS);
//
//		GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeAreas.get(0).getPlaceId(), "", "", BusinessType.UPDATECONFIG);
//		if (pollingBOGeneric.isResult()) {
//			PollingBO pollingBO = pollingBOGeneric.getData().getObj();
//			if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
//				UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
//				updateConfigBusinessBO.setPlaceId(placeAreas.get(0).getPlaceId());
//				updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
//				updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
//				updateConfigBusinessBO.setType(1);
//				// 保存收银台业务数据
//				notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
//			}
//		}
//		return new GenericResponse<>(new ObjDTO<>());
//
//	}*/
//
//	private int validateBatchAddArea(List<PlaceAreaBO> areas) {
//
//		if (CollectionUtils.isEmpty(areas)) {
//			return ServiceCodes.NULL_PARAM.getCode();
//		}
//
//		// 校验一: 区域名称输入合法性
//		for (PlaceAreaBO area : areas) {
//			int length = area.getAreaName().length();
//			// 区域名称 大于等于 2 小于等于 20
//			if (!(length >= 2 && length <= 20)) {
//				return ServiceCodes.PLACE_AREA_NAME_LENGTH_NOT_RIGHT.getCode();
//			}
//
//			// 区域名称只能为汉字、数字、字母、下划线
//			Matcher matcher = AREA_NAME_PATTERN.matcher(area.getAreaName());
//			if (!matcher.find()) {
//				return ServiceCodes.PLACE_AREA_NAME_NOT_VALID.getCode();
//			}
//		}
//
//		// 检验二: 区域名称不能重复
//		List<String> areasNameList = areas.stream().map(PlaceAreaBO::getAreaName).collect(Collectors.toList());
//		Set<String> areaNameSet = new HashSet<>(areasNameList);
//
//		String placeId = areas.get(0).getPlaceId();
//		int repeatAreasCount = areaService.countAreasByAreaName(placeId, areasNameList);
//
//		if (repeatAreasCount != 0 || areaNameSet.size() < areasNameList.size()) {
//			return ServiceCodes.PLACE_AREA_NAME_NOT_REPEAT.getCode();
//		}
//
//		return ServiceCodes.NO_ERROR.getCode();
//	}
//
//	/**
//	 * 添加/修改 区域信息
//	 *
//	 * @param bo      PlaceAreaBO
//	 * @param session session
//	 * @return SimpleDTO
//	 */
//	/*@ResponseBody
//	@PostMapping(value = "/add")
//	public GenericResponse<ObjDTO<PlaceAreaBO>> add(PlaceAreaBO bo, HttpSession session) {
//		if (null == bo || null == bo.getAreaName() || bo.getAreaName().length() < 2 || bo.getAreaName().length() > 20
//				|| null == bo.getPlaceId()) {
//			return new GenericResponse<>(BAD_PARAM);
//		}
//
//		bo.setAreaName(bo.getAreaName().trim());
//		bo.setPlaceId(bo.getPlaceId().trim());
//		if (null != bo.getAreaId()) {
//			bo.setAreaId(bo.getAreaId().trim());
//		}
//
//		Optional<PlaceArea> optPlaceArea = areaService.findByPlaceIdAndAreaId(bo.getPlaceId(), bo.getAreaId());
//		if (optPlaceArea.isPresent()) {
//			PlaceArea placeArea = optPlaceArea.get();
//			Optional<PlaceArea> repeatArea = areaService.findByPlaceIdAndAreaName(bo.getPlaceId(),
//					bo.getAreaName());
//			// 若 区域名称已存在，且已存在的区域名称不与当前区域名称相同，则不允许修改
//			if (repeatArea.isPresent() && !repeatArea.get().getAreaId().equals(bo.getAreaId())) {
//				return new GenericResponse<>(ServiceCodes.PLACE_AREA_NAME_NOT_REPEAT);
//			}
//			placeArea.setAreaName(bo.getAreaName());
//			placeArea.setIsRoom(bo.getIsRoom());
//			placeArea.setUpdated(LocalDateTime.now());
//			areaService.save(placeArea);
//
//			GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(bo.getPlaceId(), "", "", BusinessType.UPDATECONFIG);
//			if (pollingBOGeneric.isResult()) {
//				PollingBO pollingBO = pollingBOGeneric.getData().getObj();
//				if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
//					UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
//					updateConfigBusinessBO.setPlaceId(bo.getPlaceId());
//					updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
//					updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
//					updateConfigBusinessBO.setType(1);
//					// 保存收银台业务数据
//					notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
//				}
//			}
//			return new GenericResponse<>(new ObjDTO<>(placeArea.toBO()));
//		}
//		return new GenericResponse<>(ServiceCodes.NOT_FOUND);
//	}*/
//
//	/**
//	 * 跳转至编辑修改页面
//	 *
//	 * @param placeId placeId
//	 * @param areaId  areaId
//	 * @param model   model
//	 * @return String
//	 */
//	/*@GetMapping("/getIntoEdit")
//	public String edit(@RequestParam String placeId, @RequestParam String areaId, Model model,HttpSession httpSession) {
//
//		// 权限校验
//		PlaceAccount loginAccount = (PlaceAccount) httpSession.getAttribute(PLACE_SERVER_ACCOUNT);
//		if (StringUtils.isEmpty(loginAccount) || !placeId.equals(loginAccount.getPlaceId())) {
//			return "sys/login";
//		}
//
//		Optional<PlaceArea> optPlaceArea = areaService.findByPlaceIdAndAreaId(placeId, areaId);
//		PlaceArea placeArea = new PlaceArea();
//		if (optPlaceArea.isPresent()) {
//			placeArea = optPlaceArea.get();
//			model.addAttribute("title", placeArea.getAreaName());
//		} else {
//			model.addAttribute("title", "");
//		}
//		model.addAttribute("area", placeArea);
//		return "/area/edit";
//	}*/
//
//	/**
//	 * 查询 区域名称是否已存在（不与当前区域名称相同）
//	 *
//	 * @param placeId 			场所 id
//	 * @param originalAreaName  区域名称
//	 * @param areaId 			区域 id
//	 * @return 输入区域与原区域名称不同 且 已存在区域名称，返回 exist
//	 */
///*	@ResponseBody
//	@GetMapping("/exist")
//	public String exist(@RequestParam String placeId, @RequestParam("areaName") String originalAreaName, @RequestParam String areaId) {
//
//		// 返回已存在: when {输入: 区域名称 != 存储: placeId.areaId.区域名称 && 数据库中已存在 输入.区域名称}
//		Optional<PlaceArea> curAreaNameOpt = areaService.findPlaceAreaByPlaceIdAndAreaId(placeId.trim(), areaId.trim());
//
//		if (curAreaNameOpt.isPresent()) {
//			int areasCountByAreaName = areaService.countAreasByAreaName(placeId, Collections.singletonList(originalAreaName));
//			boolean inputAreaNameNotEqualsStored = !originalAreaName.equals(curAreaNameOpt.get().getAreaName());
//			boolean areaNameExisted = areasCountByAreaName > 0;
//
//			if (inputAreaNameNotEqualsStored && areaNameExisted) {
//				return "exist";
//			}
//		}
//		return "not exist";
//	}*/
//
///*	@ResponseBody
//	@GetMapping("/checkBindClient")
//	public String checkBindClientByArea(@RequestParam  String areaId,@RequestParam String placeId){
//		List<PlaceClient> placeClients = placeClientService.getClientsByPlaceIdAndAreaId(placeId,areaId);
//		if (CollectionUtils.isEmpty(placeClients)) {
//			return "no_exist";
//		}
//		return "exist";
//	}*/
//
//	/**
//	 * 删除区域
//	 *
//	 * @return 删除结果
//	 */
//	/*@PostMapping("/delete")
//	@ResponseBody
//	public GenericResponse<SimpleDTO> delete(@RequestParam String areaId, @RequestParam String placeId) {
//
//		List<PlaceClient> placeClients = placeClientService.getClientsByPlaceIdAndAreaId(placeId,areaId);
//		if (!CollectionUtils.isEmpty(placeClients)) {
//			return new GenericResponse<>(ServiceCodes.AREA_BIND_CLIENT); //该区域已绑定客户端
//		}
//
//		Optional<PlaceArea> placeAreaOpt = placeAreaService.findByPlaceIdAndAreaId(placeId,areaId);
//		if(placeAreaOpt.isPresent()){
//			placeAreaService.delete(placeAreaOpt.get());
//
//			// 删除该区域下的工作卡费率信息
//			String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
//			stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
//
//			billingServerService.deleteBillingRuleCommon(requestTicket,placeId,"1002",areaId);
//
//			GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, "", "", BusinessType.UPDATECONFIG);
//			if (pollingBOGeneric.isResult()) {
//				PollingBO pollingBO = pollingBOGeneric.getData().getObj();
//				if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
//					UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
//					updateConfigBusinessBO.setPlaceId(placeId);
//					updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
//					updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
//					updateConfigBusinessBO.setType(1);
//					// 保存收银台业务数据
//					notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
//				}
//			}
//			return new GenericResponse<>(ServiceCodes.NO_ERROR);
//		}
//
//		return new GenericResponse<>(ServiceCodes.OPT_ERROR);
//	}*/
//}
