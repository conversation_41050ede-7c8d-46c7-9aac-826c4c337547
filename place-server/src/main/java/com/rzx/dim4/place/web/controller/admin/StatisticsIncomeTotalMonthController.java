//package com.rzx.dim4.place.web.controller.admin;
//
//import com.rzx.dim4.base.bo.billing.StatisticsOperationByDayBO;
//import com.rzx.dim4.base.bo.billing.SumStatisticsOperationByDayBO;
//import com.rzx.dim4.base.bo.regcard.RegLogBO;
//import com.rzx.dim4.base.bo.shop.StatisticsGoodsSaleByDayBO;
//import com.rzx.dim4.base.dto.ListDTO;
//import com.rzx.dim4.base.dto.ObjDTO;
//import com.rzx.dim4.base.dto.PagerDTO;
//import com.rzx.dim4.base.enums.ServiceCodes;
//import com.rzx.dim4.base.response.GenericResponse;
//import com.rzx.dim4.base.service.feign.BillingServerService;
//import com.rzx.dim4.base.service.feign.RegcardServerService;
//import com.rzx.dim4.base.service.feign.ShopServerService;
//import com.rzx.dim4.base.utils.DateTimeUtils;
//import com.rzx.dim4.place.entity.PlaceAccount;
//import com.rzx.dim4.place.response.ResponsePage;
//import com.rzx.dim4.place.service.PlaceMenuService;
//import com.rzx.dim4.place.service.PlaceShiftService;
//import com.rzx.dim4.place.util.ExcelUtils;
//import com.rzx.dim4.place.web.vo.ExportIncomeResultVO;
//import com.rzx.dim4.place.web.vo.ExportIncomeTotalVO;
//import com.rzx.dim4.place.web.vo.SumExportIncomeTotalVO;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.domain.Sort;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.util.ObjectUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import javax.servlet.http.HttpSession;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
//import static com.rzx.dim4.place.web.cons.WebConstants.PLACE_SERVER_ACCOUNT;
//
///**
// * 统计数据--收入统计月统计
// */
//@Controller
//@RequestMapping("/admin/statistics/monthIncomeTotal")
//public class StatisticsIncomeTotalMonthController {
//
//    @Autowired
//    BillingServerService billingServerService;
//
//    @Autowired
//    ShopServerService shopServerService;
//    @Autowired
//    RegcardServerService regcardServerService;
//
//    @Autowired
//    private PlaceMenuService placeMenuService;
//
//    @Autowired
//    private PlaceShiftService placeShiftService;
//
//
//    private final String groupType = "2";
//
//
//    /**
//     * 收入统计页面查询表格中合计金额
//     *
//     * @param session
//     * @param dateType 时间区间
//     * @return
//     */
//    /*@GetMapping("/allInitIncomeTotalTableSum")
//    @ResponseBody
//    public GenericResponse<ObjDTO<SumExportIncomeTotalVO>> allInitIncomeTotalTableSum(HttpSession session,
//                                                                                      @RequestParam String queryType,
//                                                                                      @RequestParam String dateType) {
//
//        PlaceAccount loginAccount = (PlaceAccount) session.getAttribute(PLACE_SERVER_ACCOUNT);
//        String placeId = loginAccount.getPlaceId();
//
//        List<String> pastMonthByNum = DateTimeUtils.getPastMonthByNum(Integer.valueOf(dateType));
//        String startTime = pastMonthByNum.get(0);
//        String endTime = pastMonthByNum.get(1);
//        if("1".equals(queryType)) {
//            GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> resp =
//                    billingServerService.sumStatisticsOperationByDay(new ArrayList<String>() {{
//                        add(placeId);
//                    }}, startTime, endTime);
//            SumStatisticsOperationByDayBO returnObj = resp.getData().getObj();
//            if (!ObjectUtils.isEmpty(returnObj)) {
//                //查询商超销售统计
//                returnObj = queryAllShopAndRegIncomeInfo(returnObj, placeId, startTime, endTime);
//            }
//
//            ExportIncomeResultVO sumVO = getAllExportIncomeTotalVOS(returnObj);
//
//            if (resp.getCode() != ServiceCodes.NO_ERROR.getCode()) {
//                return new GenericResponse<>(ServiceCodes.OPT_ERROR);
//            }
//            return new GenericResponse<>(new ObjDTO<>(sumVO.getSumExportIncomeTotalVO()));
//        }else if("2".equals(queryType)){
//
//            ExportIncomeTotalVO exportIncomeTotalVO = placeShiftService.querySumShiftStatistic(placeId, startTime, endTime);
//
//            SumExportIncomeTotalVO sumExportIncomeTotalVO = formatSumVO(exportIncomeTotalVO);
//
//            return new GenericResponse<>(new ObjDTO<>(sumExportIncomeTotalVO));
//        }
//        return new GenericResponse<>(new ObjDTO<>());
//    }*/
//
//    /**
//     * 收入统计页面分页查询
//     *
//     * @param session
//     * @param dateType
//     * @param draw
//     * @param start
//     * @param length
//     * @return
//     */
//    /*@GetMapping("/initIncomeTotalTable")
//    @ResponseBody
//    public ResponsePage<ExportIncomeTotalVO> initIncomeTotalTable(HttpSession session,
//                                                                  @RequestParam String dateType,
//                                                                  @RequestParam int draw,
//                                                                  @RequestParam String queryType,
//                                                                  @RequestParam(value = "start", defaultValue = "0") int start,
//                                                                  @RequestParam(value = "length", defaultValue = "10") int length) {
//        PlaceAccount loginAccount = (PlaceAccount) session.getAttribute(PLACE_SERVER_ACCOUNT);
//        String placeId = loginAccount.getPlaceId();
//
//        int page = start / length;
//        int size = length == -1 ? 100 : length;
//        List<String> pastMonthByNum = DateTimeUtils.getPastMonthByNum(Integer.valueOf(dateType));
//        String startTime = pastMonthByNum.get(0);
//        String endTime = pastMonthByNum.get(1);
//
//        if("1".equals(queryType)) {
//
//            GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotalByMonth(size,
//                    page, new ArrayList<String>() {{
//                        add(placeId);
//                    }}, startTime, endTime);
//
//            if (resp.getCode() != ServiceCodes.NO_ERROR.getCode()) {
//                return new ResponsePage<>(++draw, 0, new ArrayList<>());
//            }
//
//            // 处理数据
//            List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
//
//            if (statisticsOperationByDayBOS.size() > 0) {
//
//                statisticsOperationByDayBOS = queryShopAndRegIncomeInfo(statisticsOperationByDayBOS, placeId, startTime, endTime);
//            }
//
//            List<ExportIncomeResultVO> vos = getExportIncomeTotalVOS(statisticsOperationByDayBOS, placeId,
//                    startTime, endTime, false);
//
//            //把金额明细数据集合返回出去
//            return new ResponsePage<>(++draw, resp.getData().getTotal(), vos.get(0).getExportIncomeTotalVOList());
//        }else if("2".equals(queryType)){
//            int i = placeShiftService.countQueryShiftStatistics(placeId, startTime, endTime, groupType);
//            if(i > 0){
//                Pageable pageable = PageRequest.of(page, size, Sort.Direction.fromString("desc"), "id");
//                List<ExportIncomeTotalVO> exportIncomeTotalVOS = placeShiftService.queryShiftStatistics(placeId, startTime, endTime, groupType, pageable);
//
//                exportIncomeTotalVOS = formatVO(exportIncomeTotalVOS);
//
//                return new ResponsePage<>(++draw, i, exportIncomeTotalVOS);
//            }
//
//        }
//
//        return new ResponsePage<>(1,0,new ArrayList<>());
//    }*/
//
//    /**
//     * 导出
//     *
//     * @param session
//     * @param dateType 筛选时间
//     * @param response
//     */
//    /*@RequestMapping(value = "/export", method = RequestMethod.GET)
//    @ResponseBody
//    public void exportStatisticsOperation(HttpSession session,
//                                          @RequestParam String dateType,
//                                          @RequestParam String queryType,
//                                          HttpServletResponse response) {
//        PlaceAccount loginAccount = (PlaceAccount) session.getAttribute(PLACE_SERVER_ACCOUNT);
//        String placeId = loginAccount.getPlaceId();
//
//        int page = 0;
//        int size = 10000; // 导出先给个较大值，一般网吧没有这么多的会员，这样就能共用消费排行分页查询。
//
//        List<String> pastMonthByNum = DateTimeUtils.getPastMonthByNum(Integer.valueOf(dateType));
//        String startTime = pastMonthByNum.get(0);
//        String endTime = pastMonthByNum.get(1);
//
//        if("1".equals(queryType)) {
//
//            GenericResponse<PagerDTO<StatisticsOperationByDayBO>> resp = billingServerService.incomeTotalByMonth(size,
//                    page, new ArrayList<String>() {{
//                        add(placeId);
//                    }}, startTime, endTime);
//            // 处理数据
//            List<StatisticsOperationByDayBO> statisticsOperationByDayBOS = resp.getData().getList();
//            if (statisticsOperationByDayBOS.size() > 0) {
//                statisticsOperationByDayBOS = queryShopAndRegIncomeInfo(statisticsOperationByDayBOS, placeId, startTime, endTime);
//            }
//            List<ExportIncomeTotalVO> exportIncomeTotalVOList = getExportIncomeConvertData(statisticsOperationByDayBOS, placeId, startTime, endTime);
//            try {
//                ExcelUtils.writeExcel(response, exportIncomeTotalVOList, placeId + "--收入统计",
//                        "收入统计", ExportIncomeTotalVO.class, true, startTime + " ~ " + endTime);
//            } catch (Exception e) {
//                e.printStackTrace();
//            } finally {
//                try {
//                    response.getOutputStream().close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }else if("2".equals(queryType)){
//
//            int i = placeShiftService.countQueryShiftStatistics(placeId, startTime, endTime, groupType);
//            if(i > 0){
//                Pageable pageable = PageRequest.of(page, size, Sort.Direction.fromString("desc"), "id");
//                List<ExportIncomeTotalVO> exportIncomeTotalVOS = placeShiftService.queryShiftStatistics(placeId, startTime, endTime, groupType, pageable);
//
//                exportIncomeTotalVOS = formatVO(exportIncomeTotalVOS);
//                ExportIncomeTotalVO exportIncomeTotalVO = placeShiftService.querySumShiftStatistic(placeId, startTime, endTime);
//                List<ExportIncomeTotalVO> exportIncomeTotalVOS1 = formatVO(Arrays.asList(exportIncomeTotalVO));
//                exportIncomeTotalVOS.addAll(exportIncomeTotalVOS1);
//                try {
//                    ExcelUtils.writeExcel(response, exportIncomeTotalVOS, placeId + "--收入统计",
//                            "收入统计", ExportIncomeTotalVO.class, true, startTime + " ~ " + endTime);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                } finally {
//                    try {
//                        response.getOutputStream().close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//
//        }
//    }*/
//
//    /**
//     * 构建导出数据转换
//     *
//     * @param operationByDayBOList 数据集
//     * @param placeId              场所id
//     * @param startTime            开始时间
//     * @param endTime              结束时间
//     * @return
//     */
//    public List<ExportIncomeTotalVO> getExportIncomeConvertData(List<StatisticsOperationByDayBO> operationByDayBOList,
//                                                                String placeId, String startTime, String endTime) {
//
//        List<ExportIncomeResultVO> vos = getExportIncomeTotalVOS(operationByDayBOList,
//                placeId, startTime, endTime, true);
//        ExportIncomeResultVO exportIncomeResultVO = vos.get(0);
//        List<ExportIncomeTotalVO> exportIncomeTotalVOList = exportIncomeResultVO.getExportIncomeTotalVOList();
//        SumExportIncomeTotalVO sumExportIncomeTotalVO = exportIncomeResultVO.getSumExportIncomeTotalVO();
//
//        ExportIncomeTotalVO vo = new ExportIncomeTotalVO();
//        vo.setCountDay("合计");
//        vo.setNetIncome(sumExportIncomeTotalVO.getSumNetIncome());
//        vo.setMallIncome(sumExportIncomeTotalVO.getSumMallIncome());
//        vo.setOnlineOutIncome(sumExportIncomeTotalVO.getSumOnlineOutIncome());
//        vo.setOtherIncome(sumExportIncomeTotalVO.getSumOtherIncome());
//        vo.setOtherCashIncome(sumExportIncomeTotalVO.getSumOtherCashIncome());
//        vo.setOnlineTopupIncome(sumExportIncomeTotalVO.getSumOnlineTopupIncome());
//        vo.setSumTotalReversal(sumExportIncomeTotalVO.getSumTotalReversal());
//        vo.setSumCashOutIncome(sumExportIncomeTotalVO.getSumCashOutIncome());
//        vo.setSumCashTopupIncome(sumExportIncomeTotalVO.getSumCashTopupIncome());
//        vo.setNetworkFeeIncome(sumExportIncomeTotalVO.getSumNetworkFeeIncome());
//        vo.setOtherOutcome(sumExportIncomeTotalVO.getSumOtherOutcome());
//        vo.setMallCashIncome(sumExportIncomeTotalVO.getMallCashIncome());
//        vo.setMallOnlineIncome(sumExportIncomeTotalVO.getMallOnlineIncome());
//        vo.setMallRefundCashTotal(sumExportIncomeTotalVO.getMallRefundCashTotal());
//        vo.setMallRefundOnlineTotal(sumExportIncomeTotalVO.getMallRefundOnlineTotal());
//        exportIncomeTotalVOList.add(vo);
//        return exportIncomeTotalVOList;
//    }
//
//    /**
//     * 组装明细金额数据
//     *
//     * @param statisticsOperationByDayBOS 数据对象
//     * @param placeId                     场所id
//     * @param startTime                   开始时间
//     * @param endTime                     结束时间
//     * @param bool                        是否导出excel
//     * @return
//     */
//    public List<ExportIncomeResultVO> getExportIncomeTotalVOS(List<StatisticsOperationByDayBO> statisticsOperationByDayBOS,
//                                                              String placeId, String startTime, String endTime,
//                                                              boolean bool) {
//        List<ExportIncomeTotalVO> vos = new ArrayList<>();
//        for (StatisticsOperationByDayBO bo : statisticsOperationByDayBOS) {
//            ExportIncomeTotalVO vo = new ExportIncomeTotalVO();
//            // 处理数据
//            int onlineIncome = bo.getSumClientTopupIncome() + bo.getSumMpTopupIncome() + bo.getSumAppTopupIncome() + bo.getSumCashierOnlineTopupIncome(); // 线上
//            int networkFeeIncome = onlineIncome + bo.getSumCashTopupIncome() - bo.getSumCashOutIncome() - bo.getSumOnlineOutIncome() + bo.getSumTotalReversal();//网费收入
//            int mallIncome = bo.getSumSaleCashTotal() + bo.getSumSaleOnlineTotal()- bo.getSumRefundCashTotal() - bo.getSumRefundOnlineTotal();//商超收入
//            int otherCashIncome =bo.getOtherIncome() + bo.getOtherOutcome() + bo.getSumRegIncome() ;//其他收入
//            vo.setCountDay(bo.getCountDay());
//            vo.setSumCashTopupIncome((float)bo.getSumCashTopupIncome() / 100);
//            vo.setSumCashOutIncome((float)bo.getSumCashOutIncome() / 100);
//            vo.setSumTotalReversal(Math.abs((float)bo.getSumTotalReversal() / 100));  //冲正展示正数
//            vo.setOtherIncome((float)(bo.getOtherIncome() + bo.getSumRegIncome())/100); // 其他收入 + 注册卡收入
//            vo.setOtherOutcome(Math.abs((float)bo.getOtherOutcome() /100));//其他支出
//            vo.setOtherCashIncome((float)(bo.getOtherIncome() + bo.getOtherOutcome() + bo.getSumRegIncome()) / 100); // 其他收支(现金)
//            vo.setOnlineTopupIncome((float)onlineIncome / 100); // 线上充值
//            vo.setOnlineOutIncome((float)bo.getSumOnlineOutIncome() / 100); // 线上退款
//            vo.setMallCashIncome((float)bo.getSumSaleCashTotal() / 100); // 商超现金收益
//            vo.setMallRefundCashTotal((float)bo.getSumRefundCashTotal() / 100);//商超线上退款
//            vo.setMallOnlineIncome((float)bo.getSumSaleOnlineTotal() / 100); // 商超线上收益
//            vo.setMallRefundOnlineTotal((float)bo.getSumRefundOnlineTotal() / 100);//商超线上退款
//            vo.setNetworkFeeIncome((float)networkFeeIncome / 100); // 网费收益
//            vo.setMallIncome((float) mallIncome / 100); // 商超收益
//            vo.setNetIncome((float)(networkFeeIncome+mallIncome+otherCashIncome)/100); // 纯收益
//            vos.add(vo);
//        }
//
//
//        ExportIncomeResultVO resultVO = new ExportIncomeResultVO();
//        resultVO.setExportIncomeTotalVOList(vos);
//
//        //true:导出，false不导出
//        if (bool) {
//            GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> resp =
//                    billingServerService.sumStatisticsOperationByDay(new ArrayList<String>(){{add(placeId);}}, startTime, endTime);
//            SumStatisticsOperationByDayBO returnObj = resp.getData().getObj();
//            if(!ObjectUtils.isEmpty(returnObj)){
//                returnObj = queryAllShopAndRegIncomeInfo(returnObj,placeId,startTime,endTime);
//            }
//            ExportIncomeResultVO allSum = getAllExportIncomeTotalVOS(returnObj);
//            resultVO.setSumExportIncomeTotalVO(allSum.getSumExportIncomeTotalVO());
//        }
//
//        List<ExportIncomeResultVO> voList = new ArrayList<>();
//        voList.add(resultVO);
//
//        return voList;
//    }
//
//    /**
//     * 组装导出数据
//     *
//     * @param bo 数据对象
//     * @return
//     */
//    public ExportIncomeResultVO getAllExportIncomeTotalVOS(SumStatisticsOperationByDayBO bo) {
//
//        SumExportIncomeTotalVO vo = new SumExportIncomeTotalVO();
//        // 处理数据
//        int onlineIncome = bo.getSumClientTopupIncome() + bo.getSumMpTopupIncome() + bo.getSumAppTopupIncome() + bo.getSumCashierOnlineTopupIncome(); // 线上
//        int networkFeeIncome = onlineIncome + bo.getSumCashTopupIncome() - bo.getSumCashOutIncome()
//                + bo.getSumTotalReversal() - bo.getSumOnlineOutIncome();
//        int mallIncome = bo.getSumSaleCashTotal() + bo.getSumSaleOnlineTotal()- bo.getSumRefundCashTotal() - bo.getSumRefundOnlineTotal();//商超收入
//        int otherCashIncome =bo.getSumOtherIncome() + bo.getSumOtherOutcome() + bo.getSumRegIncome() ;//其他收入
//        int income = bo.getSumCashTopupIncome() + (bo.getSumOtherIncome() + bo.getSumRegIncome()) + onlineIncome + bo.getSumSaleCashTotal() + bo.getSumSaleOnlineTotal();//收入
//        int expend = bo.getSumCashOutIncome() + Math.abs(bo.getSumTotalReversal()) + (Math.abs(bo.getSumOtherOutcome())) +
//                bo.getSumOnlineOutIncome() + bo.getSumRefundCashTotal() + bo.getSumRefundOnlineTotal();//支出
//        vo.setSumCashTopupIncome((float)bo.getSumCashTopupIncome() / 100); //现金充值总和
//        vo.setSumCashOutIncome((float)bo.getSumCashOutIncome() / 100); //现金退款总和
//        vo.setSumTotalReversal(Math.abs((float)bo.getSumTotalReversal()) / 100);//冲正本金总和
//        vo.setSumOnlineTopupIncome((float)onlineIncome / 100); // 线上充值
//        vo.setSumOtherCashIncome((float)(bo.getSumOtherIncome() + bo.getSumOtherOutcome() + bo.getSumRegIncome()) / 100); // 其他收支(现金)-囊括注册卡
//        vo.setSumOnlineOutIncome((float)bo.getSumOnlineOutIncome() / 100); // 线上退款
//        vo.setMallCashIncome((float)bo.getSumSaleCashTotal() / 100); // 商超现金收益
//        vo.setMallRefundCashTotal((float)bo.getSumRefundCashTotal() / 100);//商超现金退款
//        vo.setMallOnlineIncome((float)bo.getSumSaleOnlineTotal() / 100); // 商超线上收益
//        vo.setMallRefundOnlineTotal((float)bo.getSumRefundOnlineTotal() / 100);//商超线上退款
//        vo.setSumOtherIncome((float)(bo.getSumOtherIncome() + bo.getSumRegIncome())/100); // 其他收入 + 注册卡收入
//        vo.setSumOtherOutcome((float)(Math.abs(bo.getSumOtherOutcome()))/100); // 其他支出
//        vo.setSumNetworkFeeIncome((float)networkFeeIncome / 100); // 网费收益
//        vo.setSumMallIncome((float) mallIncome / 100); // 商超收益
//        vo.setSumNetIncome((float)(networkFeeIncome+mallIncome+otherCashIncome)/100); // 纯收益
//        vo.setIncome((float)income/100);
//        vo.setExpend((float)expend/100);
//        ExportIncomeResultVO resultVO = new ExportIncomeResultVO();
//        resultVO.setSumExportIncomeTotalVO(vo);
//
//        return resultVO;
//    }
//
//    //查询商超和注册卡统计收入
//    public List<StatisticsOperationByDayBO> queryShopAndRegIncomeInfo(List<StatisticsOperationByDayBO> statisticsOperationByDayBOS ,String placeId,String startTime,String endTime){
////查询商超销售统计
//        GenericResponse<ListDTO<StatisticsGoodsSaleByDayBO>> listDTOGenericResponse1 = shopServerService.goodsSaleByMonth(placeId, startTime, endTime);
//        List<StatisticsGoodsSaleByDayBO> shopList = new ArrayList<>();
//        if(listDTOGenericResponse1.isResult() && listDTOGenericResponse1.getData().getList().size() > 0){
//            shopList = listDTOGenericResponse1.getData().getList();
//        }
//        //查询注册卡销售统计
//        GenericResponse<ListDTO<RegLogBO>> listDTOGenericResponse = regcardServerService.queryRegcardLogByPlaceIdAndDate(placeId, "", startTime, endTime);
//        List<RegLogBO> regLogBOS = new ArrayList<>();
//        if(listDTOGenericResponse.isResult() && listDTOGenericResponse.getData().getList().size() > 0){
//            regLogBOS = listDTOGenericResponse.getData().getList();
//        }
//        for (StatisticsOperationByDayBO statisticsOperationByDayBO : statisticsOperationByDayBOS) {
//            String countDay = statisticsOperationByDayBO.getCountDay();
//            //商超收入
//            List<StatisticsGoodsSaleByDayBO> goodStatist = shopList.stream().filter(it -> it.getCountDay().equals(countDay)).collect(Collectors.toList());
//            if(goodStatist.size() > 0){
//                statisticsOperationByDayBO.setSumSaleCashTotal(goodStatist.get(0).getSumSaleCashTotal()); // 商超销售金额(现金)
//                statisticsOperationByDayBO.setSumSaleOnlineTotal(goodStatist.get(0).getSumSaleOnlineTotal());// 商超销售金额(线上)
//                statisticsOperationByDayBO.setSumRefundCashTotal(goodStatist.get(0).getSumRefundCashTotal()); // 商超退款金额(现金)
//                statisticsOperationByDayBO.setSumRefundOnlineTotal(goodStatist.get(0).getSumRefundOnlineTotal()); // 商超退款金额(线上)
//            }
//            //注册卡收入
//            List<RegLogBO> regLog = regLogBOS.stream().filter(it -> (it.getCreated().substring(0, 7)).equals(countDay)).collect(Collectors.toList());
//            if(regLog.size() > 0){
//                statisticsOperationByDayBO.setSumRegIncome(regLog.stream().mapToInt(it->it.getPrice()).sum());
//            }
//        }
//        return statisticsOperationByDayBOS;
//    }
//
//    //查询商超和注册卡销售合计
//    public SumStatisticsOperationByDayBO queryAllShopAndRegIncomeInfo(SumStatisticsOperationByDayBO returnObj ,String placeId,String startTime,String endTime){
//        //查询商超销售统计
//        GenericResponse<ObjDTO<StatisticsGoodsSaleByDayBO>> objDTOGenericResponse = shopServerService.sumGoodsSaleByDate(placeId, startTime, endTime);
//        if(objDTOGenericResponse.isResult()){
//            StatisticsGoodsSaleByDayBO obj = objDTOGenericResponse.getData().getObj();
//            returnObj.setSumSaleCashTotal(obj.getSumSaleCashTotal()); // 商超销售金额(现金)
//            returnObj.setSumSaleOnlineTotal(obj.getSumSaleOnlineTotal());// 商超销售金额(线上)
//            returnObj.setSumRefundCashTotal(obj.getSumRefundCashTotal()); // 商超退款金额(现金)
//            returnObj.setSumRefundOnlineTotal(obj.getSumRefundOnlineTotal()); // 商超退款金额(线上)
//        }else{
//            returnObj.setSumSaleCashTotal(0); // 商超销售金额(现金)
//            returnObj.setSumSaleOnlineTotal(0);// 商超销售金额(线上)
//            returnObj.setSumRefundCashTotal(0); // 商超退款金额(现金)
//            returnObj.setSumRefundOnlineTotal(0); // 商超退款金额(线上)
//        }
//        //查询注册卡销售统计-计算合计
//        GenericResponse<ListDTO<RegLogBO>> listDTOGenericResponse = regcardServerService.queryRegcardLogByPlaceIdAndDate(placeId, "", startTime, endTime);
//        if(listDTOGenericResponse.isResult()){
//            returnObj.setSumRegIncome(listDTOGenericResponse.getData().getList().stream().mapToInt(it->it.getPrice()).sum());
//        }
//        return returnObj;
//    }
//
//
//
//    //格式化按交班记录查询出来的数据，需要计算出总和字段,普通字段也要除以一百
//    public List<ExportIncomeTotalVO> formatVO(List<ExportIncomeTotalVO> exportIncomeTotalVOS){
//        for (ExportIncomeTotalVO exportIncomeTotalVO : exportIncomeTotalVOS) {
//            //网费收益 = 现金收入 - 现金支出 + 线上收入 - 线上支出 + 冲正金额
//            float networkFeeIncome = exportIncomeTotalVO.getSumCashTopupIncome() - Math.abs(exportIncomeTotalVO.getSumCashOutIncome()) + exportIncomeTotalVO.getOnlineTopupIncome() -
//                    Math.abs(exportIncomeTotalVO.getOnlineOutIncome()) + exportIncomeTotalVO.getSumTotalReversal();
//
//            //商超收益 = 商超线上收入 - 商超线上退款 + 商超现金收入 - 商超现金退款
//            float shopIncome = exportIncomeTotalVO.getMallOnlineIncome() - Math.abs(exportIncomeTotalVO.getMallRefundOnlineTotal()) + exportIncomeTotalVO.getMallCashIncome() -
//                    Math.abs(exportIncomeTotalVO.getMallRefundCashTotal());
//
//            //其他收入 = 其他收入 - 其他支出 + 注册卡收入
//            float otherCashIncome = exportIncomeTotalVO.getOtherIncome() - Math.abs(exportIncomeTotalVO.getOtherOutcome()) + exportIncomeTotalVO.getSumRegIncome();
//
//            //总收入 = 网费收入 + 商超收入 + 其他收入
//            float netIncome = networkFeeIncome + shopIncome + otherCashIncome;
//
//            //金额除以一百返回给前端
//            exportIncomeTotalVO.setSumCashTopupIncome(exportIncomeTotalVO.getSumCashTopupIncome() /100);
//            exportIncomeTotalVO.setSumCashOutIncome(Math.abs(exportIncomeTotalVO.getSumCashOutIncome()) /100);
//            exportIncomeTotalVO.setOnlineTopupIncome(exportIncomeTotalVO.getOnlineTopupIncome() /100);
//            exportIncomeTotalVO.setOnlineOutIncome(Math.abs(exportIncomeTotalVO.getOnlineOutIncome()) /100);
//            exportIncomeTotalVO.setSumTotalReversal(Math.abs(exportIncomeTotalVO.getSumTotalReversal()) /100);
//            exportIncomeTotalVO.setMallOnlineIncome(exportIncomeTotalVO.getMallOnlineIncome() /100);
//            exportIncomeTotalVO.setMallRefundOnlineTotal(Math.abs(exportIncomeTotalVO.getMallRefundOnlineTotal()) /100);
//            exportIncomeTotalVO.setMallCashIncome(exportIncomeTotalVO.getMallCashIncome() /100);
//            exportIncomeTotalVO.setMallRefundCashTotal(Math.abs(exportIncomeTotalVO.getMallRefundCashTotal()) /100);
//            exportIncomeTotalVO.setOtherIncome((exportIncomeTotalVO.getOtherIncome() + exportIncomeTotalVO.getSumRegIncome()) /100);
//            exportIncomeTotalVO.setOtherOutcome(Math.abs(exportIncomeTotalVO.getOtherOutcome()) /100);
//            exportIncomeTotalVO.setSumRegIncome(exportIncomeTotalVO.getSumRegIncome() /100);
//            exportIncomeTotalVO.setNetworkFeeIncome(networkFeeIncome /100);
//            exportIncomeTotalVO.setMallIncome(shopIncome /100);
//            exportIncomeTotalVO.setOtherCashIncome(otherCashIncome /100);
//            exportIncomeTotalVO.setNetIncome(netIncome /100);
//            if(StringUtils.isEmpty(exportIncomeTotalVO.getCountDay())){
//                exportIncomeTotalVO.setCountDay("合计");
//            }
//        }
//        return exportIncomeTotalVOS;
//    }
//
//    //格式化收入统计按交班统计查询合计对象
//    public SumExportIncomeTotalVO formatSumVO(ExportIncomeTotalVO exportIncomeTotalVO){
//
//        SumExportIncomeTotalVO resultVO = new SumExportIncomeTotalVO();
//        //网费收益 = 现金收入 - 现金支出 + 线上收入 - 线上支出 + 冲正金额
//        float networkFeeIncome = exportIncomeTotalVO.getSumCashTopupIncome() - Math.abs(exportIncomeTotalVO.getSumCashOutIncome()) + exportIncomeTotalVO.getOnlineTopupIncome() -
//                Math.abs(exportIncomeTotalVO.getOnlineOutIncome()) + exportIncomeTotalVO.getSumTotalReversal();
//
//        //商超收益 = 商超线上收入 - 商超线上退款 + 商超现金收入 - 商超现金退款
//        float shopIncome = exportIncomeTotalVO.getMallOnlineIncome() - Math.abs(exportIncomeTotalVO.getMallRefundOnlineTotal()) + exportIncomeTotalVO.getMallCashIncome() -
//                Math.abs(exportIncomeTotalVO.getMallRefundCashTotal());
//
//        //其他收入 = 其他收入 - 其他支出 + 注册卡收入
//        float otherCashIncome = exportIncomeTotalVO.getOtherIncome() - Math.abs(exportIncomeTotalVO.getOtherOutcome()) + exportIncomeTotalVO.getSumRegIncome();
//
//        //总收入 = 网费收入 + 商超收入 + 其他收入
//        float netIncome = networkFeeIncome + shopIncome + otherCashIncome;
//        float income =exportIncomeTotalVO.getSumCashTopupIncome() + exportIncomeTotalVO.getOnlineTopupIncome() +  exportIncomeTotalVO.getMallOnlineIncome() +
//                exportIncomeTotalVO.getMallCashIncome() + exportIncomeTotalVO.getOtherIncome()+ exportIncomeTotalVO.getSumRegIncome();//收入
//        float expend =  Math.abs(exportIncomeTotalVO.getSumCashOutIncome()) + Math.abs(exportIncomeTotalVO.getOnlineOutIncome())+ Math.abs(exportIncomeTotalVO.getSumTotalReversal())+
//                Math.abs(exportIncomeTotalVO.getMallRefundOnlineTotal()) + Math.abs(exportIncomeTotalVO.getMallRefundCashTotal()) + Math.abs(exportIncomeTotalVO.getOtherOutcome());//支出
//        resultVO.setIncome(income/100);
//        resultVO.setExpend(expend/100);
//        //金额除以一百返回给前端
//        resultVO.setSumCashTopupIncome(exportIncomeTotalVO.getSumCashTopupIncome() /100);
//        resultVO.setSumCashOutIncome(Math.abs(exportIncomeTotalVO.getSumCashOutIncome()) /100);
//        resultVO.setSumOnlineTopupIncome(exportIncomeTotalVO.getOnlineTopupIncome() /100);
//        resultVO.setSumOnlineOutIncome(Math.abs(exportIncomeTotalVO.getOnlineOutIncome()) /100);
//        resultVO.setSumTotalReversal(Math.abs(exportIncomeTotalVO.getSumTotalReversal()) /100);
//        resultVO.setMallOnlineIncome(exportIncomeTotalVO.getMallOnlineIncome() /100);
//        resultVO.setMallRefundOnlineTotal(Math.abs(exportIncomeTotalVO.getMallRefundOnlineTotal()) /100);
//        resultVO.setMallCashIncome(exportIncomeTotalVO.getMallCashIncome() /100);
//        resultVO.setMallRefundCashTotal(Math.abs(exportIncomeTotalVO.getMallRefundCashTotal()) /100);
//        resultVO.setSumOtherIncome((exportIncomeTotalVO.getOtherIncome() + exportIncomeTotalVO.getSumRegIncome()) /100);
//        resultVO.setSumOtherOutcome(Math.abs(exportIncomeTotalVO.getOtherOutcome()) /100);
////        resultVO.setSumRegIncome(exportIncomeTotalVO.getSumRegIncome() /100);
//        resultVO.setSumNetworkFeeIncome(networkFeeIncome /100);
//        resultVO.setSumMallIncome(shopIncome /100);
//        resultVO.setSumOtherCashIncome(otherCashIncome /100);
//        resultVO.setSumNetIncome(netIncome /100);
//        return resultVO;
//    }
//}
//
