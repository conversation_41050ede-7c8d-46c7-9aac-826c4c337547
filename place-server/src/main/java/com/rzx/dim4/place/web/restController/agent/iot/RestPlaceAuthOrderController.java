package com.rzx.dim4.place.web.restController.agent.iot;

import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.feign.IotServerService;
import com.rzx.dim4.base.utils.ExcelUtil;
import com.rzx.dim4.base.utils.SignUtils;
import com.rzx.dim4.place.entity.PlaceAccount;
import com.rzx.dim4.place.entity.PlaceAgent;
import com.rzx.dim4.place.service.PlaceAccountService;
import com.rzx.dim4.place.service.PlaceAgentService;
import com.rzx.dim4.place.service.agent.iot.NewPlaceAuthOrderService;
import com.rzx.dim4.place.util.TokenUtil;
import com.rzx.dim4.place.web.cons.WebConstants;
import com.rzx.dim4.place.web.controller.admin.BaseController;
import com.rzx.dim4.place.web.vo.ExportAuthOrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("/rspApi/iot/authOrder")
public class RestPlaceAuthOrderController extends BaseController {

    @Autowired
    private NewPlaceAuthOrderService newPlaceAuthOrderService;
    
    /**
     * 场所实名配置
     */
    @GetMapping("/authOrders")
    public GenericResponse<ResponsePage<LogAuthFeeBO>> authOrders(HttpServletRequest request,
                                                                  @RequestParam(name = "length", defaultValue = "10") int length,
                                                                  @RequestParam(name = "start", defaultValue = "0") int start,
                                                                  @RequestParam(name = "type", required = false, defaultValue = "") String type,
                                                                  @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
                                                                  @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
                                                                  @RequestParam(name = "ldOrderId", required = false, defaultValue = "") String ldOrderId,
                                                                  @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
                                                                  @RequestParam(name = "source", required = false, defaultValue = "") String source,
                                                                  @RequestParam(name = "status", required = false, defaultValue = "") String status,
                                                                  @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
                                                                  @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
                                                                  @RequestParam(name = "startTime", required = false) String startTime,
                                                                  @RequestParam(name = "endTime", required = false) String endTime,
                                                                  @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode) {
        PlaceAccount loginAccount = TokenUtil.getLoginAccount(request);

        return newPlaceAuthOrderService.authOrders(loginAccount,0,length,start,type,placeId,orderId,ldOrderId,effectived,source,status,idNumber,placeAuthFeeType,startTime,endTime,searchRegionCode);
    }

    @GetMapping("/queryPlaceAuthFeeByFaceId")
    public GenericResponse<ObjDTO<LogAuthFeeBO>> queryPlaceAuthFeeByFaceId(@RequestParam(name = "placeId") String placeId,
                                                                           @RequestParam(name = "faceId") String faceId,
                                                                           @RequestParam(name = "startTime") String startTime) {
        return newPlaceAuthOrderService.queryPlaceAuthFeeByFaceId(placeId,faceId,startTime);
    }
    


	/**
     * 认证费用列表导出
     */
    @RequestMapping(value="/export", method= RequestMethod.GET)
    public void exportAuthOrders  (HttpServletRequest request,
                                   @RequestParam(name = "type", required = false, defaultValue = "") String type,
                                   @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
                                   @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
                                   @RequestParam(name = "ldOrderId", required = false, defaultValue = "") String ldOrderId,
                                   @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
                                   @RequestParam(name = "source", required = false, defaultValue = "") String source,
                                   @RequestParam(name = "status", required = false, defaultValue = "") String status,
                                   @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
                                   @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode,
                                   @RequestParam(name = "dataPage", required = false, defaultValue = "0") int dataPage,
                                   HttpServletResponse response) {

        PlaceAccount loginAccount = TokenUtil.getLoginAccount(request);

        newPlaceAuthOrderService.exportAuthOrders(loginAccount,type,placeId,orderId,ldOrderId,effectived,source,status,idNumber,placeAuthFeeType,startTime,endTime,searchRegionCode,dataPage,response);
    }
    

    /**
     * 查询认证订单统计数据
     * @param type
     * @param placeId
     * @param orderId
     * @param ldOrderId
     * @param effectived
     * @param idNumber
     * @param placeAuthFeeType
     * @param startTime
     * @param endTime
     * @param searchRegionCode
     * @return
     */
    @GetMapping("/queryFeeStatistics")
    public GenericResponse<SimpleObjDTO> queryFeeStatistics (HttpServletRequest request,
                                                             @RequestParam(name = "type", required = false, defaultValue = "") String type,
                                                             @RequestParam(name = "placeId", required = false, defaultValue = "") String placeId,
                                                             @RequestParam(name = "orderId", required = false, defaultValue = "") String orderId,
                                                             @RequestParam(name = "ldOrderId", required = false, defaultValue = "") String ldOrderId,
                                                             @RequestParam(name = "effectived", required = false, defaultValue = "") String effectived,
                                                             @RequestParam(name = "idNumber", required = false, defaultValue = "") String idNumber,
                                                             @RequestParam(name = "placeAuthFeeType", required = false, defaultValue = "") String placeAuthFeeType,
                                                             @RequestParam(name = "startTime", required = false) String startTime,
                                                             @RequestParam(name = "endTime", required = false) String endTime,
                                                             @RequestParam(name = "searchRegionCode", required = false, defaultValue = "") String searchRegionCode,
                                                             @RequestParam(name = "status", required = false, defaultValue = "") String status,
                                                             @RequestParam(name = "source", required = false, defaultValue = "") String source,
                                                             @RequestParam(name = "statName", required = false, defaultValue = "") String statName) {
        PlaceAccount loginAccount = TokenUtil.getLoginAccount(request);

        return newPlaceAuthOrderService.queryFeeStatistics(loginAccount,type,placeId,orderId,ldOrderId,effectived,idNumber,placeAuthFeeType,startTime,endTime,searchRegionCode,status,source,statName);
    }
    
}
