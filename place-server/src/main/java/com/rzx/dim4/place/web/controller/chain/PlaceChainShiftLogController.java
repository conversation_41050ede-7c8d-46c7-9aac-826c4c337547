//package com.rzx.dim4.place.web.controller.chain;
//
//import com.rzx.dim4.base.bo.billing.*;
//import com.rzx.dim4.base.bo.shop.OrderRefundBO;
//import com.rzx.dim4.base.bo.shop.OrdersBO;
//import com.rzx.dim4.base.dto.ListDTO;
//import com.rzx.dim4.base.dto.PagerDTO;
//import com.rzx.dim4.base.enums.ServiceCodes;
//import com.rzx.dim4.base.enums.billing.OperationType;
//import com.rzx.dim4.base.enums.billing.SourceType;
//import com.rzx.dim4.base.enums.payment.PayType;
//import com.rzx.dim4.base.exception.ServiceException;
//import com.rzx.dim4.base.response.GenericResponse;
//import com.rzx.dim4.base.service.feign.BillingServerService;
//import com.rzx.dim4.base.service.feign.ShopServerService;
//import com.rzx.dim4.base.service.feign.billing.PlaceChainLogShiftApi;
//import com.rzx.dim4.base.utils.DateTimeUtils;
//import com.rzx.dim4.base.utils.Dim4StringUtils;
//import com.rzx.dim4.place.entity.PlaceClient;
//import com.rzx.dim4.place.entity.PlaceShift;
//import com.rzx.dim4.place.response.ResponsePage;
//import com.rzx.dim4.place.service.PlaceClientService;
//import com.rzx.dim4.place.service.PlaceShiftService;
//import com.rzx.dim4.place.web.vo.LogOperationVO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Controller;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import javax.servlet.http.HttpSession;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date 2023年06月10日 15:59
// *  用于连锁门店班次日志查询
// */
//@Controller
//@RequestMapping("/chain/shiftLog")
//@Slf4j
//public class PlaceChainShiftLogController {
//
//    @Autowired
//    private BillingServerService billingServerService;
//
//    @Autowired
//    private PlaceChainLogShiftApi placeChainLogOtherIncomeApi;
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//
//    @Autowired
//    private PlaceClientService placeClientService;
//
//    @Autowired
//    private PlaceShiftService placeShiftService;
//
//    @Autowired
//    private ShopServerService shopServerService;
//
//    @Autowired
//    private CkLogOperationApi ckLogOperationApi;
//
//    /**
//     *
//     * @param session
//     * @param idName  姓名
//     * @param idNumber  证件号
//     * @param shiftId  班次id
//     * @param placeId  门店id
//     * @param queryType   查询类型,1.计费现金收入，2.计费线上后入，3.计费退款，4.冲正记录，7结账记录,8开卡记录,13第三方充值
//     * @param draw
//     * @param start
//     * @param size
//     * @param orderColumns
//     * @param order
//     * @return
//     */
//    /*@GetMapping("/initChainLogOperationTable")
//    @ResponseBody
//    public ResponsePage<LogOperationVO> initChainLogOperationTable(HttpSession session,
//                                                                   @RequestParam(value = "idName", defaultValue = "") String idName,
//                                                                   @RequestParam(value = "idNumber", defaultValue = "") String idNumber,
//                                                                   @RequestParam(value = "clientName", defaultValue = "") String clientName,
//                                                                   @RequestParam(name = "shiftId") String shiftId,
//                                                                   @RequestParam(name = "placeId") String placeId,
//                                                                   @RequestParam(name = "queryType") String queryType,
//                                                                   @RequestParam(value = "draw") int draw,
//                                                                   @RequestParam(value = "start", defaultValue = "0") int start,
//                                                                   @RequestParam(value = "length", defaultValue = "10") int size,
//                                                                   @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                                   @RequestParam(name = "order", defaultValue = "desc") String order) {
//
//        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        if(!StringUtils.isEmpty(idNumber)){
//            queryMap.put("idNumber",idNumber);
//        }
//        if(!StringUtils.isEmpty(idName)){
//            queryMap.put("idName",idName);
//        }
//        List<SourceType> sourceTypes = new ArrayList<>();
//        Optional<PlaceShift> placeShift = null;
//        switch (queryType){
//            case "1":
//                sourceTypes.add(SourceType.CASHIER);    // 来源收银台
//                queryMap.put("sourceTypes",sourceTypes);
//                queryMap.put("operationType", OperationType.TOPUP);
//                break;
//            case "4":
//                queryMap.put("operationType", OperationType.REVERSAL);
//                break;
//            case "7":
//                queryMap.put("operationType", OperationType.LOGOUT);
//                if (!StringUtils.isEmpty(clientName)) {
//                    List<PlaceClient> clients = placeClientService.findByPlaceIdAndHostNameLike(placeId, clientName);
//                    if (!CollectionUtils.isEmpty(clients)) {
//                        List<String> clientIds = clients.stream().map(PlaceClient::getClientId).collect(Collectors.toList());
//                        queryMap.put("clientIds", clientIds);
//                    }
//                }
//                //查询班次的当班时间区间
//                placeShift = placeShiftService.findByPlaceIdAndShiftId(placeId, shiftId);
//                if(placeShift.isPresent()){
//                    queryMap.remove("shiftId"); //根据时间段查询
//                    queryMap.put("startDate", DateTimeUtils.getTimeFormat(placeShift.get().getWorkingTime()));
//                    queryMap.put("endDate", DateTimeUtils.getTimeFormat(placeShift.get().getOffWorkingTime()));
//                }
//                break;
//            case "8":
//                queryMap.put("operationType", OperationType.CREATE_CARD);
//                //查询班次的当班时间区间
//                placeShift = placeShiftService.findByPlaceIdAndShiftId(placeId, shiftId);
//                if(placeShift.isPresent()){
//                    queryMap.remove("shiftId"); //根据时间段查询
//                    queryMap.put("startDate", DateTimeUtils.getTimeFormat(placeShift.get().getWorkingTime()));
//                    queryMap.put("endDate", DateTimeUtils.getTimeFormat(placeShift.get().getOffWorkingTime()));
//                }
//                break;
//            case "13":
//                sourceTypes.add(SourceType.MARKET);
//                sourceTypes.add(SourceType.JWELL);
//                sourceTypes.add(SourceType.DABAZHANG);
//                queryMap.put("sourceTypes",sourceTypes);
//                queryMap.put("operationType", OperationType.TOPUP);
//                break;
//        }
//
//        int page = start / size;
//        GenericResponse<PagerDTO<LogOperationBO>> resp;
//        long now = System.currentTimeMillis();
//        resp = ckLogOperationApi.queryPageLogOperation(queryMap, size, page, orderColumns);
//        log.info("调用CK, 耗时:::" + (System.currentTimeMillis() - now));
//        // GenericResponse<PagerDTO<LogOperationBO>> resp = billingServerService.findLogOperation(queryMap, size, page, orderColumns, order);
//        if(ServiceCodes.NO_ERROR.getCode() != resp.getCode()){
//            resp = billingServerService.findLogOperation(queryMap, size, page, orderColumns, order);
//        }
//        ResponsePage<LogOperationVO> response = new ResponsePage<LogOperationVO>();
//        List<LogOperationVO> vos = new ArrayList<>();
//        if (resp.isResult()) {
//            List<LogOperationBO> logOperationBOS = resp.getData().getList();
//            // 身份证加密
//            logOperationBOS.forEach(e->{
//                if(e.getIdNumber().length() == 18){
//                    e.setIdNumber(e.getIdNumber().substring(0,6) + "********" + e.getIdNumber().substring(14,18));
//                }
//                LogOperationVO vo = new LogOperationVO();
//                BeanUtils.copyProperties(e, vo);
//                vos.add(vo);
//            });
//            if("7".equals(queryType)){
//                //结账记录，补充结账信息
//                List<String> loginIds = logOperationBOS.stream().map(LogOperationBO::getLoginId).collect(Collectors.toList());
//                // 内部调用
//                String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
//                stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
//                GenericResponse<ListDTO<LogLoginBO>> logLoginResult = placeChainLogOtherIncomeApi.queryLogLogin(requestTicket, placeId,loginIds);
//                if(logLoginResult.isResult()){
//                    List<LogLoginBO> logLoginBOS = logLoginResult.getData().getList();
//                    vos.forEach(e->{
//                        for (LogLoginBO logLoginBO : logLoginBOS) {
//                            if(e.getLoginId().equals(logLoginBO.getLoginId())){
//                                e.setLoginTime(logLoginBO.getLoginTime());
//                                e.setLogoutTime(logLoginBO.getLogoutTime());
//                                e.setOnlineTime(logLoginBO.getOnlineTime());
//                                e.setTotalAccount(logLoginBO.getTotalAccount());
//                                e.setLogoutOperationCreaterName(logLoginBO.getLogoutOperationCreaterName());
//                                break;
//                            }
//                        }
//                    });
//                }
//                List<String> clientIds = logOperationBOS.stream().map(LogOperationBO::getClientId).collect(Collectors.toList());
//                //补充客户机信息
//                List<PlaceClient> clientList = placeClientService.findClientByPlaceIdAndClientIds(placeId, clientIds);
//                vos.forEach(e->{
//                    for (PlaceClient placeClient : clientList) {
//                        if(e.getClientId().equals(placeClient.getClientId())){
//                            e.setClientName(placeClient.getHostName());
//                            break;
//                        }
//                    }
//                });
//            }
//
//            response.setData(vos);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//
//        }
//        return response;
//    }*/
//
////    /**
////     *
////     * @param session
////     * @param idName  姓名
////     * @param idNumber  证件号
////     * @param shiftId  班次id
////     * @param placeId  门店id
////     * @param queryType   查询类型,1.计费现金收入，2.计费线上收入，3.计费退款，4.冲正记录
////     * @param draw
////     * @param start
////     * @param size
////     * @param orderColumns
////     * @param order
////     * @return  线上收入记录
////     */
//    /*@GetMapping("/initChainLogTopupTable")
//    @ResponseBody
//    public ResponsePage<LogTopupBO> initChainLogTopupTable(HttpSession session,
//                                                          @RequestParam(value = "idName", defaultValue = "") String idName,
//                                                          @RequestParam(value = "idNumber", defaultValue = "") String idNumber,
//                                                          @RequestParam(name = "shiftId") String shiftId,
//                                                          @RequestParam(name = "placeId") String placeId,
//                                                          @RequestParam(name = "queryType") String queryType,
//                                                          @RequestParam(value = "draw") int draw,
//                                                          @RequestParam(value = "start", defaultValue = "0") int start,
//                                                          @RequestParam(value = "length", defaultValue = "10") int size,
//                                                          @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                          @RequestParam(name = "order", defaultValue = "desc") String order) {
//        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        if(!StringUtils.isEmpty(idNumber)){
//            queryMap.put("idNumber",idNumber);
//        }
//        if(!StringUtils.isEmpty(idName)){
//            queryMap.put("idName",idName);
//        }
//        List<String> sourceTypes = new ArrayList<>();
//        if("2".equals(queryType)){//计费线上收入
//            sourceTypes.add(SourceType.CLIENT.name());
//            sourceTypes.add(SourceType.WECHAT.name());
//            sourceTypes.add(SourceType.ALIPAY.name());
//            sourceTypes.add(SourceType.MINIAPP.name());
//            queryMap.put("sourceTypes",String.join(",",sourceTypes));
//            queryMap.put("optType","0");
//        }
//        int page = start / size;
//        GenericResponse<PagerDTO<LogTopupBO>> resp = billingServerService.queryLogTopups(queryMap, size, page);
//        ResponsePage<LogTopupBO> response = new ResponsePage<LogTopupBO>();
//        if (resp.isResult()) {
//            List<LogTopupBO> logOperationBOS = resp.getData().getList();
//            // 身份证加密
//            logOperationBOS.forEach(e->{
//                if(e.getIdNumber().length() == 18){
//                    e.setIdNumber(e.getIdNumber().substring(0,6) + "********" + e.getIdNumber().substring(14,18));
//                }
//            });
//            response.setData(logOperationBOS);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//        }
//        return response;
//    }*/
//
////    /**
////     *
////     * @param session
////     * @param idName  姓名
////     * @param idNumber  证件号
////     * @param shiftId  班次id
////     * @param placeId  门店id
////     * @param queryType   查询类型,1.计费现金收入，2.计费线上收入，3.计费退款，4.冲正记录
////     * @param draw
////     * @param start
////     * @param size
////     * @param orderColumns
////     * @param order
////     * @return  线上收入记录
////     */
//    /*@GetMapping("/initChainLogRefundTable")
//    @ResponseBody
//    public ResponsePage<LogRefundBO> initChainLogRefundTable(HttpSession session,
//                                                           @RequestParam(value = "idName", defaultValue = "") String idName,
//                                                           @RequestParam(value = "idNumber", defaultValue = "") String idNumber,
//                                                           @RequestParam(name = "shiftId") String shiftId,
//                                                           @RequestParam(name = "placeId") String placeId,
//                                                           @RequestParam(name = "queryType") String queryType,
//                                                           @RequestParam(value = "draw") int draw,
//                                                           @RequestParam(value = "start", defaultValue = "0") int start,
//                                                           @RequestParam(value = "length", defaultValue = "10") int size,
//                                                           @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                           @RequestParam(name = "order", defaultValue = "desc") String order) {
//        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        if(!StringUtils.isEmpty(idNumber)){
//            queryMap.put("idNumber",idNumber);
//        }
//        if(!StringUtils.isEmpty(idName)){
//            queryMap.put("idName",idName);
//        }
//        int page = start / size;
//        GenericResponse<PagerDTO<LogRefundBO>> resp = billingServerService.queryLogRefunds(queryMap, size, page);
//        ResponsePage<LogRefundBO> response = new ResponsePage<LogRefundBO>();
//        if (resp.isResult()) {
//            List<LogRefundBO> logOperationBOS = resp.getData().getList();
//            // 身份证加密
//            logOperationBOS.forEach(e->{
//                if(e.getIdNumber().length() == 18){
//                    e.setIdNumber(e.getIdNumber().substring(0,6) + "********" + e.getIdNumber().substring(14,18));
//                }
//            });
//            response.setData(logOperationBOS);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//        }
//        return response;
//    }*/
//
////    /**
////     *
////     * @param session
////     * @param shiftId  班次id
////     * @param placeId  门店id
////     * @param queryType   查询类型,9商品现金收入
////     * @param draw
////     * @param start
////     * @param size
////     * @param orderColumns
////     * @param order
////     * @return  线上收入记录
////     */
//    /*@GetMapping("/initChainShopTable")
//    @ResponseBody
//    public ResponsePage<OrdersBO> initChainShopTable(HttpSession session,
//                                                           @RequestParam(value = "orderId", defaultValue = "") String orderId,
//                                                           @RequestParam(name = "shiftId") String shiftId,
//                                                           @RequestParam(name = "placeId") String placeId,
//                                                           @RequestParam(name = "queryType") String queryType,
//                                                           @RequestParam(value = "draw") int draw,
//                                                           @RequestParam(value = "start", defaultValue = "0") int start,
//                                                           @RequestParam(value = "length", defaultValue = "10") int size,
//                                                           @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                           @RequestParam(name = "order", defaultValue = "desc") String order) {
//        Map<String, String> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        if("9".equals(queryType)){
//            queryMap.put("payType", PayType.CASH.name());
//            queryMap.put("status", "3");
//        }else if("10".equals(queryType)){
//            if(!StringUtils.isEmpty(orderId)){
//                queryMap.put("orderId",orderId);
//            }
//            queryMap.put("payTypeStr", PayType.WECHAT_PAY.name()+","+PayType.WECHAT_MINIAPP.name()+","
//                    +PayType.WECHAT_SCAN.name()+","+PayType.ALIPAY_PAY.name()+","
//                    +PayType.ALIPAY_MINIAPP.name()+","+PayType.ALIPAY_APP.name()+","+PayType.ALIPAY_SCAN.name()+","+PayType.ALIAPY_IOT.name());
//            queryMap.put("status", "3");
//        }
//        int page = start / size;
//        GenericResponse<PagerDTO<OrdersBO>> resp = shopServerService.queryOrders(queryMap, size, page);
//        ResponsePage<OrdersBO> response = new ResponsePage<OrdersBO>();
//        if (resp.isResult()) {
//            List<OrdersBO> ordersBOS = resp.getData().getList();
//            response.setData(ordersBOS);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//        }
//        return response;
//    }*/
//
////    /**
////     *
////     * @param session
////     * @param shiftId  班次id
////     * @param placeId  门店id
////     * @param queryType   查询类型,商超退款
////     * @param draw
////     * @param start
////     * @param size
////     * @param orderColumns
////     * @param order
////     * @return  线上收入记录
////     */
//    /*@GetMapping("/initChainShopReturnTable")
//    @ResponseBody
//    public ResponsePage<OrderRefundBO> initChainShopReturnTable(HttpSession session,
//                                                           @RequestParam(value = "returnType", defaultValue = "") String returnType,
//                                                           @RequestParam(name = "shiftId") String shiftId,
//                                                           @RequestParam(name = "placeId") String placeId,
//                                                           @RequestParam(value = "draw") int draw,
//                                                           @RequestParam(value = "start", defaultValue = "0") int start,
//                                                           @RequestParam(value = "length", defaultValue = "10") int size,
//                                                           @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                           @RequestParam(name = "order", defaultValue = "desc") String order) {
//        Map<String, String> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        if(!StringUtils.isEmpty(returnType)){
//            queryMap.put("status", "4");
//            if("1".equals(returnType)){
//                queryMap.put("payType", PayType.CASH.name());
//            }else if ("2".equals(returnType)){
//                queryMap.put("payType", PayType.BILLING_CARD.name());
//            }else if ("3".equals(returnType)){
//                queryMap.put("payTypeStr", PayType.WECHAT_PAY.name()+","+PayType.WECHAT_MINIAPP.name()+","
//                        +PayType.WECHAT_SCAN.name()+","+PayType.ALIPAY_PAY.name()+","
//                        +PayType.ALIPAY_MINIAPP.name()+","+PayType.ALIPAY_APP.name()+","+PayType.ALIPAY_SCAN.name()+","+PayType.ALIAPY_IOT.name());
//            }
//            GenericResponse<PagerDTO<OrdersBO>> pagerDTOGenericResponse = shopServerService.queryOrders(queryMap, 200, 0);
//            if(!pagerDTOGenericResponse.isResult() || pagerDTOGenericResponse.getData().getList().size() == 0){
//                return new ResponsePage<>(1, 0, new ArrayList<>());
//            }
//            List<String> orderIds = pagerDTOGenericResponse.getData().getList().stream().map(OrdersBO::getOrderId).collect(Collectors.toList());
//            queryMap.put("ordersIdStr",  org.apache.commons.lang.StringUtils.join(orderIds, ","));
//            queryMap.remove("status");
//            queryMap.remove("payType");
//        }
//        int page = start / size;
//        GenericResponse<PagerDTO<OrderRefundBO>> resp = shopServerService.queryOrdersRefunds(queryMap, size, page);
//        ResponsePage<OrderRefundBO> response = new ResponsePage<OrderRefundBO>();
//        if (resp.isResult()) {
//            List<OrderRefundBO> refundBOS = resp.getData().getList();
//            response.setData(refundBOS);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//        }
//        return response;
//    }*/
//
////    /**
////     *
////     * @param session
////     * @param shiftId  班次id
////     * @param placeId  门店id
////     * @param draw
////     * @param start
////     * @param size
////     * @param orderColumns
////     * @param order
////     * @return  线上收入记录
////     */
//    /*@GetMapping("/initChainLogOtherIncomeTable")
//    @ResponseBody
//    public ResponsePage<LogOtherIncomeBO> initChainLogOtherIncomeTable(HttpSession session,
//                                                           @RequestParam(name = "shiftId") String shiftId,
//                                                           @RequestParam(name = "placeId") String placeId,
//                                                           @RequestParam(value = "draw") int draw,
//                                                           @RequestParam(value = "start", defaultValue = "0") int start,
//                                                           @RequestParam(value = "length", defaultValue = "10") int size,
//                                                           @RequestParam(name = "orderColumns", defaultValue = "") String[] orderColumns,
//                                                           @RequestParam(name = "order", defaultValue = "desc") String order) {
//        Map<String, String> queryMap = new HashMap<>();
//        queryMap.put("placeId",placeId);
//        queryMap.put("shiftId",shiftId);
//        int page = start / size;
//        // 内部调用
//        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
//        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
//        GenericResponse<PagerDTO<LogOtherIncomeBO>> resp = placeChainLogOtherIncomeApi.queryLogOtherIncome(requestTicket,queryMap, size, page);
//        ResponsePage<LogOtherIncomeBO> response = new ResponsePage<LogOtherIncomeBO>();
//        if (resp.isResult()) {
//            List<LogOtherIncomeBO> logOperationBOS = resp.getData().getList();
//            response.setData(logOperationBOS);
//            response.setDraw(++draw);
//            response.setRecordsTotal(resp.getData().getTotal());
//            response.setRecordsFiltered(resp.getData().getTotal());
//        }
//        return response;
//    }*/
//}
