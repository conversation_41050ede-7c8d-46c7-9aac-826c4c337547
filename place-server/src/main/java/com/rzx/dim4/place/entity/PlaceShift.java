package com.rzx.dim4.place.entity;

import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 网吧班次信息， 包括交接班相关统计
 * 
 * <AUTHOR>
 * @date 2021年9月10日 上午10:31:19
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "place_shift", indexes = {
		@Index(name = "idx_place_shift_place_id_shift_id", columnList = "place_id, shift_id", unique = true)})
public class PlaceShift extends BaseEntity {

	private static final long serialVersionUID = 3565933743487881065L;

	@Column(name = "place_id", length = 14, nullable = false)
	private String placeId;

	@Column(name = "working_time", nullable = false, updatable = false)
	private LocalDateTime workingTime; // 上班时间

	@Column(name = "status", nullable = false)
	private int status; // 上班状态 0：上班，1：下班

	@Column(name = "shift_id", length = 8, nullable = false, updatable = false)
	private String shiftId; // 班次ID

	@Column(name = "on_duty_account_id", length = 10)
	private String onDutyAccountId; // 当班人ID

	@Column(name = "on_duty_account_name", length = 30)
	private String onDutyAccountName; // 当班人姓名

	@Column(name = "off_working_time")
	private LocalDateTime offWorkingTime; // 下班时间

	@Column(name = "successor_account_id", length = 10)
	private String successorAccountId; // 交接人

	@Column(name = "successor_account_name", length = 30)
	private String successorAccountName;

	// 班次数据统计==========================================================

	@Column(name = "member_card_cash_income", nullable = false)
	private int memberCardCashIncome; // 会员卡充值收入，

	@Column(name = "temporary_card_cash_income", nullable = false)
	private int temporaryCardCashIncome; // 临时卡现金充值总额

	@Column(name = "temporary_card_outcome", nullable = false)
	private int temporaryCardOutcome; // 临时卡现金退款总额

	@Column(name = "total_cash_income", nullable = false)
	private int totalCashIncome; // 现金总收入, memberCardTopupCashIncome + temporaryCardTopupCashIncome - temporaryCardRefundCashAmount

	@Column(name = "topup_online_income", nullable = false)
	private int topupOnlineIncome; // 在线充值收入(计费)

	@Column(name = "topup_online_refund", nullable = false)
	private int topupOnlineRefund; // 在线退款(计费)

	@Column(name = "total_online_income", nullable = false)
	private int totalOnlineIncome; // 线上总收入(计费)

	@Column(name = "other_income", nullable = false)
	private int otherIncome; // 其他收入

	@Column(name = "cash_income", nullable = false)
	private int cashIncome; // 现金收款(计费)

	@Column(name = "cash_outcome", nullable = false)
	private int cashOutcome; // 现金退款(计费)

	@Column(name = "shop_cash_total", nullable = false)
	private int shopCashTotal; // 现金收款(商超)

	@Column(name = "shop_cash_refund", nullable = false)
	private int shopCashRefund; // 现金退款(商超)

	@Column(name = "shop_online_total", nullable = false)
	private int shopOnlineTotal; // 商超线上收款

	@Column(name = "shop_online_refund", nullable = false)
	private int shopOnlineRefund; // 商超线上退款

	@Column(name = "count_good_sale", nullable = false)
	private int countGoodSale; // 商品销售数量

	@Column(name = "count_good_refund", nullable = false)
	private int countGoodRefund; // 商品退款数量

	@Column(name = "shop_card_total", nullable = false)
	private int shopCardTotal; // 商超卡扣

	@Column(name = "shop_loss_total", nullable = false)
	private int shopLossTotal; // 商超内部损耗

	@Column(name = "temporary_card_balance", nullable = false)
	private int temporaryCardBalance; // 临时卡未消费金额

	@Column(name = "total_reversal", nullable = false)
	private int totalReversal; // 冲正金额

	@Column(name = "create_member_card", nullable = false)
	private int createMemberCard; // 开会员数量

//	@Column(name = "member_card_consumption", nullable = false)
//	private int memberCardConsumption; // 会员消耗网费

	@Column(name = "create_temporary_card", nullable = false)
	private int createTemporaryCard; // 开临时卡数量

//	@Column(name = "temporary_card_consumption", nullable = false)
//	private int temporaryCardConsumption; // 临时卡消耗网费

	@Column(name = "last_shift_leave_cash", nullable = false)
	private int lastShiftLeaveCash; // 上班次留下的现金

	@Column(name = "next_shift_handover_cash", nullable = false)
	private int nextShiftHandoverCash; // 留给下个班次金额，建议不小于临时卡未消费总额，页面判断

	@Column(name = "hand_in_cash", nullable = false)
	private int handInCash; // 上缴现金 cashIncome + lastShiftLeaveCash - nextShiftHandoverCash

	@Column(name = "remark", length = 100)
	private String remark; // 备注

	@Column(name = "cashier_id", length = 10, updatable = false)
	private String cashierId; // 收银台Id

	@Column(name = "one_month_card_count", nullable = false)
	private int oneMonthCardCount; // 一个月有效期注册卡数量

	@Column(name = "three_month_card_count", nullable = false)
	private int threeMonthCardCount; // 三个月有效期注册卡数量

	@Column(name = "two_years_card_count",columnDefinition = "integer default 0")
	private int twoYearsCardCount; // 2年有效期注册卡数量

	@Column(name = "one_month_card_amount", nullable = false)
	private int oneMonthCardAmount; // 一个月有效期注册卡金额(总额)

	@Column(name = "three_month_card_amount", nullable = false)
	private int threeMonthCardAmount; // 三个月有效期注册卡金额(总额)

	@Column(name = "two_years_card_amount",columnDefinition = "integer default 0")
	private int twoYearsCardAmount; // 2年有效期注册卡金额(总额)

	@Column(name = "present_amount", nullable = false)
	private int presentAmount; // 赠送金额

	@Column(name = "sum_cash_topup_for_third", nullable = false)
	private int sumCashTopupForThird; // 第三方充值现金收入总额

	@Column(name = "sum_cash_topup_for_third_member", nullable = false)
	private int sumCashTopupForThirdMember; // 第三方充值会员卡收入总额

	@Column(name = "sum_cash_topup_for_third_temp", nullable = false)
	private int sumCashTopupForThirdTemp; //	第三方充值临时卡收入总额

	@Column(name = "sum_present_topup_for_third", nullable = false)
	private int sumPresentTopupForThird; // 第三方充值赠送总额

	@Column(name = "sum_present_topup_for_third_member", nullable = false)
	private int sumPresentTopupForThirdMember; // 第三方充值赠送总额-会员卡

	@Column(name = "sum_present_topup_for_third_temp", nullable = false)
	private int sumPresentTopupForThirdTemp; // 第三方充值赠送总额-临时卡

	@Column(name = "count_temp_surcharge" ,columnDefinition = "integer default 0")
	private int countTempSurcharge; // 临时卡附加费次数

	@Column(name = "count_member_surcharge" ,columnDefinition = "integer default 0")
	private int countMemberSurcharge; // 会员卡附加费次数

	@Column(name = "sum_temp_surcharge" ,columnDefinition = "integer default 0")
	private int sumTempSurcharge; // 临时卡附加费总额

	@Column(name = "sum_member_surcharge" ,columnDefinition = "integer default 0")
	private int sumMemberSurcharge; // 会员卡附加费总额

	/**
	 * 网费收入
	 */
	@Column(name = "internet_fee_income", columnDefinition = "integer default 0")
	private int internetFeeIncome; //销售收入-退款

	/**
	 * 新版商超收入
	 */
	@Column(name = "shop_income", columnDefinition = "integer default 0")
	private int shopIncome;


	/**
	 * 租赁收入
	 */
	@Column(name = "rent_income", columnDefinition = "integer default 0")
	private int rentIncome;

	/**
	 * 本班次中，本金的消费汇总统计
	 */
	@Column(name = "cash_amount_cost_sum", columnDefinition = "integer default 0")
	private int cashAmountCostSum;

	/**
	 * 本班次中，奖励的消费汇总统计
	 */
	@Column(name = "present_amount_cost_sum", columnDefinition = "integer default 0")
	private int presentAmountCostSum;

	/**
	 * 自定义收款
	 */
//	@Column(name = "custom_amount", columnDefinition = "integer default 0")
//	private int customAmount;

	/**
	 * 前班退款金额
	 */
	@Column(name = "pre_refund_amount", columnDefinition = "integer default 0")
	private int preRefundAmount;


	/**
	 * 三方核销金额（美团和抖音）
	 */
	@Column(name = "verify_ccoupon_amount", columnDefinition = "integer default 0")
	private int verifyCouponAmount;


	/**
	 * 美团核销金额
	 */
	@Column(name = "verify_meituan_amount", columnDefinition = "integer default 0")
	private int verifyMeiTuanAmount;

	/**
	 * 抖音核销金额
	 */
	@Column(name = "verify_douyin_amount", columnDefinition = "integer default 0")
	private int verifyDouYinAmount;


	/**
	 * 是否提现打款
	 */
	@Column(name = "withdrawal", columnDefinition = "integer default 0")
	private int withdrawal; // 0代表未提现打款，1代表已提现，完成结算

	public PlaceShiftBO toBO() {
		PlaceShiftBO bo = new PlaceShiftBO();
		BeanUtils.copyProperties(this, bo);
		return bo;
	}

}
