package com.rzx.dim4.base.utils;

import com.rzx.dim4.base.exception.ServiceException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;

class Dim4StringUtilsTest {


    @DisplayName("密码校验")
    @Test
    void isValidPassword() {
        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("");
        });

        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("124a.");
        });

        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("1234");
        });

        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("1234a");
        });

        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("123456");
        });

        // 不通过
        assertThrows(ServiceException.class, () -> {
            Dim4StringUtils.isValidPassword("abcdef");
        });

        // 通过
        Dim4StringUtils.isValidPassword("12345a");

        // 通过
        Dim4StringUtils.isValidPassword("123Asdfa45a.");
    }
}