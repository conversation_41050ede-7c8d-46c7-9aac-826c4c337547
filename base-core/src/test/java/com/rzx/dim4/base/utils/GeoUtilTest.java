package com.rzx.dim4.base.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class GeoUtilTest {

    @Test
    void distance() {

        double lon1 = 116.510958;
        double lat1 = 39.90786;

        double lon2 = 116.510842;
        double lat2 = 39.90777;

        double distance = GeoUtil.distance( 116.38, 39.9,116.40,39.9);
        double distance1 = GeoUtil.getDistance(116.38, 39.9, 116.40,39.9);
        double distance2 = GeoUtil.getDistance( lon1,lat1, lon2, lat2);

        System.out.println(distance);
        System.out.println(distance1);
        System.out.println(distance2);
    }
}