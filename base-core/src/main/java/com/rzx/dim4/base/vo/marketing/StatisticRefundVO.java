package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class StatisticRefundVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -3872569349702982339L;
    private String goodsName; // 商品名称
    private int countRefund; // 退货量
    private float avgRefundPrice; // 退货均价
    private float avgProfitLossPrice; // 损益均价
    private float avgDiscount; // 平均折扣
    private String goodsId; // 商品id

    //汇总字段
    private float profitLossPrice; // 损益金额
}
