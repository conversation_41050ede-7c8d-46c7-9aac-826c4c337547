package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 统计信息表
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel("统计信息表")
@NoArgsConstructor
@AllArgsConstructor
public class StorageGoodsStatisticsResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "合计库存数")
    private int sumGoodsStocksNum;

    @ApiModelProperty(value = "告警商品数")
    private int alarmGoodsCount;
}
