package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsApiHystrix;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsTypeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 商品类型相关接口
 *
 * <AUTHOR>
 * @date 2025年02月20日 14:42
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsTypeApi", fallback = MarketingGoodsTypeApiHystrix.class)
public interface MarketingGoodsTypeApi {

    final String URL = "/feign/marketing/goodsType";

    /**
     * 新增或修改商品种类
     *
     * @param goodsTypeBO
     * @return
     */
    @PostMapping(value = URL + "/saveTag")
    GenericResponse<ObjDTO<GoodsTypeBO>> saveTag(@RequestHeader(value = "request_ticket") String requestTicket,
                                                 @RequestParam(name = "placeId") String placeId,
                                                 @RequestParam(name = "accountId") String accountId,
                                                 @RequestBody @Validated GoodsTypeBO goodsTypeBO);

    /**
     * 分页查询商品分类
     *
     * @param goodsTypeName
     * @param placeId
     * @param page
     * @param size
     * @return
     */
    @GetMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<GoodsTypeBO>> findPageList(@RequestParam(name = "goodsTypeName", defaultValue = "") String goodsTypeName,
                                                        @RequestParam(name = "placeId") String placeId,
                                                        @RequestParam(name = "page", defaultValue = "0") int page,
                                                        @RequestParam(name = "size", defaultValue = "10") int size);





}
