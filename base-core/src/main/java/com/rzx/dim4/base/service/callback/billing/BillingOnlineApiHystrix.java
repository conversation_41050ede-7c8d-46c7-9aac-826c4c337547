package com.rzx.dim4.base.service.callback.billing;

import com.qiniu.util.Json;
import com.rzx.dim4.base.bo.billing.BillingOnlineBO;
import com.rzx.dim4.base.bo.billing.MiniAppBillingOnlineBO;
import com.rzx.dim4.base.bo.billing.third.OnlineBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.dto.*;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingOnlineApi;
import com.rzx.dim4.base.service.feign.billing.param.BillingOnlineParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BillingOnlineApiHystrix implements BillingOnlineApi {


    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> queryBillingOnlineByPlaceId(String placeId) {
        log.error("接口异常::: queryBillingOnlineByPlaceId {}", Json.encode(placeId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 当前场所是否有在线用户
     *
     * @param placeId 场所id
     * @return true 有 false 没有
     */
    @Override
    public GenericResponse<SimpleDTO> haveOnlineUser(String requestTicket, String placeId) {
        log.error("接口异常::: haveOnlineUser {}", Json.encode(placeId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingOnlineBO>> queryBillingOnlinePage(BillingOnlineParam billingOnlineParam, Pageable pageable) {
        log.error("接口异常::: queryOnlineUserPage {}", Json.encode(billingOnlineParam));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> queryBillingOnline(BillingOnlineParam billingOnlineParam) {
        log.error("接口异常::: queryOnlineUser {}", Json.encode(billingOnlineParam));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingOnlineBO>> findBillingPager(Map<String, Object> queryMap, int page, int size, String order, String[] orderColumns) {
        log.error("接口异常:::findBillingPager(size:::{},page:::{},order:::{},orderColumns:::{},queryMap:::{})", size, page, order,
                orderColumns, queryMap);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingOnlineBO>> queryLastOnlinePlaceByIdNumber(String idNumber) {
        log.error("接口异常:::queryLastOnlinePlaceByIdNumber(idNumber:::{})", idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OnlineBO>> queryOnlineInfo(String placeId, String idNumber) {
        log.error("接口异常:::queryOnlineInfo(placeId:::{}, idNumber:::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OnlineBO>> findUserOnlineInfo(String placeId, String idNumber) {
        log.error("接口异常:::findUserOnlineInfo(placeId={}, idNumber={})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<MiniAppBillingOnlineBO>> queryMiniAppBillingOnlineByPlaceId(String placeId, String areaId, String search,String packageFlag,int page, int size) {
        log.error("接口异常:::queryMiniAppBillingOnlineByPlaceId(placeId={}, areaId={}, clientName={}, idNumber={},packageFlag={})", placeId, areaId, search,packageFlag);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<MiniAppBillingOnlineBO>> queryMiniAppBillingOnlineByCondition(String placeId, String areaId, String search, String packageFlag) {
        log.error("接口异常:::queryMiniAppBillingOnlineByPlaceId(placeId={}, areaId={}, clientName={}, idNumber={},packageFlag={})", placeId, areaId, search,packageFlag);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


    @Override
    public GenericResponse<ObjDTO<MiniAppBillingOnlineBO>> queryBillingOnlineUserByPlaceIdAndIdNumber(String placeId, String clientId, String idNumber) {
        log.error("接口异常:::queryBillingOnlineUserByPlaceIdAndIdNumber(placeId={},areaId={},clientId={},idNumber={})", placeId,clientId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
