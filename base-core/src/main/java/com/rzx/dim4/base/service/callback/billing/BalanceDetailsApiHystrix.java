package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BalanceDetailsBO;
import com.rzx.dim4.base.bo.billing.ExportInternetFeeOrderBO;
import com.rzx.dim4.base.bo.place.PlaceShiftSumBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.BalanceDetailOperationType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BalanceDetailsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2025/3/4
 **/
@Slf4j
@Service
public class BalanceDetailsApiHystrix implements BalanceDetailsApi {
    @Override
    public GenericResponse<ListDTO<ExportInternetFeeOrderBO>> findBalanceDetailsExportData(Map<String, String> queryMap) {
        log.error("接口异常:::findBalanceDetailsExportData(queryMap:::{})", queryMap);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BalanceDetailsBO>> findBalanceDetailsDataPage(Map<String, String> queryMap, int page, int size) {
        log.error("接口异常:::findBalanceDetailsDataPage(queryMap:::{},size:::{},page:::{})", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BalanceDetailsBO>> queryBalanceDetailsList(String placeId, LocalDateTime startTime, LocalDateTime endTime, BalanceDetailOperationType operationType) {
        log.error("接口异常:::queryBalanceDetailsList(placeId:::{},size:::{},page:::{},BalanceDetailOperationType:::{})", placeId, startTime, endTime, operationType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftSumBO>> queryPlaceShiftStatisticsInfo(String placeId, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("接口异常:::queryPlaceShiftStatisticsInfo(placeId:::{},size:::{},page::::{})", placeId, startTime, endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BalanceDetailsBO>> queryBalanceDetails(Map<String, String> queryMap, int size, int page) {
        log.error("接口异常:::queryBalanceDetails(queryMap:::{},size:::{},page:::{})", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BalanceDetailsBO>> createBalanceDetails(String requestTicket, BalanceDetailsBO balanceDetailsBO) {
        log.error("接口异常:::createBalanceDetails(requestTicket:::{},balanceDetailsBO:::{})", requestTicket, new Gson().toJson(balanceDetailsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> addBalanceDetails(String requestTicket, BalanceDetailsBO balanceDetailsBO) {
        log.error("接口异常:::addBalanceDetails(balanceDetailsBO:::{})", new Gson().toJson(balanceDetailsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
