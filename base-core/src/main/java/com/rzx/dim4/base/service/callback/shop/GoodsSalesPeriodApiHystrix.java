package com.rzx.dim4.base.service.callback.shop;

import com.rzx.dim4.base.bo.shop.GoodsSalesPeriodBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.goods.GoodsSalesPeriodApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024年07月31日 18:05
 */
@Slf4j
@Service
public class GoodsSalesPeriodApiHystrix implements GoodsSalesPeriodApi {
    @Override
    public GenericResponse<?> saveOrUpdate(String requestTicket, GoodsSalesPeriodBO goodsSalesPeriodBO) {

        log.error("接口异常:::GoodsSalesPeriodApi.saveOrUpdate(),goodsSalesPeriodBO:::{},requestTicket:::{}",goodsSalesPeriodBO,requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> findAllByPlaceId(String placeId) {
        log.error("接口异常:::GoodsSalesPeriodApi.findAllByPlaceId(),placeId:::{}",placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
