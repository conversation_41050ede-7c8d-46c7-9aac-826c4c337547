package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR> hwx
 * @Description TODO
 * @Date 2025/2/5 11:38
 */
@Data
public class StatisticsSupplierLedgerExportVO {
    @ExcelProperty(value = "供应商名称", index = 0)
    @ColumnWidth(20)
    private String supplierName;

    @ExcelProperty(value = "采购商品品类", index = 1)
    @ColumnWidth(20)
    private int goodsKind;

    @ExcelProperty(value = "采购单总额", index = 2)
    @ColumnWidth(20)
    private float money;

    @ExcelProperty(value = "待结算总额", index = 3)
    @ColumnWidth(20)
    private float settleMoney;
}
