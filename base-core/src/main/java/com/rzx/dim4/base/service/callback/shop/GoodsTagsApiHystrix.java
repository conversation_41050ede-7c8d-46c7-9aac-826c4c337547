package com.rzx.dim4.base.service.callback.shop;

import com.rzx.dim4.base.bo.shop.GoodsTagsBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.goods.GoodsTagsApi;
import com.rzx.dim4.base.response.ResponsePage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年07月19日 14:49
 */
@Slf4j
@Service
public class GoodsTagsApiHystrix implements GoodsTagsApi {
    @Override
    public GenericResponse<ResponsePage<GoodsTagsBO>> findPage(GoodsTagsBO goodsTagsBO, int size, int page) {
        log.error("接口异常:::findPage(),goodsTagsBO:::{},size:::{},page:::{}",goodsTagsBO,size,page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsTagsBO>> findGoodsTagsByTagsId(String placeId, String tagsId) {
        log.error("接口异常:::findGoodsTageByTagsId(),placeId:::{},tagsId:::{}",placeId,tagsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsTagsBO>> findGoodsTagsByGoodsId(String placeId, String goodsId) {
        log.error("接口异常:::findGoodsTagsByGoodsId(),placeId:::{},goodsId:::{}",placeId,goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveOrUpdate(String requestTicket, GoodsTagsBO goodsTagsBO) {
        log.error("接口异常:::saveOrUpdate(),goodsTagsBO:::{},requestTicket:::{}",goodsTagsBO,requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
