package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceExpiredRemindApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * 
 * @version 1.00
 * @since 2024-10-16
 * <AUTHOR>
 * 
 *         Modified History:
 *
 */
@Slf4j
@Service
public class PlaceExpiredRemindApiHystrix implements PlaceExpiredRemindApi {
	@Override
	public GenericResponse<?> placeExpiredRemind() {
		log.error("接口异常，placeExpiredRemind");
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
}
