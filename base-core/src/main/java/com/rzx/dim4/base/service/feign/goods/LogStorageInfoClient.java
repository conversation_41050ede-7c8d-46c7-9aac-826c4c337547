package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.LogStorageStocksBO;
import com.rzx.dim4.base.bo.shop.GoodsStocksDetailBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import com.rzx.dim4.base.service.feign.goods.query.GoodsStocksDetailQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;

@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "LogStorageInfo", fallback = ShopServerServiceHystrix.class)
public interface LogStorageInfoClient {

    String URL="/admin/shop/logStorageInfo";

    /**
     * 库存变更
     * @param logStockRefundInfoQuery
     * @param pageable
     * @return
     */
    @GetMapping(URL+"/listPage")
    GenericResponse<PagerDTO<GoodsStocksDetailBO>> listPage(@SpringQueryMap GoodsStocksDetailQuery logStockRefundInfoQuery, Pageable pageable);

    /**
     * 库存变更记录
     * @param logStockRefundInfoQuery
     * @return
     */
    @GetMapping(URL+"/listLogStorageInfo")
    GenericResponse<ListDTO<GoodsStocksDetailBO>> listLogStorageInfo(@SpringQueryMap GoodsStocksDetailQuery logStockRefundInfoQuery);


    /**
     * 进退货详情
     * @param logStorageInfoQuery
     * @param pageable
     * @return
     */
    @GetMapping(URL+"/listLogStorageChangPage")
    GenericResponse<PagerDTO<LogStorageStocksBO>> logChangRecordPage(@SpringQueryMap GoodsStocksDetailQuery logStorageInfoQuery, Pageable pageable);


    /**
     * 查询进货详情
     * @param logStorageInfoQuery
     * @return
     */
    @GetMapping(URL+"/listLogStorageChang")
    GenericResponse<ListDTO<LogStorageStocksBO>> logChangRecord(@SpringQueryMap GoodsStocksDetailQuery logStorageInfoQuery);


    @GetMapping(URL+"/queryStatisticsGoodsByMonth")
     GenericResponse<ListDTO<GoodsStocksDetailBO>> queryStatisticsGoodsByMonth(@SpringQueryMap GoodsStocksDetailQuery logStorageInfoQuery);

}
