package com.rzx.dim4.base.service.callback;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.payment.*;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @date 2021年1月18日 下午3:35:27
 */
@Slf4j
@Service
public class PaymentServerServiceHystrix implements PaymentServerService {

	@Override
	public GenericResponse<ObjDTO<PaymentResultBO>> createPaymentOrder(String requestTicket,
			PaymentRequestBO paymentRequestBO) {
		log.error("接口异常:::createPaymentOrder(requestTicket:{}, request:{})", requestTicket,
				new Gson().toJson(paymentRequestBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	@Override
	public GenericResponse<ObjDTO<PaymentResultBO>> syncCreatePaymentOrder(PaymentOrderSyncBO paymentOrderSyncBO) {
		log.error("接口异常:::syncCreatePaymentOrder(request:{})", new Gson().toJson(paymentOrderSyncBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PaymentOrderBO>> queryPaymentOrder(String orderId) {
		log.error("接口异常:::queryPaymentOrder(orderId:{}) ", orderId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PaymentOrderBO>> queryPaymentOrders(Map<String, String> queryMap, int size,
			int page) {
		log.error("接口异常:::queryPaymentOrders(queryMap:{}, size:{}, page:{}) ", queryMap, size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PaymentRefundOrderBO>> queryPaymentRefundOrders(Map<String, String> queryMap,
			int size, int page) {
		log.error("接口异常:::queryPaymentRefundOrders(queryMap:{}, size:{}, page:{}) ", queryMap, size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<?> notifyPaymentOrderSync(String orderId,Integer poundage) {
		log.error("接口异常:::notifyPaymentOrderSync(orderId:{},poundage:{}) ",  orderId,poundage);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PaymentRefundOrderBO>> refundPaymentOrder(String requestTicket, String placeId, String orderId,
			int refundAmount) {
		log.error("接口异常:::refundPaymentOrder(placeId:{}, orderId:{}, refundAmount:{}) ", placeId, orderId, refundAmount);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PaymentWithdrawalResultBO>> createWithdrawal(String requestTicket, String placeId,String applyNo, int applyAmt, @RequestParam String merUsername) {
		log.error("接口异常:::createWithdrawal(placeId:{},applyNo:{},applyAmt:{},merUsername={}) ", placeId, applyNo,applyAmt, merUsername);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PaymentWithdrawalResultBO>> queryWithdrawal(String requestTicket, String applyNo, String merUsername) {
		log.error("接口异常:::queryWithdrawal(applyNo:{},merUsername:{}) ", applyNo,merUsername);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

}
