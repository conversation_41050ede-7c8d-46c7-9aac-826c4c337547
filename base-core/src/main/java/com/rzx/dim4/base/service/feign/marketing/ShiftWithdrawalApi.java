package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.ShiftWithdrawalBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.ShiftWithdrawalApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025年03月03日 16:55
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "ShiftWithdrawalApi", fallback = ShiftWithdrawalApiHystrix.class)
public interface ShiftWithdrawalApi {

    final String URL = "/feign/marketing/shiftWithdrawal";

    // 发起交班提现打款计算
    @PostMapping(URL + "/schedulerComputeAmount")
    public void schedulerComputeAmount();


    // 网吧提现分账
    @PostMapping(URL + "/schedulerWithdrawalAmount")
    public void schedulerWithdrawalAmount();


    // 检测未提现成功的网吧提现记录，进行重试
    @PostMapping(URL + "/schedulerWithdrawalCheck")
    public void schedulerWithdrawalCheck();

    // 查询提现申请记录(分账记录)
    @PostMapping(URL + "/findList")
    public GenericResponse<PagerDTO<ShiftWithdrawalBO>> findList(@RequestParam(name = "placeId") String placeId,
                                                                 @RequestParam(name = "startDate") String startDate,
                                                                 @RequestParam(name = "endDate") String endDate,
                                                                 @RequestParam int page, @RequestParam int size);

}
