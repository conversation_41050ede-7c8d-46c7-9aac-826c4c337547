package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceAreaBriefBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceAreaApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025-05-20
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceAreaApi", fallback = PlaceAreaApiHystrix.class)
public interface PlaceAreaApi {
    String URL = "/feign/place/area";


    @GetMapping(URL + "/findByPlaceId")
    GenericResponse<ListDTO<PlaceAreaBriefBO>> findByPlaceId(@RequestParam String placeId);
}
