package com.rzx.dim4.base.vo.place;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzx.dim4.base.excel.LocalDateTimeConverter;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/** 交班数据查询导出vo
 * <AUTHOR> hwx
 * @since 2025/2/21 16:14
 */
@Getter
@Setter
@NoArgsConstructor
public class PlaceShiftQueryExportVo implements Serializable {

    private static final long serialVersionUID = -3309707277413789079L;

    /**
     * 交班人
     */
    @ExcelProperty(value = "交班人", index = 0)
    private String onDutyAccountName;
    /**
     * 交班时间
     */
    @ExcelProperty(value = "交班时间", index = 1,converter = LocalDateTimeConverter.class)
    private LocalDateTime offWorkingTime;

    /**
     * 留给下个班次金额
     */
    @ExcelProperty(value = "留给下个班次金额", index = 2)
    private int nextShiftHandoverCash;

    /**
     * 线上收入
     */
    @ExcelProperty(value = "线上收入", index = 3)
    private int totalIncome;

    /**
     * 网费收入
     */
    @ExcelProperty(value = "网费收入", index = 4)
    private int internetFeeIncome;

    /**
     * 商超收入
     */
    @ExcelProperty(value = "商超收入", index = 5)
    private int shopIncome;

    /**
     * 前班退款
     */
    @ExcelProperty(value = "前班退款", index = 6)
    private int preRefundAmount;

    /**
     * 盘点库存差
     */
    @ExcelProperty(value = "盘点库存差", index = 7)
    private int storageDiff;

    public static PlaceShiftQueryExportVo valueOf(PlaceShiftQueryItemVo placeShiftQueryItemVo) {
        PlaceShiftQueryExportVo vo = new PlaceShiftQueryExportVo();
        BeanUtils.copyProperties(placeShiftQueryItemVo, vo);
        return vo;
    }
}
