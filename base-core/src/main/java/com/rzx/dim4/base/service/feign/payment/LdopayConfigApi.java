package com.rzx.dim4.base.service.feign.payment;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.payment.LdopayConfigApiHystrix;
import com.rzx.dim4.base.service.feign.payment.param.BindAccountBO;
import com.rzx.dim4.base.service.feign.payment.param.LdopayInfoBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/24
 **/
@Primary
@FeignClient(value = FeginConstant.PAYMENT_SERVER, contextId = "ldopayConfigApi", fallback = LdopayConfigApiHystrix.class)
public interface LdopayConfigApi {

    String URL = "/feign/payment/ldopay/config";

    @PostMapping(URL + "/queryAccount")
    GenericResponse<ObjDTO<LdopayInfoBO>> queryAccount(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestParam(value = "placeId", required = false) String placeId,
                                                       @RequestParam(value = "mobile", required = false) String mobile);

    @PostMapping(URL + "/bind")
    GenericResponse<ObjDTO<BindAccountBO>> bind(@RequestHeader(value = "request_ticket") String requestTicket,
                                                @RequestParam String placeId,
                                                @RequestParam String merUsername);
}
