package com.rzx.dim4.base.enums.marketing;

import com.rzx.dim4.base.enums.Convertor.AbstractEnumConverter;
import com.rzx.dim4.base.enums.Convertor.PersistEnum2DB;
import com.rzx.dim4.base.enums.billing.ActiveType;

/**
 * <AUTHOR>
 * @date 2024年08月05日 14:21
 */
public enum LogInterfaceUrl implements PersistEnum2DB<Integer> {

    /**
     * DOUYIN_QUERY_POI接口请求返回内容样例
     * {
     *     "data": {
     *         "pois": [
     *             {
     *                 "account": {
     *                     "poi_account": {
     *                         "account_type": "Trademark",
     *                         "account_id": "7377987482472499212",
     *                         "account_name": "桥外桥网咖"
     *                     }
     *                 },
     *                 "poi": {
     *                     "longitude": 83.266092,
     *                     "poi_id": "6601130488402806797",
     *                     "poi_name": "桥外桥网咖",
     *                     "address": "则新路061号",
     *                     "latitude": 43.436667
     *                 },
     *                 "root_account": {
     *                     "account_id": "7377987482472499212",
     *                     "account_name": "桥外桥网咖",
     *                     "account_type": "Trademark"
     *                 }
     *             }
     *         ],
     *         "total": 1,
     *         "error_code": 0,
     *         "description": "success"
     *     },
     *     "extra": {
     *         "error_code": 0,
     *         "description": "success",
     *         "sub_error_code": 0,
     *         "sub_description": "",
     *         "logid": "20241010182439AE91663E812F260BE91A",
     *         "now": **********
     *     }
     * }
     */
    DOUYIN_QUERY_POI(1,"douyin","https://open.douyin.com/goodlife/v1/shop/poi/query/"), //抖音-查询门店信息
    DOUYIN_MATCH_TASK(2,"douyin","https://open.douyin.com/goodlife/v1/poi/match/task/submit/"), //抖音-提交门店匹配任务
    DOUYIN_QUERY_MATCH_TASK(3,"douyin","https://open.douyin.com/goodlife/v1/poi/match/task/query/"), //抖音-查询门店匹配任务结果
    DOUYIN_QUERY_MATCH(4,"douyin","https://open.douyin.com/goodlife/v1/poi/match/relation/query/"), //抖音-查询门店匹配关系

    /**
     * {
     *     "data": {
     *         "certificates": [
     *             {
     *                 "book_info": {
     *                     "book_poi_id": "0"
     *                 },
     *                 "certificate_id": 7425464640249790503,
     *                 "encrypted_code": "CgYIASAHKAESLgosFX2d2RVEQ0axBSpJjMizkj+Yu2TLLn8zRGcofPas4tPk/9pv9Rt94XBH8dIaAA==",
     *                 "expire_time": **********,
     *                 "sku": {
     *                     "sku_id": "****************",
     *                     "sold_start_time": **********,
     *                     "third_sku_id": "3001",
     *                     "title": "内部测试套餐，请勿购买",
     *                     "account_id": "7414412708457596962",
     *                     "groupon_type": 1,
     *                     "market_price": 990
     *                 },
     *                 "start_time": **********,
     *                 "use_time_info": {
     *                     "use_time_type": 1
     *                 },
     *                 "amount": {
     *                     "brand_ticket_amount": 0,
     *                     "coupon_pay_amount": 900,
     *                     "list_market_amount": 990,
     *                     "original_amount": 900,
     *                     "pay_amount": 900
     *                 }
     *             }
     *         ],
     *         "order_id": "1072244481118420926",
     *         "verify_token": "15a171a1-5aea-46b6-8aa8-80badbafef50",
     *         "error_code": 0,
     *         "description": "success"
     *     },
     *     "extra": {
     *         "error_code": 0,
     *         "description": "success",
     *         "sub_error_code": 0,
     *         "sub_description": "",
     *         "logid": "20241014150334223CA9EE4EB7FB1FC419",
     *         "now": 1728889415
     *     }
     * }
     */
    DOUYIN_COUPON_FULFILMENT_PREPARE(5,"douyin","https://open.douyin.com/goodlife/v1/fulfilment/certificate/prepare/"), //抖音-验券准备

    /**
     * {
     *     "data": {
     *         "verify_results": [
     *             {
     *                 "msg": "履约成功",
     *                 "order_id": "1072244481118420926",
     *                 "origin_code": "***************",
     *                 "result": 0,
     *                 "verify_id": "7425951445905737766",
     *                 "account_id": "7414412708457596962",
     *                 "certificate_id": "7425464640249790503",
     *                 "code": "CgYIASAHKAESLgosqgjGYlVnK4ZM+Eg3Zyl8ru4yJylKDG4NgB3px+QZO5dQCA7kn2AQrQxd66QaAA=="
     *             }
     *         ],
     *         "error_code": 0,
     *         "description": "success"
     *     },
     *     "extra": {
     *         "error_code": 0,
     *         "description": "success",
     *         "sub_error_code": 0,
     *         "sub_description": "",
     *         "logid": "20241015184417D51AD66196F5CF0AD619",
     *         "now": **********
     *     }
     * }
     */
    DOUYIN_COUPON_FULFILMENT_VERIFY(6,"douyin","https://open.douyin.com/goodlife/v1/fulfilment/certificate/verify/"), //抖音-验券

    DOUYIN_GOODS_TYPES_QUERY(7,"douyin","https://open.douyin.com/goodlife/v1/goods/category/get/"), //抖音-查询商品品类

    DOUYIN_GOODS_CANCEL_VERIFY(8,"douyin","https://open.douyin.com/goodlife/v1/fulfilment/certificate/cancel/"), //抖音-撤销核销

    MEITUAN_GETOAUTHTOKEN(9,"meituan","https://api-open-cater.meituan.com/oauth/token"), //美团，获取场所授权token
    MEITUAN_REFRESHOAUTHTOKEN(10,"meituan","https://api-open-cater.meituan.com/oauth/refresh"), //美团，刷新场所授权token
    MEITUAN_COUPON_FULFILMENT_PREPARE(11,"meituan","https://api-open-cater.meituan.com/ddzh/tuangou/receipt/prepare"), //美团，验券准备
    MEITUAN_COUPON_FULFILMENT_VERIFY(12,"meituan","https://api-open-cater.meituan.com/ddzh/tuangou/receipt/consume"), //美团，验券
    MEITUAN_GOODS_CANCEL_VERIFY(13,"meituan","https://api-open-cater.meituan.com/ddzh/tuangou/receipt/reverseconsume"), //美团，撤销验券

    DOUYIN_GOODS_ONLINE_QUERY(14,"douyin","https://open.douyin.com/goodlife/v1/goods/product/online/query/"), //抖音，查询商品列表

    MEITUAN_GOODS_ONLINE_QUERY(15,"meituan","https://api-open-cater.meituan.com/ddzh/tuangou/product/queryproductt"), //美团，按商品分类查询商品信息

    ;



    private final int value;
    private final String source;
    private final String url;

    private LogInterfaceUrl(int value, String source ,String url) {
        this.value = value;
        this.source = source;
        this.url = url;
    }


    public int getValue() {
        return value;
    }

    public String getUrl() {
        return url;
    }

    public String getSource() {
        return source;
    }

    /**
     * 根据索引值获取实例对象
     *
     * @param index
     * @return
     */
    public static LogInterfaceUrl getLogInterfaceUrl (int index) {
        for (LogInterfaceUrl logInterfaceUrl : LogInterfaceUrl.values()) {
            if (logInterfaceUrl.getValue() == index) {
                return logInterfaceUrl;
            }
        }
        return null;
    }

    @Override
    public Integer getData() {
        return value;
    }

    public static class Converter extends AbstractEnumConverter<LogInterfaceUrl, Integer> {
        public Converter() {
            super(LogInterfaceUrl.class);
        }
    }
}
