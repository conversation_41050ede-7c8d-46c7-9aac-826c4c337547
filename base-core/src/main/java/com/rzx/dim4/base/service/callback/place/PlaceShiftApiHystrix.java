package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.user.BusinessReportType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceShiftApi;
import com.rzx.dim4.base.vo.marketing.PlaceShiftBusinessReportVO;
import com.rzx.dim4.base.vo.place.PlaceShiftDetailVO;
import com.rzx.dim4.base.vo.place.PlaceShiftQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/6/11
 **/
@Slf4j
@Service
public class PlaceShiftApiHystrix implements PlaceShiftApi {

    @Override
    public GenericResponse<ListDTO<PlaceShiftBO>> queryAll(String requestTicket, HashMap<String, String> queryMap) {
        log.error("接口异常:::queryAll()");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> submit(String requestTicket, String placeId, String accountId, String successorAccountId, String password, int nextShiftHandoverCash, String remark) {
        log.error("接口异常:::submit(),placeId:{},accountId:{},successorAccountId:{},password:{},nextShiftHandoverCash:{},remark:{}", placeId, accountId, successorAccountId, password, nextShiftHandoverCash, remark);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> submitForMiniApp(String requestTicket, String placeId, String accountId, String successorAccountId, int nextShiftHandoverCash, String remark) {
        log.error("接口异常:::submitForMiniApp(),placeId:{},accountId:{},successorAccountId:{},nextShiftHandoverCash:{},remark:{}", placeId, accountId, successorAccountId, nextShiftHandoverCash, remark);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftBO>> findPrePlaceShiftByPlaceIdAndShiftId(String placeId, String shiftId) {
        log.error("接口异常:::findPrePlaceShiftByPlaceIdAndShiftId(),placeId:{},shiftId:{}", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftBO>> findPlaceShiftByPlaceIdAndShiftId(String placeId, String shiftId) {
        log.error("接口异常:::findPlaceShiftByPlaceIdAndShiftId(),placeId:{},shiftId:{}", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftQueryVo>> query(String placeId, String startDateTime, String endDateTime) {
        log.error("接口异常:::query(),placeId:{},startDateTime:{},endDateTime:{}", placeId, startDateTime, endDateTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftDetailVO>> detail(String placeId, String shiftId) {
        log.error("接口异常:::detail(),placeId:{},shiftId:{}", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftQueryVo>> queryLimit(String placeId) {
        log.error("接口异常:::queryLimit(),placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftQueryVo>> queryLimitByRangeTime(String placeId, String startTime, String endTime) {
        log.error("接口异常:::queryLimitByRangeTime(),placeId:{},startDateTime:{},endDateTime:{}", placeId,startTime,endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByCashierId(String placeId, String cashierId) {
        log.error("接口异常:::findWorkingShiftByCashierId(),placeId:{},cashierId:{}", placeId, cashierId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftBO>> findTop1ByPlaceIdAndStatusOrderByIdDesc(String placeId, int status) {
        log.error("接口异常:::findTop1ByPlaceIdAndStatusOrderByIdDesc(),placeId:{},status:{}", placeId, status);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceShiftBO>> queryShiftListBeforeTime(String requestTicket, String startDateTime) {
        log.error("接口异常:::queryShiftListBeforeTime(),requestTicket={},startDateTime:{}",requestTicket, startDateTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> updateWithdrawal(String requestTicket, String placeId, String shiftIds) {
        log.error("接口异常:::updateWithdrawal(),placeId:{},shiftIds:{}", placeId, shiftIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceShiftBO>> findByPlaceIdAndBetweenTime(String placeId, String startTime, String endTime) {
        log.error("接口异常:::findByPlaceIdAndBetweenTime(),placeId:{},startTime:{},endTime:{}", placeId, startTime,endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MiniAppPlaceShiftBO>> getAssociatedShift(String placeId, String shiftIds) {
        log.error("接口异常:::getAssociatedShift(),placeId:{},shiftIds:{}", placeId,shiftIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftDailyBussinessBO>> getBussinessDailyReport(String placeId, String startTime) {
        log.error("接口异常:::getBussinessDailyReport(),placeId:{},startTime:{}", placeId,startTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftWeeklyReportBO>> getBussinessWeekReport(String placeId, String startTime) {
        log.error("接口异常:::getBussinessWeekReport(),placeId:{},startTime:{}", placeId,startTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceShiftMonthlyReportBO>> getBussinessMonthReport(String placeId, String startTime) {
        log.error("接口异常:::getBussinessMonthReport(),placeId:{},startTime:{}", placeId,startTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceShiftBusinessReportVO>> placeShiftBusinessReport(String placeId, BusinessReportType type, int page, int size) {
        log.error("接口异常:::placeShiftBusinessReport(),placeId:{},type:{},page:{},size:{}", placeId,type, page,size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
