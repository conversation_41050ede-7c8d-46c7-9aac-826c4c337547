package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.bo.billing.LogRoomBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 包间上机记录
 * <AUTHOR>
 * @date 2023年09月27日 14:42
 */

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "logRoomApi", fallback = BillingServerServiceHystrix.class)
public interface LogRoomApi {

    String URL="/billing/admin/logRoom";

    /**
     * 根据id和卡号查询包间上机信息
     * @param placeId
     * @param cardId
     * @return
     */
    @GetMapping(URL+"/findLogRoomByPlaceIdAndCardId")
    GenericResponse<ObjDTO<LogRoomBO>> findLogRoomByPlaceIdAndCardId(@RequestParam String placeId, @RequestParam String cardId);
}
