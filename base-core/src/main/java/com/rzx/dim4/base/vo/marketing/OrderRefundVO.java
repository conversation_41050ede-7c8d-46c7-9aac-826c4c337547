package com.rzx.dim4.base.vo.marketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.marketing.OrderRefundGoodsBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import lombok.Getter;
import lombok.Setter;
import org.checkerframework.checker.formatter.qual.Format;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款VO
 */
@Getter
@Setter
public class OrderRefundVO extends AbstractEntityBO {
	private Long id;
	private Long creater;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime created; // 账号创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime updated; // 账号更新时间
	private int deleted;
	private String placeId; // 场所ID
	private String orderId; // 订单ID
	private String refundId; // 退款ID
	private String shiftId; // 场所ID
	private String cashierId; // 收银台ID
	private String cashierName; // 收银台名称
	private int realMoney; // 订单金额
	private int refundAmount; // 退款金额
	private int refundType; // 退款方式，0只退款，1退货退款  2取消订单 ,3 系统退单
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	protected LocalDateTime refundTime; // 订单完成时间
	private String remark; // 订单备注
	private String createrName; // 操作人姓名
	private SourceType sourceType; // 来源

	private List<OrderRefundGoodsBO> orderRefundGoodsBOS;

	private OrdersBO ordersBO;
}
