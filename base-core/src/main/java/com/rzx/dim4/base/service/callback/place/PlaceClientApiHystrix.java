package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceClientApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/7
 **/
@Slf4j
@Service
public class PlaceClientApiHystrix implements Place<PERSON>lientApi {
    @Override
    public GenericResponse<ListDTO<PlaceClientBO>> getFullInfo(String requestTicket, String placeId, List<String> clientIds) {
        log.error("接口异常，getByPlaceId(placeId={} )", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceClientBO>> findByPlaceIdAndClientIds(String placeId, List<String> clientIds) {
        log.error("接口异常，findByPlaceIdAndClientIds(placeId={} )", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceClientBO>> findByPlaceId(String placeId) {
        log.error("接口异常，findByPlaceId(placeId={} )", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndClientName(String placeId, String clientName) {
        log.error("接口异常，findByPlaceIdAndClientName(placeId={},clientName={})", placeId, clientName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
