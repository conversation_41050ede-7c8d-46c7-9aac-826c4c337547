package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel("供应商管理")
public class GoodsReceiptListResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "入库单编号")
    private String goodsReceiptNum;

    @ApiModelProperty(value = "商品id")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "商品数量")
    private int number;

    @ApiModelProperty(value = "商品单价")
    private int price;

    @ApiModelProperty(value = "是否赠品：0否，1是")
    private int isGift;

    @ApiModelProperty(value = "库存总数量(库存列表详情中的总数和)")
    private int goodsStocksNum;

    @ApiModelProperty(value = "库存列表详情")
    private List<StorageGoodsResponseBO.StorageRackInfo> storageRackInfos = new ArrayList<>();
}
