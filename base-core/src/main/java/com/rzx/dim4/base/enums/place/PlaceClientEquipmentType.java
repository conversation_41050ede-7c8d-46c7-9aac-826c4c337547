package com.rzx.dim4.base.enums.place;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** 客户端设备类型
 * <AUTHOR> hwx
 * @since 2025/2/18 18:52
 */
@Getter
@AllArgsConstructor
public enum PlaceClientEquipmentType {
    MOTHERBOARD("主板"),
    CPU("CPU"),
    MEMORY("内存"),
    HARD_DISK("硬盘"),
    VIDEO_CARD("显卡"),
    DISPLAY("显示器"),
    MOUSE("鼠标"),
    KEYBOARD("键盘"),
    HEADPHONES("耳机"),
    NETWORK_CARD("网卡");

    private final String desc;
}
