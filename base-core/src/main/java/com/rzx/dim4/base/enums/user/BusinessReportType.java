package com.rzx.dim4.base.enums.user;

import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 经营报表类型
 */
@Getter
public enum BusinessReportType {

    DAILY("1"),

    WEEKLY("2"),

    MONTHLY("3");

    private final String code;

    BusinessReportType(String code) {
        this.code = code;
    }

    public static Optional<BusinessReportType> getByCode(String code) {
        for (BusinessReportType businessReport : BusinessReportType.values()) {
            if (StringUtils.endsWithIgnoreCase(businessReport.code, code)) {
                return Optional.of(businessReport);
            }
        }
        return Optional.empty();
    }
}