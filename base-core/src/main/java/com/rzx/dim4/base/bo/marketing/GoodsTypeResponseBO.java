package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品类型表
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel(value = "商品类型表")
public class GoodsTypeResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "商品类型排序")
    private int sort;

    @ApiModelProperty(value = "是否展示: 0展示,1不展示")
    private int showSwitch;

    @ApiModelProperty(value = "客户端是否展示: 0展示,1不展示")
    private int showClientSwitch;

    @ApiModelProperty(value = "收银台是否展示: 0展示,1不展示")
    private int showCashierSwitch;

    @ApiModelProperty(value = "移动端是否展示: 0展示,1不展示")
    private int showMobileSwitch;
}