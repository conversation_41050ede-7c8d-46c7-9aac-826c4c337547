package com.rzx.dim4.base.service.callback;

import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingPayApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/18
 **/
@Slf4j
@Service
public class BillingPayApiHystrix implements BillingPayApi {

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> miniCreateOrder(String requestTicket, String placeId, String idNumber, int amount, String openId, String cardTypeId, String idName, String appId) {
        log.error("接口异常:::create():::requestTicket:{},placeId:{},idNumber:{},amount:{},openId:{},cardTypeId:{},idName:{}",
                requestTicket, placeId, idNumber, amount, openId, cardTypeId, idName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> mpCreateOrder(String requestTicket, String placeId, String idNumber, int amount, String cardTypeId, String idName) {
        log.error("接口异常:::create():::requestTicket:{},placeId:{},idNumber:{},amount:{},cardTypeId:{},idName:{}",
                requestTicket, placeId, idNumber, amount, cardTypeId, idName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> newMpCreateOrder(String requestTicket, String placeId, String idNumber,
                                                                     int amount, String cardTypeId, String idName,String returnUrl,
                                                                     String topupRuleId,String openId) {
        log.error("接口异常:::newMpCreateOrder():::requestTicket:{},placeId:{},idNumber:{},amount:{},cardTypeId:{},idName:{},returnUrl:{},topupRuleId:{},openId:{}",
                requestTicket, placeId, idNumber, amount, cardTypeId, idName, returnUrl,topupRuleId,openId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

	@Override
	public GenericResponse<ObjDTO<PaymentResultBO>> iotCreateOrder(String requestTicket, String placeId,
			String cardTypeId, String idNumber, String name, int amount, String payType, String payCode) {
        log.error("接口异常:::iotCreateOrder():::requestTicket:{},placeId:{},cardTypeId:{},idNumber:{},name:{},amount:{},payType:{},payCode:{}",
                requestTicket, placeId, cardTypeId, idNumber, name, amount, payType, payCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<LogTopupBO>> queryLogTopup(String requestTicket, String placeId, String orderId) {
        log.error("接口异常:::queryLogTopup():::requestTicket:{},placeId:{},orderId:{}",requestTicket, placeId, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> aliAppCreateOrder(String requestTicket, String placeId, String idNumber,
                                                                      int amount, String openId, String cardTypeId, String idName, String topupRuleId, String appId) {
        log.error("接口异常:::aliAppCreateOrder():::requestTicket:{},placeId:{},idNumber:{},amount:{},openId:{},cardTypeId:{},idName:{},topupRuleId:{}.appId:{}",
                requestTicket, placeId, idNumber, amount, openId, cardTypeId, idName,topupRuleId,appId);
        return null;
    }
}

