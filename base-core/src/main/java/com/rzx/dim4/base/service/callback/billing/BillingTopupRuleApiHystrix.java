/*
 * @(#)BillingTopupRuleApiHystrix.java 1.00 2024-1-26
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.enums.billing.SourceType;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.billing.third.TopupRuleListBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingTopupRuleApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>Title:</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-1-26
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
@Slf4j
@Service
public class BillingTopupRuleApiHystrix implements BillingTopupRuleApi{
	
	@Override
	public GenericResponse<ListDTO<TopupRuleListBO>> queryTopupRules4Iot(String placeId, String idNumber) {
		log.error("接口异常:::queryTopupRules4Iot(placeId:::{},idNumber:::{})", placeId, idNumber);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<?> couponTopup(String placeId, String idNumber, int cashAmount, int present, SourceType sourceType, String shiftId, String couponName) {
		log.error("接口异常:::couponTopup(placeId:::{},idNumber:::{},cashAmount:::{},present:::{},sourceType:::{},shiftId:::{},couponName:::{})",
				placeId, idNumber,cashAmount,present,sourceType,shiftId,couponName);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

}
