package com.rzx.dim4.base.service.feign.user;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.user.Dim4UserApiHystrix;
import com.rzx.dim4.base.service.callback.user.WeChatUserApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025年06月17日 11:06
 */
@Primary
@FeignClient(value = FeginConstant.USER_SERVER, contextId = "WeChatUserApi", fallback = WeChatUserApiHystrix.class)
public interface WeChatUserApi {

    String URL = "/feign/user/weChatUser";

    @PostMapping(URL + "/createQrCode")
    GenericResponse<SimpleDTO> createQrCode(@RequestParam String placeId,@RequestParam String idName, @RequestParam String idNumber);

}
