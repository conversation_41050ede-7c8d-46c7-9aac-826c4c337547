package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.user.MiniApp.PlaceIncomeBO;
import com.rzx.dim4.base.bo.user.MiniApp.PlaceShiftIncomeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingIncomeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/** 营销收入
 * <AUTHOR>
 * @since 2025/4/26 17:15
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingIncomeApi", fallback = MarketingIncomeApiHystrix.class)
public interface MarketingIncomeApi {

    /**
     * 当班收入查询-网费收入、商超收入、累计收入
     * @param placeId
     * @return
     */
    @GetMapping("/feign/marketing/income/currShiftIncome")
    GenericResponse<ObjDTO<PlaceShiftIncomeBO>> currShiftIncome(@RequestParam String placeId);


    /**
     * 今日累计收入
     * @param placeId
     * @return
     */
    @GetMapping("/feign/marketing/income/todayIncomeTotal")
    GenericResponse<ObjDTO<PlaceIncomeBO>> todayIncomeTotal(@RequestParam String placeId);

    /**
     * 本月三方核销金额
     * @param placeId
     * @return
     */
    @GetMapping("/feign/marketing/income/monthVerifyCoupon")
    GenericResponse<ObjDTO<PlaceIncomeBO>> monthVerifyCoupon(@RequestParam String placeId);


    /**
     * 本月累计收入
     * @param placeId
     * @return
     */
    @GetMapping("/feign/marketing/income/monthIncomeTotal")
    GenericResponse<ObjDTO<PlaceIncomeBO>> monthIncomeTotal(@RequestParam String placeId);
}
