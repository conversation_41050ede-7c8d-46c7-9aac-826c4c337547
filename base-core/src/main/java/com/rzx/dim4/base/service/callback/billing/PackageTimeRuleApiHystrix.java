/*
 * @(#)PackageTimeRuleApiHystrix.java 1.00 2024-1-24
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.third.PackageRuleBO;
import com.rzx.dim4.base.bo.billing.third.PackageRuleBriefBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeePackageSearchBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeePackageTopUpBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PackageTimeRuleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * <p>
 * Modified History:
 * @version 1.00
 * @since 2024-1-24
 */
@Slf4j
@Service
public class PackageTimeRuleApiHystrix implements PackageTimeRuleApi {
    @Override
    public GenericResponse<ListDTO<PackageRuleBriefBO>> queryUserPackageRuleForMiniApp(InternetFeePackageSearchBO paramsBo) {
        log.error("接口异常:::queryUserPackageRuleForMiniApp(InternetFeePackageSearchBO:::{})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PackageRuleBO>> queryUserPackageRule(String placeId, String idNumber) {
        log.error("接口异常:::queryUserPackageRule(placeId:::{},idNumber:::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> packageTime(String placeId, String ruleId, String idNumber) {
        log.error("接口异常:::packageTime(placeId:::{},ruleId:::{}, idNumber:::{})", placeId, ruleId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createPackageTimeOrder(String requestTicket, String placeId, String ruleId,
                                                                           String idNumber, String openId, SourceType sourceType, String returnUrl) {
        log.error("接口异常:::createPackageTimeOrder(placeId:::{},ruleId:::{}, idNumber:::{}, openId:::{}, sourceType:::{}, returnUrl:::{})",
                placeId, ruleId, idNumber, openId, sourceType, returnUrl);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createPackageTimeOrderForMiniApp(@RequestBody InternetFeePackageTopUpBO paramsBo) {
        log.error("接口异常:::createPackageTimeOrderForMiniApp(InternetFeePackageTopUpBO = {})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}