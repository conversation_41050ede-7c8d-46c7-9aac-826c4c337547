package com.rzx.dim4.base.web.config;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Knife4j公共配置类
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
public abstract class AbstractCommonKnife4jConfig {

    @Bean
    public Docket webApiConfig(@Value("${knife4j.enable:false}") boolean swaggerKnife4jEnable) {
        log.info("::::::::::::::::::Knife4jEnable = {}::::::::::::::::::", swaggerKnife4jEnable);

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(webApiInfo())
                .enable(swaggerKnife4jEnable)
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .build();
    }

    public ApiInfo webApiInfo() {
        return new ApiInfoBuilder()
                .title(title())
                .description(description())
                .version(version())
                .build();
    }

    public abstract String title();

    public abstract String description();

    public abstract String version();
}
