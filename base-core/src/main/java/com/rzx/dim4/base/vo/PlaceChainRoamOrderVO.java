package com.rzx.dim4.base.vo;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PlaceChainRoamOrderVO extends AbstractEntityBO implements Serializable {

    private static final long serialVersionUID = -1776838823200957524L;

    private String loginId;
    private String currPlaceId; // 上机门店
    private int sumCurrTotalAccount; // 上机门店总消费
    private int sumCurrCashAccount; // 上机门店消费总本金
    private int sumCurrPresentAccount; // 上机门店消费总奖励
    private int roamCashAccount; // 漫游消费总本金
    private int roamPresentAccount; // 漫游消费总奖励
    private List<PlaceChainRoamVO> placeChainRoamVOS; // 漫游扣费对象List

}
