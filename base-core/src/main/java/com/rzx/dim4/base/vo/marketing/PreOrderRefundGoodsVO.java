package com.rzx.dim4.base.vo.marketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.enums.payment.PayType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/** 前班退款vo
 * <AUTHOR> hwx
 * @since 2025/2/26 14:44
 */
@Getter
@Setter
public class PreOrderRefundGoodsVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 7626376278475392215L;
    private String orderId; // 订单ID
    private String refundId; // 退款ID
    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private Long creater;
    private int amount; // 金额

    private PayType payType;
    private LocalDateTime orderCreated; // 支付订单创建时间
    private LocalDateTime payTime;
}
