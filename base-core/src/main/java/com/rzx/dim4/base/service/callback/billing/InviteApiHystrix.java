package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BalanceDetailsBO;
import com.rzx.dim4.base.bo.billing.InviteConfigBO;
import com.rzx.dim4.base.bo.billing.InviteOnlineBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.InviteApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年04月27日 16:37
 */
@Slf4j
@Service
public class InviteApiHystrix implements InviteApi {


    @Override
    public GenericResponse<ObjDTO<InviteConfigBO>> findByPlaceId(String placeId) {
        log.error("接口异常:::findByPlaceId(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteConfigBO>> save(InviteConfigBO inviteConfigBO) {
        log.error("接口异常:::save(inviteConfigBO:::{})", new Gson().toJson(inviteConfigBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> addInviteOnline(InviteOnlineBO inviteOnlineBO) {
        log.error("接口异常:::addInviteOnline(inviteOnlineBO:::{})", new Gson().toJson(inviteOnlineBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> joinInviteOnline(InviteOnlineBO inviteOnlineBO) {
        log.error("接口异常:::joinInviteOnline(inviteOnlineBO:::{})", new Gson().toJson(inviteOnlineBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> getInviteOnlineDetail(String placeId, String inviteCode) {
        log.error("接口异常:::findByPlaceId(placeId:::{},inviteCode:::{})", placeId,inviteCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> findByPlaceIdAndCardId(String placeId, String cardId) {
        log.error("接口异常:::findByPlaceId(placeId:::{},cardId:::{})", placeId,cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> findInviteOnline(String placeId, String idNumber) {
        log.error("接口异常:::findInviteOnline(placeId:::{},idNumber:::{})", placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<InviteOnlineBO>> queryBillingCardPage(String placeId, String type, String idNumber,String startTime,String endTime,int start,int length, Pageable pageable) {
        log.error("接口异常:::queryBillingCardPage(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BalanceDetailsBO>> queryInviteOnlineDetail(String placeId, String inviteCode) {
        log.error("接口异常:::queryInviteOnlineDetail(placeId:::{},inviteCode:::{})", placeId,inviteCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteById(String placeId, Long id) {
        log.error("接口异常:::deleteById(placeId:::{},id:::{})", placeId,id);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InviteOnlineBO>> findUnderwayInviteOnline(String placeId, String idNumber, String inviteCode) {
        log.error("接口异常:::findUnderwayInviteOnline(placeId:::{},idNumber:::{},inviteCode:::{})", placeId,idNumber,inviteCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
