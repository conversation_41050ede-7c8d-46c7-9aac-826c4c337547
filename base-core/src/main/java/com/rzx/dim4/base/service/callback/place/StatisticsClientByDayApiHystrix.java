package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.service.feign.place.StatisticsClientByDayApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024年09月10日 11:36
 */
@Slf4j
@Service
public class StatisticsClientByDayApiHystrix implements StatisticsClientByDayApi {

    @Override
    public void schedulerStatisticsPlaceClientByDay(int dayAgo) {
        log.error("接口异常:::schedulerStatisticsPlaceClientByDay(dayAgo:::{})",dayAgo);
    }
}
