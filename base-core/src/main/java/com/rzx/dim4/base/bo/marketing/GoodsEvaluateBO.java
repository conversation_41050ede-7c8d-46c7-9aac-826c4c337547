package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年01月15日 16:14
 */
@Getter
@Setter
public class GoodsEvaluateBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String orderId; // 订单ID
    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private int rating; // 商品评分
    private String comment; // 商品评论
    private String cardId; // 计费卡ID
}
