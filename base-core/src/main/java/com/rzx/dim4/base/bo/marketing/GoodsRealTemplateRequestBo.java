package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@ApiModel("固装商品模板查询请求对象")
public class GoodsRealTemplateRequestBo extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "商品名/条形码")
    private String searchKey;

    @ApiModelProperty(value = "当前页（从0开始，异常情况重置为0）")
    private Integer page;

    @ApiModelProperty(value = "每页大小（10-100,不在此范围内，重置为10）")
    private Integer size;
}