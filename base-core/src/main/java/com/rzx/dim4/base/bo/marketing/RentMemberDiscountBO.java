package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * 租赁-会员卡类型折扣
 * <AUTHOR>
 * @date 2025年07月07日 10:00
 */
@Getter
@Setter
public class RentMemberDiscountBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;

    private String placeId;

    @ApiModelProperty(value = "租赁会员卡类型名称")
    private String cardType;  // 会员卡类型名称，场所编辑卡类型时需要同步

    @ApiModelProperty(value = "租赁会员卡类型ID")
    private String cardTypeId;  // 会员卡类型ID

    @ApiModelProperty(value = "会员卡类型对应的押金折扣，默认100")
    private int depositDiscount;  // 押金折扣(默认100)

    @ApiModelProperty(value = "会员卡类型对应的租金折扣，默认100")
    private int rentDiscount;     // 租金折扣(默认100)

}
