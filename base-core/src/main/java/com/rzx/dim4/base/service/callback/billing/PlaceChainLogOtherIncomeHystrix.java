package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.bo.billing.LogOtherIncomeBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceChainLogShiftApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年06月12日 15:48
 */
@Slf4j
@Service
public class PlaceChainLogOtherIncomeHystrix implements PlaceChainLogShiftApi {

    @Override
    public GenericResponse<PagerDTO<LogOtherIncomeBO>> queryLogOtherIncome(String requestTicket,Map<String, String> queryMap, int size, int page) {
        log.error("queryLogOtherIncome 接口异常, requestTicket={},queryMap={}, size={}, page={}",requestTicket, queryMap.toString(),size,page);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogLoginBO>> queryLogLogin(String requestTicket, String placeId, List<String> loginIds) {
        log.error("queryLogLgion 接口异常, requestTicket={},placeId={},loginIds={}",requestTicket, placeId,loginIds.toString());
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
