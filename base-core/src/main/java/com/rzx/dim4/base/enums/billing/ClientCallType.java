package com.rzx.dim4.base.enums.billing;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户端呼叫类型
 *
 * <AUTHOR> hwx
 * @since 2025/3/10 16:48
 */
@AllArgsConstructor
@Getter
public enum ClientCallType {
    /**
     * 网管
     */
    NETWORK_MANAGER(0),
    /**
     * 服务员
     */
    ATTENDANT(1),
    /**
     * 保洁员
     */
    JANITOR(2),
    /**
     * 留言
     */
    LEAVE_WORD(3);




    private final int code;

    public static ClientCallType getByCode(int code) {
        for (ClientCallType clientCallType : ClientCallType.values()) {
            if (clientCallType.getCode() == code) {
                return clientCallType;
            }
        }
        throw new ServiceException(ServiceCodes.BILLING_CLIENT_ERROR_CALL_TYPE);
    }
}
