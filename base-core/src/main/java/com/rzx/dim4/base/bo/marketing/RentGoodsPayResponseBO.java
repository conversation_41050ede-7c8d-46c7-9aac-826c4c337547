package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Accessors(chain = true)
@ApiModel("租赁商品结算租金")
public class RentGoodsPayResponseBO extends AbstractBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "租赁商品Id")
    private String rentGoodsId;

    @ApiModelProperty(value = "结算合计金额(单位:分)")
    private int totalMoney;

    @ApiModelProperty(value = "结算实际金额，折扣后的租金(单位:分)")
    private int realMoney;

    @ApiModelProperty(value = "租金折扣(100代表不打折，0代表0元，95代表95折扣，0.95)")
    private int discount;

}
