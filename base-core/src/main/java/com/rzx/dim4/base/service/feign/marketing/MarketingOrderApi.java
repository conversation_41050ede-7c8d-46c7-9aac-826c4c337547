package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.MarketOrderStatisticsBO;
import com.rzx.dim4.base.bo.marketing.MiniAppTopUpOrdersBO;
import com.rzx.dim4.base.bo.marketing.OrdersStatusMiniAppBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingOrderApiHystrix;
import com.rzx.dim4.base.vo.marketing.StaticCouponVO;
import com.rzx.dim4.base.vo.marketing.StatisticsShopBusinessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;

/**
 * 订单接口
 *
 * <AUTHOR> hwx
 * @since 2025/2/21 17:15
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingOrderApi", fallback = MarketingOrderApiHystrix.class)
public interface MarketingOrderApi {


    /**
     * 统计交班订单数据
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @param shiftId
     * @param isSubmit
     * @return
     */
    @GetMapping("/feign/marketing/order/statisticsPlaceShift")
    GenericResponse<ObjDTO<MarketOrderStatisticsBO>> statisticsPlaceShift(
            @RequestParam("placeId") String placeId,
            @RequestParam("startDateTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS") LocalDateTime startDateTime,
            @RequestParam("endDateTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS") LocalDateTime endDateTime,
            @RequestParam("shiftId") String shiftId,
            @RequestParam("isSubmit") boolean isSubmit);


    @PostMapping("/feign/marketing/order/createOrderForMiniApp")
    GenericResponse<ObjDTO<PaymentResultBO>> createOrderForMiniApp(@RequestBody MiniAppTopUpOrdersBO ordersBO);

    @PostMapping("/feign/marketing/order/refundOrderForMiniApp")
    GenericResponse<SimpleDTO> refundOrderForMiniApp(@RequestBody InternetFeeOrderRefundBO paramsBo);

    @PostMapping("/feign/marketing/order/refundInternetFeePackageTimeOrderForMiniApp")
    GenericResponse<SimpleDTO> refundInternetFeePackageTimeOrderForMiniApp(@RequestBody InternetFeeOrderRefundBO paramsBo);

    @GetMapping("/feign/marketing/order/findPackageOrderPageListForMiniApp")
    GenericResponse<ObjDTO<InternetFeePackageOrderBO>> findPackageOrderPageListForMiniApp(@SpringQueryMap InternetFeeSearchPackageTimeRecordBO paramsBo);

    @GetMapping("/feign/marketing/order/findTopUpOrderPageListForMiniApp")
    GenericResponse<ObjDTO<InternetFeeTopUpOrderBO>> findTopUpOrderPageListForMiniApp(@SpringQueryMap InternetFeeSearchTopUpRecordBO paramsBo);

    @GetMapping("/feign/marketing/order/queryInternetFeeOrderDetail")
    GenericResponse<ObjDTO<OrdersStatusMiniAppBO>> queryOrderStatus(@RequestParam("placeId") String placeId, @RequestParam("orderId") String orderId);


    /**
     * 统计商超经营概况
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping("/feign/marketing/order/statisticsShopBusinessVO")
    GenericResponse<ObjDTO<StatisticsShopBusinessVO>> statisticsShopBusinessVO(@RequestParam("placeId") String placeId, @RequestParam("shiftId") String shiftId);

    /**
     * 获取优惠券赠送概况
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping("/feign/marketing/order/staticCouponVO")
    GenericResponse<ObjDTO<StaticCouponVO>> staticCouponVO(@RequestParam("placeId") String placeId, @RequestParam("shiftId") String shiftId);

}
