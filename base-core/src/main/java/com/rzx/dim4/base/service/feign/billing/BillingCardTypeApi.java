package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingCardTypeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/9
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "billingCardTypeAPi", fallback = BillingCardTypeApiHystrix.class)
public interface BillingCardTypeApi {

    String URL = "/feign/billing/cardType";

    @PostMapping(URL + "/create")
    GenericResponse<ObjDTO<BillingCardTypeBO>> create(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody BillingCardTypeBO bo);

    @PostMapping(URL + "/delete")
    GenericResponse<SimpleDTO> delete(@RequestHeader(value = "request_ticket") String requestTicket,
                                      @RequestParam Long id);
    @GetMapping(URL + "/findNeedUnionByPlaceId")
    GenericResponse<ListDTO<BillingCardTypeBO>> findNeedUnionByPlaceId(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                       @RequestParam String placeId);

    @GetMapping(URL + "/list/{placeId}")
    GenericResponse<ListDTO<BillingCardTypeBO>> list(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                       @PathVariable String placeId);

    @GetMapping(URL + "/listRemoveTempory/{placeId}")
    GenericResponse<ListDTO<BillingCardTypeBO>> listRemoveTempory(@PathVariable String placeId);

    /**
     * 场所退出连锁时，更新计费卡类型信息
     * @param requestTicket feign请求头
     * @param placeId 场所id
     * @return 更新结果
     */
    @PostMapping(URL + "/exitChain")
    GenericResponse<?> exitChain(@RequestHeader(value = "request_ticket")String requestTicket,
                                 @RequestParam String placeId);

    @PostMapping(URL + "/updateAfterChain")
    GenericResponse<?> updateAfterChain(@RequestHeader(value = "request_ticket")String requestTicket,
                                         @RequestBody List<BillingCardTypeBO> billingCardTypeBOList);

    @GetMapping(URL + "/findByPlaceIdAndChainCardTypeId")
    GenericResponse<ObjDTO<BillingCardTypeBO>> findByPlaceIdAndChainCardTypeId(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                       @RequestParam String placeId,
                                                                       @RequestParam String chainCardTypeId);
    
    @GetMapping(URL + "/findByPlaceId4iot")
    GenericResponse<ListDTO<BillingCardTypeBO>> findByPlaceId4iot(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                       @RequestParam String placeId);

    @PostMapping(URL + "/tempCardUpgrade")
    GenericResponse<SimpleDTO> tempCardUpgrade(@RequestHeader(value = "request_ticket")String requestTicket,
                                       @RequestParam String placeId,
                                       @RequestParam String cardId,
                                       @RequestParam String upgradeCardTypeId);

}
