package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.MemberEventBO;
import com.rzx.dim4.base.bo.marketing.MemberEventPushRecordBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MemberEventApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 会员关怀相关接口
 * <AUTHOR>
 * @date 2025年02月11日 14:26
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MemberEventApi", fallback = MemberEventApiHystrix.class)
public interface MemberEventApi {

    final String URL = "/feign/marketing/memberEvent";
    @PostMapping(URL + "/schedulerPushEvent")
    public void schedulerPushEvent();

    /**
     * 根据参数查询赠送礼物信息
     * @param placeId 场所id
     * @param idNumber 证件id
     * @param pushId 推送记录id
     * @return
     */
    @GetMapping(URL + "/queryEvent")
    public GenericResponse<ObjDTO<MemberEventPushRecordBO>> queryEvent(@RequestParam String placeId,
                                                                       @RequestParam String pushId);

    /**
     * 领取礼物
     * @param requestTicket
     * @param placeId 场所id
     * @param idNumber 证件id
     * @param pushId 推送记录id
     * @return
     */
    @GetMapping(URL + "/receiveEvent")
    public GenericResponse<ObjDTO<MemberEventPushRecordBO>> receiveEvent(@RequestHeader(value = "request_ticket") String requestTicket,
                                                               @RequestParam String placeId,
                                                               @RequestParam String idNumber,
                                                               @RequestParam String pushId);
}
