package com.rzx.dim4.base.service.callback.user;

import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.user.WeChatUserApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年06月17日 11:07
 */

@Slf4j
@Service
public class WeChatUserApiHystrix implements WeChatUserApi {

    @Override
    public GenericResponse<SimpleDTO> createQrCode(String placeId,String idName, String idNumber) {
        log.error("接口异常:::createQrCode，placeId:{}，idNumber:{}，idName:{}", placeId,idNumber, idName);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
