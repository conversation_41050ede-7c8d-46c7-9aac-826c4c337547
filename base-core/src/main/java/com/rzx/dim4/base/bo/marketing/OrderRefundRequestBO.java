package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel(value = "退款订单信息")
public class OrderRefundRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "订单ID", required = true)
    private String orderId;

    @ApiModelProperty(value = "退款金额")
    private int refundAmount;

    @ApiModelProperty(value = "退款方式，0只退款，1退货退款  2取消订单 ,3 系统退单")
    private int refundType;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "退款商品列表")
    private List<OrderRefundGoodsRequestBO> orderRefundGoodsBos;
}
