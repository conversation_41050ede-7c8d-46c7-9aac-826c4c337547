package com.rzx.dim4.base.service.feign.goods;


import com.rzx.dim4.base.cons.FeginConstant;

import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;

/**
 * 仓库日志
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "LogGoodsStocks", fallback = ShopServerServiceHystrix.class)
public interface LogGoodsStocksClient {



}
