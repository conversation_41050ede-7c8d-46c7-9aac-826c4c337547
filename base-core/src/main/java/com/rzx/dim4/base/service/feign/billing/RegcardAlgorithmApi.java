package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.RegcardAlgorithmApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/2/26
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "regcardAlgorithmApi", fallback = RegcardAlgorithmApiHystrix.class)
public interface RegcardAlgorithmApi {

    String URL = "/feign/billing/regcard";

    /**
     * 关闭场所注册卡开关，并发送企业微信通知
     * @param requestTicket
     * @param placeConfigBO
     * @return
     */
    @PostMapping(URL + "/close")
    GenericResponse<SimpleDTO> closeRegcard(@RequestHeader(value = "request_ticket") String requestTicket,
                                            @RequestBody PlaceConfigBO placeConfigBO);
}
