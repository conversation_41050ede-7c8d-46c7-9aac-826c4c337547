package com.rzx.dim4.base.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;

import javax.imageio.ImageIO;

import org.springframework.util.StringUtils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

/**
 * 二维码生成工具类
 */
public class QrCodeUtils {

	/**
	 * 生成二维码
	 * 
	 * @param content 二维码内容
	 * @return 图片
	 * @throws Exception
	 */
	public static String createImage(String content, int width, int height) {
		String resultImage = null;
		if (!StringUtils.isEmpty(content)) {
			try {
				ByteArrayOutputStream os = new ByteArrayOutputStream();
				@SuppressWarnings("rawtypes")
				HashMap<EncodeHintType, Comparable> hints = new HashMap<>();
				hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
				hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M); // 指定二维码的纠错等级为中级
				hints.put(EncodeHintType.MARGIN, 0); // 设置图片的边距

				QRCodeWriter writer = new QRCodeWriter();
				BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, width, height, hints);
				BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
				ImageIO.write(bufferedImage, "png", os);
				resultImage = new String(Base64.getEncoder().encodeToString(os.toByteArray()));
				return resultImage;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return resultImage;
	}

	public static void main(String[] args) throws Exception {
		System.out.println(QrCodeUtils.createImage("123", 200, 200));
	}

}
