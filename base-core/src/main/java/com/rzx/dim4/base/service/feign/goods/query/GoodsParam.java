package com.rzx.dim4.base.service.feign.goods.query;

import lombok.Data;

import java.io.Serializable;

/**
 * 收银台库存
 */
@Data
public class GoodsParam implements Serializable {


    private Long id;

    /**
     * 场所ID
     */
    private String placeId;

    /**
     * 商品信息ID
     */
    private String goodsId;


    /**
     * // 收银台--商品库存数量
     */
    private int goodsStocksNumber;

    /**
     * // 仓库--商品变化
     */
    private int storageNumber;


    /**
     * 收银台原来库存数量
     */
    private int goodsNumber;

    private String supplierId;
    private String supplierName;

    /**
     * 备注
     */
    private String remark;

    private String creator;
    private String cashierName;

}
