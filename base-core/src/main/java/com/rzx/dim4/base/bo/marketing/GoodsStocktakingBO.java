package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报损单
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
@ApiModel(description = "商品盘点实体")
public class GoodsStocktakingBO extends AbstractEntityBO
{

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "创建者ID")
    private Long creater;

    @ApiModelProperty(value = "账号创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "账号更新时间")
    private LocalDateTime updated;

    @ApiModelProperty(value = "删除状态")
    private int deleted;

    @ApiModelProperty(value = "场所ID",required = true)
    private String placeId;

    @ApiModelProperty(value = "盘点单号（系统生成，如202411201137458783）")
    private String stocktakingNum;

    @ApiModelProperty(value = "商品类型：0商品，1原料（默认商品）")
    private int type;

    @ApiModelProperty(value = "盘点仓库，货架ID，场所唯一，从100000开始递增，主仓库为000000",required = true)
    private String storageRackId;

    @ApiModelProperty(value = "盘点经手人")
    private String stocktakingHandler;

    @ApiModelProperty(value = "盘点复查人")
    private String stocktakingReviewer;

    @ApiModelProperty(value = "盘点的商品数量")
    private int goodsTotal;

    @ApiModelProperty(value = "报损数")
    private int lossNum;

    @ApiModelProperty(value = "报损金额（单位分）")
    private int lossMoney;

    @ApiModelProperty(value = "报益数")
    private int overflowNum;

    @ApiModelProperty(value = "报益金额（单位分）")
    private int overflowMoney;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盈亏数：0平，1差（默认否）")
    private int profitLossFigure;

    @ApiModelProperty(value = "是否初始化库存，初始化库存将清空库存，0不初始化，1初始化")
    private int isInit;

    @ApiModelProperty(value = "盘点商品列表",required = true)
    private List<GoodsStocktakingListBO> goodsStocktakingListBOS;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "商品种类，0固装商品，1虚拟商品，2自制商品（默认0），3优惠券商品")
    private int goodsCategory;


    @ApiModelProperty(value = "盘点经手人")
    private String stocktakingHandlerName;

    @ApiModelProperty(value = "盘点复查人")
    private String stocktakingReviewerName;
}
