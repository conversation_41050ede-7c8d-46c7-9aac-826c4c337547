package com.rzx.dim4.base.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 关于钱计算
 */

public class MoneyCountUtil {
    /**
     * 计算钱 数据库存入分 返回客户端展示元
     * @param price
     * @return
     */
    public static float price(int price){
        if (price == 0){
            return price;
        }
        BigDecimal bigDecimal = BigDecimal.valueOf(price);
        return bigDecimal.divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 计算钱 数据库存入分 返回客户端展示元
     * @param price
     * @return
     */
    public static float price(float price){
        if (price == 0){
            return 0L;
        }
        BigDecimal bigDecimal = BigDecimal.valueOf(price);
        return bigDecimal.divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 计算钱 数据库存入分 返回客户端展示元
     * @param price
     * @return
     */
    public static float price(BigDecimal price){
        if (price == null) {
            return 0L;
        }

        return price.divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).floatValue();
    }

}
