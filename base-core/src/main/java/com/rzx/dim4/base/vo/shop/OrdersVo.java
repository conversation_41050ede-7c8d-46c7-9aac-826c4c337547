package com.rzx.dim4.base.vo.shop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.shop.OrderGoodsBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class OrdersVo extends AbstractEntityBO implements Serializable {
    private static final long serialVersionUID = 1261018971627052497L;

    private Long creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime created; // 创建时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updated; // 账号更新时间

    private int deleted;

    private String placeId; // 场所ID
    private String shiftId; // 班次名称
    private String cashierId; // 收银员ID
    private String cashierName; // 收银员名称
    private String orderId; // 订单ID
    private String ldOrderId;
    private String cardId; // 计费卡ID
    private String idNumber; // 身份证号码
    private String idName; // 身份证姓名
    private String clientId; // 客户端ID
    private String clientName; // 客户端名称
    private int orderAmount; // 订单价格
    private PayType payType; // 支付方式
    private String payCode; // 付款码
    private SourceType sourceType; // 来源

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    protected LocalDateTime finishedTime; // 订单完成时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    protected LocalDateTime refundTime; // 订单完成时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    protected LocalDateTime payTime; // 订单支付时间

    protected int status; // 订单状态，0已创建，1已支付，2已完成，3部分退款，4已退款 ,5 已取消订单（查询退款单的，本身不存）
    private String remark; // 订单备注
    List<OrderGoodsBO> orderGoodsList;
}
