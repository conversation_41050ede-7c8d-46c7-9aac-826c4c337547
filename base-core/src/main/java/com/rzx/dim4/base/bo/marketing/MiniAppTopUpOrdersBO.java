package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单信息表
 *
 * <AUTHOR>
 * @date 2025年01月14日 14:16
 */
@Getter
@Setter
public class MiniAppTopUpOrdersBO extends AbstractEntityBO {

    // 场所ID
    private String placeId;
    // 计费卡ID
    private String cardId;
    // 来源
    private SourceType sourceType;
    // 支付方式
    private PayType payType;
    private String payCode; // 付款码
    private String idNumber; // 身份证号码
    private String idName; // 身份证姓名
    private Long creater;
    private String createrName; // 操作人姓名

    private String returnUrl;
    private String openId;
    private String cardTypeId;
    private String cardTypeName;
    private int totalMoney; // 订单合计价格
    private int realMoney; // 订单实际金额

    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private String goodsTypeId; // 商品类型ID
    private String goodsTypeName; // 商品类型名称
    private int quantity; // 商品数量
    private int unitPrice; // 商品单价
    private int discounts; // 折扣金额

    private String ldOrderId; // ldopay返回的订单号
    private String remark; // 订单备注

    private String specs; // 订单提交时的口味，单选

    private int isMeals; //是否套餐 0否 1是
    private String mealsId; // 套餐id
    private int goodsQuota; // 虚拟商品实际充值了多少到钱包(充值本金)
    private int goodsPresentAmount; // 虚拟商品实际充值了多少到网费（充值奖励）
    private String internetFeeId; // 网费虚拟商品绑定的网费充送活动ID
}