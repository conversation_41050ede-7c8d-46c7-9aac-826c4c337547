/*
 * @(#)PackageTimeRuleApi.java 1.00 2024-1-24
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.third.PackageRuleBO;
import com.rzx.dim4.base.bo.billing.third.PackageRuleBriefBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeePackageSearchBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeePackageTopUpBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PackageTimeRuleApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "packageTimeRuleApi", fallback = PackageTimeRuleApiHystrix.class)
public interface PackageTimeRuleApi {
    String URL = "/feign/billing/packagetime/rule";

    @PostMapping(URL + "/queryUserPackageRuleForMiniApp")
    GenericResponse<ListDTO<PackageRuleBriefBO>> queryUserPackageRuleForMiniApp(@SpringQueryMap InternetFeePackageSearchBO paramsBo);

    @PostMapping(URL + "/queryUserPackageRule")
    GenericResponse<ListDTO<PackageRuleBO>> queryUserPackageRule(@RequestParam String placeId, @RequestParam String idNumber);


    @PostMapping(URL + "/packageTime")
    GenericResponse<SimpleDTO> packageTime(@RequestParam String placeId, @RequestParam String ruleId, @RequestParam String idNumber);


    @PostMapping(URL + "/createPackageTimeOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> createPackageTimeOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                    @RequestParam String placeId,
                                                                    @RequestParam String ruleId,
                                                                    @RequestParam String idNumber,
                                                                    @RequestParam String openId,
                                                                    @RequestParam SourceType sourceType,
                                                                    @RequestParam String returnUrl);

    @PostMapping(URL + "/createPackageTimeOrderForMiniApp")
    GenericResponse<ObjDTO<PaymentResultBO>> createPackageTimeOrderForMiniApp(@RequestBody InternetFeePackageTopUpBO paramsBo);

}
