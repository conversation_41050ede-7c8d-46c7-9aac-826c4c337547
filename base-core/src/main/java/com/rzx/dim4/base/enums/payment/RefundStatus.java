package com.rzx.dim4.base.enums.payment;

/**
 * 支付类型
 * 
 * <AUTHOR>
 * @date Jul 28, 2020 4:50:38 PM
 */
/**
 * 0.订单初始化，未发送到支付渠道 <br/>
 * 1.支付渠道返回订单创建成功，等待用户支付 <br/>
 * 2.支付成功 <br/>
 * 3.支付失败 <br/>
 * 4.部分退款 <br/>
 * 5.全额退款 <br/>
 * 9.创建订单失败 <br/>
 */
public enum RefundStatus {

	SUCCESS(1), // 1.退款成功
	NOREFUND(2), // 2.未退款
	PROCESSING(3), // 3.处理中
	FAILED(4), // 4.退款失败
	;

	private final int value;

	private RefundStatus(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static RefundStatus getPayType(int code) {
		for (RefundStatus orderStatus : RefundStatus.values()) {
			if (code == orderStatus.getValue()) {
				return orderStatus;
			}
		}
		return null;
	}

}
