package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.billing.BillingOnlineBO;
import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年08月12日 15:19
 */
@Getter
@Setter
@ToString
public class DiscountCouponBO  extends AbstractEntityBO {

    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String couponId; // 优惠券ID

    private String couponName; // 优惠券名称

    /**
     * 优惠券类型id，每个优惠券都有不同的处理逻辑
     * 1.网费赠送券
     *      该券使用后直接根据当前场所和账户充值‘发放面额’数值的金额
     * 2.时长券
     *      该券创建时需要建立对应的包时规则，根据ruleId 进行绑定，使用时直接根据ruleId 进行包时
     */
    private int couponTypeId; // 优惠券类型ID

    @ApiModelProperty(value = "优惠券类型名称")
    private String couponTypeName; // 优惠券类型名称

    private String ruleId; // 规则ID

    @ApiModelProperty(value = "优惠券发放面额")
    private int amount; // 发放面额

    /**
     * 券失效时间类型
     * 1.当月有效，根据 startTime 、endTime 控制失效时间
     * 2.指定时间，根据 startTime 、endTime 控制失效时间
     * 3.指定天数，根据 startDay 计算开始时间，根据 validDays 时间失效时间
     */
    @ApiModelProperty(value = "优惠券失效时间类型：* 1.当月有效，根据 startTime 、endTime 控制失效时间\n" +
            "     * 2.指定时间，根据 startTime 、endTime 控制失效时间\n" +
            "     * 3.指定天数，根据 startDay 计算开始时间，根据 validDays 时间失效时间")
    private int failureTimeType; // 券失效时间类型

    @ApiModelProperty(value = "优惠券开始时间")
    private LocalDateTime startTime; // 开始时间

    @ApiModelProperty(value = "优惠券结束时间")
    private LocalDateTime endTime; // 结束时间

    @ApiModelProperty(value = "优惠券领券后x天生效")
    private int startDay; // 领券后x天生效

    @ApiModelProperty(value = "优惠券有效天数为x")
    private int validDays; // 有效天数为x

    @ApiModelProperty(value = "优惠券生效周工作日（周一至周日，1代表星期一），用逗号分隔，如 1, 2, 3")
    private String effectiveWeekDays = ""; // 生效周工作日（周一至周日，1代表星期一），用逗号分隔，如 1, 2, 3

    @ApiModelProperty(value = "优惠券使用介绍")
    private String remark; // 使用介绍

    @ApiModelProperty(value = "优惠券状态，0启用、1禁用")
    private int status;   //状态，0启用、1禁用

    @ApiModelProperty(value = "优惠券使用限制类型 0每次使用限制数，1每日使用限制数，2每周使用限制数，3每月使用限制数")
    private int limitType; // 使用限制类型 0每次使用限制数，1每日使用限制数，2每周使用限制数，3每月使用限制数

    @ApiModelProperty(value = "优惠券使用限制数量")
    private int limitNum; // 使用限制数量

    @ApiModelProperty(value = "图片地址，使用 ,分隔")
    private String urls; // 图片地址，使用 ,分隔

    //-----包时相关字段 新增、查询、修改时使用-↓↓↓↓↓
    @ApiModelProperty(value = "包时-id")
    private Long packageId; //id
    @ApiModelProperty(value = "包时标识 1:包时段 2:包时长")
    private int packageFlag; // 包时标识 1:包时段 2:包时长

    @ApiModelProperty(value = "包时-开始时刻")
    private float packageStartTime; // 开始时刻

    @ApiModelProperty(value = "包时-结束时刻")
    private float packageEndTime; // 结束时刻

    @ApiModelProperty(value = "包时-时长，当packageFlag==1时值为0，当packageFlag==2为计算值")
    private int durationTime; // 时长，当packageFlag==1时值为0，当packageFlag==2为计算值

    @ApiModelProperty(value = "包时长限制字段，0-不限制，1-限制结束时间")
    private int limitEndTime; // 包时长限制字段，0-不限制，1-限制结束时间

    @ApiModelProperty(value = "包时-区域ID")
    private String areaIds; // 区域ID

    @ApiModelProperty(value = "包时-卡类型ID")
    private String cardTypeIds; // 卡类型ID

    @ApiModelProperty(value = "包时段为null，或00:00:00;包时长强制结束时间,HH:MM:SS,如购买时间大于结束时间则次日生效。留空或00:00:00则不生效")
    private String limitDurationEndTime; // 包时段为null，或00:00:00;包时长强制结束时间,HH:MM:SS,如购买时间大于结束时间则次日生效。留空或00:00:00则不生效

    //-----页面展示需要的字段
    @ApiModelProperty(value = "卡类型名称")
    private String cardTypeName;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "来源")
    private SourceType sourceType;//来源

    @ApiModelProperty(value = "重复购买叠加时长开关 0:关闭  1:开启；")
    private int durationRepeatedTime; //重复购买叠加时长开关 0:关闭  1:开启；



}
