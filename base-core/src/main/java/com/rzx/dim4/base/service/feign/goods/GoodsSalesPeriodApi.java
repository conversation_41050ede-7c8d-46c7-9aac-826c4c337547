package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.GoodsSalesPeriodBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.shop.GoodsSalesPeriodApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024年07月31日 18:04
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "goodsSalesPeriodApi", fallback = GoodsSalesPeriodApiHystrix.class)
public interface GoodsSalesPeriodApi {

    String URL = "/feign/shop/goodsSalesPeriod";

    /**
     * 新增或修改
     * @return
     */
    @PostMapping(URL + "/saveOrUpdate")
    GenericResponse<?> saveOrUpdate(@RequestHeader(value = "request_ticket") String requestTicket , @RequestBody GoodsSalesPeriodBO goodsSalesPeriodBO);

    /**
     * 新增或修改
     * @return
     */
    @GetMapping(URL + "/findAllByPlaceId")
    GenericResponse<?> findAllByPlaceId(@RequestParam String placeId);

}
