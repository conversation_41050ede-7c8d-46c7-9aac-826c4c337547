/*
 * @(#)BillingTopupRuleApi.java 1.00 2024-1-26
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.rzx.dim4.base.bo.billing.third.TopupRuleListBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingTopupRuleApiHystrix;

/**
 * <p>Title:</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-1-26
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "billingTopupRuleApi", fallback = BillingTopupRuleApiHystrix.class)
public interface BillingTopupRuleApi {
    String URL = "/feign/billing/topup/rule";

    @PostMapping(URL + "/queryTopupRules4Iot")
	GenericResponse<ListDTO<TopupRuleListBO>> queryTopupRules4Iot(@RequestParam String placeId, @RequestParam String idNumber);

    /**
     * 优惠券充值会员卡奖励
     * @param placeId 场所id
     * @param idNumber  证件号码
     * @param cashAmount 充值金额
     * @param present 奖励金额
     * @param sourceType 操作来源
     * @param shiftId 班次id
     * @param couponId 优惠券id
     * @param couponName 优惠券名称
     * @return
     */
    @PostMapping(URL + "/couponTopup")
    GenericResponse<?> couponTopup(@RequestParam String placeId, @RequestParam String idNumber, @RequestParam int cashAmount,
                                   @RequestParam int present, @RequestParam SourceType sourceType, @RequestParam String shiftId, @RequestParam String couponName);

}
