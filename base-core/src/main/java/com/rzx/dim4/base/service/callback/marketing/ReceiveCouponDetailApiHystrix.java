package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.BuyGiftsBO;
import com.rzx.dim4.base.bo.marketing.ReceiveCouponDetailBO;
import com.rzx.dim4.base.dto.NumberDto;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.ReceiveCouponDetailApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年08月14日 10:27
 */
@Slf4j
@Component
public class ReceiveCouponDetailApiHystrix implements ReceiveCouponDetailApi {

    @Override
    public GenericResponse<NumberDto> findCount(Map<String, String> paramMap) {
        log.error("接口异常 ReceiveCouponDetailApiHystrix.findCount(Params={})", new Gson().toJson(paramMap));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ReceiveCouponDetailBO>> findPage(Map<String, String> param, String placeId, int size, int page) {

        log.error("接口异常，ReceiveCouponDetailApiHystrix.findPage(param={},placeId={})", param,placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> deleteDetailss(String requestTicket, String placeId,String accountId,String accountName, List<Long> ids) {

        log.error("接口异常，ReceiveCouponDetailApiHystrix.deleteDetailss(placeId={},accountId={},accountName={},ids={})", placeId,accountId,accountName,ids);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public void schedulerCheckValid() {
        log.error("接口异常，schedulerCheckValid");
    }

    @Override
    public GenericResponse<?> useTicketCodeVerify( String requestTicket,String encryptedCode, String placeId, String idName, String idNumber, SourceType operatorSourceType) {
        log.error("接口异常 useTicketCodeVerify(encryptedCode={},placeId={},idName={},idNumber={},operatorSourceType={})", encryptedCode,placeId,idName,idNumber,operatorSourceType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleObjDTO> findShiftStatistics(String placeId, String shiftId) {
        log.error("接口异常，ReceiveCouponDetailApiHystrix.findShiftStatistics(placeId={},shiftId={})", placeId,shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BuyGiftsBO>> certificatePrepare(String requestTicket, String ticketCode, String idNumber, String placeId) {
        log.error("接口异常，ReceiveCouponDetailApiHystrix.certificatePrepare(ticketCode={},idNumber={},placeId={})", ticketCode,idNumber,placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> certificateVerify(String requestTicket,Map<String,String> params) {
        log.error("接口异常，ReceiveCouponDetailApiHystrix.certificateVerify(params={})", params);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public int findUserCouponCount(String placeId, String idNumber) {
        log.error("接口异常，ReceiveCouponDetailApiHystrix.certificateVerify(placeId={},idNumber={})", placeId,idNumber);
        return 0;
    }
}
