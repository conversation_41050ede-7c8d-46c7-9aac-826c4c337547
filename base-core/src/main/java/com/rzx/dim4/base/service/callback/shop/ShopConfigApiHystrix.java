package com.rzx.dim4.base.service.callback.shop;

import com.rzx.dim4.base.bo.shop.ShopConfigBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.goods.ShopConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/6/11
 **/
@Slf4j
@Service
public class ShopConfigApiHystrix implements ShopConfigApi {
    /**
     * 查询商超配置列表，不分页
     *
     * @return
     */
    @Override
    public GenericResponse<ListDTO<ShopConfigBO>> openingList(String requestTicket) {
        log.error("接口异常:::openingList()");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
