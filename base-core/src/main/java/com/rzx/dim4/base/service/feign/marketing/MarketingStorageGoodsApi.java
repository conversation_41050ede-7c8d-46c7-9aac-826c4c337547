package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.StorageGoodsBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingStorageGoodsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> hwx
 * @since 2025/2/27 14:39
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingStorageGoodsApi", fallback = MarketingStorageGoodsApiHystrix.class)
public interface MarketingStorageGoodsApi {

    /**
     * 单仓库时获取仓库数据，多仓库时获取货架数据
     * @param placeId
     * @return
     */
    @GetMapping("/feign/marketing/storageGoods/findStorageRackByPlaceId")
    GenericResponse<ListDTO<StorageGoodsBO>> findStorageRackByPlaceId(@RequestParam(name = "placeId") String placeId);

}
