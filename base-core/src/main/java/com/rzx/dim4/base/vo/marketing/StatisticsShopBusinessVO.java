package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * 商超经营概况
 */
@Getter
@Setter
public class StatisticsShopBusinessVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 1014079134477189115L;
    /**
     * 扫码点购商品的金额
     */
    private int scanCodeGoodsAmount;
    /**
     * 本金余额购买商品的金额
      */
    private int principalAmount;
    /**
     * 奖励余额购买商品的金额
     */
    private int rewardAmount;
    /**
     * 美团支付商品的金额
     */
    private int meituanAmount;

    /**
     * 抖音支付商品的金额
     */
    private int douyinAmount;

    /**
     * 自定义收款
     */
    private int customizedAmount;


}
