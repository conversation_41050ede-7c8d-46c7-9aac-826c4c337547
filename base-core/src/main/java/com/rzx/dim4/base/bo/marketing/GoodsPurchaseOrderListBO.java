package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class GoodsPurchaseOrderListBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String purchaseOrderNum; // 采购单编号
    private String goodsId; // 商品id
    private String goodsName; // 商品名称
    private String goodsPic; // 商品图片

    @Min(value = 0,message = "参数错误")
    private int purchaseNumber; // 采购商品数量

    @Min(value = 0,message = "采购单价必须是正整数")
    private int purchasePrice; // 采购单价（单位分）



}
