package com.rzx.dim4.base.vo;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
public class PlaceProfileVO extends AbstractEntityBO implements Serializable {

    private static final long serialVersionUID = -4788799109440504875L;

    private String placeId;
    private String displayName;
    private int type;
    /**
     * 收银台投放版本
     */
    private String cashierVersion;
    /**
     * 客户端投放版本
     */
    private String clientVersion;
    private int registeredClientNum;
    private int clientNum;
    private LocalDateTime created;
    private LocalDateTime newestHbTime;
    private LocalDateTime expired;

    private String address;
    private String frequentContactor;
    private String frequentContactorMobile;
    private LocalDateTime cloudVersionExpired; // 云版本到期时间
    private String identifier; // 场所识别码
    private String regionCode; // 所在地区编码
    private int billingType; // 计费类型，0不计费，1计费
    private String merUsername; // 龙兜账号
    private double distance; // 距离
    private String loginId;
    private String cardId;
    private String clientId;
    private int totalAccount; // 总账户余额
    private int isChain; // 是否连锁
    private String thirdAccountId; // 绑定的第三方账号id
    private String thirdAccountName; // 绑定的第三方账号名称
    private int versionFlag; // 版本标志 0基础本；1标准版；2旗舰版

    private LocalDateTime firstUseV8Time; // 第一次使用 v8（当前系统）的时间；
    private LocalDateTime lastLoginTime; // 场所最后一次登录时间
    //前端需要字段
    private String accountId; // 账号ID
    private String chainId; // 连锁ID
    private String accountName; // 姓名
    private int accountType; // 账号类型: 0代理，1店长，2收银, 3大区账号，4连锁账号，5连锁子账号
    private String accountTypeName; // 账号类型: 0代理，1店长，2收银, 3大区账号，4连锁账号，5连锁子账号
    private String chainName; // 连锁名称

    public void setAccountType(int accountType){
        this.accountType = accountType;
        switch (accountType){
            case 0:
                this.accountName = "代理";
                break;
            case 1:
                this.accountName = "店长";
                break;
            case 2:
                this.accountName = "收银";
                break;
            case 3:
                this.accountName = "大区账号";
                break;
            case 4:
                this.accountName = "连锁账号";
                break;
            case 5:
                this.accountName = "连锁子账号";
                break;
            default:
                this.accountName = "未知账户";
                break;
        }
    }
}
