package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR> hwx
 * @Description TODO
 * @Date 2025/2/5 14:08
 */
@Data
public class StatisticGoodsTypeABCExportVO {
    @ExcelProperty(value = "商品名称", index = 0)
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "销量", index = 1)
    @ColumnWidth(20)
    private int salesCount;

    @ExcelProperty(value = "毛利", index = 2)
    @ColumnWidth(20)
    private float grossProfit;

    @ExcelProperty(value = "归类", index = 3)
    @ColumnWidth(20)
    private String classification;
}
