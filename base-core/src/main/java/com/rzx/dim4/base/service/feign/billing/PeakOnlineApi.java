package com.rzx.dim4.base.service.feign.billing;


import com.rzx.dim4.base.bo.billing.PeakOnlineBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PeakOnlineApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 查询网费余额明细
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "peakOnlineApi", fallback = PeakOnlineApiHystrix.class)
public interface PeakOnlineApi {


    String URL = "/feign/billing/peakOnline";

    @GetMapping(URL + "/queryByPlaceIdAndCountDay")
    GenericResponse<ObjDTO<PeakOnlineBO>> queryByPlaceIdAndCountDay( @RequestParam(name = "placeId") String placeId, @RequestParam(name = "countDay") String countDay );

}
