package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class GoodsReceiptBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String goodsReceiptNum; // 入库单编号（系统生成，如202411201137458783）
    private String storageRackId; // 入库仓库(货架管理的记录的storage_rack_id)默认主仓库(00000000)
    private String supplierId; // 供应商id

    @NotBlank(message = "供应商名称不能为空!")
    @Length(message = "供应商名称不能超过个 {max} 字符！", max = 50)
    private String supplierName; // 供应商名称

    private int type; // 商品类型：0商品，1原料（默认商品）
    private int goodsKind; // 商品品种

    @NotNull(message = "支付方式不能为空!")
    private int payType; // 支付方式：0现金，1支付宝，2微信，3挂账，4上打下，5其他

    private int goodsTotal; // 数量:每种品种的商品数量之和

    @Min(value = 0,message = "应付金额错误!")
    private int money; // 应付（单位分）

    @Min(value = 0,message = "实付金额错误!")
    private int paidMoney; // 实付（单位分）

    private int status; // 状态：0正常，1作废，2退货（默认正常）

    @Length(message = "备注不能超过个 {max} 字符！", max = 50)
    private String remark; // 备注

    private String createrName; // 创建姓名

    private List<GoodsReceiptListBO> goodsReceiptListBOS; // 入库单商品列表




}
