package com.rzx.dim4.base.enums.user;

import lombok.Getter;
import lombok.Setter;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * 公众号类型
 */
@Getter
public enum MpType {
    /**
     * 四维管家
     */
    DIM4(1),

    /**
     * 龙管家V8
     */
    V8(2),

    /**
     * V5电竞
     */
    V5(3);

    private final int code;

    MpType(int code) {
        this.code = code;
    }

    public static Optional<MpType> getByCode(int code){
        for(MpType mpType : MpType.values()){
            if(mpType.getCode() == code){
                return Optional.of(mpType);
            }
        }

        return Optional.empty();
    }

    public static Optional<MpType> getByCode(String code){
        if(StringUtils.isBlank(code)){
            return Optional.empty();
        }
        int codeInt = Integer.parseInt(code);
        for(MpType mpType : MpType.values()){
            if(mpType.getCode() == codeInt){
                return Optional.of(mpType);
            }
        }

        return Optional.empty();
    }

    public String getCodeStr(){
        return String.valueOf(this.code);
    }

}
