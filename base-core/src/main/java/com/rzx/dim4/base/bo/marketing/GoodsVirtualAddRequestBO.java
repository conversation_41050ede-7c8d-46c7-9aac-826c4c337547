package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.util.List;

/**
 * 新增虚拟商品信息实体
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel(description = "新增虚拟商品信息实体")
public class GoodsVirtualAddRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "创建者ID", hidden = true)
    private Long accountId;

    @ApiModelProperty(value = "创建者", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    @Length(message = "商品名称不能超过50字符!", max = 50)
    private String goodsName;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "区域ID")
    @Length(message = "区域类型不能超过200字符!", max = 200)
    private String areaIds;

    @ApiModelProperty(value = "虚拟商品内部编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品图片Md5")
    private String goodsPicMd5;

    @ApiModelProperty(value = "商品销售价，单位分")
    @Min(value = 0, message = "商品销售价参数错误")
    private int unitPrice;

    @ApiModelProperty(value = "商品成本价，单位分")
    @Min(value = 0, message = "商品成本价参数错误")
    private int costPrice;

    @ApiModelProperty(value = "商品网费价，单位分")
    @Min(value = 0, message = "商品网费价参数错误")
    private int networkPrice;

    @ApiModelProperty(value = "商品额度")
    private int goodsQuota;

    @ApiModelProperty(value = "支持奖励余额购买，0是，1否，默认1")
    @Max(value = 1, message = "是否支持奖励余额购买参数错误")
    @Min(value = 0, message = "是否支持奖励余额购买参数错误")
    private int supportPresentSwitch;

    @ApiModelProperty(value = "支持本金购买，0是，1否，默认0")
    @Max(value = 1, message = "是否支持本金购买参数错误")
    @Min(value = 0, message = "是否支持本金购买参数错误")
    private int supportCashSwitch;

    @ApiModelProperty(value = "单位")
    private int unit;

    @ApiModelProperty(value = "口味，多个标签用,隔开")
    @Length(message = "口味字段过长！", max = 50)
    private String specs;

    @ApiModelProperty(value = "副标题")
    @Length(message = "副标题不能超过50字符！", max = 50)
    private String subheading;

    @ApiModelProperty(value = "虚拟商品类型（0网费，1现金，2积分，3幸运抽奖，4台桌门票，5集卡活动，6砸金蛋次数，7员工投票，8网费押金，9其他）")
    @Max(value = 9, message = "虚拟商品类型参数错误")
    @Min(value = 0, message = "虚拟商品类型参数错误")
    private int virtualGoodsType;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "卡类型ID")
    @NotEmpty(message = "卡类型不能为空")
    @Length(message = "卡类型不能超过90字符！", max = 90)
    private String cardTypeIds;

    @ApiModelProperty(value = "卡类型名称")
    private String cardTypeNames;

    @ApiModelProperty(value = "销售状态，0售卖中，1停售")
    @Max(value = 1, message = "销售状态参数错误")
    @Min(value = 0, message = "销售状态参数错误")
    private int sellStatus;

    @ApiModelProperty(value = "标签ID，用,分割")
    @Length(message = "标签类型过多", max = 50)
    private String tagIds;

    @ApiModelProperty(value = "标签名称，用,分割")
    private String tagNames;

    @ApiModelProperty(value = "系统标签名称，用,分割")
    private String sysTagNames;

    @ApiModelProperty(value = "售卖周期类型，0每日、1每周、2每月、3指定时间段")
    @Max(value = 3, message = "售卖周期类型参数错误")
    @Min(value = 0, message = "售卖周期类型参数错误")
    private int cycle;

    @ApiModelProperty(value = "每日开始售卖时间点1，默认0")
    @Max(value = 24, message = "每日开始售卖时间点1参数错误")
    @Min(value = 0, message = "每日开始售卖时间点1参数错误")
    private float startTime1;

    @ApiModelProperty(value = "每日结束售卖时间段1，默认24")
    @Max(value = 24, message = "每日结束售卖时间段1参数错误")
    @Min(value = 0, message = "每日结束售卖时间段1参数错误")
    private float endTime1;

    @ApiModelProperty(value = "每日开始售卖时间点2")
    private float startTime2;

    @ApiModelProperty(value = "每日结束售卖时间段2")
    private float endTime2;

    @ApiModelProperty(value = "每日开始售卖时间点3")
    private float startTime3;

    @ApiModelProperty(value = "每日结束售卖时间段3")
    private float endTime3;

    @ApiModelProperty(value = "每周售卖日期，默认 1,2,3,4,5,6,7")
    private String weeks;

    @ApiModelProperty(value = "每月售卖日期，默认 0,1,2,3,4,...31")
    private String days;

    @ApiModelProperty(value = "商品限购类型，0每日、1每周、2每月、3永久")
    @Max(value = 3, message = "商品限购类型参数错误")
    @Min(value = 0, message = "商品限购类型参数错误")
    private int extType;

    @ApiModelProperty(value = "商品限购数量，默认0不限制")
    @Min(value = 0, message = "商品限购数量参数错误")
    private int extCount;

    @ApiModelProperty(value = "销量")
    private int initSaleNum;

    @ApiModelProperty(value = "商品排序")
    private int sort;

    @ApiModelProperty(value = "是否计算库存，0-是，1-否")
    @Max(value = 1, message = "是否计算库存参数错误")
    @Min(value = 0, message = "是否计算库存参数错误")
    private int isCalculateInventory;

    @ApiModelProperty(value = "预警值")
    @Min(value = 0, message = "预警值参数错误")
    private int stockAlarm;

    @ApiModelProperty(value = "是否客户端展示，0展示，1不展示")
    @Max(value = 1, message = "是否计算库存参数错误")
    @Min(value = 0, message = "是否计算库存参数错误")
    private int showClientSwitch;

    @ApiModelProperty(value = "是否收银台展示，0展示，1不展示")
    @Max(value = 1, message = "是否收银台展示参数错误")
    @Min(value = 0, message = "是否收银台展示参数错误")
    private int showCashierSwitch;

    @ApiModelProperty(value = "是否移动端展示，0展示，1不展示")
    @Max(value = 1, message = "是否移动端展示参数错误")
    @Min(value = 0, message = "是否移动端展示参数错误")
    private int showMobileSwitch;

    @ApiModelProperty(value = "是否允许在线支付，0是，1否")
    @Max(value = 1, message = "是否允许在线支付参数错误")
    @Min(value = 0, message = "是否允许在线支付参数错误")
    private int onlinePaySwitch;

    @ApiModelProperty(value = "是否参加排行榜，0是，1否")
    @Max(value = 1, message = "是否参加排行榜参数错误")
    @Min(value = 0, message = "是否参加排行榜参数错误")
    private int showRank;

    @ApiModelProperty(value = "业务商品ID列表")
    private List<String> goodsIds;

    @ApiModelProperty(value = "开始日期（指定时间段）")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（指定时间段）")
    private LocalDate endDate;

    @ApiModelProperty(value = "库存数")
    private int goodsStocksNum;

    @ApiModelProperty(value = "售卖时间状态")
    private boolean cycleStatus;

    @ApiModelProperty(value = "优惠券ID")
    private String couponId;

    @ApiModelProperty(value = "主仓库库存")
    private int mainGoodsStocksNum;

    @ApiModelProperty(value = "折扣模式：0指定折扣值，1折后金额")
    private int discountMode;

    @ApiModelProperty(value = "折扣率（折扣模式为0时有效）")
    private double discountRate;

    @ApiModelProperty(value = "折后金额（折扣模式为1时有效）")
    private int discountedAmount;

    @ApiModelProperty(value = "买赠，购买多少件商品才能送")
    private int needBuyNum;

    @ApiModelProperty(value = "赠送商品ID列表")
    private List<String> buyGiftsGoodIds;

    @ApiModelProperty(value = "赠送商品对象列表")
    private List<BuyGiftsGoodsBO> buyGiftsGoodsBOS;

    @ApiModelProperty(value = "赠送数量")
    private int goodsNum;

    @ApiModelProperty(value = "买赠价格")
    private int buyGiftPrice;

    @ApiModelProperty(value = "是否赠送，0否，1是")
    private int present;

    @ApiModelProperty(value = "赠送网费")
    private int presentAmount;

    @ApiModelProperty(value = "会员卡等级调整至")
    private String cardLevelTo;

    @ApiModelProperty(value = "会员卡等级名称")
    private String cardLevelToName;

    @ApiModelProperty(value = "网费充送ID")
    private String internetFeeId;
}