package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@ApiModel(description = "商品上下架记录实体")
public class GoodsMoveRecordRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "货架ID: 场所唯一，从100000开始递增，主仓库为000000", required = true)
    private String storageRackId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "上架商品列表")
    private List<GoodsInventoryChangeRecordRequestBO> goodsRecords;
}
