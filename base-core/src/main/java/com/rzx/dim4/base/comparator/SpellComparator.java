package com.rzx.dim4.base.comparator;

import java.util.Comparator;

/**
 * 比较器
 * 
 * <AUTHOR>
 * @date Apr 10, 2020 5:03:00 PM
 */
public class SpellComparator implements Comparator<Object> {
	public int compare(Object o1, Object o2) {
		try {
			String s1 = new String(o1.toString().getBytes("UTF-8"), "ISO-8859-1");
			String s2 = new String(o2.toString().getBytes("UTF-8"), "ISO-8859-1");
			return s1.compareTo(s2);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}
}