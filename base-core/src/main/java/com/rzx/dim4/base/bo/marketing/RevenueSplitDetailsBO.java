package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 订单信息表
 * <AUTHOR>
 * @date 2025年01月14日 14:16
 */
@Getter
@Setter
public class RevenueSplitDetailsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID

    private String placeName; // 场所名称

    private int realIncome; // 实收入 = 网费收入 + 商品收入

    private int revenueSplit; // 分账金额 = 实收入-手续费

    private int goodsIncome; // 商品收入

    private int internetFeeIncome; // 网费收入

    private LocalDateTime startTime; // 分账开始时段

    private LocalDateTime endTime; // 分账结束时段

    private int realMoney; // 订单实际金额（优惠后-实际付款金额）

    private int fee; // 手续费

    private String remark; //

    private int status;// 分账状态，0未打款，1打款成功，2打款失败

}
