package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品标签表
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@ApiModel("商品标签表")
public class GoodsTagsResponseBO extends AbstractEntityBO {

    @ApiModelProperty("场所ID")
    private String placeId;

    @ApiModelProperty("标签ID")
    private String tagId;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("是否展示: 0展示，1不展示")
    private int showSwitch;
}
