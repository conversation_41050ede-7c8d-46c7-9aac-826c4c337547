package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.ReportLossBO;
import com.rzx.dim4.base.bo.marketing.ReportLossRecordRequestBo;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingReportLossApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingReportLossApiHystrix implements MarketingReportLossApi {

    @Override
    public GenericResponse<PagerDTO<ReportLossBO>> findPageList(ReportLossRecordRequestBo paramsBo) {
        log.error("接口异常:::MarketingReportLossApiHystrix.findPageList(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ReportLossBO>> saveReportLoss(ReportLossBO paramsBo) {
        log.error("接口异常:::MarketingReportLossApiHystrix.saveReportLoss(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ReportLossBO>> findReportLossDetail(String placeId, String reportLossNo) {
        log.error("接口异常:::MarketingReportLossApiHystrix.findReportLossDetail(placeId={}, reportLossNo={})", placeId, reportLossNo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}