package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class PlaceShiftBusinessReportVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -3872569349702982339L;
    private String date;     // 格式"YYYY-MM-DD"
    private int onlineAmount;
    private int thirdAmount;
    private int totalAmount;  // 替换int类型
}
