package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsReceiptApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingGoodsReceiptApiHystrix implements MarketingGoodsReceiptApi {

    @Override
    public GenericResponse<PagerDTO<GoodsReceiptResponseBO>> findPageList(GoodsReceiptRecordRequestBo paramsBo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.findPageList(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveGoodsReceipt(GoodsReceiptSaveRequestBO paramsBo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.saveGoodsReceipt(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsReceiptResponseBO>> findGoodsReceiptDetail(String placeId, String goodsReceiptNo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.findGoodsReceiptDetail(placeId={}, goodsReceiptNo={})", placeId, goodsReceiptNo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StorageGoodsResponseBO>> findGoodsInventoryPage(GoodsInventoryQueryRequestBO paramsBo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.findGoodsInventoryPage(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StorageGoodsStatisticsResponseBO>> statisticsGoodsStorage(GoodsInventoryStatisticsQueryRequestBO paramsBo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.statisticsGoodsStorage(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsInventoryChangeRecordResponseBO>> queryGoodsInventoryRecordDetail(GoodsInventoryRecordQueryRequestBO paramsBo) {
        log.error("接口异常:::MarketingGoodsReceiptApiHystrix.queryGoodsInventoryRecordDetail(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}