package com.rzx.dim4.base.enums.marketing;

import lombok.Getter;

/**
 * 商品类型
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Getter
public enum GoodsCategoryType {
    REAL(0, "固装商品"),
    VIRTUAL(1, "虚拟商品");

    private final int type;
    private final String desc;

    GoodsCategoryType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean isRealGoods(Integer type) {
        return type != null && REAL.type == type;
    }

    public static boolean isVirtualGoods(Integer type) {
        return type != null && VIRTUAL.type == type;
    }
}
