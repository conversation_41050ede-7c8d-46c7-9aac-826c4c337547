package com.rzx.dim4.base.vo.place;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR> hwx
 * @since 2025/2/21 14:14
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PlaceShiftDetailExportVo implements Serializable {

    private static final long serialVersionUID = -1356556203645515394L;
    @ExcelProperty(value = "类目", index = 0)
    private String category;

    @ExcelProperty(value = "金额", index = 1)
    private String amount;

//    // 标记是否需要合并单元格
//    @ExcelIgnore
//    private Boolean isGroupTitle = false;
//
//    // 标记是否为合计行
//    @ExcelIgnore
//    private Boolean isTotal = false;
}
