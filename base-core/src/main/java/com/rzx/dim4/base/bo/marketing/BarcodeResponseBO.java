package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 商品标签表
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel("商品标签表")
public class BarcodeResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "创建者ID", hidden = true)
    private Long accountId;

    @ApiModelProperty(value = "创建者", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = " 商品种类，0:固装商品，1:虚拟商品")
    private int category;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品规格")
    private String spec;

    @ApiModelProperty(value = "商品种类")
    private String cate;

    @ApiModelProperty(value = "商品单位")
    private String unit;

    @ApiModelProperty(value = "商品销售价")
    private BigDecimal price;

    @ApiModelProperty(value = "商品成本价")
    private BigDecimal cost;

    @ApiModelProperty(value = "产地")
    private String madeIn;

    @ApiModelProperty(value = "图片url")
    private String url;

    @ApiModelProperty(value = "图片md5")
    private String md5;
}
