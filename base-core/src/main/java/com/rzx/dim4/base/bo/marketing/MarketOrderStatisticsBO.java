package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.place.PlaceShiftCouponBo;
import com.rzx.dim4.base.bo.place.PlaceShiftPackageBO;
import com.rzx.dim4.base.bo.place.PlaceShiftSaleBO;
import com.rzx.dim4.base.bo.place.PlaceShiftStorageBO;
import com.rzx.dim4.base.vo.marketing.StaticCouponVO;
import com.rzx.dim4.base.vo.marketing.StatisticsShopBusinessVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/** 交班统计数据
 * <AUTHOR> hwx
 * @since 2025/2/21 17:21
 */
@Getter
@Setter
@NoArgsConstructor
public class MarketOrderStatisticsBO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -6103865351957254174L;

    /**
     * 网费收入=网费+包时
     */
    private int internetFeeIncome;
    /**
     * 商超收入
     */
    private int shopIncome;

    /**
     * 前班退款金额
     */
    private int preRefundAmount;

    /**
     * 自定义收款
     */
//    private int customAmount;

    /**
     * 三方核销金额
     */
    private int verifyCouponAmount;

    /**
     * 美团核销金额
     */
    private int verifyMeiTuanAmount;

    /**
     * 抖音核销金额
     */
    private int verifyDouYinAmount;

    /**
     * 租赁商品收入
     */
    private int rentIncome;

    /**
     * 总收入
     */
    private int totalIncome;

    private List<PlaceShiftSaleBO> saleBos;

    private List<PlaceShiftStorageBO> storageBos;

    private List<PlaceShiftCouponBo> couponBos=new ArrayList<>();

    private List<PlaceShiftPackageBO> packageBOS=new ArrayList<>();

    /**
     * 商超经营概况
     */
    private StatisticsShopBusinessVO shopBusinessVos;

    /**
     * 优惠券赠送数据
     */
    private StaticCouponVO staticCouponVos;

}
