package com.rzx.dim4.base.enums.billing;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付状态
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Getter
public enum OrderPayStatus {

    CREATED(0, "0", "已创建"),
    PAYED(1, "1", "已支付"),
    DELIVERED(2, "2", "已派送"),
    FINISHED(3, "3", "已完成"),
    PARTIAL_REFUND(4, "4", "部分退款"),
    REFUND(5, "5", "已退款"),
    CANCELED(6, "6", "已取消");

    /**
     * 操作类型，保存到数据库
     */
    private final int code;
    /**
     * 操作类型，方便作为查询条件放到Map中
     */
    private final String type;
    /**
     * 操作类型描述
     */
    private final String desc;

    OrderPayStatus(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public static List<Integer> getAllStatus() {
        return Arrays.stream(OrderPayStatus.values()).map(OrderPayStatus::getCode).collect(Collectors.toList());
    }

    public static boolean checkOrderStatusAlreadyPayed(Integer status) {
        if (status == null) {
            return false;
        }
        return getSuccessStatus().contains(status);
    }

    public static List<Integer> getShowStatus() {
        return Lists.newArrayList(PAYED.code, DELIVERED.code, FINISHED.code, PARTIAL_REFUND.code, REFUND.code);
    }

    public static boolean isShowStatus(Integer status) {
        if (status == null) {
            return false;
        }

        List<OrderPayStatus> showStatusEnums = Lists.newArrayList(PAYED, DELIVERED, FINISHED, PARTIAL_REFUND, REFUND);
        for (OrderPayStatus value : showStatusEnums) {
            if (value.code == status) {
                return true;
            }
        }
        return false;
    }

    public static boolean isNotShowStatus(Integer status) {
        return !isShowStatus(status);
    }

    public static List<Integer> getSuccessStatus() {
        return Lists.newArrayList(PAYED.code, DELIVERED.code, FINISHED.code);
    }

    /**
     *  是否是支付/派送/完成状态
     */
    public static boolean isSuccessStatus(Integer status) {
        if (status == null) {
            return false;
        }
        return PAYED.code == status || DELIVERED.code == status || FINISHED.code == status;
    }

    public static List<Integer> getRefundStatus() {
        return Lists.newArrayList(PARTIAL_REFUND.code, REFUND.code);
    }

    /**
     * 是否是部分退款 或 已退款状态
     */
    public static boolean isRefundStatus(Integer status) {
        if (status == null) {
            return false;
        }
        return PARTIAL_REFUND.code == status || REFUND.code == status;
    }

    /**
     * 是否已退款
     */
    public static boolean isFullRefundStatus(Integer status) {
        if (status == null) {
            return false;
        }
        return REFUND.code == status;
    }
}
