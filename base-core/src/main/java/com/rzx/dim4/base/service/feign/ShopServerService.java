package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.shop.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.goods.query.GoodsQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;

import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 场所服务 待删除，不添加新接口
 * <AUTHOR>
 * @date 2023年1月30日 上午11:53:19
 * @version 1.0
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER,contextId = "ShopServerService", fallback = ShopServerServiceHystrix.class)
public interface ShopServerService {

    /***********************************/
    /************** 配置相关 ************/
    /***********************************/
    @GetMapping("/shop/admin/config/queryShopConfig")
    public GenericResponse<ObjDTO<ShopConfigBO>> queryShopConfig (@RequestParam String placeId);

    @PostMapping("/shop/admin/config/saveShopConfig")
    public GenericResponse<SimpleDTO> saveShopConfig (@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestBody ShopConfigBO shopConfigBO);
    /***********************************/
    /************** 商品相关 ************/
    /***********************************/
    @PostMapping("/shop/admin/goods/queryGoods")
    public GenericResponse<PagerDTO<GoodsBO>> queryGoods(@RequestBody GoodsQuery goodsQuery,
                                                         @RequestParam(name = "size", defaultValue = "10") int size,
                                                         @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/shop/admin/goods/save")
    public GenericResponse<ObjDTO<GoodsBO>> saveGoods (@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestBody GoodsBO goodsBO);

    @PostMapping("/shop/admin/goods/delete")
    public GenericResponse<SimpleDTO> deleteGoods (@RequestHeader(value = "request_ticket") String requestTicket,
                                                   @RequestParam String placeId,
                                                   @RequestParam String goodsId);

    @GetMapping("/shop/admin/goods/queryGoodsByGoodsId")
    public GenericResponse<ObjDTO<GoodsBO>> queryGoodsByGoodsId (@RequestParam String placeId,
                                                                 @RequestParam String goodsId);

    @GetMapping("/shop/admin/goods/queryGoodsByPlaceIds")
    public Map<String,Integer> queryGoodsByPlaceIds (@RequestParam List<String> placeIds);


    /**
     * 小程序删除商品
     * @param requestTicket
     * @return
     */
    @PostMapping("/shop/admin/goods/miniDeleteGoods")
    GenericResponse<?> miniDeleteGoods(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
                                       @RequestParam String goodsId);


    /***********************************/
    /************** 商品类型相关 ************/
    /***********************************/
    @PostMapping("/shop/admin/goodsType/queryPageGoodsType")
    public GenericResponse<PagerDTO<GoodsTypeBO>> queryPageGoodsType (@RequestBody Map<String, String> queryMap,
                                                                      @RequestParam(name = "size", defaultValue = "10") int size,
                                                                      @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/shop/admin/goodsType/save")
    public GenericResponse<ObjDTO<GoodsTypeBO>> saveGoodsType (@RequestHeader(value = "request_ticket") String requestTicket,
                                                               @RequestBody GoodsTypeBO goodsTypeBO);

    @PostMapping("/shop/admin/goodsType/batchSave")
    public GenericResponse<ObjDTO<GoodsTypeBO>> batchSaveGoodsType (@RequestHeader(value = "request_ticket") String requestTicket,
                                                               @RequestBody List<GoodsTypeBO> goodsTypeBOs);

    @PostMapping("/shop/admin/goodsType/delete")
    public GenericResponse<SimpleDTO> deleteGoodsType (@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestParam String placeId,
                                                       @RequestParam String goodsTypeId);

    @GetMapping("/shop/admin/goodsType/queryAllTypeByPlaceId")
    public GenericResponse<ListDTO<GoodsTypeBO>> queryAllTypeByPlaceId (@RequestParam String placeId);

    @GetMapping("/shop/admin/goodsType/queryGoodsTypeByGoodsTypeId")
    public GenericResponse<ObjDTO<GoodsTypeBO>> queryGoodsTypeByGoodsTypeId (@RequestParam String placeId,
                                                                             @RequestParam String goodsTypeId);

    /***********************************/
    /************** 商品订单相关 ************/
    /***********************************/
    @PostMapping("/shop/admin/orders/queryOrders")
    public GenericResponse<PagerDTO<OrdersBO>> queryOrders(@RequestBody Map<String, String> queryMap,
                                                           @RequestParam(name = "size", defaultValue = "10") int size,
                                                           @RequestParam(name = "page", defaultValue = "0") int page);


    /**
     * 查询当日的商超收入状态
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    @GetMapping("/shop/billing/shift/queryStatisticsToDay")
    public Map<String,Integer> queryStatisticsToDay (@RequestParam LocalDateTime startDateTime, @RequestParam LocalDateTime endDateTime,
                                                     @RequestParam List<String> placeIds);

    /**
     * 定时人统计营业数据
     */
    @GetMapping("shop/scheduler/goodsStatistics")
    public void statisticsGoodsSaleByDayScheduler(@RequestParam(required = false,defaultValue = "1")int dayAgo);

    /***********************************/
    /************** 商品退款订单相关 ************/
    /***********************************/
    @PostMapping("/shop/admin/ordersRefund/queryOrdersRefunds")
    public GenericResponse<PagerDTO<OrderRefundBO>> queryOrdersRefunds (@RequestBody Map<String, String> queryMap,
                                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                                        @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/shop/admin/ordersRefund/refund")
    public GenericResponse<SimpleDTO> refund (@RequestParam String placeId,
                                              @RequestParam String ordersId,
                                              @RequestParam String refundType,
                                              @RequestParam(required = false,defaultValue = "") String shiftId,
                                              @RequestParam(required = false,defaultValue = "") String cashierId,
                                              @RequestParam String cashierName,
                                              @RequestParam String remark,
                                              @RequestParam String refundGoodsJson);

    @GetMapping("/shop/admin/ordersRefund/getOrderRefund")
    public GenericResponse<ListDTO<OrderRefundGoodsBO>> getOrderRefund (@RequestParam String placeId,
                                                                        @RequestParam String ordersId);

    /***********************************/
    /************** 商品库存相关 ************/
    /***********************************/
    @PostMapping("/shop/admin/goodsStocks/queryGoodsStocks")
    public GenericResponse<PagerDTO<LogGoodsStocksBO>> queryGoodsStocks(@RequestBody Map<String, String> queryMap,
                                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                                        @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/shop/admin/goodsStocks/updateGoodsStocks")
    public GenericResponse<SimpleDTO> updateGoodsStocks (@RequestParam String placeId,
                                                         @RequestParam String stocksChangingType,
                                                         @RequestParam(required = false,defaultValue = "") String shiftId,
                                                         @RequestParam(required = false,defaultValue = "") String cashierId,
                                                         @RequestParam String cashierName,
                                                         @RequestParam String remark,
                                                         @RequestParam String stocksGoodsJson);

    /***********************************/
    /************** 商品统计相关 ************/
    /***********************************/
    @GetMapping("/shop/admin/statistics/goodsSale")
    public GenericResponse<PagerDTO<StatisticsGoodsSaleByDayBO>> goodsSale  (@RequestParam(name = "size", defaultValue = "10") int size,
                                                                             @RequestParam(name = "page", defaultValue = "0") int page,
                                                                             @RequestParam String placeId,
                                                                             @RequestParam String rankType,
                                                                             @RequestParam String startTime,
                                                                             @RequestParam String endTime);

    @GetMapping("/shop/admin/statistics/goodsSaleByMonth")
    public GenericResponse<ListDTO<StatisticsGoodsSaleByDayBO>> goodsSaleByMonth (@RequestParam String placeId,
                                                                                  @RequestParam String startTime,
                                                                                  @RequestParam String endTime);

    @GetMapping("/shop/admin/statistics/goodsSaleByDay")
    public GenericResponse<PagerDTO<StatisticsGoodsSaleByDayBO>> goodsSaleByDay  (@RequestParam(name = "size", defaultValue = "10") int size,
                                                                                  @RequestParam(name = "page", defaultValue = "0") int page,
                                                                                  @RequestParam String placeId,
                                                                                  @RequestParam String startTime,
                                                                                  @RequestParam String endTime);

    @GetMapping("/shop/admin/statistics/sumGoodsSaleByDate")
    public GenericResponse<ObjDTO<StatisticsGoodsSaleByDayBO>> sumGoodsSaleByDate (@RequestParam String placeId,
                                                                                  @RequestParam String startTime,
                                                                                  @RequestParam String endTime);


    /***********************************/
    /************** 商品交接班统计数据相关 **/
    /***********************************/
    @GetMapping("/shop/billing/shift/queryGoodsStatistics")
    public Map<String,Integer> queryGoodsStatistics (@RequestParam String placeId,
                                                     @RequestParam String shiftId);

}
