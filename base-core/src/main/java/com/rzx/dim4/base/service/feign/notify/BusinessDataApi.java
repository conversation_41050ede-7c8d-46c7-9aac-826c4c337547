package com.rzx.dim4.base.service.feign.notify;

import com.rzx.dim4.base.bo.notify.polling.BookSeatsBusinessBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.notify.BusinessDataApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/11
 **/
@Primary
@FeignClient(value = FeginConstant.NOTIFY_SERVER, contextId = "BusinessDataApi", fallback = BusinessDataApiHystrix.class)
public interface BusinessDataApi {

    String URL = "/feign/notify/business";

    @PostMapping(URL + "/pushBookSeats")
    GenericResponse<ObjDTO<BookSeatsBusinessBO>> pushBookSeats(@RequestHeader(value = "request_ticket") String requestTicket,
                                                               @RequestBody BookSeatsBusinessBO bookSeatsBusinessBO);
}
