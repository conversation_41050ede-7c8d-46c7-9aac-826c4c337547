package com.rzx.dim4.base.enums.shop;

import lombok.Getter;

/**
 * 商超订单状态
 */
@Getter
public enum ShopOrdersStatus {
    CREATED(0,"已创建"),
    PAID(1,"已支付"),
    COMPLETED(2,"已完成"),
    PART_REFUND(3,"部分退款"),
    REFUND(4,"已退款"),
    CANCELLED(5,"已取消订单（查询退款单的，本身不存）");
    private final int code;
    private final String name;
    ShopOrdersStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
