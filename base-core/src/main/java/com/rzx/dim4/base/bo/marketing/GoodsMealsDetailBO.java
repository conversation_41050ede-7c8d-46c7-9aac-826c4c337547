package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;

/**
 * 商品套餐内部商品信息
 * <AUTHOR>
 * @date 2024年12月04日 11:27
 */
@Getter
@Setter
public class GoodsMealsDetailBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;

    private String placeId; // 场所ID

    private String mealsId; // 套餐id，场所唯一，从140000开始递增

    private String goodsId; // 商品ID

    private int goodsPrice; // 价格，GoodsMeals.mealsType=0时，主商品和副商品都能设置价格，= 1时 只有副商品能设置价格

    @Digits(integer = 1,fraction = 0,message = "参数错误")
    @Max(value = 1,message = "参数错误")
    @Min(value = 0,message = "参数错误")
    private int goodsDetailType; // 0主商品，1副商品

    private String goodsName;

    private String goodsPic;
    private String goodsPicMd5;

    private int unitPrice; // 商品销售价，单位分
    private String goodsTypeId; // 商品类型ID
    private String goodsTypeName; // 商品类型名称

}
