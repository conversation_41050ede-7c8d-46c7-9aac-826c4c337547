package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.DailyGoodsBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;

@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "dailyGoodsClient", fallback = ShopServerServiceHystrix.class)
public interface DailyGoodsClient {

    String URL= "/shop/admin/dailyGoods";

    @GetMapping(URL+"/listDailyGoodsPage")
    GenericResponse<PagerDTO<DailyGoodsBO>> dailyGoodsPage(@SpringQueryMap DailyGoodsBO dailyGoodsBO);

}
