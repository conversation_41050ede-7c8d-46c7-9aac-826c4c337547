package com.rzx.dim4.base.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class IpUtils {

    // 常见的代理服务器IP头（按优先级排序）
    private static final List<String> HEADERS_TO_TRY = Arrays.asList(
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "X-Real-IP"
    );

    /**
     * 获取客户端真实IP地址
     * @param request HttpServletRequest对象
     * @return 真实IP或空字符串
     */
    public static String getClientIp(HttpServletRequest request) {
        try {
            // 1. 尝试从各个代理头中获取
            for (String header : HEADERS_TO_TRY) {
                String ip = request.getHeader(header);
                if (isValidIp(ip)) {
                    // 处理多级代理情况（取第一个有效IP）
                    return parseMultiIp(ip);
                }
            }

            // 2. 直接获取远程地址（无代理时）
            String remoteAddr = request.getRemoteAddr();
            return isValidIp(remoteAddr) ? remoteAddr : "";
        } catch (Exception e) {
            log.info("获取客户端 IP 失败，使用默认值 'unknown' 替代", e);
            return "unknown";
        }
    }

    /**
     * 验证IP有效性
     */
    private static boolean isValidIp(String ip) {
        return ip != null &&
                !ip.isEmpty() &&
                !"unknown".equalsIgnoreCase(ip) &&
                !ip.startsWith("0:0:0:0:0:0:0:1") && // 排除IPv6本地地址
                !ip.startsWith("127.0.0.1");          // 排除IPv4本地地址
    }

    /**
     * 处理多IP情况（如：X-Forwarded-For: client, proxy1, proxy2）
     */
    private static String parseMultiIp(String ip) {
        if (ip.contains(",")) {
            String[] ips = ip.split("\\s*,\\s*");
            for (String subIp : ips) {
                if (isValidIp(subIp)) {
                    return subIp;
                }
            }
        }
        return ip;
    }
}