package com.rzx.dim4.base.utils;

/**
 * 根据经纬度计算两地点距离
 *
 * <AUTHOR>
 * @since 2023/5/30
 **/
public class GeoUtil {

    /**
     *
     * @param lon1 经度 1
     * @param lat1 纬度 1
     * @param lon2 经度 2
     * @param lat2 纬度 2
     * @return 距离 米
     */
    public static double distance(double lon1, double lat1, double lon2, double lat2) {
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        double sinLat1 = Math.sin(lat1Rad);
        double sinLat2 = Math.sin(lat2Rad);
        double cosLat1 = Math.cos(lat1Rad);
        double cosLat2 = Math.cos(lat2Rad);
        double sinDeltaLat = Math.sin(deltaLat);
        double sinDeltaLon = Math.sin(deltaLon);
        double cosDeltaLat = Math.cos(deltaLat);
        double cosDeltaLon = Math.cos(deltaLon);

        double a = sinLat1 * sinLat2 + cosLat1 * cosLat2 * cosDeltaLon;

        return EARTH_RADIUS * Math.acos(a);
    }

    /**
     * 赤道半径（单位米）
     */
    static double EARTH_RADIUS = 6371000;

    /**
     *
     * @param lon1 经度 1
     * @param lat1 纬度 1
     * @param lon2 经度 2
     * @param lat2 纬度 2
     * @return  距离 米
     */
    public static double getDistance(double lon1, double lat1, double lon2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);

        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);

        double v = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        double v1 = v * EARTH_RADIUS;
        return (double) Math.round(v1 * 10000) / 10000;
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }
}
