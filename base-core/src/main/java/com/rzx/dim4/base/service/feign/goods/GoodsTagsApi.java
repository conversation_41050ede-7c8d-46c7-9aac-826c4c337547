package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.bo.shop.GoodsTagsBO;
import com.rzx.dim4.base.bo.shop.ShopConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.shop.GoodsTagsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年07月19日 14:39
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "shopTagsApi", fallback = GoodsTagsApiHystrix.class)
public interface GoodsTagsApi {

    String URL = "/feign/shop/goodsTags";

    /**
     * 分页查询商品规格列表
     * @return
     */
    @PostMapping(URL + "/findPage")
    GenericResponse<ResponsePage<GoodsTagsBO>> findPage(@RequestBody GoodsTagsBO goodsTagsBO,
                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                        @RequestParam(name = "page", defaultValue = "0") int page);

    /**
     * 查询商品规格详情
     * @return
     */
    @GetMapping(URL + "/findGoodsTagsByTagsId")
    GenericResponse<ObjDTO<GoodsTagsBO>> findGoodsTagsByTagsId(@RequestParam(name = "placeId") String placeId , @RequestParam(name = "tagsId") String tagsId);

    /**
     * 查询商品绑定的规格详情列表
     * @return
     */
    @GetMapping(URL + "/findGoodsTagsByGoodsId")
    GenericResponse<ListDTO<GoodsTagsBO>> findGoodsTagsByGoodsId(@RequestParam(name = "placeId") String placeId , @RequestParam(name = "goodsId") String goodsId);


    /**
     * 新增或修改
     * @return
     */
    @PostMapping(URL + "/saveOrUpdate")
    GenericResponse<?> saveOrUpdate(@RequestHeader(value = "request_ticket") String requestTicket , @RequestBody GoodsTagsBO goodsTagsBO);


}
