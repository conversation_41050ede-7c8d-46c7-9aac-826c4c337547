package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "std_rate")
@XmlAccessorType(XmlAccessType.NONE)
@ToString
public class BillingRuleCommonXml {

    @XmlElement(name = "std_rate_area_name")
    private String areaName; // 区域名称

    @XmlElement(name = "std_rate_card_type_name")
    private String cardTypeName; // 卡类型名称

    @XmlElement(name = "std_rate_rate_text")
    private String prices; // 价格

    @XmlElement(name = "min_consume")
    private String minConsume; // 最低消费

    @XmlElement(name = "cost_unit")
    private String unitConsume; // 单位扣费

}
