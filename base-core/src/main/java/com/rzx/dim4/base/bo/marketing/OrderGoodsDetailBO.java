package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 订单商品
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
public class OrderGoodsDetailBO extends AbstractEntityBO {

    @ApiModelProperty("场所ID")
    private String placeId;

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("商品ID")
    private String goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品数量")
    private int quantity;

    @ApiModelProperty("商品单价")
    private int unitPrice;

    @ApiModelProperty("折扣金额")
    private int discounts;

    @ApiModelProperty("商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty("商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty("订单提交时的口味")
    private String specs;

    @ApiModelProperty("商品图片")
    private String goodsPic;

    @ApiModelProperty("套餐id")
    private String mealsId;

    @ApiModelProperty("是否是赠送，0否，1是")
    private int present;

    @ApiModelProperty("本金：虚拟商品实际充值了多少到钱包")
    private int goodsQuota;

    @ApiModelProperty("赠送金额：虚拟商品实际充值了多少到网费")
    private int goodsPresentAmount;

    @ApiModelProperty("创建人ID")
    private Long creater;

    @ApiModelProperty("创建时间")
    private LocalDateTime created;

    @ApiModelProperty("网费虚拟商品绑定的网费充送活动ID")
    private String internetFeeId;

    @ApiModelProperty("订单中购买的充值赠送")
    private InternetFeeBO internetFeeBo;
}
