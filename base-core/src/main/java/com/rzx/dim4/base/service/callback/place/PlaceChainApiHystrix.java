package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceChainBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceChainApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/5
 **/
@Slf4j
@Service
public class PlaceChainApiHystrix implements PlaceChainApi {

    /**
     * 新增连锁组织
     *
     * @param requestTicket
     * @param placeChainBO  连锁组织保存对象
     */
    @Override
    public void createNewPlaceChain(String requestTicket, PlaceChainBO placeChainBO) {
        log.error("add 接口异常，PlaceChainBO={}", new Gson().toJson(placeChainBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> newChainId() {
        log.error("newChainId 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    /**
     * 更新连锁组织信息
     *
     * @param requestTicket
     * @param placeChainBO  连锁组织信息保存对象
     */
    @Override
    public void update(String requestTicket, PlaceChainBO placeChainBO) {
        log.error("update 接口异常，PlaceChainBO={}", new Gson().toJson(placeChainBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    /**
     * 查询连锁组织列表
     *
     * @param requestTicket
     * @param dtRequest     查询对象
     * @return
     */
    @Override
    public GenericResponse<PagerDTO<PlaceChainBO>> page(String requestTicket, DataTablesRequest dtRequest) {
        log.error("page 接口异常，dtRequest={}", new Gson().toJson(dtRequest));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceChainBO>> findByChainId(String requestTicket, String chainId) {
        log.error("findByChainId 接口异常，chainId={}", chainId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> resetPwd(String requestTicket, String chainId) {
        log.error("resetPwd 接口异常，chainId={}", chainId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> cleanChainMemberLimit(String requestTicket, String chainId) {
        log.error("cleanChainMemberLimit 接口异常，chainId={}", chainId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceChainBO>> findAllPlaceChains() {
        log.error("findAllPlaceChains 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceChainBO>> findByChainIds(List<String> chainIds) {
        log.error("findByChainIds 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
