/*
 * @(#)PlaceConfigApi.java 1.00 2024-7-30
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.place;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceConfigApiHystrix;

/**
 * <p>Title:场所配置接口</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-7-30
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceConfigApi", fallback = PlaceConfigApiHystrix.class)
public interface PlaceConfigApi {
	String URL = "/feign/place/placeConfig";
	
	/**
	 * 批量配置场所参数
	 * @param requestTicket
	 * @param confList
	 * @return
	 */
    @SuppressWarnings("rawtypes")
	@PostMapping(URL + "/batchSavePlaceConfig")
    GenericResponse<SimpleDTO> batchSavePlaceConfig(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody List<PlaceConfigBO> confList);
    
}
