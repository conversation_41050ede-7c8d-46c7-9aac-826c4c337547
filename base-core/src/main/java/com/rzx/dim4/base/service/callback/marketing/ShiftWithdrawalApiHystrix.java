package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.ShiftWithdrawalBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.ShiftWithdrawalApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class ShiftWithdrawalApiHystrix implements ShiftWithdrawalApi {

    @Override
    public void schedulerComputeAmount() {
        log.error("接口异常，ShiftWithdrawalApiHystrix,schedulerComputeAmount");
    }

    @Override
    public void schedulerWithdrawalAmount() {
        log.error("接口异常，ShiftWithdrawalApiHystrix,schedulerWithdrawalAmount");
    }

    @Override
    public void schedulerWithdrawalCheck() {
        log.error("接口异常，ShiftWithdrawalApiHystrix,schedulerWithdrawalCheck");
    }

    @Override
    public GenericResponse<PagerDTO<ShiftWithdrawalBO>> findList(String placeId, String startDate, String endDate, int page, int size) {
        log.error("接口异常，ShiftWithdrawalApiHystrix,findList");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
