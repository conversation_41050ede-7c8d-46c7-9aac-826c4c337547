package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsReceiptApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsReceiptApi", fallback = MarketingGoodsReceiptApiHystrix.class)
public interface MarketingGoodsReceiptApi {
    String URL = "/feign/marketing/goods/receipt";

    @PostMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<GoodsReceiptResponseBO>> findPageList(@RequestBody GoodsReceiptRecordRequestBo paramsBo);

    @PostMapping(value = URL + "/save")
    GenericResponse<SimpleDTO> saveGoodsReceipt(@RequestBody GoodsReceiptSaveRequestBO goodsReceiptBO);

    @GetMapping(value = URL + "/findDetails")
    GenericResponse<ObjDTO<GoodsReceiptResponseBO>> findGoodsReceiptDetail(@RequestParam String placeId, @RequestParam String goodsReceiptNo);

    @PostMapping(value = URL + "/findGoodsInventoryPage")
    GenericResponse<PagerDTO<StorageGoodsResponseBO>> findGoodsInventoryPage(@RequestBody GoodsInventoryQueryRequestBO paramsBo);

    @PostMapping(value = URL + "/statisticsGoodsStorage")
    GenericResponse<ObjDTO<StorageGoodsStatisticsResponseBO>> statisticsGoodsStorage(@RequestBody GoodsInventoryStatisticsQueryRequestBO paramsBo);

    @PostMapping(value = URL + "/queryGoodsInventoryRecordDetail")
    GenericResponse<PagerDTO<GoodsInventoryChangeRecordResponseBO>> queryGoodsInventoryRecordDetail(@RequestBody GoodsInventoryRecordQueryRequestBO paramsBo);
}
