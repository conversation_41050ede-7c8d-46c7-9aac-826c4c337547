package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StatisticsGoodsReceiptListExportVO implements Serializable {


    private static final long serialVersionUID = -7891666909293355225L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    private String goodsName;

    /**
     * 上次采购价
     */
    @ExcelProperty(value = "上次采购价",index = 1)
    private float prePrice;

    /**
     * 本次采购价
     */
    @ExcelProperty(value = "本次采购价",index = 2)
    private float price;

    /**
     *  均价
     */
    @ExcelProperty(value = "市场均价",index = 3)
    private float avgPrice;

}
