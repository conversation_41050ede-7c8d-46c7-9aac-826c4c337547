package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceChainLogPlaceChainApiHystrix;
import com.rzx.dim4.base.vo.PlaceChainRoamOrderVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "logPlaceChainApi", fallback = PlaceChainLogPlaceChainApiHystrix.class)
public interface PlaceChainLogPlaceChainApi {

    String URL = "/feign/billing/place/chain/logPlaceChain";

    /**
     * 连锁漫游订单信息
     * @param loginIds
     * @return
     */
    @PostMapping(URL + "/findChainRoamList")
    public GenericResponse<ListDTO<PlaceChainRoamOrderVO>> findChainRoamList (@RequestParam List<String> loginIds);

    @GetMapping(URL + "/queryRoamLoginId")
    List<String> queryRoamLoginId(@RequestParam String loginId);
}
