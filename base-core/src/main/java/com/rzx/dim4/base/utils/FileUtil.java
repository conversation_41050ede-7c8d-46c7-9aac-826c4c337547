package com.rzx.dim4.base.utils;

import org.springframework.util.StringUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

public class FileUtil {

    /**
     * 后缀分隔符
     */
    public static final String DOT = ".";

    /**
     * 文件路径结尾
     */
    public static final String END_PATH = "/";

    /**
     * 中间数组大小
     */
    private static final int ARRAY_SIZE = 4096;

    /**
     * 文件上传
     *
     * @param inputStream 上传的文件流
     * @param fileName    文件名
     * @param path        上传文件保存路径
     * @return 新文件对象
     * @throws IOException
     */
    public static File uploadFile(InputStream inputStream, String fileName, String path) throws IOException {
        if (inputStream == null) {
            return null;
        }
        String fileSuffix = getFileSuffix(fileName);
        String newFileName = null;
        if (!path.endsWith(END_PATH)) {
            path += END_PATH;
        }
        if (!StringUtils.isEmpty(fileName)) {
            BufferedInputStream in;
            BufferedOutputStream out;
            try {
                //新文件名,时间戳
                newFileName = fileName.replace(fileSuffix, "_") + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + fileSuffix;

                File targetPath = new File(path);
                if (!targetPath.exists()) {
                    targetPath.mkdirs();
                }
                File targetFile = new File(path, newFileName);

                //缓存流
                in = new BufferedInputStream(inputStream);
                out = new BufferedOutputStream(new FileOutputStream(targetFile));

                // 保存
                byte[] buffer = new byte[ARRAY_SIZE];
                int length = 0;
                while ((length = in.read(buffer)) != -1) {
                    out.write(buffer, 0, length);
                }

                //关闭流
                in.close();
                out.flush();
                out.close();

                return targetFile;
            } catch (Exception e) {
                throw new IOException("文件解压失败");
            } finally {
                inputStream.close();
            }
        }
        return null;
    }

    /**
     * 会员列表文件解密密钥
     * @param type
     * @return
     */
    public static String generateMemberFileKey(int type) {
        //获取当前时间字符串:20180411
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        //获取时间字符串md5值
        String degestStr = generateDigestWithMD5NoSalt(now).toUpperCase();

        //根据类型截取md5
        String result;
        if (type == 1) {
            result = degestStr.substring(0, 8);
        } else if (type == 2) {
            result = degestStr.substring(4, 12);
        } else {
            result = degestStr.substring(24, 32);
        }
        return result;
    }

    /**
     * 解密会员文件
     * @param type
     * @param in
     * @return
     * @throws IOException
     */
    public static byte[] decryptMemberFile(int type, InputStream in) throws IOException {
        //解密结果字符数组
        byte[] result;
        //内存输出流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        //解密过程
        String key = generateMemberFileKey(type);
        Integer keyPos = 0;
        char[] keys = key.toCharArray();

        byte[] bs = new byte[512];
        int len;
        while ((len = in.read(bs)) != -1) {
            for (int i = 0; i < len; i++) {
                bs[i] ^= keys[keyPos % (key.length())];
                keyPos++;
            }
            out.write(bs, 0, len);
        }
        result = out.toByteArray();
        //关闭流
        out.close();
        in.close();

        return result;
    }

    /**
     * 简易加密
     * 老管家加密规则
     * @param text
     * @return
     */
    public static String generateDigestWithMD5NoSalt(String text) {
        try {
            byte[] bytes = text.getBytes("UTF-8");
            return generateDigestWithMD5NoSalt(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 简易加密
     * @param bytes
     * @return
     */
    public static String generateDigestWithMD5NoSalt(byte[] bytes){
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = messageDigest.digest(bytes);
            StringBuffer hexValue = new StringBuffer(32);
            for (int i = 0; i < md5Bytes.length; i++) {
                int val = ((int) md5Bytes[i]) & 0xff;
                if (val < 16) {
                    hexValue.append("0");
                }
                hexValue.append(Integer.toHexString(val));
            }
            return hexValue.toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * zip文件密码生成方法
     * @param type
     * @param placeId
     * @return
     */
    public static String generateZipPassword(String type,String placeId){
        LocalDate now = LocalDate.now();

        StringBuffer sb = new StringBuffer(type);
        sb.append(String.format("%04X", now.getYear()));
        sb.append(placeId);
        sb.append(String.format("%02X", now.getDayOfMonth()));
        sb.append(String.format("%02X", now.getMonth().getValue()));

        return sb.toString();
    }

    /**
     * 获取文件后缀
     *
     * @param fileName
     * @return
     */
    public static String getFileSuffix(String fileName) {
        return DOT + fileName.substring(fileName.lastIndexOf(DOT) + 1);
    }

    /**
     * xml中读取对象
     *
     * @param in
     * @param tClass
     * @return
     * @throws JAXBException
     */
    public static <T> T readObj(InputStream in, Class<T> tClass) throws JAXBException {
        //获取上下文对象
        JAXBContext context = JAXBContext.newInstance(tClass);
        //通过JAXBContext对象创建对应的Unmarshaller对象。
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return (T) unmarshaller.unmarshal(in);
    }

    /**
     * 图片 base64 获取对应的 md5值
     * @param facePhotoBase64FullFormat
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String getMd5Hash(String facePhotoBase64FullFormat) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        String md5Hash;
        byte[] digest = md5.digest(Base64.getDecoder().decode(facePhotoBase64FullFormat.split(",")[1]));
        // 将字节数组转换为十六进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        md5Hash = sb.toString();

        return md5Hash;
    }
}
