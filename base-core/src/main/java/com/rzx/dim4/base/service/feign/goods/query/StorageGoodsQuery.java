package com.rzx.dim4.base.service.feign.goods.query;

import com.rzx.dim4.base.bo.shop.StorageGoodsBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 *  库存商品VO
 * <AUTHOR>
 */
@Getter
@Setter
public class StorageGoodsQuery extends StorageGoodsBO {

    private static final long serialVersionUID = -8531952373819730954L;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 原商品库存
     */
    private int newStocks;

    /**
     * 进货金额
     */
    private int price;

    /**
     * 售卖价格
     */
    private int unitPrice;


    private String goodsPic; // 商品图片

    private String goodsPicMD5; //保存图片的MD5值

    private String goodsBarcode; // 商品条形码

    /**
     * 操作人
     */
    private String creator;


    private String remark;


}
