package com.rzx.dim4.base.utils;

import com.google.common.io.BaseEncoding;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;

/**
 * 验证网址：https://oktools.net/aes
 * AES加密模式ECB，填充PKCS7，数据块256位，偏移量为空，选择对应输出，字符集UTF-8
 * 
 * <AUTHOR>
 * @date Jan 3, 2020 3:13:30 PM
 */
@Slf4j
public class AES256Cryptor {

	// ======================>BASE64<======================

	/**
	 * BASE64加密
	 * 
	 * @param clearText 明文，待加密的内容
	 * @param password  密码，加密的密码
	 * @return 返回密文，加密后得到的内容。加密错误返回null
	 * @throws UnsupportedEncodingException
	 */
	public static String encryptBase64(String clearText, String password) {
		if (StringUtils.isEmpty(password) || password.length() != 16) {
			log.error("密码不能为空，且密码长度为16位");
			return null;
		}
		try {
			byte[] cipherTextBytes = encrypt(clearText.getBytes("UTF-8"), password.getBytes());
			String cipherText = BaseEncoding.base64().encode(cipherTextBytes);
			return cipherText;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	/**
	 * BASE64解密
	 * 
	 * @param cipherText 密文，带解密的内容
	 * @param password   密码，解密的密码
	 * @return 返回明文，解密后得到的内容。解密错误返回null
	 */
	public static String decryptBase64(String cipherText, String password) {
		try {
			byte[] cipherTextBytes = BaseEncoding.base64().decode(cipherText);
			byte[] clearTextBytes = decrypt(cipherTextBytes, password.getBytes());
			return new String(clearTextBytes, "UTF-8");
		} catch (Exception e) {	
			log.error(e.getMessage());
			return null;
		}
	}

	// ======================>HEX<======================

	/**
	 * HEX加密
	 * 
	 * @param clearText 明文，待加密的内容
	 * @param password  密码，加密的密码
	 * @return 返回密文，加密后得到的内容。加密错误返回null
	 */
	public static String encryptHex(String plaintextName, String password) {
		try {
			byte[] cipherTextBytes = encrypt(plaintextName.getBytes("UTF-8"), password.getBytes());
			String cipherText = byte2hex(cipherTextBytes);
			return cipherText;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	/**
	 * HEX解密
	 * 
	 * @param encryptedName 密文，带解密的内容
	 * @param password      密码，解密的密码
	 * @return 返回明文，解密后得到的内容。解密错误返回null
	 */
	public static String decryptHex(String encryptedName, String password) {
		try {
			byte[] cipherTextBytes = hex2byte(encryptedName);
			byte[] clearTextBytes = decrypt(cipherTextBytes, password.getBytes());
			return new String(clearTextBytes, "UTF-8");
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	/**
	 * 原始加密
	 * 
	 * @param clearTextBytes 明文字节数组，待加密的字节数组
	 * @param pwdBytes       加密密码字节数组
	 * @return 返回加密后的密文字节数组，加密错误返回null
	 */
	private static byte[] encrypt(byte[] clearTextBytes, byte[] pwdBytes) {
		try {
			SecretKeySpec keySpec = new SecretKeySpec(pwdBytes, "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, keySpec);
			byte[] cipherTextBytes = cipher.doFinal(clearTextBytes);
			return cipherTextBytes;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	/**
	 * 原始解密
	 * 
	 * @param cipherTextBytes 密文字节数组，待解密的字节数组
	 * @param pwdBytes        解密密码字节数组
	 * @return 返回解密后的明文字节数组，解密错误返回null
	 */
	private static byte[] decrypt(byte[] cipherTextBytes, byte[] pwdBytes) {
		try {
			SecretKeySpec keySpec = new SecretKeySpec(pwdBytes, "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.DECRYPT_MODE, keySpec);
			byte[] clearTextBytes = cipher.doFinal(cipherTextBytes);
			return clearTextBytes;
		} catch (Exception e) {
			log.error(e.getMessage());
			return null;
		}
	}

	private static String byte2hex(byte[] bytes) {
		StringBuffer sb = new StringBuffer(bytes.length * 2);
		String tmp = "";
		for (int n = 0; n < bytes.length; n++) {
			// 整数转成十六进制表示
			tmp = (java.lang.Integer.toHexString(bytes[n] & 0XFF));
			if (tmp.length() == 1) {
				sb.append("0");
			}
			sb.append(tmp);
		}
		return sb.toString();
	}

	private static byte[] hex2byte(String str) {
		if (str == null || str.length() < 2) {
			return new byte[0];
		}
		str = str.toLowerCase();
		int l = str.length() / 2;
		byte[] result = new byte[l];
		for (int i = 0; i < l; ++i) {
			String tmp = str.substring(2 * i, 2 * i + 2);
			result[i] = (byte) (Integer.parseInt(tmp, 16) & 0xFF);
		}
		return result;
	}

}