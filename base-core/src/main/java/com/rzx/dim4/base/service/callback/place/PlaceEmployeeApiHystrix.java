package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceEmployeeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/31
 **/
@Slf4j
@Service
public class PlaceEmployeeApiHystrix implements PlaceEmployeeApi {

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> checkAccountByMobile(String requestTicket, String mobile) {
        log.error("checkAccountByMobile 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> checkAccountByMobileForRegister(String requestTicket, String mobile) {
        log.error("checkAccountByMobileForRegister 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleObjDTO> login(String requestTicket, String mobile, String password, String unionId, String openId, Integer type) {
        log.error("login 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> register(String requestTicket, PlaceAccountBO placeAccountBO, String code, String unionId) {
        log.error("register 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> resetPassword(String requestTicket, String mobile, String password, String code, String placeId) {
        log.error("resetPassword 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> bindMiniProgram(String requestTicket, String placeId, String accountId, String openId, String unionId, String mobile) {
        log.error("bindMiniProgram 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse judgeAccountBindBar(String requestTicket, String mobile) {
        log.error("judgeAccountBindBar 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> updateInfo(String requestTicket, PlaceAccountBO placeAccountBO) {
        log.error("updateInfo 接口异常，placeAccountBO={}", new Gson().toJson(placeAccountBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse unBindMiniProgram(String requestTicket, String placeId, String mobile) {
        log.error("unBindMiniProgram 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> queryAccountInfo(String requestTicket, String mobile, String placeId) {
        log.error("queryAccountInfo 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse updateMobile(String requestTicket, String mobile, String newMobile, String code) {
        log.error("updateMobile 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceAccountBO>> queryCashierList(String placeId) {
        log.error("queryCashierList 接口异常，placeId={}", placeId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }


}
