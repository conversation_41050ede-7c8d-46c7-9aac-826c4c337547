package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingVirtualGoodsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingVirtualGoodsApiHystrix implements MarketingVirtualGoodsApi {

    @Override
    public GenericResponse<PagerDTO<BarcodeResponseBO>> findVirtualGoodsTemplatePage(GoodsVirtualTemplateRequestBo paramsBo) {
        log.error("接口异常:::MarketingVirtualGoodsApiHystrix.findVirtualGoodsTemplatePage(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveVirtualGoods(GoodsVirtualAddRequestBO paramsBo) {
        log.error("接口异常:::MarketingVirtualGoodsApiHystrix.saveVirtualGoods(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateVirtualGoods(GoodsVirtualUpdateRequestBO paramsBo) {
        log.error("接口异常:::MarketingVirtualGoodsApiHystrix.updateVirtualGoods(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> removeVirtualGoods(String placeId, String goodsId) {
        log.error("接口异常:::MarketingVirtualGoodsApiHystrix.removeVirtualGoods(placeId={}, goodsId={})", placeId, goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}