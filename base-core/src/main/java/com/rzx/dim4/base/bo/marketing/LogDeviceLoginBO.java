package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年05月14日 17:09
 */
@Getter
@Setter
public class LogDeviceLoginBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;

    private String placeId; // 场所ID
    private String accountId; // 登录人id
    private String accountName; // 登录人名称
    private String uuid;

}
