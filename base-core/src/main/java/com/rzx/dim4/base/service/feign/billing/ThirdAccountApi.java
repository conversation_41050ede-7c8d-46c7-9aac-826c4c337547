package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.ThirdAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.ThirdAccountApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/3/22
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "thirdAccountApi", fallback = ThirdAccountApiHystrix.class)
public interface ThirdAccountApi {

    String URL = "/feign/billing/thirdAccount";

    /**
     * 全部第三方账号
     *
     * @param requestTicket
     * @return
     */
    @GetMapping(URL + "/all")
    GenericResponse<ListDTO<ThirdAccountBO>> all(@RequestHeader(value = "request_ticket") String requestTicket);

    /**
     * 只返回可用的第三方账号
     *
     * @param requestTicket
     * @return
     * @apiNote 只返回可用的：<br/>
     * 1、不包括 key 长度大于17的；<br/>
     * 2、不包括 deleted = 1 的；<br/>
     * 3、不包括 status = 1 的。
     */
    @GetMapping(URL + "/list")
    GenericResponse<ListDTO<ThirdAccountBO>> listCanUse(@RequestHeader(value = "request_ticket") String requestTicket);

    /**
     * 获取第三方账号列表
     * @param requestTicket
     * @param thirdAccountIds
     * @return
     */
    @GetMapping(URL + "/findByThirdAccountIds")
    GenericResponse<ListDTO<ThirdAccountBO>> findByThirdAccountIds(@RequestHeader(value = "request_ticket") String requestTicket,
                                                            @RequestParam List<String> thirdAccountIds);

    @GetMapping(URL + "/findByPlaceId")
    GenericResponse<ObjDTO<ThirdAccountBO>> findByPlaceId(@RequestHeader(value = "request_ticket") String requestTicket,
                                                          @RequestParam String placeId);
}
