package com.rzx.dim4.base.service.feign.user;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.user.Dim4UserApiHystrix;
import com.rzx.dim4.base.service.callback.user.StaffMiniAppApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025年04月24日 18:38
 */
@Primary
@FeignClient(value = FeginConstant.USER_SERVER, contextId = "StaffMiniAppApi", fallback = StaffMiniAppApiHystrix.class)
public interface StaffMiniAppApi {

    String URL = "/feign/user/staffMiniApp";

    @GetMapping(URL + "/doGetUrlLink")
    GenericResponse<SimpleDTO> doGetUrlLink(@RequestParam("query") String query,
                                            @RequestParam("path") String path);
}
