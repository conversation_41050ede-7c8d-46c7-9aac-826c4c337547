package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.LoginCashierBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceAccountApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * place-server 的 PlaceAccount feign 接口
 * @apiNote header 参数 requestTicket 必填
 *
 * <AUTHOR>
 * @since 2023/8/31
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceAccountApi", fallback = PlaceAccountApiHystrix.class)
public interface PlaceAccountApi {

    /**
     * 请求前缀，用于区分不同的接口，拼接时放最前面
     */
    String URL = "/feign/place/account";

    /**
     * 查询当前场所的收银员账号
     * @param requestTicket 请求ticket（feign调用必填）
     * @param placeId 场所id
     * @param loginName 登录名
     * @return 收银员账号
     */
    @GetMapping(URL + "/findCashierExist")
    GenericResponse<ObjDTO<PlaceAccountBO>> findExistCashierAccount(@RequestHeader(value = "request_ticket") String requestTicket,
                                                             @RequestParam("placeId") String placeId,
                                                             @RequestParam("loginName") String loginName);
    /**
     * 获取账号
     * @param requestTicket 请求ticket
     * @param placeId 场所id
     * @param type 账号类型
     * @param loginName 登录名
     * @return 账号
     */
    @GetMapping(URL + "/get")
    GenericResponse<ObjDTO<PlaceAccountBO>> get(@RequestHeader(value = "request_ticket") String requestTicket,
                                                @RequestParam("placeId") String placeId,
                                                @RequestParam("type") int type,
                                                @RequestParam("loginName") String loginName);

    @GetMapping(URL + "/getCashier")
    GenericResponse<ObjDTO<PlaceAccountBO>> getCashier(@RequestHeader(value = "request_ticket") String requestTicket,
                                                @RequestParam("placeId") String placeId,
                                                @RequestParam("type") int type,
                                                @RequestParam("accountId") String accountId);

    @GetMapping(URL + "/findByTypeAndLoginName")
    GenericResponse<ObjDTO<PlaceAccountBO>> findByTypeAndLoginName(@RequestParam("type") int type,
                                                                   @RequestParam("loginName") String loginName);
    /**
     * 收银台登录接口
     * @param requestTicket 请求ticket
     * @param loginCashierBO 登录信息
     * @return 账号
     */
    @PostMapping(URL + "/loginAtCashier")
    GenericResponse<ObjDTO<PlaceAccountBO>> loginAtCashier(@RequestHeader(value = "request_ticket") String requestTicket,
                                                           @RequestBody LoginCashierBO loginCashierBO);


    @PostMapping(URL + "/resetPwd")
    GenericResponse<?> resetPassword(@RequestHeader(value = "request_ticket") String requestTicket,
                                     @RequestParam String placeId,
                                     @RequestParam String accountId);

    @PostMapping(URL + "/findPlaceAccountByChainIdAndAccountId")
    public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByChainIdAndAccountId(@RequestParam(required = false) String chainId,@RequestParam String accountId);

    @PostMapping(URL+"/findByPlaceIdAndAccountName")
    GenericResponse<ObjDTO<PlaceAccountBO>> findByPlaceIdAndAccountName(@RequestParam String placeId, @RequestParam String accountName);

    @PostMapping(URL + "/findPageByDisplayName")
    GenericResponse<PagerDTO<PlaceAccountBO>> findPageByDisplayName(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                    @RequestParam(required = false) String displayName,
                                                                    @RequestParam String mobile,
                                                                    @RequestParam(name = "page", defaultValue = "0")  int page,
                                                                    @RequestParam(name = "size", defaultValue = "10") int size);

    // 小程序解绑账号和网吧的绑定关系
    @PostMapping(URL + "/unlockAccount")
    GenericResponse<?> unlockAccount(@RequestHeader(value = "request_ticket") String requestTicket,
                                     @RequestParam String placeId,
                                     @RequestParam String mobile);


    @GetMapping(URL + "/findByMobile")
    GenericResponse<ListDTO<PlaceAccountBO>> findByMobile(@RequestHeader(value = "request_ticket")String requestTicket,
                                                          @RequestParam String mobile);


    @GetMapping(URL + "/findByMobileAndPlaceIds")
    GenericResponse<PagerDTO<PlaceAccountBO>> findByMobileAndPlaceIds(@RequestHeader(value = "request_ticket")String requestTicket,
                                                                    @RequestParam String mobile,
                                                                    @RequestParam(required = false) List<String> placeIds,
                                                                    @RequestParam(name = "page", defaultValue = "0")  int page,
                                                                    @RequestParam(name = "size", defaultValue = "10") int size);

    @GetMapping(URL + "/findByPlaceIdAndType")
    GenericResponse<ListDTO<PlaceAccountBO>> findByPlaceIdAndType(@RequestParam String placeId, @RequestParam int type);

    @GetMapping(URL + "/findByPlaceId")
    GenericResponse<ListDTO<PlaceAccountBO>> findByPlaceId(@RequestParam String placeId);

    @GetMapping(URL + "/findByAccountIds")
    GenericResponse<ListDTO<PlaceAccountBO>> findByAccountIds(@RequestParam("accountIds") List<String> accountIds);

    @PostMapping(URL + "/pageList")
    GenericResponse<PagerDTO<PlaceAccountBO>> pageList(@RequestBody Map<String,Object> params,
                                                       @RequestParam(name = "page", defaultValue = "0")  int page,
                                                       @RequestParam(name = "size", defaultValue = "10") int size);


    @PostMapping(URL + "/deleteById")
    GenericResponse<?> deleteById(@RequestParam("id") String id);
}
