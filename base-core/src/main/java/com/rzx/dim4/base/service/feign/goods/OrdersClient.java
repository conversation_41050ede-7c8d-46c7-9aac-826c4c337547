package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 订单
 * <AUTHOR>
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "OrdersClient", fallback = ShopServerServiceHystrix.class)
public interface OrdersClient {

    String URL="/shop/admin/orders";

    /**
     *  查询
     * @param queryMap
     * @return
     */
    @PostMapping(URL+"/sumOrdersTotal")
    GenericResponse<ListDTO<OrdersBO>> sumOrdersTotal(@RequestBody Map<String, String> queryMap);

    /**
     * 查询统计
     * @param placeId
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping(URL+"/queryOrdersSum")
    GenericResponse<ListDTO> queryOrdersSum(@RequestParam String placeId, @RequestParam String startTime, @RequestParam String endTime);





}
