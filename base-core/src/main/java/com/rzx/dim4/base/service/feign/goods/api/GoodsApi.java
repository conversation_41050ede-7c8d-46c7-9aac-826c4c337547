package com.rzx.dim4.base.service.feign.goods.api;

import com.rzx.dim4.base.bo.shop.GoodsBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "goodsApi", fallback = ShopServerServiceHystrix.class)
public interface GoodsApi {
    /**
     * 商品列表
     *
     * @param
     * @return
     */
    @PostMapping(value = "/shop/api/goods/getPageList")
    GenericResponse<PagerDTO<GoodsBO>> getPageList(@RequestHeader("request_ticket") String requestTicket,
                                             @RequestParam int pageSize,
                                             @RequestParam int pageStart,
                                             @RequestParam(required = false, defaultValue = "") String goodsTypeId,
                                             @RequestParam(required = false, defaultValue = "") String goodsName,
                                             @RequestParam String placeId);
}
