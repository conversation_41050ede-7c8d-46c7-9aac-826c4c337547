package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单商品
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Getter
@Setter
public class OrderGoodsMiniAppBO extends AbstractEntityBO {
    private Long id;

    private String placeId; // 场所ID
    private String orderId; // 订单ID
    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private int quantity; // 商品数量
    private int unitPrice; // 商品单价
    private int discounts; // 折扣金额

    private String goodsTypeId; // 商品类型ID
    private String goodsTypeName; // 商品类型名称


    private int present; // 是否是赠送，0否，1是
    private String goodsPic; // 商品图片
    private String mealsId; // 套餐id
    private int goodsQuota; // 虚拟商品实际充值了多少到钱包
    private int goodsPresentAmount; // 虚拟商品实际充值了多少到网费
    private String internetFeeId; // 网费虚拟商品绑定的网费充送活动ID
    private InternetFeeBO internetFeeBO;
}