package com.rzx.dim4.base.utils;

import com.google.common.io.BaseEncoding;
import com.rzx.dim4.base.bo.idauth.TokenBO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * JJWT 工具类
 * 
 * <AUTHOR>
 * @date Dec 23, 20192:27:50 PM
 */
public class JJWTUtil {

	/**
	 * 
	 * @param claims   业务信息
	 * @param issuer   签发人
	 * @param subject  主题
	 * @param password 密钥
	 * @param timeout  超时时间，单位毫秒
	 * @return Token
	 */
	public static TokenBO create(Map<String, Object> claims, String issuer, String subject, String password,
								 Long timeout) {

		if (timeout == null || timeout < (1000 * 60) || timeout > (1000 * 60 * 60 * 24 * 7)) {
			timeout = Long.valueOf(1000 * 60 * 60 * 24 * 7);
		}

		long nowMillis = System.currentTimeMillis();
		Date nowDate = new Date(nowMillis);

		long expMillis = nowMillis + timeout;
		Date expDate = new Date(expMillis);

		String jwtId = UUID.randomUUID().toString().replace("-", "").toLowerCase();

		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

		SecretKey key = generalKey(password);

		JwtBuilder builder = Jwts.builder().setClaims(claims).setId(jwtId).setIssuedAt(nowDate).setIssuer(issuer)
				.setSubject(subject).signWith(signatureAlgorithm, key).setExpiration(expDate);

		String token = builder.compact();

		return new TokenBO(token, expMillis);
	}

	/**
	 * 解密jwt
	 *
	 * @param jwt
	 * @return
	 * @throws Exception
	 */
	public static Claims parse(String password, String jwtToken) {
		SecretKey key = generalKey(password); // 签名秘钥，和生成的签名的秘钥一模一样
		Claims claims = Jwts.parser() // 得到DefaultJwtParser
				.setSigningKey(key) // 设置签名的秘钥
				.parseClaimsJws(jwtToken).getBody(); // 设置需要解析的jwt
		return claims;
	}

	/**
	 * 
	 * @param password
	 * @return
	 */
	private static SecretKey generalKey(String password) {
		byte[] encodedKey = BaseEncoding.base64().encode(password.getBytes()).getBytes();
		SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
		return key;
	}

	public static void main(String[] args) {
		String issuer = "penn.vip";
		String subject = "4dim-realname";
		Map<String, Object> claims = new HashMap<>();
		claims.put("uid", "u100001");
		claims.put("user_name", "admin");
		claims.put("nick_name", "X-rapido");
		String password = "123456";
		try {
			TokenBO jwt = JJWTUtil.create(claims, issuer, subject, password, Long.valueOf(1000 * 60 * 60));
			System.out.println("JWT(" + jwt.getToken().length() + ")：" + jwt.getToken());
			System.out.println("\n------------解密--------------\n");
			Claims c = JJWTUtil.parse(password, jwt.getToken());
			System.out.println(c.getId());
			System.out.println(c.getIssuedAt());
			System.out.println(c.getExpiration());
			System.out.println(c.getSubject());
			System.out.println(c.getIssuer());
			System.out.println(c.get("uid", String.class));
			System.out.println(c.get("user_name", String.class));
			System.out.println(c.get("nick_name", String.class));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}