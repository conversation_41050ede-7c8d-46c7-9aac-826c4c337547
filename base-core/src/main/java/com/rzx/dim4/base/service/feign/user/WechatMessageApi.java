package com.rzx.dim4.base.service.feign.user;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.service.callback.user.WechatMessageApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023年11月08日 10:06
 */
@Primary
@FeignClient(value = FeginConstant.USER_SERVER, contextId = "WechatMessageApi", fallback = WechatMessageApiHystrix.class)
public interface WechatMessageApi {


    String URL = "/feign/user/wechatMessage";

    /**
     * 发送上机通知
     */
    @GetMapping(URL + "/sendComputeruccessfully")
    void sendComputeruccessfully(@RequestParam(value = "sendType", required = false) String sendType,
                                 @RequestParam(value = "cashierType", required = false) String cashierType,
                                 @RequestParam("idNumber") String idNumber,
                                 @RequestParam("placeName") String placeId,
                                 @RequestParam("clientName") String clientName,
                                 @RequestParam("onlineTime") String onlineTime,
                                 @RequestParam("amount") int amount);

    /**
     * 发送下机通知
     */
    @GetMapping(URL + "/sendComputerCheckOut")
    void sendComputerCheckOut(@RequestParam(value = "sendType", required = false) String sendType,
                              @RequestParam(value = "cashierType", required = false) String cashierType,
                              @RequestParam("idNumber") String idNumber,
                              @RequestParam("beginTime") String beginTime,
                              @RequestParam("endTime") String endTime,
                              @RequestParam("amount") int amount,
                              @RequestParam("balance") int balance);

    /**
     * 服务完成通知
     */
    @GetMapping(URL + "/sendGiftMessage")
    void sendGiftMessage(@RequestParam("idNumber") String idNumber,
                         @RequestParam("placeId") String placeId,
                         @RequestParam("eventId") String pushId,
                         @RequestParam("dateTime") String dateTime,
                         @RequestParam("message") String message);
    /**
     * 充值成功通知
     */
    @GetMapping(URL + "/sendTopupSuccessMessage")
    void sendTopupSuccessMessage(@RequestParam("placeId") String placeId,
                                 @RequestParam("idNumber") String idNumber,
                                 @RequestParam("placeName") String placeName,
                                 @RequestParam("cardIdName") String cardIdName,
                                 @RequestParam("topupMoney") int topupMoney,
                                 @RequestParam("balanceMoney") int balanceMoney,
                                 @RequestParam("created") LocalDateTime created);
    /**
     * 优惠券核销成功通知
     */
    @GetMapping(URL + "/sendCouponVerificationSuccessMessage")
    void sendCouponVerificationSuccessMessage(@RequestParam("placeId") String placeId,
                                              @RequestParam("idNumber") String idNumber,
                                               @RequestParam("placeName") String placeName,
                                               @RequestParam("couponName") String couponName,
                                               @RequestParam("encryptedCode") String encryptedCode,
                                               @RequestParam("created") LocalDateTime created);

    /**
     * 订座成功通知
     */
    @GetMapping(URL + "/sendBookSeatsSuccessMessage")
    void sendBookSeatsSuccessMessage(@RequestParam("placeId") String placeId,
                                     @RequestParam("idNumber") String idNumber,
                                     @RequestParam("placeName") String placeName,
                                     @RequestParam("clientName")  String clientName,
                                     @RequestParam("startTime") LocalDateTime startTime,
                                     @RequestParam("endTime") LocalDateTime endTime,
                                     @RequestParam("code") String code);

    /**
     * 订座取消通知
     */
    @GetMapping(URL + "/sendBookSeatsCancelMessage")
    void sendBookSeatsCancelMessage(@RequestParam("placeId") String placeId,
                                    @RequestParam("idNumber") String idNumber,
                                    @RequestParam("placeName")  String placeName,
                                    @RequestParam("clientName") String clientName,
                                    @RequestParam("startTime")  LocalDateTime startTime,
                                    @RequestParam("endTime") LocalDateTime endTime);

    /**
     * 激活成功通知
     */
    @GetMapping(URL + "/sendActiveCardSuccessMessage")
    void sendActiveCardSuccessMessage(@RequestParam("placeId") String placeId,
                                      @RequestParam("idNumber") String idNumber,
                                      @RequestParam("placeName")  String placeName,
                                      @RequestParam("activeTime")  LocalDateTime activeTime,
                                      @RequestParam("cardIdName") String cardIdName);

    /**
     * 通知员工
     */
    @GetMapping(URL + "/sendMessageToStaff")
    void sendMessageToStaff(@RequestParam("placeId") String placeId,
                            @RequestParam("hostName") String hostName,
                            @RequestParam("message") String message);

    /**
     * 交班成功通知
     */
    @GetMapping(URL + "/sendShiftSubmitMessage")
    void sendShiftSubmitMessage(@RequestParam("placeId") String placeId,
                                @RequestParam("onDutyAccountName") String onDutyAccountName,
                                @RequestParam("successorName") String successorName,
                                @RequestParam("workingTime") String workingTime,
                                @RequestParam("shiftId") String shiftId);


    /**
     * 发送打款成功通知
     */
    @GetMapping(URL + "/sendWithdrawalAmountMessage")
    void sendWithdrawalAmountMessage(@RequestParam("placeId") String placeId,
                                     @RequestParam("withdrawalTime") String withdrawalTime,
                                     @RequestParam("totalMoney") int totalMoney);
}
