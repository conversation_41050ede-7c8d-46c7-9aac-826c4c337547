package com.rzx.dim4.base.service.callback.user;

import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023年11月08日 10:08
 */
@Slf4j
@Service
public class WechatMessageApiHystrix implements WechatMessageApi {
    @Override
    public void sendComputeruccessfully(String sendType,String cashierType,String idNumber, String placeId, String clientName, String onlineTime, int amount) {
        log.error("接口异常:::sendComputeruccessfully,sendType:(),idNumber:{},placeId:{},clientName:{},onlineTime:{},amount:{}",sendType,
                idNumber, placeId, clientName, onlineTime,amount);
    }

    @Override
    public void sendComputerCheckOut(String sendType,String cashierType,String idNumber, String beginTime, String endTime, int amount, int balance) {
        log.error("接口异常:::sendComputerCheckOut，sendType:(),idNumber:{},beginTime:{},endTime:{},amount:{},balance:{}",sendType,
                idNumber, beginTime, endTime, amount,balance);
    }

    @Override
    public void sendGiftMessage(String idNumber, String placeId, String pushId, String dateTime, String message) {
        log.error("接口异常:::sendGiftMessage，idNumber:(),placeId:{},pushId:{},dateTime:{},message:{}",idNumber,
                placeId, pushId, dateTime, message);
    }

    @Override
    public void sendTopupSuccessMessage(String placeId,String idNumber, String placeName, String cardIdName, int topupMoney, int balanceMoney, LocalDateTime created) {
        log.error("接口异常:::sendTopupSuccessMessage，placeId:{},idNumber:(),placeName:{},cardIdName:{},topupMoney:{},balanceMoney:{},created:{}",placeId, idNumber,
                placeName, cardIdName, topupMoney, balanceMoney,created);
    }

    @Override
    public void sendCouponVerificationSuccessMessage(String placeId,String idNumber, String placeName, String couponName, String encryptedCode, LocalDateTime created) {
        log.error("接口异常:::sendCouponVerificationSuccessMessage，placeId:{},idNumber:(),placeName:{},couponName:{},encryptedCode:{},created:{}",
                placeId, idNumber, placeName, couponName, encryptedCode,created);
    }

    @Override
    public void sendBookSeatsSuccessMessage(String placeId,String idNumber, String placeName, String clientName, LocalDateTime startTime, LocalDateTime endTime, String code) {
        log.error("接口异常:::sendBookSeatsSuccessMessage，placeId:{},idNumber:(),placeName:{},clientName:{},startTime:{},endTime:{},code:{}",placeId, idNumber,
                placeName, clientName, startTime, endTime,code);
    }

    @Override
    public void sendBookSeatsCancelMessage(String placeId,String idNumber, String placeName, String clientName, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("接口异常:::sendBookSeatsCancelMessage，placeId:{},idNumber:(),placeName:{},clientName:{},startTime:{},endTime:{}",placeId, idNumber,
                placeName, clientName, startTime, endTime);
    }

    @Override
    public void sendActiveCardSuccessMessage(String placeId,String idNumber, String placeName, LocalDateTime activeTime, String cardIdName) {
        log.error("接口异常:::sendActiveCardSuccessMessage，placeId:{},idNumber:(),placeName:{},activeTime:{},cardIdName:{}",placeId, idNumber,
                placeName, activeTime, cardIdName);
    }

    @Override
    public void sendMessageToStaff(String placeId, String hostName, String message) {
        log.error("接口异常:::sendMessageToStaff, placeId:{}, hostName: {}, message:{}", placeId, hostName, message);
    }

    @Override
    public void sendShiftSubmitMessage(String placeId, String onDutyAccountName, String successorName, String workingTime,String shiftId) {
        log.error("接口异常:::sendShiftSubmitMessage, placeId:{}, onDutyAccountName: {}, successorName:{}, workingTime:{}, shiftId:{}", placeId, onDutyAccountName, successorName,workingTime,shiftId);
    }

    @Override
    public void sendWithdrawalAmountMessage(String placeId, String withdrawalTime, int totalMoney) {
        log.error("接口异常:::sendWithdrawalAmountMessage, placeId:{}, withdrawalTime: {}, totalMoney:{}", placeId, withdrawalTime, totalMoney);
    }
}
