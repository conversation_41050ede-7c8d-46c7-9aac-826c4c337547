package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "regcard")
@Getter
@Setter
public class RegCardXml {

    @XmlElement(name = "regcard_cardNo")
    private String serialNumber; // 顺序号

    @XmlElement(name = "regcard_cardpass")
    private String cardNumber; // 卡号

    @XmlElement(name = "regcard_type")
    private String validDays; // 卡类型

    @XmlElement(name = "regcard_sale")
    private String price; // 单价

}
