package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "computer")
@XmlAccessorType(XmlAccessType.NONE)
public class PlaceClientXml {

    @XmlElement(name = "computer_area_name")
    private String areaName;

    @XmlElement(name = "computer_name")
    private String hostName;

}
