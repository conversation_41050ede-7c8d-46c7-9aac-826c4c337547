package com.rzx.dim4.base.enums.notify;

/**
 * 卡来源类型（中文名称，srcId）
 * 
 * <AUTHOR>
 * @date 2021年3月24日 下午5:11:59
 */
public enum DataSrc {

	ID_CARD("刷ID卡", 0), //
	MANUAL("手工输入", 1), //
	ID_CARD_SECOND("刷二代证", 2), //
	SCAN("扫描", 3), //
	FINGERPRINT("刷指纹获取的卡号", 14), //
	RZX_APP("扫描任子行APP", 15), //
	JB_APP("扫描聚宝APP", 16), //
	JW_APP("扫描九威APP", 17), //
	WX_APP("扫描万象APP", 18), //
	FACE("人脸识别", 19), //
	WECHAT_SCAN("微信扫码", 20), //
	HNSY("湖南斯雨指纹", 21), //
	FACE_MANUAL("人脸识别人工审核", 22), //
	YSW_APP("易上网APP", 23), //
	ALJC_APP("阿拉警察APP", 24), //
	YSW_APP_SACN("易上网APP扫码上机", 25), //
	ESM_APP("E实名APP", 26), //
	JLGA_APP("吉林公安APP", 27), //
	EWT_APP("e网通APP", 28), //
	GZGA_APP("贵州公安APP", 29), //
	CTID("网证CTID扫码上机", 30), //
	XJWZ("新疆网证", 31), //
	HUNAN_EID("湖南电子身份证", 32), //
	HUBEI_EID("湖北电子身份证", 33), //
	HOTEL("酒店认证", 34), //
	SCAN_4WGJ("四维管家", 35), //
	SJT("上机堂", 36), //
	DODONEW_AUTO("嘟嘟牛自动收银", 37), //
	HENAN_EID("河南电子身份证", 38), //
	ALIPAY_IOT("支付宝IOT", 39),

	;

	public static String getName(int index) {
		for (DataSrc src : DataSrc.values()) {
			if (src.getIndex() == index) {
				return src.name;
			}
		}
		return String.valueOf(index);
	}

	private DataSrc(String name, int index) {
		this.name = name;
		this.index = index;
	}

	private String name; // 中文名称
	private int index; // srcId

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

}