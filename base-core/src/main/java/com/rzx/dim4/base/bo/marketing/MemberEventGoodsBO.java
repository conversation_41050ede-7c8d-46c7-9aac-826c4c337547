package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年02月10日 16:16
 */
@Getter
@Setter
@ToString
public class MemberEventGoodsBO extends AbstractEntityBO {

    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String eventId; // 活动id
    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private int quantity; // 商品数量
    private String goodsPic; // 商品图片
    private int unitPrice; // 商品销售价，单位分
}
