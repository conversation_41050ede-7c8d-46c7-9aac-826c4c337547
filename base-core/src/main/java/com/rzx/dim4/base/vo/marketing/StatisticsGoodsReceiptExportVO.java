package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StatisticsGoodsReceiptExportVO extends AbstractBO implements Serializable {

    private static final long serialVersionUID = -5610660906423721466L;
    @ExcelProperty(value = "商品名称", index = 0)
    private String goodsName;

    @ExcelProperty(value = "入库量",index = 1)
    private int countReceipt;

    @ExcelProperty(value = " 合计金额",index = 2)
    private float sumReceiptTotal;
    /**
     * 入库均价
     */
    @ExcelProperty(value = "入库均价",index = 3)
    private float avgReceiptTotal;
}
