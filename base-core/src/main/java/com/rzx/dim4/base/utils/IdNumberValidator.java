package com.rzx.dim4.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IdNumberValidator {

	/**
	 * 根据身份证号判断是否为男性
	 */
	public static boolean isMale(String idNumber) {
		if (StringUtils.isEmpty(idNumber) || !verificate(idNumber)) {
			return false;
		}
		char genderChar = idNumber.charAt(16);
		int genderNum = Character.getNumericValue(genderChar);
		return genderNum % 2 == 1;
	}

	/**
	 * 根据身份证号获取性别
	 */
	public static String getGender(String idNumber, boolean isChinese) {
		if (StringUtils.isEmpty(idNumber) || !verificate(idNumber)) {
			return "未知";
		}
		char genderChar = idNumber.charAt(16);
		int genderNum = Character.getNumericValue(genderChar);
		return isChinese ? genderNum % 2 == 1 ? "男" : "女" : genderNum % 2 == 1 ? "Male" : "Female";
	}

	/**
	 * 身份证验证
	 * 
	 * @param idNumber
	 * @return
	 */
	public static boolean verificate(String idNumber) {
		String[] wf = { "1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2" };
		String[] checkCode = { "7", "9", "10", "5", "8", "4", "2", "1", "6", "3", "7", "9", "10", "5", "8", "4", "2" };
		String iDCardNo = "";
		try {
			// 判断号码的长度 15位或18位
			if (idNumber.length() != 15 && idNumber.length() != 18) {
				log.info("身份证号码长度应该为15位或18位");
				return false;
			}
			if (idNumber.length() == 18) {
				iDCardNo = idNumber.substring(0, 17);
			} else if (idNumber.length() == 15) {
				iDCardNo = idNumber.substring(0, 6) + "19" + idNumber.substring(6, 15);
			}
			if (isStrNum(iDCardNo) == false) {
				log.info("身份证15位号码都应为数字;18位号码除最后一位外,都应为数字");
				return false;
			}
			// 判断出生年月
			String strYear = iDCardNo.substring(6, 10);// 年份
			String strMonth = iDCardNo.substring(10, 12);// 月份
			String strDay = iDCardNo.substring(12, 14);// 月份
			if (isStrDate(strYear + "-" + strMonth + "-" + strDay) == false) {
				log.info("身份证生日无效");
				return false;
			}
			GregorianCalendar gc = new GregorianCalendar();
			SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
			if ((gc.get(Calendar.YEAR) - Integer.parseInt(strYear)) > 150
					|| (gc.getTime().getTime() - s.parse(strYear + "-" + strMonth + "-" + strDay).getTime()) < 0) {
				log.info("身份证生日不在有效范围");
				return false;
			}
			if (Integer.parseInt(strMonth) > 12 || Integer.parseInt(strMonth) == 0) {
				log.info("身份证月份无效");
				return false;
			}
			if (Integer.parseInt(strDay) > 31 || Integer.parseInt(strDay) == 0) {
				log.info("身份证日期无效");
				return false;
			}
			// 判断地区码
			HashMap<String, String> area = getAreaCode();
			if (area.get(iDCardNo.substring(0, 2)) == null) {
				log.info("身份证地区编码错误");
				return false;
			}
			// 判断最后一位
			int theLastOne = 0;
			for (int i = 0; i < 17; i++) {
				theLastOne = theLastOne
						+ Integer.parseInt(String.valueOf(iDCardNo.charAt(i))) * Integer.parseInt(checkCode[i]);
			}
			int modValue = theLastOne % 11;
			String strVerifyCode = wf[modValue];
			iDCardNo = iDCardNo + strVerifyCode;
			if (idNumber.length() == 18 && !iDCardNo.equals(idNumber)) {
				log.info("身份证无效，不是合法的身份证号码");
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 地区代码
	 * 
	 * @return Hashtable
	 */
	public static HashMap<String, String> getAreaCode() {
		HashMap<String, String> map = new HashMap<String, String>();
		map.put("11", "北京");
		map.put("12", "天津");
		map.put("13", "河北");
		map.put("14", "山西");
		map.put("15", "内蒙古");
		map.put("21", "辽宁");
		map.put("22", "吉林");
		map.put("23", "黑龙江");
		map.put("31", "上海");
		map.put("32", "江苏");
		map.put("33", "浙江");
		map.put("34", "安徽");
		map.put("35", "福建");
		map.put("36", "江西");
		map.put("37", "山东");
		map.put("41", "河南");
		map.put("42", "湖北");
		map.put("43", "湖南");
		map.put("44", "广东");
		map.put("45", "广西");
		map.put("46", "海南");
		map.put("50", "重庆");
		map.put("51", "四川");
		map.put("52", "贵州");
		map.put("53", "云南");
		map.put("54", "西藏");
		map.put("61", "陕西");
		map.put("62", "甘肃");
		map.put("63", "青海");
		map.put("64", "宁夏");
		map.put("65", "新疆");
		map.put("71", "台湾");
		map.put("81", "香港");
		map.put("82", "澳门");
		map.put("83", "台湾");
		map.put("91", "国外");
		map.put("92", "国外");
		map.put("93", "国外");
		map.put("94", "国外");
		map.put("95", "国外");
		map.put("96", "国外");
		return map;
	}

	/**
	 * 判断字符串是否为数字
	 * 
	 * @param str
	 * @return
	 */
	private static boolean isStrNum(String str) {
		Pattern pattern = Pattern.compile("[0-9]*");
		Matcher isNum = pattern.matcher(str);
		if (isNum.matches()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 判断字符串是否为日期格式
	 * 
	 * @param strDate
	 * @return
	 */
	private static boolean isStrDate(String strDate) {
		Pattern pattern = Pattern.compile(
				"^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
		Matcher m = pattern.matcher(strDate);
		if (m.matches()) {
			return true;
		} else {
			return false;
		}
	}

}