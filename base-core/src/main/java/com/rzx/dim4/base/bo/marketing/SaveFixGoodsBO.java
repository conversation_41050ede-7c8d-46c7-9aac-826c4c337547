package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商品标签表
 *
 * <AUTHOR>
 * @date 2024年12月03日 15:27
 */
@Getter
@Setter
public class SaveFixGoodsBO extends AbstractEntityBO {

    private PlaceAccountBO webLoginAccount;


    private FixGoodsUpdateBO goodsBO;

}
