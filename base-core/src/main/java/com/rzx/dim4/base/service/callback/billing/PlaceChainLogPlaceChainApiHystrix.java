package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceChainLogPlaceChainApi;
import com.rzx.dim4.base.vo.PlaceChainRoamOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PlaceChainLogPlaceChainApiHystrix implements PlaceChainLogPlaceChainApi {
    @Override
    public GenericResponse<ListDTO<PlaceChainRoamOrderVO>> findChainRoamList(List<String> loginIds) {
        log.error("findChainRoamList 接口异常, loginIds={}",loginIds);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<String> queryRoamLoginId(String loginId) {
        log.error("queryRoamLoginId 接口异常, loginId={}",loginId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

}
