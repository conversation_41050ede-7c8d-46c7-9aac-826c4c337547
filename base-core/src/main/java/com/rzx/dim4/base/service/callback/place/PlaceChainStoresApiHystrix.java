package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceChainStoresApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/9
 **/
@Slf4j
@Service
public class PlaceChainStoresApiHystrix implements PlaceChainStoresApi {


    @Override
    public GenericResponse<?> add(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("add 接口异常，PlaceChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> checkBeforeAdd(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("checkBeforeAdd 接口异常，PlaceChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> doAdd(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("doAdd 接口异常，PlaceChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveAll(String requestTicket, List<PlaceChainStoresBO> placeChainStoresBOS) {
        log.error("add 接口异常，placeChainStoresBOS={}", new Gson().toJson(placeChainStoresBOS));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceChainStoresBO>> page(String requestTicket, DataTablesRequest dtRequest) {
        log.error("page 接口异常，dtRequest={}", new Gson().toJson(dtRequest));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> edit(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("edit 接口异常，PlaceChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> exit(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("exit 接口异常，PlaceChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceChainStoresBO>> queryPlaceChainStoresByChainId(String requestTicket, String chainId) {
        log.error("queryPlaceChainStoresByChainId 接口异常，chainId={}", chainId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
