package com.rzx.dim4.base.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 只有一个string类型的bo
 * 
 * <AUTHOR>
 * @date 2019年9月24日下午5:35:11
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimpleDTO extends AbstractDTO implements Serializable {

	private static final long serialVersionUID = -4329920667164004625L;
	String result;

}
