package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 租赁-区域折扣
 * <AUTHOR>
 * @date 2025年07月07日 10:00
 */
@Getter
@Setter
public class RentAreaDiscountBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;

    private String placeId;

    @ApiModelProperty(value = "租赁区域名称")
    private String areaName;  // 区域名称

    @ApiModelProperty(value = "租赁区域ID")
    private String areaId;  // 区域Id

    @ApiModelProperty(value = "该区域的押金折扣，默认100")
    private int depositDiscount;  // 押金折扣(默认100)

    @ApiModelProperty(value = "该区域的租金折扣，默认100")
    private int rentDiscount;     // 租金折扣(默认100)

}
