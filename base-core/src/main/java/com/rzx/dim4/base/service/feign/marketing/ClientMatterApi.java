package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.marketing.ClientMatterBO;
import com.rzx.dim4.base.bo.marketing.ClientMatterQuantityBO;
import com.rzx.dim4.base.bo.marketing.OrderRefundBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.ClientMatterApiHystrix;
import com.rzx.dim4.base.vo.marketing.MarketingTipsListVO;
import com.rzx.dim4.base.vo.marketing.MarketingTipsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 事物管理api接口
 *
 * <AUTHOR> hwx
 * @since 2025/3/4 11:57
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "ClientMatterApi", path = "/feign/marketing/clientMatter", fallback = ClientMatterApiHystrix.class)
public interface ClientMatterApi {
    /**
     * 保存
     *
     * @param requestTicket
     * @param bo
     * @return
     */
    @PostMapping("/save")
    GenericResponse<ObjDTO<ClientMatterBO>> save(@RequestHeader("request_ticket") String requestTicket, @RequestBody ClientMatterBO bo);

    /**
     * 查询待办任务数量
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @GetMapping("/queryClientMatterNum")
    GenericResponse<ObjDTO<ClientMatterQuantityBO>> queryClientMatterNum(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId);

    /**
     * 查询待办任务列表
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @GetMapping("/queryClientMatter")
    GenericResponse<PagerDTO<MarketingTipsListVO>> queryClientMatter(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam int page,@RequestParam int size);



    /**
     * 获取触发任务的用户信息
     *
     * @param requestTicket
     * @param placeId
     * @param placeId
     * @return
     */
    @GetMapping("/queryMatterUser")
    GenericResponse<ListDTO<BillingCardBO>> queryMatterUser(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String idNumber);

    /**
     * 呼叫类型待办任务完成接口
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @GetMapping("/updateClientMatter")
    GenericResponse<ObjDTO<ClientMatterBO>> updateClientMatter(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String matterId);


    /**
     * 派送接口（小程序代办任务）
     *
     * @param requestTicket
     * @param status
     * @param goodsId
     * @param placeId
     * @param orderId
     * @return
     */
    @PostMapping("/deliveryOrder")
    GenericResponse<ObjDTO<OrdersBO>> modifyOrderStatus(
            @RequestHeader("request_ticket") String requestTicket,
            @RequestParam int status,
            @RequestParam(required = false, name = "goodsId") String goodsId,
            @RequestParam String placeId,
            @RequestParam String orderId,
            @RequestParam String accountId);

    /**
     * 全部退单(小程序代办任务)
     * @param requestTicket
     * @param refundBO
     * @return
     */
    @PostMapping("/refundOrder")
    GenericResponse<?> refundOrder(@RequestHeader("request_ticket") String requestTicket, @RequestBody OrderRefundBO refundBO);


    @GetMapping("/bigScreenLogin")
    GenericResponse<?> bigScreenLogin(@RequestParam("placeId") String placeId,
                                      @RequestParam("accountId") String accountId,
                                      @RequestParam("accountName") String accountName,
                                      @RequestParam("code") String code);



    @PostMapping("/clientMatterExpiredRemind")
    GenericResponse<?> clientMatterExpiredRemind();


    /**
     * 处理留言
     *
     * @param placeId
     * @return
     */
    @PostMapping("/handleLeaveWordData")
    GenericResponse<?> handleLeaveWordData(@RequestParam String placeId, @RequestParam String clientId);

    /**
     * 派送接口（小程序商超订单）
     *
     * @param requestTicket
     * @param status
     * @param goodsId
     * @param placeId
     * @param orderId
     * @return
     */
    @PostMapping("/deliveryOrderForOrder")
    GenericResponse<ObjDTO<OrdersBO>> deliveryOrderForOrder(
            @RequestHeader("request_ticket") String requestTicket,
            @RequestParam int status,
            @RequestParam(required = false, name = "goodsId") String goodsId,
            @RequestParam String placeId,
            @RequestParam String orderId,
            @RequestParam String accountId);

    /**
     * 全部退单(小程序商超订单)
     * @param requestTicket
     * @param refundBO
     * @return
     */
    @PostMapping("/refundOrderForOrder")
    GenericResponse<?> refundOrderForOrder(@RequestHeader("request_ticket") String requestTicket, @RequestBody OrderRefundBO refundBO);
}
