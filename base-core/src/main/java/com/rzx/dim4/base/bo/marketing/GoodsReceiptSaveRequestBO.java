package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 新增入库记录请求对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ApiModel("新增入库记录请求对象")
public class GoodsReceiptSaveRequestBO extends AbstractEntityBO {
    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "仓库货架ID,从100000开始递增,默认主仓库为000000")
    private String storageRackId;

    @ApiModelProperty(value = "供应商ID", required = true)
    private String supplierId;

    @ApiModelProperty(value = "商品类型：0商品，1原料（默认商品0）", required = true)
    private int type;

    @ApiModelProperty(value = "支付方式：0现金，1支付宝，2微信，3挂账，4上打下，5其他", required = true)
    private int payType;

    @ApiModelProperty(value = "应付（单位分）", required = true)
    private int money;

    @ApiModelProperty(value = "实付（单位分）", required = true)
    private int paidMoney;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID", hidden = true)
    private Long accountId;

    @ApiModelProperty(value = "创建人姓名", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "入库单商品列表", required = true)
    private List<GoodsReceiptListSaveRequestBO> goodsReceiptList;
}
