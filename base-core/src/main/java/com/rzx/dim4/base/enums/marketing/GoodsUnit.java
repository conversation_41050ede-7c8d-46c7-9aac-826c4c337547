package com.rzx.dim4.base.enums.marketing;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024年12月05日 17:46
 */
@Getter
public enum GoodsUnit {


    ONE(0,"无"),
    PIECE(1,"个"),
    BAG(2,"袋"),
    PACK(3,"包"),
    BOX(4,"盒"),
    STRIP(5,"条"),
    SET(6,"台"),
    PIE(7,"只"),
    ITEM(8,"件"),
    SHEET(9,"张"),
    ROLL(10,"卷"),
    TIN(11,"听"),
    BOTTLE(12,"瓶"),
    CAN(13,"罐"),
    CUP(14,"杯"),
    BUCKET(15,"桶"),
    PORTION(16,"份"),
    GRAM(17,"克"),
    KILOGRAM(18,"千克"),
    MILL<PERSON>ITER(19,"毫升"),
    LITER(20,"升"),
    KETTLE(21,"壶"),
    <PERSON><PERSON><PERSON>(22,"元"),
    <PERSON><PERSON><PERSON>(23,"角"),
    <PERSON><PERSON>(24,"分"),
    POIN<PERSON>(25,"点"),
    JIN(26,"斤");
    ;

    private final int value;

    private final String name;

    GoodsUnit(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public double getValue() {
        return value;
    }

    public static String getNameByValue(int value) {
        for (GoodsUnit unit : GoodsUnit.values()) {
            if (unit.value == value) {
                return unit.name;
            }
        }
        return null;
    }
}
