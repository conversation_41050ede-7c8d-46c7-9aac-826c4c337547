package com.rzx.dim4.base.utils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;


/**
 * <AUTHOR>
 * @dateTime 2021-4-21 16:42:24
 */
public class SignUtils {
    
    private static String KSort(Map<String, Object> map) {
        StringBuffer sb = new StringBuffer();
        map.entrySet().stream().sorted(Map.Entry.comparingByKey())
        .forEachOrdered(x -> {
        	if(x.getKey().equals("sign") || x.getValue() instanceof String[] || x.getValue() instanceof List) {
        		return;
        	}
        	if(StringUtils.isEmpty(x.getValue())) {
        		return;
        	}
        	sb.append(x.getKey() + "=" + x.getValue() + "&");
        });
        return sb.toString().substring(0, sb.length() - 1);
    }

    public static String generateSign4common(Map<String, Object> paramMap) {
    	try {
    		String saltKey = "6aessdd9wdc9c374ttwe2bwwa54afe1c";
    		String src = KSort(paramMap) + "&" + saltKey;
            String sign = DigestUtils.md5DigestAsHex(src.getBytes());
			return sign;
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
		return null;
    }
    
    public static boolean checkSign(Map<String, Object> paramMap) {
    	String srcSign = (String) paramMap.get("sign");
    	String timestamp = (String) paramMap.get("timestamp");
    	if(StringUtils.isEmpty(timestamp)) {
    		return false;
    	}
    	// 签名有效期默认3分钟;
    	long intervalSeconds = Math.abs(System.currentTimeMillis()/1000 - Long.parseLong(timestamp));
    	if(intervalSeconds > 60*3) {
    		return false;
    	}
    	
    	if(StringUtils.isEmpty(srcSign)) {
    		return false;
    	}
    	String destSign = generateSign4common(paramMap);
    	if(!destSign.equals(srcSign)) {
    		return false;
    	}
    	return true;
    }
    
    public static String generateSign4w(Map<String, Object> paramMap, String saltKey) {
    	try {
    		String src = KSort(paramMap) + "&" + saltKey;
            String sign = DigestUtils.md5DigestAsHex(src.getBytes());
			return sign;
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
		return null;
    }
    
    public static void main(String[] das) throws Exception {
        Map<String,Object> paramMap=new HashMap<String, Object>();
        paramMap.put("thirdAccountId", "100161");
        paramMap.put("placeId", "**************");
        paramMap.put("idNumber", "421023198712287557");
        paramMap.put("cashierId", new String[]{"********","46411"});
        paramMap.put("idName", "胡敏");
        paramMap.put("timestamp", System.currentTimeMillis()/1000 +"");
        paramMap.put("sign", generateSign4common(paramMap));

		System.out.println(paramMap);
		
		System.out.println(generateSign4common(paramMap));
    }

	/**
	 * 对数据生成签名，排除掉sign和空字段，进行自然排序，然后拼接成key=value&key=value的形式
	 * @param data 签名参数
	 * @return 签名
	 */
	public static String createSign(Map<String, Object> data) {
		return data.entrySet().stream()
				.filter(e -> !ObjectUtils.isEmpty(e.getValue()) && !"sign".equals(e.getKey())) // 排除掉sign和空字段
				.sorted(Map.Entry.comparingByKey(Comparator.naturalOrder())) // 自然排序
				.map(e -> e.getKey() + "=" + e.getValue())
				.collect(Collectors.joining("&"));
	}

}
