package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.dto.PagerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 报损单
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
@ApiModel(description = "小程序,商品盘点的实体")
public class MiniAppGoodsStocktakingBO extends AbstractEntityBO
{
    @ApiModelProperty(value = "盘点损溢合计")
    private int lossOrOverFlowTotal;

}
