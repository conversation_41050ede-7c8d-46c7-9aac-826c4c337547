package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.util.List;

/**
 * 新增固装商品信息实体
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel(description = "新增固装商品信息实体")
public class GoodsRealAddRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "创建者ID", hidden = true)
    private Long accountId;

    @ApiModelProperty(value = "创建者", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "条形码列表")
    private List<String> barcodes;

    @ApiModelProperty(value = "商品名称")
    @Length(message = "商品名称不能超过50字符!", max = 50)
    private String goodsName;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "卡类型ID",required = true)
    @NotEmpty(message = "卡类型不能为空")
    @Length(message = "卡类型不能超过90字符！", max = 90)
    private String cardTypeIds;

    @ApiModelProperty(value = "区域ID")
    @Length(message = "区域类型不能超过200字符!", max = 200)
    private String areaIds;

    @ApiModelProperty(value = "商品限购类型，0每日、1每周、2每月、3永久")
    @Max(value = 3, message = "商品限购类型参数错误")
    @Min(value = 0, message = "商品限购类型参数错误")
    private int extType;

    @ApiModelProperty(value = "商品限购数量，默认0不限制")
    @Min(value = 0, message = "商品限购数量参数错误")
    private int extCount;

    @ApiModelProperty(value = "预警值")
    @Min(value = 0, message = "预警值参数错误")
    private int stockAlarm;

    @ApiModelProperty(value = "是否计算库存，0-是，1-否")
    @Max(value = 1, message = "是否计算库存参数错误")
    @Min(value = 0, message = "是否计算库存参数错误")
    private int isCalculateInventory;

    @ApiModelProperty(value = "商品排序")
    private int sort;

    @ApiModelProperty(value = "销量")
    private int initSaleNum;

    @ApiModelProperty(value = "是否允许在线支付，0是，1否")
    @Max(value = 1, message = "是否允许在线支付参数错误")
    @Min(value = 0, message = "是否允许在线支付参数错误")
    private int onlinePaySwitch;

    @ApiModelProperty(value = "销售状态，0售卖中，1停售")
    @Max(value = 1, message = "销售状态参数错误")
    @Min(value = 0, message = "销售状态参数错误")
    private int sellStatus;

    @ApiModelProperty(value = "是否移动端展示，0展示，1不展示")
    @Max(value = 1, message = "是否移动端展示参数错误")
    @Min(value = 0, message = "是否移动端展示参数错误")
    private int showMobileSwitch;

    @ApiModelProperty(value = "是否收银台展示，0展示，1不展示")
    @Max(value = 1, message = "是否收银台展示参数错误")
    @Min(value = 0, message = "是否收银台展示参数错误")
    private int showCashierSwitch;

    @ApiModelProperty(value = "是否客户端展示，0展示，1不展示")
    @Max(value = 1, message = "是否计算库存参数错误")
    @Min(value = 0, message = "是否计算库存参数错误")
    private int showClientSwitch;

    @ApiModelProperty(value = "售卖周期类型，0每日、1每周、2每月、3指定时间段")
    @Max(value = 3, message = "售卖周期类型参数错误")
    @Min(value = 0, message = "售卖周期类型参数错误")
    private int cycle;

    @ApiModelProperty(value = "每月售卖日期，默认 0,1,2,3,4,...31")
    private String days;

    @ApiModelProperty(value = "每日开始售卖时间点1，默认0")
    @Max(value = 24, message = "每日开始售卖时间点1参数错误")
    @Min(value = 0, message = "每日开始售卖时间点1参数错误")
    private float startTime1;

    @ApiModelProperty(value = "每日结束售卖时间段1，默认24")
    @Max(value = 24, message = "每日结束售卖时间段1参数错误")
    @Min(value = 0, message = "每日结束售卖时间段1参数错误")
    private float endTime1;

    @ApiModelProperty(value = "每日开始售卖时间点2")
    private float startTime2;

    @ApiModelProperty(value = "每日结束售卖时间段2")
    private float endTime2;

    @ApiModelProperty(value = "每日开始售卖时间点3")
    private float startTime3;

    @ApiModelProperty(value = "每日结束售卖时间段3")
    private float endTime3;

    @ApiModelProperty(value = "每周售卖日期，默认 1,2,3,4,5,6,7")
    private String weeks;

    @ApiModelProperty(value = "开始日期（指定时间段）")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（指定时间段）")
    private LocalDate endDate;

    @ApiModelProperty(value = "商品销售价，单位分")
    @Digits(integer = 7, fraction = 0, message = "商品销售价参数错误")
    @Min(value = 0, message = "商品销售价参数错误")
    private int unitPrice;

    @ApiModelProperty(value = "商品成本价，单位分")
    @Digits(integer = 7, fraction = 0, message = "商品成本价参数错误")
    @Min(value = 0, message = "商品成本价参数错误")
    private int costPrice;

    @ApiModelProperty(value = "商品网费价，单位分")
    @Digits(integer = 7, fraction = 0, message = "商品网费价参数错误")
    @Min(value = 0, message = "商品网费价参数错误")
    private int networkPrice;

    @ApiModelProperty(value = "支持奖励余额购买，0是，1否，默认1")
    @Max(value = 1, message = "是否支持奖励余额购买参数错误")
    @Min(value = 0, message = "是否支持奖励余额购买参数错误")
    private int supportPresentSwitch;

    @ApiModelProperty(value = "支持本金购买，0是，1否，默认0")
    @Max(value = 1, message = "是否支持本金购买参数错误")
    @Min(value = 0, message = "是否支持本金购买参数错误")
    private int supportCashSwitch;

    @ApiModelProperty(value = "副标题")
    @Length(message = "副标题不能超过50字符！", max = 50)
    private String subheading;

    @ApiModelProperty(value = "单位，需要给出码表")
    private int unit;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品图片Md5")
    private String goodsPicMd5;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商名字", hidden = true)
    private String supplierName;

    @ApiModelProperty(value = "口味，多个标签用,隔开")
    @Length(message = "口味字段过长！", max = 50)
    private String specs;

    @ApiModelProperty(value = "标签ID，用,分割")
    @Length(message = "标签类型过多", max = 50)
    private String tagIds;

    @ApiModelProperty(value = "商品额度")
    private int goodsQuota;

    @ApiModelProperty(value = "是否参加排行榜，0是，1否")
    @Max(value = 1, message = "是否参加排行榜参数错误")
    @Min(value = 0, message = "是否参加排行榜参数错误")
    private int showRank;

    @ApiModelProperty(value = "优惠券ID")
    private String couponId;
}