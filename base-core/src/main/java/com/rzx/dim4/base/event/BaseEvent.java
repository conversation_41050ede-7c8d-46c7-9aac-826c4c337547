package com.rzx.dim4.base.event;

import org.springframework.context.ApplicationEvent;

/**
 * 基础事件
 * <AUTHOR> hwx
 * @since 2025/3/3 18:02
 */
public abstract class BaseEvent extends ApplicationEvent {
    private static final long serialVersionUID = 4319180183358290606L;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public BaseEvent(Object source) {
        super(source);
    }
}
