package com.rzx.dim4.base.service.callback;

import com.qiniu.util.Json;
import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.billing.third.ThirdAuthorityBO;
import com.rzx.dim4.base.bo.billing.third.ThirdPlaceConfigBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.BillingServerService;
import com.rzx.dim4.base.service.feign.billing.LogLoginApi;
import com.rzx.dim4.base.service.feign.billing.LogOperationApi;
import com.rzx.dim4.base.service.feign.billing.LogRoomApi;
import com.rzx.dim4.base.service.feign.billing.param.LogLoginQuery;
import com.rzx.dim4.base.service.feign.billing.param.LogOperationParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BillingServerServiceHystrix implements BillingServerService, LogOperationApi, LogLoginApi, LogRoomApi {

    @Override
    public GenericResponse<ObjDTO<LogQrCodeBO>> findQrCodeByToken(String token) {
        log.error("接口异常:::findQrCodeByToken(token:{})", token);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveLogQrCode(String requestTicket, LogQrCodeBO logQrCodeBO) {
        log.error("接口异常:::saveLogQrCode(requestTicket:{},cardTypeBO:{})", requestTicket, logQrCodeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> newLogQrCode(String requestTicket, LogQrCodeBO logQrCodeBO) {
        log.error("接口异常:::newLogQrCode(requestTicket:{},cardTypeBO:{})", requestTicket, logQrCodeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> clearHistoryQrcode(String requestTicket, String placeId,int qrcodeRefreshTime, int qrcodeRefreshTimeCashier) {
        log.error("接口异常:::clearHistoryQrcode(requestTicket:{},qrcodeRefreshTime:{},qrcodeRefreshTimeCashier:{},placeId:{})", qrcodeRefreshTime, qrcodeRefreshTimeCashier,placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardTypeBO>> createBillingCardType(String requestTicket,
                                                                            BillingCardTypeBO cardTypeBO) {
        log.error("接口异常:::saveCardType(requestTicket:{},cardTypeBO:{})", requestTicket, cardTypeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> batchCreateBillingCard(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody List<BillingCardBO> billingCardBOList) {
        log.error("接口异常:::batchCreateCard(requestTicket:{},billingCardBOList:{})", requestTicket, billingCardBOList);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveAllTempWanxiangUsers(String requestTicket, List<TempWanxiangUserBO> TempWanxiangUserBOs) {
        log.error("接口异常:::saveAllTempWanxiangUsers(requestTicket:{},TempWanxiangUserBOs:{})", requestTicket, TempWanxiangUserBOs);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardTypeBO>> findCardTypeByPlaceId(String placeId) {
        log.error("接口异常:::findCardTypeByPlaceId(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteCardType(String requestTicket, Long id) {
        log.error("接口异常:::deleteTopupRule(requestTicket:{},id:{})", requestTicket, id);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardTypeBO>> findCardTypeByPlaceIdAndCardTypeId(String placeId,
                                                                                         String cardTypeId) {
        log.error("接口异常:::findCardTypeByPlaceIdAndCardTypeId(placeId:{}, cardTypeId:{})", placeId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> findCardTypeByPoints(BillingCardBO billingCard) {
        log.error("接口异常:::findCardTypeByPoints(placeId:{}, points:{}, cardTypeId:{})", billingCard.getPlaceId(), billingCard.getPoints(),billingCard.getCardTypeId());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findBillingCardInfos(String paramJson) {
        log.error("接口异常:::findBillingCardInfos(paramJson:{})", paramJson);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardBO>> billingCards(String placeId, String cardTypeId, String idNumber,String idName,int size, int page,
                                                                  String[] orderColumns, String order) {
        log.error("接口异常:::billingCards(placeId:{},cardTypeId{}, idNumber:{},size:{},page:{},orderColumns:{},order:{})",
                placeId, cardTypeId, idNumber, size, page, orderColumns, order);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardBO>> chainBillingCards(String placeId, String chainCardTypeId, String idNumber, String chainId,
                                                                      String idName, int size, int page, String[] orderColumns, String order,String createCardPlaceId) {
        log.error("接口异常:::billingCards(placeId:{},chainCardTypeId{}, idNumber:{},size:{},page:{},orderColumns:{},order:{}chainId:{}idName:{})",
                placeId, chainCardTypeId, idNumber, size, page, orderColumns, order, chainId, idName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> billingCardActivation(String requestTicket,
                                                                        String placeId,
                                                                        String idNumber,
                                                                        ActiveType activeType, String phoneNumber) {
        log.error("接口异常:::billingCardActivation(requestTicket:{},placeId:{},idNumber:{},activeType:{},phoneNumber:{})", requestTicket, placeId,
                idNumber, activeType,phoneNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> queryCommonRuleS(String placeId, String areaId,
                                                                         String cardTypeId) {
        log.error("接口异常:::queryCommonRuleS(placeId:::{}, areaId:::{}, cardTypeId:::{}", placeId, areaId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> queryCommonRuleByPlaceIdAndRuleId(String placeId, String ruleId) {
        log.error("接口异常:::queryCommonRuleByPlaceIdAndRuleId(placeId:::{}, ruleId:::{}, ", placeId, ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> createBillingRuleCommons(String requestTicket,
                                                                                 BillingRuleCommonBO billingRuleCommonBO) {
        log.error("接口异常:::createBillingRuleCommons(requestTicket:::{}, billingRuleCommonBO:::{}", requestTicket,
                billingRuleCommonBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingRuleCommonBO>> batchCreate(String requestTicket,
                                                                     List<BillingRuleCommonBO> billingRuleCommonBOS) {
        log.error("接口异常:::batchCreate(requestTicket:::{}, billingRuleCommonBOS:::{}", requestTicket,
                billingRuleCommonBOS);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteBillingRuleCommon(String requestTicket, String placeId, String cardTypeId,
                                                              String areaId) {
        log.error("接口异常:::deleteBillingRuleCommon(requestTicket:::{}, placeId:::{}, cardTypeId:::{}, areaId:::{}",
                requestTicket, placeId, cardTypeId, areaId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> editBillingRuleCommonConsume(String requestTicket, String placeId, String ruleId, int minConsume, int unitConsume, int deductionTime, int freeMinutes) {
        log.error("接口异常:::editBillingRuleCommonConsume(requestTicket:::{}, placeId:::{}, ruleId:::{}, minConsume:::{}, unitConsume:::{}, deductionTime:::{}, freeMinutes:::{}",
                requestTicket, placeId, ruleId, minConsume, unitConsume, deductionTime, freeMinutes);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceProfileBO>> queryMinPriceAndMaxPriceByPlaceIds(List<String> placeIds) {
        log.error("接口异常:::queryMinPriceAndMaxPriceByPlaceIds(placeIds:::{})",placeIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingRulePackageTimeBO>> queryPageBillingRulePackageTime(String placeId, int size,
                                                                                               int page, String[] orderColumns, String order,
                                                                                               String areaId,
                                                                                               String cardTypeId,
                                                                                               SourceType sourceType,
                                                                                               String packageFlag,
                                                                                               String ruleName) {
        log.error("接口异常:::queryPageBillingRulePackageTime(placeId:::{}, size:::{}, page:::{}, orderColumns:::{}, order:::{}, areaId:::{}, cardTypeId:::{}, sourceType:::{}, packageFlag:::{}, ruleName:::{}",
                placeId, size, page, orderColumns, order, areaId, cardTypeId,sourceType,packageFlag,ruleName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime(String placeId,
                                                                                         String ruleId) {
        log.error("接口异常:::queryBillingRulePackageTime(placeId:::{}, ruleId:::{}", placeId, ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> createBillingRulePackageTime(String requestTicket,
                                                                                          BillingRulePackageTimeBO billingRulePackageTimeBO) {
        log.error("接口异常:::createBillingRulePackageTime(requestTicket:::{}, billingRulePackageTimeBO:::{}",
                requestTicket, billingRulePackageTimeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteBillingRulePackageTime(String requestTicket, String placeId,
                                                                   String ruleId) {
        log.error("接口异常:::deleteBillingRulePackageTime(requestTicket:::{}, placeId:::{}, ruleId:::{}", requestTicket,
                placeId, ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> createTopupRule(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestBody TopupRuleBO topupRuleBo) {
        log.error("接口异常:::add(requestTicket:{},topupRuleBo:{})", requestTicket, topupRuleBo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<TopupRuleBO>> deleteTopupRule(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody TopupRuleBO topupRuleBo) {
        log.error("接口异常:::deleteTopupRule(requestTicket:{},topupRuleBo:{})", requestTicket, topupRuleBo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRuleByPlaceId(@RequestParam String placeId) {
        log.error("接口异常:::queryTopupRuleByPlaceId(placeId:::{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

//    @Override
//    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRuleByPlaceId(@RequestParam String placeId,
//                                                                         @RequestParam(name = "cardTypeId", required = false) String cardTypeId, @RequestParam int topupType) {
//        log.error("接口异常:::queryTopupRuleByPlaceId(placeId:::{}", placeId);
//        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
//    }

    @Override
    public GenericResponse<ObjDTO<TopupRuleBO>> queryTopupRuleByPlaceIdAndTopupRuleId(String placeId,
                                                                                      String topupRuleId) {
        log.error("接口异常:::queryByPlaceIdAndTopupRuleId(placeId:::{}, topupRuleId:::{}", placeId, topupRuleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogTopupBO>> queryLogTopups(Map<String, Object> queryMap, int size, int page) {
        log.error("接口异常:::queryLogTopups(queryMap:::{}, size:::{}, page:::{}", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> querySumCashTopupAndPresentTopup(Map<String, Object> queryMap) {
        log.error("接口异常:::querySumCashTopupAndPresentTopup(queryMap:::{}", queryMap);
        return null;
    }

    @Override
    public GenericResponse<PagerDTO<LogRefundBO>> queryLogRefunds(Map<String, Object> queryMap, int size, int page) {
        log.error("接口异常:::queryLogRefunds(queryMap:::{}, size:::{}, page:::{}", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> querySumCashRefundAndOnlineRefund(Map<String, Object> queryMap) {
        log.error("接口异常:::querySumCashRefundAndOnlineRefund(queryMap:::{}", queryMap);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<PaymentRefundOrderBO>> refund(String requestTicket, String placeId, String orderId,
                                                                String operatorName, String sourceType) {
        log.error("接口异常:::refund(requestTicket:::{},placeId:::{},orderId:::{},operatorName:::{},sourceType:::{}",
                requestTicket, placeId, orderId, operatorName, sourceType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> reissue(String requestTicket, String placeId, String orderId) {
        log.error("接口异常:::reissue(requestTicket:::{},placeId:::{},orderId:::{}", requestTicket, placeId, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdPlaceConfigBO>> findThirdPlaceConfigByPlaceId(String placeId) {
        log.error("接口异常:::getThirdAccount(findThirdPlaceConfigByPlaceId:::{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ThirdAccountBO>> accounts(Map<String, String> queryMap, int page, int size,
                                                              String order, String[] orderColumns) {
        log.error("接口异常:::accounts(queryMap:::{}, page:::{}, size:::{}, order:::{}, orderColumns:::{}", queryMap, page,
                size, order, orderColumns);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdAccountBO>> getThirdAccount(String thirdAccountId) {
        log.error("接口异常:::getThirdAccount(thirdAccountId:::{}", thirdAccountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdAccountBO>> saveThirdAccount(String requestTicket,
                                                                    ThirdAccountBO thirdAccountBO) {
        log.error("接口异常:::saveThirdAccount(requestTicket:::{},thirdAccountBO:::{}", requestTicket, thirdAccountBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdAuthorityBO>> findThirdAuthorityByThirdAccountId(String thirdAccountId) {
        log.error("接口异常:::findThirdAuthorityByThirdAccountId(thirdAccountId:::{}", thirdAccountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdAuthorityBO>> saveThirdAuthority(String requestTicket,
                                                                        ThirdAuthorityBO thirdAuthorityBO) {
        log.error("接口异常:::saveThirdAuthority(requestTicket:::{},thirdAuthorityBO:::{}", requestTicket,
                thirdAuthorityBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> queryConsumeBalance(String idNumber) {
        log.error("接口异常:::queryConsumeBalance(idNumber:{})", idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> cardDetails(String placeId, String idNumber) {
        log.error("接口异常:::cardDetails(placeId:::{}, idNumber:::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRules(String placeId, String cardTypeId) {
        log.error("接口异常:::queryTopupRules(placeId:{}, cardTypeId:{})", placeId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRulesNew(String placeId, String cardTypeId) {
        log.error("接口异常:::queryTopupRulesNew(placeId:{}, cardTypeId:{})", placeId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<TopupRuleBO>> queryTopupGift(String placeId, String idNumber, int amount,String cardTypeId) {
        log.error("接口异常:::queryTopupGift(placeId:{}, idNumber:{}, amount:{}, cardTypeId:{})", placeId, idNumber,amount,cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingOnlineBO>> clientLogin(String requestTicket, String placeId, String clientId,
                                                                String areaId, String idNumber,String unlockCode) {
        log.error("接口异常:::clientLogin(requestTicket:::{}, placeId:::{},clientId:::{},areaId:::{},idNumber:::{},unlockCode:::{})",
                requestTicket, placeId, clientId, areaId, idNumber,unlockCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> queryFinishTop5(String placeId, String idNumber) {
        log.error("接口异常:::queryFinishTop5( placeId:::{},idNumber:::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> queryOnlineClientInfos(
            @RequestBody ListDTO<BillingOnlineBO> billingOnlineBOListDTO) {
        log.error("接口异常:::queryOnlineClientInfos( placeClientBOListDTO:::{})", billingOnlineBOListDTO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingOnlineBO>> queryBillingOnlineByIdNumber(String placeId,String idNumber) {
        log.error("接口异常:::queryBillingOnlineByIdNumber(placeId:::{},idNumber:::{})", placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> queryLateBillingOnlines(String placeId, List<String> cardIds) {
        log.error("接口异常:::queryLateBillingOnlines(placeId:::{},cardIds:::{})", placeId, cardIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingOnlineBO>> queryChainLateBillingOnlines(List<String> placeIds, String idNumber) {
        log.error("接口异常:::queryChainLateBillingOnlines(placeIds:::{},idNumber:::{})", placeIds, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingOnlineBO>> findUnfinishedByPlaceIdAndClientId(String placeId,
                                                                                       String clientId) {
        log.error("接口异常:::findUnfinishedByPlaceIdAndClientId(placeId:::{},clientId:::{})", placeId, clientId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingOnlineBO>> findUnfinishedByPlaceIdAndClientIds(String placeId, List<String> clientIds) {
        log.error("接口异常:::findUnfinishedByPlaceIdAndClientIds(placeId:::{},clientIds:::{})", placeId, clientIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBO>> logout(String requestTicket, String placeId, String clientId,
                                                      String cardId, String operationAccountId, String operationAccountName,
                                                      SourceType sourcesType) {
        log.error(
                "接口异常:::logout(requestTicket:::{},placeId:::{},clientId:::{},cardId:::{},operationAccountId:::{},operationAccountName:::{},sourcesType:::{})",
                requestTicket, placeId, clientId, cardId, operationAccountId, operationAccountName, sourcesType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ClientVersionBO>> queryClientVersion(String versionNumber, String clientType,
                                                                         int length, int start, String[] orderColumns, String order) {
        log.error(
                "接口异常:::queryClientVersion(versionNumber:{}, clientType:{}, length:{}, start:{}, orderColumns:{}, order:{})",
                versionNumber, clientType, length, start, orderColumns, order);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> checkVersionNumber(String versionNumber, int clientType) {
        log.error("接口异常:::save(versionNumber:::{},clientType:::{})", versionNumber, clientType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientVersionBO>> checkFileMD5(int clientType, String fileMD5) {
        log.error("接口异常:::save(clientType:::{},fileMD5:::{})", clientType, fileMD5);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> save(String requestTicket, ClientVersionBO clientVersionBO) {
        log.error("接口异常:::save(requestTicket:::{},clientVersionBO:::{})", requestTicket, clientVersionBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientVersionBO>> findByVersionId(String VersionId) {
        log.error("接口异常:::findByVersionId(VersionId:::{})", VersionId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ClientUpgradeBO>> queryVersionUpgrade(String versionNumber, String placeId,
                                                                          String clientType, int length, int start, String[] orderColumns, String order) {
        log.error(
                "接口异常:::queryVersionUpgrade(versionNumber:{}, placeId:{}, clientType:{}, length:{}, start:{}, orderColumns:{}, order:{})",
                versionNumber, placeId, clientType, length, start, orderColumns, order);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<ClientVersionBO>> findClientVersions(String clientType) {
        log.error("接口异常:::findClientVersions(clientType:::{})", clientType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientUpgradeBO>> findClientUpgradeByPlaceId(String clientType, String placeId) {
        log.error("接口异常:::findClientUpgradeByPlaceId(clientType:::{},placeId:::{})", clientType, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<ClientUpgradeBO>> findByClientTypeAndDeletedAndPlaceIdIn(int clientType,
                                                                                            List<String> placeIds) {
        log.error("接口异常:::findByClientTypeAndDeletedAndPlaceIdIn(clientType:::{},placeIds:::{})", clientType, placeIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> clientUpgradeSave(String requestTicket, ClientUpgradeBO clientUpgradeBO) {
        log.error("接口异常:::clientUpgradeSave(requestTicket:::{},clientUpgradeBO:::{})", requestTicket, clientUpgradeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<ClientUpgradeBO>> findClientUpgrades(String clientType) {
        log.error("接口异常:::findClientUpgrades(clientType:::{})", clientType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<ClientUpgradeBO>> findByClientTypeAndVersionIdOrderById(int clientType,
                                                                                           String versionId) {
        log.error("接口异常:::findByClientTypeAndVersionIdOrderById(clientType:::{},versionId:::{})", clientType,
                versionId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> batchUpgradeSave(String requestTicket, List<ClientUpgradeBO> clientUpgradeBOs) {
        log.error("接口异常:::batchUpgradeSave(requestTicket:::{},clientUpgradeBOs:::{})", requestTicket, clientUpgradeBOs);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

	@Override
	public GenericResponse<ObjDTO<PaymentResultBO>> createOrderPayByAppletOfWeChat(String requestTicket, String placeId, String idNumber, int amount, String openId, String appId) {
		log.error("createOrderPayByAppletOfWeChat 接口异常:::参数(requestTicket:::{}, placeId:::{}, idNumber:::{}, amount:::{}, openId:::{}, appId:::{})", requestTicket,
				placeId, idNumber, amount, openId, appId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

    @Override
    public GenericResponse<SimpleDTO> weChatTopupOrderCreate(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String idNumber, @RequestParam int amount) {
        log.error("接口异常:::create(request_ticket:::{}, placeId:::{}, idNumber:::{}, amount:::{})", requestTicket,
                placeId, idNumber, amount);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentOrderBO>> queryWechatTopupOrder(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String orderId) {
        log.error("接口异常:::create(request_ticket:::{}, orderId:::{})", requestTicket, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogTopupBO>> queryWeChatTopupOrders(@RequestBody Map<String, Object> queryMap,
                                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                                        @RequestParam(name = "page", defaultValue = "1") int page) {
        log.error("接口异常:::queryWeChatTopupOrders(queryMap:::{}, size:::{}, page:::{})", queryMap.toString(), size,
                page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> weChatCreateBillingCard(String requestTicket,
                                                                          String idNumber,
                                                                          String name,
                                                                          String placeId,
                                                                          String activeType,
                                                                          String cardTypeId, String phoneNumber) {
        log.error("接口异常:::weChatCreateBillingCard(idNumber:::{}, name:::{}, placeId:::{}, cardTypeId:::{})", idNumber, name, placeId,cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogOperationBO>> findLogOperation(Map<String, Object> queryMap, int size, int page,
                                                                      String[] orderColumns, String order) {
        log.error("接口异常:::findLogOperation)");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> querySumCostAndPresent(Map<String, Object> queryMap) {
        log.error("接口异常:::querySumCostAndPresent");
        return null;
    }

    @Override
    public List<BillingOnlineBO> findUnfinishedByPlaceId(String placeId) {
        log.error("接口异常:::findUnfinishedByPlaceId(placeId:::{}))", placeId);
        return new ArrayList<>();
    }

    @Override
    public GenericResponse<PagerDTO<LogLoginBO>> findLogLogin(String placeId, String idNumber, String clientName,
                                                              String startDate, String endDate, int size, int page, String[] orderColumns, String order) {
        log.error("接口异常:::findLogLogin");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<String> queryPlaceIdsByCountDay(String startTime, String endTime) {
        log.error("接口异常:::queryPlaceIdsByCountDay");
        return null;
    }

    @Override
    public GenericResponse<SimpleDTO> savePlaceBizConfig(String requestTicket, PlaceBizConfigBO placeBizConfigBO) {
        log.error("接口异常:::savePlaceBizConfig(placeBizConfigBO:::{}, requestTicket:::{})", placeBizConfigBO,
                requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceBizConfigBO>> queryPlaceBizConfig(String placeId) {
        log.error("接口异常:::queryPlaceBizConfig(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardIds(String placeId, List<String> cardIds) {
        log.error("接口异常:::findByPlaceIdAndCardIds(palceId:::{}, cardIds:::{}))", placeId, cardIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> findByPlaceIdAndCardId(String placeId, String cardId) {
        log.error("接口异常:::findByPlaceIdAndCardId(placeId:::{}, cardId:::{}))", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateCardType(String requestTicket, String placeId, String cardTypeId,
                                                     String cardIds) {
        log.error("接口异常:::updateCardType(requestTicket:::{}, palceId:::{}, cardIds:::{}))", requestTicket, placeId,
                cardIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> chainUpdateCardType(String requestTicket, String chainCardTypeId, List<String> idNumbers, String chainId) {
        log.error("接口异常:::updateCardType(requestTicket:::{}, chainCardTypeId:::{}, idNumbers:::{}, chainId:::{}))", requestTicket, chainCardTypeId,
                idNumbers, chainId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> verifyUpdateCardTypeParam(String placeId, String cardTypeId, String cardId) {
        log.error("接口异常:::verifyUpdateCardTypeParam placeId:::{}, cardTypeId:::{}, cardId:::{}))", placeId, cardTypeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> findBillingCard(String placeId, String idNumber) {
        log.error("接口异常:::findBillingCard(palceId:::{}, idNumber:::{}))", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> findBillingCardAllType(String placeId, String idNumber) {
        log.error("接口异常:::findBillingCardAllType(palceId:::{}, idNumber:::{}))", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardBlackListBO>> blackLists(Map<String, String> queryMap, int page,
                                                                        int size, String order, String[] orderColumns) {
        log.error("接口异常:::blackLists(queryMap:::{}, page:::{}, size:::{}, order:::{}, orderColumns:::{}", queryMap,
                page, size, order, orderColumns);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBlackListBO>> saveBillingCardBlackList(String requestTicket,
                                                                                    BillingCardBlackListBO billingCardBlackListBO) {
        log.error("接口异常:::saveBillingCardBlackList(requestTicket:::{}, billingCardBlackListBO:::{}))", requestTicket,
                billingCardBlackListBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteBillingCardBlackList(String requestTicket, String placeId, String cardId) {
        log.error("接口异常:::deleteBillingCardBlackList(requestTicket:::{}, placeId:::{}, cardId:::{}))", requestTicket,
                placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<CashierTaskBO>> createCashierTask(String requestTicket, CashierTaskBO cashierTaskBO) {
        log.error("接口异常:::createCashierTask(requestTicket:::{},cashierTaskBO:::{})", requestTicket, cashierTaskBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<CashierTaskBO>> listCashierTask(int start, int length, String[] orderColumns,
                                                                    String order, String search) {
        log.error("接口异常:::listCashierTask");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<CashierAuthorityBO>> createCashierAuthority(String requestTicket,
                                                                              CashierAuthorityBO cashierAuthorityBO) {
        log.error("接口异常:::createCashierTask(requestTicket:::{},cashierTaskBO:::{})", requestTicket, cashierAuthorityBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<CashierAuthorityBO>> findCashierAuthorityByPlaceIdAndAccountId(String placeId,
                                                                                                 String accountId) {
        log.error("接口异常:::findCashierAuthorityByPlaceIdAndAccountId(placeId:::{}, accountId:::{})", placeId, accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<ServiceIndexes> findNeedServiceIndexes() {
        log.error("接口异常:::findNeedServiceIndexes()");
        return null;
    }

    @Override
    public GenericResponse<SimpleDTO> saveLogShift(String placeId, String shiftId) {
        log.error("接口异常:::saveLogShift(placeId:::{}, shiftId:::{})", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatisticsOperationByDayBO>> incomeTotal(int size, int page, List<String> placeIds,
                                                                             String startTime, String endTime) {
        log.error("接口异常:::incomeTotal(size:::{}, page:::{}, placeIds:::{}, startTime:::{}, endTime:::{})", size, page,
                placeIds, startTime, endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatisticsOperationByDayBO>> incomeTotalByMonth(int size, int page, List<String> placeIds,
                                                                             String startTime, String endTime) {
        log.error("接口异常:::incomeTotalByMonth(size:::{}, page:::{}, placeIds:::{}, startTime:::{}, endTime:::{})", size, page,
                placeIds, startTime, endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> sumStatisticsOperationByDay(List<String> placeIds,
                                                                                              String startTime, String endTime) {
        log.error("接口异常:::sumStatisticsOperationByDay(placeIds:::{}, startTime:::{}, endTime:::{})", placeIds, startTime,
                endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StatisticsOperationByDayBO>> sumTotalIncome(List<String> placeIds, String startTime, String endTime, int flag) {
        log.error("接口异常:::sumTotalIncome(placeIds:::{}, startTime:::{}, endTime:::{})", placeIds, startTime,
                endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StatisticsOperationByDayBO>> sumTotalIncomeRank(List<String> placeIds, int flag) {
        log.error("接口异常:::sumTotalIncomeRank(placeIds:::{}, flag:::{})", placeIds, flag);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, String> getActiveTypes() {
        log.error("接口异常:::getActiveTypes()");
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<SurchargeConfigBO>> querySurchargeConfigByPlaceId(String placeId) {
        log.error("接口异常:::querySurchargeConfigByPlaceId(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveSurchargeConfig(String requestTicket, SurchargeConfigBO surchargeConfigBO) {
        log.error("接口异常:::saveSurchargeConfig(requestTicket:::{},surchargeConfigBO:::{})", requestTicket,
                surchargeConfigBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogOperationBO>> findLogOperationByReversalAndDateTime(String placeId,
                                                                                          String startDateTime, String endDateTime) {
        log.error("接口异常:::findLogOperationByReversalAndDateTime");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogOperationBO>> findHistoryPlacePage(String requestTicket, String idNumber) {
        log.error("接口异常:::findHistoryPlacePage idNumber:{}",idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<Map<String, String>> findConsumerRanking(String placeId, String startDateTime, String endDateTime, String type) {
        log.error("接口异常:::findConsumerRanking placeId:{},startDateTime:{},endDateTime:{},type:{},",placeId,startDateTime,endDateTime,type);
        return new ArrayList<>();
    }

    @Override
    public GenericResponse<ListDTO<StatisticsOnlineByHourBO>> queryStatisticsOnline(String placeId, String countDay) {
        log.error("接口异常:::queryStatisticsOnline");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> queryTemporaryCard(String placeId) {
        log.error("接口异常:::queryTemporaryCard(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StatisticsOnlineCardBO>> statisticsOnlineCard(String placeId) {
        log.error("接口异常:::statisticsOnlineCard(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> batchLogout(String placeId, String clientIds, String cardIds, String loginName,
                                          String loginPass, String sourceType) {
        log.error(
                "接口异常:::batchLogout(placeId:::{},clientIds:::{},cardIds:::{},loginName:::{},loginPass:::{},sourceType:::{})",
                placeId, clientIds, cardIds, loginName, loginPass, sourceType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogLoginBO>> queryPageLogout(String placeId, String startLogoutTime,
                                                                 String endLogoutTime, int logoutType,  String loginIds,int size, int page, String[] orderColumns, String order) {
        log.error(
                "接口异常:::queryPageLogLogins(placeId:::{},startLogoutTime:::{},endLogoutTime:::{},logoutType:::{},loginIds:::{},size:::{},page:::{},orderColumns:::{},order:::{})",
                placeId, startLogoutTime, endLogoutTime, logoutType, loginIds,size, page, orderColumns, order);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateExpired(Map<String, Object> map) {
        log.error("接口异常:::updateExpired(map:::{})", map);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RewardPointsRuleBO>> queryRewardPointsRule(String placeId) {
        log.error(
                "接口异常:::queryRewardPointsRule(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RewardPointsRuleBO>> editRewardPoints(String requestTicket, RewardPointsRuleBO rewardPointsRuleBO) {
        log.error(
                "接口异常:::editRewardPoints(requestTicket:::{},rewardPointsRuleBO:::{})",
                requestTicket, rewardPointsRuleBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ExchangePointsRuleBO>> queryPageExchangePointsRule(String placeId, int size, int page, String[] orderColumns, String order) {
        log.error(
                "接口异常:::queryPageExchangePointsRule(placeId:::{},size:::{},page:::{},orderColumns:::{},order:::{})",
                placeId, size, page, orderColumns, order);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ExchangePointsRuleBO>> editExchangePoints(String requestTicket, ExchangePointsRuleBO exchangePointsRuleBO) {
        log.error(
                "接口异常:::editRewardPoints(requestTicket:::{},exchangePointsRuleBO:::{})", requestTicket, exchangePointsRuleBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteExchangePoints(String requestTicket, String placeId, String exchangePointsRuleId) {
        log.error(
                "接口异常:::deleteExchangePoints(requestTicket:::{},placeId:::{},exchangePointsRuleId:::{})",
                requestTicket, placeId, exchangePointsRuleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogPointsBO>> initExchangePointsTable(int size, int page, String[] orderColumns, String order, String placeId, String exchangePointsType, String idNumber, String cardTypeId, String createrName, String idName, String startDate, String endDate) {
        log.error(
                "接口异常:::initExchangePointsTable(size:::{},page:::{},orderColumns:::{},order:::{},placeId:::{},exchangePointsType:::{},idNumber:::{},cardTypeId:::{},createrName:::{},idName:::{},startDate:::{},endDate:::{})",
                size, page, orderColumns, order, placeId, exchangePointsType, idNumber, cardTypeId, createrName, idName, startDate, endDate);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<SecurityRequestConfigBO>> queryInterfaceRequestNumberLimit(int type) {
        log.error("接口异常:::queryInterfaceRequestNumberLimit(type:::{}))", type);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<SecurityRequestConfigBO>> queryInterfaceRequestNumberByPlaceId(String placeId) {
        log.error("接口异常:::queryInterfaceRequestNumberByPlaceId(placeId:::{}))", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> activeCard4Dim(@RequestHeader("request_ticket") String requestTicket,
                                             @RequestParam String placeId,
                                             @RequestParam String idNumber,
                                             @RequestParam(required = false,defaultValue = "") String phoneNumber) {
        log.error("接口异常:::activeCard4Dim(placeId:::{}))", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> statisticsShiftByPlaceIdAndShiftId(String placeId, String shiftId) {
        log.error("接口异常:::statisticsShiftByPlaceIdAndShiftId(palceId:::{}, shiftId:::{}))", placeId, shiftId);
        return null;
    }

    @Override
    public Map<String, Integer> statisticsDashboardByPlaceId(List<String> placeId) {
        log.error("接口异常:::statisticsDashboardByPlaceId(palceId:::{}))", placeId);
        return null;
    }

    @Override
    public GenericResponse<PagerDTO<StatisticSumTopupBO>> sumTopupByPlaceIdGroupByCardId(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam String placeId,
            @RequestParam String start, @RequestParam String end, @RequestParam String desc) {
        log.error("接口异常:::sumTopupByPlaceIdGroupByCardId");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> statisticsTopupAndConsumptionByCardId(String placeId, String cardId, String startTime,
                                                                      String endTime) {
        log.error(
                "接口异常:::statisticsTopupAndConsumptionByCardId(palceId:::{}, cardId:::{}, startTime:::{}, endTime:::{}))",
                placeId, cardId, startTime, endTime);
        return null;
    }

    @Override
    public GenericResponse<PagerDTO<StatisticSumConsumptionBO>> sumConsumptionByPlaceIdGroupByIdNumber(int size,
                                                                                                       int page, String placeId, String start, String end, String orderColumn) {
        log.error("接口异常:::sumConsumptionByPlaceIdGroupByIdNumber");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatisticsByDayBO>> queryDashboardStatistics(int size, int page, String startDate,
                                                                                 String endDate, String placeId) {
        log.error("接口异常:::queryDashboardStatistics(size:::{}, page:::{}, startDate:::{}, endDate:::{}, placeId:::{})",
                size, page, startDate, endDate, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


    @Override
    public GenericResponse<PagerDTO<StatisticsByDayBO>> queryMonthDashboardStatistics(int size, int page, String startDate,
                                                                                 String endDate, String placeId) {
        log.error("接口异常:::queryMonthDashboardStatistics(size:::{}, page:::{}, startDate:::{}, endDate:::{}, placeId:::{})",
                size, page, startDate, endDate, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> queryAgentDashboardStatistics(List<String> placeArray) {
        log.error("接口异常:::queryAgentDashboardStatistics(placeArray:::{})", placeArray);
        return null;
    }

    @Override
    public List<Map<String, String>> querySumOnlineNum(String startDate, String endDate, List<String> placeIds) {
        log.error("接口异常:::querySumOnlineNum(startDate:::{},endDate:::{},placeArray:::{})", startDate, endDate,
                placeIds);
        return null;
    }

    @Override
    public List<Map<String, String>> queryTotalConsumptionByPlaceIds(String startDate, String endDate, List<String> placeIds) {
        log.error("接口异常:::queryTotalConsumptionByPlaceIds(startDate:::{},endDate:::{},placeArray:::{})", startDate, endDate,
                placeIds);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<StatisticsByDayBO>> sumDashboardStatistics(String startDate, String endDate, List<String> placeIds) {
        log.error("接口异常:::sumDashboardStatistics(startDate:::{},endDate:::{},placeIds:::{})", startDate, endDate,
                placeIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> statisticsShiftAllCostByPlaceIdAndShiftId(String placeId, String shiftId) {
        log.error("接口异常:::statisticsShiftAllCostByPlaceIdAndShiftId(placeId:::{}, shiftId:::{}))", placeId, shiftId);
        return new HashMap<>();
    }

    @Override
    public GenericResponse<ListDTO<StatisticsByDayBO>> statisticsEveryDaySumCost(String startDate, String endDate, List<String> placeIds) {
        log.error("接口异常:::statisticsEveryDaySumCost(placeIds:::{}, startDate:::{}, endDate:::{}))", placeIds, startDate, endDate);
        return null;
    }

    @Override
    public Map<String, Integer> incomeToDayDetails(String placeId,String startTime,String endTime) {
        log.error("接口异常:::statisticsEveryDaySumCost(placeId:::{},startTime:::{},endTime:::{}))", placeId,startTime,endTime);
        return new HashMap<>();
    }

    @Override
    public Map<String, Integer> queryOtherIncome(String placeId, String startTime, String endTime) {
        log.error("接口异常:::queryOtherIncome(placeId:::{},startTime:::{},endTime:::{}))", placeId,startTime,endTime);
        return new HashMap<>();
    }

    @Override
    public Map<String, Integer> queryOtherIncomeByShift(String placeId, String shiftId) {
        log.error("接口异常:::queryOtherIncomeByShift(placeId:::{},shiftId:::{}))", placeId,shiftId);
        return new HashMap<>();
    }

    @Override
    public List<Map<String, Object>>  incomeToDayDetailList(String placeId,String startTime,String endTime) {
        log.error("接口异常:::incomeToDayDetailList(placeId:::{},startTime:::{},endTime:::{}))", placeId,startTime,endTime);
        return new ArrayList<>();
    }

    @Override
    public Map<String, Integer> incomeByShiftDetails(String placeId, String shiftId) {
        log.error("接口异常:::incomeByShiftDetails(placeId:::{},:::{}))", placeId,shiftId);
        return new HashMap<>();
    }

    @Override
    public GenericResponse<PagerDTO<ClientWallpaperBO>> queryClientWallpaper(int length, int start) {
        log.error("接口异常:::queryClientWallpaper");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<ClientWallpaperDeliverBO>> queryClientWallpaperDeliver(String search, int length,
                                                                                           int start) {
        log.error("接口异常:::queryClientWallpaperDeliver");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveClientWallpaper(String requestTicket, ClientWallpaperBO clientWallpaperBO) {
        log.error("接口异常:::saveClientWallpaper");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveClientWallpaperDeliver(String requestTicket,
                                                                 @RequestBody ClientWallpaperDeliverBO clientWallpaperDeliverBO) {
        log.error("接口异常:::saveClientWallpaperDeliverList");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveDefaultClientWallpaperDeliver(String requestTicket,
                                                                        @RequestBody ClientWallpaperDeliverBO clientWallpaperDeliverBO) {
        log.error("接口异常:::saveSystemClientWallpaper");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<CashierMessageNotifyBO>> queryPageCashierMessageNotify(int length, int start) {
        log.error("接口异常:::queryPageCashierMessageNotify");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveCashierMessage(String requestTicket, CashierMessageNotifyBO cashierMessageNotifyBO) {
        log.error("接口异常:::saveCashierMessage");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<CashierMessageNotifyBO>> queryOneCashierMessage(String notifyId) {
        log.error("接口异常:::queryOneCashierMessage");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveCashierMessageDeliver(String requestTicket, CashierMessageNotifyDeliverBO cashierMessageNotifyDeliverBO) {
        log.error("接口异常:::saveCashierMessageDeliver");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<CashierMessageNotifyDeliverBO>> queryPageCashierMessageDeliver(String search, int length, int start) {
        log.error("接口异常:::queryPageCashierMessageDeliver");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientWallpaperBO>> queryOneClientWallpaper(int id) {
        log.error("接口异常:::queryOneClientWallpaper");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingRuleAccBO>> queryPageBillingRuleAcc(String placeId, int size, int page,
                                                                               String[] orderColumns, String order) {
        log.error("接口异常:::queryPageBillingRuleAcc");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleAccBO>> queryBillingRuleAcc(String placeId, String ruleId) {
        log.error("接口异常:::queryBillingRuleAcc");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingRuleAccBO>> createBillingRuleAcc(String requestTicket,
                                                                          BillingRuleAccBO billingRuleAccBO) {
        log.error("接口异常:::createBillingRuleAcc");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteBillingRuleAcc(String requestTicket, String placeId, String ruleId) {
        log.error("接口异常:::deleteBillingRuleAcc");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> billingCardUpdateAccount(String requestTicket, String placeId, String cardId, int cashAccount,
                                                       int presentAccount, int updateFlag, SourceType sourceType, String cardTypeId,String orderId) {
        log.error("接口异常:::billingCardUpdateAccount");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> refundForInternetFeePackageTimeOrder(String requestTicket, BillingCardBalanceUpdateRequestBO paramsBo) {
        log.error("接口异常:::refundForInternetFeePackageTimeOrder:::Params={}", Json.encode(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateBillingCardAccountForRefund(String requestTicket, BillingCardBalanceUpdateRequestBO paramsBo) {
        log.error("接口异常:::updateBillingCardAccountForRefund:::Params={}", Json.encode(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogOperationBO>> queryLogOperationCheckoutPage(LogOperationParam logOperationParam, Pageable pageable) {
        log.error("接口异常:::queryLogOperationCheckoutPage:{}", Json.encode(logOperationParam));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogLoginBO>> queryLogLoginPage(Map<String, Object> map, int size, int page,
                                                                   String[] orderColumns, String order) {
        log.error("接口异常:::queryLogLoginPage(size:::{}, page:::{}, orderColumns:::{}, order:::{}, map:::{})",
                size, page, orderColumns, order, map);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogLoginBO>> queryLogLogin(LogLoginQuery logLoginParam) {
        log.error("接口异常:::queryLogLogin");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogLoginBO>> findByPlaceIdAndLoginIdIn(String placeId, List<String> loginIds) {
        log.error("接口异常:::findByPlaceIdAndLoginIdIn");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> checkoutLogout(String requestTicket, String placeId, List<String> cardIds) {
        log.error("接口异常:::checkoutLogout:{}:cardIds:{}", placeId, Json.encode(cardIds));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceId(String requestTicket, String placeId, String idNumber) {
        log.error("接口异常:::findLastLoginByIdNumberAndPlaceId 参数:requestTicket:{},placeId:{},idNumber:{}", requestTicket, placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNull(String placeId, String idNumber) {
        log.error("接口异常:::findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNull 参数:placeId:{},idNumber:{}", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNotNull(List<String> placeIds, String idNumber) {
        log.error("接口异常:::findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNotNull 参数:placeIds:{},idNumber:{}", placeIds, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<Map<String, String>> queryEverydayClientOnlineTime(String placeId, int dayAgo) {
        log.error("接口异常:::queryEverydayClientOnlineTime 参数:placeId:{},dayAgo:{}", placeId, dayAgo);
        return new ArrayList<>();
    }

    @Override
    public List<BillingCardBO> queryLastLoginUserByDayAgo(String placeId, int dayAgo,String cardTypeIds) {
        log.error("接口异常:::queryLastLoginUserByDayAgo 参数:placeId:{},dayAgo:{},cardTypeIds:{}", placeId, dayAgo,cardTypeIds);
        return new ArrayList<>();
    }

    @Override
    public GenericResponse<ListDTO<LogLoginBO>> queryLastLogOutTime(String placeId, List<String> cardIds) {
        log.error("接口异常:::queryLastLogOutTime 参数:placeId:{},cardIds:{}", placeId, cardIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBO>> queryLastLoginRecord(String idNumber) {
        log.error("接口异常:::queryLastLoginRecord 参数:idNumber:{}", idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Integer sumOnlineTimeByPlaceAndIdNumber(String placeId, String idNumber, LocalDateTime startDate, LocalDateTime endDate) {
        log.error("接口异常:::sumOnlineTimeByPlaceAndIdNumber 参数:placeId：{},idNumber:{}", placeId,idNumber);
        return 0;
    }

    @Override
    public GenericResponse<ObjDTO<LogRoomBO>> findLogRoomByPlaceIdAndCardId(String placeId, String cardId) {
        log.error("接口异常:::findLogRoomByPlaceIdAndCardId 参数:placeId:{},cardId:{}", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}

