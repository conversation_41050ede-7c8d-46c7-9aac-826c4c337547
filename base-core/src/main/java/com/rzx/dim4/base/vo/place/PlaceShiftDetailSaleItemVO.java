package com.rzx.dim4.base.vo.place;

import com.rzx.dim4.base.bo.place.PlaceShiftSaleBO;
import com.rzx.dim4.base.enums.place.PlaceShiftSaleStatisticType;
import lombok.Data;

import java.util.List;

/** 交班统计销售数据
 * <AUTHOR> hwx
 * @since 2025/2/24 16:23
 */
@Data
public class PlaceShiftDetailSaleItemVO {
    private PlaceShiftSaleStatisticType statisticType;
    /**
     * 合计金额
     */
    private int totalAmount;
    /**
     * 销售数据
     */
    private List<PlaceShiftSaleBO> saleBos;

    private String goodsTypeId;
    private String goodsTypeName;
}
