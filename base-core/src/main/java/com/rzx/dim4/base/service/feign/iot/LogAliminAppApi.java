package com.rzx.dim4.base.service.feign.iot;

import com.rzx.dim4.base.bo.iot.LogAliminAppBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.iot.LogAliminAppApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023年12月15日 18:30
 */
@Primary
@FeignClient(value = FeginConstant.IOT_SERVER, contextId = "IotAliminAppApi", fallback = LogAliminAppApiHystrix.class)
public interface LogAliminAppApi {

    String URL = "/feign/iot/aliminApp";

    /**
     * 查询
     *
     * @return
     */
    @GetMapping(URL + "/queryStatisticsAliminApp")
    GenericResponse<ListDTO<LogAliminAppBO>> queryStatisticsAliminApp(@RequestParam(name = "queryType") String queryType,      //查询时间条件 day 、month 、year
                                                               @RequestParam(name = "startTime") String startTime,      //开始时间
                                                               @RequestParam(name = "endTime") String endTime,         //结束时间
                                                               @RequestParam(required = false,name = "placeType") String placeType, //行业
                                                               @RequestParam(required = false,name = "provinceCode") String provinceCode, //地区
                                                               @RequestParam(required = false,name = "cityCode") String cityCode); //地区
}
