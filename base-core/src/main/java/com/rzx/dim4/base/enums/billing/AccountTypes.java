package com.rzx.dim4.base.enums.billing;

import lombok.Getter;

/**
 * 账户类型
 */
@Getter
public enum AccountTypes {

    INTERNET_REWARD(0, "0", "网费本金"),
    INTERNET_PRINCIPAL(1, "1", "网费奖励"),
    ;

    /**
     * 操作类型，保存到数据库
     */
    private final int code;
    /**
     * 操作类型，方便作为查询条件放到Map中
     */
    private final String type;
    /**
     * 操作类型描述
     */
    private final String desc;

    AccountTypes(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
}