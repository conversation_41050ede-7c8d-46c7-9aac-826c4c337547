package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.rzx.dim4.base.excel.LocalDateTimeConverter;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@HeadRowHeight(value = 20)//设置表头行高
@ColumnWidth(value = 15)//设置表头行宽
public class StatisticsLossProfitExportVO implements Serializable {

    private static final long serialVersionUID = 8214584587245507439L;
    @ExcelProperty(value = "损益类型", index = 0)
    private String profitLossType;

    @ExcelProperty(value = "对应单据号", index = 1)
    private String id;

    @ExcelProperty(value = "品种",index = 2)
    private String typeName;

    @ColumnWidth(value = 20)
    @ExcelProperty(value = "数量", index = 3)
    private int num;

    @ColumnWidth(value = 20)
    @ExcelProperty(value = "损益金额", index = 4)
    private float profitLossPrice;

    @ColumnWidth(value = 20)
    @ExcelProperty(value = "发生时间", index = 5,converter = LocalDateTimeConverter.class)
    private LocalDateTime created;

}
