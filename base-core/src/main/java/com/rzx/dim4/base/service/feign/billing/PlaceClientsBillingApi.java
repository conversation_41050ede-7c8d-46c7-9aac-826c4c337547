package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.ExchangeComputerRequestBO;
import com.rzx.dim4.base.bo.billing.GetOffComputerRequestBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceClientsBillingApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "PlaceClientsBillingApi", fallback = PlaceClientsBillingApiHystrix.class)
public interface PlaceClientsBillingApi {

    String URL = "/feign/billing/clients";

    @PostMapping(URL + "/exchangeComputer")
    GenericResponse<SimpleDTO> exchangeComputer(@RequestBody ExchangeComputerRequestBO params);

    @PostMapping(URL + "/getOffComputer")
    GenericResponse<SimpleDTO> getOffComputer(@RequestBody GetOffComputerRequestBO paramsBo);
}
