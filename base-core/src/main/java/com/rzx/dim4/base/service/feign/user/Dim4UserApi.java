package com.rzx.dim4.base.service.feign.user;

import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.user.Dim4UserApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/7/9
 **/
@Primary
@FeignClient(value = FeginConstant.USER_SERVER, contextId = "dim4UserApi", fallback = Dim4UserApiHystrix.class)
public interface Dim4UserApi {


    String URL = "/feign/user/dim4User";

    /**
     * 清除过期的认证图片数据
     *
     * @param days
     * @return
     */
    @GetMapping(URL + "/deleteExpireAuthImagesInfo")
    GenericResponse<?> deleteExpireAuthImagesInfo(@RequestParam("days") int days);

    /**
     * 获取用户微信实名认证图片信息
     *
     * @param idNumber
     * @return {@link Dim4UserBO}
     */
    @GetMapping(URL + "/getInfo")
    GenericResponse<ObjDTO<Dim4UserBO>> getInfo(@RequestParam("idNumber") String idNumber);

    @GetMapping(URL + "/getDim4UserInfoList")
    GenericResponse<ListDTO<Dim4UserBO>> getDim4UserInfoList(@RequestParam("idNumbers") List<String> idNumbers);

    /**
     * 保存iot实名认证信息
     *
     * @param requestTicket
     * @param name
     * @param idNumber
     * @param authImageKey
     * @param authImageMd5
     * @return
     * @throws Exception
     */
    @PostMapping(URL + "/saveUser4Iot")
    GenericResponse<ObjDTO<Dim4UserBO>> saveUser4Iot(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @RequestParam("name") String name,
                                                     @RequestParam("idNumber") String idNumber,
                                                     @RequestParam("authImageKey") String authImageKey,
                                                     @RequestParam("authImageMd5") String authImageMd5);

}
