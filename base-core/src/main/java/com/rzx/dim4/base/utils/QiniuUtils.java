package com.rzx.dim4.base.utils;

import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.processing.OperationManager;
import com.qiniu.processing.OperationStatus;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.qiniu.util.StringUtils;
import com.qiniu.util.UrlSafeBase64;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 七牛工具类
 *
 * <AUTHOR>
 * @date 2019-09-12 11:39:32
 */
public class QiniuUtils {

	public static final String ASSETS_DOMAIN_HTTPS = "https://4wgj.topfreeweb.net/";// 七牛域名HTTPS格式
	public static final String ASSETS_DOMAIN = "http://assets.topfreeweb.net/";// 七牛域名
	private static final String ACCESS_KEY = "yPdpmIMljcZdX9nLSrXSt8_6ibjN7MliGPc_Pzw6";
	private static final String SECRET_KEY = "ho6MJdn-OS7LdCBF4OAtFoHEwtT5JWPd3mDFzQX1";
	private static final String BUCKET_NAME = "4dim";

	/**
	 * 获得Auth
	 * 
	 * @return
	 */
	private static Auth getAuth() {
		return Auth.create(ACCESS_KEY, SECRET_KEY);
	}

	/**
	 * 获取上传的TOKEN
	 * 
	 * @return
	 */
	public static String getUpToken() {
		return getAuth().uploadToken(BUCKET_NAME);
	}

	/**
	 * 上传文件到七牛
	 * 
	 * @param file 文件
	 * @param key  上传路径
	 * @return true 成功，false 失败
	 * @throws QiniuException
	 */
	public static boolean upload(byte[] file, String key) throws Exception {
		Configuration cfg = new Configuration(Region.region2());
		UploadManager uploadManager = new UploadManager(cfg);
		Response response = uploadManager.put(file, key, getUpToken());
		if (response.isOK()) {
			return true;
		}
		throw new Exception("文件上传失败:::" + response.getInfo());
	}

	/**
	 * 获取七牛对象的URL
	 * 
	 * @param key
	 * @return
	 */
	public static String getDownloadUrl(String key) {
		return ASSETS_DOMAIN_HTTPS + key;
	}

	/**
	 * 获取七牛对象存储的KEY：将本地上传的图片的使用uuid随机重命名，再加上固定的头像前缀返回
	 * 
	 * @param localAvatarName 本地头像图片的名称
	 * @return key 七牛对象存储的KEY
	 */
	public static String getAvatarKeyForQiniu(String localAvatarName) throws Exception {
		if (org.springframework.util.StringUtils.isEmpty(localAvatarName) || localAvatarName.split("\\.").length < 2) {
			throw new Exception("文件名不符合格式");
		}
		String[] splits = localAvatarName.split("\\.");
		String suffix = splits[splits.length - 1];
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "account/avatar/" + uuid + "." + suffix;
		return avatarKey;
	}

	/**
	 * @param photoBase64Header 图片base64编码的header
	 * @return key 七牛对象存储的KEY
	 */
	public static String getFacePhotoKeyForQiniu(String photoBase64Header) throws Exception {
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "auth/idcard/" + uuid + "." + photoBase64Header.split(";")[0].substring(11);
		return avatarKey;
	}

	/**
	 *
	 *
	 * @return key 七牛对象存储的KEY
	 */
	public static String getFileKeyForQiniu(String suffix) throws Exception {
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "client/version/download/" + uuid + "." + suffix;
		return avatarKey;
	}

	public static String getWallpaperKeyForQiniu(String suffix) throws Exception {
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "client/wallpaper/" + uuid + "." + suffix;
		return avatarKey;
	}

	/**
	 * 代理商充值打款凭证
	 * @param suffix
	 * @return
	 * @throws Exception
	 */
	public static String getAgentVoucherKeyForQiniu(String suffix) throws Exception {
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "agent/voucher/" + uuid + "." + suffix;
		return avatarKey;
	}

	/**
	 * 商超--商品图片
	 * @param suffix
	 * @return
	 * @throws Exception
	 */
	public static String getGoodsPicKeyForQiniu(String suffix) throws Exception {
		String uuid = UUID.randomUUID().toString();
		String avatarKey = "shop/goodsPic/" + uuid + "." + suffix;
		return avatarKey;
	}

	/**
	 * 龙管家数据转换--原zip文件
	 * @param suffix
	 * @return
	 * @throws Exception
	 */
	public static String getLgjDataKeyForQiniu(String suffix,String placeId) throws Exception {
		String time = LocalDateTime.now().toString().substring(0,19);
		String avatarKey = "lgjData/multipartFile/" + placeId + "_" + time + "." + suffix;
		return avatarKey;
	}

	/**
	 * place-server意见反馈图片
	 * @param suffix
	 * @return
	 * @throws Exception
	 */
	public static String getPlaceServerQuestionForQiniu(String suffix) throws Exception{
		String uuid = UUID.randomUUID().toString().trim().replaceAll("-","");
		String questionPicKey = "place/questionPic/" + uuid + "." + suffix;
		return questionPicKey;
	}

	public static String getLocalModeDataKeyForQiniu(String suffix,String placeId) throws Exception {
		String time = LocalDateTime.now().toString().substring(0,19);
		String avatarKey = "localMode/" + placeId + "_" + time + "." + suffix;
		return avatarKey;
	}

	public static boolean deleteFile( String fileKey){
		Configuration cfg = new Configuration(Region.region0());
		Auth auth = getAuth();
		BucketManager bucketManager = new BucketManager(auth, cfg);
		try {
			bucketManager.delete(BUCKET_NAME, fileKey);
			return true;
		} catch (QiniuException ex) {
			ex.printStackTrace();
		}
		return false;
	}


	public static String getCompUpToken(int index) {
		Auth auth =  getAuth();
		StringMap putPolicy = new StringMap();
		//数据处理指令，支持多个指令
		String saveMp4Entry = String.format("%s:weixin_public/video_record/"+UUID.randomUUID()+".mp4", BUCKET_NAME);
		String avthumbMp4Fop = String.format("avthumb/mp4/vcodec/libx264/s/320x240/autoscale/1/an/1/compressLevel/1.5|saveas/%s", UrlSafeBase64.encodeToString(saveMp4Entry));
//		String saveMp4Entry = String.format("weixin_public/video_record/"+UUID.randomUUID().toString(), BUCKET_NAME);
//		String avthumbMp4Fop = String.format("avthumb/mp4|saveas/%s", UrlSafeBase64.encodeToString(saveMp4Entry));
		//将多个数据处理指令拼接起来
		String persistentOpfs = StringUtils.join(new String[]{
				avthumbMp4Fop
		}, ";");
		putPolicy.put("persistentOps", persistentOpfs);
		//数据处理队列名称，必填
		putPolicy.put("persistentPipeline", "avthumb-pipe"+index);
		//数据处理完成结果通知地址
//		putPolicy.put("persistentNotifyUrl", "http://api.example.com/qiniu/pfop/notify");
		long expireSeconds = 3600;
		return auth.uploadToken(BUCKET_NAME, null, expireSeconds, putPolicy);
	}

	/**
	 * 查询文件上传压缩的进度
	 * @return
	 */
	public static OperationStatus getFileUpPro(String persistentId) throws Exception{
		Auth auth = getAuth();

		Configuration cfg = new Configuration(Region.region0());

		OperationManager operationManager = new OperationManager(auth, cfg);

		OperationStatus operationStatus = operationManager.prefop(persistentId);

		return operationStatus;
	}
}
