package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.GoodsLabelBO;
import com.rzx.dim4.base.bo.shop.GoodsTagsBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.callback.shop.GoodsLabelApiHystrix;
import com.rzx.dim4.base.service.callback.shop.GoodsTagsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024年07月19日 14:39
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "shopLabelApi", fallback = GoodsLabelApiHystrix.class)
public interface GoodsLabelApi {

    String URL = "/feign/shop/goodsLabel";


    /**
     * 新增商品标签
     * @return
     */
    @PostMapping(URL + "/addLabel")
    GenericResponse<ObjDTO<GoodsLabelBO>> addLabel(@RequestBody GoodsLabelBO goodsLabelBO,
                                                   @RequestHeader(value = "request_ticket") String requestTicket );

    /**
     * 删除商品标签
     * @return
     */
    @GetMapping(URL + "/delLabel")
    GenericResponse<ObjDTO<GoodsLabelBO>> delLabel(@RequestParam(name = "placeId") String placeId ,
                                                   @RequestParam(name = "id") String id,
                                                   @RequestHeader(value = "request_ticket") String requestTicket);


}
