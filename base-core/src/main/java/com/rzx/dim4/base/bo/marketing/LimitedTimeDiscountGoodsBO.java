package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class LimitedTimeDiscountGoodsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String limitedTimeDiscountId; // 限时折扣ID
    private String goodsId; // 商品id
    private String goodsName; // 商品名称
    private String goodsPic; // 商品图片
    private int number; // 商品数量
    private int price; // 发生单价
    private int isGift; // 是否赠品（默认否）0否，1是





}
