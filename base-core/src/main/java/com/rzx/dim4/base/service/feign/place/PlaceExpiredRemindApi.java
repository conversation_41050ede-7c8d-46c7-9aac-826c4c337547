/*
 * @(#)PlaceConfigApi.java 1.00 2024-7-30
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceExpiredRemindApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <p>Title:短信通知快要到期的网吧老板</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-10-16
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceExpiredRemindApi", fallback = PlaceExpiredRemindApiHystrix.class)
public interface PlaceExpiredRemindApi {
	String URL = "/feign/place";
	
	/**
	 * 短信通知快要到期的网吧老板
	 * @return
	 */

	@PostMapping(URL + "/placeExpiredRemind")
    GenericResponse<?> placeExpiredRemind();
    
}
