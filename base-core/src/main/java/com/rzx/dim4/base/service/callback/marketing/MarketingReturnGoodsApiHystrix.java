package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.ReturnGoodsBO;
import com.rzx.dim4.base.bo.marketing.ReturnGoodsRecordRequestBo;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingReturnGoodsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingReturnGoodsApiHystrix implements MarketingReturnGoodsApi {

    @Override
    public GenericResponse<PagerDTO<ReturnGoodsBO>> findPageList(ReturnGoodsRecordRequestBo paramsBo) {
        log.error("接口异常:::MarketingReturnGoodsApiHystrix.findPageList(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ReturnGoodsBO>> saveReturnGoods(ReturnGoodsBO paramsBo) {
        log.error("接口异常:::MarketingReturnGoodsApiHystrix.saveReturnGoods(Params={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ReturnGoodsBO>> findReturnGoodsDetail(String placeId, String returnGoodsNo) {
        log.error("接口异常:::MarketingReturnGoodsApiHystrix.findReturnGoodsDetail(placeId={}, returnGoodsNo={})", placeId, returnGoodsNo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}