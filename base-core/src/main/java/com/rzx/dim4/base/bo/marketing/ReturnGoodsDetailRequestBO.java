package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class ReturnGoodsDetailRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "商品id", required = true)
    private String goodsId;

    @ApiModelProperty(value = "商品数量", required = true)
    private int number;

    @ApiModelProperty(value = "发生单价", required = true)
    private int price;

    @ApiModelProperty(value = "是否赠品（默认否）0否，1是", required = true)
    private int isGift;

    @ApiModelProperty(value = "损益（单位分）")
    private int profitLossPrice;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "单间商品折扣（默认100%）")
    private int discount;
}
