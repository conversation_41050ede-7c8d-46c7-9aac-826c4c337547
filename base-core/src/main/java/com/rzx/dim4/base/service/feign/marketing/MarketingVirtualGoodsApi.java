package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingVirtualGoodsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingVirtualGoodsApi", fallback = MarketingVirtualGoodsApiHystrix.class)
public interface MarketingVirtualGoodsApi {
    String URL = "/feign/marketing/virtual/goods";

    @PostMapping(URL + "/findVirtualGoodsTemplatePage")
    GenericResponse<PagerDTO<BarcodeResponseBO>> findVirtualGoodsTemplatePage(@RequestBody GoodsVirtualTemplateRequestBo paramsBo);

    @PostMapping(URL + "/saveVirtualGoods")
    GenericResponse<SimpleDTO> saveVirtualGoods(@RequestBody GoodsVirtualAddRequestBO paramsBo);

    @PutMapping(URL + "/updateVirtualGoods")
    GenericResponse<SimpleDTO> updateVirtualGoods(@RequestBody GoodsVirtualUpdateRequestBO paramsBo);

    @DeleteMapping(URL + "/removeVirtualGoods")
    GenericResponse<SimpleDTO> removeVirtualGoods(@RequestParam String placeId, @RequestParam String goodsId);
}
