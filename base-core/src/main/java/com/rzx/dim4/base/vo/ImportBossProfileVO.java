package com.rzx.dim4.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportBossProfileVO {
    @ExcelProperty(value = "prov", index = 0)
    private String prov;

    @ExcelProperty(value = "Area", index = 1)
    private String area;

    @ExcelProperty(value = "zone", index = 2)
    private String zone;

    @ExcelProperty(value = "Addr", index = 3)
    private String address;

    @ExcelProperty(value = "CmpLimt", index = 4)
    private int clientNum;

    @ExcelProperty(value = "boss_number", index = 5)
    private String placeId;

    @ExcelProperty(value = "Name", index = 6)
    private String name;

    @ExcelProperty(value = "lastdate4bar", index = 7)
    private String lastdate4bar; // 过期时间

    @ExcelProperty(value = "accreditExpireDate", index = 8)
    private String accreditExpireDate; // 云板到期时间

    @ExcelProperty(value = "Linkmen", index = 9)
    private String frequentContactor; // 联系人

    @ExcelProperty(value = "Phone", index = 10)
    private String frequentContactorMobile; // 联系电话

    @ExcelProperty(value = "Auditid", index = 11)
    private String auditId; // 审计id

    @ExcelProperty(value = "isWz", index = 12)
    private int isWz; // 是否是网租

    @ExcelProperty(value = "tradeType", index = 13)
    private int type; // 0:网吧  1:酒店

}
