package com.rzx.dim4.base.bo.marketing;


import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商品类型新增BO
 *
 * <AUTHOR>
 * @date 2024年12月03日 15:27
 */
@Getter
@Setter
public class SaveGoodsTypeAddBO {

    private PlaceAccountBO webLoginAccount;

   private GoodsTypeBO goodsTypeBO;
}
