package com.rzx.dim4.base.service.feign.notify;

import com.rzx.dim4.base.bo.notify.region.RegionCodeBO;
import com.rzx.dim4.base.bo.notify.region.RegionCountryBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.notify.RegionServerApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/5/21
 **/
@Primary
@FeignClient(value = FeginConstant.NOTIFY_SERVER, contextId = "RegionServerApi", fallback = RegionServerApiHystrix.class)
public interface RegionServerApi {

    String URL = "/feign/notify/region";

    /**
     * 根据地区编码获取包含详细对应省市区信息的对象
     *
     * @param countryId
     * @return
     */
    @GetMapping(URL + "/regionCode")
    GenericResponse<ObjDTO<RegionCodeBO>> regionCode(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @RequestParam String countryId);

    /**
     * 根据地区编码列表获取包含详细对应省市区信息的对象列表
     *
     * @param countryIds
     * @return
     */
    @PostMapping(URL + "/regionCodes")
    GenericResponse<ListDTO<RegionCodeBO>> regionCodes(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestBody Set<String> countryIds);
    
    @GetMapping(URL + "/regionCountry")
    GenericResponse<ObjDTO<RegionCountryBO>> regionCountry(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @RequestParam String countryId);
}
