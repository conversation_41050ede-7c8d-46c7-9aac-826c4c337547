package com.rzx.dim4.base.enums.payment;

/**
 * 支付方式
 * 
 * <AUTHOR>
 * @date Jul 28, 2020 4:50:38 PM
 */
public enum PayType {

	CASH("0000", "现金"), // 0000-现金
	BILLING_CARD("0001", "计费卡"), // 0001计费卡
	LOSS("1009", "内部损耗"), // 商超中的逻辑
	GIVE("1010", "礼客赠送"), //礼客赠送
	WECHAT_PAY("1001", "微信扫码支付"), // 1001-微信支付（正扫）
	WECHAT_MP("1002", "微信公众号支付"), // 1002-微信公众号
	WECHAT_MINIAPP("1003", "微信小程序"), // 1003-微信小程序
	WECHAT_APP("1004", "微信APP支付"), // 1004-微信APP支付
	WECHAT_SCAN("1005", "微信条码支付"), // 1005-微信扫码支付（反扫/人脸）

	ALIPAY_PAY("2001", "支付宝扫码支付"), // 2001-支付宝扫码支付（正扫）
	ALIPAY_MP("2002", "支付宝公众号支付"), // 2002-支付宝公众号
	ALIPAY_MINIAPP("2003", "支付宝小程序支付"), // 2003-支付宝小程序
	ALIPAY_APP("2004", "支付宝APP付"), // 2004-支付宝APP
	ALIPAY_SCAN("2005", "支付宝条码支付"), // 2005-支付宝条码支付（反扫/人脸）
	ALIAPY_IOT("9005", "支付宝IOT"), // 9005-支付宝IOT

	MARKET_PAY("3001", "营销大师支付"), //
	JWELL_PAY("3002", "九威支付"), //

	THIRD_PAY("3002", "第三方"), // 第三方充值

	AGGREGATE_PAY("4001", "扫码支付"), //
	AGGREGATE_PAY_ALI("40011", "支付宝支付"), //
	AGGREGATE_PAY_WECHAT("40012", "微信支付"), //
	DOUYIN("40013", "抖音付款"), //
	MEITUAN("40014", "美团付款"), //

	; //

	private final String code;
	private final String message;


	private PayType(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static PayType getPayTypeByCode(String code) {
		if (code == null) {
			return null;
		}
		for (PayType payType : PayType.values()) {
			if (code.equals(payType.getCode())) {
				return payType;
			}
		}
		return null;
	}

	@Override
	public String toString(){
		return name();
	}

	public static boolean hasName(String name) {
		try {
			Enum.valueOf(PayType.class, name);
			return true;
		} catch (IllegalArgumentException e) {
			return false;
		}
	}

}
