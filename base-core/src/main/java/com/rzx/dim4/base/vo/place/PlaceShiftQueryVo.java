package com.rzx.dim4.base.vo.place;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/** 交班查询接口
 * <AUTHOR> hwx
 * @since 2025/2/26 11:31
 */
@Getter
@Setter
public class PlaceShiftQueryVo extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -2239021300883220531L;

    /**
     * 交班记录数据
     */
    private List<PlaceShiftQueryItemVo> itemVos;

    /**
     * 线上收入总额
     */
    private int totalIncome;

    /**
     * 留给下个班次金额总额
     */
    private int nextShiftHandoverCash;
}
