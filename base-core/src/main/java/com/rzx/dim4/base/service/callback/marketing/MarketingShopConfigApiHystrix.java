package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.ShopConfigBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingShopConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025年02月11日 14:26
 */
@Slf4j
@Component
public class MarketingShopConfigApiHystrix implements MarketingShopConfigApi {

    @Override
    public GenericResponse<ObjDTO<ShopConfigBO>> updateOrderSwitch(String requestTicket, String placeId, int orderSwitch) {
        log.error("接口异常，saveShopConfig(placeId={},  orderSwitch={})", placeId, orderSwitch);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ShopConfigBO>> saveInventoryConfig(String requestTicket, @RequestBody PlaceAccountBO miniAppLoginAccount, String placeId, int whetherInventoryManage, int distinguishShelfInventory, int stockOutShowSwitch) {
        log.error("接口异常，saveInventoryConfig(miniAppLoginAccount={},placeId={},,whetherInventoryManage={},distinguishShelfInventory={},  stockOutShowSwitch={})",
                new Gson().toJson(miniAppLoginAccount), placeId, whetherInventoryManage, distinguishShelfInventory, stockOutShowSwitch);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ShopConfigBO>> findPlaceShopConfig(String placeId) {
        log.error("接口异常，findPlaceShopConfig(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ShopConfigBO>> saveCustomPayLimit(String requestTicket, String placeId, String accountId, int customizedPayLimit) {
        log.error("接口异常，setCustomPayLimit(requestTicket={},placeId={},accountId={},customizedPayLimit={})", requestTicket, placeId, accountId, customizedPayLimit);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse clearSuperMarketData(String requestTicket, String placeId, String accountId) {
        log.error("接口异常，clearSuperMarketData(requestTicket={},placeId={},accountId={})", requestTicket, placeId,accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
