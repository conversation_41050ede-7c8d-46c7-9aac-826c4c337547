package com.rzx.dim4.base.enums;

/**
 * 规则： code % 10 == 0, 表示业务处理成功，否则表示业务处理失败
 *
 * <AUTHOR>
 * @date Dec 24, 20194:44:12 PM
 */
public enum ServiceCodes {

    NO_ERROR(100000, "操作成功"), //
    OPT_REPEAT(100001, "重复操作"), //
    OPT_ERROR(100002, "操作失败"), //

    // 参数异常
    BAD_ID(400001, "ID错误"), //
    NO_ID(400002, "ID不存在"), //
    BAD_TICKET(400003, "请求Ticket错误"), //
    NO_TICKET(400004, "请求Ticket不存在"), //
    BAD_PARAM(400005, "参数错误"), ///
    NULL_PARAM(400006, "参数不能为空"), //
    BAD_FORMAT(400007, "参数格式错误"), //
    IP_ERROR(400008, "IP错误"), //

    ID_NUMBER_ERROR(400009, "身份证信息不合法"), //
    BAD_TIMESTAMP(400011, "时间戳错误"), //
    INVALID_TIMESTAMP(400012, "时间戳已失效"), //
    BAD_ENCRYPT(400013, "加密失败"), //
    BAD_DECRYPT(400014, "解密失败"), //
    NO_OFF_CLIENT(400016, "当前选择终端正在上机计费，请待终端下机后绑定"), //
    EXIST_NAME(400015, "当前名称已存在，请更换"), //
    CONFLICT_RULE(400017, "和当前场所下该卡类型已存在的规则冲突，请修改"), //
    NO_CONFIG(400018, "配置数据不存在"), //
    BAD_CONFIG(400019, "配置错误"), //
    EXPORT_ERROR(400020, "导出失败"), //
    PARAM_FORMAT_ERROR(400021, "参数格式错误"), //
    START_TIME_AFTER_END_TIME_ERROR(400022, "开始时间不能大于结束时间"),
    DATETIME_PARSE_ERROR(400023, "时间格式解析错误"),
    SEARCH_KEY_LENGTH_ERROR(400024, "查询内容长度至少6位"),

    // admin-server
    ADMIN_LOGIN_SUCC(800000, "登录成功"), //
    ADMIN_LOGIN_FAIL(800001, "登录失败"), //
    ADMIN_BAD_ACCOUNT(800002, "账号错误"), //
    ADMIN_BAD_PASSWORD(800003, "密码错误"), //
    ADMIN_BAD_MOBILE(800004, "手机错误"), //
    ADMIN_BAD_CODE(800005, "验证码错误"), //
    ADMIN_EXP_CODE(800006, "验证码已过期"), //
    ADMIN_EXIST_ACCOUNT(800007, "账号已存在"),

    // place-server
    PLACE_LOGIN_SUCC(810000, "登录成功"), //
    PLACE_LOGIN_FAIL(810001, "登录失败"), //
    PLACE_BAD_ACCOUNT(810002, "账号错误"), //
    PLACE_BAD_PASSWORD(810003, "密码错误"), //
    PLACE_BAD_MOBILE(810004, "手机错误"), //
    PLACE_BAD_CODE(810005, "验证码错误"), //
    PLACE_EXP_CODE(810006, "验证码已过期"), //
    PLACE_EXIST_ACCOUNT(810007, "账号已存在"), //
    PLACE_EXPIRED(810008, "场所已过期"), //
    PLACE_CASHIER_NO_WORKING(810009, "收银员未上班"), //
    PLACE_ACCOUNT_NOT_FOUND(810011, "账号未找到"), //
    PLACE_PLACE_PROFILE_NOT_FOUND(810012, "场所信息未找到"), //
    PLACE_PLACE_CONFIG_NOT_FOUND(810013, "场所配置未找到"), //
    PLACE_IDENTIFIER_NOT_FOUND(810014, "场所识别码未找到"), //
    PLACE_AREA_NOT_FOUND(810015, "区域未找到"), //
    PLACE_CLIENT_NOT_FOUND(810016, "客户端未找到"), //
    PLACE_SOFTWARE_EXPIRED(810017, "软件授权已过期"), //
    PLACE_CLIENT_OVER_LIMIT(810018, "客户端数量超过上限"), //
    PLACE_CLIENT_REGISTERED_FAIL(810019, "客户端注册失败"), //
    PLACE_CASHIER_REGISTERED_FAIL(810021, "收银员数量超过上限"), //
    PLACE_REGCARD_CONFIG_NOT_OPEN(810022, "场所注册卡配置未开启"), //
    PLACE_AREA_NAME_NOT_REPEAT(810023, "区域名称不能重复"), //
    PLACE_AREA_NAME_LENGTH_NOT_RIGHT(810024, "区域名称长度必须大于2，且小于20"), //
    PLACE_AREA_NAME_NOT_VALID(810025, "区域名称只能为汉字、数字、字母、下划线"), //
    PLACE_CASHIER_OVER_LIMIT(810026, "收银台数量超过上限"), //
    PLACE_PRIMARY_CASHIER_LIMIT(810027, "主收银台只能有一个"), //
    PLACE_CASHIER_IP_NOT_FOUND(810028, "未找到收银台Ip地址"), //
    PLACE_NOT_DELETE_PRIMARY_CASHIER(810029, "主收银台不能删除"), //
    PLACE_CASHIER_ON_WORKING(810031, "该收银员正在当班中"), //
    PLACE_AGENT_FREE_RENEWAL_FAIL(810032, "该场所免费延期7天使用次数超限（2次/年/场所）"), //
    PLACE_AGENT_SPOT_NOT_ENOUGH(810033, "当前点数不足"), //
    PLACE_AGENT_NOT_CREATE(810034, "当前账号不允许创建下级代理商"), //
    PLACE_SECOND_AGENT_NOT_BIND(810035, "绑定失败，该代理商级别不是总代"), //
    PLACE_NOT_MARKET(810036, "该场所不是营销大师类型场所"), //
    PLACE_NOT_JWELL(810037, "该场所不是九威类型场所"), //
    PLACE_NOT_DABAZHANG(810038, "该场所不是大巴掌类型场所"), //
    PLACE_DEVICE_NOT_FOUND(810039, "设备未找到"), //
    PLACE_AUTH_CONFIG_NOT_FOUND(810040, "实名配置没找到"), //

    PLACE_CLIENT_HOSTNAME_EXIST(810041, "该机器名称已存在"), //

    PLACE_REALNAME_DISABLED(810042, "场所实名禁用"), //
    PLACE_DEVICE_IOT_AUTH_ERROR(810043, "设备实名认证错误"), //
    PALCE_DEVICE_IOT_AUTH_INFO_NOT_FOUND(810044, "没有实名信息"), //
    PLACE_DEVICE_AUTH_FEE_NONEED(810045, "无需实名支付"), //
    PLACE_DEVICE_ALREADY_BIND(810046, "该设备已在其他场所下绑定"), //
    PLACE_DEVICE_DISABLED(810047, "设备不可用"), //

    PLACE_CHAIN_STORES_NOT_FOUND(810048, "连锁店信息未找到"), //
    PLACE_CHAIN_STORES_EXIST(810049, "连锁店信息已存在"), //
    PLACE_RESTART_PLEASE(810050, "中心配置有变动，请重启收银台"), //
    PLACE_CLIENT_ADD_HOSTNAME_EXIST(810051, "注意：机器号设置重复，请检查后重新提交"), //

    PLACE_AT_LAST_ONE_RIGHT(810052, "请勾选至少一个控制权限"),

    PLACE_CASHIER_EXPIRED(810053, "您的软件服务时间已到期，请续费！"), //
    PLACE_INTERFACE_REQUEST_NUMBER_UP_LIMIT(810054, "该接口请求次数已经超过数量"),
    PLACE_BIZ_CONFIG_REALNAME_SURCHARGE_NOT_OPEN(810055, "场所附加费配置未开启"),
    PLACE_SURCHARGE_CONFIG_NOT_FOUND(810056, "场所附加费未找到"),
    PLACE_SURCHARGE_ACTIVE_TYPE_NOT_HAVE_REALNAME(810057, "附加费触发方式未配置实名扣点"),


    PLACE_UPDATE_VERSION_FLAG_NEED_EXIT_CHAIN(810058, "请先退出连锁，再将'版本标志'修改成基础版"),

    PLACE_SURCHARGE_ACTIVE_TYPE_NOT_CONFIG(810059, "请先联系技术支持或代理配置附加费触发方式。"),
    PLACE_LOGIN_EXPIRED(810060, "登录已失效，请重新登录"),
    PLACE_CONFIG_ONLINE_PAY_NONSUPPORT(810061, "场所不支持在线支付！"),
    PLACE_SHIFT_NOT_FOUND(810062, "班次不存在！"),
    PLACE_SHIFT_IS_OFF(810063, "班次已交班！"),
    PLACE_SHIFT_ORDERS_NOT_DELIVERY(810064, "当前班次有未派送订单，请派送后再交班"), //


    // billing-server
    BILLING_LOGIN_SUCC(830000, "登录成功"), //
    BILLING_LOGIN_FAIL(830001, "登录失败"), //
    BILLING_PASSWORD_ERROR(830002, "密码错误"), //
    BILLING_INSUFFICIENT_BALANCE(830003, "余额不足"), //
    BILLING_SERVICE_INDEX_ERROR(830004, "业务索引错误"), //
    BILLING_AMOUNT_ERROR(830005, "金额错误"), //
    BILLING_PLACE_PROFILE_NOT_FOUND(830006, "场所信息未找到"), //
    BILLING_PLACE_CONFIG_NOT_FOUND(830007, "场所配置未找到"), //
    BILLING_IDENTIFIER_NOT_FOUND(830008, "识别码未找到"), //
    BILLING_AREA_NOT_FOUND(830009, "区域未找到"), //
    BILLING_DIVERT_PACKAGE_TIME_FAIL_FOR_INSUFFICIENT_BALANCE(830010, "包时金额不足，转包时失败，执行普通计费"), //
    BILLING_CLIENT_NOT_FOUND(830011, "客户端未找到"), //

    BILLING_CARD_NOT_FOUND(830012, "计费卡未找到"), //

    BILLING_CARD_TYPE_NOT_FOUND(830013, "计费卡类型未找到"), //
    BILLING_CARD_MODIFY_ERROR(830030, "修改失败！正在上机的连锁会员，请下机后修改"), //

    BILLING_ONLINE_NOT_FOUND(830014, "在线信息未找到"), //

    BILLING_RULE_NOT_FOUND(830015, "计费规则未找到"), //
    BILLING_TOPUP_NOT_FOUND(830016, "充值规则未找到"), //
    BILLING_LOG_HB_NOT_FOUND(830017, "心跳信息未找到"), //
    BILLING_LOG_LOGIN_NOT_FOUND(830018, "登录信息未找到"), //
    BILLING_LOG_SHIFT_NOT_FOUND(830019, "班次信息未找到"), //
    BILLING_LOG_TOPUP_NOT_FOUND(830021, "充值记录未找到"), //
    BILLING_LOG_PTR_NOT_FOUND(830022, "预包时记录未找到"), //
    BILLING_UPDATE_VERSION_NOT_FOUND(830023, "更新版本未找到"), //
    BILLING_PT_AREA_ERROR(830024, "包时区域错误"), //
    BILLING_PT_NOT_START(830025, "包时未开始"), //
    BILLING_PT_CONFLICT(830026, "包时冲突"), //
    BILLING_PT_AREA_CONFLICT(830027, "包时区域冲突"), //
    BILLING_PT_CARD_TYPE_CONFLICT(830028, "包时卡类型冲突"), //
    BILLING_PT_REPEAT(830029, "包时重复"), //
    BILLING_CARD_IS_ONLINE(830031, "该计费卡已在线"), //
    BILLING_CARD_IS_EXSIT(830032, "该计费卡已存在"), //
    BILLING_CARD_NO_ACTIVATION(830033, "计费卡未激活"), //
    BILLING_CLIENT_IS_ONLINE(830034, "客户端已在线"), //
    BILLING_SELF_CHECKOUT_NOT_SUPPORT(830035, "自助结账不支持"), //
    BILLING_REVERSAL_ONLINE(830036, "不支持在线冲正"), //
    BILLING_RULE_CONFLICT(830037, "计费规则冲突"), //
    BILLING_CARD_TYPE_NAME_IS_EXSIT(830038, "该计费卡类型名称已存在"), //
    BILLING_CASHIER_TASK_NOT_FOUND(830039, "收银台任务未找到"), //
    BILLING_PT_END(830041, "今日包时已过"), //
    BILLING_CASH_PT_TOPUP_NOT_ENOUGH(830042, "包时请先充值"), //
    BILLING_CLIENT_ONLINE(830043, "客户端已上机"), //
    BILLING_PT_END_INCLUDE(830044, "包时失败，所选包时与当前计费规则冲突"), //
    BILLING_DATE_PARSE_ERROR(830045, "日期格式转换错误"), //
    BILLING_TOPUP_TYPE2_EXISTED(830046, "会员日相同【充值金额-赠送金额】规则已存在"), //
    BILLING_DATE_AND_CARD_TYPE_CANNOT_NULL(830047, "生效日期、卡类型不能为空"), //
    BILLING_TOPUP_TYPE3_EXISTED(830048, "相同【充值金额】充多少送多少规则已存在"), //
    BILLING_TOPUP_TYPE1_EXISTED(830049, "卡类型、充值金额对应充值赠送已存在"), //
    BILLING_TOPUP_DATE_AMOUNT_CARD_TYPE_INVALID(830051, "该日期下，卡类型已有相同充值金额赠送规则！"), //
    BILLING_LOG_SHIFT_NOT_ON_DUTY(830052, "当前账号不是当班人"), //
    BILLING_CASHIER_NO_AUTH(830053, "收银员权限不足"), //
    BILLING_CASHIER_AUTHORITY_NOT_FOUND(830054, "收银员权限未找到"), //
    BILLING_REFUND_EXCEED(830055, "余额不足，请修改冲正金额"), //
    BILLING_CARD_TYPE_IDENTICAL(830056, "选择的卡类型与当前卡类型相同"), //
    BILLING_PRICE_FORMAT_ERROR(8300057, "7*24小时费率价格格式错误"), //
    BILLING_SHIFT_ACCOUNT_ID_ERROR(8300058, "不能交班给另一个正在当班的收银员"), //
    BILLING_NOT_UPDATE_TEMPORARY_CARD(8300059, "不能更改为临时卡"), //
    BILLINT_ONLINE_NOT_SUPPORT(830061, "不支持在线修改"), //
    BILLING_ONLINE_MODIFY_SUPPORT(830062, "修改成功！正在上机的用户，结账后生效"), //
    BILLING_ONLINE_DELETE_NOT_SUPPORT(830063, "正在上机，不能删除该会员"), //
    BILLING_MEMBER_DAY_TOPUP_OVER(830064, "该用户会员日赠送次数已达上限..."), //
    BILLING_CARD_LIMIT_LOGIN(830065, "当前时段禁止该用户上机，请联系管理员解禁！"), //
    BILLING_INSUFFICIENT_CASH_BALANCE(830066, "该包时必须使用本金余额，当前本金余额不足!"), //
    BILLING_QR_CODE_ERROR(830067, "二维码生成错误"), //
    BILLING_QR_CODE_NOT_FOUND(830068, "二维码信息未找到"), //
    BILLING_TYPE_NOT_LOGIN(830069, "该接口不支持计费类型场所"), //
    BILLING_SCAN_QRCODE_LIMIT(830071, "请扫描客户端二维码"), //
    BILLING_TEMP_CARD_NOT_SUPPORT(830072, "临时卡不支持"), //
    BILLING_CASHIER_QR_CODE_NOT_SUPPORT(830073, "收银台实名二维码不支持"), //
    BILLING_FILE_CONFIG__READ_ERROR(830074, "配置文件读取失败"), //
    BILLING_FILE_PASSWORD_ERROR(830075, "文件密码错误"), //
    BILLING_RATE_FILE_NOT_FOUND(830076, "费率文件未找到"), //
    BILLING_ROOM_NOT_SUPPORT_TEMP_CARD(830077, "包间区域不支持临时卡上机"), //
    BILLING_PREPARE_PACKAGE(831000, "预包时成功"), //
    BILLING_REWARD_POINTS_RULE_NOT_FOUND(830077, "积分赠送规则未找到"), //
    BILLING_EXCHANGE_POINTS_RULE_NOT_FOUND(830078, "积分兑换规则未找到"), //
    BILLING_REWARD_POINTS_RULE_NOT_OPEN(830079, "积分赠送规则未开启"), //
    BILLING_EXCHANGE_POINTS_RULE_NOT_OPEN(830081, "积分兑换规则未开启"), //
    BILLING_ALREADY_SIGNED(830082, "您今天已经签到过了!"), //
    BILLING_POINTS_NOT_ENOUGH(830083, "积分不足!"), //
    BILLING_TEMPORARY_NOT_SUPPORT_EXCHANGE(830084, "临时卡不支持积分兑换!"), //
    BILLING_TEMPORARY_NOT_SUPPORT_SIGNED(830085, "临时卡不支持签到!"), //
    BILLING_POINTS_EXCHANGE_PRESENT_EXISTED(830086, "该金额已设置兑换规则,请勿重复添加!"), //
    BILLING_POINTS_EXCHANGE_EXISTED(830087, "该积分已设置兑换规则,请勿重复添加!"), //
    BILLING_ROOM_AREA_NOT_SUPPORT_EXCHANGE(830088, "包间上机不支持跨区换机"), //
    BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE(830089, "副卡包间上机不支持操作包时"), //
    BILLING_ANTI_ADDICTION_NO_PERMISSION(830091, "当前账号暂不允许登录"),
    BILLING_REQUEST_REPEAT(830099, "操作过于频繁，请稍后再试"), //
    BILLING_PLACE_CHAIN_NOT_FOUNT(830101, "连锁信息没有找到"), //
    BILLING_CARD_TYPE_USED(830102, "该卡类型已被使用"), //
    BILLING_CARD_TYPE_NOT_ALLOW_DELETE(830103, "该卡类型不允许删除"), //
    BILLING_PLACE_ALREADY_JOIN_CHAIN(830104, "该场所已经加入连锁"), //
    BILLING_ROAM_ALREADY_LOGIN(830105, "该用户正在连锁内其他分店上机,请先结账"), //
    BILLING_TEMP_CARD_DELETE_NOT_SUPPORT(830106, "不支持删除临时卡"), //
    BILLING_CHAIN_CARD_DELETE_NOT_SUPPORT(830107, "不支持删除连锁会员"), //
    BILLING_CHAIN_CARD_TYPE_NOT_EXIST(830108, "该连锁卡类型不存在"), //
    BILLING_FAILED_TO_CREATE_CARD(830109, "开卡失败"),
    BILLING_WANXIANG_REPEAT_USER(830111, "存在重复万象用户"),
    BILLING_TOPUP_MODE0_EXISTED(830112, "按充值规则赠送模式下，相同【充值金额-赠送金额】规则已存在"), //
    BILLING_TOPUP_MODE1_EXISTED(830113, "按充多少送多少模式下，相同【充值金额】规则已存在"), //
    BILLING_REFUND_TO_REVOKE(830114, "修正后奖励大于原订单，仅支持“撤销”操作"), //

    BILLING_CHAIN_CARD_STATE_ERROR(830115, "当前门店存在临时卡，请先结账"), //
    BILLING_ONLINE_PACKAGE_NOT_SUPPORT(830116, "在线包时不支持"),
    BILLING_PACKAGE_TIME_COMING_END(830117, "该包时即将结束,请选择其他包时规则"),
    BILLING_COMMON_ONLINE_NOT_CONTINUE_PACKAGE(830118, "标准计费中,不能续包时"),
    BILLING_RULE_NOT_FUTURE_PACKAGE(830119, "该包时规则不能提前包时"),
    BILLING_RULE_BALANCE_NOT_CASH_REFUND(830120, "余额包时不支持现金退费，请选择余额退费"),
    BILLING_EXIST_FUTURE_PACKAGE_RULE(830121, "存在未使用的预包时,请先取消该包时再结账"),
    BILLING_COMMON_ONLINE_NOT_CONVERT_PACKAGE(830122, "标准计费中,不能转包时"),
    BILLING_RULE_NOT_AVAILABLE(830123, "计费规则不可用"), //
    BILLING_PACKAGE_RULE_END(830124, "该包时规则已结束"), //
    BILLING_CASHIER_UPGRADE(830125, "请升级收银台版本后再重新操作"), //
    BILLING_NEED_ACTIVE_IN_CASHIER(830126, "请先至收银台激活"), //
    BILLING_QR_CODE_LOSE_EFFICACY(830127, "二维码已过期，请刷新"), //
    BILLING_PACKAGE_ORDER_NOT_SUPPORT_REVERSAL(830128, "包时订单不支持冲正撤销"), //
    BILLING_LGJ_DATE_TRANSFORM_MEMBER_PROCESSING(830129, "当前正在处理会员数据，请稍后执行"), //
    BILLING_CLIENT_NOT_SUPPORT_BOOK_SEATS(830131, "客户端不支持订座，请升级客户端"),
    BILLING_BOOK_SEATS_UNLOCK_CODE_WRONG(830132, "机器已预定，解锁码错误"),
    BILLING_CLIENT_ON_BOOKING(830133, "客户端正在订座中"),
    BILLING_CUSTOMER_ON_BOOKING(830134, "用户正在订座中"),
    BILLING_CARD_TYPE_NOT_SUPPORT(830135, "当前卡类型不支持"),
    BILLING_CUR_AREA_NO_RIGHT_BOOKING(830136, "限制上机区域，订座失败"),
    BILLING_QR_NEED_WECHAT(830137, "请使用微信扫码"),
    BILLING_BOOK_SEATS_IS_NOT_OPEN(830138, "订座未开启"),
    BILLING_QR_CODE_IS_USED(830139, "二维码已使用，请点击刷新机器二维码"),
    BILLING_CONFIG_QRCODE_NO_SUPPORT(830141, "场所不支持二维码支付"), //
    BILLING_TEMP_CARD_NOT_ALLOW_SELF_CHECKOUT_BY_CASH(830142, "门店临时卡还有未消费\"现金\"，请前往收银台结账！"),
    BILLING_EXIST_FUTURE_PACKAGE_RULE_NOT_UPDATE_CARD_TYPE(830143, "存在未使用的包时,请先取消该包时再更改卡类型"),
    BILLING_REVERSAL_POINTS_OVER_CARD_POINTS(830144, "调减积分大于会员卡可操作积分，请修改后再操作!"), //
    BILLING_CASHIER_LOGOUT_ALL_CARD_STATE_HINT(830145, "全场结账成功，现金临时卡用户请手动结账退款!"), //
    BILLING_TOPUP_CAN_USE_NOT_FOUND(830146, "找不到可用的充值记录"),
    BILLING_HOTEL_NOT_SUPPORT_ONLINE_LOGOUT(830147, "酒店不支持移动端结账!"),
    BILLING_WECHAT_UPGRADE_NOT_SUPPORT_TEMPCARD(830148, "公众号升级会员只支持临时卡!"),
    BILLING_EXIST_UPGRADE_RECORD(830149, "已经存在未生效的升级卡类型记录!"),
    BILLING_NOT_SUPPORT_TEMPCARD_UPGRADE(830150, "当前场所不支持临时卡自助升级会员!"),
    BILLING_CURRENT_SHIFT_NOT_OFF(830151, "班次未结束"),
    BILLING_CARD_NON_ID_NUMBER_LIMIT(830152, "开卡失败，非身份证开卡已达上限"),
    BILLING_DUDUNIU_REPEAT_USER(830153, "存在重复嘟嘟牛用户"),
    BILLING_MEMBER_DATA_REQUEST_LIMIT(830154, "页面请求频繁，已超过最大请求限制，如需帮助，请联系您的代理或客服解决！"),
    BILLING_CURRENT_CLIENT_NOT_ALLOW_BOOK_SEATS(830155, "当前客户端不允许订座"),
    BILLING_LGJ_CONFIG_FORBIDDEN(830156, "当前场所禁止升级，请联系管理员修改配置"), //
    BILLING_CONFIG_FORBID_EXCHANGE(830157, "本网吧禁止客户端换机，请前往收银台操作"), //
    BILLING_IN_PROGRESS(830158, "其他扣费业务正在进行中，请稍后重试"), //
    //    BILLING_QUERY_TIME_IS_TOO_THREE(830161, "目前仅支持查询四个月时间数据！"), //
    BILLING_QUERY_TIME_IS_TOO_THREE(830161, "单次查询时间跨度最多两个月！"), //
    BILLING_CONFIG_AGE_CONFIG_NO_PASS(830159, "激活失败，上网年龄不符合要求！"), //
    BILLING_EXIST_FUTURE_PACKAGE_RULE_HINT(830163, "存在未使用的预包时,请先取消该包时再副卡上机!"),

    BILLING_NOW_TIME_BUSY(830164, "目前是系统繁忙阶段，请在22点后进行交班操作！"), //
    BILLING_TOO_LONG_TIME_NO_SHIFT(830165, "长时间未交班，请联系技术支持后台操作"), //

    BILLING_CURRENT_CLIENT_REPETITION_BOOK_SEATS(830167, "客户端已被其他人预订！"),

    BILLING_PAY_CODE_ERROR(830168, "条形码格式错误！"),

    BILLING_ACTIVATE_FAILED_INSUFFICIENT_BALANCE(830169, "激活失败,余额不足扣附加费"), //
    BILLING_BOOK_SEATS_NO_REPETITION(830170, "客户端未被预订"), //

    BILLING_RULE_BALANCE_CONVERT_NOT_CASH_REFUND(830171, "余额包时转现金包时的不支持现金退费，请选择余额退费"),
    BILLING_CLIENT_ERROR_CALL_TYPE(830172, "客户端错误呼叫类型"),
    BILLING_PEAK_ONLINE_NOT_FOUND(830173, "当日高峰在线人数信息未找到"), //

    BILLING_INVITE_UNDERWAY(830174, "用户正在请客上网中"),

    BILLING_INVITE_LIMIT_NUM_ERROR(830175, "请客人数超出限制"),

    BILLING_INVITE_DATE_ERROR(830176, "邀请码已过期"),

    BILLING_INVITE_ROOM_NOT_SUPPORT(830177, "请客上网中，包间登入不支持"),

    BILLING_INVITE_ONLINE_NOT_FOUND(830178, "请客上网信息未找到"),

    BILLING_INVITE_PACKAGE_NOT_SUPPORT(830179, "请客上网中，不支持包时"),

    BILLING_ROOM_INVITE_NOT_SUPPORT(830180, "包间上网中，请客上网不支持"),

    BILLING_PACKAGE_NOT_CROSS_AREA_EXCHANGE(830181, "该包时规则不允许跨区换机"),

    BILLING_PACKAGE_CROSS_AREA_EXCHANGE(830182, "包时跨区换机，请在当前客户端完成剩余操作"), //

    BILLING_NOT_PACKAGE_EXCHANGE(830200, "不是包时跨区域换机，正常登入"),



    // iot-server
    IOT_ACTIVATE_SUCCESS(200000, "IOT激活成功"),
    IOT_ACTIVATE_WAITING(220000, "IOT激活等待中"),
    IOT_ACTIVATE_FAILED(240000, "IOT激活失败"),
    IOT_ACTIVATE_TOKEN_EXCEPTION(250000, "IOT激活Token没有找到"),
    IOT_ALIFACE_INIT_ERROR(250001, "支付宝人脸认证初始化失败！"),

    IOT_ALIFACE_GET_URL_ERROR(250003, "支付宝人脸认证获取URL失败！"),
    IOT_ALIFACE_QUERY_RESULT_ERROR(250005, "支付宝人脸认证结果查询失败！"),

    IOT_MOBILE_ILLEGAL(250006, "手机号码非法"),

    // payment-server
    PAYMENT_CREATE_SUCC(860000, "订单创建成功"), //
    PAYMENT_REFUND_SUCC(860020, "退款成功"), //
    PAYMENT_CREATE_FAIL(860001, "创建订单失败"), //
    PAYMENT_BAD_AMOUNT(860002, "订单金额错误"), //
    PAYMENT_BAD_STORENO(860003, "收款账号错误"), //
    PAYMENT_BAD_PLACEID(860004, "场所ID错误"), //
    PAYMENT_BAD_PAYTYPE(860005, "支付类型错误"), //
    PAYMENT_BAD_BIZSERVER(860006, "业务中心错误"), //
    PAYMENT_UNREGISTERED(860007, "业务类型未注册"), //
    PAYMENT_BAD_USER(860008, "用户信息错误"), //
    PAYMENT_BAD_IDNUMBER(860009, "身份证号错误"), //
    PAYMENT_PAY_SUCCESS(860010, "支付成功"), //
    PAYMENT_PAY_WAITING(860011, "等待支付"), //
    PAYMENT_BAD_ORDERID(860012, "订单号错误"), //
    PAYMENT_BAD_QUERY(860013, "错误的查询"), //
    PAYMENT_BAD_STATUS(860014, "订单状态错误"), //
    PAYMENT_REFUND_ERROR(860015, "退款失败"), //
    PAYMENT_BAD_MERCHANT(860016, "商户未注册"), //
    PAYMENT_BAD_BIZPAYINFO(860017, "支付参数错误"), //
    PAYMENT_REFUND_REPEAT(860018, "订单重复退款"), //
    PAYMENT_BAD_REFUND_AMOUNT(860019, "退款金额错误"), //
    PAYMENT_BAD_ORDER_ALREADY_PUSH(860021, "该订单已推送"), //
    PAYMENT_REFUND_DOING(860022, "订单退款中"), //

    PAYMENT_REPEAT(860023, "订单退款中"), //
    PAYMENT_PAY_ERROR(860024, "支付失败"), //
    PAYMENT_PAY_CANCEL(860025, "订单已取消"), //
    PAYMENT_WITHDRAWAL_ERROR(860026, "提现失败"), //

    PAYMENT_WITHDRAWAL_SUCC(860027, "提现成功"), //
    PAYMENT_WITHDRAWAL_QUERY_ERROR(860028, "提现查询失败"), //

    PAYMENT_WITHDRAWAL_NOT_FOUND(860029, "申请编号不存在"), //

    // regcard-server
    REGCARD_SUCCESS(850000, "证件状态正常"), //
    REGCARD_DISABLED(850001, "注册卡未启用"), //
    REGCARD_UNREGISTED(850002, "证件未注册"), //
    REGCARD_EXPIRED(850003, "证件已过期"), //
    REGCARD_INVALID(850004, "证件状态无效"), //
    REGCARD_CONFIG_ERROR(850005, "注册卡设置错误"), //
    REGCARD_SERVER_ERROR(850006, "注册卡中心异常"), //
    REGCARD_SERVER_RESP_ERROR(850007, "注册卡中心返回异常"), //
    REGCARD_REGISTERD_ERROR(850008, "注册失败"), //
    REGCARD_ONUNBIND_ERROR(850009, "解绑失败"), //
    REGCARD_SERVER_CONFIG_ERROR(510012, "场所没有配置信息"), //
    REGCARD_INVALID_NEED_BINDING(510013, "请到收银台绑定注册卡"), //
    // 30215 手机号已经被注册
//    REGCARD_MOBILE_EXIST(510014, "手机号已经被注册"),

    // 20201 获取数据库操作对象失败
    EGCARD_BINDING_DB_ERROR(510015, "获取数据库操作对象失败"),
    // 10201 地区编码未填写
    EGCARD_BINDING_AREA_CODE_EMPTY(510016, "地区编码未填写"),
    // 30201 该地区未开通手机业务
    EGCARD_BINDING_AREA_CODE_NOT_OPEN(510017, "该地区未开通手机业务"),
    // 30202 认证卡序列号不存在
    EGCARD_BINDING_CARD_NUMBER_NOT_EXIST(510018, "认证卡序列号不存在"),
    //30203 网吧账户不存在
    EGCARD_BINDING_ACCOUNT_NOT_EXIST(510019, "网吧账户不存在"),
    //30204 该网吧不支持当前配置的绑定模式
    EGCARD_BINDING_ACCOUNT_NOT_SUPPORT(510020, "该网吧不支持当前配置的绑定模式"),
    //30205 认证卡序列号不能为空
    EGCARD_BINDING_CARD_NUMBER_NOT_EMPTY(510021, "认证卡序列号不能为空"),
    //30206 证件号码已经被注册
    EGCARD_BINDING_ID_NUMBER_EXIST(510022, "证件号码已经被注册"),
    //    //30207 手机号码已经被注册
    EGCARD_BINDING_MOBILE_EXIST(510023, "手机号码已经被注册"),
    //30208 功能被禁止使用
    EGCARD_BINDING_FUNCTION_FORBIDDEN(510024, "功能被禁止使用"),
    //30209 此序列号只能供网吧账户充值使用
    EGCARD_BINDING_CARD_NUMBER_FORBIDDEN(510025, "此序列号只能供网吧账户充值使用"),
    //30210 序列号非法
    EGCARD_BINDING_CARD_NUMBER_ILLEGAL(510026, "序列号非法"),
    //30211 座机号码登记次数超过设置最大值
    EGCARD_BINDING_TEL_NUMBER_MAX(510027, "座机号码登记次数超过设置最大值"),
    //30212 手机号码非法
    EGCARD_BINDING_MOBILE_ILLEGAL(510028, "手机号码非法"),
    //30213 该手机号在规定的时间范围内绑定的次数过多
    EGCARD_BINDING_MOBILE_MAX(510029, "该手机号在规定的时间范围内绑定的次数过多"),
    //30214 此序列号只能供网民证件注册使用
    EGCARD_BINDING_CARD_NUMBER_FORBIDDEN2(510030, "此序列号只能供网民证件注册使用"),
    // 30215 证件或手机号码已被注册并且与您输入的不一致
    EGCARD_BINDING_MOBILE_AND_ID_NUMBER_NOT_SAME(510031, "证件或手机号码已被注册并且与您输入的不一致"),
    // 40200 接口调用完成
    REGCARD_BINDING_SUCCESS(510032, "接口调用完成"),

    //31001 短信发送失败
    REGCARD_SMS_SEND_FAIL(510033, "短信发送失败"),
    //31002 未超过短信发送间隔时间
    REGCARD_SMS_TOO_FREQUENTLY(510034, "未超过短信发送间隔时间"),
    //41000 接口调用完成
    REGCARD_SMS_SUCCESS(510035, "接口调用完成"),
    // 40900 查询失败，请确认填写的证件是否已注册
    REGCARD_SMS_QUERY_FAIL(510036, "查询失败，请确认填写的证件是否已注册"),


    // notify-server
    NOTIFY_SMS_SEND_SUCC(800000, "短信发送成功"), //
    NOTIFY_SMS_SEND_FAIL(800001, "短信发送失败"), //
    NOTIFY_SMS_TOO_FREQUENTLY(800002, "短信发送太频繁"), //
    NOTIFY_SMS_CODE_ERROR(800003, "验证码错误或者已失效"), //
    NOTIFY_SMS_TEMPLATE_UNDEFINED(800004, "短信模板未定义"), //
    NOTIFY_SMS_BAD_MOBILE(800005, "手机号错误"), //
    NOTIFY_SMS_CODE_CORRECT(800010, "验证码正确"), //

    // user-server
    ACCOUNT_NO_BOUND(860001, "账号未绑定"), //
    SCAN_QRCODE_INVALID(860002, "扫码已经失效，请重新扫码"), //

    // shop-server
    SHOP_GOODS_BARCODE_IS_EXIST(880001, "条形码已存在"), //
    SHOP_GOODS_TYPE_SALES_TIME_OVERL_APPING(880002, "销售时间存在交叉"), //
    SHOP_GOODS_STOCKS_OPT_TYPE_ERROR(880003, "库存操作类型错误"), //
    SHOP_GOODS_TYPE_DELETE_ERROR_BY_GOODS(880004, "该商品类型下存在商品数，不能删除"), //
    SHOP_ORDER_GOODS_NUM_ERROR(880005, "该笔订单已全额退款!"), //
    SHOP_ORDER_REFUND_NUM_ERROR(880006, "退款订单数错误!"), //
    SHOP_GOODS_PROHIBITED_CASH(880007, "该商品不能使用现金余额购买!"), //
    SHOP_GOODS_NOT_IN_SALES_TIME(880008, "该商品不在销售时间范围内!"), //
    SHOP_GOODS_STOCKS_NOT_ENOUGH(880009, "该商品库存数不足!"), //
    SHOP_GOODS_BUY_NOT_USE_CASH(880011, "客户端支付不能使用现金!"), //
    SHOP_GOODS_SOURCE_ERROR(880012, "请求来源错误!"), //
    SHOP_GOODS_ORDER_TOTAL_ERROR(880013, "订单金额错误!"), //
    SHOP_GOODS_DELETE_ERROR_BY_CASHIER(880014, "该商品还有收银台库存数，不能删除"), //

    SHOP_GOODS_DELETE_ERROR_BY_STOREAGE(880015, "该商品还有仓库库存数，不能删除"), //

    SHOP_CONFIG_ERROR(880016, "商超配置异常!"), //

    SHOP_CONFIG_PLACE_NOT_SUPPORT(880017, "场所未开通商超功能!"), //

    SHOP_CONFIG_PROHIBITED_CASH(880018,"场所不支持商品余额支付!"), //
    SHOP_STORAGE_TYPE_NONSUPPORT(880019,"仓库模式不支持!"), //
    SHOP_CONFIG_CUSTOMIZED_PAY_LIMIT(880020,"场所超出商超配置的自定义收款限额!"), //

    SHOP_SUPPLIER_NOT_EXIST(880021,"供应商信息不存在!"), //

    SHOP_GOODS_BARCODE_REPETITION(880019, "条形码重复!"), //
    SHOP_GOODS_NOT_EXIST(880021, "商品信息不存在!"), //

    SHOP_RACK_NOT_EXIST(880023, "货架信息不存在!"), //

    SHOP_RACK_STORAGE_DATA_ERROR(880025, "商品货架库存数据异常!"), //

    // 认证异常
    NO_KEY(401001, "ClientId或者ClientSecret不存在"), //
    BAD_KEY(401002, "ClientId或者ClientSecret错误"), //
    KEY_EXIRED(401003, "ClientId或者ClientSecret已失效"), //
    AUTH_USER_EXIRED(401004, "用户已经失效"), //
    AUTH_USER_BIZ_ERROR(401005, "Token所属用户没有当前业务权限"), //
    NO_TOKEN(401101, "请在请求中设置TOKEN"), //
    BAD_TOKEN(401102, "TOKEN错误，请检查是否正确"), //
    TOKEN_EXIRED(401103, "TOKEN已过期，请重新获取"), //
    BAD_SIGN(401104, "签名错误"), //
    ILLEGAL_PATH(401105, "非法路径"), //
    ILLEGAL_PARAM(401106, "非法参数"), //
    FREQUENT_REQUESTS(401107, "请求过于频繁"), //
    EXPIRED_TIMESTAMP(401108, "请求已失效"), //
    FREQUENT_METHOD_ERROR(401109, "请求方式错误"), //

    // 授权异常
    NO_RIGHT(403001, "无权操作"), //

    ERROR_IP(403003, "IP地址受限"), //

    // 资源异常
    NO_SERVICE(404001, "服务调用失败"), //
    RES_EXISTS(404002, "资源已存在"), //
    RES_ERROR(404003, "资源有误"), //
    NOT_FOUND(404404, "没有符合要求的数据"), //
    NO_OPERATE_RIGHT(404005, "无操作权限，请联系管理员"), //

    // 未指明的异常
    UNSPECIFIED(500001, "系统异常,请稍后再试"), //
    INTERNAL_EXCEPTION(500002, "系统内部错误，请稍后再试"), //
    SYSTEM_ERROR(500003, "系统异常，请联系系统管理员"),

    // 实名认证
    AUTH_SUCC(900000, "认证成功"), //
    BAD_ID_NUMBER(900001, "身份证号错误"), //
    BAD_REGION_CODE(900002, "地区编码错误"), //
    ID_NUMBER_NO_RECORD(900003, "身份证号没有记录"), //
    INCONSISTENT(900004, "姓名身份证号不一致"), //
    BAD_PHOTO_OR_VIDEO(900005, "人像不一致"), //
    NO_AUTH_RULES(900006, "认证规则不存在"), //
    NO_AUTH_INFO(900007, "认证信息不存在"), //
    NO_PHOTO(900008, "照片信息不存在"), //
    VIDEO_TOO_LARGE(900009, "视频文件过大"), //
    LIVENESS_CHECK_SUCC(900010, "活体检测成功"), //
    NO_FACE(900011, "未检测到人脸特征"), //
    LIVENESS_FAILED(900012, "未检测到活体特征"), //
    NO_AUTH_PHOTO(900013, "证件照片不存在"), //
    PHOTO_TOO_LARGE(9000014, "照片文件过大"), //
    PHOTO_TOO_SMALL(9000015, "照片文件过小"), //
    PHOTO_FORMAT_ERROR(900016, "照片格式错误"), //
    IDCARD_NOT_RECOGNIZED(900017, "身份证照片无法识别"), //
    EXPIRED_AUTH_INFO(900018, "认证信息已失效"), //
    AUTH_RESULT_IS_NULL(900019, "认证返回结果为空"), //
    FACEID_NOT_MATCH_IDNUMBER(900020, "人脸与身份信息不匹配"),

    HIGHMAX_NOT_MATCH_IDNUMBER(900032, "认证信息或手机号不匹配"), //

    HIGHMAX_PHONE_ERROR(900033, "认证手机号不存在或异常"), //

    AUTH_FAIL(900099, "认证失败"), //

    PIXTALKS_SERVER_ERROR(900021, "图语认证失败"), //
    SENSETIME_SERVER_ERROR(900022, "商汤认证失败"), //
    LINKFACE_SERVER_ERROR(900023, "今始认证失败"), //
    JIXIN_SERVER_ERROR(900024, "吉信认证失败"), //
    DABBY_SERVER_ERROR(900025, "大白认证失败"), //
    EID43_SERVER_ERROR(900026, "EID43认证失败"), //
    TCREDIT_SERVER_ERROR(900027, "天创认证失败"), //
    PAY_REALNAME_AUTH_FEE(900028, "请支付实名认证费用"), //
    HIGHMAX_SERVER_ERROR(900029, "海脉云认证失败"), //

    CTID_SERVER_ERROR(900031, "CTID认证失败"), //

    LIVENESS_CHECK_FAIL(900033, "检测失败，请重试!"), //

    // 第三方
    THIRD_AUTHORITY_NOT_FOUND(930001, "账号接口权限未找到"), //
    THIRD_NO_AUTHORITY(930002, "接口权限不足"), //
    THIRD_SERVER_ERROR(930003, "第三方接口错误"), //
    THIRD_CHECK_FAIL(930004, "第三方验证失败"), //
    // 权限不匹配
    THIRD_AUTHORITY_NOT_MATCH(930005, "权限不匹配"), //


    // 企业信息验证
    IDAUTH_COMPANY_MATCH(900100, "企业验证成功"), //
    IDAUTH_COMPANY_NOT_MATCH(900101, "企业验证失败"), //
    IDAUTH_COMPANY_IDNUMBER_NOT_MATCH(900102, "身份证号不匹配"), //
    IDAUTH_COMPANY_LEGALPERSONNAME_NOT_MATCH(900103, "企业法人不匹配"), //
    IDAUTH_COMPANY_COMPANYNAME_NOT_MATCH(900104, "企业名称不匹配"), //
    IDAUTH_COMPANY_CREDITCODE_NOT_MATCH(900105, "企业标识不匹配"), //

    // 对接微信相关
    GET_TOKEN_FAIL(940002, "获取Token失败"), //
    GET_TICKET_FAIL(940003, "获取ticket失败"), //
    GET_QRCODE_FAIL(940004, "生成二维码失败"), //
    GET_PLACE_CONFIG_FAIL(940005, "获取场所配置失败"), //
    GET_PLACE_INFO_FAIL(940006, "获取场所信息失败"), //
    PLACE_NOT_SUPPORT_ONLINE_TOPUP(940007, "该场所不支持在线充值"), //
    TEMPORARY_NOT_SUPPORT_ONLINE_PAY(940008, "临时卡不支持在线支付"), //
    PLACE_NOT_REGISTER_PAY_AMOUNT(940009, "该场所暂未注册为支付商户"), //
    GET_CARD_INFO_FAIL(940010, "无法获取计费卡信息"), //
    USER_CREATE_FAIL(940012, "创建用户失败"), //

    USER_INFO_WRONG(940013, "用户信息未找到"),

    // 业主相关
    REGISTER_FAIL(950002, "注册失败"), //
    ACCOUNT_ERROR(950003, "账号错误"), //
    PASSWORD_ERROR(950004, "密码错误"), //
    PASSWORD_UPDATE_FAIL(950005, "密码修改失败"), //
    PLACE_NOT_NULL(950006, "场所不能为空"), //
    BAD_CARD_TYPE_NAME(950007, "该卡类型名称已存在"), //
    NO_ISREGISTERED(950008, "未找到小微商户报备状态"), //
    CREATE_ORDER_FAILED(950009, "创建订单失败"), //
    NO_RATE(950010, "当前场所没有设置续费费率信息"), //

    // 导入相关
    WRONG_PLACEID(950011, "导入数据中有场所Id错误数据"), //
    EMPTY_DATA(950012, "导入数据为空"), //
    DUPLICATION_DATA_IN_EXCEL(950013, "表格中数据重复"), //
    DUPLICATION_DATA_WITH_DB(950014, "和已有数据重复"), //
    OVERSIZE_DATA(950015, "导入数据单次不超过一千条"), //
    WRONG_AREA_NAME(950016, "区域名称错误"), //
    FILE_OVER_MAX_SIZE(950017, "文件大小超过限制"), //
    FILE_FORMAT_ERROR(950018, "文件格式错误"), //
    FILE_DUPLICATION(950019, "文件重复"), //
    FILE_UPLOAD_FAIL(950024, "文件上传失败"), //
    FILE_UPLOAD_SERVICE_EXCEPTION(950025, "文件上传服务异常"),
    VERSION_NUMBER_ERROR(950021, "版本号已存在"), //
    AREA_BIND_CLIENT(950022, "该区域已绑定客户端"), //
    FILE_FORMAT_NOT_XLSX(950023, "文件格式类型必须为xlsx"), //
    USER_LOGIN_ERROR(940011, "用户未登录"),

    USER_TEMPORARY_CARD_SETTLE_ACCOUNTS_ERROR(940014, "临时卡请前往收银台结账"), //

    USER_BAND_PROFILE_REPETITION(940015, "您已经绑定了该门店"),
    USER_BAND_PROFILE_NOT_SUPPORTED(940016, "此功能需要标准版及以上才能使用，请先升级版本"),
    PASSWORD_NOT_MEET_REQUIREMENTS(950023, "密码不符合要求，请修改。密码长度为至少6位，且必须同时包含至少一个字母和一个数字，且不能为默认密码。"),

    USER_MINIAPP_ENTRANCE_DISUSE(950025, "不支持当前程序上机，请返回微信扫码！"),

    USER_LOGIN_PASS_LENGTH_ERROR(950026, "密码长度至少为6位"),


    // market-server
    MARKET_COUPON_REPEAT(960001, "优惠券名称重复！"),

    MARKET_COUPON_DATA_ERROR(960003, "优惠券数据异常！"),

    MARKET_COUPON_DATA_NOT_FOUND(960005, "优惠券类型不存在！"),

    MARKET_COUPON_DATA_DISABLED(960007, "该优惠券已禁用！"),

    MARKET_STORE_STATUS_ERROR(960009, "未提交匹配任务！"),

    MARKET_STORE_NOT_BIND(960011, "商户未绑定第三方账户！"),

    MARKET_STORE_REPETITIVE_BINDING(960013, "商户已绑定第三方场所！"),

    MARKET_COUPON_NOT_FOUND(960015, "未识别到有效数据！"),

    MARKET_COUPON_NOT_BING(960017, "抖音优惠券未绑定系统优惠券类型！"),
    MARKET_COUPON_INVALID(960019, "优惠券未到生效时间！"),
    MARKET_COUPON_LOSE_EFFICACY(960021, "优惠券已失效！"),
    MARKET_COUPON_EXCEED_THE_LIMIT(960023, "已达到使用最大数！"),
    MARKET_COUPON_EXIST_FUTURE_PACKAGE_RULE_HINT(960025, "存在未使用的预包时,请先取消该包时再使用优惠券!"),

    MARKET_COUPON_PACKAGE_RULE_HINT(960026, "存在使用中的包时,请等待包时结束再使用优惠券!"),
    MARKET_COUPON_PACKAGE_RULE_NOT_FOUND(960027, "包时规则不存在!"),

    MARKET_STORE_IS_BIND(960028, "商户已绑定第三方账户！请取消绑定后再重新绑定"),
    MARKET_COUPON_PACKAGE_TIME_VERIFY(960029, "优惠券包时未到开始时间！"),

    MARKET_GOODS_SUPPLIERS_NOT_FOUND(960030, "供应商未找到！"),

    MARKET_SHOP_NAME_REPETITION(960031, "名称重复！"),
    MARKET_ORDER_NUM_CREATE_EXCEPTION(960032, "生成单号异常！"),
    MARKET_GOODS_PURCHASE_ORDER_NOT_FOUND(960033, "采购订单未找到！"),

    MARKET_GOODS_RECEIPT_NOT_FOUND(960034, "入库单未找到！"),
    MARKET_STORAGE_GOODS_NOT_FOUND(960035, "库存信息未找到！"),

    MARKET_GOODS_RECEIPT_REPEAT(960036, "商品入库过于频繁，请稍后再试！"),

    MARKET_STORAGE_GOODS_LOW_STORE(960037, "当前库存不足,请调整数量后再操作"),
    MARKET_RETURN_GOODS_REPEAT(960038, "商品退货过于频繁，请稍后再试！"),

    MARKET_RETURN_GOODS_NOT_FOUND(960039, "退货单未找到！"),

    MARKET_REPORT_LOSS_REPEAT(960040, "商品报损过于频繁，请稍后再试！"),

    MARKET_SHOP_GOODS_BARCODE_REPETITION(960037, "条形码重复!"), //

    MARKET_SHOP_GOODS_NOT_EXIST(960039, "商品信息不存在!"), //

    MARKET_SHOP_RACK_NOT_EXIST(960041, "货架信息不存在!"), //

    MARKET_SHOP_RACK_STORAGE_DATA_ERROR(960043, "商品货架库存数据异常!"), //

    MARKET_SHOP_RACK_STORAGE_NUMBER_DEFICIENCY(960045, "商品库存不足!"), //

    MARKET_SHOP_CONFIG_PLACE_NOT_SUPPORT(960047, "场所未开通商超功能!"), //

    MARKET_SHOP_CONFIG_STORAGE_TYPE_NOT_SUPPORT(960049, "请先开启多仓库模式!"), //

    MARKET_MAIN_SHELF_DISABLE(960051, "主吧台不支持当前操作!"), //

    MARKET_SHOP_GOODS_TYPE_DELETE_ERROR_BY_GOODS(960053, "该商品类型下存在商品数，不能删除"), //

    MARKET_SHOP_GOODS_STORAGE_MOVE_CONFLICT(960055, "上下架操作冲突!"), //

    MARKET_REPORT_LOSS_NOT_FOUND(960056, "报损单未找到！"),

    MARKET_LIMITED_TIME_DISCOUNT_NOT_FOUND(960057, "限时折扣未找到！"),

    MARKET_BUY_GIFTS_NOT_FOUND(960058, "买赠信息未找到！"),

    MARKET_BUY_GIFTS_REPEAT(960059, "新增买赠过于频繁，请稍后再试！"),

    MARKET_LIMITED_TIME_DISCOUNT_REPEAT(960060, "新增限时折扣过于频繁，请稍后再试！"),

    MARKET_DISCOUNT_RATE_ERROR(960061, "指定折扣值错误，请输入0-10的正数"),

    MARKET_STOP_TAKING_ORDERS(960063, "暂停接单，请联系收银台"),


    MARKET_GOODS_NOT_IN_SALES_TIME(960065, "该商品不在销售时间范围内!"), //

    MARKET_GOODS_STOP_SELLING(960067, "商品已停售!"), //

    MARKET_SHOP_GOODS_ORDER_TOTAL_ERROR(960069, "订单总金额错误!"), //

    MARKET_CARD_TYPE_NONSUPPORT(960071, "会员类型不支持!"), //
    MARKET_SHOP_ORDER_REFUND_NUM_ERROR(960073, "退款订单数错误!"), //

    MARKET_SHOP_CONFIG_PROHIBITED_CASH(960075,"场所不支持商品余额支付!"), //

    MARKET_SHOP_NOT_SUPPORT_ONLINE_PAY(960077, "商品不支持在线支付"), //

    MARKET_GOODS_PROHIBITED_CASH(960077,"商品不支持余额本金支付!"), //
    MARKET_GOODS_PROHIBITED_PRESENT(960079,"商品不支持余额奖励支付!"), //

    MARKET_SHOP_NOT_SUPPORT_PAY(960081, "商品不支持余额支付"), //

    MARKET_MEALS_GOODS_ERROR(960083,"套餐商品错误!"), //

    MARKET_SHOP_GOODS_NAME_REPETITION(960085,"商品名称重复!"), //

    MARKET_BARCODE_REPETITION(960086,"新增商品库过于频繁，请稍后再试！"), //

    MARKET_GOODS_STOCKTAKING_REPEAT(960087, "商品盘点过于频繁，请稍后再试！"),

    MARKET_GOODS_STOCKTAKING_NOT_FOUND(960088, "商品盘点未找到！"),

    MARKET_MEMBER_EVENT_TYPE_ERROR(960089, "活动信息不存在！"),

    MARKET_MEMBER_EVENT_REPEATED_DRAW(960090, "活动商品已领取！"),

    MARKET_BUYGIFTS_STATUS_ERROR(960091, "买赠活动已停止！"),

    MARKET_BUYGIFTS_GOODS_LIST_EMPTY(960092, "买赠活动无附带商品！"),

    MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD(960093, "当前时间不在可销售时间内！"),

    MARKET_LIMIT_SALES_TYPE_ERROR(960094, "包时购买次数到达上限！"),

    MARKET_PACKAGE_RULE_FORBIDDEN(960095, "包时套餐禁止使用！"),

    MARKET_PACKAGE_RULE_PAY_TYPE_NONSUPPORT(960096, "包时套餐不支持该支付方式！"),

    MARKET_PACKAGE_RULE_TYPE_ERROR(960097, "包时套餐类型错误！"),
    MARKET_SHOP_ORDER_GOODS_NUMBER_MAX_ERROR(960098, "购买数量超出限制！"),
    MARKET_INTERNET_FEE_EMPTY(960099, "网费充送数据未找到！"),

    MARKET_SHOP_COUPON_NONSUPPORT(960099, "该套餐不支持优惠券！"),

    MARKET_SHOP_ORDER_AREA_ERROR(960100, "订单商品购买区域不允许！"),

    MARKET_NO_CHOOSE_GOODS(960101, "请选择商品！"),
    MARKET_CANNOT_OPERATE(960102, "不可操作！"),

    MARKET_COUPON_DELETE_ERROR(960103, "存在未使用优惠券，请先作废再删除！"),
    MARKET_GOODS_CATEGORY_TYPE_NOT_FOUND(960104, "商品种类不存在！"),
    MARKET_ORDER_TYPE_REFUND_NONSUPPORT(960105, "订单类型不支持退款！"),
    MARKET_PLACE_MEITUAN_NOT_BOUND(960106, "场所未绑定美团！"),
    MARKET_PLACE_MEITUAN_AUTH_LOS_EFFICACY(960106, "场所美团授权已失效！"),

    MARKET_ORDER_REFUND_TO_EXCEED_THE_TIME_LIMIT(960107, "订单退款超过期限！"),
    MARKET_GOODS_DELETE(960108, "选择商品已删除！"),
    MARKET_PACKAGE_RULE_LIMIT_LOGIN_BUY(960109, "该包时限制上机后才能购买！"),
    MARKET_GOODS_NONSUPPORT(960110, "订单商品类型不支持！"),
    MARKET_GOODS_NAME_NOT_EMPTY(960111, "商品名称不能为空"),
    MARKET_GOODS_CATEGORY_NOT_EMPTY(960112, "商品类型不能为空"),

    MARKET_BUY_GIFTS_VERIFY_RECORD_NOT_FOUND(960113, "核销记录未找到"),

    MARKET_RENT_CONFIG_NOT_FOUND(960114, "租赁配置信息未找到！"),
    MARKET_BUY_GIFTS_GOODS_IS_USED(960115, "核销商品已使用"),
    MARKET_BUY_GIFTS_PACKAGE_RULE_NOT_ALLOW(960116, "买赠设置包时规则冲突"),
    MARKET_STOP_RENT_ORDERS(960117, "暂停租赁设备，请联系收银台"),
    MARKET_ORDER_STATUS_NOT_SUPPORT_SETTLEMENT(960118, "当前订单状态不能结算"),
    MARKET_RENT_ORDER_SETTLEMENT_ERROR(960119, "租赁订单结算金额错误!"), //
    MARKET_RENT_BLACK_LIST(960120, "用户已被拉入租赁黑名单！"),
    MARKET_DEPOSIT_GOODS_NOT_FOUND(960121, "押金订单商品不存在！"),
    MARKET_DEPOSIT_IN_FREE_TIME(960122, "该租赁订单处于免费时长内，无需支付租金");

    /**
     * 业务码
     */
    private final int code;

    /**
     * 描述
     */
    private final String message;

    /**
     * @param code    错误码
     * @param message 描述
     */
    private ServiceCodes(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据编码查询枚举。
     *
     * @param code 编码。
     * @return 枚举。
     */
    public static ServiceCodes getByCode(int code) {
        for (ServiceCodes value : ServiceCodes.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return UNSPECIFIED;
    }

    /**
     * 枚举是否包含此code
     *
     * @param code 枚举code
     * @return 结果
     */
    public static Boolean contains(int code) {
        for (ServiceCodes value : ServiceCodes.values()) {
            if (code == value.getCode()) {
                return true;
            }
        }
        return false;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}