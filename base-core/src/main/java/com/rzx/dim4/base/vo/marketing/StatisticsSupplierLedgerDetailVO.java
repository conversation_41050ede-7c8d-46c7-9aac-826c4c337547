package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class StatisticsSupplierLedgerDetailVO extends AbstractEntityBO {
    private Long id;
    private LocalDateTime created; // 账号创建时间
    private String placeId; // 场所ID
    private String supplierId; // 供应商id
    private String supplierName; // 供应商名称
    private String orderNum; // 单编号
    private int goodsKindTotal; // 商品种类数目
    private int goodsTotal; // 商品总数目
    private int amount; // 应付金额（单位分）
    private int payAmount; // 实际支付金额（单位分）
    private int status; // 订单状态0待入库，1已入库
    private int payType; // 1.采购：支付方式：0现金，1支付宝，2微信，3挂账，4其他   2。退货: 支付方式：0现金，1支付宝，2微信，3挂账，4上打下，5其他
    private String type;



}
