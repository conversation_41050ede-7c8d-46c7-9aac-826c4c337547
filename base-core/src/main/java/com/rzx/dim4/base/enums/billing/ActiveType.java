package com.rzx.dim4.base.enums.billing;

import com.rzx.dim4.base.enums.Convertor.AbstractEnumConverter;
import com.rzx.dim4.base.enums.Convertor.PersistEnum2DB;

import java.util.Map;
import java.util.TreeMap;

/**
 * 计费卡激活方式
 */
public enum ActiveType implements PersistEnum2DB<Integer> {

    SWIPE_ID_CARD(0,"ID卡"), // 通过ID卡刷卡器刷任子行发行的ID卡进行激活或开卡
    MANUAL_INPUT(1,"实名手输"), // 通过实名的手工录入输入框输入证件号进行激活或开卡
    SWIPE_ID_NUMBER_1(2,"刷身份证"), // 通过刷任子行实名支持的二代证刷卡器进行激活或开卡
    SWIPE_ID_NUMBER_2(3,"刷身份证"), //  通过任子行实名支持的扫描仪扫描证件进行激活或开卡
    MOBILE(8,"手机号"), // 手机业务,手机号绑定证件号,证件号作为上网卡号激活或开卡
    FINGERPRINT_1(14,"指纹"), // 通过任子行实名支持的纹设备录指纹绑定证件号,证件号作为卡号,可通过指纹激活或开卡
    APP_LONG_GUAN_JIA(15,"龙管家APP"), // 通过龙管家APP扫描二维码激活或开卡
    APP_SHI_MING_BAO(16,"实名宝APP"), // 通过实名宝APP扫描二维码激活或开卡
    APP_JWELL(17,"九威APP"), // 通过九威APP扫描二维码激活或开卡
    APP_WAN_XIANG(18,"万象APP"), // 通过万象APP扫描二维码激活或开卡
    SWIPE_ID_NUMBER_3(19,"人脸"), // 通过任子行实名手工录入证件号,或刷二代证进行人脸识别激活或开卡
    WECHAT_RZX(20,"公众号"), // 通过关注任子行公众号,绑定账户后,扫描二维码激活或开卡
    FINGERPRINT_2(21,"指纹"), //  通过湖南长沙斯雨指纹激活或开卡
    FACE(22,"人脸识别"), // 人脸识别人工审核
    APP_YI_SHANG_WANG(23,"易上网APP"), // 易上网APP
    APP_A_LA_JING_CHA(24,"阿拉警察APP"), // 阿拉警察APP
    APP_YI_SHANG_WANG_SCAN(25,"易上网APP"), // 易上网APP扫码上机
    APP_YI_SHI_MING(26,"易实名APP"), // 易实名APP
    APP_JI_LIN_GONG_AN(27,"吉林公安APP"), // 吉林公安APP
    APP_E_WANG_TONG(28,"e网通APP"), // e网通APP
    APP_GUI_ZHOU_GONG_AN(29,"贵州公安APP"), // 贵州公安APP
    NETWORK_CERTIFICATE(30,"网证"), // 网证CTID扫码上机
    NETWORK_CERTIFICATE_1(31,"网证"), //  新疆网证App
    SWIPE_ID_NUMBER_4(32,"刷身份证"), // 湖南电子身份证
    SWIPE_ID_NUMBER_5(33,"刷身份证"), // 湖北电子身份证
    HOTEL(34,"酒店认证"), // (选住)酒店认证
    SWGJ_MANUAL_INPUT(35,"计费手输"), // 计费手输
    SELF_MACHINE_SHANG_JI_TANG(36,"上机堂自助机"), // 上机堂自助机
    SELF_MACHINE_DU_DU_NIU_CASHIER(37,"嘟嘟牛自助收银机"), // 嘟嘟牛自助收银机
    SWIPE_ID_NUMBER_6(38,"刷身份证"), // 河南电子身份证
    ALI_IOT(39,"支付宝IOT"), // 支付宝IOT
    SELF_MACHINE_SI_NAI_PU(40,"斯耐浦自助机"), // 斯耐浦自助机
    THIRD(41,"第三方运营软件"), // 第三方运营软件
    SELF_MACHINE_DU_DU_NIU(42,"嘟嘟牛自助机"), // 嘟嘟牛自助机
    SWIPE_ID_NUMBER_7(43,"刷身份证"), // 四川电子身份证
    WAN_JIA_JI_FEI(44,"万家计费"), // 万家计费
    CHU_ZHOU_WAN_XIANG(45,"滁州万象一体机"), //  滁州万象一体机
    YUE_JU_MA(46,"粤居码"), // 粤居码
    SELF_MACHINE_JWELL(47,"九威自助机"), // 九威自助

    SWGJ_WECHAT(51,"计费公众号"), //  计费公众号
    MINI_APP(52, "计费小程序"), // 小程序

    // 128之后 自定义类型
    RZX_REALNAME(129,"任子行实名扣点"), // 任子行实名扣点
    
    ALIPAY_MINI_APP(131, "支付宝小程序"), // 支付宝小程序

    JIANGXI_ID_NUMBER(54,"江西电子身份证"), // 江西电子身份证
    ;

    private final int value;
    private final String display;

    private ActiveType(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }


    public static Map<String,String> getNeedActiveTypes() {
        TreeMap<String,String> map = new TreeMap<>();
        map.put(RZX_REALNAME.display,String.valueOf(RZX_REALNAME.value)); // 任子行实名扣点
        map.put(SWGJ_MANUAL_INPUT.display,String.valueOf(SWGJ_MANUAL_INPUT.value)); // 计费手输
        // 手输
        map.put(MANUAL_INPUT.display,String.valueOf(MANUAL_INPUT.value)); // 手输
        // 刷身份证类型
        String swipeIdNumber = SWIPE_ID_NUMBER_1.value + "," + SWIPE_ID_NUMBER_2.value + "," + SWIPE_ID_NUMBER_3.value
                + "," + SWIPE_ID_NUMBER_4.value + "," + SWIPE_ID_NUMBER_5.value + "," + SWIPE_ID_NUMBER_6.value
                + "," + SWIPE_ID_NUMBER_7.value;
        map.put(SWIPE_ID_NUMBER_1.display,swipeIdNumber); // 刷身份证

        // 网证类型
        String networkCertificate = NETWORK_CERTIFICATE.value + "," + NETWORK_CERTIFICATE_1.value;
        map.put(NETWORK_CERTIFICATE.display,networkCertificate); // 网证

        map.put(SELF_MACHINE_JWELL.display,String.valueOf(SELF_MACHINE_JWELL.value)); // 九威自助机
        map.put(APP_YI_SHANG_WANG.display,String.valueOf(APP_YI_SHANG_WANG.value)); // 易上网APP

        // map.put(ALI_IOT.display,String.valueOf(ALI_IOT.value)); // 支付宝IOT
        map.put(SWGJ_WECHAT.display,String.valueOf(SWGJ_WECHAT.value)); // 计费公众号
        map.put(MINI_APP.display,String.valueOf(MINI_APP.value)); // 计费小程序
        return map;
    }

    /**
     * 根据索引值获取实例对象
     *
     * @param index
     * @return
     */
    public static ActiveType getActiveTypes (int index) {
        for (ActiveType activeType : ActiveType.values()) {
            if (activeType.getValue() == index) {
                return activeType;
            }
        }
        return null;
    }

    @Override
    public Integer getData() {
        return value;
    }

    public static class Converter extends AbstractEnumConverter<ActiveType, Integer> {
        public Converter() {
            super(ActiveType.class);
        }
    }
}
