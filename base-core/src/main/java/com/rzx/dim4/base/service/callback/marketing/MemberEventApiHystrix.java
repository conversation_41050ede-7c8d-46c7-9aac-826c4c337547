package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.MemberEventBO;
import com.rzx.dim4.base.bo.marketing.MemberEventPushRecordBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MemberEventApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025年02月11日 14:26
 */
@Slf4j
@Component
public class MemberEventApiHystrix implements MemberEventApi {
    @Override
    public void schedulerPushEvent() {
        log.error("接口异常，MemberEventApiHystrix,schedulerPushEvent");
    }

    @Override
    public GenericResponse<ObjDTO<MemberEventPushRecordBO>> queryEvent(String placeId, String pushId) {
        log.error("接口异常，queryEvent(placeId={},  pushId={})", placeId, pushId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MemberEventPushRecordBO>> receiveEvent(String requestTicket, String placeId, String idNumber, String pushId) {
        log.error("接口异常，receiveEvent(placeId={}, idNumber={}, pushId={})", placeId,idNumber, pushId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
