package com.rzx.dim4.base.service.callback.user;

import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.user.Dim4UserApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/7/9
 **/
@Slf4j
@Service
public class Dim4UserApiHystrix implements Dim4UserApi {
    @Override
    public GenericResponse<?> deleteExpireAuthImagesInfo(int days) {
        log.error("接口异常:::deleteExpireAuthImagesInfo，days:{}", days);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<Dim4UserBO>> getInfo(String idNumber) {
        log.error("接口异常:::getInfo，idNumber:{}", idNumber);

        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<Dim4UserBO>> getDim4UserInfoList(List<String> idNumbers) {
        log.error("接口异常:::getDim4UserInfoList");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<Dim4UserBO>> saveUser4Iot(String requestTicket, String name, String idNumber,
                                                            String authImageKey, String authImageMd5) {
        log.error("接口异常:::saveUser4Iot, requestTicket:{},name:{},idNumber:{},authImageKey:{}, authImageMd5:{}", requestTicket,
                name, idNumber, authImageKey, authImageMd5);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
