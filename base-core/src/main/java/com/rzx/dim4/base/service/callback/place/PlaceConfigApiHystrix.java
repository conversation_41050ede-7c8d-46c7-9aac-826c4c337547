package com.rzx.dim4.base.service.callback.place;

import java.util.List;

import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceConfigApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * 
 * @version 1.00
 * @since 2024-7-30
 * <AUTHOR>
 * 
 *         Modified History:
 *
 */
@Slf4j
@Service
public class PlaceConfigApiHystrix implements PlaceConfigApi {
	@SuppressWarnings("rawtypes")
	@Override
	public GenericResponse<SimpleDTO> batchSavePlaceConfig(String requestTicket,
			List<PlaceConfigBO> confList) {
		log.error("接口异常，batchSavePlaceConfig(requestTicket={})", requestTicket);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
}
