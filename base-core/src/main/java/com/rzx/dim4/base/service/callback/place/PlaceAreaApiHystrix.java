package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceAreaBriefBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceAreaApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @since 2025-05-20
 **/
@Slf4j
@Service
public class PlaceAreaApiHystrix implements PlaceAreaApi {

    @Override
    public GenericResponse<ListDTO<PlaceAreaBriefBO>> findByPlaceId(String placeId) {
        log.error("findByPlaceId 接口异常placeId={}", placeId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
