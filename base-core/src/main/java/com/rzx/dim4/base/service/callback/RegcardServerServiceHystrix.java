package com.rzx.dim4.base.service.callback;

import com.rzx.dim4.base.bo.billing.RegcardVerificationCodeBO;
import com.rzx.dim4.base.bo.regcard.RegLogBO;
import com.rzx.dim4.base.bo.regcard.RegcardBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.regcard.ConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.RegcardServerService;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年6月16日 上午10:58:48
 */
@Slf4j
@Service
public class RegcardServerServiceHystrix implements RegcardServerService {

    @Override
    public GenericResponse<ObjDTO<ConfigBO>> getConfig(String placeId) {
        log.error("接口异常::: getConfig(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveConfig(String requestTicket, String placeId, String serverGroupName) {
        log.error("接口异常::: saveConfig(placeId:::{}, serverGroupName:::{})", placeId, serverGroupName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> onRegist(String requestTicket, String placeId, String auditId, String areaId,
                                               String idName,String idNumber, String mobile, String validDays,String shiftId,int price) {
        log.error("接口异常::: onRegist(requestTicket:::{},placeId:::{}, auditId:::{}, areaId:::{}, idNumber:::{}, mobile:::{}, validDays:::{}, shiftId:::{}, price:::{})",
                requestTicket,placeId, auditId, areaId, idNumber, mobile, validDays, shiftId, price);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> OnRegistByPhysicalCard(String requestTicket, String placeId, String auditId, String areaId, String idNumber, String mobile, String cardNumber) {
        log.error("接口异常::: OnRegistByPhysicalCard(placeId:::{}, auditId:::{}, areaId:::{}, idNumber:::{}, mobile:::{}, cardNumber:::{})",
                placeId, auditId, areaId, idNumber, mobile, cardNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> onActive(String requestTicket, String placeId, String auditId, String areaId, String idNumber) {
        log.error("接口异常::: onActive(placeId:::{}, auditId:::{}, areaId:::{}, idNumber:::{})", placeId, auditId, areaId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> queryUnusedCount(String placeId, String validDays) {
        log.error("接口异常::: queryUnusedCount(placeId:::{}, validDays:::{})", placeId, validDays);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<RegcardBO>> PageQueryRegcardByPlaceId(String placeId, String cardNumber, int startRow, int endRow) {
        log.error("接口异常::: PageQueryRegcardByPlaceId(placeId:::{}, cardNumber:::{},startRow:::{},endRow:::{})", placeId, cardNumber,startRow,endRow);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<RegLogBO>> PageQueryReglogByPlaceId(String placeId, String idNumber, int startRow, int endRow) {
        log.error("接口异常::: PageQueryReglogByPlaceId(placeId:::{}, idNumber:::{},startRow:::{},endRow:::{})", placeId, idNumber,startRow,endRow);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> OnUnbind(String requestTicket, String placeId, String auditId, String areaId, String idNumber) {
        log.error("接口异常::: OnUnBind(requestTicket:::{}, placeId:::{},auditId:::{},areaId:::{},idNumber:::{})", requestTicket, placeId,auditId,areaId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> queryPassword(String placeId, String auditId, String areaId, String idNumber, String mobile) {
        log.error("接口异常::: queryPassword(placeId:::{},auditId:::{},areaId:::{},idNumber:::{},mobile:::{})", placeId,auditId,areaId,idNumber,mobile);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> importRegcard(String requestTicket, List<RegcardBO> regcardBOs) {
        log.error("接口异常::: importRegcard(requestTicket:::{},regcardBOs:::{})", requestTicket,regcardBOs);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteRegcard(String requestTicket, String swgjPlaceId) {
        log.error("接口异常::: deleteRegcard(requestTicket:::{},swgjPlaceId:::{})", requestTicket,swgjPlaceId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse <ListDTO<RegLogBO>> QueryRegcardLogByPlaceIdAndShiftId(String placeId, String shiftId) {
        return null;
    }

    @Override
    public GenericResponse<PagerDTO<RegLogBO>> PageQueryRegcardLogByPlaceIdAndShiftId(String placeId, String shiftId, int startRow, int endRow,String idName,String idNumber) {
        return null;
    }

    @Override
    public GenericResponse<ListDTO<RegLogBO>> queryRegcardLogByPlaceIdAndDate(String placeId, String shiftId, String startDateTime, String endDateTime) {
        log.error("接口异常::: QueryRegcardLogByPlaceIdAndDate(placeId:::{},shiftId:::{},startDateTime:::{},endDateTime:::{})", placeId,shiftId,startDateTime,endDateTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<RegLogBO>> PageQueryRegcardUsedByParam(String placeId, String cardNumber, String cardType, String idNumber, String idName, String mobile, String shiftId, int startRow, int endRow, String startTime, String endTime) {
        log.error("接口异常::: PageQueryRegcardUsedByParam(placeId:::{},cardNumber:::{},cardType:::{},idNumber:::{},idName:::{},mobile:::{},shiftId:::{},startRow:::{},endRow:::{},startTime:::{},endTime:::{})", placeId,cardNumber,cardType,idNumber,idName,mobile,shiftId,startRow,endRow,startTime,endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<RegcardBO>> PageQueryRegcardUnusedByParam(String placeId, String cardNumber, String cardType, int startRow, int endRow) {
        log.error("接口异常::: PageQueryRegcardUnusedByParam(placeId:::{},cardNumber:::{},cardType:::{},startRow:::{},endRow:::{})", placeId,cardNumber,cardType,startRow,endRow);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<RegLogBO>> QueryRegcardUsedByParam(String placeId, String cardNumber, String cardType, String idNumber, String idName, String mobile, String shiftId, String startTime, String endTime) {
        log.error("接口异常::: QueryRegcardUsedByParam(placeId:::{},cardNumber:::{},cardType:::{},idNumber:::{},idName:::{},mobile:::{},shiftId:::{},startTime:::{},endTime:::{})", placeId,cardNumber,cardType,idNumber,idName,mobile,shiftId,startTime,endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 获取注册中心手机号解绑注册卡时的发送短信的验证码
     *
     * @param requestTicket
     * @param placeId       场所 id
     * @param phoneNumber   手机号
     * @return
     */
    @Override
    public GenericResponse<ObjDTO<RegcardVerificationCodeBO>> sendVerificationCode(String requestTicket, String placeId, String phoneNumber) {
        log.error("接口异常::: getVerificationCode(requestTicket:::{},placeId:::{},phoneNumber:::{})", requestTicket,placeId,phoneNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 手机号解绑注册卡
     *
     * @param requestTicket
     * @param placeId
     * @param auditId
     * @param areaId
     * @param idName
     * @param idNumber
     * @param mobile
     * @param validDays
     * @param shiftId
     * @param price
     * @param verificationCode
     * @return
     */
    @Override
    public GenericResponse<SimpleDTO> forceBind(String requestTicket, String placeId, String auditId, String areaId, String idName, String idNumber, String mobile, String validDays, String shiftId, int price, String verificationCode) {
        log.error("接口异常:::unbindByPhoneNumber");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> forcedBindingByByPhysical(String requestTicket, String placeId, String auditId, String areaId, String idNumber, String mobile, String cardNumber, String verificationCode) {
        log.error("接口异常:::unbindByPhoneNumber");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
