package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.notify.SmsLogBO;
import com.rzx.dim4.base.bo.notify.polling.*;
import com.rzx.dim4.base.bo.notify.region.RegionCityBO;
import com.rzx.dim4.base.bo.notify.region.RegionCountryBO;
import com.rzx.dim4.base.bo.notify.region.RegionProvinceBO;
import com.rzx.dim4.base.bo.notify.region.RegionTownBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.NotifyServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 认证服务 待删除，不添加新接口
 *
 * <AUTHOR>
 * @date Nov 19, 201910:33:14 AM
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.NOTIFY_SERVER, contextId = "NotifyServerService", fallback = NotifyServerServiceHystrix.class)
public interface NotifyServerService {

    // ================================短信相关接口=============================================

    @PostMapping(value = "/sms/send")
    public Boolean send(@RequestHeader("request_ticket") String requestTicket,
                        @RequestHeader("access_key") String accessKey,
                        @RequestBody Map<String, String> params);

    @PostMapping("/sms/logs")
    public PagerDTO<SmsLogBO> logs(@RequestBody Map<String, String> queryMap,
                                   @RequestParam(name = "page", defaultValue = "1") int page,
                                   @RequestParam(name = "size", defaultValue = "10") int size,
                                   @RequestParam(name = "order", defaultValue = "desc") String order,
                                   @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    // ==============================获取全国省市区接口=========================================

    @GetMapping(value = "/region/provinces")
    public GenericResponse<ListDTO<RegionProvinceBO>> provinces();

    @GetMapping(value = "/region/cities")
    public GenericResponse<ListDTO<RegionCityBO>> cities(@RequestParam String provinceId);

    @GetMapping(value = "/region/countries")
    public GenericResponse<ListDTO<RegionCountryBO>> countries(@RequestParam String cityId);

    @GetMapping(value = "/region/towns")
    public GenericResponse<ListDTO<RegionTownBO>> towns(@RequestParam String countryId);

    /**
     * 根据code查询对应的 code - name 键值对
     *
     * @param provinceId 省id
     * @param cityId     市id
     * @param countryId  区id
     * @param townId     街道id
     * @return
     */
    @GetMapping(value = "/region/findNameByCodes")
    public Map<String, String> findNameByCodes(@RequestParam(name = "provinceId", required = false) String provinceId,
                                               @RequestParam(name = "cityId", required = false) String cityId,
                                               @RequestParam(name = "countryId", required = false) String countryId,
                                               @RequestParam(name = "townId", required = false) String townId);

    // =============================5.0上机数据统计接口========================================

    @GetMapping(value = "/statistics/provinces")
    public List<Map<String, String>> getStatProvinces();

    @GetMapping(value = "/statistics/citys")
    public List<Map<String, String>> getStatCitys(@RequestParam String provinceCode);

    @PostMapping("/statistics/saveStatisticsAuth")
    public GenericResponse<SimpleDTO> saveStatisticsAuth(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestParam String account,
                                                         @RequestParam String provinceCode);

    @PostMapping("/statistics/queryStatisticsAuth")
    public Map<String, String> queryStatisticsAuth(@RequestParam String account);

    @PostMapping("/statistics/queryStatistics")
    public List<Map<String, String>> queryStatistics(@RequestParam String startDate,
                                                     @RequestParam String endDate,
                                                     @RequestParam String bizType,
                                                     @RequestParam String provinceCode,
                                                     @RequestParam String cityCode,
                                                     @RequestParam String dateType);

    @PostMapping("/statistics/queryStatisticsBySrcId")
    public List<Map<String, String>> queryStatisticsBySrcId(@RequestParam String startDate,
                                                            @RequestParam String endDate,
                                                            @RequestParam String bizType,
                                                            @RequestParam String provinceCode,
                                                            @RequestParam String cityCode,
                                                            @RequestParam String dateType,
                                                            @RequestParam String srcId);

    // =============================轮询业务接口========================================
    @PostMapping("/business/pushActiveBusinessData")
    public GenericResponse<ObjDTO<ActiveBusinessBO>> pushActiveBusinessData(@RequestBody ActiveBusinessBO activeBusinessBO);

    @PostMapping("/business/pushCancelActiveBusinessData")
    public GenericResponse<ObjDTO<CancelActiveBusinessBO>> pushCancelActiveBusinessData(@RequestBody CancelActiveBusinessBO cancelActiveBusinessBO);

    @PostMapping("/business/pushCreateCardBusinessData")
    public GenericResponse<ObjDTO<CreateCardBusinessBO>> pushCreateCardBusinessData(@RequestBody CreateCardBusinessBO createCardBusinessBO);

    @PostMapping("/business/pushLoginBusinessData")
    public GenericResponse<ObjDTO<LogLoginBusinessBO>> pushLoginBusinessData(@RequestBody LogLoginBusinessBO logLoginBusinessBO);

    @PostMapping("/business/pushPackageExchangeBusinessData")
    public GenericResponse<ObjDTO<PackageExchangeBusinessBO>> pushPackageExchangeBusinessData(@RequestBody PackageExchangeBusinessBO packageExchangeBusinessBO);

    @PostMapping("/business/pushLogoutBusinessData")
    public GenericResponse<ObjDTO<LogoutBusinessBO>> pushLogoutBusinessData(@RequestBody LogoutBusinessBO logoutBusinessBO);

    @PostMapping("/business/pushTopupAndDeductionBusinessData")
    public GenericResponse<ObjDTO<TopupAndDeductionBusinessBO>> pushTopupAndDeductionBusinessData(@RequestBody TopupAndDeductionBusinessBO topupAndDeductionBusinessBO);

    @PostMapping("/business/pushRealnameBusinessData")
    public GenericResponse<ObjDTO<RealnameBusinessBO>> pushRealnameBusinessData(@RequestBody RealnameBusinessBO RealnameBusinessBO);

    @PostMapping("/business/pushUpdateConfigBusinessData")
    public GenericResponse<ObjDTO<UpdateConfigBusinessBO>> pushUpdateConfigBusinessData(@RequestBody UpdateConfigBusinessBO updateConfigBusinessBO);

    @PostMapping("/business/pushShopBuyBusinessData")
    public GenericResponse<ObjDTO<ShopBuyBusinessBO>> pushShopBuyBusinessData(@RequestBody ShopBuyBusinessBO shopBuyBusinessBO);

    @PostMapping("/business/pushThirdExchangeBusinessData")
    public GenericResponse<ObjDTO<ThirdExchangeBusinessBO>> pushThirdExchangeBusinessData(@RequestBody ThirdExchangeBusinessBO thirdExchangeBusinessBO);

    @PostMapping("/polling/savePolling")
    public GenericResponse<ObjDTO<PollingBO>> savePolling(@RequestParam String placeId,
                                                          @RequestParam String clientId,
                                                          @RequestParam String idNumber,
                                                          @RequestParam BusinessType businessType);

    @PostMapping("/business/pushCouponBusinessData")
    public GenericResponse<ObjDTO<CouponBusinessBO>> pushCouponBusinessData(@RequestBody CouponBusinessBO couponBusinessBO);

    @PostMapping("/business/pushUpdateCardTypeBusinessData")
    public GenericResponse<ObjDTO<UpdateCardTypeBusinessBO>> pushUpdateCardTypeBusinessData(@RequestBody UpdateCardTypeBusinessBO updateCardTypeBusinessBO);
}
