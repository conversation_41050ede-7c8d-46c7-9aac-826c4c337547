package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.MagCardBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.callback.place.MagCardApiHystrix;
import com.rzx.dim4.base.service.callback.place.PlaceAccountApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月07日 18:39
 */

@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "MagCardApi", fallback = MagCardApiHystrix.class)
public interface MagCardApi {

    /**
     * 请求前缀，用于区分不同的接口，拼接时放最前面
     */
    String URL = "/feign/place/magCard";

    @PostMapping(URL + "/findPage")
    GenericResponse<ResponsePage<MagCardBO>> findPage(@RequestBody Map<String,String> param);

    @PostMapping(URL + "/import")
    GenericResponse<?> importDate(@RequestBody List<MagCardBO> bos);

    @PostMapping(URL + "/deleteByCardNo")
    GenericResponse<?> deleteByCardNo(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String deleteByCardNo);

    @GetMapping(URL + "/findLoginInfoByCardNo")
    GenericResponse<ObjDTO<MagCardBO>> findLoginInfoByCardNo(@RequestParam String cardNo);

    @GetMapping(URL + "/findByCardNo")
    GenericResponse<ObjDTO<MagCardBO>> findByCardNo(@RequestParam String cardNo);

    @PostMapping(URL + "/edit")
    GenericResponse<?> edit(@RequestHeader(value = "request_ticket") String requestTicket ,@RequestBody MagCardBO magCardBO);
}
