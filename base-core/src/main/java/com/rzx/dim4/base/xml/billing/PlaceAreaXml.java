package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "area")
@XmlAccessorType(XmlAccessType.NONE)
@Getter
@Setter
public class PlaceAreaXml {

    @XmlElement(name = "area_name")
    private String areaName;

    @XmlElement(name = "level")
    private int level;

}
