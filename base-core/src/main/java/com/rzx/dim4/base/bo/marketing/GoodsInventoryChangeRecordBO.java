package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * 商品操作数目明细表
 * <AUTHOR>
 * @date 2024年12月04日 14:43
 */
@Getter
@Setter
@ApiModel(description = "商品库存变更记录实体")
public class GoodsInventoryChangeRecordBO extends AbstractEntityBO
{

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "创建者ID")
    private Long creater;

    @ApiModelProperty(value = "账号创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "账号更新时间")
    private LocalDateTime updated;

    @ApiModelProperty(value = "删除状态")
    private int deleted;

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "变更记录批次标识，一次操作全部用同一个ID记录（如：一次上下架会操作多个商品，则该表需要记录多条数据和同一个change_record_id标识）")
    private String changeRecordId;

    @ApiModelProperty(value = "变动前库存")
    private int oldStorageNumber;

    @ApiModelProperty(value = "变化数量")
    private int changeNumber;

    @ApiModelProperty(value = "变动方式：0-入库，1-上下架，2-盘点，3-调出，4-调入，5-退货，6-报损，7-销售，8-派奖")
    private int changeRecordType;

    @ApiModelProperty(value = "货架ID，场所唯一，从100000开始递增，主仓库为000000")
    private String storageRackId;

    @ApiModelProperty(value = "操作人姓名")
    private String createrName;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品销售价，单位分")
    private int unitPrice;

    @ApiModelProperty(value = "商品成本价，单位分")
    private int costPrice;

    @ApiModelProperty(value = "货架名称")
    private String storageRackName;

    @ApiModelProperty(value = "来源类型，0：客户端；1：收银台；2：微信；3：系统")
    private SourceType sourceType;
}
