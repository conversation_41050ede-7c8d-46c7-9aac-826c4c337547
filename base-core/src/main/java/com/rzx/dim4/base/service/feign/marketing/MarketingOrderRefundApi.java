package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingOrderRefundApiHystrix;
import com.rzx.dim4.base.vo.marketing.PreOrderRefundGoodsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/** 订单退款接口
 * <AUTHOR> hwx
 * @since 2025/2/21 17:15
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingOrderRefundApi", fallback = MarketingOrderRefundApiHystrix.class)
public interface MarketingOrderRefundApi {

    /**
     * 查询前班退款订单
     * @param placeId
     * @param shiftId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    @GetMapping("/feign/marketing/orderRefund/queryPreRefund")
    GenericResponse<ListDTO<PreOrderRefundGoodsVO>> queryPreRefund(@RequestParam String placeId, @RequestParam String shiftId, @RequestParam String startDateTime,
                                                                @RequestParam String endDateTime);
}
