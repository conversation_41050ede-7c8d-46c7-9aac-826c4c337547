package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 *  商品售卖信息和基础信息表
 * <AUTHOR>
 * @date 2025-05-28
 */
@Getter
@Setter
public class InternetFeeMiniAppBO extends AbstractEntityBO {
	private Long id;

	private String placeId; // 场所ID

	private String name; // 名称

	private String internetFeeId; // 网费充送Id，单场所唯一，从110000开始递增

	private String description; // 活动说明

	private int cycle; // 活动周期，0每日、1每周、2每月、3指定日期

	private String goodsId; // 充值金额，网费虚拟商品id

	private String goodsPic; // 商品图片

	private int presentAmount; // 赠送金额

	private String cardTypeIds; // 可购买卡类型登记

	private int onlyGirl; // 仅限女性会员参加

	private String levelTo; // 等级调整至：会员卡等级

	private String levelToName; // 等级调整至：会员卡等级名称

	private int participationTimesLimit; // 参与次数限制，根据限制周期来做限制，如限制周期为每周，那么就是每周限制买赠这个商品几次。

	private int limitCycle; // 限制周期，0每日（默认），1每周，2每月

	private int hideEventEntrance; // 隐藏活动入口：0否，1是（默认否）

	private String hiddenWeeks; // 隐藏日期为每周，逗号分隔如，1，2，3

	private String hiddenDays; // 隐藏日期（每月），逗号分隔如，1，2，3，4，5

	private LocalDate hideStartDate; // 隐藏周期为指定日期，隐藏开始日期 2012-01-01

	private LocalDate hideEndDate; // 隐藏周期为指定日期，隐藏结束日期 2099.12.31

	private int storeMemberVisible; // 仅本店会员可见：0否（默认否），1是

	private float startTime; // 每日开始售卖时间点，默认0（每个时间段都放值，只有符合的数据能被查询）

	private float endTime; // 每日结束售卖时间段，默认24

	private String weeks; // 每周售卖日期，默认 1,2,3,4,5,6,7

	private String days; // 每月售卖日期，默认 0,1,2,3,4,...31

	private LocalDate startDate; // 指定时间段开始日期 2012-01-01

	private LocalDate endDate; // 指定时间段结束日期 2099.12.31

	private int allowDowngrade; // 目标会员等级较高时，允许降级,钱包余额小于 等于{$}元时触发（不填则不限制）

	private int downgradeTriggerAmount; // 降级触发的金额

	private int unitPrice; // 充值金额，网费销售价

}
