package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.GoodsStorageRackResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsSuppliersResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsTagsResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsTypeResponseBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingCommonApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingCommonApi", fallback = MarketingCommonApiHystrix.class)
public interface MarketingCommonApi {
    String URL = "/feign/marketing/common";

    @GetMapping(URL + "/findGoodsCategoryList")
    GenericResponse<ListDTO<GoodsTypeResponseBO>> findGoodsCategoryList(@RequestParam(name = "placeId") String placeId);

    @GetMapping(URL + "/findSupplierList")
    GenericResponse<ListDTO<GoodsSuppliersResponseBO>> findSupplierList(@RequestParam(name = "placeId") String placeId);

    /**
     * 查询所有自定义标签（系统标签使用固定的tagId）
     */
    @GetMapping(URL + "/findGoodsTagList")
    GenericResponse<ListDTO<GoodsTagsResponseBO>> findTagList(@RequestParam(name = "placeId") String placeId);

    @GetMapping(URL + "/findStorageRackList")
    GenericResponse<ListDTO<GoodsStorageRackResponseBO>> findStorageRackList(@RequestParam(name = "placeId") String placeId);
}
