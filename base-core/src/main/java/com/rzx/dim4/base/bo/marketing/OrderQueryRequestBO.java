package com.rzx.dim4.base.bo.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@Accessors(chain = true)
@ApiModel("订单查询参数")
public class OrderQueryRequestBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "卡号")
    private String cardId;

    @ApiModelProperty(value = "姓名")
    private String idName;

    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "金额")
    private Integer moneyMoreThan;

    @ApiModelProperty(value = "支付方式")
    private String payType;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "状态, 1:已支付, 2:已派送, 3:已完成, 4:部分退款 5:退款")
    private Integer status;

    @ApiModelProperty(value = "订单类型: 1商品订单，2团购订单，3网费充值订单，4包时订单，6网费支付的包时订单，9自定义收款")
    private Integer orderType;

    @ApiModelProperty(value = "来源类型： CLIENT,CASHIER,WECHAT,ALIPAY,SYSTEM,MINIAPP,MARKET,JWELL,DABAZHANG,YISHANGWANG,OTHER,MANAGEMENT,PMS,LONGGUANJIA,QINGWANG,RZXREALNAME,IOT,JIELA,YUNTU,WAILIAN,WANGYU,QUANYOU,WANGZHE,WHANGYIYUN,DIANJING,YINGXING,XIONGSHIYE,DOUYIN,MEITUAN,SHANGJITANG")
    private String sourceType;

    @ApiModelProperty(value = "当前页（从0开始，异常情况重置为0）")
    private Integer page;

    @ApiModelProperty(value = "每页大小（10-100,不在此范围内，重置为10）")
    private Integer size;
}
