package com.rzx.dim4.base.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 存储一个数字
 * <AUTHOR> hwx
 * @since 2025/2/21 17:02
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NumberDto extends AbstractDTO implements Serializable {
    private static final long serialVersionUID = -1259445834443672114L;
    Number result;
}
