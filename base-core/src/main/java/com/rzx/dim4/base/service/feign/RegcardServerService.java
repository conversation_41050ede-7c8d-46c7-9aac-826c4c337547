package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.billing.RegcardVerificationCodeBO;
import com.rzx.dim4.base.bo.regcard.ConfigBO;
import com.rzx.dim4.base.bo.regcard.RegLogBO;
import com.rzx.dim4.base.bo.regcard.RegcardBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.RegcardServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 注册卡服务
 *
 * <AUTHOR>
 * @version 1。0
 * @date 2021年6月16日 上午10:59:33
 */
@Primary
@FeignClient(value = FeginConstant.REGCARD_SERVER, contextId = "RegcardServerService", fallback = RegcardServerServiceHystrix.class)
public interface RegcardServerService {

    /**
     * 查询配置
     *
     * @param placeId 场所 id
     * @return
     */
    @GetMapping("/config/query")
    GenericResponse<ObjDTO<ConfigBO>> getConfig(@RequestParam String placeId);

    /**
     * 保存配置
     *
     * @param requestTicket
     * @param placeId
     * @param serverGroupName
     * @return
     */
    @PostMapping("/config/save")
    GenericResponse<SimpleDTO> saveConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                          @RequestParam String placeId,
                                          @RequestParam String serverGroupName);

    /**
     * 注册卡注册（非实体卡）
     *
     * @param requestTicket
     * @param placeId       场所ID，14位编码
     * @param auditId       审计ID，10位编码
     * @param areaId        区域唯一编码
     * @param idName        姓名
     * @param idNumber      身份证号码
     * @param mobile        手机号
     * @param validDays     注册卡类型
     * @param shiftId       班次号
     * @param price         价格
     * @return
     */
    @PostMapping("/regcard/onRegist")
    GenericResponse<SimpleDTO> onRegist(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam String placeId,
                                        @RequestParam String auditId,
                                        @RequestParam String areaId,
                                        @RequestParam String idName,
                                        @RequestParam String idNumber,
                                        @RequestParam String mobile,
                                        @RequestParam String validDays,
                                        @RequestParam String shiftId,
                                        @RequestParam int price);

    /**
     * 注册卡注册逻辑(实体卡)
     *
     * @param requestTicket
     * @param placeId       场所ID，14位编码
     * @param auditId       审计ID，10位编码
     * @param areaId        区域唯一编码
     * @param idNumber      身份证号码
     * @param mobile        手机号
     * @param cardNumber    实体卡卡号
     * @return
     */
    @PostMapping("/regcard/OnRegistByPhysicalCard")
    GenericResponse<SimpleDTO> OnRegistByPhysicalCard(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestParam String placeId,
                                                      @RequestParam String auditId,
                                                      @RequestParam String areaId,
                                                      @RequestParam String idNumber,
                                                      @RequestParam String mobile,
                                                      @RequestParam String cardNumber);

    /**
     * 查询注册卡激活
     *
     * @param requestTicket
     * @param placeId
     * @param auditId
     * @param areaId
     * @param idNumber
     * @return
     */
    @PostMapping("/regcard/onActive")
    GenericResponse<SimpleDTO> onActive(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam String placeId,
                                        @RequestParam String auditId,
                                        @RequestParam String areaId,
                                        @RequestParam String idNumber);

    /**
     * 查询未使用的注册卡总数
     *
     * @param auditId
     * @param validDays
     * @return
     */
    @GetMapping("/regcard/queryUnusedCount")
    GenericResponse<SimpleDTO> queryUnusedCount(@RequestParam String auditId, @RequestParam String validDays);

    /**
     * 分页查询当前场所注册卡信息
     *
     * @param placeId
     * @param cardNumber
     * @param startRow
     * @param endRow
     * @return
     */
    @PostMapping("/regcard/pageQueryRegcard")
    GenericResponse<PagerDTO<RegcardBO>> PageQueryRegcardByPlaceId(@RequestParam String placeId,
                                                                   @RequestParam String cardNumber,
                                                                   @RequestParam(name = "startRow", defaultValue = "0") int startRow,
                                                                   @RequestParam(name = "endRow", defaultValue = "10") int endRow);

    /**
     * 分页查询当前场所的所有注册卡日志信息
     *
     * @param placeId
     * @param idNumber
     * @param startRow
     * @param endRow
     * @return
     */
    @PostMapping("/regcardLog/pageQueryRegcardLog")
    GenericResponse<PagerDTO<RegLogBO>> PageQueryReglogByPlaceId(@RequestParam String placeId,
                                                                 @RequestParam String idNumber,
                                                                 @RequestParam(name = "startRow", defaultValue = "0") int startRow,
                                                                 @RequestParam(name = "endRow", defaultValue = "10") int endRow);

    /**
     * 注册卡解绑
     *
     * @param requestTicket
     * @param placeId       场所ID，14位编码
     * @param auditId       审计ID，10位编码
     * @param areaId        区域唯一编码
     * @param idNumber      身份证号码
     * @return
     */
    @PostMapping("/regcard/OnUnbind")
    GenericResponse<SimpleDTO> OnUnbind(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam String placeId,
                                        @RequestParam String auditId,
                                        @RequestParam String areaId,
                                        @RequestParam String idNumber);

    @GetMapping("/password/queryPassword")
    GenericResponse<SimpleDTO> queryPassword(@RequestParam String placeId,
                                             @RequestParam String auditId,
                                             @RequestParam String areaId,
                                             @RequestParam String idNumber,
                                             @RequestParam String mobile);

    @PostMapping("/regcard/batchSaveRegcard")
    GenericResponse<SimpleDTO> importRegcard(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestBody List<RegcardBO> regcardBOs);

    @PostMapping("/regcard/deleteRegcard")
    GenericResponse<SimpleDTO> deleteRegcard(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestParam String swgjPlaceId);


    /************** 查询班次和场所下的注册卡相关信息,消费了多少张注册卡及金额
     * date:2023.6.26
     * author:qiudenghui
     * **/
    @PostMapping("/regcardLog/QueryRegcardLogByPlaceIdAndShiftId")
    GenericResponse<ListDTO<RegLogBO>> QueryRegcardLogByPlaceIdAndShiftId(@RequestParam String placeId,
                                                                          @RequestParam String shiftId);

    /**
     * 查询班次和场所下的注册卡相关信息
     *
     * @param placeId
     * @param shiftId
     * @param startRow 这个值得计算好偏移量，等于 startRow * pageSize，实际应该是 pageStart * pageSize
     * @param endRow
     * @param idName
     * @param idNumber
     * @return
     */
    @PostMapping("/regcardLog/PageQueryRegcardLogByPlaceIdAndShiftId")
    GenericResponse<PagerDTO<RegLogBO>> PageQueryRegcardLogByPlaceIdAndShiftId(@RequestParam String placeId, @RequestParam String shiftId,
                                                                               @RequestParam(name = "startRow", defaultValue = "0") int startRow,
                                                                               @RequestParam(name = "endRow", defaultValue = "10") int endRow,
                                                                               @RequestParam(name = "idName", defaultValue = "") String idName,
                                                                               @RequestParam(name = "idNumber", defaultValue = "") String idNumber);

    @PostMapping("/regcardLog/queryRegcardLogByPlaceIdAndDate")
    GenericResponse<ListDTO<RegLogBO>> queryRegcardLogByPlaceIdAndDate(@RequestParam String placeId,
                                                                       @RequestParam(required = false) String shiftId,
                                                                       @RequestParam String startDateTime,
                                                                       @RequestParam String endDateTime);

    @PostMapping("/regcardLog/pageQueryRegcardUsed")
    GenericResponse<PagerDTO<RegLogBO>> PageQueryRegcardUsedByParam(@RequestParam String placeId,
                                                                    @RequestParam String cardNumber,
                                                                    @RequestParam String cardType,
                                                                    @RequestParam String idNumber,
                                                                    @RequestParam String idName,
                                                                    @RequestParam String mobile,
                                                                    @RequestParam String shiftId,
                                                                    @RequestParam(name = "startRow", defaultValue = "0") int startRow,
                                                                    @RequestParam(name = "endRow", defaultValue = "10") int endRow,
                                                                    @RequestParam String startTime,
                                                                    @RequestParam String endTime);

    @PostMapping("/regcard/pageQueryRegcardUnused")
    GenericResponse<PagerDTO<RegcardBO>> PageQueryRegcardUnusedByParam(@RequestParam String placeId,
                                                                       @RequestParam String cardNumber,
                                                                       @RequestParam String cardType,
                                                                       @RequestParam(name = "startRow", defaultValue = "0") int startRow,
                                                                       @RequestParam(name = "endRow", defaultValue = "10") int endRow);


    @PostMapping("/regcardLog/queryRegcardUsed")
    GenericResponse<ListDTO<RegLogBO>> QueryRegcardUsedByParam(@RequestParam String placeId,
                                                               @RequestParam String cardNumber,
                                                               @RequestParam String cardType,
                                                               @RequestParam String idNumber,
                                                               @RequestParam String idName,
                                                               @RequestParam String mobile,
                                                               @RequestParam String shiftId,
                                                               @RequestParam String startTime,
                                                               @RequestParam String endTime);

    /**
     * 获取注册中心手机号解绑注册卡时的发送短信的验证码
     *
     * @param requestTicket
     * @param placeId       场所 id
     * @param phoneNumber   手机号
     * @return
     */
    @GetMapping("/regcard/sendVerificationCode")
    GenericResponse<ObjDTO<RegcardVerificationCodeBO>> sendVerificationCode(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                   @RequestParam String placeId,
                                                                   @RequestParam String phoneNumber);

    /**
     *
     *
     * @return
     */
    @PostMapping("/regcard/forcedBinding")
    GenericResponse<SimpleDTO> forceBind(@RequestHeader(value = "request_ticket") String requestTicket,
                                         @RequestParam String placeId,
                                         @RequestParam String auditId,
                                         @RequestParam String areaId,
                                         @RequestParam String idName,
                                         @RequestParam String idNumber,
                                         @RequestParam String mobile,
                                         @RequestParam String validDays,
                                         @RequestParam String shiftId,
                                         @RequestParam int price,
                                         @RequestParam String verificationCode);

    @PostMapping("/regcard/forcedBindingByByPhysical")
    GenericResponse<SimpleDTO> forcedBindingByByPhysical(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestParam String placeId,
                                                         @RequestParam String auditId,
                                                         @RequestParam String areaId,
                                                         @RequestParam String idNumber,
                                                         @RequestParam String mobile,
                                                         @RequestParam String cardNumber,
                                                         @RequestParam String verificationCode);
}
