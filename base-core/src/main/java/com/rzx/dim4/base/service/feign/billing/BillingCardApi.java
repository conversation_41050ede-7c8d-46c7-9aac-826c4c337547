package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BillingCardBriefBO;
import com.rzx.dim4.base.bo.billing.BillingCardVO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeeSearchBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingCardApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "billingCardApi", fallback = BillingCardApiHystrix.class)
public interface BillingCardApi {

    String URL = "/feign/billing/card";


    @PostMapping(URL + "/findByPlaceIdAndIdNumbers")
    GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndIdNumbers(@RequestParam String placeId, @RequestParam List<String> idNumbers);

    @PostMapping(URL + "/sendInternetFeeForMiniApp")
    GenericResponse<SimpleDTO> sendInternetFeeForMiniApp(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestParam String placeId,
                                                         @RequestParam String cardId,
                                                         @RequestParam int presentAccount);

    /**
     * 查询
     *
     * @param queryMap
     * @return
     */
    @GetMapping(URL + "/queryBillingCardPage")
    GenericResponse<PagerDTO<BillingCardBO>> queryBillingCardPage(@SpringQueryMap Map<String, Object> queryMap, Pageable pageable);


    @PostMapping(URL + "/queryBillingCardPageForMiniApp")
    GenericResponse<PagerDTO<BillingCardBO>> queryBillingCardPageForMiniApp(@RequestBody InternetFeeSearchBO paramsBo);

    /**
     * 根据场所id和计费卡类型id查询计费卡列表
     *
     * @param placeId    场所id
     * @param cardTypeId 计费卡类型id
     * @return 计费卡列表
     */
    @GetMapping(URL + "/findByPlaceIdAndCardTypeId")
    GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardTypeId(@RequestParam String placeId,
                                                                       @RequestParam String cardTypeId);

    /**
     * 删除余额为零的计费卡
     *
     * @param placeId
     * @param cardId
     * @return
     */
    @PostMapping(URL + "/removeUnderfundedCard")
    GenericResponse<SimpleDTO> removeUnderfundedCard(@RequestParam String placeId,
                                                     @RequestParam String cardId);

    /**
     * 删除余额为零的计费卡
     *
     * @param placeIds
     * @param idNumbers
     * @return
     */
    @PostMapping(URL + "/removeChainCard")
    GenericResponse<SimpleDTO> removeChainCard(@RequestHeader(value = "request_ticket") String requestTicket,
                                               @RequestParam List<String> placeIds,
                                               @RequestParam List<String> idNumbers);

    /**
     * 场所退出连锁时，更新计费卡信息
     *
     * @param requestTicket feign请求头
     * @param placeId       场所id
     * @return 更新结果
     */
    @PostMapping(URL + "/exitChain")
    GenericResponse<?> exitChain(@RequestHeader(value = "request_ticket") String requestTicket,
                                 @RequestParam String placeId);


    /**
     * 根据chainId和idNumber查询会员卡信息
     *
     * @param chainId  场所ids
     * @param idNumber 用户证件号
     * @return 计费卡列表
     */
    @GetMapping(URL + "/findByChainIdAndIdNumber")
    GenericResponse<ListDTO<BillingCardBO>> findByChainIdAndIdNumber(@RequestParam String chainId,
                                                                     @RequestParam String idNumber);

    @PostMapping(URL + "/activation")
    GenericResponse<?> activation(@RequestHeader(value = "request_ticket") String requestTicket,
                                  @RequestParam String placeId,
                                  @RequestParam String idNumber,
                                  @RequestParam int activeType);

    @PostMapping(URL + "/newActivation")
    GenericResponse<?> newActivation(@RequestHeader(value = "request_ticket") String requestTicket,
                                     @RequestParam String placeId,
                                     @RequestParam String idNumber,
                                     @RequestParam(required = false, name = "name") String name,
                                     @RequestParam int activeType,
                                     @RequestParam(required = false, defaultValue = "") String phoneNumber);

    @PostMapping(URL + "/iotCreateBillingCard")
    GenericResponse<ObjDTO<BillingCardBO>> iotCreateBillingCard(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam String cardTypeId,
            @RequestParam String placeId,
            @RequestParam String activeType,
            @RequestParam(name = "cashierId", required = false, defaultValue = "") String cashierId, @RequestParam String mobile);

    @PostMapping(URL + "/iotCreateCardWithAmount")
    GenericResponse<ObjDTO<PaymentResultBO>> iotCreateCardWithAmount(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String cardTypeId,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam int amount,
            @RequestParam(name = "cashierId", required = false, defaultValue = "") String cashierId,
            @RequestParam String payType,
            @RequestParam(required = false) String payCode, @RequestParam String mobile);

    @PostMapping(URL + "/savePolling4CreateCard")
    GenericResponse<?> savePolling4CreateCard(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String idNumber, @RequestParam String cashierId, @RequestParam String phoneNumber);

    @PostMapping(URL + "/findCardAndCommonRule")
    GenericResponse<ObjDTO<BillingCardBO>> findCardAndCommonRule(@RequestParam String placeId, @RequestParam String areaId, @RequestParam String cardId);

    @PostMapping(URL + "/findCardByPlaceIdAndLikeIdNumber")
    GenericResponse<ListDTO<BillingCardBO>> findCardByPlaceIdAndLikeIdNumber(@RequestParam String placeId, @RequestParam String idNumber);

    @PostMapping(URL + "/findCardByPlaceIdAndLikeIdNumberForMiniApp")
    GenericResponse<ObjDTO<BillingCardBO>> findCardByPlaceIdAndLikeIdNumberForMiniApp(@RequestParam String placeId, @RequestParam String idNumber);

    @PostMapping(URL + "/queryPageMember")
    GenericResponse<PagerDTO<BillingCardVO>> queryPageMember(@RequestBody List<String> params);

    @PostMapping(URL + "/modifyBillingCard")
    GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCard(@RequestParam("placeId") String placeId,
                                                             @RequestParam("cardId") String cardId,
                                                             @RequestParam("remark") String remark);


    @PostMapping(URL + "/modifyBillingCardType")
    GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCardType(@RequestParam("placeId") String placeId,
                                                                 @RequestParam("cardId") String cardId,
                                                                 @RequestParam("cardTypeId") String cardTypeId);

    @PostMapping(URL + "/findByPlaceIdAndCardId")
    GenericResponse<ObjDTO<BillingCardBriefBO>> findByPlaceIdAndCardId(@RequestParam("placeId") String placeId,
                                                                       @RequestParam("cardId") String cardId);

    @PostMapping(URL + "/modifyBillingCardTypeForCashierMember")
    GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCardTypeForCashierMember(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                                 @RequestParam("placeId") String placeId,
                                                                                 @RequestParam("cardId") String cardId,
                                                                                 @RequestParam("cardTypeId") String cardTypeId,
                                                                                 @RequestParam("accountId") String accountId);

    @GetMapping(URL + "/activeCardList")
    GenericResponse<ListDTO<BillingCardBO>> activeCardList(@RequestParam String placeId, @RequestParam(value = "idNumber", required = false) String idNumber);

    @PostMapping(URL + "/updatePhoneNumber")
    GenericResponse<SimpleDTO> updatePhoneNumber(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam("placeId") String placeId,
            @RequestParam("cardId") String cardId,
            @RequestParam("phoneNumber") String phoneNumber);


    @PostMapping(URL + "/cleanPresentAmount")
    GenericResponse<SimpleDTO> cleanPresentAmount(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam("placeId") String placeId,
            @RequestParam("cardId") String cardId);


    @PostMapping(URL + "/updateLoginPass")
    GenericResponse<SimpleDTO> updateLoginPass(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam("placeId") String placeId,
            @RequestParam("cardId") String cardId,
            @RequestParam("loginPass") String loginPass);


    @PostMapping(URL + "/updateBillingCardType")
    GenericResponse<SimpleDTO> updateBillingCardType(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam("placeId") String placeId,
            @RequestParam("cardId") String cardId,
            @RequestParam("cardTypeId") String cardTypeId);


    /**
     * 根据场所id和计费卡类型id列表查询计费卡列表
     *
     * @param placeId 场所id
     * @param cardIds 计费卡类型id列表
     * @return 计费卡列表
     */
    @GetMapping(URL + "/findByPlaceIdAndCardIds")
    GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardIds(@RequestParam String placeId,
                                                                    @RequestParam List<String> cardIds);

    /**
     * 根据身份证信息批量查询用户简单信息（因为身份证号和姓名是一一对应的，不会随着场所不一样而变化）
     */
    @PostMapping(URL + "/findByIdNumbers")
    GenericResponse<ListDTO<BillingCardBriefBO>> findByIdNumbers(@RequestBody List<String> idNumbers);

//    /**
//     * 根据场所id和计费卡类型id列表查询计费卡列表
//     *
//     * @param placeId  场所id
//     * @param clientId 客户端id
//     * @param idNumber 身份证号
//     * @return 计费卡列表
//     */
//    @GetMapping(URL + "/findByPlaceIdAndClientIdsAndIdNumbers")
//    GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndClientIdsAndIdNumbers(@RequestParam String placeId,
//                                                                    @RequestParam(required = false) List<String> clientIds,
//                                                                    @RequestParam(required = false) List<String> idNumbers);


}
