package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.marketing.OrderGoodsBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/** 订单tipsVo
 * <AUTHOR> hwx
 * @since 2025/3/5 10:20
 */
@Getter
@Setter
public class OrdersTipsVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -155364296230506241L;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private String placeId; // 场所ID
    private String orderId; // 订单ID
    private String clientId; // 客户端ID
    private String clientName; // 客户端名称
    private int totalMoney; // 订单合计价格
    private LocalDateTime payTime; // 订单支付时间
    private int realMoney; // 订单实际金额（优惠后-实际付款金额）;租赁商品为【租金】
    private int deposit;// 租赁押金
    private String remark; // 订单备注
    private int status;// 订单状态，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消
    private List<OrderGoodsTipsVO> orderGoodsList;
    private int orderType;// 订单类型1 商品订单，2团购订单，3网费充值订单， //todo 4包时订单
    private PayType payType; // 支付方式
    private SourceType sourceType; // 来源
}
