package com.rzx.dim4.base.enums.payment;

/**
 * 支付类型
 * 
 * <AUTHOR>
 * @date Jul 28, 2020 4:50:38 PM
 */
/**
 * 0.订单初始化，未发送到支付渠道 <br/>
 * 1.支付渠道返回订单创建成功，等待用户支付 <br/>
 * 2.支付成功 <br/>
 * 3.支付失败 <br/>
 * 4.部分退款 <br/>
 * 5.全额退款 <br/>
 * 9.创建订单失败  <br/>
 */
public enum OrderStatus {

	INIT(1), // 1.订单初始化，未发送到支付渠道
	WAITING(2), // 2.支付渠道返回订单创建成功，等待用户支付
	SUCCEED(3), // 3.支付成功
	REFUND(4), // 4.发生退款
	TOPUP_SUCCESS(5), // 5.充值到账
	FAILED(9), // 创建失败
	;

	private final int value;

	private OrderStatus(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static OrderStatus getPayType(int code) {
		for (OrderStatus orderStatus : OrderStatus.values()) {
			if (code == orderStatus.getValue()) {
				return orderStatus;
			}
		}
		return null;
	}

}
