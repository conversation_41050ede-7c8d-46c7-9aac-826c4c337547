package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceClientApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/7
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceClientApi", fallback = PlaceClientApiHystrix.class)
public interface PlaceClientApi {
    String URL = "/feign/place/placeClient";

    /**
     * 客户端列表（补充了 areaName/isRoom）
     *
     * @param requestTicket ticket，用于防重复请求
     * @param placeId       场所id
     * @param clientIds     客户端id（可选）
     * @return
     */
    @GetMapping(URL + "/getFullInfo")
    GenericResponse<ListDTO<PlaceClientBO>> getFullInfo(@RequestHeader(value = "request_ticket") String requestTicket,
                                                        @RequestParam String placeId,
                                                        @RequestParam(required = false) List<String> clientIds);


    @GetMapping(URL + "/findByPlaceIdAndClientIds")
    GenericResponse<ListDTO<PlaceClientBO>> findByPlaceIdAndClientIds(@RequestParam String placeId, @RequestParam List<String> clientIds);

    @GetMapping(URL + "/findByPlaceId")
    GenericResponse<ListDTO<PlaceClientBO>> findByPlaceId(@RequestParam String placeId);

    @GetMapping(URL + "/findByPlaceIdAndClientName")
    GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndClientName(@RequestParam String placeId, @RequestParam String clientName);
}
