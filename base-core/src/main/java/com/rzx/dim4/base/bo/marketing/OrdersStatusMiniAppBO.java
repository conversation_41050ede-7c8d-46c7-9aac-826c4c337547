package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 订单 信息表
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Getter
@Setter
public class OrdersStatusMiniAppBO extends AbstractEntityBO {

    private String placeId;

    private String orderId;

    // 订单支付时间
    private LocalDateTime payTime;

    // 订单实际金额（优惠后:实际付款金额）
    private int realMoney;

    // 订单状态，查看OrderPayStatus，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消
    private int status;

    // 订单类型1 商品订单，2团购订单，3网费充值订单，4包时订单，9自定义收款
    private int orderType;
}