package com.rzx.dim4.base.service.callback;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.iot.AppUpgradeBO;
import com.rzx.dim4.base.bo.iot.AppVersionInfoBO;
import com.rzx.dim4.base.bo.iot.FacePlaceWhitelistBO;
import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.IotDeviceBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.iot.LogFaceAuthBO;

import com.rzx.dim4.base.bo.iot.PlanPayConfigBO;
import com.rzx.dim4.base.bo.iot.PlanPayConfigConectionBO;

import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.IotServerService;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @date Nov 19, 201910:33:28 AM
 */
@Slf4j
@Service
public class IotServerServiceHystrix implements IotServerService {

	private Gson gson = new Gson();

	@Override
	public GenericResponse<SimpleDTO> savePlaceDevice(String requestTicket, IotDeviceBO iotDeviceBo) {
		log.error("接口异常::: savePlaceDevice(placeDeviceBo:::{})", gson.toJson(iotDeviceBo));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<IotDeviceBO>> queryPlaceDevices(Map<String, Object> queryMap, int size,
																	int page) {
		log.error("接口异常::: queryPlaceDevices(queryMap:::{}, size:::{}, page:::{})", gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<IotDeviceBO>> queryPlaceDevice(String serialno) {
		log.error("接口异常::: queryPlaceDevice(serialno:::{})", serialno);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<IotDeviceBO>> queryPlaceDeviceByPlaceId(String placeId) {
		log.error("接口异常::: queryPlaceDeviceByPlaceId(placeId:::{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public Map<String, Integer> queryActiveDeviceNum() {
		log.error("接口异常::: queryActiveDeviceNum()");
		return null;
	}

	@Override
	public GenericResponse<SimpleDTO> deleteDevice (String serialno) {
		log.error("接口异常::: deleteDevice(serialno:::{})", serialno);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> savePlaceAuthConfig(String requestTicket, IotAuthConfigBO iotAuthConfigBO) {
		log.error("接口异常::: savePlaceAuthConfig(placeAuthConfigBO:::{})", gson.toJson(iotAuthConfigBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<IotAuthConfigBO>> queryPlaceAuthConfig(String placeId) {
		log.error("接口异常::: queryPlaceAuthConfig(placeId:::{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> batchStop(String requestTicket, String placeIds, String authFeeTypes) {
		log.error("接口异常::: batchStop(requestTicket:::{}, placeIds:::{}, authFeeTypes:::{})", requestTicket, placeIds, authFeeTypes);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deleteAuthConfig(String placeId, String placeAuthFeeType) {
		log.error("接口异常::: deleteAuthConfig(placeId:::{}, placeAuthFeeType:::{})", placeId, placeAuthFeeType);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<LogAuthFeeBO>> queryPlaceAuthFees(Map<String, Object> queryMap, int size, int page) {
		log.error("接口异常::: queryPlaceAuthFees(queryMap:::{}, size:::{}, page:::{})", queryMap, size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public Map<String, Integer> queryFeeStatistics(Map<String, Object> queryMap) {
		log.error("接口异常::: queryFaceStatistics(queryMap:::{})", queryMap);
		return null;
	}

	@Override
	public GenericResponse<PagerDTO<LogFaceAuthBO>> queryFaceAuths(Map<String, Object> queryMap, int size, int page) {
		log.error("接口异常::: queryFaceAuths(queryMap:::{}, size:::{}, page:::{})", queryMap, size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public Map<String, Integer> queryFaceStatistics(Map<String, Object> queryMap) {
		log.error("接口异常::: queryFaceStatistics(queryMap:::{})", queryMap);
		return null;
	}

	@Override
	public GenericResponse<PagerDTO<IotAuthConfigBO>> queryPlaceAuthConfigs(Map<String, Object> queryMap, int size,
                                                                            int page) {
		log.error("接口异常::: queryPlaceAuthConfigs(queryMap:::{}, size:::{}, page:::{})", gson.toJson(queryMap), size,
				page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<?> modifyOrderStatus(String orderId, int status, String account) {
		log.error("接口异常::: queryPlaceAuthConfigs(orderId:::{}, status:::{}, account:::{})", orderId, status, account);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlanPayConfigBO>> getPlanPayConfigs(Map<String, Object> queryMap, int size,
			int page) {
		log.error("接口异常::: getPlanPayConfigs(queryMap:::{}, size:::{}, page:::{})", gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlanPayConfigConectionBO>> getPlanPayConfigConections(Map<String, Object> queryMap) {
		log.error("接口异常::: getPlanPayConfigConections(queryMap:::{})", gson.toJson(queryMap));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> savePlanPayConfig(String requestTicket, PlanPayConfigBO planPayConfigBO) {
		log.error("接口异常::: savePlanPayConfig(requestTicket:::{}, planPayConfigBO:::{})", requestTicket, gson.toJson(planPayConfigBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	
	@Override
	public GenericResponse<SimpleDTO> modifyPlanPayConfig(String requestTicket, PlanPayConfigBO planPayConfigBO) {
		log.error("接口异常::: modifyPlanPayConfig(requestTicket:::{}, planPayConfigBO:::{})", requestTicket, gson.toJson(planPayConfigBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> addPlanPayConfigConection(String requestTicket, PlanPayConfigConectionBO planPayConfigConectionBO) {
		log.error("接口异常::: addPlanPayConfigConection(requestTicket:::{}, planPayConfigConectionBO:::{})", requestTicket, gson.toJson(planPayConfigConectionBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deleteConnections4Bar(String requestTicket, PlanPayConfigConectionBO planPayConfigConectionBO) {
		log.error("接口异常::: deleteConnections4Bar(requestTicket:::{}, planPayConfigConectionBO:::{})", requestTicket, gson.toJson(planPayConfigConectionBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> updateAreaConnections(String requestTicket, PlanPayConfigConectionBO planPayConfigConectionBO) {
		log.error("接口异常::: updateAreaConnections(requestTicket:::{}, planPayConfigConectionBO:::{})", requestTicket, gson.toJson(planPayConfigConectionBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);

	}

	@Override
	public GenericResponse<PagerDTO<PlanPayConfigConectionBO>> getPlanPayConfigConections4Page(
			Map<String, Object> queryMap, int size, int page) {
		log.error("接口异常::: getPlanPayConfigConections4Page(queryMap:::{}, size:::{}, page:::{})", gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlanPayConfigBO>> getActiveConfigsBySingelBar(Map<String, Object> queryMap, int size, int page) {
		log.error("接口异常::: getActiveConfigsBySingelBar(queryMap:::{}, size:::{}, page:::{})", gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	public GenericResponse<SimpleDTO> batchModifyPlaceDevice(String requestTicket, Map<String, Object> paramMap) {
		log.error("接口异常::: batchModifyPlaceDevice(requestTicket:::{}, paramMap:::{})", requestTicket, gson.toJson(paramMap));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<LogAuthFeeBO>> queryPlaceAuthFeeByFaceId(Map<String, Object> queryMap) {
		log.error("接口异常::: queryPlaceAuthFeeByFaceId(queryMap:::{})",  gson.toJson(queryMap));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<AppVersionInfoBO>> queryAppVersionInfos(Map<String, Object> queryMap, int size,
			int page) {
		log.error("接口异常::: queryAppVersionInfos(queryMap:::{}, size:::{}, page:::{})",  gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deleteVersion(String requestTicket, String versionCode) {
		log.error("接口异常::: deleteVersion(requestTicket:::{}, id:::{})", requestTicket, versionCode);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> saveAppVersion(String requestTicket, AppVersionInfoBO bo) {
		log.error("接口异常::: saveAppVersion(requestTicket:::{}, AppVersionInfoBO:::{})", requestTicket, gson.toJson(bo));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<AppUpgradeBO>> queryAppUpgradeInfos(Map<String, Object> queryMap, int size,
			int page) {
		log.error("接口异常::: queryAppUpgradeInfos(queryMap:::{}, size:::{}, page:::{})",  gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> saveUpgradeInfos(String requestTicket, AppUpgradeBO bo) {
		log.error("接口异常::: saveUpgradeInfos(requestTicket:::{}, AppUpgradeBO:::{})", requestTicket, gson.toJson(bo));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> updateVersionCode(String requestTicket, AppUpgradeBO bo) {
		log.error("接口异常::: updateVersionCode(requestTicket:::{}, AppUpgradeBO:::{})", requestTicket, gson.toJson(bo));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<AppVersionInfoBO>> getAppVersionInfo(Map<String, Object> queryMap) {
		log.error("接口异常::: getAppVersionInfo(queryMap:::{})", gson.toJson(queryMap));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<FacePlaceWhitelistBO>> queryFaceWhitelist4place(Map<String, Object> queryMap,
			int size, int page) {
		log.error("接口异常::: queryFaceWhitelist4place(queryMap:::{}, size:::{}, page:::{})",  gson.toJson(queryMap), size, page);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> saveFaceWhitelist4place(String requestTicket, FacePlaceWhitelistBO bo) {
		log.error("接口异常::: saveFaceWhitelist4place(requestTicket:::{}, FacePlaceWhitelistBO:::{})", requestTicket, gson.toJson(bo));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deleteFaceWhitelist4place(String requestTicket, String placeId) {
		log.error("接口异常::: deleteFaceWhitelist4place(requestTicket:::{}, placeId:::{})", requestTicket, placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	
}
