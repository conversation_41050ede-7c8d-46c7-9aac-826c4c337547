package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "member_card_config")
@XmlAccessorType(XmlAccessType.NONE)
@ToString
public class BillingCardTypeXml {

    @XmlElement(name = "disable")
    private int deleted;

    @XmlElement(name = "card_type")
    private String cardTypeId;

    @XmlElement(name = "card_name")
    private String typeName;
}
