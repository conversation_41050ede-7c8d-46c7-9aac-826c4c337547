package com.rzx.dim4.base.enums.idauth;

/**
 * 验证类型
 * 
 * <AUTHOR>
 * @date Dec 19, 201911:39:54 AM
 */
public enum AuthType {

	ONE_TO_ONE("1比1认证"), //
	TWO_ELEMENTS("2要素认证"), // 姓名，身份证号，两要素认证
	THREE_ELEMENTS("3要素认证"), // 姓名，身份证号，两要素认证
	LIVENESS("活体认证"), // 姓名，身份证号，人脸视频认证
	IDCARD_OCR("身份证OCR"), // 身份证OCR
	COMPANY_VERIFY("企业信息认证"), // 企业信息认证

	THREE_ELEMENTS_MOBILE("3要素认证(手机号)"), // 姓名，身份证号，手机号,三要素认证
	;

	private final String name;

	private AuthType(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}
}
