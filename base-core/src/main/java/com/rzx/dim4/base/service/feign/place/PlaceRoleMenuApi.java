package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceMenuBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceConfigApiHystrix;
import com.rzx.dim4.base.service.callback.place.PlaceRoleMenuApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025年08月04日 13:38
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceRoleMenuApi", fallback = PlaceRoleMenuApiHystrix.class)
public interface PlaceRoleMenuApi {

    String URL = "/feign/place/placeRoleMenu";

    /**
     * 根据placeId、accountId查询用户菜单权限列表
     */
    @GetMapping(URL + "/getPlaceRoleMenuList")
    public GenericResponse<ListDTO<PlaceMenuBO>> getPlaceRoleMenuList(@RequestParam String placeId,@RequestParam String accountId);


}
