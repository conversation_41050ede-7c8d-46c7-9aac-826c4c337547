package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.OrderRefundBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 退款订单查询
 * <AUTHOR>
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "OrdersRefund", fallback = ShopServerServiceHystrix.class)
public interface OrdersRefundClient {

    String URL="/shop/admin/ordersRefund";


    /**
     * 查询退单
     * @param queryMap
     * @return
     */
    @PostMapping(URL+"/listOrdersRefund")
    GenericResponse<ListDTO<OrderRefundBO>> queryOrdersRefunds(@RequestBody Map<String, String> queryMap);
}
