package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024年08月02日 18:22
 */
@Getter
@Setter
public class MarketDouyinStoreBO extends AbstractEntityBO {

    private static final long serialVersionUID = -3507212869714335520L;

    private String placeId; // 场所id - 对应抖音接口的 ext_id（第三方门店id）

    private String poiId; // 抖音商户门店id

    private String poiName; // 抖音商户门店名称

    private String province; // 省份名称

    private String city; // 城市名称

    private String address; // 详细地址

    private Double longitude; //门店的经度，精度为小数点后6位，需使用 GCJ-02 坐标系统

    private Double latitude; //门店的纬度，精度为小数点后6位，需使用 GCJ-02 坐标系统

    private String headImageUrls; //门店头图链接 如：["https://www.xx.com/1.jpg","https://www.xx.com/2.jpg"] 是一个数组

    private String industryCode; //门店的行业类目 code "011607"

    private String openimes; //门店营业时间 json格式如 周一9点到18点: {"1":["09:00-18:00"],"2":[09:00-18:00]}

    private String contactTel; //门店座机号码

    private String contactPhone; //门店手机号码

    private int status; //匹配状态，0匹配中，1匹配失败，匹配成功，3未匹配

    private String accountId; //抖音门店账户

    private String accountName; //抖音门店名称

    private String rootAccountId; //抖音门店账户(总部子账号)

    private String rootAccountName; //抖音门店名称

    private int verificationPlatformFlag; // 场所绑定第三方核销平台是否开启
    private Long creater;
}
