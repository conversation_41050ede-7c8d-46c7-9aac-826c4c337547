package com.rzx.dim4.base.enums.place;

/**
 * 网吧认证费用模式
 * 
 * <AUTHOR>
 * @date 2022年7月22日 下午2:41:33
 */
public enum PlaceAuthFeeTypes {

	Free(-1, "免费"), // 免费(禁用)
	EveryTime(0, "次卡"), // 按次
	ByDay(1, "日卡"), // 按天
	ByWeek(7, "周卡"), // 按周
	ByMonth(30, "月卡"), // 按月
	ByQuarter(90, "季卡"), // 按季
	ByYear(365, "年卡"), // 按年
	ByOther(99, "其他") // 其他 （老版本hotel认证费）
	;

	private final int value;
	private final String name;

	private PlaceAuthFeeTypes(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public static PlaceAuthFeeTypes getPlaceAccountTypes(int i) {
		for (PlaceAuthFeeTypes placeAccountTypes : PlaceAuthFeeTypes.values()) {
			if (i == placeAccountTypes.getValue()) {
				return placeAccountTypes;
			}
		}
		return null;
	}
	
	public static PlaceAuthFeeTypes getPlaceAccountTypesByEmName(String enName) {
		for (PlaceAuthFeeTypes placeAccountTypes : PlaceAuthFeeTypes.values()) {
			if (enName.equals(placeAccountTypes.toString())) {
				return placeAccountTypes;
			}
		}
		return null;
	}

}
