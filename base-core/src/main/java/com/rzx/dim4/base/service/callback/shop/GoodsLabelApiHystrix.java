package com.rzx.dim4.base.service.callback.shop;

import com.rzx.dim4.base.bo.shop.GoodsLabelBO;
import com.rzx.dim4.base.bo.shop.GoodsTagsBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.feign.goods.GoodsLabelApi;
import com.rzx.dim4.base.service.feign.goods.GoodsTagsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024年07月19日 14:49
 */
@Slf4j
@Service
public class GoodsLabelApiHystrix implements GoodsLabelApi {

    @Override
    public GenericResponse<ObjDTO<GoodsLabelBO>> addLabel(GoodsLabelBO goodsLabelBO, String requestTicket) {
        log.error("接口异常:::addLabel(),placeId:::{},tagsId:::{},tagsId:::{},labelName:::{}",goodsLabelBO.getPlaceId(),goodsLabelBO.getTagsId(),
                goodsLabelBO.getId(),goodsLabelBO.getLabelName());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsLabelBO>> delLabel(String placeId, String id, String requestTicket) {
        log.error("接口异常:::delLabel(),placeId:::{},id:::{}",placeId,id);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
