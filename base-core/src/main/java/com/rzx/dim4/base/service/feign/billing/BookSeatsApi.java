package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BookSeatsBO;
import com.rzx.dim4.base.bo.billing.BookSeatsConfigBO;
import com.rzx.dim4.base.bo.billing.BookSeatsCustomerContextBO;
import com.rzx.dim4.base.bo.billing.BookSeatsQueryBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BookSeatsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/12
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "bookSeatsApi", fallback = BookSeatsApiHystrix.class)
public interface BookSeatsApi {

    String URL = "/feign/billing/bookSeats";

    /**
     * 用户待订座页面信息
     *
     * @param requestTicket 请求防止重复ticket
     * @param placeId       场所id
     * @param idNumber      证件号
     * @return 场所座位订座信息
     */
    @GetMapping(URL + "/customer/context")
    GenericResponse<ObjDTO<BookSeatsCustomerContextBO>> customerContext(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                        @RequestParam String placeId,
                                                                        @RequestParam String idNumber);

    /**
     * 用户执行订座
     *
     * @param requestTicket 请求防止重复ticket
     * @param bookSeatsBO   订座信息（必填字段：placeId、idNumber、clientIds、areaId、roomFlag、
     *                      duration、ruleId、price、estimatedCost）
     * @return 订座信息
     */
    @PostMapping(URL + "/customer/create")
    GenericResponse<ObjDTO<BookSeatsBO>> customerCreate(@RequestHeader(value = "request_ticket") String requestTicket,
                                                        @RequestBody BookSeatsBO bookSeatsBO);

    /**
     * 用户取消订座
     * @param requestTicket 请求防止重复ticket
     * @param bookSeatsId 订座记录id
     * @param operator 操作人id(billingCard.id)
     * @return 是否成功
     */
    @PostMapping(URL + "/customer/cancel")
    GenericResponse<?> customerCancel(@RequestHeader(value = "request_ticket") String requestTicket,
                                      @RequestParam long bookSeatsId,
                                      @RequestParam long operator);

    /**
     * 用户端获取订座列表
     * @param requestTicket 请求防止重复ticket
     * @param queryBO 订座列表筛选条件(placeId、idNumber 必填)
     * @return 订座列表
     */
    @PostMapping(URL + "/customer/list")
    GenericResponse<PagerDTO<BookSeatsBO>> customerGetList(@RequestHeader(value = "request_ticket") String requestTicket,
                                                           @RequestBody BookSeatsQueryBO queryBO);

    @GetMapping(URL + "/config")
    GenericResponse<ObjDTO<BookSeatsConfigBO>> getConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestParam(value = "placeId") String placeId);

    @PostMapping(URL + "/config/update")
    GenericResponse<?> updateConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                    @RequestBody BookSeatsConfigBO configBO);
}
