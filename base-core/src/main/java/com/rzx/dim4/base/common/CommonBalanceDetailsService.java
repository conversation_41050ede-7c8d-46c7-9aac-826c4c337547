package com.rzx.dim4.base.common;

import com.rzx.dim4.base.bo.billing.BalanceDetailsBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.billing.BalanceDetailsApi;
import com.rzx.dim4.base.service.feign.place.PlaceProfileApi;
import com.rzx.dim4.base.utils.ParamHandleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网费余额明细
 * 注意：这是公共类，方便user-server和Marketing-server服务继承，收银台和小程序都在调用，禁止在这个类上加Component和Service
 * <p>
 * 这样做的目的是：减少微服务链路数（其实可以直接放在marketing服务中，小程序调用时，会增加一层链路数，增加了失败的概率）
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Slf4j
public abstract class CommonBalanceDetailsService {
    @Resource
    private BalanceDetailsApi balanceDetailsApi;

    @Resource
    private PlaceServerService placeServerService;

    @Resource
    private PlaceProfileApi placeProfileApi;

    /**
     * 分页查询网费余额明细
     * @param placeId
     * @param idNumber
     * @param startDate
     * @param endDate
     * @param type  网费类型，0支出，1收入
     * @param accountType   账户类型，0本金，1奖励
     * @param page
     * @param size
     * @return
     */
    public GenericResponse<PagerDTO<BalanceDetailsBO>> findPageList(String placeId, String idNumber, String startDate, String endDate, String type, String accountType,int page, int size) {

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        log.info("CommonBalanceDetailsService.findPageList Params=(placeId={},idNumber={},type={},startDate={},endDate={},start={},size={})", placeId, idNumber, type, startDate, endDate, page, size);

        Map<String, String> params = new HashMap<>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("placeId", placeId);
        params.put("idNumber", idNumber);
        params.put("type", type);
        params.put("accountType", accountType);
        params.put("deleted", "0");

        page = ParamHandleUtil.handlePage(page);
        size = ParamHandleUtil.handleSize(size);

        GenericResponse<PagerDTO<BalanceDetailsBO>> balanceDetailsResponse = balanceDetailsApi.queryBalanceDetails(params, size, page);
        if (!balanceDetailsResponse.isResult() || CollectionUtils.isEmpty(balanceDetailsResponse.getData().getList())) {
            return new GenericResponse<>(new PagerDTO<>(0, Collections.emptyList()));
        }
        List<BalanceDetailsBO> balanceDetailsBoList = balanceDetailsResponse.getData().getList();
        List<String> placeIdList = balanceDetailsBoList.stream().map(BalanceDetailsBO::getPlaceId).distinct().collect(Collectors.toList());

        // 获取网吧名称
        Map<String, String> placeNameMap = new HashMap<>();
        Map<String, String> queryParam = new HashMap<>();
        queryParam.put("placeIds", String.join(",", placeIdList));
        GenericResponse<ListDTO<PlaceProfileBO>> placeProfileResponse = placeProfileApi.queryAll(queryParam);
        if (placeProfileResponse.isResult()) {
            List<PlaceProfileBO> PlaceProfileBOs = placeProfileResponse.getData().getList();
            PlaceProfileBOs.forEach(e -> {
                placeNameMap.put(e.getPlaceId(), e.getDisplayName());
            });
        }
       // String placeName = placeProfileResponse.isResult() ? placeProfileResponse.getData().getObj().getDisplayName() : "";

        // 获取机器名称
        Map<String, Map<String, String>> clientNameMap = new HashMap<>();
        placeIdList.forEach(e -> {
            Set<String> clientIds = balanceDetailsBoList.stream().filter(item ->
                    !StringUtils.isEmpty(item.getClientId()) && SpecialPlaceClients.isNotSpecialPlaceClient(item.getClientId()) && e.equals(item.getPlaceId())
            ).map(BalanceDetailsBO::getClientId).collect(Collectors.toSet());
            // 批量获取ClientName保存到Map中
            GenericResponse<ListDTO<PlaceClientBO>> placeClientResponse = placeServerService.queryAllClientByClientIds(e, new ArrayList<>(clientIds));
            if (placeClientResponse.isResult()) {
                List<PlaceClientBO> placeClientBOS = placeClientResponse.getData().getList();
                Map<String, String> clientIdsMap = SpecialPlaceClients.toMap(); // 不要忘记这几个特殊的情况
                placeClientBOS.forEach(o -> {
                    if (clientIds.contains(o.getClientId())) {
                        clientIdsMap.put(o.getClientId(),o.getHostName());
                    }
                });
                if (!ObjectUtils.isEmpty(clientIdsMap)) {
                    clientNameMap.put(e, clientIdsMap);
                }
            }
        });

        // 批量获取ClientName保存到Map中
//        Map<String, String> placeClientMap = SpecialPlaceClients.toMap();
//        ResultHandleUtil.handleCollection(clientIds, checkedClientIds -> {
//            GenericResponse<ListDTO<PlaceClientBO>> placeClientResponse = placeServerService.queryAllClientByClientIds(placeId, new ArrayList<>(checkedClientIds));
//            ResultHandleUtil.handleListResponse(placeClientResponse, placeClientBoList -> placeClientBoList.forEach(placeClientBo -> {
//                placeClientMap.put(placeClientBo.getClientId(), placeClientBo.getHostName());
//            }));
//        });

        balanceDetailsBoList.forEach(item -> {
            // 设置网吧名称
            item.setPlaceName(placeNameMap.get(item.getPlaceId()));
            // 设置客户端名称
            if (!ObjectUtils.isEmpty(clientNameMap)) {
                clientNameMap.keySet().forEach(key -> {
                    if (item.getPlaceId().equals(key)) {
                        item.setClientName(clientNameMap.get(key).get(item.getClientId()));
                    }
                });
            }
        });

        return new GenericResponse<>(new PagerDTO<>(balanceDetailsResponse.getData().getTotal(), balanceDetailsBoList));
    }
}