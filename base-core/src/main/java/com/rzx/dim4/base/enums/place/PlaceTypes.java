package com.rzx.dim4.base.enums.place;

/**
 * 场所类型
 *
 * <AUTHOR>
 * @date 2019年10月9日上午10:09:22
 */
public enum PlaceTypes {

	//	Cybercafe(0), // 网吧
//	Hotel(1), // 酒店
//	Rental(2), // 网租
	Normal(3), // 普通场所
	;

	private final int value;

	private PlaceTypes(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static PlaceTypes getPlaceTypes(int index) {
		for (PlaceTypes placeTypes : PlaceTypes.values()) {
			if (placeTypes.getValue() == index) {
				return placeTypes;
			}
		}
		return PlaceTypes.Normal;
	}

}
