package com.rzx.dim4.base.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * datatables 插件列对象
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DataTablesColumn {
	/**
	 * 列对应字段
	 */
	private String data;

	private String name;
	private Boolean searchable;
	private Boolean orderable;
	/**
	 * 筛选对象
	 */
	private DataTablesSearch search;
}
