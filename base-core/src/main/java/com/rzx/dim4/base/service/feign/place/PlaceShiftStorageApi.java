package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.bo.place.PlaceShiftStorageBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceShiftApiHystrix;
import com.rzx.dim4.base.service.callback.place.PlaceShiftStorageApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * 班次库存数据接口
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceShiftStorageApi", fallback = PlaceShiftStorageApiHystrix.class)
public interface PlaceShiftStorageApi {

    String URL = "/feign/place/placeShiftStorage";


    /**
     * 根据placeId和shiftId查询库存数据
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping(URL + "/findAllByPlaceIdAndShiftId")
    GenericResponse<ListDTO<PlaceShiftStorageBO>> findAllByPlaceIdAndShiftId(@RequestParam String placeId,
                                                                             @RequestParam String shiftId);

    @PostMapping(URL+"/clearLatest")
    GenericResponse<SimpleDTO> clearLatest(@RequestParam String accountId,
                                           @RequestParam String placeId);
}
