package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Getter
@Setter
public class StatisticRefundExportVO implements Serializable {
    private static final long serialVersionUID = -183120736797340483L;

    @ExcelProperty(value = "商品名称",index = 0)
    private String goodsName; // 商品名称

    @ExcelProperty(value = "退货量",index = 1)
    private int countRefund; // 退货量

    @ExcelProperty(value = "退货均价",index = 2)
    private float avgRefundPrice; // 退货均价

    @ExcelProperty(value = "损益均价",index = 3)
    private float avgProfitLossPrice; // 损益均价

    @ExcelProperty(value = "平均折扣",index = 4)
    private float avgDiscount; // 平均折扣
}
