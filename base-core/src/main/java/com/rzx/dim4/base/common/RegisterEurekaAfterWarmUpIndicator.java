package com.rzx.dim4.base.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

/**
 * 流程：服务注册到Eureka后，其状态状态是DOWN，预热90秒后，DiscoveryClient会刷新服务状态UP到注册中心Eureka上
 * <p>
 * 目的：服务启动完成后，在大概30-60秒内，JVM会进行一些初始化操作，比如加载静态资源，初始化静态方法等等，这些操作会占用一些时间，
 * 导致服务处理请求能力不足，甚至宕机。预热60秒就是给JVM一些时间初始化，服务处理请求能力才会达到正常水平。
 * <p>
 * 为啥是60秒? 因为60秒钟内，大概有10-30秒的时间是在启动过程中就花掉了，真正预热的时间就剩下30-50秒了。
 * <p>
 * 原理：根据Eureka源码，DiscoveryClient在启动时，调用getHealthCheckHandler方法，获取EurekaHealthCheckHandler.afterPropertiesSet()加载HealthIndicator.class，
 * 此时，就会加载本类RegisterEurekaAfterWarmUpIndicator，再调用health()方法，判断isWarmupCompleted是否为true，如果为true，则返回UP，否则返回DOWN
 * <p>
 * 注意：想要使用本类，需在application.yml中配置eureka.client.healthcheck.enabled=true
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
public class RegisterEurekaAfterWarmUpIndicator implements HealthIndicator {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 预热时间 60秒
    private static final long WARMUP_WAITING_TIME = 60 * 1000L;

    // 是否预热完成
    private volatile boolean isWarmupCompleted = false;

    @PostConstruct
    public void init() {
        // 异步等待预热完成
        CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();

            log.info("========>开始预热,请耐心等待 @ {}", DATE_FORMATTER.format(LocalDateTime.now()));
            try {
                Thread.sleep(WARMUP_WAITING_TIME);
            } catch (Exception e) {
                log.info("========>温馨提示：预热失败,但不影响服务正常启动。服务不会进行预热,按照以前流程,注册服务信息到Eureka上,其服务状态立马为UP,预热异常信息：{}", e.getMessage());
            }

            // 不论预热成功还是失败，都将 isWarmupCompleted 设置为true
            isWarmupCompleted = true;

            log.info("========>预热完成 @ {},耗时：{}秒", DATE_FORMATTER.format(LocalDateTime.now()), (System.currentTimeMillis() - startTime) / 1000L);
        });
    }

    @Override
    public Health health() {
        if (isWarmupCompleted) {
            return Health.up().withDetail("message", "服务正常").build();
        } else {
            log.info("========>预热中,请耐心等待 @ {}", DATE_FORMATTER.format(LocalDateTime.now()));
            return Health.down().withDetail("message", "预热中").build();
        }
    }
}