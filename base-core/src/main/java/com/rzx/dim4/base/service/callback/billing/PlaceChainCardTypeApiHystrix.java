package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceChainCardTypeBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceChainCardTypeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/8
 **/
@Slf4j
@Service
public class PlaceChainCardTypeApiHystrix implements PlaceChainCardTypeApi {

    @Override
    public GenericResponse<?> add(PlaceChainCardTypeBO placeChainCardTypeBO) {
        log.error("add 接口异常, placeChainCardTypeBO={}", new Gson().toJson(placeChainCardTypeBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> edit(PlaceChainCardTypeBO placeChainCardTypeBO) {
        log.error("edit 接口异常, placeChainCardTypeBO={}", new Gson().toJson(placeChainCardTypeBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    /**
     * 根据连锁id获取连锁下的会员卡列表
     *
     * @param chainId
     * @return
     */
    @Override
    public GenericResponse<ListDTO<PlaceChainCardTypeBO>> queryByChainId(String requestTicket,String chainId) {
        log.error("接口异常::: queryChainCardTypeByChainId,,,requestTicket:::{}, chainId:::{} ", requestTicket,chainId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceChainCardTypeBO>> page(String requestTicket, DataTablesRequest dtRequest) {
        log.error("page 接口异常, dtRequest={}", new Gson().toJson(dtRequest));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> delete(String requestTicket, String chainId, String cardTypeId) {
        log.error("delete 接口异常, chainId={}, cardTypeId={}", chainId, cardTypeId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
