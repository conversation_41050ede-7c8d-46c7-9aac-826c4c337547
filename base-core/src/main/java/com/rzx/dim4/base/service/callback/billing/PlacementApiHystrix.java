package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.PlacementVO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlacementApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025年05月10日 11:30
 */
@Slf4j
@Component
public class PlacementApiHystrix implements PlacementApi {
    @Override
    public GenericResponse<ListDTO<PlacementVO>> findPlacementByPlaceId(String placeId) {
        log.error("findPlacementByPlaceId 接口异常,  placeId={}", placeId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
