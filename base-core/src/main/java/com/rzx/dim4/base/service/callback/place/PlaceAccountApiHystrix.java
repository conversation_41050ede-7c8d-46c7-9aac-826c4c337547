package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.LoginCashierBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceAccountApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/31
 **/
@Slf4j
@Service
public class PlaceAccountApiHystrix implements PlaceAccountApi {
    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> findExistCashierAccount(String requestTicket, String placeId, String loginName) {
        log.error("findCashierExist 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> get(String requestTicket, String placeId, int type, String loginName) {
        log.error("get 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> getCashier(String requestTicket, String placeId, int type, String accountId) {
        log.error("get 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> findByTypeAndLoginName(int type, String loginName) {
        log.error("get 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> loginAtCashier(String requestTicket, LoginCashierBO loginCashierBO) {
        log.error("submitCheckAccount 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> resetPassword(String requestTicket, String placeId, String accountId) {
        log.error("resetPassword 接口异常");
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByChainIdAndAccountId(String chainId, String accountId) {
        log.error("findPlaceAccountByChainIdAndAccountId 接口异常{},{}", chainId, accountId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceAccountBO>> findByPlaceIdAndAccountName(String placeId, String accountName) {
        log.error("findByPlaceIdAndAccountName 接口异常{},{}", placeId, accountName);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceAccountBO>> findPageByDisplayName(String requestTicket, String displayName, String mobile, int page, int size) {
        log.error("findPageByDisplayName 接口异常{},{}", displayName, mobile);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> unlockAccount(String requestTicket, String placeId, String mobile) {
        log.error("unlockAccount 接口异常{},{}", placeId, mobile);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceAccountBO>> findByMobile(String requestTicket, String mobile) {
        log.error("findByMobile 接口异常mobile={}", mobile);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceAccountBO>> findByMobileAndPlaceIds(String requestTicket, String mobile, List<String> placeIds, int page, int size) {
        log.error("findByMobileAndPlaceIds 接口异常mobile={}", mobile);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceAccountBO>> findByPlaceIdAndType(String placeId, int type) {
        log.error("findByPlaceIdAndType 接口异常placeId={}, type={}", placeId, type);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceAccountBO>> findByPlaceId(String placeId) {
        log.error("findByPlaceId 接口异常placeId={}", placeId);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceAccountBO>> findByAccountIds(List<String> accountIds) {
        log.error("findByAccountIds 接口异常accountIds={}", accountIds);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceAccountBO>> pageList(Map<String, Object> params, int page, int size) {
        log.error("pageList 接口异常 params={}", new Gson().toJson(params));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> deleteById(String id) {
        log.error("deleteById 接口异常 id={}", id);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
