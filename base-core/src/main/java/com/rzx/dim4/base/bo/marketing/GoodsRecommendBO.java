package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 首页推广商品信息表
 * <AUTHOR>
 * @date 2024年12月04日 10:25
 */
@Getter
@Setter
public class GoodsRecommendBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;

    private String placeId; // 场所ID

    private float startTime; // 开始时间

    private float endTime; // 结束时间

    private String goodsIds; // 结束时间

    //转换为时间格式
    private LocalDateTime startDateTime;
    private LocalDateTime endDateTime;

    private List<GoodsBO> goodsList;
    private List<String> goodsIdList;
}
