package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Accessors(chain = true)
@ApiModel("租赁商品折扣信息")
public class RentGoodsDiscountResponseBO extends AbstractBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "租赁商品Id")
    private String rentGoodsId;

    @ApiModelProperty(value = "押金折扣率，100默认不打折，0代表免费")
    private int depositDiscount;

    @ApiModelProperty(value = "租金折扣率，100默认不打折，0代表免费")
    private int rentDiscount;
}
