package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.MarketOrderStatisticsBO;
import com.rzx.dim4.base.bo.marketing.MiniAppTopUpOrdersBO;
import com.rzx.dim4.base.bo.marketing.OrdersStatusMiniAppBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.*;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingOrderApi;
import com.rzx.dim4.base.vo.marketing.StaticCouponVO;
import com.rzx.dim4.base.vo.marketing.StatisticsShopBusinessVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;

/**
 * <AUTHOR> hwx
 * @since 2025/2/21 17:22
 */
@Component
@Slf4j
public class MarketingOrderApiHystrix implements MarketingOrderApi {
    @Override
    public GenericResponse<ObjDTO<MarketOrderStatisticsBO>> statisticsPlaceShift(@RequestParam String placeId,
                                                                                 @RequestParam LocalDateTime startDateTime,
                                                                                 @RequestParam LocalDateTime endDateTime, @RequestParam String shiftId, boolean isSubmit) {
        log.error("接口异常:::statistics(placeId:::{},startDateTime:::{},endDateTime:::{},shiftId:::{})", placeId, startDateTime, endDateTime, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createOrderForMiniApp(@RequestBody MiniAppTopUpOrdersBO ordersBO) {
        log.error("接口异常:::MarketingOrderApi.createOrderForMiniApp(ordersBO = {})", new Gson().toJson(ordersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> refundOrderForMiniApp(InternetFeeOrderRefundBO paramsBo) {
        log.error("接口异常:::MarketingOrderApi.refundOrderForMiniApp::::placeId: {},orderId: {}", paramsBo.getPlaceId(), paramsBo.getOrderId());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> refundInternetFeePackageTimeOrderForMiniApp(InternetFeeOrderRefundBO paramsBo) {
        log.error("接口异常:::MarketingOrderApi.refundInternetFeePackageTimeOrderForMiniApp::::placeId: {},orderId: {}", paramsBo.getPlaceId(), paramsBo.getOrderId());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InternetFeePackageOrderBO>> findPackageOrderPageListForMiniApp(@SpringQueryMap InternetFeeSearchPackageTimeRecordBO paramsBo) {
        log.error("接口异常:::MarketingOrderApi.findPackageOrderPageListForMiniApp(paramsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<InternetFeeTopUpOrderBO>> findTopUpOrderPageListForMiniApp(@SpringQueryMap InternetFeeSearchTopUpRecordBO paramsBo) {
        log.error("接口异常:::MarketingOrderApi.findTopUpOrderPageListForMiniApp(paramsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StatisticsShopBusinessVO>> statisticsShopBusinessVO(String placeId, String shiftId) {
        log.error("接口异常:::statisticsShopBusinessVO(placeId:::{},shiftId:::{})", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StaticCouponVO>> staticCouponVO(String placeId, String shiftId) {
        log.error("接口异常:::staticCouponVO(placeId:::{},shiftId:::{})", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersStatusMiniAppBO>> queryOrderStatus(String placeId, String orderId) {
        log.error("接口异常:::MarketingOrderApi.queryOrderStatus::::placeId: {},orderId: {}", placeId, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
