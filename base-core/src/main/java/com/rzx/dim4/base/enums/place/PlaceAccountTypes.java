package com.rzx.dim4.base.enums.place;

/**
 * 场所账号类型
 * 
 * <AUTHOR>
 * @date 2019年10月9日上午11:44:48
 */
public enum PlaceAccountTypes {

	AGENT(0), // 代理商
	MANAGER(1), // 店长
	CASHIER(2), // 收银员
	AREA_ACCOUNT(3), // 大区账号
	CHAIN_ADMIN(4), // 连锁账号
	CHAIN_SUB_ACCOUNT(5), // 连锁子账号
	;

	private final int value;

	private PlaceAccountTypes(int value) {
		this.value = value;
	}

	public int getValue() {
		return value;
	}

	public static PlaceAccountTypes getPlaceAccountTypes(int i) {
		for (PlaceAccountTypes placeAccountTypes : PlaceAccountTypes.values()) {
			if (i == placeAccountTypes.getValue()) {
				return placeAccountTypes;
			}
		}
		return null;
	}
}
