package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.RentApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 租赁相关api接口
 *
 * <AUTHOR> hwx
 * @since 2025/3/4 11:57
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "RentApi", path = "/feign/marketing/rent", fallback = RentApiHystrix.class)
public interface RentApi {

    /**
     * 保存租赁设置(初始化)
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @PostMapping("/initRentConfig")
    GenericResponse<ObjDTO<RentConfigBO>> initRentConfig(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId);


    /**
     * 通过场所编码查询
     *
     * @param placeId
     * @return
     */
    @GetMapping("/findConfigByPlaceId")
    GenericResponse<ObjDTO<RentConfigBO>> findConfigByPlaceId(@RequestParam String placeId);


    /**
     * 通过场所编码，会员卡号码查询用户的租赁订单
     *
     * @param placeId
     * @return
     */
    @PostMapping("/findRentOrderBySomeCondition")
    GenericResponse<ListDTO<OrdersBO>> findRentOrderBySomeCondition(@RequestParam String placeId,
                                                                    @RequestParam String cardId);


    /**
     * 保存/编辑会员卡类型折扣
     *
     * @param requestTicket
     * @param bo
     * @return
     */
    @PostMapping("/saveOrUpdateRentMemberDiscount")
    GenericResponse<ObjDTO<RentMemberDiscountBO>> saveOrUpdateRentMemberDiscount(@RequestHeader("request_ticket") String requestTicket, @RequestBody RentMemberDiscountBO bo);



    /**
     * 保存/编辑区域类型折扣
     *
     * @param requestTicket
     * @param bo
     * @return
     */
    @PostMapping("/saveOrUpdateRentAreaDiscount")
    GenericResponse<ObjDTO<RentAreaDiscountBO>> saveOrUpdateRentAreaDiscount(@RequestHeader("request_ticket") String requestTicket, @RequestBody RentAreaDiscountBO bo);


    /**
     * 保存/编辑区域类型折扣(入参为List<PlaceAreaBO> placeAreaBOList)
     *
     * @param requestTicket
     * @param placeId
     * @param placeAreaBOList
     * @return
     */
    @PostMapping("/updateRentAreaDiscountByAreaIds")
    GenericResponse<ObjDTO<RentAreaDiscountBO>> updateRentAreaDiscountByAreas(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId,@RequestBody List<PlaceAreaBO> placeAreaBOList);


    /**
     * 保存/编辑会员折扣(入参为List<BillingCardTypeBO> billingCardTypeBOList)
     *
     * @param requestTicket
     * @param placeId
     * @param billingCardTypeBOList
     * @return
     */
    @PostMapping("/updateRentMemberDiscountByCardTypes")
    GenericResponse<ObjDTO<RentMemberDiscountBO>> updateRentMemberDiscountByCardTypes(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId,@RequestBody List<BillingCardTypeBO> billingCardTypeBOList);
}
