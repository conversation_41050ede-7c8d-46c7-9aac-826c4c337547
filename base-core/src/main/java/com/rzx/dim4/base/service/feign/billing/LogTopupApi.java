package com.rzx.dim4.base.service.feign.billing;


import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.LogTopupApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 查询网费余额明细
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "logTopupApi", fallback = LogTopupApiHystrix.class)
public interface LogTopupApi {


    String URL = "/feign/billing/logTopup";
    @PostMapping(URL + "/createLogTopup")
    GenericResponse<ObjDTO<LogTopupBO>> createLogTopup(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody LogTopupBO logTopupBO);


    @PostMapping(URL + "/updateLogTopup")
    GenericResponse<?> updateLogTopup(@RequestHeader(value = "request_ticket") String requestTicket,  @RequestBody OrdersBO ordersBO);
}
