package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.*;
import java.util.List;

@Getter
@Setter
@XmlRootElement(name = "charge_reward")
@XmlAccessorType(XmlAccessType.NONE)
@ToString
public class TopupRuleXml {

    /**
     * 充值金额xml解析对象
     * 单位：元
     */
    @XmlElement(name = "charge")
    private String chargeXml;

    /**
     * 奖励金额xml解析对象
     * 单位：元
     */
    @XmlElement(name = "reward")
    private String rewardXml;

    /**
     * 是否分期xml解析对象
     * 分期次数
     */
    @XmlElement(name = "by_stages")
    private int byStages;

    /**
     * 关联卡类型名称集合
     * 用于解析xml
     */
    @XmlElementWrapper(name = "charge_reward_card_names")
    @XmlElement(name = "charge_reward_card_name")
    private List<String> cardNames;

}
