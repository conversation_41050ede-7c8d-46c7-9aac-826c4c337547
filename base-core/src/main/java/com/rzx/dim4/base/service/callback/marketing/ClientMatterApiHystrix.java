package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.marketing.ClientMatterBO;
import com.rzx.dim4.base.bo.marketing.ClientMatterQuantityBO;
import com.rzx.dim4.base.bo.marketing.OrderRefundBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.ClientMatterApi;
import com.rzx.dim4.base.vo.marketing.MarketingTipsListVO;
import com.rzx.dim4.base.vo.marketing.MarketingTipsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class ClientMatterApiHystrix implements ClientMatterApi {
    @Override
    public GenericResponse<ObjDTO<ClientMatterBO>> save(@RequestHeader("request_ticket") String requestTicket, @RequestBody ClientMatterBO bo) {
        log.error("接口异常,ClientMatterFeignApiHystrix#save,bo:{}", new Gson().toJson(bo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientMatterQuantityBO>> queryClientMatterNum(String requestTicket, String placeId) {
        log.error("接口异常,ClientMatterFeignApiHystrix#queryClientMatterNum,placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<MarketingTipsListVO>> queryClientMatter(String requestTicket, String placeId,int page,int size) {
        log.error("接口异常,ClientMatterFeignApiHystrix#queryClientMatter,placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> queryMatterUser(String requestTicket, String placeId, String idNumber) {
        log.error("接口异常,ClientMatterFeignApiHystrix#queryMatterUser,placeId:{},idNumber:{}", placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ClientMatterBO>> updateClientMatter(String requestTicket, String placeId, String matterId) {
        log.error("接口异常,ClientMatterFeignApiHystrix#updateClientMatter,placeId:{},matterId:{}", placeId, matterId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> modifyOrderStatus(String requestTicket, int status, String goodsId, String placeId, String orderId,String accountId) {
        log.error("接口异常,ClientMatterFeignApiHystrix#modifyOrderStatus,status:{},goodsId:{},placeId:{},orderId:{},accountId:{}", status, goodsId, placeId, orderId,accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrderRefundBO>> refundOrder(String requestTicket, OrderRefundBO refundBO) {
        log.error("接口异常,ClientMatterFeignApiHystrix#refundOrder,refundBO:{}",refundBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> bigScreenLogin(String placeId, String accountId, String accountName, String code) {
        log.error("接口异常,bigScreenLogin#refundOrder,placeId:{},accountId:{},accountName:{},code:{}",placeId, accountId, accountName, code);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> clientMatterExpiredRemind() {
        log.error("接口异常,clientMatterExpiredRemind");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> handleLeaveWordData(String placeId, String clientId) {
        log.error("接口异常,handleLeaveWordData# placeId:{},clientId:{}",placeId, clientId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> deliveryOrderForOrder(String requestTicket, int status, String goodsId, String placeId, String orderId,String accountId) {
        log.error("接口异常,ClientMatterFeignApiHystrix#deliveryOrderForOrder,status:{},goodsId:{},placeId:{},orderId:{},accountId:{}", status, goodsId, placeId, orderId,accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> refundOrderForOrder(String requestTicket, OrderRefundBO refundBO) {
        log.error("接口异常,ClientMatterFeignApiHystrix#refundOrderForOrder,refundBO:{}",refundBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
