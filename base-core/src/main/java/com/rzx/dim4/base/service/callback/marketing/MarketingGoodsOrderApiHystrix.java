package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.MiniAppOrderQueryBO;
import com.rzx.dim4.base.bo.marketing.statistics.StatisticsMiniAppOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年02月21日 11:12
 */
@Slf4j
@Component
public class MarketingGoodsOrderApiHystrix implements MarketingGoodsOrderApi {

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> getOrder(String orderId, String placeId) {

        log.error("接口异常，MarketingGoodsOrderApiHystrix.getOrder(placeId={},orderId={})",placeId,orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createShopOrder(String requestTicket, OrdersBO ordersBO) {
        log.error("接口异常，MarketingGoodsOrderApiHystrix.createShopOrder(ordersBO={})", new Gson().toJson(ordersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<OrdersBO>> queryOrderList( String placeId, String idNumber, int page, int size) {
        log.error("接口异常，MarketingGoodsOrderApiHystrix.queryOrderList(placeId={},idNumber={})",placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> miniAppCreateShopOrder(String requestTicket, OrdersBO ordersBO) {
        log.error("接口异常，MarketingGoodsOrderApiHystrix.miniAppCreateShopOrder(ordersBO={})", new Gson().toJson(ordersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StatisticsMiniAppOrderBO>> miniAppQueryOrderList(MiniAppOrderQueryBO queryBO) {
        log.error("接口异常，MarketingGoodsOrderApiHystrix.miniAppQueryOrderList(queryBO={})", new Gson().toJson(queryBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> createGoodsGiftOrder(OrdersBO ordersBO) {
        log.error("接口异常，MarketingGoodsOrderApiHystrix.createGoodsGiftOrder(queryBO={})", new Gson().toJson(ordersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


}
