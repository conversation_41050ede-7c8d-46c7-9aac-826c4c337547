package com.rzx.dim4.base.vo.place;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.billing.BalanceDetailsShiftBO;
import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.bo.place.PlaceShiftCouponBo;
import com.rzx.dim4.base.vo.marketing.StaticCouponVO;
import com.rzx.dim4.base.vo.marketing.StatisticsShopBusinessVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 交班统计数据
 *
 * <AUTHOR> hwx
 * @since 2025/2/21 17:21
 */
@Getter
@Setter
@NoArgsConstructor
public class PlaceShiftDetailVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -6103865351957254174L;

    private PlaceShiftBO placeShiftBO;

    /**
     * 销售数据
     */
    private List<PlaceShiftDetailSaleItemVO> saleItems = new ArrayList<>();

    /**
     * 货架固装商品分类数据
     */
    private List<PlaceShiftStorageItemVO> storageItems = new ArrayList<>();

    /**
     * 优惠券数据
     */
    private List<PlaceShiftCouponBo> couponBos = new ArrayList<>();

    /**
     * 商超经营概况
     */
    private StatisticsShopBusinessVO shopBusinessVos;

    /**
     * 网费赠送数据
     */
    private BalanceDetailsShiftBO internetGift;

    /**
     * 优惠券赠送数据
     */
    private StaticCouponVO staticCouponVos;

    /**
     * 未配送的商品订单数量
     */
    private int nonDeliveryNumber;
}
