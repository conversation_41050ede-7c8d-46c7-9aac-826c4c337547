package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.bo.user.MiniApp.MiniAppUserBO;
import com.rzx.dim4.base.bo.user.WeChatUserBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.UserServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * 场所服务 待删除，不添加新接口
 * <AUTHOR>
 * @date Jun 15, 2020 11:01:38 AM
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.USER_SERVER,contextId = "UserServerService", fallback = UserServerServiceHystrix.class)
public interface UserServerService {

	@PostMapping("/user/dim4/save")
	public GenericResponse<ObjDTO<Dim4UserBO>> saveUser(@RequestHeader(value = "request_ticket") String requestTicket,
			@RequestParam String name, @RequestParam String idNumber);

	@GetMapping("/user/dim4/userId/{userId}")
	public GenericResponse<ObjDTO<Dim4UserBO>> getUserByUserId(@PathVariable("userId") String userId);

	@GetMapping("/user/dim4/idNumber/{idNumber}")
	public GenericResponse<ObjDTO<Dim4UserBO>> getUserByIdNumber(@PathVariable("idNumber") String idNumber);

	@GetMapping("/user/wechat/idNumber/{idNumber}")
	public GenericResponse<ObjDTO<WeChatUserBO>> getWechatUserByIdNumber(@PathVariable("idNumber") String idNumber);

	@PostMapping("/user/wechat/mp/qrcode/create")
	public GenericResponse<SimpleDTO> createQrCode(@RequestParam("sceneStr") String sceneStr);

	@GetMapping("/user/weChat/payOrder/sendTopupSuccess")
	void weChatSendTopupSuccess(@RequestParam("paramMapJson") String paramMapJson);

	@PostMapping("/miniApp/user/queryMiniAppUsers")
	public GenericResponse<PagerDTO<MiniAppUserBO>> queryMiniAppUsers(@RequestParam String placeId,
																	  @RequestParam(name = "size", defaultValue = "10") int size,
																	  @RequestParam(name = "page", defaultValue = "1") int page,
																	  @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
																	  @RequestParam(name = "order", defaultValue = "desc") String order,
																	  @RequestParam(required = false) String accountIds);

	@GetMapping("/miniApp/user/unbound")
	public GenericResponse<SimpleDTO> miniAppUnbound (@RequestHeader(value = "request_ticket") String requestTicket,
													  @RequestParam String placeId,
													  @RequestParam String openid);
}
