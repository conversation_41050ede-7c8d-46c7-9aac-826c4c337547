package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 货架信息表
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@ApiModel("货架信息表")
public class GoodsStorageRackResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "货架ID: 场所唯一，从100000开始递增,主仓库为000000")
    private String storageRackId;

    @ApiModelProperty(value = "货架名称")
    private String storageRackName;

    @ApiModelProperty(value = "收银台ID")
    private String cashierId;

    @ApiModelProperty(value = "客户端id列表，和货架收银台绑定后客户端购买商品推送到指定收银台")
    private String clientIds;
}
