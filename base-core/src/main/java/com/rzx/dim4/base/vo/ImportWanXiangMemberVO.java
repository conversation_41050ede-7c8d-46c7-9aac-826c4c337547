package com.rzx.dim4.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportWanXiangMemberVO {

    @ExcelProperty(value = "万象cardId", index = 0)
    private String cardId;

    @ExcelProperty(value = "带星号的证件号", index = 1)
    private String cardIdShow;

    @ExcelProperty(value = "带星号的姓名", index = 2)
    private String nameShow;

    @ExcelProperty(value = "总余额(元)", index = 3)
    private String remain;

}
