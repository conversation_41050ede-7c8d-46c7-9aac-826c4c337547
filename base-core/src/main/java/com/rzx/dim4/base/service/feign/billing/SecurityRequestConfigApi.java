package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.SecurityRequestConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.SecurityRequestConfigApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 接口安全请求次数设置
 * <AUTHOR>
 * @date 2024年5月31
 */

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "securityRequestConfigApi", fallback = SecurityRequestConfigApiHystrix.class)
public interface SecurityRequestConfigApi {

    String URL="/billing/admin/billing/securityRequestConfig";

    @GetMapping(URL+"/findByPlaceIdIn")
    GenericResponse<ListDTO<SecurityRequestConfigBO>> findSecurityRequestConfigByPlaceIdIn(@RequestParam List<String> placeIds);
}
