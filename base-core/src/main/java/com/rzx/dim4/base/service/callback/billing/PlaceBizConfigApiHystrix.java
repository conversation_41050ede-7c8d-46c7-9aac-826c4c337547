package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.PlaceBizConfigBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceBizConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/12
 **/
@Slf4j
@Component
public class PlaceBizConfigApiHystrix implements PlaceBizConfigApi {
    @Override
    public GenericResponse<ObjDTO<PlaceBizConfigBO<Float>>> query(String placeId) {
        log.error("接口异常:::queryPlaceBizConfig(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> save(String requestTicket, PlaceBizConfigBO<Float> placeBizConfigBO) {
        log.error("接口异常:::savePlaceBizConfig(placeBizConfigBO:::{}, requestTicket:::{})", placeBizConfigBO,
                requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveAntiAddiction(String requestTicket, PlaceBizConfigBO<Float> placeBizConfigBO) {
        log.error("接口异常:::saveAntiAddiction(placeBizConfigBO:::{}, requestTicket:::{})", placeBizConfigBO,
                requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> closeBizConfigTempCardPointsUpgrade(String requestTicket, String placeId,
                                                                  int upgradeUserLevelFlag,int downgradeUserLevelFlag) {
        log.error("接口异常:::closeBizConfigTempCardPointsUpgrade(placeId:::{}, upgradeUserLevelFlag:::{}, downgradeUserLevelFlag:::{}, requestTicket:::{})", placeId,upgradeUserLevelFlag,
                downgradeUserLevelFlag,requestTicket);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceBizConfigBO>> queryByPlaceIds(List<String> placeIds) {
        log.error("接口异常:::queryByPlaceIds(placeIds:::{}",placeIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceBizConfigBO>> queryByThirdAccountIds(String requestTicket,
                                                                             List<String> thirdAccountIds) {
        log.error("接口异常:::queryByThirdAccountIds(thirdAccountIds:::{}",thirdAccountIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
