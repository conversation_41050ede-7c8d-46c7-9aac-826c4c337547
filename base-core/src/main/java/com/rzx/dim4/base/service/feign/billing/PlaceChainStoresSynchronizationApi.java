package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceChainStoresSynchronizationApiHystrix;
import com.rzx.dim4.base.service.feign.place.PlaceChainStoresApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * 注意：
 * 本接口是 place-server 服务对 PlaceChainStores 表数据更新同步使用，只允许 place-server 调用。
 * 其他服务需要更新 PlaceChainStores 表数据，请使用 place-server 的 PlaceChainStoresApi 接口。
 * @see PlaceChainStoresApi
 *
 * <AUTHOR>
 * @since 2023/6/9
 **/

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "placeChainStoresSynchronizationApi", fallback = PlaceChainStoresSynchronizationApiHystrix.class)
public interface PlaceChainStoresSynchronizationApi {

    String URL = "/feign/billing/place/chain/stores";

    /**
     *
     *
     * @apiNote 只限 place-server 调用
     *
     * @param requestTicket
     * @param placeChainStoresBO
     * @return
     */
    @PostMapping(URL + "/save")
    GenericResponse<?> save(@RequestHeader(value = "request_ticket")String requestTicket,
                           @RequestBody PlaceChainStoresBO placeChainStoresBO);


    /**
     *
     * @apiNote 只限 place-server 调用
     *
     * @param requestTicket
     * @param placeChainStoresBOS
     * @return
     */
    @PostMapping(URL + "/update")
    GenericResponse<?> update(@RequestHeader(value = "request_ticket")String requestTicket,
                              @RequestBody List<PlaceChainStoresBO> placeChainStoresBOS);

    /**
     *
     * @apiNote 只限 place-server 调用
     *
     * @param requestTicket
     * @param placeChainStoresBO
     * @return
     */
    @PostMapping(URL + "/exit")
    GenericResponse<?> exit(@RequestHeader(value = "request_ticket")String requestTicket,
                            @RequestBody PlaceChainStoresBO placeChainStoresBO);

    /**
     *
     * @apiNote 只限 place-server 调用
     *
     * @param requestTicket
     * @return
     */
    @PostMapping(URL + "/openChainAllPlaceShareMemberPoint")
    GenericResponse<?> openChainAllPlaceShareMemberPoint(@RequestHeader(value = "request_ticket")String requestTicket,
                            @RequestParam("chainId") String chainId);
}
