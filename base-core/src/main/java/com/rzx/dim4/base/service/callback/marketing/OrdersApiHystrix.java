package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.OrdersApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年03月03日 16:55
 */
@Slf4j
@Component
public class OrdersApiHystrix implements OrdersApi {
    @Override
    public GenericResponse<ObjDTO<OrdersBO>> queryOrderByPlaceIdAndOrderId(String placeId, String orderId) {
        log.error("接口异常，OrdersApi.queryOrderByPlaceIdAndOrderId(placeId={}, orderId={})", placeId, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> saveOrder(OrdersBO ordersBO) {
        log.error("接口异常，OrdersApi.saveOrder(ordersBO={})",new Gson().toJson(ordersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> notifyOrder(String orderId,String payRefundId, Integer poundage,Integer refundPoundage) {
        log.error("接口异常，OrdersApi.notifyOrder(ordersId={}，payRefundId={},poundage={},refundPoundage={})",orderId,payRefundId,poundage,refundPoundage);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> updateOrderFee(String orderId, Integer poundage) {
        log.error("接口异常，OrdersApi.updateOrderFee(ordersId={},poundage={})",orderId,poundage);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<OrdersBO>> findPage(String placeId,String idNumber,String orderType,String startTime,String endTime,int size,int page) {
        log.error("接口异常，OrdersApi.findPage(placeId={},idNumber={})",placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public int sumMoneyByPlaceAndIdNumberAndTime(String placeId, String idNumber, LocalDateTime startDate, LocalDateTime endDate) {
        log.error("接口异常，OrdersApi.sumMoneyByPlaceAndIdNumberAndTime(placeId={},idNumber={},startDate ={},endDate={})",placeId,idNumber,startDate,endDate);
        return 0;
    }

    @Override
    public int countNonDeliveryOrders(String placeId, LocalDateTime startDate, LocalDateTime endDate) {
        log.error("接口异常，OrdersApi.countNonDeliveryOrders(placeId={},startDate ={},endDate={})",placeId,startDate,endDate);
        return 0;
    }

    @Override
    public GenericResponse<ListDTO<OrdersBO>> queryRentingOrderByPlaceId(String placeId,String idNumber) {
        log.error("接口异常，OrdersApi.queryRentingOrderByPlaceId(placeId={},idNumber={})",placeId,idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);

    }
}
