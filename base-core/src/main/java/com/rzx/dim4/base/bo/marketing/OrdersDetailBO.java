package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单信息表
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ApiModel("订单详情表")
public class OrdersDetailBO extends AbstractEntityBO {

    @ApiModelProperty("场所ID")
    private String placeId;

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("计费卡ID")
    private String cardId;

    @ApiModelProperty("身份证号码")
    private String idNumber;

    @ApiModelProperty("身份证姓名")
    private String idName;

    @ApiModelProperty("订单状态，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消")
    private int status;

    @ApiModelProperty("订单类型1 商品订单，2团购订单，3网费充值订单，4包时订单，9自定义收款")
    private int orderType;

    @ApiModelProperty("卡类型ID")
    private String cardTypeId;

    @ApiModelProperty("卡类型名称")
    private String cardTypeName;

    @ApiModelProperty("订单支付时间")
    private LocalDateTime payTime;

    @ApiModelProperty("订单完成时间")
    private LocalDateTime finishedTime;

    @ApiModelProperty("订单退款时间")
    private LocalDateTime refundTime;

    @ApiModelProperty("来源")
    private SourceType sourceType;

    @ApiModelProperty("订单合计价格")
    private int totalMoney;

    @ApiModelProperty("订单实际金额（优惠后,实际付款金额）")
    private int realMoney;

    @ApiModelProperty("手续费")
    private int fee;

    @ApiModelProperty("支付方式")
    private PayType payType;

    @ApiModelProperty("订单备注")
    private String remark;

    @ApiModelProperty("收银台ID")
    private String cashierId;

    @ApiModelProperty("收银台名称")
    private String cashierName;

    @ApiModelProperty("创建人ID")
    private Long creater;

    @ApiModelProperty("创建时间")
    private LocalDateTime created;

    @ApiModelProperty("操作人姓名")
    private String createrName;

    @ApiModelProperty("订单中购买的商品列表")
    private List<OrderGoodsDetailBO> orderGoodsList = new ArrayList<>();


    @ApiModelProperty("包时规则id")
    private String ruleId;

    @ApiModelProperty("订单中购买的包时信息")
    private BillingRulePackageTimeBO rulePackageTimeBo;
}
