package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingCardLoginServiceApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/10/26
 **/
@Slf4j
@Service
public class BillingCardLoginServiceApiHystrix implements BillingCardLoginServiceApi {

    /**
     * 校验是否允许在客户端直接扫码激活登录
     *
     * @param placeId  场所id
     * @param idNumber 身份证号
     * @param cardId   计费卡号
     * @return
     * @apiNote placeId必填，idNumber/cardId 二选一，
     */
    @Override
    public GenericResponse<?> checkForbiddenClientActiveDirectly(String requestTicket, String placeId, String idNumber, String cardId) {
        log.error("接口异常:::checkForbiddenClientActiveDirectly(placeId:::{},idNumber:::{},cardId:::{})", placeId, idNumber, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
