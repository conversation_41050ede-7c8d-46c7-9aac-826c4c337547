package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceProfileApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @apiNote header 参数 requestTicket 必填
 * @since 2023/5/23
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceProfileApi", fallback = PlaceProfileApiHystrix.class)
public interface PlaceProfileApi {
    String URL = "/feign/place/placeProfile";

    /**
     * 获取场所基本信息
     *
     * @param placeId
     * @return
     * @throws ServiceException(ServiceCodes.NULL_PARAM)
     */
    @GetMapping(URL + "/info")
    GenericResponse<ObjDTO<PlaceProfileBO>> findPlaceByPlaceId(@RequestParam String placeId);


    @GetMapping(URL + "/client")
    GenericResponse<ObjDTO<PlaceClientBO>> findClient(@RequestParam String placeId, @RequestParam String clientId);


    @GetMapping(URL + "/config")
    GenericResponse<ObjDTO<PlaceConfigBO>> config(@RequestParam String placeId);


    @GetMapping(URL + "/queryAll")
    GenericResponse<ListDTO<PlaceProfileBO>> queryAll(@RequestParam Map<String, String> param);

    /**
     * 根据区域编码匹配场所
     * @param num - 从左到右匹配多少位
     * @param regionCodes
     * @return
     */
    @PostMapping(URL + "/findByRegionCodeLike")
	GenericResponse<ListDTO<PlaceProfileBO>> findByRegionCodeLike(@RequestParam int num, @RequestBody List<String> regionCodes);

    @GetMapping(URL + "/findByRegionCode")
    GenericResponse<ListDTO<PlaceProfileBO>> findByRegionCode(@RequestParam String regionCode);

    /**
     * 根据网吧名称模糊搜索网吧列表
     * @param displayName
     * @return
     */
    @GetMapping(URL + "/findByDisplayName")
    GenericResponse<ListDTO<PlaceProfileBO>> findByDisplayName(@RequestParam String displayName);

    @PostMapping(URL + "/save")
    GenericResponse<ObjDTO<PlaceProfileBO>> save(@RequestBody PlaceProfileBO placeProfileBO);

}
