package com.rzx.dim4.base.enums.billing;

import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/** 终端扫码方式
 * <AUTHOR> hwx
 * @since 2025/2/27 10:01
 */
public enum ClientAuthType {
    YISHANGWANG("100130", "易上网App"),
    JWELL("100024", "九威"),
    DABAZHANG("100047", "大巴掌"),
    ALIPAY("ALIRZXAPP", "支付宝"),
    WECHAT(Arrays.asList("MINIAPP","MPWECHAT","V8MPWECHAT","ZFDJMP"), "微信");

    private final Set<String> codes;
    private final String displayName;

    // 构造器重载
    ClientAuthType(String code, String displayName) {
        this.codes = Collections.singleton(code);
        this.displayName = displayName;
    }

    ClientAuthType(List<String> codes, String displayName) {
        this.codes = new HashSet<>(codes);
        this.displayName = displayName;
    }

    private static final Map<String, ClientAuthType> authTypeMap = new HashMap<>();
    static {
        for (ClientAuthType value : values()) {
            for (String code : value.codes) {
                authTypeMap.put(code,value);
            }
        }
    }

    public static List<ClientAuthType> parse(String input) {
        if (StringUtils.isEmpty(input)) {
            //默认微信
            return Collections.singletonList(WECHAT);
        }

        return Arrays.stream(input.split(","))
                .map(String::trim)
                .map(authTypeMap::get)
                .filter(Objects::nonNull)
                .distinct()
                .sorted(Comparator.comparingInt(ClientAuthType::ordinal)) //排序一下
                .collect(Collectors.toList());
    }
}
