package com.rzx.dim4.base.utils;

import org.springframework.util.StringUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024年07月01日 11:07
 */
public class WechatAgeConfigUtils {

    //根基配置校验身份证年龄是否符合要求
    public static Boolean verifyWechatAgeConfig(String wechatAgeConfig,String idNumber){
        if(idNumber.length() != 18){
            return true;
        }
        if (!StringUtils.isEmpty(wechatAgeConfig) && wechatAgeConfig.contains("-")) {
            // log.info("获取到场所上机年龄限制");
            String wechatAgeConfigMin = wechatAgeConfig.split("-")[0];
            String wechatAgeConfigMax = wechatAgeConfig.split("-")[1];

            int yearBirth = Integer.parseInt(idNumber.substring(6, 10));
            int monthBirth = Integer.parseInt(idNumber.substring(10, 12));
            int dayBirth = Integer.parseInt(idNumber.substring(12, 14));

            //获取当前年月日并计算年龄
            LocalDate localDate = LocalDate.now();
            int year = localDate.getYear();
            int month = localDate.getMonth().getValue();
            int dayOfMonth = localDate.getDayOfMonth();
            int age = year - yearBirth;
            if (month < monthBirth || (month == monthBirth && dayOfMonth < dayBirth)) {
                age--;
            }
            boolean is_adult = age >= Integer.parseInt(wechatAgeConfigMin) && age <= Integer.parseInt(wechatAgeConfigMax);
            // log.info("the age is {}, is_adult={}", age, is_adult);
            if (is_adult) {
                //符合要求
               return true;
            } else {
                //不符合
               return false;
            }
        } else {
            //log.info("场所没有设置上机年龄限制");
            return true;
        }
    }
}
