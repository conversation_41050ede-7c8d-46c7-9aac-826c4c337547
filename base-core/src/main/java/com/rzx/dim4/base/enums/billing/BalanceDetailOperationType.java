package com.rzx.dim4.base.enums.billing;

import lombok.Getter;

/**
 * BalanceDetail 表中OperationType
 */
@Getter
public enum BalanceDetailOperationType {

    START_ONLINE_DEDUCTION(0, "0", "上机扣费"),
    ONLINE_CHARGE_INTERNET_FEE(1, "1", "在线充值网费"),
    ONLINE_BUY_PACKAGE_TIME(2, "2", "在线购买包时"),
    INTERNET_FEE_BUY_PACKAGE_TIME(3, "3", "网费购买包时"),
    INTERNET_FEE_BUY_GOODS(4, "4", "网费购买商品"),
    CHANGE_MACHINE_DIFFERENCE(5, "5", "换机补差价"),
    ONLINE_LOGIN_DEDUCTION(6, "6", "上机登录扣除"),
    ORDER_SEAT_DEDUCTION(7, "7", "订座扣费"),
    ADDITIONAL_FEE_DEDUCTION(8, "8", "附加费扣费"),
    INTERNET_FEE_SEND_FREE(12, "12", "小程序网费赠送"),
    INTERNET_FEE_REFUND(13, "13", "退款"),
    CASHIER_ADD_AMOUNT(14, "14", "收银台加钱"),
    CASHIER_REDUCE_AMOUNT(15, "15", "收银台减钱"),
    MINI_APP_REDUCE_AMOUNT(16, "16", "小程序减钱"),
    PACKAGE_TIME_FROM_BILLING_CARD(17, "17", "网费支付包时"),
    ORDER_REFUND(18, "18", "订单退款"),
    ;

    /**
     * 操作类型，保存到数据库
     */
    private final int code;
    /**
     * 操作类型，方便作为查询条件放到Map中
     */
    private final String type;
    /**
     * 操作类型描述
     */
    private final String desc;

    BalanceDetailOperationType(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

}
