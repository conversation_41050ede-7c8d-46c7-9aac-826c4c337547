package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@HeadRowHeight(value = 20)//设置表头行高
@ColumnWidth(value = 15)//设置表头行宽
public class StatisticsGoodsTypeSaleExportVO implements Serializable {


    private static final long serialVersionUID = -4024032118997501837L;

    @ExcelProperty(value = "分类名称", index = 0)
    private String goodsTypeName; // 商品类型名称

    @ExcelProperty(value = "销售量",index = 1)
    private int countSale; // 销售数量(总量)

    @ExcelProperty(value = "销售额",index = 2)
    private float sumSaleTotal; // 销售金额(总额)

    //毛利
    @ExcelProperty(value = "毛利",index = 3)
    private float grossProfit;

    //毛利率
    @ExcelProperty(value = "毛利率",index = 4)
    private double grossProfitMargin;

    /**
     * 分类均价
     */
    @ExcelProperty(value = "分类均价",index = 5)
    private float unitPrice;

    /**
     * 实际均价
     */
    @ExcelProperty(value = "实际均价",index = 6)
    private float avgSalePrice;
}
