package com.rzx.dim4.base.service.feign.goods;


import com.rzx.dim4.base.bo.shop.StorageGoodsBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import com.rzx.dim4.base.service.feign.goods.query.GoodsParam;
import com.rzx.dim4.base.service.feign.goods.query.StorageGoodsQuery;
import com.rzx.dim4.base.service.feign.goods.query.StorageGoodsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仓库商品
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "StorageGoods", fallback = ShopServerServiceHystrix.class)
public interface StorageGoodsClient {

    String GOODS_STORE_URL = "shop/admin/storage";


    /**
     * 仓库商品分页
     *
     * @param goodsQuery
     * @param pageable
     * @return
     */
    @GetMapping(GOODS_STORE_URL+"/listStoragePage")
    GenericResponse<PagerDTO<StorageGoodsBO>> listStoragePage(@SpringQueryMap StorageGoodsQuery goodsQuery, Pageable pageable);


    @GetMapping(GOODS_STORE_URL+"/listStorage")
    GenericResponse<ListDTO<StorageGoodsBO>> listStorage(@SpringQueryMap StorageGoodsQuery goodsQuery);


    /**
     *  新增仓库，入库
     * @param requestTicket
     * @param storageGoodsQuery
     * @return
     */
    @PostMapping(GOODS_STORE_URL+"/save")
    GenericResponse<?> saveStorageGoods (@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody List<StorageGoodsQuery> storageGoodsQuery);



    /**
     *  获取库存
     * @param goodsQuery
     * @return
     */
    @GetMapping(GOODS_STORE_URL+"/getStorage")
    GenericResponse<ObjDTO<StorageGoodsBO>> getStorage(@SpringQueryMap StorageGoodsQuery goodsQuery);

    /**
     * 盘点库存
     * @param requestTicket
     * @param goodsVos
     * @return
     */
    @PostMapping(GOODS_STORE_URL+"/checksGoodsNumber")
    GenericResponse<?> checksGoodsNumber(@RequestHeader(value = "request_ticket") String requestTicket,@RequestBody List<GoodsParam> goodsVos);

    /**
     * 上下架
     * @param placeId
     * @param goodsId
     * @param hitShelves 1 上架-上架收银台，0下架-下架到仓库
     * @param goodsStocks
     * @return
     */
    @GetMapping(GOODS_STORE_URL+"/upDown")
    GenericResponse<?> upDown(@RequestParam String placeId, @RequestParam(name = "goodsId") String goodsId,
           @RequestParam(name = "hitShelves") Integer hitShelves,
                              @RequestParam(name = "goodsStocks") Integer goodsStocks,@RequestParam("creator") String creator ,@RequestParam("remark") String remark);

    /**
     * 退货
     * @param requestTicket
     * @param storageGoodsQuery
     * @return
     */
    @PostMapping(GOODS_STORE_URL+"/refundGoods")
    GenericResponse<?> refundGoods(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody List<StorageGoodsVo> storageGoodsQuery);


    /**
     * 新增商品信息
     * @param requestTicket
     * @param goodsInfoBO
     * @return
     */
    @PostMapping(GOODS_STORE_URL+"/saveGoodsInfo")
    GenericResponse<?> saveGoodsStorageInfo(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody StorageGoodsBO goodsInfoBO);

    /**
     * 删除库存
     * @param goodsId
     * @return
     */
    @GetMapping(GOODS_STORE_URL+"/deleteGoodsStorage")
    GenericResponse<?> deleteGoodsStorage(@RequestParam("placeId") String placeId,@RequestParam("goodsId") String goodsId);
}
