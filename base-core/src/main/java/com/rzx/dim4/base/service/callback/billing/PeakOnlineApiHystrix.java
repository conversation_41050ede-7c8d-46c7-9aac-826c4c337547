package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.PeakOnlineBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PeakOnlineApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2025/3/4
 **/
@Slf4j
@Service
public class PeakOnlineApiHystrix implements PeakOnlineApi {
    @Override
    public GenericResponse<ObjDTO<PeakOnlineBO>> queryByPlaceIdAndCountDay(String placeId, String countDay) {
        log.error("接口异常:::queryByPlaceIdAndCountDay(placeId:::{},countDay:::{})", placeId, countDay);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
