package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 买赠表
 * <AUTHOR>
 * @date 2024年12月05日
 */
@Getter
@Setter
@ToString
public class BuyGiftsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String buyGiftsId; // 买赠id，场所唯一，从160000开始递增
    private String buyGiftsName; // 活动名称

    private int needBuyNum; // 需购买数量。购买数量达到多少时符合满赠条件

    private int eventDate; // 活动日期：0指定日期，1每日，2每周，3每月（默认指定日期）

    private float startTime; // 活动开始时间。默认0

    private float endTime; // 活动结束时间。默认24

    private LocalDate startDate; // 指定时间段开始日期 1900.1.1

    private LocalDate endDate; // 指定时间段结束日期 2099.12.31

    private String weeks; // 每周售卖日期，默认 1,2,3,4,5,6,7

    private String days; // 每月售卖日期，默认 0,1,2,3,4,...31

    private int balancePay; // 允许钱包余额支付：0否（默认），1是

    private int storeMemberVisible; // 仅本店会员可见：0否，1是（默认否）

    private int autoDelivery; // 是否自动派送：0否，1是（默认否）

    private int eventGoodsType; // 活动商品类型：0指定商品，1全店商品（默认指定商品）

    private String eventGoods; // 活动商品：-1代表全店商品；选择”指定商品”的时候，直接算则商品和选择按类添加商品都是保存的商品id，如1，6，5，9

    private String cardTypeIds; // 参与卡类型

    private int hiddenCycle; // 隐藏周期：0每周，1每月，2指定日期（默认每周）

    private String hiddenWeeks; // 隐藏日期为每周，逗号分隔如，1，2，3

    private String hiddenDays; // 隐藏日期（每月和指定日期都是这给个格式），逗号分隔如，1，2，3，4，5

    private int hideEventEntrance; // 隐藏活动入口：0否，1是（默认否）

    private String participationFrequencyLimit; // 参与次数限制，根据限制周期来做限制，如限制周期为每周，那么就是每周限制买赠这个商品几次。

    private String eventDescribe; // 活动说明

    private int eventType; // 活动分类（从配置分类加载），默认0，网费充

    private int limitCycle; // 限制周期，0每日，1每周，2每月。默认每日

    private int status; // 启用状态，0启用，1关闭

    private LocalDate hideStartDate; // 隐藏周期为指定时间，开始日期 2012-01-01

    private LocalDate hideEndDate; // 隐藏周期为指定时间，结束日期 2099.12.31
    private String upCardTypeId; // 升级到指定计费卡类型 1000临时卡，1001 普通卡，1002 工作卡，三个固定值
    private String marketingPlatform; //营销平台 douyin、meituan
    private String platformId; //营销平台id

    //业务字段
    private List<String> buyGiftsIds; // 买赠信息Id
    private List<BuyGiftsGoodsBO> buyGiftsGoodsBOS; // 买赠信息的赠送商品列表
    private SourceType sourceType;//来源

    //--核销字段 ↓↓↓↓
    private String encryptedCode; //核销时需要使用的商品码
    private String skuId;         //抖音商品id
    private Long soldStartTime;   //抖音商品销售时间
    private Long soldEndTime;   //商品销售时间

}
