package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 用户的优惠券列表
 * <AUTHOR>
 * @date 2024年08月13日 17:02
 */
@Getter
@Setter
@ToString
public class ReceiveCouponDetailBO extends AbstractEntityBO {

    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID

    @ApiModelProperty(value = "优惠券ID")
    private String couponId; // 优惠券ID

    @ApiModelProperty(value = "优惠券名称")
    private String couponName; // 优惠券名称

    @ApiModelProperty(value = "优惠券类型ID")
    private int couponTypeId; // 优惠券类型ID

    @ApiModelProperty(value = "优惠券类型名称")
    private String couponTypeName; // 优惠券类型名称

    @ApiModelProperty(value = "规则ID")
    private String ruleId; // 规则ID

    @ApiModelProperty(value = "发放面额")
    private int amount; // 发放面额

    @ApiModelProperty(value = "优惠券开始时间")
    private LocalDateTime startTime; // 开始时间

    @ApiModelProperty(value = "优惠券结束时间")
    private LocalDateTime endTime; // 结束时间

    @ApiModelProperty(value = "使用时间")
    private LocalDateTime useTime; // 使用时间

    @ApiModelProperty(value = "生效周工作日（周一至周日，1代表星期一），用逗号分隔，如 1, 2, 3")
    private String effectiveWeekDays = ""; // 生效周工作日（周一至周日，1代表星期一），用逗号分隔，如 1, 2, 3

    @ApiModelProperty(value = "优惠券状态:0未使用，1已使用，2已失效")
    private int status;   //状态 0未使用，1已使用，2已失效

    @ApiModelProperty(value = "班次")
    private String shiftId; // 班次

    @ApiModelProperty(value = "发放人姓名")
    private String operatorName; // 发放人姓名

    @ApiModelProperty(value = "领取人身份证号码")
    private String idNumber; // 领取人身份证号码

    @ApiModelProperty(value = "领取人姓名")
    private String idName; // 领取人姓名

    @ApiModelProperty(value = "领取渠道")
    private SourceType couponSourceType; // 领取渠道

    @ApiModelProperty(value = "领取记录id")
    private String couponDetailId; // 领取记录id，使用的时候根据这个去绑定

    @ApiModelProperty(value = "优惠券code")
    private String couponCode;

    @ApiModelProperty(value = "包时时间")
    private double packageTime;

    @ApiModelProperty(value = "优惠券详情")
    private DiscountCouponBO discountCouponBO;

}

