package com.rzx.dim4.base.enums.marketing;

import lombok.Getter;

/**
 * 订单类型
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Getter
public enum OrderType {

    SHOP(1, "1", "商品订单"),
    GROUP_PURCHASE(2, "2", "团购订单"),
    TOP_UP(3, "3", "网费充值订单"),
    PACKAGE_TIME(4, "4", "包时订单"),
    PACKAGE_TIME_BILLING_CARD(6, "6", "网费支付的包时订单"),
    PACKAGE_TIME_SUPPLEMENT(7, "7", "补差价的包时订单"),
    GIFT_GIVE(8, "8", "礼客赠送"),
    CUSTOM(9, "9", "自定义收款"),
    DEPOSIT(10, "10", "设备租赁押金"),
    RENT(11, "11", "设备租赁租金");

    /**
     * 操作类型，保存到数据库
     */
    private final int code;

    /**
     * 操作类型，方便作为查询条件放到Map中
     */
    private final String type;

    /**
     * 操作类型描述
     */
    private final String desc;

    OrderType(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
}
