package com.rzx.dim4.base.service.feign.billing;


import com.rzx.dim4.base.bo.admin.JoinPlaceToChainBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingPlaceChainApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 *
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "placeChainApi", fallback = BillingPlaceChainApiHystrix.class)
public interface BillingPlaceChainApi {


    String URL = "/feign/billing/place/chain/main";

    @PostMapping(URL + "/placeJoinChain")
    GenericResponse<?> placeJoin<PERSON>hain(@RequestHeader(value = "request_ticket") String requestTicket,
                                      @RequestBody JoinPlaceToChainBO joinPlaceToChainBO);

}
