package com.rzx.dim4.base.service.callback;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.vo.PlaceProfileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @date Nov 19, 201910:33:21 AM
 */
@Slf4j
@Service
public class PlaceServerServiceHystrix implements PlaceServerService {

	private Gson gson = new Gson();

	@Override
	public GenericResponse<ObjDTO<PlaceProfileBO>> savePlaceProfile(String requestTicket,
			PlaceProfileBO placeProfileBO) {
		log.error("接口异常::: savePlaceProfile(requestTicket:::{}, placeProfileBO:::{})", requestTicket,
				gson.toJson(placeProfileBO));
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceProfileBO>> findByPlaceId(String placeId) {
		log.error("接口异常::: getByPlaceId(placeId:::{} )", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> findByPlaceIds(List<String> placeIds) {
		log.error("接口异常::: findPlaceProfiles(placeIds:::{} )", placeIds);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> findPlaceProfilesDistance(List<String> placeIds, String longitude, String latitude) {
		log.error("接口异常::: findPlaceProfilesDistance(placeIds:::{},longitude:::{},latitude:::{} )", placeIds,longitude,latitude);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	@Override
	public GenericResponse<ObjDTO<PlaceProfileBO>> findByIdentifier(String identifier) {
		log.error("接口异常::: getByIdentifier(identifier:::{} )", identifier);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlaceProfileVO>> fuzzy(int page, int size, String order, String[] orderColumns,
			Map<String, Object> queryMap) {
		log.error("接口异常::: fuzzy(queryMap:::{},page:{}, size:{},order:::{},orderColumns:::{})", queryMap, page, size,
				order, orderColumns);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> list() {
		log.error("接口异常::: list，获取网吧列表");
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceConfigBO>> savePlaceConfig(String requestTicket, PlaceConfigBO configBo) {
		log.error("接口异常::: save(requestTicket:::{}, configBo:::{})", requestTicket, configBo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceConfigBO>> findPlaceConfigByPlaceId(String placeId) {
		log.error("接口异常::: getPlaceConfig(placeId:::{} )", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse updateUpgradeV8Status(String placeId, int upgradeV8Status) {
		log.error("接口异常::: updateUpgradeV8Status(placeId:::{} ,upgradeV8Status:::{})", placeId,upgradeV8Status);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceConfigBO>> findPlaceConfigByPlaceIds(List<String> placeIds) {
		log.error("接口异常::: findPlaceConfigByPlaceIds(placeIds:::{})", placeIds);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

//	@Override
//	public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByLoginName(String loginName) {
//		log.error("接口异常::: findPlaceAccountByLoginName(loginName:::{} )", loginName);
//		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
//	}

	@Override
	public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByPlaceIdAndAccountId(String placeId,
			String accountId) {
		log.error("接口异常::: findPlaceAccountByPlaceIdAndLoginName(placeId:::{},accountId:::{} )", placeId, accountId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAccountBO>> findAccountByPlaceId(String placeId) {
		log.error("接口异常::: findPlaceAccountsByPlaceId(placeId:::{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAccountBO>> savePlaceAccount(String requestTicket, PlaceAccountBO accountBo) {
		log.error("接口异常::: savePlaceAccount(requestTicket:::{}, accountBo:::{})", requestTicket, accountBo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAccountBO>> batchSavePlaceAccount(String requestTicket, List<PlaceAccountBO> placeAccountBOS,String type) {
		log.error("接口异常::: savePlaceAccount(requestTicket:::{}, placeAccountBOS:::{})", requestTicket, placeAccountBOS);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByChainIdAndAccountId(String chainId, String accountId) {
		log.error("接口异常::: findPlaceAccountByChainIdAndAccountId(chainId:::{}, accountId:::{})", chainId, accountId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAccountBO>> storeManagerLogin(String loginName, String loginPass) {
		log.error("接口异常::: counterLogin(loginName:::{}, loginPass:::{})", loginName, loginPass);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAccountBO>> findCashierAccounts(String placeId, String flag, String cashierId) {
		log.error("接口异常::: findCashierAccounts(placeId:::{}, flag:::{}, cashierId:::{})", placeId, flag, cashierId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

//	@Override
//	public GenericResponse<ObjDTO<PlaceAccountBO>> submitCheckAccount(String username, String password, String placeId,
//			String cashierId) {
//		log.error("接口异常::: submitCheckAccount(username:::{}, password:::{}, placeId:::{}, cashierId:::{})", username,
//				password, placeId, cashierId);
//		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
//	}

	@Override
	public GenericResponse<PagerDTO<PlaceAgentBO>> findPageAgentAccounts(int size, int page, String[] orderColumns,
			String order, int type, String loginName, String level, String creater, String model) {
		log.error(
				"接口异常::: findPageAgentAccounts(size:::{}, page:::{}, orderColumns:::{}, order:::{}, type:::{}, loginName:::{}, level:::{}, creater:::{}, model:::{})",
				size, page, orderColumns, order, type, loginName, level, creater, model);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAgentBO>> savePlaceAgent(String requestTicket, PlaceAgentBO placeAgentBO) {
		log.error("接口异常::: savePlaceAgent(requestTicket:::{}, placeAgentBO:::{})", requestTicket, placeAgentBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAgentBO>> findByAccountId(String accountId) {
		log.error("接口异常::: findByAccountId(accountId:::{})", accountId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<LogAgentBalanceBO>> saveLogAgentBalance(String requestTicket,
			LogAgentBalanceBO logAgentBalanceBO) {
		log.error("接口异常::: saveLogAgentBalance(requestTicket:::{}, logAgentBalanceBO:::{})", requestTicket,
				logAgentBalanceBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceRenewalBO>> savePlaceAgentRenewal(String requestTicket, String loginPass,
			String createFlag, String mobile, PlaceRenewalBO placeRenewalBO) {
		log.error(
				"接口异常::: savePlaceAgentRenewal(requestTicket:::{}, placeRenewalBO:::{}, loginPass:::{}, createFlag:::{}, mobile:::{})",
				requestTicket, placeRenewalBO, loginPass, createFlag, mobile);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceRenewalBO>> queryRenewalByAccountId(String accountId) {
		log.error("接口异常::: queryRenewalByAccountId(accountId:::{})", accountId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<LogAgentBalanceBO>> queryPageLogAgentBalance(int size, int page,
			String[] orderColumns, String order, Map<String, Object> queryMap) {
		log.error(
				"接口异常::: queryPageLogAgentBalance(size:::{}, page:::{}, orderColumns:::{}, order:::{}, queryMap:::{})",
				size, page, orderColumns, order, queryMap);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAgentBO>> queryPlaceAgentLikeLoginName(String loginName) {
		log.error("接口异常::: queryPlaceAgentLikeLoginName(loginName:::{})", loginName);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<LogRenewalBO>> queryPageRenewalList(int size, int page, String[] orderColumns,
			String order, Map<String, Object> queryMap) {
		log.error("接口异常::: queryPageRenewalList(size:::{}, page:::{}, orderColumns:::{}, order:::{}, queryMap:::{})",
				size, page, orderColumns, order, queryMap);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAgentBO>> bindPlaceSubmit(String requestTicket, PlaceAgentBO placeAgentBO) {
		log.error("接口异常::: bindPlaceSubmit(requestTicket:::{}, placeAgentBO:::{})", requestTicket, placeAgentBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> clientRegistered(String requestTicket, String identifier,
			String hostName, String ipAddr, String macAddr, String clientVersion, String osVersion, String jfVersion,
			String spVersion, String jkVersion, String gxVersion) {
		log.error(
				"接口异常:::clientRegistered(identifier:{},hostName:{},ipAddr:{},macAddr:{},clientVersion:{},osVersion:{},jfVersion:{},spVersion:{},jkVersion:{},gxVersion:{})",
				identifier, hostName, ipAddr, macAddr, clientVersion, osVersion, jfVersion, spVersion, jkVersion,
				gxVersion);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> batchSaveClient(String requestTicket, List<PlaceClientBO> clientBOS) {
		log.error("接口异常:::batchSaveClient(requestTicket:{},clientBOS:{})", requestTicket, clientBOS);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> queryPlaceClient(String placeId, String clientId) {
		log.error("接口异常:::queryPlaceClient(placeId:{},clientId:{})", placeId, clientId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public Map<String, Integer> queryClientCount(String placeId) {
		log.error("接口异常:::queryClientCount(placeId:::{}))", placeId);
		return null;
	}

	@Override
	public GenericResponse<ListDTO<PlaceClientBO>> queryAllClientByClientIds(String placeId, List<String> clientIds) {
		log.error("接口异常:::queryAllClientByClientIds(placeId:::{},clientIds:::{}))", placeId, clientIds);
		return null;
	}

	@Override
	public GenericResponse<ListDTO<PlaceClientBO>> queryClientsByPlaceIdAndAreaId(String placeId, String areaId) {
		log.error("接口异常:::queryClientsByPlaceIdAndAreaId(placeId:::{},areaId:::{}))", placeId, areaId);
		return null;
	}

	@Override
	public GenericResponse<ListDTO<PlaceClientBO>> findByPlaceIdAndHostNameLike(String placeId, String hostName) {
		log.error("接口异常:::queryClientByPlaceIdAndHostName(placeId:::{},hostName:::{}))", placeId, hostName);
		return null;
	}
	// TODO 返回 null，调用方可能会报空指针，如需修改，返回 return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndHostName(String placeId, String hostName) {
		log.error("接口异常:::findByPlaceIdAndHostName(placeId:::{},hostName:::{}))", placeId, hostName);
		return null;
	}

	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndMacAddr(String requestTicket, String placeId, String macAddr) {
		log.error("接口异常:::findByPlaceIdAndMacAddr(requestTicket:{},placeId:{},macAddr:{})", requestTicket, placeId, macAddr);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> savePlaceArea(String requestTicket, PlaceAreaBO areaBo) {
		log.error("接口异常:::savePlaceArea(requestTicket:{},areaBo:{})", requestTicket, areaBo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAreaBO>> savePlaceAreaBo(String requestTicket, PlaceAreaBO areaBo) {
		log.error("接口异常:::savePlaceAreaBo(requestTicket:{},areaBo:{})", requestTicket, areaBo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAreaBO>> batchSavePlaceArea(String requestTicket, List<PlaceAreaBO> placeAreaBOS) {
		log.error("接口异常:::batchSavePlaceArea(requestTicket:{},placeAreaBOS:{})", requestTicket, placeAreaBOS);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceAreaBO>> findPlaceAreaByPlaceIdAndAreaId(String placeId, String areaId) {
		log.error("接口异常:::findPlaceArea(placeId:{},areaId:{})", placeId, areaId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAreaBO>> findByPlaceIdAndAreaIdIn(String placeId, List<String> areaIds) {
		log.error("接口异常:::findByPlaceIdAndAreaIdIn(placeId:{},areaIds:{})", placeId, areaIds);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceAreaBO>> findPlaceAreaByPlaceId(String placeId) {
		log.error("接口异常:::findPlaceAreaByPlaceId(placeId:{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deletePlaceArea(String areaId, String placeId) {
		log.error("接口异常:::deleteArea(placeId:{},areaId:{})", placeId,areaId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceShiftBO>> initShiftTable(String startTime, String endTime, String placeId) {
		log.error("接口异常:::initShiftTable(placeId:{},startTime:{},endTime:{})", placeId, startTime, endTime);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
	@Override
	public GenericResponse<ListDTO<PlaceShiftBO>> newInitShiftTable(String startTime, String endTime, String placeId) {
		log.error("接口异常:::newInitShiftTable(placeId:{},startTime:{},endTime:{})", placeId, startTime, endTime);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByShiftId(String placeId, String shiftId) {
		log.error("接口异常:::findWorkingShiftByShiftId(placeId:{},shiftId:{})", placeId, shiftId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByCashierId(String placeId, String cashierId) {
		log.error("接口异常:::findWorkingShiftByCashierId(placeId:{},cashierId:{})", placeId, cashierId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceShiftBO>> savePlaceShift(String requestTicket, PlaceShiftBO placeShiftBO) {
		log.error("接口异常:::savePlaceShift(requestTicket:{},placeShiftBO:{})", requestTicket, placeShiftBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingByAccountId(String placeId, String accountId) {
		log.error("接口异常:::findWorkingByAccountId(placeId:{},accountId:{})", placeId, accountId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlaceShiftBO>> queryPageShifts(Map<String, String> queryMap, int size, int page,
			String[] orderColumns, String order) {
		log.error("接口异常:::queryPageShifts(queryMap:{},size:{},page:{},orderColumns:{},order:{})", queryMap, size, page,
				orderColumns, order);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlaceProfileBO>> findPlaceProfilePage(String order, String[] orderColumns, Map<String, String> queryMap) {
		log.error("接口异常:::findPlaceProfilePage(order:{},orderColumns:{},queryMap:{})", order, orderColumns,queryMap);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> nearbyProfiles(String lng, String lat) {
		log.error("接口异常:::nearbyProfiles(lng:{},lat:{})", lng, lat);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceProfileBO>> queryPlaceByAuditId(String auditId) {
		log.error("接口异常:::queryPlaceByAuditId(auditId:{})", auditId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceClientBO>> queryAllClients(@RequestParam("placeId") String placeId) {
		log.error("接口异常:::queryAllClients(placeId:{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<PagerDTO<PlaceClientBO>> listClients(@RequestParam(name = "placeId") String placeId,
			@RequestParam(name = "areaId", defaultValue = "") String areaId,
			@RequestParam(name = "length", defaultValue = "10") int length,
			@RequestParam(name = "start", defaultValue = "0") int start,
			@RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
			@RequestParam(name = "order", defaultValue = "desc") String order) {
		log.error(
				"接口异常::: listClients(placeId:::{}, areaId:::{}, length:::{}, start:::{}, orderColumns:::{}, order:::{})",
				placeId, areaId, length, start, orderColumns, order);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceCashierBO>> saveCashier(String requestTicket, PlaceCashierBO bo) {
		log.error("接口异常::: saveCashier(requestTicket:::{}, PlaceCashierBO:::{})", requestTicket, bo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceCashierBO>> saveCashier(PlaceCashierBO bo) {
		log.error("接口异常::: saveCashier(PlaceCashierBO:::{})",  bo);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceCashierBO>> queryAllCashiers(String placeId) {
		log.error("接口异常::: queryAllCashiers(placeId:::{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> queryPlaceByType(int type) {
		log.error("接口异常:::queryPlaceByType(type:{})", type);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public List<String> queryPlaceIdsByRegionCode(String regionCode) {
		log.error("接口异常:::queryPlaceIdsByRegionCode(type:{})", regionCode);
		return null;
	}

	@Override
	public GenericResponse<SimpleDTO> savePlaceClient(String requestTicket, PlaceClientBO placeClientBO) {
		log.error("接口异常:::savePlaceClient(requestTicket:{},areaBo:{})", requestTicket, placeClientBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> savePlaceClientReturnBo(String requestTicket, PlaceClientBO placeClientBO) {
		log.error("接口异常:::savePlaceClientReturnBo(requestTicket:{},areaBo:{})", requestTicket, placeClientBO);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceClientBO>> findClientByPlaceIdAndClientId(String placeId, String areaId) {
		log.error("接口异常:::findClientByPlaceIdAndClientId(requestTicket:{},areaBo:{})", placeId, areaId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> deletePlaceClient(String placeId, String clientId) {
		log.error("接口异常:::deletePlaceClient(placeId:{},areaId:{})", placeId,clientId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceShiftBO>> queryWorkShifts(String placeId) {
		log.error("接口异常:::queryWorkShifts(placeId:{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<PlaceShiftBO>> queryDefaultWorkShift(String placeId) {
		log.error("接口异常:::queryDefaultWorkShift(placeId:{})", placeId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ListDTO<PlaceShiftBO>> queryPlaceShiftsByPlaceIdAndShiftIds(String placeId, List<String> shiftIds) {
		log.error("接口异常:::queryPlaceShiftsByPlaceIdAndShiftIds(placeId:{},shiftIds:{})", placeId,shiftIds);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
}
