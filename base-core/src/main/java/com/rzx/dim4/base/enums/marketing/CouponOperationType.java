package com.rzx.dim4.base.enums.marketing;

import com.rzx.dim4.base.enums.billing.SourceType;

/**
 * <AUTHOR>
 * @date 2024年08月16日 9:45
 */
public enum CouponOperationType {

    PRESENT_COUPON,//赠送优惠券
    USE_COUPON, //使用优惠券
    DEL_COUPON,//删除优惠券
    EXPIRED_COUPON,//优惠券失效
    CANCEL_COUPON;//作废优惠券

    public static boolean hasName(String name) {
        try {
            Enum.valueOf(CouponOperationType.class, name);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
