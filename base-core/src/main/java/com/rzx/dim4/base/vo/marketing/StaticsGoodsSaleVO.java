package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StaticsGoodsSaleVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 1901261361481870428L;
    /**
     * 累计销售数
     */
    private int totalCountSale;
    /**
     * 累计毛利
     */
    private int totalGrossProfit;

    /**
     * 平均毛利率
     */
    private double avgGrossProfitMargin;

    /**
     * 单品均价
     */
    private double avgSalePrice;
}
