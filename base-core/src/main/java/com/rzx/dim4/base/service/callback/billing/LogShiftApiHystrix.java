package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.LogShiftBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.LogShiftApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> hwx
 * @since 2025/2/26 15:54
 */
@Slf4j
@Component
public class LogShiftApiHystrix implements LogShiftApi {
    @Override
    public GenericResponse<?> save(String requestTicket, LogShiftBO logShiftBo) {
        log.error("接口异常:::save(logShiftBo:::{})", new Gson().toJson(logShiftBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
