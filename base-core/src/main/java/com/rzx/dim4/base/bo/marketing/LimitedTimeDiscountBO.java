package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限时折扣
 * <AUTHOR>
 * @date 2024年12月05日
 */
@Getter
@Setter
@ToString
public class LimitedTimeDiscountBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String limitedTimeDiscountId; // 限时折扣id，场所唯一，从150000开始递增

    private String name; // 活动名称

    private int discountMode; // 折扣模式：0指定折扣值，1指定折后金额

    private double discountRate; // 折扣率，当折扣模式为0的时候，该字段有值

    private int discountedAmount; // 折后金额。当折扣模式为1的时候，该字段有值

    private int eventDate; // 活动日期：0每日，1每周，2指定日期

    private float startTime; // 活动开始时间。默认0

    private float endTime; // 活动结束时间。默认24

    private int balancePay; // 允许钱包余额支付：0否（默认），1是

    private int balanceDiscount; // 钱包余额享受优惠折扣：0否（默认），1是

    private int onlineNoDiscount; // 在线支付不享受该活动优惠折扣：0否（默认），1是

    private String eventGoods; // 活动商品：-1代表全店商品；选择”指定商品”的时候，直接算则商品和选择按类添加商品都是保存的商品id，如1，6，5，9

    private String weeks; // 每周售卖日期，默认 1,2,3,4,5,6,7

    private LocalDate startDate; // 指定时间段开始日期 2012-01-01

    private LocalDate endDate; // 指定时间段结束日期 2099.12.31

    private int type; // 商品类型：0全店商品，1指定商品（默认全店商品）

    private int expired; // 商品类型：0未过期，1过期

    private List<GoodsBO> goodsBOS;  //限时折扣指定商品列表

}
