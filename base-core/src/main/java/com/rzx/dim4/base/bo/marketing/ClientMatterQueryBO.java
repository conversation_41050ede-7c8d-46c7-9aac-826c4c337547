package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.enums.marketing.ClientMatterType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事物处理查询bo
 * <AUTHOR> hwx
 * @since 2025/3/4 13:44
 */
@Data
public class ClientMatterQueryBO {

    private String placeId; // 场所ID

    /**
     * 事物类型
     */
    private List<ClientMatterType> matterTypes;

    /**
     * 可操作的状态,0表示不可操作，1表示可操作
     */
    private Integer operateStatus;

    private LocalDateTime startTime;
    private LocalDateTime endTime;

    private List<Integer> statusList;
}
