package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.user.customer.MiniAuthContextBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingPlaceProfileApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/23
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "BillingPlaceProfileApi", fallback = BillingPlaceProfileApiHystrix.class)
public interface BillingPlaceProfileApi {

    String URL = "/feign/billing/placeProfile";

    /**
     *
     * 获取认证相关的场所信息、场所是否开启认证收费、用户是否开卡/卡信息、本次认证是否收费
     *
     * @param token 扫码  token
     * @param idNumber 微信用户身份证号
     * @return
     */
    @GetMapping(URL + "/miniAuthContext")
    GenericResponse<ObjDTO<MiniAuthContextBO>> context(@RequestParam String token, @RequestParam String idNumber, @RequestParam String idName, @RequestParam(required = false) String scanState);

    /**
     *
     * 获取收银端二维码相关的场所信息、场所是否开启认证收费、收银台信息
     *
     * @param token 扫码  token
     * @return
     */
    @GetMapping(URL + "/miniCashierAuthContext")
    GenericResponse<ObjDTO<MiniAuthContextBO>> cashierContext(@RequestParam String token);
}
