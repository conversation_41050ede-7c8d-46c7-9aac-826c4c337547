package com.rzx.dim4.base.enums.billing;


/**
 * 操作类型
 *
 * <AUTHOR>
 * @date 2021年9月2日 上午11:56:49
 */
public enum OperationType {

    TOPUP, // 充值 --充网费
    PRESENT, // 赠送 --充赠送网费

    LOGOUT, // 登出(结账) --结账下机
    LOGOUT_ALL, // 登出(结账) --结账下机 全场结账
    CANCELLATION, // 注销(临时卡) --退卡(临时卡)
    CANCELLATION_ALL, // 注销(临时卡) --退卡(临时卡) 全场结账
    REVERSAL, // 冲正
    REFUND, // 退款,商品退款，取消包时退款，销卡退款,在线支付退款
    BUY, // 商品购买(卡扣)
    INTERNET_GIFT, // 赠送网费

    CREATE_CARD, // 开卡

    LOGIN, // 登录上机
    ACTIVATE_CARD, // 激活
    CANCEL_ACTIVATE, // 取消激活
    BEGIN_PACKAGE_TIME, // 包时开始
    END_PACKAGE_TIME, // 包时结束
    ACC, // 累计包时
    EXCHANGE, // 换机
    DELETE_CARD, // 删除会员卡

    THIRD_DEDUCTION, // 第三方扣款
    SURCHARGE, // 附加费
//    CONTINUE_PACKAGE_TIME, // 续包时
//    FUTURE_PACKAGE_TIME, // 预包时
//    CONVERT_PACKAGE_TIME, // 转包时
    CANCEL_PACKAGE_TIME, //  取消包时
    PACKAGE_TIME, // 包时
    CONVERT_BILLING_RULE, // 转换计费规则

    CREATE_BOOK_SEATS, // 创建订座
    END_BOOK_SEATS, // 结束订座
    UP_GRADE_USER_LEVEL,// 自动升级
    DOWN_GRADE_USER_LEVEL, // 自动降级
    REVERSAL_POINTS, // 积分调整

    ;


    public static boolean hasName(String name) {
        try {
            Enum.valueOf(OperationType.class, name);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
