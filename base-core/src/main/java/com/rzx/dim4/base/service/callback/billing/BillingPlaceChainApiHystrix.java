package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.admin.JoinPlaceToChainBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingPlaceChainApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/4/16
 **/
@Slf4j
@Service
public class BillingPlaceChainApiHystrix implements BillingPlaceChainApi {

    @Override
    public GenericResponse<?> placeJoinChain(String requestTicket, JoinPlaceToChainBO joinPlaceToChainBO) {
        log.error("placeJoinChain 接口异常, joinPlaceToChainBO={}",
                new Gson().to<PERSON>son(joinPlaceToChainBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
