package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.ShopConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.shop.ShopConfigApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/6/11
 **/

@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "shopConfigApi", fallback = ShopConfigApiHystrix.class)
public interface ShopConfigApi {

    String URL = "/feign/shop/config";

    /**
     * 查询商超配置列表，不分页
     * @return
     */
    @GetMapping(URL + "/openingList")
    GenericResponse<ListDTO<ShopConfigBO>> openingList(@RequestHeader(value = "request_ticket") String requestTicket);

}
