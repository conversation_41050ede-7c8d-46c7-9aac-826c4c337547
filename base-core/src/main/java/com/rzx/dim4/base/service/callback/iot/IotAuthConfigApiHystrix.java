package com.rzx.dim4.base.service.callback.iot;

import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.iot.IotAuthConfigApi;

import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/24
 **/
@Slf4j
@Service
public class IotAuthConfigApiHystrix implements IotAuthConfigApi {
    @Override
    public GenericResponse<ListDTO<IotAuthConfigBO>> queryAuthConfig(String placeId,  String faceclientType, String placeType) {
        log.error("接口异常，queryAuthConfig(placeId={}, faceclientType={}, placeType={} )", placeId, faceclientType, placeType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<IotAuthConfigBO>> needCharge(String placeId, String faceclientType, String placeType) {
        log.error("接口异常，needCharge(placeId:::{}, faceclientType={}, placeType={})", placeId, faceclientType, placeType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogAuthFeeBO>> currentNeedCharge(String placeId, String idNumber) {
        log.error("接口异常，currentNeedCharge(placeId={}，idNumber={})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<LogAuthFeeBO>> saveLogAfterSuccess(SaveLogAfterSuccessBO saveLogAfterSuccessBO) {
        log.error("接口异常，saveLogAfterSuccess");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 创建小程序人脸认证套餐购买订单
     *
     * @param requestTicket
     * @param placeId
     * @param idNumber
     * @param amount
     * @param openId
     * @return
     */
    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createAuthPayOrder(String requestTicket, String placeId, String idNumber, String openId
            , long authChargeRuleId, int amount, String appId,String faceId) {
        log.error("接口异常, createAuthPayOrder(requestTicket={}, placeId={}, idNumber={}, amount={}, openId={}, appId={}, faceId={})",
                requestTicket,  placeId,  idNumber,  amount,  openId ,appId,faceId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> mpAuthPayOrder(String requestTicket, String placeId, String idNumber,
                                                                   long authChargeRuleId, int amount,String returnUrl, String faceId,
                                                                   String openId) {
        log.error("接口异常, mpAuthPayOrder(requestTicket={}, placeId={}, idNumber={}, amount={}, returnUrl={},faceId={},openId={})",
                requestTicket,  placeId,  idNumber,  amount ,returnUrl ,faceId,openId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 查询小程序人脸认证套餐支付结果
     *
     * @param requestTicket
     * @param orderId
     * @return
     */
    @Override
    public GenericResponse<ObjDTO<LogAuthFeeBO>> queryAuthPayOrder(String requestTicket, String orderId) {
        log.error("接口异常, queryAuthPayOrder(requestTicket={}, orderId={})",
                requestTicket,  orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
