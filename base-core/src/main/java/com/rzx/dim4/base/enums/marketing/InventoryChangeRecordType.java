package com.rzx.dim4.base.enums.marketing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品库存操作变动方式:0-入库，1-上下架，2-盘点，3-调出，4-调入，5-退货，6-报损，7-销售，8-派奖
 * <AUTHOR> hwx
 * @since 2025/2/25 15:54
 */
@Getter
@AllArgsConstructor
public enum InventoryChangeRecordType {
    IN(0,"入库"),
    GOODS_MOVE(1,"上下架"),
    STOCK_TAKING(2,"盘点"),
    ADJUST_OUT(3,"调出"),
    ADJUST_IN(4,"调入"),
    RETURN(5,"退货"),
    LOSS(6,"报损"),
    SALE(7,"销售"),
    AWARD(8,"派奖");

    private final int value;
    private final String desc;
}
