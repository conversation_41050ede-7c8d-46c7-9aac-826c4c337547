package com.rzx.dim4.base.utils;

import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName IdAuthRequest
 * @Description 获取 token
 * <AUTHOR>
 * @Date 2021/4/8 上午10:12
 * @Version 1.0
 */
public class IdAuthRequest {
	private String clientId;
	private String clientSecret;
	private String userId;

	public IdAuthRequest(String clientId, String clientSecret, String userId) {
		this.clientId = clientId;
		this.clientSecret = clientSecret;
		this.userId = userId;
	}

	private static final int TIMEOUT = 10000;

	public String getToken(String dim4Host) throws Exception {

		if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret) || StringUtils.isEmpty(userId)) {
			return null;
		}

		// 初始化Client
		CloseableHttpClient httpClient = HttpClients.createDefault();
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(TIMEOUT)
				.setConnectionRequestTimeout(TIMEOUT).setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();

		// 设置参数
		List<BasicNameValuePair> list = new ArrayList<BasicNameValuePair>();
		list.add(new BasicNameValuePair("clientId", clientId)); // 请求参数
		list.add(new BasicNameValuePair("clientSecret", clientSecret)); // 请求参数

		// 设置entity
		UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, "UTF-8");
		String getTokenUrl = "http://" + dim4Host + "/idauth-server/idauth/token/getToken";
		// 接口地址和请求设置
		HttpPost httpPost = new HttpPost(getTokenUrl);
		httpPost.setConfig(requestConfig);
		httpPost.setEntity(entity);

		// 发送请求并接受结果
		HttpResponse httpResponse = httpClient.execute(httpPost);

		// 对结果的处理
		if (httpResponse != null) {
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				String result = EntityUtils.toString(httpResponse.getEntity());
				@SuppressWarnings("deprecation")
				JsonObject jsonObject = new JsonParser().parse(result).getAsJsonObject();
				return jsonObject.get("data").getAsJsonObject().get("obj").getAsJsonObject().get("token").toString()
						.replaceAll("\"", "");
			}
		}

		return null;
	}

	/**
	 * 实名认证3要素接口调用DEMO
	 *
	 * @param facePhotoBase64     文件
	 * @param idNumber 身份证号号码
	 * @param name     姓名
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("deprecation")
	public String threeElementsRequest(String facePhotoBase64, String token, String idNumber, String name, String dim4Host)
			throws Exception {

		if (StringUtils.isEmpty(facePhotoBase64) || StringUtils.isEmpty(token) || StringUtils.isEmpty(idNumber)
				|| StringUtils.isEmpty(name)) {
			return null;
		}

		// Client初始化
		CloseableHttpClient httpClient = HttpClients.createDefault();
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(TIMEOUT)
				.setConnectionRequestTimeout(TIMEOUT).setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();

		// 设置签名
		StringBuffer toSign = new StringBuffer();
		toSign.append("facePhotoBase64=").append(facePhotoBase64).append("&");
		toSign.append("idNumber=").append(idNumber).append("&");
		toSign.append("name=").append(name).append("&");
		toSign.append(userId);
		HashFunction hashFunction = Hashing.md5();
		HashCode signHash = hashFunction.hashString(toSign.toString(), StandardCharsets.UTF_8);

		// 创建请求表单
		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		builder.setCharset(Charset.forName(HTTP.UTF_8));
		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

		// 请求参数中文乱码的处理
		ContentType contentType = ContentType.create(HTTP.PLAIN_TEXT_TYPE, HTTP.UTF_8);
		StringBody nameBody = new StringBody(name, contentType);
		builder.addPart("name", nameBody); // 姓名
		builder.addTextBody("idNumber", idNumber); // 身份证号码
		builder.addTextBody("facePhotoBase64", facePhotoBase64); // 图片Base64
		builder.addTextBody("sign", signHash.toString()); // 签名
		String threeElements = "http://" +  dim4Host + "/idauth-server/idauth/auth/3e";
		// 配置接口地址和请求设置
		HttpPost httpPost = new HttpPost(threeElements);
		httpPost.setConfig(requestConfig);

		// 设置Token
		httpPost.setHeader("Authorization", "Bearer " + token);

		// 填充表单
		httpPost.setEntity(builder.build());

		// 发送请求并接受结果
		HttpResponse httpResponse = httpClient.execute(httpPost);

		// 对结果的处理
		String strResult = "";
		if (httpResponse != null) {
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				strResult = EntityUtils.toString(httpResponse.getEntity());
			}
		}
		return strResult;
	}

	/**
	 * 实名认证2要素接口
	 * @param token
	 * @param idNumber
	 * @param name
	 * @return
	 * @throws Exception
	 */
    public String twoElementsRequest(String token, String idNumber, String name, String dim4Host)
			throws Exception {

		if (StringUtils.isEmpty(token) || StringUtils.isEmpty(idNumber)
				|| StringUtils.isEmpty(name)) {
			return null;
		}

		// Client初始化
		CloseableHttpClient httpClient = HttpClients.createDefault();
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(TIMEOUT)
				.setConnectionRequestTimeout(TIMEOUT).setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();

		// 设置签名
		StringBuffer toSign = new StringBuffer();
		toSign.append("idNumber=").append(idNumber).append("&");
		toSign.append("name=").append(name).append("&");
		toSign.append(userId);
		HashFunction hashFunction = Hashing.md5();
		HashCode signHash = hashFunction.hashString(toSign.toString(), StandardCharsets.UTF_8);

		// 创建请求表单
		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		builder.setCharset(Charset.forName(HTTP.UTF_8));
		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

		// 请求参数中文乱码的处理
		ContentType contentType = ContentType.create(HTTP.PLAIN_TEXT_TYPE, HTTP.UTF_8);
		StringBody nameBody = new StringBody(name, contentType);
		builder.addPart("name", nameBody); // 姓名
		builder.addTextBody("idNumber", idNumber); // 身份证号码
		builder.addTextBody("sign", signHash.toString()); // 签名
		String twoElements  = "http://" + dim4Host + "/idauth-server/idauth/auth/2e";

		// 配置接口地址和请求设置
		HttpPost httpPost = new HttpPost(twoElements);
		httpPost.setConfig(requestConfig);

		// 设置Token
		httpPost.setHeader("Authorization", "Bearer " + token);

		// 填充表单
		httpPost.setEntity(builder.build());

		// 发送请求并接受结果
		HttpResponse httpResponse = httpClient.execute(httpPost);

		// 对结果的处理
		String strResult = "";
		if (httpResponse != null) {
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				strResult = EntityUtils.toString(httpResponse.getEntity());
			}
		}
		return strResult;
	}

}
