package com.rzx.dim4.base.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

public class DateTimeUtils {


	final static String yyyy_mm_dd_HH_MM_ss = "yyyy-MM-dd HH:mm:ss";

	final static String yyyy_mm_dd = "yyyy-MM-dd";

	final static String yyyy_mm="yyyy-MM";

	/**
	 * UTC格式转LocalDateTime 支持带时区格式和不带时区格式 <br/>
	 * "2021-06-08T11:57:40.671" 和 "2021-06-08T11:57:40.671+08:00"
	 *
	 * @param utc
	 * @return LocalDateTime
	 */
	public static LocalDateTime convertLocalDateTime(String utc) {
		if (StringUtils.isEmpty(utc)) {
			return null;
		}
		if (utc.indexOf("+") > 0) {
			utc = utc.substring(0, utc.indexOf("+")) + "Z";
		}
		Instant instant = Instant.parse(utc);
		return LocalDateTime.ofInstant(instant, TimeZone.getTimeZone("Asia/Shangha").toZoneId());
		// 转成系统默认时区的时间
		// return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
	}

	/**
	 * 将分钟转为中文"xx小时xx分钟"
	 *
	 * @param minutes
	 * @return
	 */
	public static String minutesTransformToStringDes(int minutes) {
		return (minutes / 60) + "小时" + (minutes % 60) + "分钟";
	}

	/**
	 * 时间差转为中文"xx天xx小时xx分钟xx秒"
	 *
	 * @param duration
	 * @return
	 */
	public static String durationTransformToStringDes(Duration duration) {
		long dayOfDuration = duration.toDays();
		long hourOfDuration = duration.toHours() % 24;
		long minOfDuration = duration.toMinutes() % 60;
		//long secondOfDuration = duration.toMillis() / 1000 % 60;

		String stringSec = "";
		if (dayOfDuration != 0) {
			stringSec = dayOfDuration + "天";
		}
		if (hourOfDuration != 0) {
			stringSec = stringSec + hourOfDuration + "小时";
		}
		if (minOfDuration != 0) {
			stringSec = stringSec + minOfDuration + "分钟";
		}
//        if (secondOfDuration != 0) {
//            stringSec = stringSec + secondOfDuration + "秒";
//        }
		return stringSec;
	}


	/**
	 * 参数时间与当前时间获取间隔时长
	 *
	 * @param loginTime
	 * @return
	 */
	public static Long getOnlineTimeNumber(LocalDateTime loginTime) {
		if (loginTime == null) {
			return null;
		}
		Duration duration = Duration.between(loginTime, LocalDateTime.now());
		Long onlineTime = duration.toMinutes();
		return onlineTime;
	}

    /**
     * 处理时长
     * @param onlineTimes 分钟
     * @return
     */
    public static String getOnlineTimes (Double onlineTimes) {
        if (onlineTimes == null) {
            return "0小时0分";
        }
        float hours = new BigDecimal( onlineTimes / 60).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
        double hour = Math.floor(hours);
		System.out.println(hour);
        double minus = onlineTimes - hour*60;
		System.out.println(minus);
        return new Double(hour).intValue() + "小时" + Math.ceil(minus) + "分钟";
    }
	/**
	 * LocalDateTime转String
	 *
	 * @param localDateTime
	 * @param format
	 * @return
	 */
	public static String format(LocalDateTime localDateTime, String format) {
		DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
		return df.format(localDateTime);
	}

	/**
	 * String 转localDateTime
	 *
	 * @param dateStr
	 * @return
	 */
	public static LocalDateTime formatStrToDate(String dateStr,String format) {
		if(StringUtils.isEmpty(dateStr)){
			return null;
		}
		DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
		return LocalDateTime.parse(dateStr,df);
	}

	/**
	 * @param localDateTime
	 * @return
	 */
	public static String getTimeFormat(LocalDateTime localDateTime) {
		if (localDateTime != null) {
			return format(localDateTime, yyyy_mm_dd_HH_MM_ss);
		}
		return "";
	}

	/**
	 * @param localDateTime
	 * @return
	 */
	public static String getTimeFormatDateToString(LocalDateTime localDateTime) {
		if (localDateTime != null) {
			return format(localDateTime, yyyy_mm_dd);
		}
		return "";
	}
	/**
	 * 本周开始时间
	 *
	 * @return
	 */
	public static LocalDateTime weekStartTime() {
		LocalDate now = LocalDate.now();
		return LocalDateTime.of(now.minusDays(now.getDayOfWeek().getValue() - 1), LocalTime.MIN);
	}

	/**
	 * 本周结束时间
	 *
	 * @return
	 */
	public static LocalDateTime weekEndTime() {
		LocalDate now = LocalDate.now();
		return LocalDateTime.of(now.plusDays(7 - now.getDayOfWeek().getValue()), LocalTime.MAX);
	}

	/**
	 * 本月开始时间
	 *
	 * @return
	 */
	public static LocalDateTime monthStartTime() {
		return LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
	}

	/**
	 * 传入时间的当月结束时间
	 *
	 * @return
	 */
	public static LocalDateTime monthEndTime (LocalDateTime dateTime) {
		return LocalDateTime.of(dateTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
	}

	/**
	 * 本季度开始时间
	 *
	 * @return
	 */
	public static LocalDateTime quarterStartTime() {
		LocalDate now = LocalDate.now();
		Month month = Month.of(now.getMonth().firstMonthOfQuarter().getValue());
		return LocalDateTime.of(LocalDate.of(now.getYear(), month, 1), LocalTime.MIN);
	}

	/**
	 * 本季度结束时间
	 *
	 * @return
	 */
	public static LocalDateTime quarterEndTime() {
		LocalDate now = LocalDate.now();
		Month month = Month.of(now.getMonth().firstMonthOfQuarter().getValue()).plus(2L);
		return LocalDateTime.of(LocalDate.of(now.getYear(), month, month.length(now.isLeapYear())), LocalTime.MAX);
	}

	/**
	 * 获取两个时间中间的所有日期
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static List<LocalDateTime> getDatesBetween(LocalDateTime startDate, LocalDateTime endDate) {
		List<LocalDateTime> dates = new ArrayList<>();
		long numOfDaysBetween = ChronoUnit.DAYS.between(startDate, endDate);
		for (int i = 0; i <= numOfDaysBetween; i++) {
			LocalDateTime date = startDate.plusDays(i);
			dates.add(date);
		}
		return dates;
	}

	/**
	 * 获取 num 个月前的
	 * @param num
	 * @return [startTimeStr，endTimeStr]
	 */
	public static List<String> getPastMonthByNum(int num){
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();
		// 获取上num 个月的年份和月份
		YearMonth previousMonth = YearMonth.from(currentDate).minusMonths(num);
		int year = previousMonth.getYear();
		Month month = previousMonth.getMonth();
		// 构造上num 个月的第一天
		LocalDateTime startOfPreviousMonth = LocalDateTime.of(year, month, 1,0,0,0);

		// 构造上个月的最后一天
		LocalDateTime endOfCurrentMonth = LocalDateTime.of(currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);

		// 格式化输出
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String formattedStartTime = startOfPreviousMonth.format(formatter);
		String formattedEndTime = endOfCurrentMonth.format(formatter);
//		System.out.println(formattedStartTime);
//		System.out.println(formattedEndTime);
		return Arrays.asList(formattedStartTime,formattedEndTime);
	}

	/**
	 * 处理map里的dateTimetoString后转dateTime问题
	 * @param dateTimeStr
	 * @return
	 */
	public static LocalDateTime DateFormat (String dateTimeStr) {
		String result = "";
		DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		if (dateTimeStr.length() >= 19) {
			result = dateTimeStr.substring(0,19).replace("T"," ");
		} else if (dateTimeStr.length() >= 16) {
			result = dateTimeStr.substring(0,16).replace("T"," ") + ":00";
		} else if (dateTimeStr.length() >= 13){
			result = dateTimeStr.substring(0,13).replace("T"," ") + ":00:00";
		} else {
			result = dateTimeStr.substring(0,11).replace("T"," ") + "00:00:00";
		}
        return LocalDateTime.parse(result, fmt);
	}

	/**
	 * 根据入参的 数字 转换为当日的时间点
	 * @param floatTime
	 * @return
	 */
	public static LocalDateTime floatToDateTime(float floatTime){
		LocalDateTime now = LocalDateTime.now();
		double startHours = Math.floor(floatTime);

		// 转换为分钟
		double startMinute = floatTime - startHours;
		startMinute = new BigDecimal(startMinute * 60).setScale(2, RoundingMode.HALF_UP).floatValue();

		// 取整
		startMinute = Math.round(startMinute);

		// 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
		LocalDateTime middleTime = now.minusHours(now.getHour()).minusMinutes(now.getMinute()).minusSeconds(now.getSecond()).minusNanos(now.getNano());

		LocalDateTime startTime = middleTime.plusHours(Double.valueOf(startHours).longValue()).plusMinutes(Double.valueOf(startMinute).longValue());

		return startTime;
	}

	public static String formatYYMMDD(LocalDateTime localDateTime){
		if(ObjectUtils.isEmpty(localDateTime)){
			return "";
		}
		return format(localDateTime, "yyMMdd");
	}

	public static String formatChinese(LocalDateTime localDateTime){
		if(ObjectUtils.isEmpty(localDateTime)){
			return "";
		}
		return format(localDateTime, "yyyy年MM月dd日");
	}


	public static String formatYYYYMM (LocalDateTime localDateTime) {
		return format(localDateTime, yyyy_mm);
	}

	/**
	 * Date转LocalDateTime
	 * @param date
	 * @return
	 */
	public static LocalDateTime convertToLocalDateTime(Date date) {
		return Instant.ofEpochMilli(date.getTime())
				.atZone(ZoneId.systemDefault())
				.toLocalDateTime();
	}

	/**
	 * 判断当前时间是否在某个时间段内
	 * @param current
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isInDateTimeRange(LocalTime  current, LocalTime start, LocalTime end) {
		return (current.isAfter(start) || current.equals(start))
				&& (current.isBefore(end) || current.equals(end));
	}


	public static float timeFormatFloat(Time time){
		int hour = time.getHours();    // 小时
		int minute = time.getMinutes();  // 分钟
		int second = time.getSeconds();  // 秒
		return hour + (minute / 60.0f) + (second / 3600.0f);
	}

	public static Time floatFormatTime(float hoursFloat){
		// 2. 小时小数形式（float） → 时间字符串
		int hours = (int) hoursFloat;
		float remaining = hoursFloat - hours;
		int minutes = (int) (remaining * 60);
		remaining = remaining * 60 - minutes;
		int seconds = Math.round(remaining * 60);

		// 处理可能的溢出（如seconds=60）
		if (seconds >= 60) {
			seconds -= 60;
			minutes++;
		}
		if (minutes >= 60) {
			minutes -= 60;
			hours++;
		}

		LocalTime reconstructedTime = LocalTime.of(hours, minutes, seconds);
		return Time.valueOf(reconstructedTime.format(DateTimeFormatter.ISO_LOCAL_TIME));
	}

	public static float floatTimeClearSecond(float floatTime){
		Time time = floatFormatTime(floatTime);
		int hour = time.getHours();    // 小时
		int minute = time.getMinutes();  // 分钟
		return hour + (minute / 60.0f);
	}


	public static LocalDateTime parseYYYYMMddHHmmss(String dateStr) {
		if(StringUtils.isEmpty(dateStr)){
			return null;
		}
		DateTimeFormatter df = DateTimeFormatter.ofPattern(yyyy_mm_dd_HH_MM_ss);
		return LocalDateTime.parse(dateStr,df);
	}

	public static String formatYYYYMMddHHmmss (LocalDateTime localDateTime) {
		return format(localDateTime, yyyy_mm_dd_HH_MM_ss);
	}

	public static String formatYYYYMMddHHmmssSSS (LocalDateTime localDateTime) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
		return localDateTime.format(formatter);
	}

}
