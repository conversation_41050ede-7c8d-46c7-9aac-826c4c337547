package com.rzx.dim4.base.utils;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.OrderUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * bean工具类
 */
@Component
public class SpringUtils implements ApplicationContextAware {
    private static final Logger log = LoggerFactory.getLogger(SpringUtils.class);

    private static ApplicationContext applicationContext;

    public static Object getBean(String beanName){
        return applicationContext.getBean(beanName);
    }

    public static <T> T getBean(String beanName, Class<T> beanClass){
        return applicationContext.getBean(beanName, beanClass);
    }

    public static <T> T getBean(Class<T> beanClass){
        return applicationContext.getBean(beanClass);
    }

    public static <T> Map<String, T> getBeanMap(Class<T> beanClass){
        return applicationContext.getBeansOfType(beanClass);
    }

    public static <T> List<T> getBeans(Class<T> beanClass){
        Map<String, T> beansOfType = applicationContext.getBeansOfType(beanClass);
        if (CollectionUtils.isEmpty(beansOfType)){
            return Collections.emptyList();
        }
        ArrayList<T> beanList = new ArrayList<>(beansOfType.values());
        beanList.sort(Comparator.comparingInt(o -> OrderUtils.getOrder(o.getClass())));
        return beanList;
    }

    public static void publishEvent(Object event){
        if (event == null) {
            return;
        }
        try {
            applicationContext.publishEvent(event);
        } catch (Exception e) {
            log.error("publishEvent error,event:{}", new Gson().toJson(event), e);
        }
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Environment getEnvironment() {
        return applicationContext.getEnvironment();
    }

    public static String getApplicationName() {
        return applicationContext.getApplicationName();
    }



    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }
}