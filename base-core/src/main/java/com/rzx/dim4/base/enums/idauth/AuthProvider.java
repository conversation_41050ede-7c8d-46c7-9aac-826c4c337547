package com.rzx.dim4.base.enums.idauth;

/**
 * <AUTHOR>
 * @date Dec 19, 201911:40:25 AM
 */
public enum AuthProvider {

    TOPFREEWEB_2E("任网游(本地)"), // 本地实名库
    LINKFACE_2E("今始科技(二要素)"), // LINKFACE
    LINKFACE_3E("今始科技(三要素)"), // LINKFACE
    SENSETIME_3E("商汤科技(三要素)"), // 商汤
    SENSETIME_1TO1("商汤科技(一比一)"), // 商汤
    SENSETIME_L("商汤科技(活体认证)"), // 商汤
    SENSETIME_LC("商汤科技(活体检测)"), // 商汤
    PIXTALKS_3E("图语科技(三要素)"), // 图语
    PIXTALKS_1TO1("图语科技(一比一)"), // 图语
    PIXTALKS_L("图语科技(活体认证)"), // 图语
    JIXINTECH_3E("吉信科技(三要素)"), // 吉信
    JIXINTECH_COM("吉信科技(企业认证)"), // 吉信
    DABBY_3E("大白科技(3要素)"), // 大白
    TCREDIT_2E("天创信用(2要素)"), // 天创
    TCREDIT_3E("天创信用(3要素)"), // 天创
    HIGHMAX_3E("海脉云(3要素)"), // 海脉云

    HIGHMAX_2E("海脉云(2要素)"), // 海脉云

    CTID_3E("CTID(3要素)"), // CTID

    // CTID_2E("CTID(2要素)"), // CTID
    ;

    private final String name;

    private AuthProvider(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

}
