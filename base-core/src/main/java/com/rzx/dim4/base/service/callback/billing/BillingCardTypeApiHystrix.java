package com.rzx.dim4.base.service.callback.billing;

import java.util.List;

import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingCardTypeApi;

import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/9
 **/
@Slf4j
@Service
public class BillingCardTypeApiHystrix implements BillingCardTypeApi {
    @Override
    public GenericResponse<ObjDTO<BillingCardTypeBO>> create(String requestTicket, BillingCardTypeBO bo) {
        log.error("createBillingCardType 接口异常，requestTicket:{},bo:{}", requestTicket, bo);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> delete(String requestTicket, Long id) {
        log.error("delete 接口异常，requestTicket:{},id:{}", requestTicket, id);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardTypeBO>> findNeedUnionByPlaceId(String requestTicket, String placeId) {
        log.error("findByPlaceId 接口异常，requestTicket:{},placeId:{}", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardTypeBO>> list(String requestTicket, String placeId) {
        log.error("list 接口异常，requestTicket:{},placeId:{}", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardTypeBO>> listRemoveTempory(String placeId) {
        log.error("listRemoveTempory 接口异常,placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 场所退出连锁时，更新计费卡类型信息
     *
     * @param requestTicket feign请求头
     * @param placeId       场所id
     * @return 更新结果
     */
    @Override
    public GenericResponse<?> exitChain(String requestTicket, String placeId) {
        log.error("exitChain 接口异常，requestTicket:{},placeId:{}", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> updateAfterChain(String requestTicket, List<BillingCardTypeBO> billingCardTypeBOList) {
        log.error("update 接口异常，requestTicket:{},billingCardTypeBOList:{}", requestTicket, billingCardTypeBOList);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardTypeBO>> findByPlaceIdAndChainCardTypeId(String requestTicket, String placeId, String chainCardTypeId) {
        log.error("findByPlaceIdAndChainCardTypeId 接口异常，requestTicket:{},placeId:{},chainCardTypeId:{}", requestTicket, placeId, chainCardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

	@Override
	public GenericResponse<ListDTO<BillingCardTypeBO>> findByPlaceId4iot(String requestTicket, String placeId) {
        log.error("findByPlaceId4iot 接口异常，requestTicket:{},placeId:{}", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

    @Override
    public GenericResponse<SimpleDTO> tempCardUpgrade(String requestTicket, String placeId, String cardId, String upgradeCardTypeId) {
        log.error("tempCardUpgrade 接口异常，requestTicket:{},placeId:{},cardId:{},upgradeCardTypeId:{}", requestTicket, placeId, cardId, upgradeCardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
