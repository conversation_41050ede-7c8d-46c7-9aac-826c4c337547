package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "cloudfee_configs")
@Getter
@Setter
public class RateConfigXml {

    /**
     * 卡类型集合
     */
    @XmlElementWrapper(name="member_card_configs")
    @XmlElement(name = "member_card_config")
    private List<BillingCardTypeXml> billingCardTypeXml;
    /**
     * 工作人员集合
     */
    @XmlElementWrapper(name = "user_configs")
    @XmlElement(name = "user_config")
    private List<PlaceAccountXml> placeAccountXml;
    /**
     * 营业区域集合
     */
    @XmlElementWrapper(name = "areas")
    @XmlElement(name = "area")
    private List<PlaceAreaXml> placeAreaXml;
    /**
     * 终端集合
     */
    @XmlElementWrapper(name = "computers")
    @XmlElement(name = "computer")
    private List<PlaceClientXml> placeClientXml;

    /**
     * 会员日集合
     */
    @XmlElementWrapper(name = "memberdays")
    @XmlElement(name = "memberday")
    private List<BillingRuleMemberDayXml> billingRuleMemberDayXml;

    /**
     * 费率设置
     */
    @XmlElement(name = "rates")
    private AllRatesXml ratesXml;

}
