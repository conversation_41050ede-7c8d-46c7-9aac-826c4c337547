package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 商品图片信息实体
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "商品图片信息实体")
public class GoodsPictureBO extends AbstractEntityBO {

    @ApiModelProperty(value = "商品图片Md5")
    private String md5;

    @ApiModelProperty(value = "商品图片路径")
    private String url;
}
