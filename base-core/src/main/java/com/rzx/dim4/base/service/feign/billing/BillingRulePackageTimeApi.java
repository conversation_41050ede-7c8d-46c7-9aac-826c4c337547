/*
 * @(#)BillingTopupRuleApi.java 1.00 2024-1-26
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingRulePackageTimeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "BillingRulePackageTimeApi", fallback = BillingRulePackageTimeApiHystrix.class)
public interface BillingRulePackageTimeApi {
    String URL = "/feign/billing/packageTime";

    /**
     * 根据placeId和ruleId查询包时规则
     * @param placeId
     * @param ruleId
     * @return
     */
    @GetMapping(URL + "/findByPlaceIdAndRuleId")
	GenericResponse<ObjDTO<BillingRulePackageTimeBO>> findByPlaceIdAndRuleId(@RequestParam String placeId, @RequestParam String ruleId);
    /**
     * 根据placeId和ruleIds查询包时规则列表
     * @param placeId
     * @param ruleIds
     * @return
     */
    @PostMapping(URL + "/findByRuleIds")
	GenericResponse<ListDTO<BillingRulePackageTimeBO>> findByRuleIds(@RequestParam String placeId, @RequestParam List<String> ruleIds);

    /**
     * 余额包时
     * @param requestTicket
     * @param placeId
     * @param cardId
     * @param ruleId
     * @param sourceTypeStr
     * @return
     */
    @PostMapping(URL + "/balancePackageTime")
    GenericResponse<SimpleDTO> balancePackageTime(@RequestHeader(value = "request_ticket") String requestTicket,
                                                  @RequestParam String placeId,
                                                  @RequestParam String cardId,
                                                  @RequestParam String ruleId,
                                                  @RequestParam String sourceTypeStr);

    /**
     * 在线包时
     * @param requestTicket
     * @param placeId
     * @param cardId
     * @param ruleId
     * @param sourceTypeStr
     * @return
     */
    @PostMapping(URL + "/OnlinePackageTime")
    GenericResponse<ObjDTO<PaymentResultBO>> OnlinePackageTime(@RequestHeader(value = "request_ticket") String requestTicket,
                                                               @RequestParam String placeId,
                                                               @RequestParam String cardId,
                                                               @RequestParam String ruleId,
                                                               @RequestParam String sourceTypeStr,
                                                               @RequestParam(required = false) String payCode);

    /**
     * 优惠券核销包时
     * @param requestTicket
     * @param placeId
     * @param cardId
     * @param ruleId
     * @param sourceTypeStr
     * @param shiftId
     * @param couponName
     * @return
     */
    @PostMapping(URL + "/couponPackageTime")
    GenericResponse<?> couponPackageTime(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam String placeId,
                                         @RequestParam String cardId, @RequestParam String ruleId, @RequestParam String sourceTypeStr,
                                         @RequestParam String shiftId, @RequestParam String couponName,@RequestParam String couponTypeStr);

    @PostMapping(URL + "/updatePackageTimeStatus")
    GenericResponse<SimpleDTO> updatePackageTimeStatus(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestParam String placeId,
                                                       @RequestParam String ruleId,
                                                       @RequestParam String forbidden);
    //校验包时购买次数
    @GetMapping(URL + "/findPackageTimeBuyCount")
    GenericResponse<SimpleDTO> findPackageTimeBuyCount(@RequestParam String placeId,
                                                       @RequestParam String cardId,
                                                       @RequestParam String ruleId);

}
