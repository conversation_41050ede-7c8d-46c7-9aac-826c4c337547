package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 货架信息表
 * <AUTHOR>
 * @date 2024年12月04日 11:36
 */
@Getter
@Setter
public class GoodsStorageRackBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID

    private String storageRackId; // 货架id，场所唯一，从100000开始递增,主仓库为000000

    private String storageRackName; // 货架名称

    private String cashierId; // 收银台ID

    private String clientIds; // 客户端id列表，和货架收银台绑定后客户端购买商品推送到指定收银台
}
