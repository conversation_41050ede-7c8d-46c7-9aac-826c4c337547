package com.rzx.dim4.base.enums.shop;

import java.util.Arrays;
import java.util.Optional;

public enum StocksChangingType {

	SELLING("零售卖出"), //
	REFUND("零售退货退款"), // 收银台退货
	STOCK_PLUS("商品进货"), //
	STOCK_MINUS("商品退货"), //
	FIX_PLUS("修正(+)"), //
	FIX_MINUS("修正(-)"), //
	UP_GOODS("商品上架到收银台"),
	OUT_GOODS("商品从收银台下架"),
	CHECK("库存盘点"),
	SALE("零售卖出"),

	;

	private final String message;

	public static StocksChangingType getTypeName(String message){
		Optional<StocksChangingType> first = Arrays.stream(StocksChangingType.values()).filter(item -> message.equals(item.message)).findFirst();
		return first.orElse(null);
	}


	private StocksChangingType(String message) {
		this.message = message;
	}

	public String getMessage() {
		return message;
	}

}
