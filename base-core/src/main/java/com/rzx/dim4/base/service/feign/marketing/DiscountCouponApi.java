package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.DiscountCouponBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.DiscountCouponApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 优惠券相关接口
 * <AUTHOR>
 * @date 2024年08月12日 15:24
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "DiscountCouponApi", fallback = DiscountCouponApiHystrix.class)
public interface DiscountCouponApi {

    final String URL = "/feign/marketing/discountCoupon";

    @PostMapping(URL + "/save")
    GenericResponse<ObjDTO<DiscountCouponBO>> createOrUpdate(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody DiscountCouponBO discountCouponBO);

    /**
     * 查询优惠券详情
     * @param placeId 场所id，必填
     * @param couponId 优惠券id，必填
     * @return
     */
    @GetMapping(URL + "/queryByPlaceIdAndCouponId")
    GenericResponse<ObjDTO<DiscountCouponBO>> queryByPlaceIdAndCouponId(@RequestParam String placeId, @RequestParam String couponId);

    /**
     * 批量删除优惠券
     * @param placeId 场所id，必填
     * @param ids 删除的id列表，注意不是couponId
     * @return
     */
    @PostMapping(URL + "/batchDelete")
    GenericResponse<?> batchDelete(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam String placeId, @RequestBody List<Long> ids);

    /**
     * 分页查询优惠券列表
     * @param placeId 场所id
     * @param couponTypeId 优惠券类型id
     * @param couponName 优惠券名称
     * @param size 分页参数
     * @param page 分页参数
     * @return
     */
    @GetMapping(URL + "/findPage")
    GenericResponse<PagerDTO<DiscountCouponBO>> findPage(@RequestParam String placeId,
                                                         @RequestParam(required = false) String couponTypeId,
                                                         @RequestParam(required = false) String couponName,
                                                         @RequestParam(name = "size", defaultValue = "10") int size,
                                                         @RequestParam(name = "page", defaultValue = "1") int page);

    /**
     * 修改优惠券使用状态
     * @param requestTicket
     * @param placeId
     * @param couponId
     * @param status
     * @return
     */
    @PostMapping(URL + "/updateStatus")
    GenericResponse<?> updateStatus(@RequestHeader(value = "request_ticket") String requestTicket,
                                    @RequestParam String placeId, @RequestParam String couponId, @RequestParam int status);


    /**
     * 赠送优惠券
     * @param requestTicket
     * @param placeId 场所id
     * @param idNumber 证件id
     * @param couponId 优惠券id
     * @param quantity 数量
     * @return
     */
    @PostMapping(URL + "/giveCoupon")
    GenericResponse<?> giveCoupon(@RequestHeader(value = "request_ticket") String requestTicket,
                                  @RequestParam String placeId,
                                  @RequestParam String idNumber,
                                  @RequestParam String couponId,
                                  @RequestParam String remark,
                                  @RequestParam SourceType sourceType,
                                  @RequestParam int quantity,
                                  @RequestBody PlaceAccountBO placeAccountBO);
}
