/*
 * @(#)ClientBusinessIds.java 1.00 2025-3-4
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.enums;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * 
 * @version 1.00
 * @since 2025-3-4
 * <AUTHOR>
 * 
 *         Modified History:
 *
 */
public enum ClientBusinessIds {
	ALIPAY_IOT("20100", "支付宝IOT"), 
	ALIPAY_MINI_APP("20101", "支付宝小程序"),

	CLIENT("20120", "客户端"),

	CASHIER("20130", "收银台"),

	WECHAT_MP("20140", "微信公众号"),
	WECHAT_MINI_APP("20150", "微信小程序");

	/**
	 * business_id 业务编号
	 */
	private final String code;

	/**
	 * 描述
	 */
	private final String msg;

	/**
	 * @param code
	 * @param message
	 */
	private ClientBusinessIds(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public String getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}

}
