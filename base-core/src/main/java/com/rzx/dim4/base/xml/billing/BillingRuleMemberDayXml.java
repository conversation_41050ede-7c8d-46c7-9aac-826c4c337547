package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

@Getter
@Setter
@XmlRootElement(name = "memberday")
@XmlAccessorType(XmlAccessType.NONE)
public class BillingRuleMemberDayXml {

    @XmlElement(name = "GID")
    private String gId; // id

    @XmlElement(name = "recharge")
    private String recharge; // 充值金额

    @XmlElement(name = "reward")
    private String reward; // 奖励金额

    @XmlElement(name = "mode")
    private int mode; // 充赠模式

    @XmlElementWrapper(name = "memberdaydates")
    @XmlElement(name = "memberdaydate")
    private List<String> memberdaydate;// 会员日 日期

    @XmlElementWrapper(name = "cardtypes")
    @XmlElement(name = "cardtype")
    private List<String> cardNames;

}
