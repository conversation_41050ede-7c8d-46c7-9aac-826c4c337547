package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.SecurityRequestConfigBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.SecurityRequestConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/12
 **/
@Slf4j
@Component
public class SecurityRequestConfigApiHystrix implements SecurityRequestConfigApi {
    @Override
    public GenericResponse<ListDTO<SecurityRequestConfigBO>> findSecurityRequestConfigByPlaceIdIn(List<String> placeIds) {
        log.error("接口异常:::findSecurityRequestConfigByPlaceIdIn(placeIds:::{})", placeIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
