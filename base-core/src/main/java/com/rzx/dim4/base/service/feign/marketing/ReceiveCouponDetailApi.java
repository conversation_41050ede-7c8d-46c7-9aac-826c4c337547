package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.BuyGiftsBO;
import com.rzx.dim4.base.bo.marketing.ReceiveCouponDetailBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.NumberDto;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.ReceiveCouponDetailApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 个人优惠券相关接口
 * <AUTHOR>
 * @date 2024年08月14日 10:26
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "ReceiveCouponDetailApi", fallback = ReceiveCouponDetailApiHystrix.class)
public interface ReceiveCouponDetailApi {

    final String URL = "/feign/marketing/receiveCouponDetail";

    @PostMapping(URL + "/findCount")
    GenericResponse<NumberDto> findCount(@RequestBody Map<String, String> paramMap);

    /**
     * 分页查询
     * @param param 具体查询条件，详情 {@link com.rzx.dim4.marketing.service.ReceiveCouponDetailService.findAll}
     * @param placeId 场所id，必填
     * @param size 分页参数
     * @param page 分页参数
     * @return
     */
    @PostMapping(URL + "/findPage")
    public GenericResponse<PagerDTO<ReceiveCouponDetailBO>> findPage(@RequestBody Map<String,String> param,
                                                                     @RequestParam String placeId,
                                                                     @RequestParam(name = "size", defaultValue = "10") int size,
                                                                     @RequestParam(name = "page", defaultValue = "1") int page);

    /**
     * 批量删除已领取的优惠券（逻辑删除）
     * @param requestTicket
     * @param placeId 场所id
     * @param ids id列表
     * @return
     */
    @PostMapping(URL + "/deleteDetailss")
    public GenericResponse<?> deleteDetailss(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestParam String placeId,
                                             @RequestParam String accountId,
                                             @RequestParam String accountName,
                                             @RequestBody List<Long> ids);

    /**
     * 定时器失效优惠券
     */
    @GetMapping(URL + "/schedulerCheckValid")
    public void schedulerCheckValid();

    /**
     * 使用本地优惠券
     * @param requestTicket
     * @param encryptedCode 券码
     * @param placeId 场所id
     * @param idName 姓名
     * @param idNumber 证件号
     * @param operatorSourceType 来源
     * @return
     */
    @PostMapping(URL + "/useTicketCodeVerify")
    public GenericResponse<?> useTicketCodeVerify(@RequestHeader(value = "request_ticket") String requestTicket,
                                                  @RequestParam String encryptedCode,
                                                  @RequestParam String placeId,
                                                  @RequestParam String idName,
                                                  @RequestParam String idNumber,
                                                  @RequestParam SourceType operatorSourceType);

    /**
     * 查询优惠券统计
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping(URL + "/findShiftStatistics")
    public GenericResponse<SimpleObjDTO> findShiftStatistics(@RequestParam String placeId,@RequestParam String shiftId);


    /**
     * 验券准备
     * @param ticketCode 券码中扫出来的url或者code
     * @return
     */
    @PostMapping(URL + "/certificatePrepare")
    public GenericResponse<ObjDTO<BuyGiftsBO>> certificatePrepare(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                  @RequestParam("ticketCode")String ticketCode,
                                                                  @RequestParam("idNumber")String idNumber,
                                                                  @RequestParam("placeId")String placeId);

    /**
     * 验券---第三方券
     * @param encryptedCode 券码，需要从 certificatePrepare 方法获取
     * @param placeId 场所id
     * @param idName 姓名
     * @param idNumber 证件号
     * @param buyGiftsId 买赠活动id，需要从 certificatePrepare 方法获取
     * @param creater 创建人id
     * @param createrName 创建人名称
     * @param sourceType 优惠券来源
     * @param operatorSourceType 操作来源
     * @param clientId 客户端id
     * @param remark 备注
     * @param cashierId 收银台id
     * @return
     */
    @PostMapping(URL + "/certificateVerify")
    public GenericResponse<?> certificateVerify(@RequestHeader(value = "request_ticket") String requestTicket,
                                                @RequestBody Map<String,String> params);


    /**
     * 查询用户优惠券总数
     * @param placeId
     * @param idNumber
     * @return
     */
    @GetMapping(URL + "/findUserCouponCount")
    public int findUserCouponCount(@RequestParam("placeId")String placeId,
                                   @RequestParam("idNumber")String idNumber);
}
