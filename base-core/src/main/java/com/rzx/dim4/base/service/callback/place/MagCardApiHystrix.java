package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.MagCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.response.ResponsePage;
import com.rzx.dim4.base.service.feign.place.MagCardApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年11月07日 18:39
 */
@Slf4j
@Service
public class MagCardApiHystrix implements MagCardApi {

    @Override
    public GenericResponse<ResponsePage<MagCardBO>> findPage(Map<String, String> param) {
        log.error("MagCardApiHystrix.findPage 接口异常，param={}", new Gson().toJson(param));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> importDate(List<MagCardBO> bos) {
        log.error("MagCardApiHystrix.findPage 接口异常，bos={}", new Gson().toJson(bos));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> deleteByCardNo(String requestTicket, String deleteByCardNo) {
        log.error("MagCardApiHystrix.deleteByCardNo 接口异常，deleteByCardNo={}", deleteByCardNo);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MagCardBO>> findLoginInfoByCardNo(String cardNo) {
        log.error("MagCardApiHystrix.deleteByCardNo 接口异常，findLoginInfoByCardNo={}", cardNo);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
    @Override
    public GenericResponse<ObjDTO<MagCardBO>> findByCardNo(String cardNo) {
        log.error("MagCardApiHystrix.findByCardNo 接口异常，findLoginInfoByCardNo={}", cardNo);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> edit(String requestTicket, MagCardBO magCardBO) {
        log.error("MagCardApiHystrix.edit 接口异常，magCardBO={}", new Gson().toJson(magCardBO));
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
