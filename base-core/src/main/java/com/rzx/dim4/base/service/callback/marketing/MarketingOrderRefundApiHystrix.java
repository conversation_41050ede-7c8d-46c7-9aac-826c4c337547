package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingOrderRefundApi;
import com.rzx.dim4.base.vo.marketing.PreOrderRefundGoodsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> hwx
 * @since 2025/2/26 12:02
 */
@Component
@Slf4j
public class MarketingOrderRefundApiHystrix implements MarketingOrderRefundApi {
    @Override
    public GenericResponse<ListDTO<PreOrderRefundGoodsVO>> queryPreRefund(@RequestParam String placeId, @RequestParam String shiftId, @RequestParam String startDateTime,
                                                                          @RequestParam String endDateTime) {
        log.error("接口异常:::queryPreRefund(placeId:::{},shiftId:::{},startDateTime:::{},endDateTime:::{})", placeId, shiftId, startDateTime, endDateTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
