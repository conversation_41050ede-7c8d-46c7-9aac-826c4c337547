package com.rzx.dim4.base.service.feign.goods.query;

import lombok.Data;

@Data
public class StorageGoodsVo {
    /**
     * 原商品库存
     */
    private int newStocks;

    /**
     * 场所ID
     */
    private String placeId;

    /**
     * 商品信息ID
     */
    private String goodsId;

    /**
     *  // 商品类型ID
     */
    private String goodsTypeId;

    /**
     * // 商品库存数量
     */
    private int goodsStocks;

    /**
     * 操作人
     */
    private String creator;

    /**
     * 退款总金额
     */
    private int priceTotal;

    private String supplierId;

    private String remark;

}
