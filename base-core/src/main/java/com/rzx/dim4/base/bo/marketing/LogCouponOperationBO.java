package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.CouponOperationType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * 用户优惠券操作记录
 * <AUTHOR>
 * @date 2024年08月15日 16:32
 */
@Getter
@Setter
@ToString
public class LogCouponOperationBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String couponId; // 优惠券ID
    private String couponDetailId; // 领取记录id
    private String shiftId; // 班次ID
    private String createrName; // 操作人姓名
    private String clientId; // 客户端ID， 开卡可能没有客户端ID
    private CouponOperationType operationType; // 操作类型
    private SourceType sourceType; // 来源，0：客户端；1：收银台；2：微信；3：系统
    private String couponCode; // 优惠券券码、核销后需要写入
    private String idNumber; // 领取人身份证号码
    private String idName; // 领取人姓名
    private String couponName; // 优惠券名称
    private int amount; // 发放面额
    private String certificateId; // 代表一张券码的标识（撤销时需要），不等于券码但与券码一一对应，验券前可通过订单接口查询
    private String verifyId; // 代表券码一次核销的唯一标识(验券时返回) (次卡撤销多次时请填0)
}
