package com.rzx.dim4.base.enums.place;

/**
 * <AUTHOR>
 * @date 2025年07月18日 15:29
 */
public enum FunctionType {
    TOPUP("充值"),
    SHOPPING("购物"),
    TIME_PACKAGE("包时"),
    BOOK_SEATS("订座"),
    INVITE("请客上网"),
    COUPON_REDEMPTION("卡券核销"),
    ROOM_RESERVATION("预定包间"),
    GIFT_EXCHANGE("兑换礼品");

    private final String description;

    FunctionType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }

    //返回一个存着所有FunctionType的ArrayList集合
    public static FunctionType[] getAllFunctionTypes() {
        return FunctionType.values();
    }




}
