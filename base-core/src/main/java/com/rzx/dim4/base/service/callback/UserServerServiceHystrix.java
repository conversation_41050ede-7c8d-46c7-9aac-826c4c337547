package com.rzx.dim4.base.service.callback;

import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.bo.user.MiniApp.MiniAppUserBO;
import com.rzx.dim4.base.bo.user.WeChatUserBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.UserServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

//import com.rzx.dim4.base.bo.user.WeChatUserBO;
//import com.rzx.dim4.base.dto.ObjDTO;

/**
 * 
 * <AUTHOR>
 * @date Jun 15, 2020 11:07:41 AM
 */
@Slf4j
@Service
public class UserServerServiceHystrix implements UserServerService {

	@Override
	public GenericResponse<SimpleDTO> createQrCode(String sceneStr) {
		log.error("接口异常::: createQrCode(sceneStr:::{})", sceneStr);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<Dim4UserBO>> saveUser(String requestTicket, String name, String idNumber) {
		log.error("接口异常::: saveUser(name:::{},idNumber:::{})", name, idNumber);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<Dim4UserBO>> getUserByUserId(String userId) {
		log.error("接口异常::: getUserByUserId(userId:::{})", userId);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<Dim4UserBO>> getUserByIdNumber(String idNumber) {
		log.error("接口异常::: getUserByIdNumber(idNumber:::{})", idNumber);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<ObjDTO<WeChatUserBO>> getWechatUserByIdNumber(String idNumber) {
		log.error("接口异常:::getWechatUserByIdNumber(idNumber:{})", idNumber);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public void weChatSendTopupSuccess(@RequestParam String paramMapJson) {
		log.error("接口异常:::weChatSendTopupSuccess(paramMapJson:{})", paramMapJson);
	}

	@Override
	public GenericResponse<PagerDTO<MiniAppUserBO>> queryMiniAppUsers(String placeId, int size, int page,
			String[] orderColumns, String order,String accountIds) {
		log.error("接口异常:::queryMiniAppUsers(placeId:{},size:{},page:{},orderColumns:{},order:{},accountIds:{})", placeId, size, page,
				orderColumns, order,accountIds);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	@Override
	public GenericResponse<SimpleDTO> miniAppUnbound(String requestTicket, String placeId, String openid) {
		log.error("接口异常:::miniAppUnbound(requestTicket:{},placeId:{},openid:{})", requestTicket, placeId, openid);
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
}
