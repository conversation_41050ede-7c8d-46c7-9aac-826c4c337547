package com.rzx.dim4.base.vo.place;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> hwx
 * @since 2025/2/21 16:14
 */
@Getter
@Setter
public class PlaceShiftQueryItemVo implements Serializable {
    private static final long serialVersionUID = -161683625173906069L;
    private String placeId;
    private String shiftId;
    /**
     * 交班人id
     */
    private String onDutyAccountId;
    /**
     * 交班人
     */
    private String onDutyAccountName;
    /**
     * 交班时间
     */
    private LocalDateTime offWorkingTime;

    /**
     * 接班时间
     */
    private LocalDateTime workingTime;

    /**
     * 留给下个班次金额
     */
    private int nextShiftHandoverCash;

    /**
     * 线上收入
     */
    private int totalIncome;

    /**
     * 网费收入
     */
    private int internetFeeIncome;

    /**
     * 商超收入
     */
    private int shopIncome;

    /**
     * 前班退款
     */
    private int preRefundAmount;

    /**
     * 盘点库存差
     */
    private int storageDiff;
}
