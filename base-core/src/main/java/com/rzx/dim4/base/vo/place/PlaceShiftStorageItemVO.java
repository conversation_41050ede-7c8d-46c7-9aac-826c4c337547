package com.rzx.dim4.base.vo.place;

import com.rzx.dim4.base.bo.place.PlaceShiftSaleBO;
import com.rzx.dim4.base.bo.place.PlaceShiftStorageBO;
import com.rzx.dim4.base.enums.place.PlaceShiftSaleStatisticType;
import lombok.Data;

import java.util.List;

/** 交班统计库存数据
 * <AUTHOR> hwx
 * @since 2025/2/24 16:23
 */
@Data
public class PlaceShiftStorageItemVO {
    /**
     * 账存总数
     */
    private int totalStorage;
    /**
     * 实际总数
     */
    private int totalActual;
    /**
     * 盈亏数
     */
    private int totalProfitLoss;
    /**
     * 盈亏额
     */
    private int totalProfitLossPrice;
    /**
     * 销售数据
     */
    private List<PlaceShiftStorageBO> storageBOS;

    private String goodsTypeId;
    private String goodsTypeName;
}
