package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.GoodsStocktakingBO;
import com.rzx.dim4.base.bo.marketing.MarketOrderStatisticsBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingOrderApi;
import com.rzx.dim4.base.service.feign.marketing.MarketingShiftApi;
import com.rzx.dim4.base.vo.place.PlaceShiftDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * <AUTHOR> hwx
 * @since 2025/2/21 17:22
 */
@Component
@Slf4j
public class MarketingShiftApiHystrix implements MarketingShiftApi {
    @Override
    public GenericResponse<ObjDTO<PlaceShiftDetailVO>> statistics(String placeId, PlaceAccountBO miniAppLoginAccount) {
        log.error("接口异常:::MarketingShiftApiHystrix.statistics(placeId:::{},miniAppLoginAccount:::{})", placeId, new Gson().toJson(miniAppLoginAccount));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsStocktakingBO>> saveGoodsStocktaking(@RequestBody GoodsStocktakingBO goodsStocktakingBO) {
        log.error("接口异常:::MarketingShiftApiHystrix.saveGoodsStocktaking(goodsStocktakingBO:::{})", new Gson().toJson(goodsStocktakingBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

//    @Override
//    public GenericResponse<ObjDTO<PlaceShiftDetailVO>> submit(String placeId, PlaceAccountBO miniAppLoginAccount) {
//        return null;
//    }
}
