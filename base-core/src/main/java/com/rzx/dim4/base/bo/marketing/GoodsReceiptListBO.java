package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class GoodsReceiptListBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String goodsReceiptNum; // 入库单编号

    @NotBlank(message = "商品id不能为空!")
    private String goodsId; // 商品id

    private String goodsName; // 商品名称
    private String goodsPic; // 商品图片
    private String goodsTypeId; // 商品类型ID

    @Min(value = 1,message = "参数错误,商品数量最小为1!")
    private int number; // 商品数量
    private int price; // 发生单价

    @Max(value = 1,message = "参数错误")
    @Min(value = 0,message = "参数错误")
    private int isGift; // 是否赠品（默认否）0否，1是

    private String goodsTypeName; // 商品类型名称





}
