package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.Digits;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品标签表
 *
 * <AUTHOR>
 * @date 2024年12月03日 15:27
 */
@Getter
@Setter
public class SaveGoodsMoveRecordAddBO extends AbstractEntityBO {

    private PlaceAccountBO webLoginAccount;


    private List<GoodsMoveRecordBO> goodsMoveRecordBOS;

}
