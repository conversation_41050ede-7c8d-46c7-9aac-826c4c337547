package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.marketing.ClientMatterType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/** 事物数量BO
 * <AUTHOR> hwx
 * @since 2025/3/4 11:44
 */
@Getter
@Setter
public class ClientMatterQuantityBO extends AbstractEntityBO implements Serializable {

    private static final long serialVersionUID = 419375847851900455L;

    private String placeId; // 场所ID

    private Integer clientMatterNum; // 代办任务数量
}
