package com.rzx.dim4.base.vo.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.excel.LocalDateTimeConverter;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 赠送网费订单导出
 *
 * <AUTHOR>
 * @since 2025-05-14
 **/
@Getter
@Setter
@HeadRowHeight(value = 20)//设置表头行高
@ColumnWidth(value = 15)//设置表头行宽
public class ExportInternetFeeOrderVO extends AbstractEntityBO {

    @ColumnWidth(value = 25)
    @ExcelProperty(value = "会员ID", index = 0)
    private String cardId;

    @ColumnWidth(value = 18)
    @ExcelProperty(value = "金额", index = 1)
    private String amount;

    @ColumnWidth(value = 25)
    @ExcelProperty(value = "操作人", index = 2)
    private String operator;

    @ColumnWidth(value = 25)
    @ExcelProperty(value = "时间", index = 3, converter = LocalDateTimeConverter.class)
    private LocalDateTime created;

    @ColumnWidth(value = 50)
    @ExcelProperty(value = "备注", index = 4)
    private String remark;
}
