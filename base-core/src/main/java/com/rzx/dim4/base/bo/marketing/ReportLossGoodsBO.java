package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 报损单商品列表
 * <AUTHOR>
 * @date 2024年12月04日
 */
@Getter
@Setter
@ToString
public class ReportLossGoodsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String reportLossNum; // 报损单单号
    private String goodsId; // 商品id
    private String goodsName; // 商品名称
    private String goodsPic; // 商品图片
    private int number; // 数量
    private int price; // 发生单价（单位分）


}
