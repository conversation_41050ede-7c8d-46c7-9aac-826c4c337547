package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.user.BusinessReportType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceShiftApiHystrix;
import com.rzx.dim4.base.vo.marketing.PlaceShiftBusinessReportVO;
import com.rzx.dim4.base.vo.place.PlaceShiftDetailVO;
import com.rzx.dim4.base.vo.place.PlaceShiftQueryVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/6/11
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceShiftApi", fallback = PlaceShiftApiHystrix.class)
public interface PlaceShiftApi {

    String URL = "/feign/place/shift";


    @PostMapping(URL + "/queryAll")
    GenericResponse<ListDTO<PlaceShiftBO>> queryAll(@RequestHeader(value = "request_ticket") String requestTicket,
                                                    @RequestBody HashMap<String, String> queryMap);

    /**
     * 交班接口，暂定只支持密码
     *
     * @param requestTicket
     * @param placeId
     * @param accountId
     * @param successorAccountId
     * @param password
     * @param nextShiftHandoverCash
     * @param remark
     * @return
     */
    @PostMapping(URL+"/submit")
    GenericResponse<?> submit(@RequestHeader(value = "request_ticket") String requestTicket,
                              @RequestParam String placeId,
                              @RequestParam String accountId,
                              @RequestParam String successorAccountId,
                              @RequestParam String password,
                              @RequestParam int nextShiftHandoverCash,
                              @RequestParam(required = false,defaultValue = "") String remark
                              );




    /**
     * 小程序交班接口
     *
     * @param requestTicket
     * @param placeId
     * @param accountId
     * @param successorAccountId
     * @param nextShiftHandoverCash
     * @param remark
     * @return
     */
    @PostMapping(URL+"/submitForMiniApp")
    GenericResponse<?> submitForMiniApp(@RequestHeader(value = "request_ticket") String requestTicket,
                              @RequestParam String placeId,
                              @RequestParam String accountId,
                              @RequestParam String successorAccountId,
                              @RequestParam int nextShiftHandoverCash,
                              @RequestParam(required = false,defaultValue = "") String remark
    );

    @GetMapping(URL+"/findPrePlaceShift")
    GenericResponse<ObjDTO<PlaceShiftBO>> findPrePlaceShiftByPlaceIdAndShiftId(@RequestParam String placeId, @RequestParam String shiftId);

    @GetMapping(URL+"/findPlaceShiftByPlaceIdAndShiftId")
    GenericResponse<ObjDTO<PlaceShiftBO>> findPlaceShiftByPlaceIdAndShiftId(@RequestParam String placeId, @RequestParam String shiftId);


    /**
     * 查询交班记录
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    @GetMapping(URL+"/query")
    GenericResponse<ObjDTO<PlaceShiftQueryVo>> query(@RequestParam String placeId, @RequestParam String startDateTime, @RequestParam String endDateTime);

    /**
     * 获取交班班次详情
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping(URL+"/detail")
    GenericResponse<ObjDTO<PlaceShiftDetailVO>> detail(@RequestParam String placeId, @RequestParam String shiftId);

    /**
     * 查询:限制个数3
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL+"/queryLimit")
    GenericResponse<ObjDTO<PlaceShiftQueryVo>> queryLimit(@RequestParam String placeId);

    /**
     * 查询:限制个数3
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL+"/queryLimitByRangeTime")
    GenericResponse<ObjDTO<PlaceShiftQueryVo>> queryLimitByRangeTime(@RequestParam String placeId, @RequestParam String startTime, @RequestParam String endTime);




    @GetMapping(URL+"/findWorkingShiftByCashierId")
    GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByCashierId(@RequestParam String placeId,
                                                                             @RequestParam String cashierId);

    @GetMapping(URL+"/findTop1ByPlaceIdAndStatus")
    GenericResponse<ObjDTO<PlaceShiftBO>> findTop1ByPlaceIdAndStatusOrderByIdDesc(@RequestParam String placeId, @RequestParam int status);

    @GetMapping(URL+"/queryShiftBeforeTime")
    GenericResponse<ListDTO<PlaceShiftBO>> queryShiftListBeforeTime(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam String startDateTime);


    @PostMapping(URL+"/updateWithdrawal")
    GenericResponse<?> updateWithdrawal(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String shiftIds);

    @GetMapping(URL+"/findByPlaceIdAndBetweenTime")
    GenericResponse<ListDTO<PlaceShiftBO>> findByPlaceIdAndBetweenTime(@RequestParam String placeId, @RequestParam String startTime, @RequestParam String endTime );


    /**
     * 获取交班关联班次
     *
     * @param placeId
     * @param shiftIds
     * @return
     */
    @GetMapping(URL+"/getAssociatedShift")
    GenericResponse<ObjDTO<MiniAppPlaceShiftBO>> getAssociatedShift(@RequestParam String placeId, @RequestParam String shiftIds);


    /**
     * 获取当天经营日报
     *
     * @param placeId
     * @param startTime
     * @return
     */
    @GetMapping(URL+"/bussinessDailyReport")
    GenericResponse<ObjDTO<PlaceShiftDailyBussinessBO>> getBussinessDailyReport(@RequestParam String placeId, @RequestParam String startTime);

    /**
     * 获取当周经营日报
     *
     * @param placeId
     * @param startTime
     * @return
     */
    @GetMapping(URL+"/bussinessWeekReport")
    GenericResponse<ObjDTO<PlaceShiftWeeklyReportBO>> getBussinessWeekReport(@RequestParam String placeId, @RequestParam String startTime);

    /**
     * 获取当月经营日报
     *
     * @param placeId
     * @param startTime
     * @return
     */
    @GetMapping(URL+"/bussinessMonthReport")
    GenericResponse<ObjDTO<PlaceShiftMonthlyReportBO>> getBussinessMonthReport(@RequestParam String placeId, @RequestParam String startTime);

    /**
     *
     * @param placeId
     * @param page
     * @param size
     * @return
     */
    @GetMapping(URL+"/shiftDailyReport")
    GenericResponse<PagerDTO<PlaceShiftBusinessReportVO>> placeShiftBusinessReport(@RequestParam String placeId,
                                                                                   @RequestParam BusinessReportType type,
                                                                                   @RequestParam int page,
                                                                                   @RequestParam int size);
}
