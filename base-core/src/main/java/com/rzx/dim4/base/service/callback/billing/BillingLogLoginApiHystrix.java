package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.bo.billing.LogLoginQueryRequestBO;
import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.bo.billing.LogOperationQueryRequestBO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingLogLoginApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Service
public class BillingLogLoginApiHystrix implements BillingLogLoginApi {
    @Override
    public GenericResponse<PagerDTO<LogLoginBO>> findLogLoginPage(LogLoginQueryRequestBO paramsBo) {
        log.info("接口异常:::BillingLogLoginApiHystrix.findLogLoginPage({})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogOperationBO>> findLogOperationPage(@RequestBody LogOperationQueryRequestBO paramsBo) {
        log.info("接口异常:::BillingLogLoginApiHystrix.findLogOperationPage({})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
