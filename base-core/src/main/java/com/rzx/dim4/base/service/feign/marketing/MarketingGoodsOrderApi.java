package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.MiniAppOrderQueryBO;
import com.rzx.dim4.base.bo.marketing.statistics.StatisticsMiniAppOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsOrderApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 营销商品订单相关接口
 *
 * <AUTHOR>
 * @date 2025年02月21日 11:11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsOrderApi", fallback = MarketingGoodsOrderApiHystrix.class)
public interface MarketingGoodsOrderApi {

    final String URL = "/feign/marketing/goodsOrder";

    @PostMapping(value = URL + "/getOrder")
    GenericResponse<ObjDTO<OrdersBO>> getOrder(@RequestParam String orderId,
                                               @RequestParam String placeId);


    @PostMapping(value = URL + "/createShopOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> createShopOrder(@RequestHeader("request_ticket") String requestTicket,
                                                             @RequestBody OrdersBO ordersBO);

    @GetMapping(value = URL + "/queryOrderList")
    GenericResponse<PagerDTO<OrdersBO>> queryOrderList(@RequestParam String placeId,
                                                       @RequestParam String idNumber,
                                                       @RequestParam int page,
                                                       @RequestParam int size);

    @PostMapping(value = URL + "/miniAppCreateShopOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> miniAppCreateShopOrder(@RequestHeader("request_ticket") String requestTicket,
                                                                    @RequestBody OrdersBO ordersBO);

    @PostMapping(value = URL + "/miniAppQueryOrderList")
    GenericResponse<ObjDTO<StatisticsMiniAppOrderBO>> miniAppQueryOrderList(@RequestBody MiniAppOrderQueryBO queryBO);

    @PostMapping(value = URL + "/createGoodsGiftOrder")
    GenericResponse<ObjDTO<OrdersBO>> createGoodsGiftOrder(@RequestBody OrdersBO ordersBO);
}
