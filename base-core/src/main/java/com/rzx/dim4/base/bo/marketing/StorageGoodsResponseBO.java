package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存信息表
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel("库存信息表")
public class StorageGoodsResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "库存数量")
    private int goodsStocksNum;

    @ApiModelProperty(value = "成本价 单位分")
    private int costPrice;

    @ApiModelProperty(value = "零售价 单位分")
    private int unitPrice;

    @ApiModelProperty(value = "单位")
    private int unit;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "货架信息")
    private List<StorageRackInfo> StorageRackInfos;


    @Data
    public static class StorageRackInfo {
        @ApiModelProperty(value = "货架id，场所唯一，从100000开始递增,主仓库为000000")
        private String storageRackId;

        @ApiModelProperty(value = "货架名称")
        private String storageRackName;

        @ApiModelProperty(value = "商品ID")
        private String goodsId;

        @ApiModelProperty(value = "库存数量，最小值0")
        private int goodsStocksNum;
    }
}
