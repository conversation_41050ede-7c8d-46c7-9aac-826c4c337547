package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.OrdersApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年03月03日 16:55
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "OrdersApi", fallback = OrdersApiHystrix.class)
public interface OrdersApi {

    String URL = "/feign/marketing/orders";

    @GetMapping(URL + "/queryOrderByPlaceIdAndOrderId")
    GenericResponse<ObjDTO<OrdersBO>> queryOrderByPlaceIdAndOrderId(@RequestParam String placeId, @RequestParam String orderId);

    @PostMapping(value = URL + "/saveOrder")
    GenericResponse<ObjDTO<OrdersBO>> saveOrder(@RequestBody OrdersBO ordersBO);

    @GetMapping(value = URL + "/notifyOrder")
    public GenericResponse<ObjDTO<OrdersBO>> notifyOrder(@RequestParam String orderId, @RequestParam String payRefundId, @RequestParam Integer poundage, @RequestParam Integer refundPoundage);

    //扫码枪支付完成后再次收到推送更新订单手续费
    @PostMapping(value = URL + "/updateOrderFee")
    GenericResponse<ObjDTO<OrdersBO>> updateOrderFee(@RequestParam String orderId, @RequestParam Integer poundage);

    @GetMapping(value = URL + "/findPage")
    GenericResponse<PagerDTO<OrdersBO>> findPage(@RequestParam String placeId,
                                                 @RequestParam(required = false) String idNumber,
                                                 @RequestParam String orderType,
                                                 @RequestParam(required = false) String startTime,
                                                 @RequestParam(required = false) String endTime,
                                                 @RequestParam(name = "length", defaultValue = "10") int size,
                                                 @RequestParam(name = "start", defaultValue = "0") int page);


    /**
     * 查询用户上机时长
     * @param idNumber
     */
    @GetMapping(value=URL + "/sumMoneyByPlaceAndIdNumberAndTime")
    int sumMoneyByPlaceAndIdNumberAndTime(@RequestParam String placeId, @RequestParam String idNumber, @RequestParam LocalDateTime startDate, @RequestParam LocalDateTime endDate);

    /**
     * 查询网吧当班未派送订单数量
     */
    @GetMapping(value=URL + "/countNonDeliveryOrders")
    int countNonDeliveryOrders(@RequestParam String placeId, @RequestParam LocalDateTime startDate, @RequestParam LocalDateTime endDate);

    /**
     * 查询租赁中的押金订单
     */
    @GetMapping(URL + "/queryRentingOrderByPlaceId")
    GenericResponse<ListDTO<OrdersBO>> queryRentingOrderByPlaceId(@RequestParam String placeId,@RequestParam String idNumber);
}
