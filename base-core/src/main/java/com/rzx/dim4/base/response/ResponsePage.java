package com.rzx.dim4.base.response;

import com.rzx.dim4.base.dto.AbstractDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 接口返回列表分页对象
 * 
 * 使用Datatables，教程访问
 * 
 * @see <a href="https://datatables.net"> Datatables.net </a>
 *
 * <AUTHOR>
 * @date 2019-09-17 09-49:43
 * @param T VO对象
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ResponsePage<T> extends AbstractDTO {

	private int draw;
	private int recordsTotal;
	private int recordsFiltered;
	private List<T> data;
	private String error;

	/**
	 * 
	 * @param draw  表格绘制计数器
	 * @param total 总记录数
	 * @param data  数据列表
	 */
	public ResponsePage(int draw, int total, List<T> data) {
		this.draw = draw;
		this.recordsFiltered = total;
		this.recordsTotal = total;
		this.data = data;
	}

	/**
	 * 
	 * @param draw  表格绘制计数器
	 * @param error 错误信息
	 */
	public ResponsePage(int draw, String error) {
		this.draw = draw;
		this.recordsFiltered = 0;
		this.recordsTotal = 0;
		this.data = new ArrayList<T>();
		this.error = error;
	}

}
