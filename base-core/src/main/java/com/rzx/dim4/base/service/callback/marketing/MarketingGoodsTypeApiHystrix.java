package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.GoodsTypeBO;
import com.rzx.dim4.base.bo.marketing.SaveGoodsTypeAddBO;
import com.rzx.dim4.base.bo.marketing.ShopConfigBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsTypeApi;
import com.rzx.dim4.base.service.feign.marketing.MarketingShopConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025年02月11日 14:26
 */
@Slf4j
@Component
public class MarketingGoodsTypeApiHystrix implements MarketingGoodsTypeApi {


    @Override
    public GenericResponse<ObjDTO<GoodsTypeBO>> saveTag(String requestTicket, String placeId, String accountId, GoodsTypeBO goodsTypeBO) {
        log.error("接口异常，saveTag(requestTicket={},placeId={},accountId={},addBO={})", requestTicket, placeId, accountId, new Gson().toJson(goodsTypeBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsTypeBO>> findPageList(String goodsTypeName, String placeId, int page, int size) {
        log.error("接口异常，findPageList(goodsTypeName={},placeId={},page={},size={})", goodsTypeName, placeId, page, size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
