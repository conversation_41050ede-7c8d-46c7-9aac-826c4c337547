package com.rzx.dim4.base.cons;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 系统用到的常量
 *
 * <AUTHOR>
 * @date Jul 22, 2020 10:50:37 AM
 */
public final class BaseConstants {

    // 私有构造方法， 不允许实例化
    private BaseConstants() {
    }

    public static final String REDIS_KEY_PREFIX_GET_IDENTIFIER_BY_PLACEID_ = "[place]_get_identifier_by_placeid_"; // 根据placeId获取识别码的key

    public static final String KEY_THIRD_ACCOUNT_BY_PLACEID= "billing:third_account_by_placeid_"; // 根据placeId获取对应的第三方账号信息
    public static final String PLACE_EXCHANGE_OPERATION = "换"; // 会员详情查询操作记录，换机操作的常量字段

    public static final String AES_CBC_KEY = "rwy@1218tanqcode"; // 超管登入密码AES CBC 加密key

    public static final String AES_CBC_IV = "rwy@1218tanqcode";

    public static final String THIRD_TYPE_JWELL = "九威"; // 九威

    public static final String THIRD_TYPE_MARKET = "营销大师"; // 营销大师

    public static final String THIRD_DA_BA_ZHANG = "大巴掌"; // 营销大师

    public static final String THIRD_TYPE_YI_SHANG_WANG = "易上网APP"; // 易上网APP

    public static final String THIRD_TYPE_QING_WANG = "轻网联盟"; // 轻网联盟

    public static final String THIRD_TYPE_IOT = "IOT"; // IOT设备/IOT小程序

    public static final String BILLING_LOGIN = "billing_login_";  // 登入接口保存redis的key前缀

	public static final String DEFAULT_PASSWORD = "lgj123456"; // 默认密码

    public static final LocalDateTime DEAD_LINE = LocalDateTime.of(2049, 12, 31, 23, 59, 59);

    /**
     * 通过第三方接口进入大巴掌的，不要密码，用这个字段标识
     */
    public static final String REQUEST_FROM_THIRD_API_FLAG  = "requestFromThirdApiFlag";

    // 临时卡 id
    public static final String TEMP_CARD_TYPE_ID = "1000";

    // 员工卡 id
    public static final String EMPLOYEE_CARD_TYPE_ID = "1002";

    // 会员卡以外的卡类型列表
    public static final List<String> CARD_TYPE_ID_LIST_EXCLUDE_MEMBER = Arrays.asList(BaseConstants.EMPLOYEE_CARD_TYPE_ID, BaseConstants.TEMP_CARD_TYPE_ID);

    public static final String MEMBER_CARD_DESC = "会员卡";

    public static final String CASHIER_USER_TOKEN_HEAD = "BILLING:CASHIER_USER_ACCOUNT_" ;
    public static final String CLIENT_USER_TOKEN_HEAD = "BILLING:CLIENT_USER_ACCOUNT_" ;

    public static final String WEB_USER_TOKEN_HEAD = "PLACE:USER_ACCOUNT_" ;

    public static final String TIPS_CLIENT_MATTER ="MARKETING:TIPS_CLIENT_MATTER_"; //事务处理tips的redis的key
    public static final String TIPS_GOODS_ORDER ="MARKETING:TIPS_GOODS_ORDER_"; //订单的tips的redis的key


    public static final String MINI_APP_CLIENT_MATTER ="MARKETING:MINI_APP_CLIENT_MATTER_"; //事务处理小程序的redis的key
    public static final String MINI_APP_GOODS_ORDER ="MARKETING:MINI_APP_GOODS_ORDER_"; //订单的小程序的redis的key


    public static final String BIGSCREEN_KEY = "MARKETING:QRCODE:";
}
