package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 固装商品修改BO
 *
 * <AUTHOR>
 * @date 2025年06月27日 15:27
 */
@Getter
@Setter
public class FixGoodsUpdateBO extends AbstractEntityBO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "创建者")
    private Long creater;

    @ApiModelProperty(value = "账号创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "账号更新时间")
    private LocalDateTime updated;

    @ApiModelProperty(value = "删除状态")
    private Integer deleted;

    @ApiModelProperty(value = "场所ID",required = true)
    private String placeId;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    @Length(message = "商品名称不能超过50字符!", max = 50)
    private String goodsName;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "区域ID")
    @Length(message = "区域类型不能超过200字符!", max = 200)
    private String areaIds;

    @ApiModelProperty(value = "商品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "虚拟商品内部编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品销售价，单位分")
    private Integer unitPrice;

    @ApiModelProperty(value = "商品成本价，单位分")
    private Integer costPrice;

    @ApiModelProperty(value = "商品网费价，单位分")
    private Integer networkPrice;

    @ApiModelProperty(value = "商品额度")
    private Integer goodsQuota;

    @ApiModelProperty(value = "支持奖励余额购买，0是，1否，默认1")
    private Integer supportPresentSwitch;

    @ApiModelProperty(value = "支持本金购买，0是，1否，默认0")
    private Integer supportCashSwitch;

    @ApiModelProperty(value = "单位，需要给出码表")
    private Integer unit;

    @ApiModelProperty(value = "口味，多个标签用,隔开")
    @Length(message = "口味字段过长！", max = 50)
    private String specs;

    @ApiModelProperty(value = "副标题")
    @Length(message = "副标题不能超过50字符！", max = 50)
    private String subheading;

    @ApiModelProperty(value = "虚拟商品类型（0网费，1现金，2积分，3幸运抽奖，4台桌门票，5集卡活动，6砸金蛋次数，7员工投票，8网费押金，9其他）")
    private Integer virtualGoodsType;

    @ApiModelProperty(value = "商品种类，0固装商品，1虚拟商品，2自制商品，3优惠券",required = true)
    private Integer goodsCategory;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "卡类型ID")
    @Length(message = "卡类型不能超过90字符！", max = 90)
    private String cardTypeIds;

    @ApiModelProperty(value = "卡类型名称")
    private String cardTypeNames;

    @ApiModelProperty(value = "销售状态，0售卖中，1停售")
    private Integer sellStatus;

    @ApiModelProperty(value = "标签ID，用,分割")
    @Length(message = "标签类型过多", max = 50)
    private String tagIds;

    @ApiModelProperty(value = "标签名称，用,分割")
    private String tagNames;

    @ApiModelProperty(value = "系统标签名称，用,分割")
    private String sysTagNames;

    @ApiModelProperty(value = "售卖周期类型，0每日、1每周、2每月、3指定时间段")
    private Integer cycle;

    @ApiModelProperty(value = "每日开始售卖时间点1，默认0")
    private float startTime1;

    @ApiModelProperty(value = "每日结束售卖时间段1，默认24")
    private float endTime1;

    @ApiModelProperty(value = "每日开始售卖时间点2")
    private float startTime2;

    @ApiModelProperty(value = "每日结束售卖时间段2")
    private float endTime2;

    @ApiModelProperty(value = "每日开始售卖时间点3")
    private float startTime3;

    @ApiModelProperty(value = "每日结束售卖时间段3")
    private float endTime3;

    @ApiModelProperty(value = "每周售卖日期，默认 1,2,3,4,5,6,7")
    private String weeks;

    @ApiModelProperty(value = "每月售卖日期，默认 0,1,2,3,4,...31")
    private String days;

    @ApiModelProperty(value = "商品限购类型，0每日、1每周、2每月、3永久")
    private Integer extType;

    @ApiModelProperty(value = "商品限购数量，默认0不限制")
    private Integer extCount;

    @ApiModelProperty(value = "销量")
    private Integer initSaleNum;

    @ApiModelProperty(value = "商品排序")
    private Integer sort;

    @ApiModelProperty(value = "是否计算库存，0-是，1-否")
    private Integer isCalculateInventory;

    @ApiModelProperty(value = "预警值")
    private Integer stockAlarm;

    @ApiModelProperty(value = "是否客户端展示，0展示，1不展示")
    private Integer showClientSwitch;

    @ApiModelProperty(value = "是否收银台展示，0展示，1不展示")
    private Integer showCashierSwitch;

    @ApiModelProperty(value = "是否移动端展示，0展示，1不展示")
    private Integer showMobileSwitch;

    @ApiModelProperty(value = "是否允许在线支付，0是，1否")
    private Integer onlinePaySwitch;

    @ApiModelProperty(value = "是否参加排行榜，0是，1否")
    private Integer showRank;

    @ApiModelProperty(value = "条形码列表")
    private List<String> barcodes;

    @ApiModelProperty(value = "业务商品ID列表")
    private List<String> goodsIds;

    @ApiModelProperty(value = "开始日期（指定时间段）")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期（指定时间段）")
    private LocalDate endDate;

    @ApiModelProperty(value = "库存数")
    private Integer goodsStocksNum;

    @ApiModelProperty(value = "售卖时间状态")
    private boolean cycleStatus;

    @ApiModelProperty(value = "优惠券ID")
    private String couponId;

    @ApiModelProperty(value = "主仓库库存")
    private Integer mainGoodsStocksNum;

    @ApiModelProperty(value = "折扣模式：0指定折扣值，1折后金额")
    private Integer discountMode;

    @ApiModelProperty(value = "折扣率（折扣模式为0时有效）")
    private double discountRate;

    @ApiModelProperty(value = "折后金额（折扣模式为1时有效）")
    private Integer discountedAmount;

    @ApiModelProperty(value = "买赠，购买多少件商品才能送")
    private Integer needBuyNum;

    @ApiModelProperty(value = "赠送商品ID列表")
    private List<String> buyGiftsGoodIds;

    @ApiModelProperty(value = "赠送商品对象列表")
    private List<BuyGiftsGoodsBO> buyGiftsGoodsBOS;

    @ApiModelProperty(value = "赠送数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "买赠价格")
    private Integer buyGiftPrice;

    @ApiModelProperty(value = "是否赠送，0否，1是")
    private Integer present;

    @ApiModelProperty(value = "赠送网费")
    private Integer presentAmount;

    @ApiModelProperty(value = "会员卡等级调整至")
    private String cardLevelTo;

    @ApiModelProperty(value = "会员卡等级名称")
    private String cardLevelToName;

    @ApiModelProperty(value = "网费充送ID")
    private String internetFeeId;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "网费充送对象")
    private InternetFeeBO internetFeeBO;

}
