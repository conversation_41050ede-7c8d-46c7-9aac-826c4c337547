package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.GoodsInventoryChangeRecordBO;
import com.rzx.dim4.base.bo.marketing.GoodsMoveRecordBO;
import com.rzx.dim4.base.bo.marketing.GoodsTypeBO;
import com.rzx.dim4.base.bo.marketing.SaveGoodsMoveRecordAddBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsApiHystrix;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsMoveRecordApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 库存管理->上下架管理相关接口
 *
 * <AUTHOR>
 * @date 2025年02月20日 14:42
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsMoveRecordApi", fallback = MarketingGoodsMoveRecordApiHystrix.class)
public interface MarketingGoodsMoveRecordApi {

    String URL = "/feign/marketing/goodsMoveRecord";

    /**
     * 分页查询上下架记录
     *
     * @param recordType
     * @param placeId
     * @param page
     * @param size
     * @return
     */
    @GetMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<GoodsMoveRecordBO>> findPageList(@RequestParam(name = "recordType", required = false) String recordType,
                                                              @RequestParam(name = "placeId") String placeId,
                                                              @RequestParam(name = "startDate", required = false) String startDate,
                                                              @RequestParam(name = "endDate", required = false) String endDate,
                                                              @RequestParam(name = "page", defaultValue = "0") int page,
                                                              @RequestParam(name = "size", defaultValue = "10") int size);


    /**
     * 查询上下架记录详情
     *
     * @param changeRecordId
     * @param placeId
     * @return
     */
    @GetMapping(value = URL + "/findDetail")
    GenericResponse<ListDTO<GoodsInventoryChangeRecordBO>> findDetail(@RequestParam(name = "changeRecordId") String changeRecordId,
                                                                      @RequestParam(name = "placeId") String placeId,
                                                                      @RequestParam(name = "recordType") int recordType);


    /**
     * 商品上下架到仓库\货架接口
     *
     * @param saveGoodsMoveRecordAddBO
     * @return
     */
    @PostMapping(URL + "/save")
    GenericResponse<?> saveGoodsMoveRecord(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody SaveGoodsMoveRecordAddBO saveGoodsMoveRecordAddBO);

}
