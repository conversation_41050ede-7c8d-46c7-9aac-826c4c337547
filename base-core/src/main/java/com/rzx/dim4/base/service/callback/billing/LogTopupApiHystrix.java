package com.rzx.dim4.base.service.callback.billing;

import com.qiniu.util.Json;
import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.LogTopupApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2025/3/21
 **/
@Slf4j
@Service
public class LogTopupApiHystrix implements LogTopupApi {
    @Override
    public GenericResponse<ObjDTO<LogTopupBO>> createLogTopup(String requestTicket, LogTopupBO logTopupBO) {
        log.error("接口异常:::createLogTopup(placeId:::{}，logTopupBO:::{})", requestTicket, Json.encode(logTopupBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> updateLogTopup(String requestTicket, OrdersBO ordersBO) {
        log.error("接口异常:::updateLogTopup(requestTicket:::{}，ordersBO:::{})", requestTicket,ordersBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
