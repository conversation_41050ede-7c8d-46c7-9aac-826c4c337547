package com.rzx.dim4.base.exception;

import com.rzx.dim4.base.enums.ServiceCodes;
import lombok.Getter;

/**
 * 自定义service异常
 * 
 * <AUTHOR>
 * @date Dec 23, 20194:13:05 PM
 */
public class ServiceException extends RuntimeException {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8743787682084319948L;

	protected final ServiceCodes serviceCode;

	@Getter
	protected String detailedMsg = null;

	/**
	 * 无参默认构造UNSPECIFIED
	 */
	public ServiceException() {
		super(ServiceCodes.UNSPECIFIED.getMessage());
		this.serviceCode = ServiceCodes.UNSPECIFIED;
		this.detailedMsg = null;
	}

	/**
	 * 指定错误码构造通用异常
	 * 
	 * @param serviceCode 错误码
	 */
	public ServiceException(final ServiceCodes serviceCode) {
		super(serviceCode.getMessage());
		this.serviceCode = serviceCode;
		this.detailedMsg = null;
	}

	/**
	 * 指定详细描述构造通用异常
	 * 
	 * @param detailedMessage 详细描述
	 */
	public ServiceException(final String detailedMessage) {
		super(detailedMessage);
		this.serviceCode = ServiceCodes.UNSPECIFIED;
		this.detailedMsg = detailedMessage;
	}

	/**
	 * 指定导火索构造通用异常
	 *
	 * @param t
	 */
	public ServiceException(final Throwable t) {
		super(t);
		this.serviceCode = ServiceCodes.UNSPECIFIED;
	}

	/**
	 * 构造通用异常
	 * 
	 * @param serviceCode       错误码
	 * @param detailedMessage 详细描述
	 */
	public ServiceException(final ServiceCodes serviceCode, final String detailedMessage) {
		super(detailedMessage);
		this.serviceCode = serviceCode;
		this.detailedMsg = detailedMessage;
	}

	/**
	 * 构造通用异常
	 * 
	 * @param serviceCode 错误码
	 * @param t
	 */
	public ServiceException(final ServiceCodes serviceCode, final Throwable t) {
		super(serviceCode.getMessage(), t);
		this.serviceCode = serviceCode;
		this.detailedMsg = null;
	}

	/**
	 * 构造通用异常
	 * 
	 * @param detailedMessage 详细描述
	 * @param t               导火索
	 */
	public ServiceException(final String detailedMessage, final Throwable t) {
		super(detailedMessage, t);
		this.serviceCode = ServiceCodes.UNSPECIFIED;
		this.detailedMsg = detailedMessage;
	}

	/**
	 * 构造通用异常
	 * 
	 * @param serviceCode       错误码
	 * @param detailedMessage 详细描述
	 * @param t
	 */
	public ServiceException(final ServiceCodes serviceCode, final String detailedMessage, final Throwable t) {
		super(detailedMessage, t);
		this.serviceCode = serviceCode;
		this.detailedMsg = detailedMessage;
	}

	/**
	 * Getter method for property <tt>serviceCode</tt>.
	 *
	 * @return property value of serviceCode
	 */
	public ServiceCodes getErrorCode() {
		return serviceCode;
	}

}
