package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StatisticsGoodsReceiptVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -6637425481851991174L;
    private int countReceipt; // 入库数量
    private int sumReceiptTotal; // 入库金额
    /**
     * 入库均价
     */
    private double avgReceiptTotal;
}
