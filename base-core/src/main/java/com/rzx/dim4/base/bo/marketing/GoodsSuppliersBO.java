package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
@ApiModel(description = "供应商信息实体")
public class GoodsSuppliersBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    @ApiModelProperty(value = "供应商id")
    private String supplierId; // 供应商id

    @Length(message = "供应商名称不能超过个 {max} 字符！", max = 20)
    @ApiModelProperty(value = "供应商名称",required = true)
    private String supplierName; // 供应商名称

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确!")
    @ApiModelProperty(value = "供应商电话",required = true)
    private String supplierPhone; // 供应商电话

    @Min(value = 0,message = "供应周期参数错误;")
    @Max(value = 5,message = "供应周期必须小于6;")
    @ApiModelProperty(value = "供应周期")
    private int supplyCycle; // 供应周期，0现结，1挂账，2上打下，3月结，4季结，5结算方式

    @Max(value = 1000,message = "结算周期必须小于1000;")
    @ApiModelProperty(value = "结算周期")
    private int settlementCycle; // 结算周期(天)

    @Length(message = "供应商地址不能超过个 {max} 字符！", max = 100)
    @ApiModelProperty(value = "供应商地址")
    private String supplierAddr; // 供应商地址

    @Length(message = "备注不能超过个 {max} 字符！", max = 100)
    @ApiModelProperty(value = "备注")
    private String remark; // 备注

    @Length(message = "名片正面不能超过个 {max} 字符！", max = 200)
    @ApiModelProperty(value = "名片正面")
    private String frontBusinessCard; // 名片正面

    @Length(message = "名片反面不能超过个 {max} 字符！", max = 200)
    @ApiModelProperty(value = "名片反面")
    private String reverseBusinessCard; // 名片反面

    @ApiModelProperty(value = "供货品类")
    private int supplyCategory; //


}
