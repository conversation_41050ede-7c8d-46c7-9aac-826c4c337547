package com.rzx.dim4.base.utils;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleResultDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.function.CheckConsumer;
import com.rzx.dim4.base.function.ConvertFunction;
import com.rzx.dim4.base.response.GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 处理结果的工具类: 处理判断结果为空的情况，减少重复代码
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Slf4j
public class ResultHandleUtil {

    /**
     * 判断是否为异常
     */
    public static boolean isFailure(GenericResponse<?> response) {
        return response == null || !response.isResult();
    }

    /**
     * 判断是否为成功
     */
    public static boolean isSuccess(GenericResponse<?> response) {
        return !isFailure(response);
    }

    /**
     * 处理集合
     */
    public static <T> void handleCollection(Collection<T> collection, Consumer<Collection<T>> consumer) {
        if (CollectionUtils.isNotEmpty(collection) && consumer != null) {
            consumer.accept(collection);
        }
    }

    /**
     * 处理List
     */
    public static <T> void handleList(List<T> list, Consumer<List<T>> consumer) {
        if (CollectionUtils.isNotEmpty(list) && consumer != null) {
            consumer.accept(list);
        }
    }

    /**
     * 处理List
     */
    public static <T> void handleList(List<T> list, CheckConsumer checkConsumer) {
        if (CollectionUtils.isNotEmpty(list) && checkConsumer != null) {
            checkConsumer.handle();
        }
    }

    /**
     * List<T> 转换成 List<R>
     */
    public static <T, R> List<R> convertList(List<T> list, Function<List<T>, List<R>> function) {
        return CollectionUtils.isNotEmpty(list) && function != null ? function.apply(list) : new ArrayList<>();
    }

    /**
     * List<T> 转换成 List<R>
     */
    public static <T, R> List<R> convertList(List<T> list, ConvertFunction<List<R>> function) {
        return CollectionUtils.isNotEmpty(list) && function != null ? function.convert() : new ArrayList<>();
    }

    /**
     * 处理SimpleResultResponse结果
     */
    public static <T> void handleSimpleResultResponse(GenericResponse<SimpleResultDTO<T>> response, Consumer<T> consumer) {
        if (response != null && response.isResult()) {
            SimpleResultDTO<T> simpleResult = response.getData();
            if (simpleResult != null && simpleResult.getData() != null && consumer != null) {
                consumer.accept(simpleResult.getData());
            }
        }
    }

    /**
     * 处理ObjectResponse结果
     */
    public static <T extends AbstractBO> void handleObjectResponse(GenericResponse<ObjDTO<T>> response, Consumer<T> consumer) {
        if (response != null && response.isResult()) {
            ObjDTO<T> objDTO = response.getData();
            if (objDTO != null && objDTO.getObj() != null && consumer != null) {
                consumer.accept(objDTO.getObj());
            }
        }
    }

    /**
     * 处理ObjectResponse结果
     */
    public static <T extends AbstractBO> void handleObjectResponse(GenericResponse<ObjDTO<T>> response, Consumer<T> consumer, CheckConsumer otherConsumer) {
        if (response != null && response.isResult()) {
            ObjDTO<T> objDTO = response.getData();
            if (objDTO != null && objDTO.getObj() != null && consumer != null) {
                consumer.accept(objDTO.getObj());
            } else {
                otherConsumer.handle();
            }
        } else {
            otherConsumer.handle();
        }
    }

    /**
     * 从ObjectResponse结果中获取对象
     */
    public static <T extends AbstractBO> T getFromResponse(GenericResponse<ObjDTO<T>> response, ServiceCodes serviceCodes) {
        if (response == null || !response.isResult()) {
            log.info("GenericResponse.getFromResponse 1 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(serviceCodes == null ? ServiceCodes.OPT_ERROR : serviceCodes);
        }
        ObjDTO<T> objDTO = response.getData();
        if (objDTO == null || objDTO.getObj() == null) {
            log.info("GenericResponse.getFromResponse 2 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(serviceCodes == null ? ServiceCodes.OPT_ERROR : serviceCodes);
        }
        return objDTO.getObj();
    }

    /**
     * 从ObjectResponse结果中获取对象
     */
    public static <T extends AbstractBO> T getFromResponse(GenericResponse<ObjDTO<T>> response, String errorMessage) {
        if (response == null || !response.isResult()) {
            log.info("GenericResponse.getFromResponse 3 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(StringUtils.isEmpty(errorMessage) ? ServiceCodes.OPT_ERROR.getMessage() : errorMessage);
        }
        ObjDTO<T> objDTO = response.getData();
        if (objDTO == null || objDTO.getObj() == null) {
            log.info("GenericResponse.getFromResponse 4 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(StringUtils.isEmpty(errorMessage) ? ServiceCodes.OPT_ERROR.getMessage() : errorMessage);
        }
        return objDTO.getObj();
    }

    /**
     * 从ListDTOResponse结果中获取对象
     */
    public static <T extends AbstractBO> List<T> getListFromResponse(GenericResponse<ListDTO<T>> response, ServiceCodes serviceCodes) {
        if (response == null || !response.isResult()) {
            log.info("GenericResponse.getListFromResponse 1 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(serviceCodes == null ? ServiceCodes.OPT_ERROR : serviceCodes);
        }
        ListDTO<T> listDTO = response.getData();
        if (listDTO == null) {
            log.info("GenericResponse.getListFromResponse 2 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(serviceCodes == null ? ServiceCodes.OPT_ERROR : serviceCodes);
        }
        return CollectionUtils.isEmpty(listDTO.getList()) ? new ArrayList<>() : listDTO.getList();
    }

    /**
     * 从ListDTOResponse结果中获取对象
     */
    public static <T extends AbstractBO> List<T> getListFromResponse(GenericResponse<ListDTO<T>> response, String errorMessage) {
        if (response == null || !response.isResult()) {
            log.info("GenericResponse.getListFromResponse 3 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(StringUtils.isEmpty(errorMessage) ? ServiceCodes.OPT_ERROR.getMessage() : errorMessage);
        }
        ListDTO<T> listDTO = response.getData();
        if (listDTO == null) {
            log.info("GenericResponse.getListFromResponse 4 出现异常：{}", new Gson().toJson(response));
            throw new ServiceException(StringUtils.isEmpty(errorMessage) ? ServiceCodes.OPT_ERROR.getMessage() : errorMessage);
        }
        return CollectionUtils.isEmpty(listDTO.getList()) ? new ArrayList<>() : listDTO.getList();
    }

    /**
     * 处理ListDTOResponse结果
     */
    public static <T extends AbstractBO> void handleListResponse(GenericResponse<ListDTO<T>> response, Consumer<Collection<T>> consumer) {
        if (response != null && response.isResult()) {
            List<T> dataList = response.getData().getList();
            if (CollectionUtils.isNotEmpty(dataList) && consumer != null) {
                consumer.accept(dataList);
            }
        }
    }

    /**
     * 处理通用型Response结果
     */
    public static void handleResponse(GenericResponse<?> response, CheckConsumer consumer) {
        if (response != null && response.isResult() && response.getData() != null && response.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            consumer.handle();
        }
    }

    /**
     * 转换成ListDTOResponse结果
     */
    public static <T extends AbstractBO, R extends AbstractEntityBO> GenericResponse<ListDTO<R>> toListDTOResponse(GenericResponse<ListDTO<T>> response, Function<List<T>, List<R>> function) {
        if (response != null && response.isResult()) {
            List<T> dataList = response.getData().getList();
            if (CollectionUtils.isNotEmpty(dataList) && function != null) {
                return GenericResponse.toListDTOResponse(function.apply(dataList));
            }
        }
        return GenericResponse.toListDTOResponse(new ArrayList<>());
    }

    /**
     * List对象 转换成 ListDTOResponse结果
     */
    public static <T, R extends AbstractBO> GenericResponse<ListDTO<R>> toListDTOResponse(List<T> list, ConvertFunction<List<R>> function) {
        return GenericResponse.toListDTOResponse(CollectionUtils.isNotEmpty(list) && function != null ? function.convert() : new ArrayList<>());
    }

    /**
     * List对象 转换成 ListDTOResponse结果
     */
    public static <T, R extends AbstractBO> GenericResponse<ListDTO<R>> toListDTOResponse(List<T> list, Function<T, R> function) {
        if (CollectionUtils.isNotEmpty(list) && function != null) {
            return GenericResponse.toListDTOResponse(list.stream().map(function).collect(Collectors.toList()));
        }
        return GenericResponse.toListDTOResponse(new ArrayList<>());
    }

    /**
     * 转换成PagerDTOResponse结果
     */
    public static <T, R extends AbstractBO> GenericResponse<PagerDTO<R>> toPagerDTOResponse(Page<T> page, Function<List<T>, List<R>> function) {
        if (page == null || CollectionUtils.isEmpty(page.getContent()) || function == null) {
            return new GenericResponse<>(new PagerDTO<>(0, new ArrayList<>()));
        }

        return new GenericResponse<>(new PagerDTO<>((int) page.getTotalElements(), function.apply(page.getContent())));
    }

    /**
     * 转换成PagerDTOResponse结果
     */
    public static <T, R extends AbstractBO> GenericResponse<PagerDTO<R>> convertToPagerDTOResponse(Page<T> page, Function<T, R> function) {
        if (page == null || CollectionUtils.isEmpty(page.getContent()) || function == null) {
            return new GenericResponse<>(new PagerDTO<>(0, new ArrayList<>()));
        }

        List<R> list = page.getContent().stream().map(function).collect(Collectors.toList());
        return new GenericResponse<>(new PagerDTO<>((int) page.getTotalElements(), list));
    }

    /**
     * 处理分页结果
     */
    public static <T extends AbstractBO> void handlePageResponse(GenericResponse<PagerDTO<T>> response, Consumer<Collection<T>> consumer) {
        if (response != null && response.isResult() && consumer != null) {
            List<T> dataList = response.getData().getList();
            if (CollectionUtils.isNotEmpty(dataList)) {
                consumer.accept(dataList);
            }
        }
    }

    /**
     * 分割List，适合批量保存修改数据时候，分批处理
     */
    public static <T> List<List<T>> splitListToSubList(List<T> list, int size) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 只允许5-500
        size = Math.max(Math.min(size, 500), 5);

        List<List<T>> resultsList = new ArrayList<>();
        // 分批获取
        int pages = list.size() % size == 0 ? list.size() / size : list.size() / size + 1;
        int index = 0;
        for (int i = 0; i < pages - 1; i++) {
            index += size;
            resultsList.add(list.subList(i * size, (i + 1) * size));
        }
        if (index < list.size()) {
            resultsList.add(list.subList(index, list.size()));
        }

        return resultsList;
    }
}
