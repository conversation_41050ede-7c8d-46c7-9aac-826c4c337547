package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存信息表
 * <AUTHOR>
 * @date 2024年12月04日 13:33
 */
@Getter
@Setter
public class StorageGoodsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String goodsId; // 商品ID
    private String goodsTypeId; // 商品类型ID
    private String storageRackId; // 货架id，场所唯一，从100000开始递增,主仓库为000000
    private int goodsStocksNum; // 库存数量，最小值0
    private int stockAlarm; //预警值
    private int sort; //商品类型排序
    private String goodsName;
    private int unitPrice; //零售价 单位分
    private int unit; //单位
    private String goodsPic;
    private int costPrice;  //进货价，单位分
    private List<StorageRackInfo> StorageRackInfos;
    private int mainGoodsStocksNum; // 主仓库库存

    //业务字段
    private int sumGoodsStocksNum; //合计库存数
    private int sumCostPrice; //合计成品总计
    private int sumUniTprice; //合计毛利
    private int alarmGoodsCount;//告警商品数


    @Data
    public static class StorageRackInfo {
        private String storageRackId; // 货架id，场所唯一，从100000开始递增,主仓库为000000

        private String storageRackName; // 货架名称

        private String goodsId; // 商品ID

        private int goodsStocksNum; // 库存数量，最小值0
    }
}
