package com.rzx.dim4.base.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年04月16日 15:02
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimpleObjDTO extends AbstractDTO implements Serializable {

    private static final long serialVersionUID = -4329920667164004625L;

    Map result;
}
