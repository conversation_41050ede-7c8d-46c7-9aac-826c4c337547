package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.InviteApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年04月27日 16:32
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "InviteApi", fallback = InviteApiHystrix.class)
public interface InviteApi {

    String URL = "/feign/billing/invite";


    @GetMapping(URL + "/config/findByPlaceId")
    GenericResponse<ObjDTO<InviteConfigBO>> findByPlaceId(@RequestParam String placeId);

    @PostMapping(URL + "/config/save")
    GenericResponse<ObjDTO<InviteConfigBO>> save(@RequestBody InviteConfigBO inviteConfigBO);

    @PostMapping(URL + "/inviteOnline/add")
    GenericResponse<ObjDTO<InviteOnlineBO>> addInviteOnline(@RequestBody InviteOnlineBO inviteOnlineBO);

    @PostMapping(URL + "/inviteOnline/join")
    GenericResponse<ObjDTO<InviteOnlineBO>> joinInviteOnline(@RequestBody InviteOnlineBO inviteOnlineBO);

    @GetMapping(URL + "/inviteOnline/getInviteOnlineDetail")
    GenericResponse<ObjDTO<InviteOnlineBO>> getInviteOnlineDetail(@RequestParam String placeId,@RequestParam String inviteCode);

    @GetMapping(URL + "/inviteOnline/findByPlaceIdAndCardId")
    GenericResponse<ObjDTO<InviteOnlineBO>> findByPlaceIdAndCardId(@RequestParam String placeId,@RequestParam String cardId);

    @GetMapping(URL + "/inviteOnline/findInviteOnline")
    GenericResponse<ObjDTO<InviteOnlineBO>> findInviteOnline(@RequestParam String placeId,@RequestParam String idNumber);

    @GetMapping(URL + "/inviteOnline/queryInviteOnliePage")
    GenericResponse<PagerDTO<InviteOnlineBO>> queryBillingCardPage(@RequestParam(value = "placeId") String placeId,
                                                                   @RequestParam(value = "type",required = false) String type,
                                                                   @RequestParam(value = "idNumber",required = false) String idNumber,
                                                                   @RequestParam(value = "startTime",required = false) String startTime,
                                                                   @RequestParam(value = "endTime",required = false) String endTime,
                                                                   @RequestParam(value = "start", defaultValue = "0") int start,
                                                                   @RequestParam(value = "length", defaultValue = "10") int length,
                                                                   Pageable pageable);

    @GetMapping(URL + "/inviteOnline/queryInviteOnlineDetail")
    GenericResponse<ListDTO<BalanceDetailsBO>> queryInviteOnlineDetail(@RequestParam String placeId,@RequestParam String inviteCode);

    @GetMapping(URL + "/inviteOnline/delete")
    GenericResponse<SimpleDTO> deleteById(@RequestParam String placeId, @RequestParam Long id);

    @GetMapping(URL + "/inviteOnline/findUnderwayInviteOnline")
    GenericResponse<ObjDTO<InviteOnlineBO>> findUnderwayInviteOnline(@RequestParam String placeId,@RequestParam String idNumber,@RequestParam String inviteCode);
}
