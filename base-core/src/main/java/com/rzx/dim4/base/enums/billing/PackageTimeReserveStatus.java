package com.rzx.dim4.base.enums.billing;

import lombok.Getter;

/**
 * PackageTimeReserve记录的状态
 */
@Getter
public enum PackageTimeReserveStatus {

    NOT_USED(0, "0", "预包时，未使用的状态"),
    USING(1, "1", "正在使用"),
    USED(2, "2", "已使用 或 已失效 或 已退款"),
    ;

    /**
     * 操作类型，保存到数据库
     */
    private final int code;
    /**
     * 操作类型，方便作为查询条件放到Map中
     */
    private final String type;
    /**
     * 操作类型描述
     */
    private final String desc;

    PackageTimeReserveStatus(int code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
}
