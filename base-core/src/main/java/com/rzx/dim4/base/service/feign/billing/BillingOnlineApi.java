package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BillingOnlineBO;
import com.rzx.dim4.base.bo.billing.MiniAppBillingOnlineBO;
import com.rzx.dim4.base.bo.billing.third.OnlineBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.*;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingOnlineApiHystrix;
import com.rzx.dim4.base.service.feign.billing.param.BillingOnlineParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 在线用户
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "billingOnlineApi", fallback = BillingOnlineApiHystrix.class)
public interface BillingOnlineApi {

    String URL = "/feign/billing/online";

    /**
     * 获取在线用户
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/queryBillingOnlineByPlaceId")
    GenericResponse<ListDTO<BillingOnlineBO>> queryBillingOnlineByPlaceId(@RequestParam("placeId") String placeId);

    /**
     * 当前场所是否有在线用户
     *
     * @param placeId 场所id
     * @return true 有 false 没有
     */
    @GetMapping(URL + "haveOnlineUser")
    GenericResponse<SimpleDTO> haveOnlineUser(@RequestHeader(value = "request_ticket") String requestTicket,
                                              @RequestParam("placeId") String placeId);

    /**
     * 分页查询在线用户
     *
     * @param billingOnlineParam
     * @param pageable
     * @return
     */
    @GetMapping(URL + "/queryBillingOnlinePage")
    GenericResponse<PagerDTO<BillingOnlineBO>> queryBillingOnlinePage(@SpringQueryMap BillingOnlineParam billingOnlineParam, Pageable pageable);


    @GetMapping(URL + "/queryBillingOnline")
    GenericResponse<ListDTO<BillingOnlineBO>> queryBillingOnline(@SpringQueryMap BillingOnlineParam billingOnlineParam);


    @PostMapping(URL + "/findBillingPager")
    GenericResponse<PagerDTO<BillingOnlineBO>> findBillingPager(@RequestBody Map<String, Object> queryMap,
                                                                @RequestParam(name = "page", defaultValue = "0") int page,
                                                                @RequestParam(name = "size", defaultValue = "10") int size,
                                                                @RequestParam(name = "order", defaultValue = "desc") String order,
                                                                @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    /**
     * 查询最后一次上机的记录
     *
     * @param idNumber
     * @return
     */
    @GetMapping(URL + "/queryLastOnlinePlaceByIdNumber")
    GenericResponse<ObjDTO<BillingOnlineBO>> queryLastOnlinePlaceByIdNumber(@RequestParam("idNumber") String idNumber);

    @GetMapping(URL + "/queryOnlineInfo")
    GenericResponse<ObjDTO<OnlineBO>> queryOnlineInfo(@RequestParam("placeId") String placeId, @RequestParam("idNumber") String idNumber);


    @GetMapping(URL + "/findUserOnlineInfo")
    public GenericResponse<ObjDTO<OnlineBO>> findUserOnlineInfo(@RequestParam("placeId") String placeId, @RequestParam("idNumber") String idNumber);

    /**
     * 小程序获取在线用户(分页)
     *
     * @param placeId
     * @param areaId
     * @param search
     * @return
     */
    @GetMapping(URL + "/queryMiniAppBillingOnlineByPlaceId")
    GenericResponse<PagerDTO<MiniAppBillingOnlineBO>> queryMiniAppBillingOnlineByPlaceId(@RequestParam("placeId") String placeId,
                                                                                         @RequestParam(value = "areaId", required = false) String areaId,
                                                                                         @RequestParam(value = "search",required = false) String search,
                                                                                         @RequestParam(value = "packageFlag", required = false) String packageFlag,
                                                                                         @RequestParam(name = "page") int page,
                                                                                         @RequestParam(name = "size") int size);


    /**
     * 小程序获取在线用户(列表)
     *
     * @param placeId
     * @param areaId
     * @param search
     * @return
     */
    @GetMapping(URL + "/queryMiniAppBillingOnlineByCondition")
    GenericResponse<ListDTO<MiniAppBillingOnlineBO>> queryMiniAppBillingOnlineByCondition(@RequestParam("placeId") String placeId,
                                                                                          @RequestParam(value = "areaId", required = false) String areaId,
                                                                                          @RequestParam(value = "search", required = false) String search,
                                                                                          @RequestParam(value = "packageFlag", required = false) String packageFlag);



    /**
     * 根据会员身份证号模糊查询会员信息(根据机器号，身份证号)
     *
     * @param placeId
     * @param clientId
     * @param idNumber
     * @return
     */
    @GetMapping(URL + "/queryBillingOnlineUserByPlaceIdAndIdNumber")
    GenericResponse<ObjDTO<MiniAppBillingOnlineBO>> queryBillingOnlineUserByPlaceIdAndIdNumber(@RequestParam("placeId") String placeId,
                                                                                    @RequestParam(value = "clientId",required = false) String clientId,
                                                                                    @RequestParam(value = "idNumber",required = false) String idNumber);


}
