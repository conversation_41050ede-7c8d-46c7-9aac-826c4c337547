package com.rzx.dim4.base.service.feign.goods.api;

import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.shop.CartGoodsBO;
import com.rzx.dim4.base.bo.shop.OrdersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单web feign接口
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "ordersApi", fallback= ShopServerServiceHystrix.class)
public interface OrdersApi {

    @PostMapping(value = "/shop/api/orders/getOrder")
    GenericResponse<ObjDTO<OrdersBO>> getOrder(@RequestHeader("request_ticket") String requestTicket,
                                               @RequestParam String orderId,
                                               @RequestParam String placeId);


    @PostMapping(value = "/shop/api/orders/createShopOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> createShopOrder(@RequestHeader("request_ticket") String requestTicket,
                                                             @RequestBody List<CartGoodsBO> cartGoodsBos, @RequestParam String placeId,
                                                             @RequestParam(required = false)String clientName, @RequestParam(required = false)String clientId,
                                                             @RequestParam SourceType sourceType, @RequestParam PayType payType, @RequestParam String openId, @RequestParam String returnUrl,
                                                             @RequestParam String idName, @RequestParam String idNumber, @RequestParam(required = false) String cardId, @RequestParam(required = false,defaultValue = "") String remark);

    @GetMapping(value = "/shop/api/orders/queryPayOrder")
    GenericResponse<ObjDTO<PaymentOrderBO>> queryPayOrder(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String orderId);

    @GetMapping(value = "/shop/api/orders/queryOrderList")
    GenericResponse<PagerDTO<OrdersBO>> queryOrderList(@RequestHeader("request_ticket") String requestTicket,
                                                       @RequestParam String placeId,
                                                       @RequestParam String idNumber,
                                                       @RequestParam int page,
                                                       @RequestParam int size);
}
