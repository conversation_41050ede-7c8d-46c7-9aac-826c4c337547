package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
@Setter
@Getter
public class StaticsWordOfMouthVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -4353903812250571471L;
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 销售量
     */
    private int countSale;
    /**
     * 退货量
     */
    private int countRefund;
    /**
     * 退货率
     */
    private double refundRate;
    /**
     * 当前评分
     */
    private int score;
    /**
     * 评论次数
     */
    private long countComment;
}
