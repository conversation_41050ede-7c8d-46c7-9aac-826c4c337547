package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.BookSeatsBO;
import com.rzx.dim4.base.bo.billing.BookSeatsConfigBO;
import com.rzx.dim4.base.bo.billing.BookSeatsCustomerContextBO;
import com.rzx.dim4.base.bo.billing.BookSeatsQueryBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BookSeatsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/12
 **/
@Slf4j
@Service
public class BookSeatsApiHystrix implements BookSeatsApi {
    /**
     * 用户待订座页面信息
     *
     * @param requestTicket 请求防止重复ticket
     * @param placeId       场所id
     * @param idNumber      证件号
     * @return 场所座位订座信息
     */
    @Override
    public GenericResponse<ObjDTO<BookSeatsCustomerContextBO>> customerContext(String requestTicket, String placeId, String idNumber) {
        log.error("接口异常:::customerContext(requestTicket:::{},placeId:::{},idNumber:::{})", requestTicket, placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 用户执行订座
     *
     * @param requestTicket 请求防止重复ticket
     * @param bookSeatsBO   订座信息（必填字段：placeId、idNumber、clientIds、areaId、roomFlag、
     *                      duration、ruleId、price、estimatedCost）
     * @return 订座信息
     */
    @Override
    public GenericResponse<ObjDTO<BookSeatsBO>> customerCreate(String requestTicket, BookSeatsBO bookSeatsBO) {
        log.error("接口异常:::customerCreate(requestTicket:::{},bookSeatsBO:::{})", requestTicket, bookSeatsBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 用户取消订座
     *
     * @param requestTicket 请求防止重复ticket
     * @param bookSeatsId   订座记录id
     * @param operator      操作人id(billingCard.id)
     * @return 是否成功
     */
    @Override
    public GenericResponse<?> customerCancel(String requestTicket, long bookSeatsId, long operator) {
        log.error("接口异常:::customerCancel(requestTicket:::{},bookSeatsId:::{},operator:::{})", requestTicket, bookSeatsId, operator);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 用户端获取订座列表
     *
     * @param requestTicket 请求防止重复ticket
     * @param queryBO       订座列表筛选条件(placeId、idNumber 必填)
     * @return 订座列表
     */
    @Override
    public GenericResponse<PagerDTO<BookSeatsBO>> customerGetList(String requestTicket, BookSeatsQueryBO queryBO) {
        log.error("接口异常:::customerGetList(requestTicket:::{},queryBO:::{})", requestTicket, queryBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BookSeatsConfigBO>> getConfig(String requestTicket, String placeId) {
        log.error("接口异常:::getConfig(requestTicket:::{},placeId:::{})", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> updateConfig(String requestTicket, BookSeatsConfigBO configBO) {
        log.error("接口异常:::updateConfig(requestTicket:::{},configBO:::{})", requestTicket, configBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
