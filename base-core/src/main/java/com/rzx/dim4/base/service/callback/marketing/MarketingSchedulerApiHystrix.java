package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingSchedulerApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> hwx
 * @since 2025/2/6 15:08
 */
@Component
@Slf4j
public class MarketingSchedulerApiHystrix implements MarketingSchedulerApi {

    @Override
    @GetMapping("/marketing/scheduler/statisticsCostOfGoodsSale")
    public void statisticsCostOfGoodsSale(@RequestHeader(value = "request_ticket") String requestTicket) {
        log.error("接口异常，MarketingSchedulerApiHystrix.statisticsCostOfGoodsSale");
    }

    @Override
    @GetMapping("/marketing/scheduler/goodsStatistics")
    public void goodsStatistics(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam(required = false, defaultValue = "1") int dayAgo) {
        log.error("接口异常，MarketingSchedulerApiHystrix.goodsStatistics,dayAgo:{}",dayAgo);
    }
}
