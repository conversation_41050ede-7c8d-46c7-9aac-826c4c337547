package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.service.callback.marketing.MarketingSchedulerApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 优惠券统计定时器接口
 * <AUTHOR> hwx
 * @Description TODO
 * @Date 2025/2/6 15:03
 */
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingScheduler", fallback = MarketingSchedulerApiHystrix.class)
public interface MarketingSchedulerApi {

    /**
     * 定时统计商超商品销售成本数据
     */
    @GetMapping("/feign/marketing/scheduler/statisticsCostOfGoodsSale")
    void statisticsCostOfGoodsSale(@RequestHeader(value = "request_ticket") String requestTicket);

    /**
     * 定时统计商超数据
     * @param dayAgo
     */
    @GetMapping("/feign/marketing/scheduler/goodsStatistics")
    void goodsStatistics(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam(required = false, defaultValue = "1") int dayAgo);
}
