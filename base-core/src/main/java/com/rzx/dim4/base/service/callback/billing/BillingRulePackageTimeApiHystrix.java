package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingRulePackageTimeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年08月15日 17:06
 */
@Slf4j
@Service
public class BillingRulePackageTimeApiHystrix implements BillingRulePackageTimeApi {
    @Override
    public GenericResponse<ListDTO<BillingRulePackageTimeBO>> findByRuleIds(String placeId, List<String> ruleIds) {

        log.error("接口异常:::BillingRulePackageTimeApiHystrixindByRuleIds(placeId:::{},ruleIds:::{})", placeId, ruleIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
    @Override
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> findByPlaceIdAndRuleId(String placeId, String ruleId) {

        log.error("接口异常:::findByPlaceIdAndRuleId(placeId:::{},ruleId:::{})", placeId, ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> balancePackageTime(String requestTicket, String placeId, String cardId, String ruleId, String sourceTypeStr) {
        log.error("接口异常:::balancePackageTime(requestTicket:::{},placeId:::{},cardId:::{},ruleId:::{},sourceTypeStr:::{})", requestTicket, placeId, cardId, ruleId, sourceTypeStr);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> OnlinePackageTime(String requestTicket, String placeId, String cardId, String ruleId, String sourceTypeStr,String payCode) {
        log.error("接口异常:::OnlinePackageTime(requestTicket:::{}, placeId:::{}, cardId:::{}, ruleId:::{}, sourceTypeStr:::{}, payCode:::{})", requestTicket, placeId, cardId, ruleId, sourceTypeStr,payCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> couponPackageTime (String requestTicket, String placeId, String cardId, String ruleId, String sourceTypeStr, String shiftId, String couponName,String couponTypeStr) {
        log.error("接口异常:::couponPackageTime(requestTicket:::{}, placeId:::{}, cardId:::{}, ruleId:::{}, sourceTypeStr:::{}, shiftId:::{}, couponName:::{},couponTypeStr:::{})", requestTicket, placeId, cardId, ruleId, sourceTypeStr, shiftId, couponName,couponTypeStr);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updatePackageTimeStatus(String requestTicket, String placeId, String ruleId, String forbidden) {
        log.error("接口异常:::OnlinePackageTime(updatePackageTimeStatus:::{}, placeId:::{}, ruleId:::{}, forbidden:::{})", requestTicket, placeId, ruleId, forbidden);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> findPackageTimeBuyCount(String placeId, String cardId, String ruleId) {
        log.error("接口异常:::findPackageTimeBuyCount(placeId:::{}, cardId:::{}, ruleId:::{})", placeId, cardId, ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
