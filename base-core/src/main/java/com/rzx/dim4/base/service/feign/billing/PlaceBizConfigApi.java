package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.PlaceBizConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceBizConfigApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/12
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "PlaceBizConfigApi", fallback = PlaceBizConfigApiHystrix.class)
public interface PlaceBizConfigApi {

    String URL = "/feign/placeBizConfig";

    @GetMapping(URL + "/query")
    GenericResponse<ObjDTO<PlaceBizConfigBO<Float>>> query(@RequestParam String placeId);

    @PostMapping(URL + "/save")
    GenericResponse<SimpleDTO> save(@RequestHeader(value = "request_ticket") String requestTicket,
                                                  @RequestBody PlaceBizConfigBO<Float> placeBizConfigBO);


    @PostMapping(URL + "/save/antiAddiction")
    GenericResponse<?> saveAntiAddiction(@RequestHeader(value = "request_ticket") String requestTicket,
                                         @RequestBody PlaceBizConfigBO<Float> placeBizConfigBO);

    @PostMapping(URL + "/save/closeBizConfigTempCardPointsUpgrade")
    GenericResponse<?> closeBizConfigTempCardPointsUpgrade(@RequestHeader(value = "request_ticket") String requestTicket,@RequestParam String placeId
            ,@RequestParam int upgradeUserLevelFlag,@RequestParam int downgradeUserLevelFlag);

    @PostMapping(URL + "/queryByPlaceIds")
    GenericResponse<ListDTO<PlaceBizConfigBO>> queryByPlaceIds(@RequestBody List<String> placeIds);

    @GetMapping(URL + "/queryByThirdAccountIds")
    GenericResponse<ListDTO<PlaceBizConfigBO>> queryByThirdAccountIds(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                      @RequestParam List<String> thirdAccountIds);
}
