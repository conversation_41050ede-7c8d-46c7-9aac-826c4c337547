package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.enums.marketing.ClientMatterType;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 事物tips vo
 * <AUTHOR> hwx
 * @since 2025/3/5 10:39
 */
@Setter
@Getter
public class ClientMatterTipsVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -605598641021926254L;
    private String placeId; // 场所ID

    /**
     * 事物ID
     */
    private String matterId;

    /**
     * 事物类型
     */
    private ClientMatterType matterType;

    /**
     * 客户端id
     */
    private String clientId;

    private String clientName; // 客户端名称

    private String idNumber; // 身份证号

    private String refId; // 下单时关联订单id
    /**
     * 内容
     */
    private String content;

    private LocalDateTime created; // 账号创建时间
}
