package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BillingCardBriefBO;
import com.rzx.dim4.base.bo.billing.BillingCardVO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeeSearchBO;
import com.rzx.dim4.base.dto.*;
import com.rzx.dim4.base.dto.billiing.BillingCardUserDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingCardApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/30
 **/
@Slf4j
@Service
public class BillingCardApiHystrix implements BillingCardApi {
    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndIdNumbers(String placeId, List<String> idNumbers) {
        log.error("接口异常:::findByPlaceIdAndIdNumbers(placeId::::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> sendInternetFeeForMiniApp(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                @RequestParam String placeId,
                                                                @RequestParam String cardId,
                                                                @RequestParam int presentAccount) {
        log.error("接口异常:::sendInternetFeeForMiniApp(placeId:::{},cardId:::{},presentAccount:::{})", placeId, cardId, presentAccount);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardBO>> queryBillingCardPage(Map<String, Object> queryMap, Pageable pageable) {
        log.error("接口异常:::queryBillingCardPage(queryMap:::{},pageable:::{})", queryMap, pageable);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardBO>> queryBillingCardPageForMiniApp(@RequestBody InternetFeeSearchBO paramsBo) {
        log.error("接口异常:::queryBillingCardPageForMiniApp(Params:::{})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardTypeId(String placeId, String cardTypeId) {
        log.error("接口异常:::findByPlaceIdAndCardTypeId(placeId:::{},cardTypeId:::{})", placeId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> removeUnderfundedCard(String placeId, String cardId) {
        log.error("接口异常:::removeUnderfundedCard(placeId:::{},cardId:::{})", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> removeChainCard(String requestTicket, List<String> placeIds, List<String> idNumbers) {
        log.error("接口异常:::removeChainCard(placeIds:::{},idNumbers:::{})", placeIds, idNumbers);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> exitChain(String requestTicket, String placeId) {
        log.error("接口异常:::exitChain(requestTicket:::{},placeId:::{})", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findByChainIdAndIdNumber(String chainId, String idNumber) {
        log.error("接口异常:::findByChainIdAndIdNumber(chainId:::{},idNumber:::{})", chainId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> activation(String requestTicket, String placeId, String idNumber, int activeType) {
        log.error("接口异常:::active(placeId:::{},idNumber:::{},activeType:::{})", placeId, idNumber, activeType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> newActivation(String requestTicket, String placeId, String idNumber, String name, int activeType, String phoneNumber) {
        log.error("接口异常:::active(placeId:::{},idNumber:::{},name:::{},activeType:::{})", placeId, idNumber, name, activeType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> iotCreateBillingCard(String requestTicket, String idNumber,
                                                                       String name, String cardTypeId, String placeId, String activeType, String cashierId, String mobile) {
        log.error("接口异常:::iotCreateBillingCard(placeId:::{},idNumber:::{},name:::{},activeType:::{},cardTypeId:::{},cashierId:::{},mobile:::{})", placeId, idNumber, name, activeType, cardTypeId, cashierId, mobile);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> iotCreateCardWithAmount(String requestTicket, String placeId,
                                                                            String cardTypeId, String idNumber, String name, int amount, String cashierId, String payType,
                                                                            String payCode, String mobile) {
        log.error("接口异常:::iotCreateBillingCard(placeId:::{},idNumber:::{},name:::{},cardTypeId:::{},cashierId:::{},payType:::{},payCode:::{},mobile:::{})",
                placeId, idNumber, name, cardTypeId, cashierId, payType, payCode, mobile);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> savePolling4CreateCard(String requestTicket, String placeId, String idNumber,
                                                     String cashierId, String phoneNumber) {
        log.error("接口异常:::savePolling4CreateCard(placeId:::{},idNumber:::{},cashierId:::{},phoneNumber:::{})",
                placeId, idNumber, cashierId, phoneNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> findCardAndCommonRule(String placeId, String areaId, String cardId) {
        log.error("接口异常:::findCardAndCommonRule(placeId:::{},cardId:::{},areaId:::{})", placeId, cardId, areaId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findCardByPlaceIdAndLikeIdNumber(String placeId, String idNumber) {
        log.error("接口异常:::findCardByPlaceIdAndLikeIdNumber(placeId:::{},idNumber::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> findCardByPlaceIdAndLikeIdNumberForMiniApp(String placeId, String idNumber) {
        log.error("接口异常:::findCardByPlaceIdAndLikeIdNumberForMiniApp(placeId:::{},idNumber::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<BillingCardVO>> queryPageMember(List<String> params) {
        log.error("接口异常:::queryPageMember " + new Gson().toJson(params));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCard(String placeId, String cardId, String remark) {
        log.error("接口异常:::modifyBillingCard(placeId:::{},cardId:::{},remark:::{})",
                placeId, cardId, remark);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCardType(String placeId, String cardId, String cardTypeId) {
        log.error("接口异常:::modifyBillingCardType(placeId:::{},cardId:::{},cardTypeId:::{})",
                placeId, cardId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBriefBO>> findByPlaceIdAndCardId(String placeId, String cardId) {
        log.error("接口异常:::findByPlaceIdAndCardId(placeId:::{},cardId:::{})", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> modifyBillingCardTypeForCashierMember(String requestTicket, String placeId, String cardId, String cardTypeId, String accountId) {
        log.error("接口异常:::modifyBillingCardTypeForCashierMember(requestTicket:::{},placeId:::{},cardId:::{},cardTypeId:::{},accountId:::{})", requestTicket,
                placeId, cardId, cardTypeId, accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


    @Override
    public GenericResponse<SimpleDTO> updatePhoneNumber(String requestTicket, String placeId, String cardId, String phoneNumber) {
        log.error("接口异常:::updatePhoneNumber(placeId:::{},cardId:::{},phoneNumber:::{})", placeId, cardId, phoneNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> cleanPresentAmount(String requestTicket, String placeId, String cardId) {
        log.error("接口异常:::cleanPresentAmount(placeId:::{},cardId:::{})", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateLoginPass(String requestTicket, String placeId, String cardId, String loginPass) {
        log.error("接口异常:::updateLoginPass(placeId:::{},cardId:::{},loginPass:::{})", placeId, cardId, loginPass);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> activeCardList(String placeId, String idNumber) {
        log.error("接口异常:::activeCardPage(placeId:::{},idNumber:::{})", placeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


    @Override
    public GenericResponse<SimpleDTO> updateBillingCardType(String requestTicket, String placeId, String cardId, String cardTypeId) {
        log.error("接口异常:::updateBillingCardType(placeId:::{},cardId:::{},cardTypeId:::{})", placeId, cardId, cardTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardIds(String placeId, List<String> cardIds) {
        log.error("接口异常:::findByPlaceIdAndCardIds(placeId:::{},cardIds:::{})", placeId, cardIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<BillingCardBriefBO>> findByIdNumbers(List<String> idNumbers) {
        log.error("接口异常:::findByIdNumbers(idNumbers:::{})", idNumbers);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
