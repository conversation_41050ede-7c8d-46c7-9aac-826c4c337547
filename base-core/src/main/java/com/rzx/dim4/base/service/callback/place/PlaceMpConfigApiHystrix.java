package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceMpConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceMpConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年07月22日 14:51
 */
@Slf4j
@Service
public class PlaceMpConfigApiHystrix implements PlaceMpConfigApi {

    @Override
    public GenericResponse<ObjDTO<PlaceMpConfigBO>> findByPlaceId(String placeId) {
        log.error("接口异常，findByPlaceId(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
