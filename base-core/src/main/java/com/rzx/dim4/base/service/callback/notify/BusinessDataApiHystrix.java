package com.rzx.dim4.base.service.callback.notify;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.notify.polling.BookSeatsBusinessBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.notify.BusinessDataApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/11
 **/
@Slf4j
@Service
public class BusinessDataApiHystrix implements BusinessDataApi {
    @Override
    public GenericResponse<ObjDTO<BookSeatsBusinessBO>> pushBookSeats(String requestTicket,
                                                                      BookSeatsBusinessBO bookSeatsBusinessBO) {
        log.error("接口异常:::pushBookSeats():::bookSeatsBusinessBO:{}", new Gson().to<PERSON>son(bookSeatsBusinessBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
