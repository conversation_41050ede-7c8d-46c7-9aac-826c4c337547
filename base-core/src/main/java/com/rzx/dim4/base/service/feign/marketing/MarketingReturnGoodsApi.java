package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.ReturnGoodsBO;
import com.rzx.dim4.base.bo.marketing.ReturnGoodsRecordRequestBo;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingReturnGoodsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingReturnGoodsApi", fallback = MarketingReturnGoodsApiHystrix.class)
public interface MarketingReturnGoodsApi {
    String URL = "/feign/marketing/returnGoods";

    @PostMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<ReturnGoodsBO>> findPageList(@RequestBody ReturnGoodsRecordRequestBo paramsBo);

    @PostMapping(URL + "/save")
    GenericResponse<ObjDTO<ReturnGoodsBO>> saveReturnGoods(@RequestBody ReturnGoodsBO returnGoodsBO);


    @GetMapping(URL + "/findDetails")
    GenericResponse<ObjDTO<ReturnGoodsBO>> findReturnGoodsDetail(@RequestParam String placeId, @RequestParam String returnGoodsNo);
}
