package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 订单商品tips VO
 */
@Data
public class OrderGoodsTipsVO {
	private String placeId; // 场所ID
	private String orderId; // 订单ID
	private String goodsId; // 商品ID
	private String goodsName; // 商品名称
	private String goodsTypeId; // 商品类型ID
	private String goodsTypeName; // 商品类型名称
	private int discounts; // 折扣金额 = 商品原价 - 商品单价
	private int quantity; // 商品数量
	private String goodsPic; // 商品图片
	private int status;// 商品订单状态，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消
	private int unitPrice; // 商品单价
	private int goodsCategory; // 商品种类，0固装商品，1虚拟商品，2自制商品(默认0),3优惠券商品
	private int present; // 是否是赠送，0否，1是
}
