package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.MarketDouyinStoreBO;
import com.rzx.dim4.base.bo.marketing.MeituanTokenBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingThirdPartyStoreApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024年08月12日 15:24
 */
@Slf4j
@Component
public class MarketThirdPartyStoreApiHystrix implements MarketingThirdPartyStoreApi {
    @Override
    public GenericResponse<ListDTO<MarketDouyinStoreBO>> goodLifeQuery(String placeId, String poiId, String accountId) {
        log.error("接口异常，goodLifeQuery,placeId={},poiId={},accountId={}", placeId, poiId, accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveMarketDouyinStore(String requestTicket, MarketDouyinStoreBO marketDouyinStoreBO) {
        log.error("接口异常，saveMarketDouyinStore,requestTicket={},marketDouyinStoreBO={}", requestTicket, marketDouyinStoreBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MarketDouyinStoreBO>> queryDouYinStoreByPlaceId(String placeId) {
        log.error("接口异常，saveMarketDouyinStore,placeId={}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MeituanTokenBO>> queryMeituanStoreByPlaceId(String placeId) {
        log.error("接口异常，queryMeituanStoreByPlaceId,placeId={}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
