package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "member_card_config")
@XmlAccessorType(XmlAccessType.NONE)
public class PlaceAccountXml {

    @XmlElement(name = "IsDisable")
    protected int deleted;

    @XmlElement(name = "user_name")
    private String accountName; // 姓名

}
