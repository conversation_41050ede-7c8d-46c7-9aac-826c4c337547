package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "rates")
@Getter
@Setter
public class AllRatesXml {

    /**
     * 基础费率集合
     */
    @XmlElementWrapper(name = "std_rates")
    @XmlElement(name = "std_rate")
    private List<BillingRuleCommonXml> billingRuleCommonXml;
    /**
     * 自动包时段
     */
    @XmlElementWrapper(name = "rate_autos")
    @XmlElement(name = "rate_auto")
    private List<BillingRuleAccXml> billingRuleAccXml;
    /**
     * 固定包时段
     */
    @XmlElementWrapper(name = "rate_fixs")
    @XmlElement(name = "rate_fix")
    private List<BillingRulePackageXml> billingRulePackageXml;

    /**
     * 充值奖励
     */
    @XmlElementWrapper(name = "charge_rewards")
    @XmlElement(name = "charge_reward")
    private List<TopupRuleXml> topupRuleXml;

}
