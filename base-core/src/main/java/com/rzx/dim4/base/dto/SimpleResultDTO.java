package com.rzx.dim4.base.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * JPA SQL查询结果DTO
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SimpleResultDTO<T> extends AbstractDTO implements Serializable {
    private static final long serialVersionUID = -4329920667164004625L;

    private T data;
}
