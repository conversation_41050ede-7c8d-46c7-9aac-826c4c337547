package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单 信息表
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Getter
@Setter
public class OrdersMiniAppBO extends AbstractEntityBO {
    private Long id;

    private String placeId; // 场所ID
    private String orderId; // 订单ID
    private LocalDateTime payTime; // 订单支付时间
    private LocalDateTime finishedTime; // 订单完成时间
    private SourceType sourceType; // 来源
    private int totalMoney; // 订单合计价格
    private int realMoney; // 订单实际金额（优惠后-实际付款金额）

    private PayType payType; // 支付方式

    private String remark; // 订单备注

    private String cardId; // 计费卡ID
    private String idNumber; // 身份证号码
    private String idName; // 身份证姓名
    private int status;// 查看OrderPayStatus，订单状态，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消
    private int packageTimeStatus;


    private int isMeals; //是否套餐 0否 1是
    private int orderType;// 订单类型1 商品订单，2团购订单，3网费充值订单，4包时订单，9自定义收款
    private String ruleId; // 包时规则id
    private String cardTypeId;
    private String cardTypeName;

    private int totalAccount;

    private Long creater;
    private String createrName; // 操作人姓名
    private LocalDateTime created; // 账号创建时间

    private String packageName;
    private String hostName;
    private String headImageUrl;

    private List<InternetFeeMiniAppBO> internetGifts = new ArrayList<>();
}
