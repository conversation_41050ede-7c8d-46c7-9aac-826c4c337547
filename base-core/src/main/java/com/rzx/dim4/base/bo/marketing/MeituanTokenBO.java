package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年04月07日 18:17
 */

@Getter
@Setter
@ToString
public class MeituanTokenBO  extends AbstractEntityBO {

    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;


    private String placeId; // 场所ID
    private LocalDateTime expired;   //过期时间
    private int expiresIn;      //失效时间，单位s
    private int businessId;      //业务编码
    private String opBizCode; // 授权实体的唯一标识
    private String opBizName; // 授权实体名称（如果授权实体是门店则为门店名）
}
