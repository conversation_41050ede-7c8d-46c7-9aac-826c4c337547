package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.bo.billing.LogOtherIncomeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceChainLogOtherIncomeHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年06月12日 15:47
 * 连锁交班记录相关接口
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "placeChainLogOtherIncomeApi", fallback = PlaceChainLogOtherIncomeHystrix.class)
public interface PlaceChainLogShiftApi {

    String URL = "/feign/billing/place/chain/logShift";

    @PostMapping(URL + "/queryLogOtherIncome")
    public GenericResponse<PagerDTO<LogOtherIncomeBO>> queryLogOtherIncome(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody Map<String, String> queryMap,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping(URL + "/queryLogLogin")
    public GenericResponse<ListDTO<LogLoginBO>> queryLogLogin(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam("placeId")String placeId,
            @RequestBody List<String> loginIds);
}
