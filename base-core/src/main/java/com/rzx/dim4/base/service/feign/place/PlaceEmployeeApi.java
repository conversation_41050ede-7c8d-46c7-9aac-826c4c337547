package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceEmployeeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * place-server 的 PlaceAccount feign 接口
 *
 * <AUTHOR>
 * @apiNote header 参数 requestTicket 必填
 * @since 2023/8/31
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "EmployeeApi", fallback = PlaceEmployeeApiHystrix.class)
public interface PlaceEmployeeApi {

    /**
     * 请求前缀，用于区分不同的接口，拼接时放最前面
     */
    String URL = "/feign/place/employee";


    /**
     * 根据手机号判断员工账号是否存在
     *
     * @param requestTicket 请求ticket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/checkAccountByMobile")
    GenericResponse<ObjDTO<PlaceAccountBO>> checkAccountByMobile(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                 @RequestParam(value = "mobile") String mobile);

    /**
     * 根据手机号判断员工账号是否存在
     *
     * @param requestTicket 请求ticket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/checkAccountByMobileForRegister")
    GenericResponse<ObjDTO<PlaceAccountBO>> checkAccountByMobileForRegister(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                 @RequestParam(value = "mobile") String mobile);


    /**
     * 登录
     *
     * @param requestTicket 请求ticket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/login")
    GenericResponse<SimpleObjDTO> login(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam(value = "mobile") String mobile,
                                        @RequestParam(value = "password") String password,
                                        @RequestParam(value = "unionId") String unionId,
                                        @RequestParam(value = "openId") String openId,
                                        @RequestParam(value = "type") Integer type);

    /**
     * 注册
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @PostMapping(URL + "/register")
    public GenericResponse<ObjDTO<PlaceAccountBO>> register(@RequestHeader(value = "request_ticket") String requestTicket,
                                                            @RequestBody PlaceAccountBO placeAccountBO,
                                                            @RequestParam(value = "code") String code,
                                                            @RequestParam(value = "unionId") String unionId);

    /**
     * 找回密码
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/resetPwd")
    GenericResponse<?> resetPassword(@RequestHeader(value = "request_ticket") String requestTicket,
                                     @RequestParam(value = "mobile") String mobile,
                                     @RequestParam(value = "password") String password,
                                     @RequestParam(value = "code") String code,
                                     @RequestParam(value = "placeId", required = false) String placeId);


    /**
     * 绑定
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/bindMiniProgram")
    GenericResponse bindMiniProgram(@RequestHeader(value = "request_ticket") String requestTicket,
                                    @RequestParam(value = "placeId") String placeId,
                                    @RequestParam(value = "accountId") String accountId,
                                    @RequestParam(value = "openId") String openId,
                                    @RequestParam(value = "unionId") String unionId,
                                    @RequestParam(value = "mobile") String mobile);

    /**
     * 判断员工账号是否绑定至少一个网吧
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/judgeAccountBindBar")
    GenericResponse judgeAccountBindBar(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam(value = "mobile") String mobile);


    /**
     * 上传员工信息
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @PostMapping(URL + "/updateInfo")
    GenericResponse<ObjDTO<PlaceAccountBO>> updateInfo(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestBody PlaceAccountBO placeAccountBO);


    /**
     * 解绑
     *
     * @param requestTicket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/unBindMiniProgram")
    GenericResponse unBindMiniProgram(@RequestHeader(value = "request_ticket") String requestTicket,
                                      @RequestParam(value = "placeId") String placeId,
                                      @RequestParam(value = "mobile") String mobile);


    /**
     * 小程序我的功能-我的资料
     *
     * @param requestTicket 请求ticket（feign调用必填）
     * @return 收银员账号
     */
    @GetMapping(URL + "/queryAccountInfo")
    GenericResponse<ObjDTO<PlaceAccountBO>> queryAccountInfo(@RequestHeader(value = "request_ticket") String requestTicket,
                                                             @RequestParam(value = "mobile") String mobile,
                                                             @RequestParam(value = "placeId", required = false) String placeId);

    /**
     * 小程序我的功能-修改手机号码
     *
     * @param requestTicket 请求ticket（feign调用必填）
     * @return 收银员账号
     */
    @PostMapping(URL + "/updateMobile")
    GenericResponse updateMobile(@RequestHeader(value = "request_ticket") String requestTicket,
                                 @RequestParam(value = "mobile") String mobile,
                                 @RequestParam(value = "newMobile") String newMobile,
                                 @RequestParam(value = "code") String code);

    /**
     * 查询收银台账户
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/queryCashierList")
    GenericResponse<ListDTO<PlaceAccountBO>> queryCashierList( @RequestParam(value = "placeId") String placeId);

}
