package com.rzx.dim4.base.utils;

import com.rzx.dim4.base.cons.BaseConstants;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 * <AUTHOR>
 * @date 2019年10月23日上午11:21:20
 */
public class Dim4StringUtils {

	/**
	 * 生成1-13位随机数,
	 * 
	 * @param length 长度
	 * @return
	 */
	public static String generateCode(int length) {
		String code = Long.toString(System.nanoTime());
		if (length > code.length() || length < 1) {
			length = 6;
		}
		return code.substring(code.length() - length);
	}

	/**
	 * 没有-的UUID
	 * 
	 * @return
	 */
	public static String getUUIDWithoutHyphen() {
		return UUID.randomUUID().toString().replaceAll("-", "");
	}

	public static boolean isDigit(String str) {
		// 该正则表达式可以匹配所有的数字 包括负数
		Pattern pattern = Pattern.compile("-?[0-9]+(\\.[0-9]+)?");
		String bigStr;
		try {
			bigStr = new BigDecimal(str).toString();
		} catch (Exception e) {
			return false;// 异常 说明包含非数字。
		}

		Matcher isNum = pattern.matcher(bigStr); // matcher是全匹配
		if (!isNum.matches()) {
			return false;
		}
		return true;
	}

	/**
	 * 
	 * @param string   被替换的字符串
	 * @param position 从左往右，从1开始，替换replace长度的字符串
	 * @param replace  替换的字符串
	 * @return
	 */
	public static String replace(String string, int position, String replace) {
		if (position < 1 || position > string.length() || replace.length() + position > string.length() + 1) { //
			return string;
		}
		StringBuffer newString = new StringBuffer();
		newString.append(string.substring(0, position - 1));
		newString.append(replace);
		newString.append(string.substring(position + replace.length() - 1));
		return newString.toString();
	}

	public static void main(String[] args) {
		String code = "440309003001";
		System.out.println(code);
		System.out.println(Dim4StringUtils.replace(code, 5, "0000000"));
	}

	/**
	 * 密码强度校验
	 * @apiNote 至少6位，不能是默认密码，且必须同时包含字母和数字
	 * 校验强度前，需要校验密码是否正确
	 * @param password 待校验密码
	 */
	public static void isValidPassword(String password) {
		if (StringUtils.isEmpty(password)) {
			throw new ServiceException(ServiceCodes.PASSWORD_NOT_MEET_REQUIREMENTS);
		}

		if (password.equals(BaseConstants.DEFAULT_PASSWORD)) {
			throw new ServiceException(ServiceCodes.PASSWORD_NOT_MEET_REQUIREMENTS);
		}

		// ^ 表示匹配字符串的开头。
		// (?=.*[a-zA-Z]) 表示字符串中必须包含至少一个字母。
		// (?=.*\d) 表示字符串中必须包含至少一个数字。
		// .{6,} 表示字符串长度至少为 6。
		// $ 表示匹配字符串的结尾。
        String regex = "^(?=.*[a-zA-Z])(?=.*\\d).{6,}$";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(password);

		if (!matcher.matches()) {
			throw new ServiceException(ServiceCodes.PASSWORD_NOT_MEET_REQUIREMENTS);
		}

	}

	/**
	 * 密码强度校验
	 * @apiNote 至少6位，不能是默认密码，且必须同时包含字母和数字
	 * 校验强度前，需要校验密码是否正确
	 * @param password 待校验密码
	 */
	public static void isValidMiniAppPassword(String password) {
		if (StringUtils.isEmpty(password)) {
			throw new ServiceException(ServiceCodes.PASSWORD_NOT_MEET_REQUIREMENTS);
		}

		// ^ 表示匹配字符串的开头。
		// (?=.*[a-zA-Z]) 表示字符串中必须包含至少一个字母。
		// (?=.*\d) 表示字符串中必须包含至少一个数字。
		// .{6,} 表示字符串长度至少为 6。
		// $ 表示匹配字符串的结尾。
		String regex = "^(?=.*[a-zA-Z])(?=.*\\d).{6,}$";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(password);

		if (!matcher.matches()) {
			throw new ServiceException(ServiceCodes.PASSWORD_NOT_MEET_REQUIREMENTS);
		}

	}
}
