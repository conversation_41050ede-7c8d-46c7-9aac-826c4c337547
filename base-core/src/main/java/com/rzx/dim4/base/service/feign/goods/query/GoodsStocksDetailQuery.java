package com.rzx.dim4.base.service.feign.goods.query;

import com.rzx.dim4.base.bo.shop.GoodsStocksDetailBO;

import lombok.Data;

import java.util.List;

/**
 * 仓库操作明细
 */
@Data
public class GoodsStocksDetailQuery extends GoodsStocksDetailBO {

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    private String startCountDay;

    private String endCountDay;

    /**
     *  操作类型
     */
    private List<String> operationTypes;

    private String supplierName;

}
