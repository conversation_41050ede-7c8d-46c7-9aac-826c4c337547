package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
public class OrderRefundGoodsBO extends AbstractEntityBO {
	private Long id;
	private Long creater;
	private LocalDateTime created; // 账号创建时间
	private LocalDateTime updated; // 账号更新时间
	private int deleted;
	private String placeId; // 场所ID
	private String orderId; // 订单ID
	private String refundId; // 退款ID
	private String goodsId; // 商品ID
	private String goodsName; // 商品名称
	private int quantity; // 退款数量
	private int unitPrice; // 商品单价
	private int discounts; // 折扣金额
	private String goodsTypeId; // 商品类型ID
	private String goodsTypeName; // 商品类型名称
	private int goodsCategory; // 商品种类，0固装商品，1虚拟商品，2自制商品(默认0),3优惠券商品
	private int present; // 是否是赠送，0否，1是

	private OrderGoodsBO ordergoodsbo;
}
