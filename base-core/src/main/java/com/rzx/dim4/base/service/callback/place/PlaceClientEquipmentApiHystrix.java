package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceClientEquipmentDetailBO;
import com.rzx.dim4.base.bo.place.PlaceClientEquipmentBO;
import com.rzx.dim4.base.bo.place.PlaceClientEquipmentAggregateBo;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceClientEquipmentApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> hwx
 * @since 2025/2/18 15:56
 */
@Service
@Slf4j
public class PlaceClientEquipmentApiHystrix implements PlaceClientEquipmentApi {
    @Override
    public GenericResponse<ListDTO<PlaceClientEquipmentBO>> queryByPlaceIdAndClientId(@RequestParam String placeId,
            @RequestParam String clientId) {
        log.error("接口异常:::queryByPlaceIdAndEquipmentId(placeId:::{},equipmentId:::{})", placeId, clientId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> batchDelete(@RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId, @RequestParam List<String> equipmentIds) {
        log.error("接口异常:::batchDelete(placeId:::{},equipmentId:::{})", placeId, equipmentIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> batchUpdate(@RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId, @RequestBody List<PlaceClientEquipmentBO> bos) {
        log.error("接口异常:::batchUpdate(placeId:::{},bos:::{})", placeId, bos);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceClientEquipmentDetailBO>> detail(@RequestParam String placeId,
            @RequestParam String clientId) {
        log.error("接口异常:::detail(placeId:::{},clientId:::{})", placeId, clientId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<PlaceClientEquipmentAggregateBo>> pageAggregate(@RequestParam String placeId,
            @RequestParam(required = false, defaultValue = "") String equipmentType,
            @RequestParam(required = false, defaultValue = "") String equipmentName,
            @RequestParam(required = false, defaultValue = "") String areaId,
            @RequestParam(required = false, defaultValue = "") String clientName,
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "10") int size) {
        log.error("接口异常:::pageAggregate(placeId:::{},itemType:::{},itemName:::{},areaId:::{},clientName:::{})", 
                 placeId, equipmentType, equipmentName, areaId, clientName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
