package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.marketing.statistics.StatisticsGoodsSaleByDayBO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StatisticsGoodsSaleByDayVO extends StatisticsGoodsSaleByDayBO {
    private static final long serialVersionUID = -3467097021781133992L;
    //毛利
    private int grossProfit;
    //毛利率
    private double grossProfitMargin;
    //平均售价/实际均价
    private float avgSalePrice;
    /**
     * 入库均价
     */
    private float avgReceiptTotal;

}
