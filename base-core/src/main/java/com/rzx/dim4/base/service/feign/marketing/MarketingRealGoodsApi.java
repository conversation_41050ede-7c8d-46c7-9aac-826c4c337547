package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingRealGoodsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-06-18
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingRealGoodsApi", fallback = MarketingRealGoodsApiHystrix.class)
public interface MarketingRealGoodsApi {
    String URL = "/feign/marketing/real/goods";

    @PostMapping(URL + "/findRealGoodsTemplatePage")
    GenericResponse<PagerDTO<BarcodeResponseBO>> findRealGoodsTemplatePage(@RequestBody GoodsRealTemplateRequestBo paramsBo);

    @PostMapping(URL + "/saveRealGoods")
    GenericResponse<SimpleDTO> saveRealGoods(@RequestBody GoodsRealAddRequestBO paramsBo);

    @PutMapping(URL + "/updateRealGoods")
    GenericResponse<SimpleDTO> updateRealGoods(@RequestBody GoodsRealUpdateRequestBO paramsBo);

    @DeleteMapping(URL + "/removeRealGoods")
    GenericResponse<SimpleDTO> removeRealGoods(@RequestParam String placeId, @RequestParam String goodsId);


    @PostMapping(URL + "/editFixGoods")
    GenericResponse<?> editFixGoods(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody @Validated SaveFixGoodsBO saveFixGoodsBO);


    @PostMapping(URL + "/batchDeleteGoods")
    GenericResponse<?> batchDeleteGoods(@RequestHeader(value = "request_ticket") String requestTicket,
                                        @RequestParam(name = "placeId") String placeId,
                                        @RequestParam(name = "goodsIdList") List<String> goodsIdList,
                                        @RequestBody PlaceAccountBO placeAccountBO);

    @PostMapping(URL + "/batchImport")
    GenericResponse<SimpleDTO> batchImport(@RequestHeader(value = "request_ticket") String requestTicket,@RequestBody List<BarcodeResponseBO> list);

}
