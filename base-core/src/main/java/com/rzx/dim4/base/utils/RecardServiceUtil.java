/*
 * @(#)RecardServiceUtil.java 1.00 2024-8-5
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.utils;

import org.springframework.util.StringUtils;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.SourceType;

/**
 * <p>Title:</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-8-5
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
public class RecardServiceUtil {

	/**
	 * 判断是否需要检查注册卡
	 * 
	 * @param regcard - 注册卡开关
	 * @param checkRegcard - 移动端扫码是否跳过注册卡绑定开关 
	 * @param regcardCheckedClients - 移动端扫码终端类型字符串 
	 * @param sourceType - 来源
	 * @return
	 */
	public static boolean isNeedCheckRegcard(int regcard, int checkRegcard, String regcardCheckedClients, SourceType sourceType) {
		if(regcard == 0) {
			// 关闭注册卡
			return false;
		}
		if(checkRegcard == 0) {
			// 移动端扫码不跳过注册卡绑定（收银端需要弹检查注册卡弹窗）
			return true;
		}
		
		String sourceClient = "-1";
    	if(sourceType.equals(SourceType.WECHAT)) {
    		sourceClient = "1";
    	}else if(sourceType.equals(SourceType.YISHANGWANG)) {
    		sourceClient = "2";
    	}else if(sourceType.equals(SourceType.IOT)) {
    		sourceClient = "3";
    	}else if(sourceType.equals(SourceType.ALIPAY)) {
    		sourceClient = "4";
    	}
    	if(!StringUtils.isEmpty(regcardCheckedClients) && !"0".equals(regcardCheckedClients) && !regcardCheckedClients.contains(sourceClient)) {
    		return true;// 收银端需要弹检查注册卡弹窗
    	}
    	
    	return false;
	}
	
	/**
	 * 判断是否需要检查注册卡
	 * @param placeConfig
	 * @param sourceType
	 * @return
	 */
	public static boolean isNeedCheckRegcard(PlaceConfigBO placeConfig, SourceType sourceType) {
		return isNeedCheckRegcard(placeConfig.getRegcard(), placeConfig.getCheckRegcard(), placeConfig.getRegcardCheckedClients(), sourceType);
	}
	
	/**
	 * 来源客户端是否属于移动端跳过选项
	 * @param sourceType
	 * @param regcardCheckedClients
	 * @return
	 */
	public static boolean isInRegcardCheckedClients(SourceType sourceType, String regcardCheckedClients) {
		String sourceClient = "-1";
    	if(sourceType.equals(SourceType.WECHAT)) {
    		sourceClient = "1";
    	}else if(sourceType.equals(SourceType.YISHANGWANG)) {
    		sourceClient = "2";
    	}else if(sourceType.equals(SourceType.IOT)) {
    		sourceClient = "3";
    	}else if(sourceType.equals(SourceType.ALIPAY)) {
    		sourceClient = "4";
    	}
    	if(StringUtils.isEmpty(regcardCheckedClients) || "0".equals(regcardCheckedClients)) {
    		return true;
    	}
    	if(regcardCheckedClients.contains(sourceClient)) {
    		return true;
    	}
    	
    	return false;
	}
	
	/**
	 * 来源客户端是否属于移动端跳过选项
	 * @param activeType
	 * @param regcardCheckedClients
	 * @return
	 */
	public static boolean isInRegcardCheckedClients(ActiveType activeType, String regcardCheckedClients) {
    	String sourceClient = "-1";
    	if(ActiveType.SWGJ_WECHAT.getDisplay().equals(activeType.getDisplay())) {
    		sourceClient = "1";
    	}else if(ActiveType.APP_YI_SHANG_WANG.getDisplay().equals(activeType.getDisplay())) {
    		sourceClient = "2";
    	}else if(ActiveType.ALI_IOT.getDisplay().equals(activeType.getDisplay())) {
    		sourceClient = "3";
    	}else if(ActiveType.ALIPAY_MINI_APP.getDisplay().equals(activeType.getDisplay())) {
    		sourceClient = "4";
    	}
    	if(StringUtils.isEmpty(regcardCheckedClients) || "0".equals(regcardCheckedClients)) {
    		return true;
    	}
    	if(regcardCheckedClients.contains(sourceClient)) {
    		return true;
    	}
    	
    	return false;
	}
	
}
