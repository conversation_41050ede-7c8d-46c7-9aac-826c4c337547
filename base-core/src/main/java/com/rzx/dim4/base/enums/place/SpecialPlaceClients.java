package com.rzx.dim4.base.enums.place;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 特殊的客户端来源（在收银台扣款明细中展示）
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Getter
public enum SpecialPlaceClients {

    MINIAPP("-1", "小程序"),
    STORE_BACKEND("-2", "后台管理系统"),
    WECHAT_MP("-3", "微信公众号"),
    CASHIER("-4", "收银台");

    private final String clientId;
    private final String clientName;

    SpecialPlaceClients(String clientId, String clientName) {
        this.clientId = clientId;
        this.clientName = clientName;
    }

    public static SpecialPlaceClients findByClientId(String clientId) {
        for (SpecialPlaceClients clientIDType : values()) {
            if (clientIDType.getClientId().equals(clientId)) {
                return clientIDType;
            }
        }
        return null;
    }

    public static boolean isSpecialPlaceClient(String clientId) {
        for (SpecialPlaceClients clientIDType : values()) {
            if (clientIDType.getClientId().equals(clientId)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isNotSpecialPlaceClient(String clientId) {
        return !isSpecialPlaceClient(clientId);
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        for (SpecialPlaceClients clientIDType : values()) {
            map.put(clientIDType.getClientId(), clientIDType.getClientName());
        }
        return map;
    }
}
