package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
public class GoodsPurchaseOrderBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String supplierId; // 供应商id
    private String supplierName; // 供应商名称
    private String purchaseOrderNum; // 采购单编号
    private int goodsKindTotal; // 采购商品种类数目
    private int goodsTotal; // 采购商品总数目
    private String payTypeName;
    private String createrName;

    @Min(value = 0,message = "采购金额错误;")
    private int purchaseAmount; // 采购金额（单位分）

    @Min(value = 0,message = "实际支付金额错误;")
    private int payAmount; // 实际支付金额（单位分）

    private int status; // 采购订单状态0待入库，1已入库

    @NotNull(message = "支付方式不能为空!")
    @Min(value = 0,message = "支付方式最小为0;")
    @Max(value = 4,message = "支付方式最大为4;")
    private int payType; // 支付方式：0现金，1支付宝，2微信，3挂账，4其他
    private List<GoodsPurchaseOrderListBO> goodsPurchaseOrderListBOS; // 商品采购订单清单列表



}
