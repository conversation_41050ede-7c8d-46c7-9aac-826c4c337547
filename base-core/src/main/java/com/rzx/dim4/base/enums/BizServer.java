package com.rzx.dim4.base.enums;

/**
 * 服务标识
 *
 * <AUTHOR>
 * @date Jul 28, 2020 4:50:38 PM
 */
public enum BizServer {

	ALL("全部"), // 所有业务服务
	PLACE("代理端服务"), // place-server
	BILLING("计费服务"), //
	REALNAME("实名服务"), //
	USER("用户服务"), //
	AUTH("授权服务"), //
	PAYMENT("支付服务"), //
	SHOP("商超服务"), //
	IOT("iot服务"), // iot-server
	NONE("无"), // 无业务服务

	MARKETING("营销服务"), // 无业务服务
	;

	private final String value;

	private BizServer(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}

}
