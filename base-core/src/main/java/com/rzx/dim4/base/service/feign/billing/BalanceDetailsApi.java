package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BalanceDetailsBO;
import com.rzx.dim4.base.bo.billing.ExportInternetFeeOrderBO;
import com.rzx.dim4.base.bo.place.PlaceShiftSumBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.billing.BalanceDetailOperationType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BalanceDetailsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 查询网费余额明细
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "balanceDetailsApi", fallback = BalanceDetailsApiHystrix.class)
public interface BalanceDetailsApi {

    String URL = "/feign/billing/balanceDetails";

    @PostMapping(URL + "/findBalanceDetailsExportData")
    GenericResponse<ListDTO<ExportInternetFeeOrderBO>> findBalanceDetailsExportData(@RequestBody Map<String, String> queryMap);

    @PostMapping(URL + "/findBalanceDetailsDataPage")
    GenericResponse<PagerDTO<BalanceDetailsBO>> findBalanceDetailsDataPage(@RequestBody Map<String, String> queryMap,
                                                                           @RequestParam(name = "page", defaultValue = "0") int page,
                                                                           @RequestParam(name = "size", defaultValue = "10") int size);

    @PostMapping(URL + "/queryBalanceDetailsList")
    GenericResponse<ListDTO<BalanceDetailsBO>> queryBalanceDetailsList(@RequestParam String placeId,
                                                                       @RequestParam LocalDateTime startTime,
                                                                       @RequestParam LocalDateTime endTime,
                                                                       @RequestParam BalanceDetailOperationType operationType);

    @GetMapping(URL + "/queryPlaceShiftStatisticsInfo")
    GenericResponse<ObjDTO<PlaceShiftSumBO>> queryPlaceShiftStatisticsInfo(@RequestParam String placeId,
                                                                           @RequestParam LocalDateTime startTime,
                                                                           @RequestParam LocalDateTime endTime);

    @PostMapping(URL + "/queryBalanceDetails")
    GenericResponse<PagerDTO<BalanceDetailsBO>> queryBalanceDetails(@RequestBody Map<String, String> queryMap,
                                                                    @RequestParam(name = "size", defaultValue = "10") int size,
                                                                    @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping(URL + "/createBalanceDetails")
    GenericResponse<ObjDTO<BalanceDetailsBO>> createBalanceDetails(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody BalanceDetailsBO balanceDetailsBO);

    @PostMapping(URL + "/addBalanceDetails")
    GenericResponse<SimpleDTO> addBalanceDetails(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody BalanceDetailsBO balanceDetailsBO);
}
