package com.rzx.dim4.base.service.feign.iot;

import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.bo.user.Dim4UserBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.iot.IotAuthConfigApiHystrix;
import lombok.Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/24
 **/
@Primary
@FeignClient(value = FeginConstant.IOT_SERVER, contextId = "IotAuthConfigApi", fallback = IotAuthConfigApiHystrix.class)
public interface IotAuthConfigApi {

    String URL = "/feign/iot/authConfig";

    /**
     * 查询场所下可选的实名收费规则
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/{placeId}")
    GenericResponse<ListDTO<IotAuthConfigBO>> queryAuthConfig(@PathVariable String placeId,
                                                              @RequestParam(name="faceclientType",required=false) String faceclientType,
                                                              @RequestParam(name="placeType",required=false) String placeType);

    /**
     * 当前场所下实名认证是否开启收费，开启了返回收费规则
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/needCharge/{placeId}")
    GenericResponse<ListDTO<IotAuthConfigBO>> needCharge(@PathVariable String placeId,@RequestParam(name="faceclientType",required=false) String faceclientType,
    		@RequestParam(name="placeType",required=false) String placeType);

    /**
     * 当前用户在当前场所本次实名认证是否需要收费
     *
     * @param placeId
     * @param idNumber
     * @return
     */
    @GetMapping(URL + "/currentNeedCharge")
    GenericResponse<ObjDTO<LogAuthFeeBO>> currentNeedCharge(@RequestParam String placeId,
                                                            @RequestParam String idNumber);


    @PostMapping(URL + "/saveLogAfterSuccess")
    GenericResponse<ObjDTO<LogAuthFeeBO>> saveLogAfterSuccess(@RequestBody SaveLogAfterSuccessBO saveLogAfterSuccessBO);


    /**
     * 创建小程序人脸认证套餐购买支付订单
     *
     * @param requestTicket
     * @param placeId
     * @param idNumber
     * @param amount
     * @param openId
     * @return
     */
    @GetMapping(URL + "/create/authPayOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> createAuthPayOrder(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String idNumber,
            @RequestParam String openId,
            @RequestParam long authChargeRuleId,
            @RequestParam int amount,
            @RequestParam(required = false) String appId,
            @RequestParam(required = false) String faceId);

    /**
     * 创建公众号人脸认证套餐购买支付订单
     *
     * @param requestTicket
     * @param placeId
     * @param idNumber
     * @param amount
     * @param returnUrl 支付完成后龙兜跳转返回的页面
     * @return
     */
    @GetMapping(URL + "/create/mpAuthPayOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> mpAuthPayOrder(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String idNumber,
            @RequestParam long authChargeRuleId,
            @RequestParam int amount,
            @RequestParam(required = false) String returnUrl,
            @RequestParam(required = false) String faceId,
            @RequestParam(required = false) String openId);


    /**
     * 查询小程序人脸认证套餐支付结果
     *
     * @param requestTicket
     * @param orderId
     * @return
     */
    @GetMapping(URL + "/query/authPayResult")
    GenericResponse<ObjDTO<LogAuthFeeBO>> queryAuthPayOrder(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String orderId);


    @Data
    class SaveLogAfterSuccessBO {
        private PlaceProfileBO placeProfileBO;
        private Dim4UserBO dim4UserBO;
        private String serialno;
    }
}
