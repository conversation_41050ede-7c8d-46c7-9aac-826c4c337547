package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 固装商品查询BO
 *
 * <AUTHOR>
 * @date 2024年12月03日 15:27
 */
@Getter
@Setter
public class FixGoodsQueryBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "排除排行榜商品的标志")
    private String showRank;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "商品名称或条码")
    private String goodsNameOrBarCode;

    @ApiModelProperty(value = "商品类别（0固装商品，1虚拟商品）",required = true)
    private String goodsCategory;

    @ApiModelProperty(value = "商品状态,0 上架,1下架")
    @Pattern(regexp = "^[01]$", message = "商品状态只能为 0 或 1")
    private String sellStatus;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "标签ID，逗号分隔")
    private String tagIds;

    @ApiModelProperty(value = "虚拟商品类型")
    private String virtualGoodsType;

    @ApiModelProperty(value = "价格类型：0售价，1成本价", example = "0")
//  @Pattern(regexp = "^[01]$", message = "价格类型只能为 0 或 1")
    private String priceType="0";

    @ApiModelProperty(value = "是否查询库存（传货架ID）")
    private String queryStorage;

    @ApiModelProperty(value = "售价起始价格")
    private Double startUnitPrice;

    @ApiModelProperty(value = "售价结束价格")
    private Double endUnitPrice;

    @ApiModelProperty(value = "成本起始价格")
    private Double startCostPrice;

    @ApiModelProperty(value = "成本结束价格")
    private Double endCostPrice;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "页数,从0开始",required = true)
    @Min(value = 0, message = "页数不能小于0")
    private int page = 0;

    @ApiModelProperty(value = "每页大小",required = true)
    @Max(value = 100, message = "每页大小最多为100")
    @Min(value = 1, message = "每页大小必须至少为1")
    private int size = 10;
}
