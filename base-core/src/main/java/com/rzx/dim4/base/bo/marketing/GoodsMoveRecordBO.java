package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;


import javax.persistence.Column;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月04日 15:17
 */
@Getter
@Setter
@ApiModel(description = "商品上下架记录实体")
public class GoodsMoveRecordBO extends AbstractEntityBO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "创建者ID")
    private Long creater;

    @ApiModelProperty(value = "账号创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "账号更新时间")
    private LocalDateTime updated;

    @ApiModelProperty(value = "删除状态")
    private int deleted;

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "记录类型，0上架，1下架")
    @Max(value = 1, message = "参数错误")
    @Min(value = 0, message = "参数错误")
    private int recordType;

    @ApiModelProperty(value = "货架ID，场所唯一，从100000开始递增，主仓库为000000")
    private String storageRackId;

    @ApiModelProperty(value = "货架名称")
    private String storageRackName;

    @ApiModelProperty(value = "上架商品种类数目")
    private int goodsKindTotal;

    @ApiModelProperty(value = "上架商品总数目")
    private int goodsTotal;

    @ApiModelProperty(value = "操作记录ID，用该ID查询操作记录表能查询出所有操作商品列表")
    private String changeRecordId;

    @ApiModelProperty(value = "备注")
    @Length(message = "备注不能超过个 {max} 字符！", max = 50)
    private String remark;

    @ApiModelProperty(value = "上下架商品列表")
    private List<GoodsInventoryChangeRecordBO> moveRecordGoods;


    @ApiModelProperty(value = "操作人姓名")
    private String createrName;
}
