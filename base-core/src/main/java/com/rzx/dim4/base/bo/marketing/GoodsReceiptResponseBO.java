package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel("供应商管理")
public class GoodsReceiptResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "入库单编号")
    private String goodsReceiptNum;

    @ApiModelProperty(value = "入库仓库(货架管理的记录的storage_rack_id)默认主仓库(000000)")
    private String storageRackId;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "商品类型：0商品，1原料（默认商品）")
    private int type;

    @ApiModelProperty(value = "商品品种：0普通，1半成品，2半成品（默认普通）")
    private int goodsKind;

    @ApiModelProperty(value = "支付方式：0现金，1支付宝，2微信，3挂账，4上打下，5其他")
    private int payType;

    @ApiModelProperty(value = "数量:每种品种的商品数量之和")
    private int goodsTotal;

    @ApiModelProperty(value = "应付（单位分）")
    private int money;

    @ApiModelProperty(value = "实付（单位分）")
    private int paidMoney;

    @ApiModelProperty(value = "状态：0正常，1作废，2退货（默认正常）")
    private int status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID")
    private Long creater;

    @ApiModelProperty(value = "创建人姓名")
    private String createrName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "入库单商品列表")
    private List<GoodsReceiptListResponseBO> goodsReceiptListBOS;
}
