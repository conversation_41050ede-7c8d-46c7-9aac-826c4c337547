package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 退货
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Getter
@Setter
@ToString
public class ReturnGoodsRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "账户ID", hidden = true)
    private Long accountId;

    @ApiModelProperty(value = "账户名称", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "供应商id", hidden = true)
    private String supplierId;

    @ApiModelProperty(value = "商品类型：0商品，1原料（默认商品）")
    private int type;

    @ApiModelProperty(value = "商品品种")
    private int goodsKind;

    @ApiModelProperty(value = "支付方式：0现金，1支付宝，2微信，3挂账，4上打下，5其他")
    private int payType;

    @ApiModelProperty(value = "数量:每种品种的商品数量之和")
    private int goodsTotal;

    @ApiModelProperty(value = "应付（单位分）")
    private int money;

    @ApiModelProperty(value = "实付（单位分）")
    private int paidMoney;

    @ApiModelProperty(value = "损益（单位分）：每件商品的损益和")
    private int profitLossPrice;

    @ApiModelProperty(value = "退货单商品列表")
    private List<ReturnGoodsDetailRequestBO> returnGoodsDetails;
}
