package com.rzx.dim4.base.service.callback;

import com.rzx.dim4.base.bo.idauth.*;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.IdauthServerService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date Dec 25, 20195:26:02 PM
 */
@Slf4j
@Service
public class IdauthServerServiceHystrix implements IdauthServerService {

    @Override
    public GenericResponse<ObjDTO<AuthRuleBO>> getAuthRule(Long ruleId) {
        log.error("接口异常::: getAuthRule(ruleId:::{})-------", ruleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<AuthRuleBO>> saveAuthRule(String requestTicket, AuthRuleBO authRuleBO) {
        log.error("接口异常::: saveAuthRule(requestTicket:::{}, callRuleBO:::{})---", requestTicket, authRuleBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteAuthRule(String requestTicket, Long authRuleId) {
        log.error("接口异常::: deleteAuthRule(requestTicket:::{}, authRuleId:::{})---", requestTicket, authRuleId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<IdCardBO>> queryIdCard(String idNumber) {
        log.error("接口异常::: getIdCard(idNumber:::{})---", idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<AuthRuleBO>> list(String userId, String placeId, String regionCode, int page,
                                                      int size, String order, String[] orderColumns) {
        log.error(
                "接口异常::: list(userId:::{}, placeId:::{},regionCode:::{}, page:::{}, size:::{}, order:::{}, orderColumns:::{})---",
                userId, placeId, regionCode, page, size, order, orderColumns);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> statsByDay(String date) {
        log.error("接口异常::: statsByDay(date:::{})---", date);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatsByDayBO>> queryStatsByDay(String userId, String startDate, String endDate,
                                                                   int page, int size, String order, String[] orderColumns) {
        log.error(
                "接口异常::: queryStatsByDay(userId:::{}, startDate:::{},endDate:::{}, page:::{}, size:::{}, order:::{}, orderColumns:::{})---",
                userId, startDate, endDate, page, size, order, orderColumns);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StatsByDayBO>> queryCallDetail(String userId, String startDate, String endDate, String authType) {
        log.error(
                "接口异常::: queryCallDetail(userId:::{}, startDate:::{},endDate:::{},authType:::{})---",
                userId, startDate, endDate, authType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StatsByDayBO>> queryCallDetailByToday(String userId, String authType) {
        log.error("接口异常::: queryCallDetailByToday(userId:::{},authType:::{})", userId, authType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatsByDayBO>> queryErrorDetail(String userId, String startDate, String endDate, String authType, int curPage, int pageSize) {
        log.error(
                "接口异常::: queryErrorDetail(userId:::{}, startDate:::{},endDate:::{},authType:::{},curPage:::{},pageSize:::{})---",
                userId, startDate, endDate, authType, curPage, pageSize);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatsByDayBO>> queryErrorDetailByToday(String userId, String authType, int curPage, int pageSize) {
        log.error(
                "接口异常::: queryErrorDetail(userId:::{},authType:::{},curPage:::{},pageSize:::{})---",
                userId, authType, curPage, pageSize);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> auth3e(String authorization, String sign, String idNumber, String name, String facePhotoBase64) {
        log.error("接口异常::: auth3e(authorization:::{}, idNumber:::{},name:::{}, sign:::{})---", authorization, idNumber, name, sign);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> auth2e(String authorization, String sign, String idNumber, String name) {
        log.error("接口异常::: auth2e(authorization:::{}, idNumber:::{},name:::{}, sign:::{})---", authorization, idNumber, name, sign);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> checkRealnameAuth(String idNumber, int mins) {
        log.error("接口异常::: checkRealnameAuth(idNumber:::{},mins:::{})---", idNumber, mins);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> livenessCheck(String authorization, String sign, MultipartFile video) {
        log.error("接口异常::: livenessCheck(sign:::{},authorization:::{})---", sign, authorization);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<UserBO>> getUser(String userId) {
        log.error("接口异常::: getUser(userId:::{})", userId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<UserBO>> saveUser(String requestTicket, UserBO userBO) {
        log.error("接口异常::: saveUser(requestTicket:::{},UserBO:::{})", requestTicket, userBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<UserKeyBO>> getKey(String userId) {
        log.error("接口异常::: getKey(userId:::{})", userId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> resetKey(String requestTicket, String userId) {
        log.error("接口异常::: resetKey(requestTicket:::{},userId:::{})", requestTicket, userId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<UserBO>> fuzzy(String search, int page, int size, String order, String[] orderColumns) {
        log.error(
                "接口异常::: fuzzy(search:::{}, page:::{},size:::{}, order:::{},orderColumns:::{})---",
                search, page, size, order, orderColumns);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<UserBO>> findUsers() {
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public List<UserAccessBO> getUserAccessList(String userId) {
        log.error("接口异常::: getUserAccessList(userId:::{})", userId);
        return null;
    }

    @Override
    public GenericResponse<SimpleDTO> saveUserAccess(String requestTicket, UserAccessBO userAccessBO) {
        log.error("接口异常::: saveUserAccess(requestTicket:::{},userAccessBO:::{})", requestTicket, userAccessBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteUserAccess(String requestTicket, UserAccessBO userAccessBO) {
        log.error("接口异常::: deleteUserAccess(requestTicket:::{},userAccessBO:::{})", requestTicket, userAccessBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveAccessList(String requestTicket, AccessListBO accessListBO) {
        log.error("接口异常::: deleteUserAccess(requestTicket:::{},accessListBO:::{})", requestTicket, accessListBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<AccessListBO>> getAccessList(String userId) {
        log.error("接口异常::: getAccessList(userId:::{})", userId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
