package com.rzx.dim4.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Slf4j
public class FtpUtils {

    private static final String SERVER = "************";

    private static final int PORT = 21;

    private static final String USER_NAME = "dev";

    private static final String PASS_WORD = "lg6yCAaLHAEZzzq794";

    private static final String REMOTE_FILE_PATH = "/var/ftp/pub";

    public static final String SERVER_DOMAIN = "assets.4wgj.com";// Ftp域名

    /**
     * MultipartFile上传到ftp服务器
     * @param file
     * @param filePath
     * @return customFileName 自定义文件名,不传则用默认生成的文件名
     * @throws IOException
     */
    public static String ftpUpload (MultipartFile file, String filePath, String customFileName) throws IOException {

        // 文件后缀
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        String uuid = UUID.randomUUID().toString();
        String fileName = uuid + "." + suffix;
        if (!StringUtils.isEmpty(customFileName)) {
            fileName = customFileName;
        }

        FTPClient ftp = new FTPClient();
        Path tempFile = Files.createTempFile("prefix", "suffix");
        try {
            ftp.connect(SERVER, PORT);
            ftp.login(USER_NAME, PASS_WORD);
            ftp.enterLocalPassiveMode(); // 如果服务器在防火墙后面，需要设置被动模式
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            ftp.setControlEncoding("UTF-8");

            // 检查目标目录是否存在,如果没有则新建
            if (!ftp.changeWorkingDirectory( REMOTE_FILE_PATH + filePath)) {
                String[] dirs = (REMOTE_FILE_PATH + filePath).split("/");
                StringBuffer sb = new StringBuffer();
                for (String dir : dirs) {
                    if (dir.equals("")) {
                        continue;
                    }
                    sb.append("/").append(dir);
                    if (!ftp.changeWorkingDirectory(sb.toString())) {
                        if (!ftp.makeDirectory(sb.toString())) {
                            throw new IOException("Could not create directory: " + sb.toString());
                        }
                        ftp.changeWorkingDirectory(sb.toString());
                    }
                }
            }

            // 将MultipartFile的内容复制到临时文件
            Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

            FileInputStream input = new FileInputStream(tempFile.toFile());
            boolean success = ftp.storeFile(REMOTE_FILE_PATH + filePath + "/" + fileName, input);
            input.close();
            Files.deleteIfExists(tempFile); // 删除临时文件
            if (success) {
                log.info("文件上传ftp服务器成功。。。。。。");
            }
            return "https://" + SERVER_DOMAIN + REMOTE_FILE_PATH + filePath + "/" + fileName;
        } catch (Exception e) {
            throw new IOException(e);
        } finally {
            try {
                if (ftp.isConnected()) {
                    ftp.logout();
                    ftp.disconnect();
                    Files.deleteIfExists(tempFile); // 删除临时文件
                }
            } catch (IOException ex) {
                throw new IOException(ex);
            }
        }
    }

}
