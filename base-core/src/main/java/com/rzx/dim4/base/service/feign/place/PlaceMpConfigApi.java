/*
 * @(#)PlaceConfigApi.java 1.00 2024-7-30
 *
 * Copyright (c) 2005 Shenzhen Surfilter Network Technology Co.,Ltd. All rights reserved.
 */
package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceMpConfigBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceMpConfigApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;


/**
 * <p>Title:场所配置接口</p>
 * <p>Description:</p>
 * @version 1.00 
 * @since 2024-7-30
 * <AUTHOR>
 *  
 * Modified History: 
 *
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceMpConfigApi", fallback = PlaceMpConfigApiHystrix.class)
public interface PlaceMpConfigApi {
	String URL = "/feign/place/placeMpConfig";

	@GetMapping(URL + "/findByPlaceId")
    GenericResponse<ObjDTO<PlaceMpConfigBO>> findByPlaceId(@RequestParam String placeId);
    
}
