package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 盘点的商品列表
 * <AUTHOR>
 * @date 2024年12月03日
 */
@Getter
@Setter
@ToString
@ApiModel(description = "商品盘点列表实体")
public class GoodsStocktakingListBO extends AbstractEntityBO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "创建者ID")
    private Long creater;

    @ApiModelProperty(value = "账号创建时间")
    private LocalDateTime created;

    @ApiModelProperty(value = "账号更新时间")
    private LocalDateTime updated;

    @ApiModelProperty(value = "删除状态")
    private int deleted;

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "商品盘点单号")
    private String stocktakingNum;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品图片")
    private String goodsPic;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "账存数")
    private int storageNumber;

    @ApiModelProperty(value = "进货价")
    private int costPrice;

    @ApiModelProperty(value = "实际数量")
    private int actualNumber;

    @ApiModelProperty(value = "盈亏金额")
    private int profitLossMoney;

    @ApiModelProperty(value = "商品销售价")
    private int unitPrice;

    @ApiModelProperty(value = "排序顺序")
    private int sortOrder;
}
