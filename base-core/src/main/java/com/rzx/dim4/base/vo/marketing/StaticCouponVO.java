package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠券表bo
 */
@Getter
@Setter
public class StaticCouponVO extends AbstractEntityBO implements Serializable {


    private static final long serialVersionUID = 502469515230730840L;

    private List<StaticCouponItemVO> couponItemVos; // 优惠券ID
    private int amountSum; // 合计金额
    private int countSum;  //合计数量
}

