package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.payment.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.PaymentServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 认证服务 待删除，不添加新接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.PAYMENT_SERVER, contextId = "PaymentServerService", fallback = PaymentServerServiceHystrix.class)
public interface PaymentServerService {

    /**
     * 创建支付订单
     *
     * @param requestTicket
     * @param orderAmount   订单金额（单位分）
     * @param orderDesc     订单描述
     * @param storeNo       收款账户
     * @param payType       支付类型
     * @param bizServer     业务服务
     * @param bizType       业务类型
     * @param idNumber      身份证号（可选）
     * @param placeId       网吧ID（可选）
     * @return
     */
    @PostMapping(value = "/payment/order/create")
    GenericResponse<ObjDTO<PaymentResultBO>> createPaymentOrder(@RequestHeader("request_ticket") String requestTicket,
                                                                @RequestBody PaymentRequestBO PaymentRequestBO);

    /**
     * 同步创建支付订单
     */
    @PostMapping(value = "/payment/order/syncCreate")
    GenericResponse<ObjDTO<PaymentResultBO>> syncCreatePaymentOrder(@RequestBody PaymentOrderSyncBO paymentOrderSyncBO);

    /**
     * 根据订单号查询订单
     *
     * @param orderId 订单ID（调用/payment/order/create返回的orderId）
     * @return
     * @see com.rzx.dim4.payment.web.controller.PaymentQueryController#queryPaymentOrder(String)
     */
    @GetMapping(value = "/payment/order/query/order")
    GenericResponse<ObjDTO<PaymentOrderBO>> queryPaymentOrder(@RequestParam String orderId);

    /**
     * 条件查询订单列表
     *
     * @param queryMap 支持的参数包括：orderId，placeId，idNumber，bizServer，bizType，status，startTime，endTime
     * @param size     返回的记录数
     * @param page     第几页
     * @return
     */
    @PostMapping(value = "/payment/order/query/orders")
    GenericResponse<PagerDTO<PaymentOrderBO>> queryPaymentOrders(@RequestBody Map<String, String> queryMap,
                                                                 @RequestParam(name = "size", defaultValue = "10") int size,
                                                                 @RequestParam(name = "page", defaultValue = "1") int page);

    @PostMapping(value = "/payment/refund/refund")
    GenericResponse<ObjDTO<PaymentRefundOrderBO>> refundPaymentOrder(@RequestHeader("request_ticket") String requestTicket,
                                                                     @RequestParam String placeId,
                                                                     @RequestParam String orderId,
                                                                     @RequestParam int refundAmount);

    @PostMapping(value = "/payment/refund/query/orders")
    GenericResponse<PagerDTO<PaymentRefundOrderBO>> queryPaymentRefundOrders(@RequestBody Map<String, String> queryMap,
                                                                             @RequestParam(name = "size", defaultValue = "10") int size,
                                                                             @RequestParam(name = "page", defaultValue = "1") int page);

    @PostMapping(value = "/payment/order/ldopay/notifySync")
    GenericResponse<?> notifyPaymentOrderSync(@RequestParam String orderId, @RequestParam(required = false) Integer poundage);

    @PostMapping(value = "/payment/withdrawal/create")
    GenericResponse<ObjDTO<PaymentWithdrawalResultBO>> createWithdrawal(@RequestHeader("request_ticket") String requestTicket,
                                                                     @RequestParam String placeId, @RequestParam String applyNo,
                                                                     @RequestParam int applyAmt, @RequestParam String merUsername);

    @GetMapping(value = "/payment/withdrawal/query")
    GenericResponse<ObjDTO<PaymentWithdrawalResultBO>> queryWithdrawal(@RequestHeader("request_ticket") String requestTicket,
                                                                        @RequestParam String applyNo, @RequestParam String merUsername);

}
