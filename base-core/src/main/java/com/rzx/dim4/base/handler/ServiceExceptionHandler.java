package com.rzx.dim4.base.handler;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.exception.ServiceStringException;
import com.rzx.dim4.base.response.GenericResponse;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 全局异常处理捕获处理器
 *
 * <AUTHOR>
 * @since 2023/4/21
 **/
@Slf4j
@ControllerAdvice
public class ServiceExceptionHandler {

    /**
     * 业务逻辑异常，捕获 （主要service 层抛出的）ServiceException
     *
     * @param request
     * @param ex
     * @return
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public GenericResponse<?> handleServiceException(HttpServletRequest request, ServiceException ex) {
        log.info("全局[ServiceException]处理---------------------------------------");

        String url = request.getRequestURI();

        ServiceCodes serviceCode = ex.getErrorCode();

        // 额外自定义的错误信息
        if (!StringUtils.isBlank(ex.getDetailedMsg())) {
            String detailedMsg = ex.getDetailedMsg();
            log.warn("handleServiceException()......url={}, code={}, message={}, detailMsg={}", url, serviceCode.getCode(), serviceCode.getMessage(), detailedMsg);
            printLogStackTrace(ex);
            return new GenericResponse<>(serviceCode, detailedMsg);
        }

        log.warn("handleServiceException()......url={}, code={}, message={}", url, serviceCode.getCode(), serviceCode.getMessage());
        printLogStackTrace(ex);
        return new GenericResponse<>(serviceCode);
    }

    /**
     * 适用于需要直接返回字符串的场景
     * @param request
     * @param ex
     * @return
     */
    @ExceptionHandler(ServiceStringException.class)
    @ResponseBody
    public String handleServiceStringException(HttpServletRequest request, ServiceStringException ex) {
        log.info("全局[handleServiceStringException]处理---------------------------------------");

        String url = request.getRequestURI();
        String message = ex.getMessage();

        log.warn("handleServiceStringException()......url={}, message={}", url,  message);
        printLogStackTrace(ex);
        return message;
    }

    /**
     * 打印出错的堆栈信息
     * @param ex 异常
     */
    private static void printLogStackTrace(RuntimeException ex) {
        StackTraceElement[] stackTraceElements = ex.getStackTrace();
        StringBuffer sb = new StringBuffer();
        for (StackTraceElement stackTraceElement : stackTraceElements) {
            if (stackTraceElement.toString().contains("com.rzx.dim4")) {
                sb.append(stackTraceElement).append("\n");
            }
        }
        log.warn("ServiceExceptionHandler.printLogStackTrace()......stackTrace=\n{}", sb);
    }

    /**
     * 请求参数异常
     * @param request 请求
     * @param e 异常
     * @return GenericResponse 异常包装信息
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public GenericResponse<SimpleDTO> handleMissingServletRequestParameterException(HttpServletRequest request, MissingServletRequestParameterException e) {
        log.info("全局[MissingServletRequestParameterException]处理---------------------------------------", e);

        GenericResponse<SimpleDTO> response = new GenericResponse<>(ServiceCodes.BAD_PARAM);
        log.error("方法:::{} code:{}, msg:{}",request.getRequestURI(),
                ServiceCodes.BAD_PARAM.getCode() ,ServiceCodes.BAD_PARAM.getMessage());
        return response;
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public GenericResponse<?> exceptionHandler(MethodArgumentNotValidException exception) {
        //用于拦截 @Validated 注解抛出的参数校验异常
        BindingResult result = exception.getBindingResult();
        StringBuilder stringBuilder = new StringBuilder();
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            errors.forEach(p -> {
                FieldError fieldError = (FieldError) p;
                stringBuilder.append(fieldError.getDefaultMessage());
            });
        }
        return new GenericResponse(stringBuilder.toString());
    }

    @ResponseBody
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public GenericResponse<?> httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException exception) {
        //用于拦截 @RequestBody 参数不符合格式抛出的异常
        log.info("--------全局异常处理，参数错误--------"+exception.getMessage());
        return new GenericResponse(ServiceCodes.BAD_PARAM);
    }


    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public GenericResponse<?> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex,HttpServletRequest request) {
        //用于拦截 post/get 请求方式错误的异常
        log.info("方法:::{} {} 请求方式错误",request.getRequestURI(),ex.getMethod());
        return new GenericResponse<>(ServiceCodes.FREQUENT_METHOD_ERROR);
    }

    /**
     * 处理其他异常，包装一层
     *
     * @param e 实际异常
     * @return GenericResponse
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public GenericResponse<?> exceptionHandler(Exception e) {
        log.error("------全局缺省异常处理------", e);
        return new GenericResponse<>(ServiceCodes.SYSTEM_ERROR);
    }


}