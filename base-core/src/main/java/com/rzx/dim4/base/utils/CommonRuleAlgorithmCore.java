package com.rzx.dim4.base.utils;

import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年10月30日 17:42
 */
public class CommonRuleAlgorithmCore {

    /**
     * 根据prices获取当天当前小时的费率价格 (返回的是分)
     *
     * @param prices
     * @return
     */
    public static int getPrice(String prices) {
        // 星期 7*24数据
        String[] weekData = prices.split("_");

        // 获取当前星期数
        int weekNum = LocalDateTime.now().getDayOfWeek().getValue();

        // 取出当前星期数的 7*24费率信息
        String priceStr = weekData[weekNum - 1];

        // 获取当天当前小时的费率
        int nowHour = LocalDateTime.now().getHour();
        String[] priceArray = priceStr.split(",", -1);
        String price = priceArray[nowHour];

        if (StringUtils.isEmpty(price)) {
            return 0;
        }

        try {
            return Integer.parseInt(price);
        } catch (Exception e) {
            return 0;
        }
    }
}
