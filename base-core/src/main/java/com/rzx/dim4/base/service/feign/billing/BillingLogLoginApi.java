package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.bo.billing.LogLoginQueryRequestBO;
import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.bo.billing.LogOperationQueryRequestBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingLogLoginApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "BillingLogLoginApi", fallback = BillingLogLoginApiHystrix.class)
public interface BillingLogLoginApi {

    String URL = "/feign/cashier";

    @PostMapping(URL + "/log/login/page")
    GenericResponse<PagerDTO<LogLoginBO>> findLogLoginPage(@RequestBody LogLoginQueryRequestBO params);

    @PostMapping(URL + "/log/operation/page")
    GenericResponse<PagerDTO<LogOperationBO>> findLogOperationPage(@RequestBody LogOperationQueryRequestBO paramsBo);
}
