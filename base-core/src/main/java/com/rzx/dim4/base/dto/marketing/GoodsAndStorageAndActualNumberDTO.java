package com.rzx.dim4.base.dto.marketing;


import java.math.BigDecimal;

/** 商品数据、账存数量和实际数量的投影
 * <AUTHOR> hwx
 * @since 2025/2/25 11:10
 */
public interface GoodsAndStorageAndActualNumberDTO extends GoodsIdAndNameAndTypeIdAndTypeNameDTO {

    /**
     * 账存数量
     */
    Integer getStorageNumber();

    /**
     * 实际数量
     */
    Integer getActualNumber();

    /**
     * 库存差
     */
    Integer getStorageDiff();

    /**
     * 单价
     */
    Integer getUnitPrice();
}
