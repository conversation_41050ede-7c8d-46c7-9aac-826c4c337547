package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveQueryBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveStatusBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveUpdateStatusBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PackageTimeReserveApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023年10月25日 18:41
 */
@Slf4j
@Component
public class PackageTimeReserveApiHystrix implements PackageTimeReserveApi {
    @Override
    public GenericResponse<ObjDTO<PackageTimeReserveBO>> queryByPlaceIdAndCardId(String placeId, String cardId) {
        log.error("接口异常:::queryByPlaceIdAndCardId(placeId:::{}，cardId:::{})", placeId, cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PackageTimeReserveBO>> queryByPlaceIdAndCardIdAndOrderId(String placeId, String cardId, String orderId) {
        log.error("接口异常:::queryByPlaceIdAndCardIdAndOrderId(placeId:::{}, cardId:::{}, orderId={})", placeId, cardId, orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PackageTimeReserveStatusBO>> queryPackageTimeStatus(PackageTimeReserveStatusBO params) {
        log.error("接口异常:::queryPackageTimeStatus(Params={})", new Gson().toJson(params));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updatePackageTimeStatus(PackageTimeReserveUpdateStatusBO params) {
        log.error("接口异常:::updatePackageTimeStatus(Params={})", new Gson().toJson(params));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PackageTimeReserveQueryBO>> getBatchPackageTimeStatus(@RequestBody PackageTimeReserveQueryBO params) {
        log.error("接口异常:::getBatchPackageTimeStatus(Params={})", new Gson().toJson(params));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
