package com.rzx.dim4.base.dto.marketing;

import com.rzx.dim4.base.enums.payment.PayType;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/** 退款商品订单和订单的投影
 * <AUTHOR> hwx
 * @since 2025/2/26 14:32
 */
public interface OrderRefundGoodsAndOrderDTO extends GoodsIdAndNameAndTypeIdAndTypeNameDTO {
    String getOrderId();

    String getRefundId();

    /**
     * 金额
     * @return
     */
    BigDecimal getAmount();

    LocalDateTime getPayTime();

    PayType getPayType();

    /**
     * 创建人
     * @return
     */
    Long getCreater();

    /**
     * 支付订单创建时间
     * @return
     */
    LocalDateTime getOrderCreated();
}
