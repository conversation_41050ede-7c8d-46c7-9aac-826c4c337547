package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.place.PlaceChainCardTypeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceChainCardTypeApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/8
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "placeChainCardTypeApi", fallback = PlaceChainCardTypeApiHystrix.class)
public interface PlaceChainCardTypeApi {

    String URL = "/feign/billing/place/chain/card/type";

    @PostMapping(URL + "/add")
    GenericResponse<?> add(@RequestBody PlaceChainCardTypeBO placeChainCardTypeBO);

    @PostMapping(URL + "/edit")
    GenericResponse<?> edit(@RequestBody PlaceChainCardTypeBO placeChainCardTypeBO);

    /**
     * 根据连锁id获取连锁下的会员卡列表
     * @param chainId
     * @return
     */
    @GetMapping(URL+"/queryByChainId")
    GenericResponse<ListDTO<PlaceChainCardTypeBO>> queryByChainId(
            @RequestHeader(value = "request_ticket") String requestTicket,@RequestParam("chainId") String chainId);


    @PostMapping(URL + "/list")
    GenericResponse<PagerDTO<PlaceChainCardTypeBO>> page(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestBody DataTablesRequest dtRequest);

    @PostMapping(URL + "/delete")
    GenericResponse<?> delete(@RequestHeader(value = "request_ticket") String requestTicket,
                              @RequestParam String chainId,
                              @RequestParam String cardTypeId);
}
