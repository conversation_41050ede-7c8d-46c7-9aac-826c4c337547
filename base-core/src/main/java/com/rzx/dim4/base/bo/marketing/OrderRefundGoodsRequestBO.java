package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "退款商品")
public class OrderRefundGoodsRequestBO extends AbstractEntityBO {
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "退款数量")
    private int quantity;
}
