package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@HeadRowHeight(value = 20)//设置表头行高
@ColumnWidth(value = 15)//设置表头行宽
public class StatisticsSlowMovingGoodsExportVO implements Serializable {

    private static final long serialVersionUID = -7852768133277533391L;

    @ExcelProperty(value = "商品名称", index = 0)
    private String goodsName; // 商品名称

    @ExcelProperty(value = "单价",index = 1)
    private float unitPrice; // 商品单价

    @ExcelProperty(value = "销售量",index = 2)
    private int countSale; // 销售数量(总量)

    @ExcelProperty(value = "销售额",index = 3)
    private float sumSaleTotal; // 销售金额(总额)

    //毛利
    @ExcelProperty(value = "毛利",index = 4)
    private float grossProfit;

    //毛利率
    @ExcelProperty(value = "毛利率",index = 5)
    private double grossProfitMargin;

    //平均售价
    @ExcelProperty(value = "平均售价",index = 6)
    private float avgSalePrice;
}
