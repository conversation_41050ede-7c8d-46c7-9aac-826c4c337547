package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年02月10日 17:00
 */
@Getter
@Setter
@ToString
public class MemberEventPushRecordBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String eventId; // 活动id
    private String memberEventPushRecordId; // 推送记录id
    private String eventName; // 活动名称
    private String cardId; // 计费卡ID
    private String idNumber; // 身份证号码
    private String idName; // 姓名
    private String cardTypeId; // 计费卡类型 1000临时卡，1001 普通卡，1002 工作卡，三个固定值
    private String cardTypeName; // 计费卡名称
    private int eventType; //活动类型，1流失会员召回活动
    private String pushData; // 推送文本 或者 url
    private int pushStatus; // 0已推送，1已到账，2直接到账
    private LocalDateTime receiveTime; // 领取时间
    private SourceType sourceType; // 领取来源
    private String placeName; // 场所名称

    private List<MemberEventPushGoodsRecordBO> memberEventPushGoodsRecordBOS;
}
