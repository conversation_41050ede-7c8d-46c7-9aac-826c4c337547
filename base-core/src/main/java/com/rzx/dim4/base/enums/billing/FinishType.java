package com.rzx.dim4.base.enums.billing;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/6
 **/
@Getter
public enum FinishType {

    // expire
    EXPIRED(0, "到期"),
    USER_LOGIN(1, "用户登录"),
    NOT_ENOUGH_BALANCE(2, "余额不足"),
    CASHIER_CANCEL(3, "收银台取消"),
    USER_CANCEL(4, "用户取消"),
    ;

    private final int value;

    private final String desc;

    FinishType(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static FinishType getByValue(int value) {
        for (FinishType type : FinishType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new ServiceException(ServiceCodes.BAD_PARAM);
    }

    public static FinishType getByName(String name) {
        for (FinishType type : FinishType.values()) {
            if (type.getDesc().equals(name)) {
                return type;
            }
        }
        throw new ServiceException(ServiceCodes.BAD_PARAM);
    }
}
