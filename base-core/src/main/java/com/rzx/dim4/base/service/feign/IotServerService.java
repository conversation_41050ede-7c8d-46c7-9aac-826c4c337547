package com.rzx.dim4.base.service.feign;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.rzx.dim4.base.bo.iot.AppUpgradeBO;
import com.rzx.dim4.base.bo.iot.AppVersionInfoBO;
import com.rzx.dim4.base.bo.iot.FacePlaceWhitelistBO;
import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.IotDeviceBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.iot.LogFaceAuthBO;
import com.rzx.dim4.base.bo.iot.PlanPayConfigBO;
import com.rzx.dim4.base.bo.iot.PlanPayConfigConectionBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.IotServerServiceHystrix;

@Primary
@FeignClient(value = "iot-server", fallback = IotServerServiceHystrix.class)
public interface IotServerService {

	/***********************************/
	/********** 网吧设备信息相关 **********/
	/***********************************/
	@PostMapping("/api/iot/device/savePlaceDevice")
	public GenericResponse<SimpleDTO> savePlaceDevice(@RequestHeader(value = "request_ticket") String requestTicket,
													  @RequestBody IotDeviceBO iotDeviceBo);

	@PostMapping("/api/iot/device/iot/queryPlaceDevices")
	public GenericResponse<PagerDTO<IotDeviceBO>> queryPlaceDevices(@RequestBody Map<String, Object> queryMap,
																	@RequestParam(name = "size", defaultValue = "10") int size,
																	@RequestParam(name = "page", defaultValue = "1") int page);

	@GetMapping("/api/iot/device/iot/queryPlaceDevice")
	GenericResponse<ObjDTO<IotDeviceBO>> queryPlaceDevice(@RequestParam String serialno);

	@GetMapping("/api/iot/device/iot/queryPlaceDeviceByPlaceId")
	GenericResponse<ListDTO<IotDeviceBO>> queryPlaceDeviceByPlaceId(@RequestParam String placeId);

	@GetMapping("/api/iot/device/iot/queryActiveDeviceNum")
	public Map<String,Integer> queryActiveDeviceNum ();

	@GetMapping("/api/iot/device/iot/delete")
	public GenericResponse<SimpleDTO> deleteDevice (@RequestParam String serialno);
	
	@PostMapping("/api/iot/device/batchModifyPlaceDevice")
	public GenericResponse<SimpleDTO> batchModifyPlaceDevice(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody Map<String, Object> queryMap);


	/***********************************/
	/********** 网吧认证配置相关 **********/
	/***********************************/
	@PostMapping("/api/iot/authconfig/save")
	public GenericResponse<SimpleDTO> savePlaceAuthConfig(@RequestHeader(value = "request_ticket") String requestTicket,
														  @RequestBody IotAuthConfigBO iotAuthConfigBO);

	@PostMapping("/api/iot/authconfig/queryPlaceAuthConfigs")
	public GenericResponse<PagerDTO<IotAuthConfigBO>> queryPlaceAuthConfigs(@RequestBody Map<String, Object> queryMap,
                                                                            @RequestParam(name = "size", defaultValue = "10") int size,
                                                                            @RequestParam(name = "page", defaultValue = "1") int page);

	@GetMapping("/api/iot/authconfig/queryPlaceAuthConfig")
	GenericResponse<ObjDTO<IotAuthConfigBO>> queryPlaceAuthConfig(@RequestParam String id);

	@GetMapping("/api/iot/authconfig/batchStop")
	public GenericResponse<SimpleDTO> batchStop (@RequestHeader(value = "request_ticket") String requestTicket,
												 @RequestParam String placeIds,
												 @RequestParam String authFeeTypes);

	@GetMapping("/api/iot/authconfig/delete")
	public GenericResponse<SimpleDTO> deleteAuthConfig (@RequestParam String placeId,
														@RequestParam String placeAuthFeeType);

	/***********************************/
	/********** 网吧实名人脸订单相关 ******/
	/***********************************/
	@PostMapping("/api/iot/authFee/queryPlaceAuthFees")
	public GenericResponse<PagerDTO<LogAuthFeeBO>> queryPlaceAuthFees(@RequestBody Map<String, Object> queryMap,
																	  @RequestParam(name = "size", defaultValue = "10") int size,
																	  @RequestParam(name = "page", defaultValue = "1") int page);

	@PostMapping("/api/iot/authFee/queryFeeStatistics")
	public Map<String, Integer> queryFeeStatistics(@RequestBody Map<String, Object> queryMap);
	
	@PostMapping("/api/iot/authFee/modifyOrderStatus")
	public GenericResponse<?> modifyOrderStatus(@RequestParam String orderId, @RequestParam int status, @RequestParam String account);
	
	@PostMapping("/api/iot/authFee/queryPlaceAuthFeeByFaceId")
	public GenericResponse<ObjDTO<LogAuthFeeBO>> queryPlaceAuthFeeByFaceId(@RequestBody Map<String, Object> queryMap);
	
	/***********************************/
	/********** 网吧人脸记录相关 ******/
	/***********************************/
	@PostMapping("/api/iot/faceAuth/queryFaceAuths")
	public GenericResponse<PagerDTO<LogFaceAuthBO>> queryFaceAuths(@RequestBody Map<String, Object> queryMap,
																   @RequestParam(name = "size", defaultValue = "10") int size,
																   @RequestParam(name = "page", defaultValue = "1") int page);


	/*********************************/
	/**********新套餐配置*************/
	/*********************************/
	@PostMapping("/plan/pay/config/getPlanPayConfigs")
	public GenericResponse<PagerDTO<PlanPayConfigBO>> getPlanPayConfigs(@RequestBody Map<String, Object> queryMap,
			@RequestParam(name = "size", defaultValue = "10") int size,
			@RequestParam(name = "page", defaultValue = "1") int page);
	
	@PostMapping("/plan/pay/config/getPlanPayConfigConections")
	public GenericResponse<ListDTO<PlanPayConfigConectionBO>> getPlanPayConfigConections(@RequestBody Map<String, Object> queryMap);
	
	@PostMapping("/plan/pay/config/save")
	public GenericResponse<SimpleDTO> savePlanPayConfig(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlanPayConfigBO bo);
	
	@PostMapping("/plan/pay/config/modify")
	public GenericResponse<SimpleDTO> modifyPlanPayConfig(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlanPayConfigBO planPayConfigBO);
	
	@PostMapping("/plan/pay/config/connection/add")
	public GenericResponse<SimpleDTO> addPlanPayConfigConection(
			@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlanPayConfigConectionBO planPayConfigConectionBO);
	
	@PostMapping("/plan/pay/config/barConnection/delete")
	public GenericResponse<SimpleDTO> deleteConnections4Bar(
			@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlanPayConfigConectionBO planPayConfigConectionBO);
	
	
	@PostMapping("/plan/pay/config/areaConnection/update")
	public GenericResponse<SimpleDTO> updateAreaConnections(
			@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlanPayConfigConectionBO planPayConfigConectionBO);

	@PostMapping("/api/iot/faceAuth/queryFaceStatistics")
	public Map<String,Integer> queryFaceStatistics(@RequestBody Map<String, Object> queryMap);


	@PostMapping("/plan/pay/config/getPlanPayConfigConections4Page")
	public GenericResponse<PagerDTO<PlanPayConfigConectionBO>> getPlanPayConfigConections4Page(@RequestBody Map<String, Object> queryMap,
			@RequestParam(name = "size", defaultValue = "5") int size, @RequestParam(name = "page", defaultValue = "1") int page);

	@PostMapping("/plan/pay/config/getActiveConfigsBySingelBar")
	public GenericResponse<PagerDTO<PlanPayConfigBO>> getActiveConfigsBySingelBar(@RequestBody Map<String, Object> queryMap,
			@RequestParam(name = "size", defaultValue = "5") int size, @RequestParam(name = "page", defaultValue = "1") int page);

	/********支付宝新安卓设备IOT APP版本管理***开始*****/
	@PostMapping("/api/app/version/queryAppVersionInfos")
	public GenericResponse<PagerDTO<AppVersionInfoBO>> queryAppVersionInfos(@RequestBody Map<String, Object> queryMap, @RequestParam(name = "size", defaultValue = "10") int size,
			@RequestParam(name = "page", defaultValue = "1") int page);

	@GetMapping("/api/app/version/getAppVersionInfo")
	GenericResponse<ObjDTO<AppVersionInfoBO>> getAppVersionInfo(@RequestBody Map<String, Object> queryMap);
	
	@GetMapping("/api/app/version/deleteVersion")
	public GenericResponse<SimpleDTO> deleteVersion(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String versionCode);

	@PostMapping("/api/app/version/saveAppVersion")
	public GenericResponse<SimpleDTO> saveAppVersion(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody AppVersionInfoBO bo);

	@PostMapping("/api/app/upgrade/queryAppUpgradeInfos")
	public GenericResponse<PagerDTO<AppUpgradeBO>> queryAppUpgradeInfos(@RequestBody Map<String, Object> queryMap, @RequestParam(name = "size", defaultValue = "5") int size,
			@RequestParam(name = "page", defaultValue = "1") int page);

	@PostMapping("/api/app/upgrade/saveUpgradeInfos")
	public GenericResponse<SimpleDTO> saveUpgradeInfos(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody AppUpgradeBO bo);

	@PostMapping("/api/app/upgrade/updateVersionCode")
	public GenericResponse<SimpleDTO> updateVersionCode(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody AppUpgradeBO bo);
	/********支付宝新安卓设备IOT APP版本管理***结束*****/
	
	/********人脸套餐白名单***开始*****/
	@PostMapping("/api/faceplace/whitelist/queryFaceWhitelist4place")
	public GenericResponse<PagerDTO<FacePlaceWhitelistBO>> queryFaceWhitelist4place(@RequestBody Map<String, Object> queryMap,
			@RequestParam(name = "size", defaultValue = "10") int size, @RequestParam(name = "page", defaultValue = "1") int page);
	
	@PostMapping("/api/faceplace/whitelist/saveFaceWhitelist4place")
	public GenericResponse<SimpleDTO> saveFaceWhitelist4place(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody FacePlaceWhitelistBO bo);

	@GetMapping("/api/faceplace/whitelist/deleteFaceWhitelist4place")
	public GenericResponse<SimpleDTO> deleteFaceWhitelist4place(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId);
	
	
	/********人脸套餐白名单***结束*****/
}

