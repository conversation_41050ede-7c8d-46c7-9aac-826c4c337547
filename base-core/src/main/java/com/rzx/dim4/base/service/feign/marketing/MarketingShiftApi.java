package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.GoodsStocktakingBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingShiftApiHystrix;
import com.rzx.dim4.base.vo.place.PlaceShiftDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/** 订单接口
 * <AUTHOR> hwx
 * @since 2025/2/21 17:15
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingShiftApi", fallback = MarketingShiftApiHystrix.class)
public interface MarketingShiftApi {
    /**
     * 统计交班订单数据
     *
     * @param placeId
     * @param miniAppLoginAccount
     * @return
     */
    @PostMapping("/feign/marketing/shift/statistics")
    GenericResponse<ObjDTO<PlaceShiftDetailVO>> statistics(@RequestParam("placeId") String placeId, @RequestBody PlaceAccountBO miniAppLoginAccount);


    @PostMapping("/feign/marketing/shift/saveGoodsStocktaking")
    GenericResponse<ListDTO<GoodsStocktakingBO>> saveGoodsStocktaking(@RequestBody GoodsStocktakingBO goodsStocktakingBO);

//
//    @PostMapping("/feign/marketing/shift/submit")
//    GenericResponse<ObjDTO<PlaceShiftDetailVO>> submit(
//            @RequestParam("placeId") String placeId,
//            @RequestBody PlaceAccountBO miniAppLoginAccount);


}
