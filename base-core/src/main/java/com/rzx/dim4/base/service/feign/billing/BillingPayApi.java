package com.rzx.dim4.base.service.feign.billing;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingPayApiHystrix;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/18
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "billingPayApi", fallback = BillingPayApiHystrix.class)
public interface BillingPayApi {

    String URL = "/feign/billing/payOrder";

    /**
     * 创建充值订单(有无会员卡都可以充值)
     * @param requestTicket 请求
     * @param placeId 场所id
     * @param idNumber 证件号
     * @param amount 充值金额
     * @param openId 微信openId
     * @param cardTypeId 会员卡类型id
     * @param idName 证件名称
     * @param appId 微信appId（不传默认使用四维管家小程序appId）
     * @return 订单信息
     */
    @PostMapping(URL + "/mini/create")
    GenericResponse<ObjDTO<PaymentResultBO>> miniCreateOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                             @RequestParam String placeId,
                                                             @RequestParam String idNumber,
                                                             @RequestParam int amount,
                                                             @RequestParam String openId,
                                                             @RequestParam(required = false) String cardTypeId,
                                                             @RequestParam(required = false) String idName,
                                                             @RequestParam(required = false) String appId);

    /**
     * 创建支付订单,微信公众号支付
     * @param requestTicket 请求
     * @param placeId 场所id
     * @param idNumber 证件号
     * @param amount 充值金额
     * @param cardTypeId 会员卡类型id
     * @param idName 证件名称
     * @return 订单信息
     */
    @PostMapping(URL + "/mp/create")
    GenericResponse<SimpleDTO> mpCreateOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestParam String placeId,
                                             @RequestParam String idNumber,
                                             @RequestParam int amount,
                                             @RequestParam(required = false) String cardTypeId,
                                             @RequestParam(required = false) String idName);
    /**
     * 创建支付订单,微信公众号支付
     * @param requestTicket 请求
     * @param placeId 场所id
     * @param idNumber 证件号
     * @param amount 充值金额
     * @param cardTypeId 会员卡类型id
     * @param idName 证件名称
     * @return 订单信息
     */
    @PostMapping(URL + "/newMp/create")
    GenericResponse<ObjDTO<PaymentResultBO>> newMpCreateOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestParam String placeId,
                                             @RequestParam String idNumber,
                                             @RequestParam int amount,
                                             @RequestParam(required = false) String cardTypeId,
                                             @RequestParam(required = false) String idName,
                                             @RequestParam(required = false) String returnUrl,
                                             @RequestParam(required = false) String topupRuleId,
                                             @RequestParam(required = false) String openId);

    @PostMapping(URL + "/iot/iotCreateOrder")
    GenericResponse<ObjDTO<PaymentResultBO>> iotCreateOrder(@RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String cardTypeId,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam int amount,
            @RequestParam String payType,
            @RequestParam(required = false) String payCode);
    
    @PostMapping(URL + "/logTopup/queryLogTopup")
    public GenericResponse<ObjDTO<LogTopupBO>> queryLogTopup(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String orderId);

    @PostMapping(URL + "/aliapp/createOrder")
    public GenericResponse<ObjDTO<PaymentResultBO>> aliAppCreateOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                      @RequestParam String placeId,
                                                                      @RequestParam String idNumber,
                                                                      @RequestParam int amount,
                                                                      @RequestParam String openId,
                                                                      @RequestParam(required = false) String cardTypeId,
                                                                      @RequestParam(required = false) String idName,
                                                                      @RequestParam(required = false) String topupRuleId,
                                                                      @RequestParam String appId);
}
