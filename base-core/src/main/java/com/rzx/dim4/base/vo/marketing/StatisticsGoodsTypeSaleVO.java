package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.marketing.statistics.StatisticsGoodsSaleByDayBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StatisticsGoodsTypeSaleVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 2094538089130188959L;
    private String placeId; // 场所ID
    private String goodsId; // 商品ID
    private String goodsName; // 商品名称
    private int countSale; // 销售数量(总量)
    private int sumSaleTotal; // 销售金额(总额)
    private String goodsTypeId; // 商品类型ID
    private String goodsTypeName; // 商品类型名称
    private int countRefund; // 退款量(总量)
    private int sumRefundTotal; // 退款金额(总额)
    private int countReceipt; // 入库数量
    private int sumReceiptTotal; // 入库金额
    private int sumRefundDiscountsTotal; //退款折扣金额
    private int sumOriginSaleTotal; // 原来销售金额(总额)
    private String supplierId; // 供应商id
    //毛利
    private int grossProfit;
    //毛利率
    private double grossProfitMargin;

    /**
     * 实际均价
     */
    private float avgSalePrice;

    /**
     * 分类均价
     */
    private float unitPrice;
}
