package com.rzx.dim4.base.service.feign.payment.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.rzx.dim4.base.bo.AbstractBO;
import lombok.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/24
 **/
@EqualsAndHashCode(callSuper = false)
@Data
public class LdopayInfoBO extends AbstractBO {
    private String phone;

    private String merUsername;

    @JsonProperty("bank_name")
    @SerializedName("bank_name")
    private String bankName;

    @JsonProperty("bank_owner_name")
    @SerializedName("bank_owner_name")
    private String bankOwnerName;

    @JsonProperty("bank_card_no")
    @SerializedName("bank_card_no")
    private String bankCardNo;
}