package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年02月20日 14:43
 */
@Slf4j
@Component
public class MarketingGoodsApiHystrix implements MarketingGoodsApi {
    @Override
    public GenericResponse<ListDTO<GoodsPictureBO>> findAllGoodsPictures(String placeId) {
        log.error("接口异常，MarketingGoodsApiHystrix.findAllGoodsPictures(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsTypeBO>> getGoodsTypeList(String placeId) {

        log.error("接口异常，MarketingGoodsApiHystrix.getGoodsTypeList(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsBO>> getGoodsPageList(int pageSize, int pageStart, String goodsTypeId, String goodsName,
                                                               String placeId,String idNumber,String areaId,String showClientSwitch) {

        log.error("接口异常，MarketingGoodsApiHystrix.getGoodsPageList(pageSize={},pageStart={},goodsTypeId={},goodsName={},placeId={},idNumber={},areaId={})",
                pageSize, pageStart, goodsTypeId, goodsName, placeId,idNumber,areaId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsBO>> getMiniAppGoodsPageList(int size, int page, String goodsTypeId, String goodsName, String placeId) {
        log.error("接口异常，MarketingGoodsApiHystrix.getMiniAppGoodsPageList(pageSize={},pageStart={},goodsTypeId={},goodsName={},placeId={})", size, page, goodsTypeId, goodsName, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsBO>> saveGoods(String requestTicket, GoodsBO goodsBO) {
        log.error("接口异常，MarketingGoodsApiHystrix.saveGoods(goodsBO={})", new Gson().toJson(goodsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsSuppliersBO>> batchSaveSuppliers(List<GoodsSuppliersBO> suppliersBOS) {
        log.error("接口异常，MarketingGoodsApiHystrix.batchSaveSuppliers(suppliersBOS={})",new Gson().toJson(suppliersBOS));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsSuppliersBO>> saveGoodsSuppliers(String requestTicket,GoodsSuppliersBO goodsSuppliersBO) {
        log.error("接口异常，MarketingGoodsApiHystrix.saveGoodsSuppliers(goodsSuppliersBO={})",new Gson().toJson(goodsSuppliersBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsBO>> querySupplyGoodsDetails(String placeId, String supplierId) {
        log.error("接口异常，MarketingGoodsApiHystrix.querySupplyGoodsDetails(placeId={},supplierId={})", placeId, supplierId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsSuppliersBO>> pageGoodsSuppliers(String placeId, String search, int page, int size) {
        log.error("接口异常，MarketingGoodsApiHystrix.pageGoodsSuppliers(placeId={},search={},page={},size={})", placeId, search,page,size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsBO>> batchSaveGoods(List<GoodsBO> goodsBOS) {
        log.error("接口异常，MarketingGoodsApiHystrix.batchSaveGoods(goodsBOS={})",new Gson().toJson(goodsBOS));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsBO>> findByPlaceIdAndGoodsId(String placeId, String goodsId) {
        log.error("接口异常，MarketingGoodsApiHystrix.findByPlaceIdAndGoodsId(placeId={},goodsId={})", placeId, goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsBO>> findByPlaceIdAndGoodsIdIn(String placeId, List<String> goodsIds) {
        log.error("接口异常，MarketingGoodsApiHystrix.findByPlaceIdAndGoodsIdIn(placeId={},goodsIds={})", placeId, goodsIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsMiniAppBO>> findTopupPageListForMiniApp(String placeId, String cardTypeId, String idNumber) {
        log.error("接口异常，MarketingGoodsApiHystrix.findTopupPageListForMiniApp(placeId={},cardTypeId={},idNumber={} )", placeId, cardTypeId, idNumber);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsBO>> findTopupPageList(String placeId, String cardTypeId, String idNumber, String areaId, SourceType sourceType, int size, int start) {
        log.error("接口异常，MarketingGoodsApiHystrix.findTopupPageList(placeId={},cardTypeId={},idNumber={},areaId={},size={},start={} sourceType={} )",
                placeId, cardTypeId, idNumber, areaId, size, start, sourceType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsBO>> miniAppFindTopupPageList(String placeId, String cardTypeId, String idNumber, String areaId, SourceType sourceType, int size, int page) {
        log.error("接口异常，MarketingGoodsApiHystrix.miniAppFindTopupPageList(placeId={},cardTypeId={},idNumber={},areaId={},size={},page={} sourceType={} )",
                placeId, cardTypeId, idNumber, areaId, size, page, sourceType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsBO>> pageFixGoods(FixGoodsQueryBO queryBO) {
        log.error("接口异常，MarketingGoodsApiHystrix.findFixGoodsPageList(queryBO={})",new Gson().toJson(queryBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public void joinChainUpdateCardTypeIds(String placeId, String cardTypeIds) {
        log.error("接口异常，MarketingGoodsApiHystrix.joinChainUpdateCardTypeIds(placeId={},cardTypeIds={})",placeId,cardTypeIds);
    }

}
