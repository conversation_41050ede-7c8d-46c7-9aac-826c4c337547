package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceChainStoresApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 * @apiNote header 参数 requestTicket 必填
 * <AUTHOR>
 * @since 2023/6/9
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "placeChainStoresApi", fallback = PlaceChainStoresApiHystrix.class)
public interface PlaceChainStoresApi {

    String URL = "/feign/place/chain/stores";

    @PostMapping(URL + "/add")
    GenericResponse<?> add(@RequestHeader(value = "request_ticket")String requestTicket,
                           @RequestBody PlaceChainStoresBO placeChainStoresBO);

    @PostMapping(URL + "/checkBeforeAdd")
    GenericResponse<?> checkBeforeAdd(@RequestHeader(value = "request_ticket")String requestTicket,
                           @RequestBody PlaceChainStoresBO placeChainStoresBO);

    @PostMapping(URL + "/doAdd")
    GenericResponse<?> doAdd(@RequestHeader(value = "request_ticket")String requestTicket,
                                      @RequestBody PlaceChainStoresBO placeChainStoresBO);

    @PostMapping(URL + "/saveAll")
    GenericResponse<?> saveAll(@RequestHeader(value = "request_ticket")String requestTicket,
                               @RequestBody List<PlaceChainStoresBO> placeChainStoresBOS);

    @PostMapping(URL + "/page")
    GenericResponse<PagerDTO<PlaceChainStoresBO>> page(@RequestHeader(value = "request_ticket")String requestTicket,
                                   @RequestBody DataTablesRequest dtRequest);

    @PostMapping(URL + "/edit")
    GenericResponse<?> edit(@RequestHeader(value = "request_ticket")String requestTicket,
                            @RequestBody PlaceChainStoresBO placeChainStoresBO);

    @PostMapping(URL + "/exit")
    GenericResponse<?> exit(@RequestHeader(value = "request_ticket")String requestTicket,
                            @RequestBody PlaceChainStoresBO placeChainStoresBO);

    @GetMapping(URL + "/queryPlaceChainStoresByChainId")
    GenericResponse<ListDTO<PlaceChainStoresBO>> queryPlaceChainStoresByChainId(
            @RequestHeader(value = "request_ticket")String requestTicket, @RequestParam String chainId);
}
