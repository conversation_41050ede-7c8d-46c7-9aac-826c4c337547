package com.rzx.dim4.base.vo.marketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
public class StatisticsLossProfitVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 6676325874501187097L;

    private String profitLossType;

    private String id;

    private String typeId;

    private String typeName;

    private int num;

    private int profitLossPrice;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime created;
}
