package com.rzx.dim4.base.utils;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年09月02日 11:59
 */
public class VerifyParam {

    /**
     * 校验两个时间的差是否大于2个月
     * @param startDate
     * @param endDate
     */
    public static void checkTimeDifferenceByMonth(LocalDateTime startDate,LocalDateTime endDate){
        if(null == startDate || null == endDate){
            return;
        }
        int year = endDate.getYear() - startDate.getYear(); //计算相差的年
        int month = endDate.getMonthValue() - startDate.getMonthValue(); //计算相差的月
        int timeDifference = (year*12) + month;//计算相差的合计月数
        System.out.println(timeDifference);
        if(timeDifference > 1){
            throw new ServiceException(ServiceCodes.BILLING_QUERY_TIME_IS_TOO_THREE);
        }
    }
}
