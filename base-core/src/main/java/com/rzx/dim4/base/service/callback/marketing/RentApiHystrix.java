package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.RentApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@Component
public class RentApiHystrix implements RentApi {


    @Override
    public GenericResponse<ObjDTO<RentConfigBO>> initRentConfig(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId) {
        log.error("接口异常,RentApiHystrix#initRentConfig,requestTicket:{},placeId:{}",requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RentConfigBO>> findConfigByPlaceId(String placeId) {
        log.error("接口异常,RentApiHystrix#findConfigByPlaceId,placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<OrdersBO>> findRentOrderBySomeCondition(String placeId,String cardId) {
        log.error("接口异常,RentApiHystrix#findRentOrderBySomeCondition,placeId:{},,cardId:{}", placeId,cardId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RentMemberDiscountBO>> saveOrUpdateRentMemberDiscount(String requestTicket, RentMemberDiscountBO bo) {
        log.error("接口异常,RentApiHystrix#findRentOrderBySomeCondition,bo:{}", new Gson().toJson(bo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RentAreaDiscountBO>> saveOrUpdateRentAreaDiscount(String requestTicket, RentAreaDiscountBO bo) {
        log.error("接口异常,RentConfigApiHystrix#saveOrUpdateRentAreaDiscount,bo:{}", new Gson().toJson(bo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RentAreaDiscountBO>> updateRentAreaDiscountByAreas(String requestTicket, String placeId, List<PlaceAreaBO> placeAreaBOList) {
        log.error("接口异常,RentConfigApiHystrix#updateRentAreaDiscountByAreaIds,placeIds:{},bo:{}", placeId,new Gson().toJson(placeAreaBOList));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<RentMemberDiscountBO>> updateRentMemberDiscountByCardTypes(String requestTicket, String placeId, List<BillingCardTypeBO> billingCardTypeBOList) {
        log.error("接口异常,RentConfigApiHystrix#updateRentMemberDiscountByCardTypes,placeIds:{},bo:{}", placeId,new Gson().toJson(billingCardTypeBOList));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


}
