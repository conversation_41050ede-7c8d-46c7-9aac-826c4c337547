package com.rzx.dim4.base.service.callback.place;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceProfileApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/23
 **/
@Slf4j
@Service
public class PlaceProfileApiHystrix implements PlaceProfileApi {
    @Override
    public GenericResponse<ObjDTO<PlaceProfileBO>> findPlaceByPlaceId(String placeId) {
        log.error("接口异常，getByPlaceId(placeId={} )", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceClientBO>> findClient(String placeId, String clientId) {
        log.error("接口异常，findClient(placeId={}, clientId={})", placeId, clientId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceConfigBO>> config(String placeId) {
        log.error("接口异常，config(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceProfileBO>> queryAll(Map<String, String> param) {
        log.error("接口异常，config(param={})", param.toString());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

	@Override
	public GenericResponse<ListDTO<PlaceProfileBO>> findByRegionCodeLike(int num, List<String> regionCodes) {
        log.error("接口异常，config(num={}, regionCodes={})", num, regionCodes.toString());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

    @Override
    public GenericResponse<ListDTO<PlaceProfileBO>> findByRegionCode(String regionCode) {
        log.error("接口异常，findByRegionCode(regionCode={})", regionCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<PlaceProfileBO>> findByDisplayName(String displayName) {
        log.error("接口异常，findByDisplayName(displayName={})", displayName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceProfileBO>> save(PlaceProfileBO placeProfileBO) {
         log.error("接口异常，save(placeProfileBO={})", new Gson().toJson(placeProfileBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
