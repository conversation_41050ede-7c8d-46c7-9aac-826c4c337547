package com.rzx.dim4.base.response;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.dto.AbstractDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 接口返回的数据的封装
 * 
 * <AUTHOR>
 * @date 2019年9月24日下午5:39:20
 */
@Data
public class GenericResponse<T extends AbstractDTO> implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5184903216805092103L;

	/**
	 * 请求是否被响应
	 */
	private boolean result;
	/**
	 * 0代表正确，非0代表不同的错误情况
	 */
	private int code;
	/**
	 * 失败时返回的错误信息
	 */
	private String message;
	/**
	 * 成功时返回的数据
	 */
	private T data;

	public GenericResponse() {
		this.result = Boolean.TRUE;
		this.code = ServiceCodes.NO_ERROR.getCode();
		this.message = ServiceCodes.NO_ERROR.getMessage();
	}

	public GenericResponse(boolean result) {
		this.result = result;
		if (result) {
			this.code = ServiceCodes.NO_ERROR.getCode();
			this.message = ServiceCodes.NO_ERROR.getMessage();
		} else {
			this.code = ServiceCodes.OPT_ERROR.getCode();
			this.message = ServiceCodes.OPT_ERROR.getMessage();
		}
	}

	public GenericResponse(String errorMsg) {
		this.result = Boolean.FALSE;
		this.code = ServiceCodes.OPT_ERROR.getCode();
		this.message = errorMsg;
	}

	public GenericResponse(ServiceCodes serviceCode, String errorMsg) {
		this(serviceCode);
		this.message = errorMsg;
	}

	public GenericResponse(ServiceCodes serviceCode) {
		if (serviceCode.getCode() % 10 == 0) {
			this.result = Boolean.TRUE;
		} else {
			this.result = Boolean.FALSE;
		}
		this.code = serviceCode.getCode();
		this.message = serviceCode.getMessage();
	}

	public GenericResponse(T data) {
		this.result = Boolean.TRUE;
		this.code = ServiceCodes.NO_ERROR.getCode();
		this.message = ServiceCodes.NO_ERROR.getMessage();
		this.data = data;
	}

	public GenericResponse(Exception e) {
		this.result = Boolean.FALSE;
		this.code = ServiceCodes.UNSPECIFIED.getCode();
		this.message = e.getMessage();
	}

	/**
	 * 转SimpleDTO格式的GenericResponse
	 */
	public static GenericResponse<SimpleDTO> toSimpleResponse(String successMessage) {
		return new GenericResponse<>(new SimpleDTO(successMessage));
	}

	/**
	 * 成功的GenericResponse
	 */
	public static <T extends AbstractDTO> GenericResponse<T> toSuccessResponse(T data) {
		return new GenericResponse<>(data);
	}

	/**
	 * 成功的GenericResponse<ObjDTO>
	 */
	public static <T extends AbstractBO> GenericResponse<ObjDTO<T>> toSuccessResponse(T data) {
		return new GenericResponse<>(new ObjDTO<>(data));
	}

	/**
	 * 失败的GenericResponse
	 */
	public static <T extends AbstractDTO> GenericResponse<T> toFailureResponse(String failureMessage) {
		return new GenericResponse<>(failureMessage);
	}

	/**
	 * 失败的GenericResponse
	 */
	public static <T extends AbstractDTO> GenericResponse<T> toFailureResponse(ServiceCodes serviceCodes) {
		GenericResponse<T> genericResponse = new GenericResponse<>();
		genericResponse.setResult(false);
		genericResponse.setCode(serviceCodes.getCode());
		genericResponse.setMessage(serviceCodes.getMessage());
		return genericResponse;
	}

	/**
	 * 转化成ListDTO格式的GenericResponse
	 */
	public static <T extends AbstractBO> GenericResponse<ListDTO<T>> toListDTOResponse(List<T> dataList) {
		GenericResponse<ListDTO<T>> response = new GenericResponse<>();
		response.setResult(true);
		response.setCode(ServiceCodes.NO_ERROR.getCode());
		response.setMessage(ServiceCodes.NO_ERROR.getMessage());
		response.setData(new ListDTO<>(dataList));
		return response;
	}
}
