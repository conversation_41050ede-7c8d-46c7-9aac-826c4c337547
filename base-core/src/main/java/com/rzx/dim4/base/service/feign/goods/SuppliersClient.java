package com.rzx.dim4.base.service.feign.goods;

import com.rzx.dim4.base.bo.shop.SuppliersBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;

import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import com.rzx.dim4.base.service.feign.goods.query.SuppliersQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER,contextId = "suppliersClient",fallback = ShopServerServiceHystrix.class)
public interface SuppliersClient {

    String SUPPLIER_URL ="/shop/admin/suppliers";

    /**
     *  查询
     * @param suppliersQuery
     * @return
     */
    @GetMapping(SUPPLIER_URL+"/listSuppliers")
    GenericResponse<ListDTO<SuppliersBO>> listSuppliers(@SpringQueryMap SuppliersQuery suppliersQuery);

    /**
     * 查询
     * @param suppliersQuery
     * @param pageable
     * @return
     */
    @GetMapping(SUPPLIER_URL+"/listSupplierPages")
    GenericResponse<PagerDTO<SuppliersBO>> listPages(@SpringQueryMap SuppliersQuery suppliersQuery, Pageable pageable);

    /**
     * 下拉框使用
     * @param placeId
     * @return
     */
    @GetMapping(SUPPLIER_URL+"/listNames")
    GenericResponse<ListDTO<SuppliersBO>> listSupplierNames(@RequestParam String placeId);

    /**
     * 新增或者更新
     * @param requestTicket
     * @param storageGoodsBO
     * @return
     */
    @PostMapping(SUPPLIER_URL+"/save")
    GenericResponse<?> save(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody SuppliersBO storageGoodsBO);


    @GetMapping(SUPPLIER_URL+"/delete")
    void delete(@RequestParam String supplierId , @RequestParam String placeId );
}
