package com.rzx.dim4.base.web.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfig {

//	@Bean
//	Logger.Level feignLoggerLevel() {
//		return Logger.Level.FULL;
//	}

	/**
	 * @Description 替换解析queryMap的类，实现父类中变量的映射
	 * @date 2023/3/21 16:59
	 * @version V1.0.0
	 */
//	@Bean
//	public Feign.Builder feignBuilder() {
//		return Feign.builder()
//				.queryMapEncoder(new BeanQueryMapEncoder())
//				.retryer(Retryer.NEVER_RETRY);
//	}
}