package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.ReportLossBO;
import com.rzx.dim4.base.bo.marketing.ReportLossRecordRequestBo;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingReportLossApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingReportLossApi", fallback = MarketingReportLossApiHystrix.class)
public interface MarketingReportLossApi {
    String URL = "/feign/marketing/reportLoss";

    @PostMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<ReportLossBO>> findPageList(@RequestBody ReportLossRecordRequestBo paramsBo);

    @PostMapping(URL + "/save")
    GenericResponse<ObjDTO<ReportLossBO>> saveReportLoss(@RequestBody ReportLossBO reportLossBO);


    @GetMapping(URL + "/findDetails")
    GenericResponse<ObjDTO<ReportLossBO>> findReportLossDetail(@RequestParam String placeId, @RequestParam String reportLossNo);
}
