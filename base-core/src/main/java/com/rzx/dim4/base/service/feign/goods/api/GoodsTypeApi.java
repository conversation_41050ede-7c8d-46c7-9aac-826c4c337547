package com.rzx.dim4.base.service.feign.goods.api;

import com.rzx.dim4.base.bo.shop.GoodsTypeBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.ShopServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商品web feign接口
 */
@Primary
@FeignClient(value = FeginConstant.SHOP_SERVER, contextId = "goodsTypeApi", fallback = ShopServerServiceHystrix.class)
public interface GoodsTypeApi {

    /**
     * 获取商品类型
     *
     * @return
     */
    @PostMapping(value = "/shop/api/goodsType/getList")
    GenericResponse<ListDTO<GoodsTypeBO>> getList(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId);
}
