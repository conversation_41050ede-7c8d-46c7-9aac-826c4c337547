package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.MarketDouyinStoreBO;
import com.rzx.dim4.base.bo.marketing.MeituanTokenBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketThirdPartyStoreApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 第三方场所相关接口
 * <AUTHOR>
 * @date 2024年08月12日 15:24
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingThirdPartyStoreApi", fallback = MarketThirdPartyStoreApiHystrix.class)
public interface MarketingThirdPartyStoreApi {

    /**
     * 查询门店信息
     * @param placeId 场所id，必填
     * @param poiId 抖音商户门店id，选填
     * @param accountId 抖音门店账户id，选填
     * @return
     */

    @GetMapping("/feign/marketing/marketDouyinStore/goodLifeQuery")
    GenericResponse<ListDTO<MarketDouyinStoreBO>> goodLifeQuery(@RequestParam String placeId, @RequestParam String poiId, @RequestParam(required = false) String accountId);


    /**
     * 保存门店商户信息
     * @param requestTicket
     * @param marketDouyinStoreBO
     * @return
     */
    @PostMapping("/feign/marketing/marketDouyinStore/saveMarketDouyinStore")
    GenericResponse<SimpleDTO> saveMarketDouyinStore(@RequestParam String requestTicket, @RequestBody MarketDouyinStoreBO marketDouyinStoreBO);

    @GetMapping("/feign/marketing/marketDouyinStore/queryDouYinStoreByPlaceId")
    GenericResponse<ObjDTO<MarketDouyinStoreBO>> queryDouYinStoreByPlaceId(@RequestParam String placeId);

    @GetMapping("/feign/marketing/marketDouyinStore/queryMeituanStoreByPlaceId")
    GenericResponse<ObjDTO<MeituanTokenBO>> queryMeituanStoreByPlaceId(@RequestParam String placeId);
}
