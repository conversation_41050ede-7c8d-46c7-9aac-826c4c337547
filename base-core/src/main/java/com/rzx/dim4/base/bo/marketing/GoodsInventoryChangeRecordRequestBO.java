package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品操作数目明细表
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Getter
@Setter
@ApiModel(description = "商品库存变更记录实体")
public class GoodsInventoryChangeRecordRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "变化数量")
    private int changeNumber;

    @ApiModelProperty(value = "商品成本价，单位分")
    private int costPrice;
}
