package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceMenuBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceRoleMenuApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年08月04日 13:39
 */
@Slf4j
@Service
public class PlaceRoleMenuApiHystrix implements PlaceRoleMenuApi {
    @Override
    public GenericResponse<ListDTO<PlaceMenuBO>> getPlaceRoleMenuList(String placeId, String accountId) {
        log.error("接口异常:::getPlaceRoleMenuList ,placeId:{},accountId:{}", placeId, accountId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
