package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceChainBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceChainApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 连锁组织相关接口
 * @apiNote header 参数 requestTicket 必填
 * <AUTHOR>
 * @since 2023/6/5
 **/
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceChainApi", fallback = PlaceChainApiHystrix.class)
public interface PlaceChainApi {
    String URL = "/feign/place/chain";

    /**
     * 新增连锁组织
     * @param placeChainBO 连锁组织保存对象
     */
    @PostMapping(URL + "/add")
    void createNewPlaceChain(@RequestHeader(value = "request_ticket") String requestTicket,
                             @RequestBody PlaceChainBO placeChainBO);

    @GetMapping(URL + "/newChainId")
    GenericResponse<SimpleDTO> newChainId();

    /**
     * 更新连锁组织信息
     * @param placeChainBO 连锁组织信息保存对象
     */
    @PostMapping(URL + "/update")
    void update(@RequestHeader(value = "request_ticket") String requestTicket,
                @RequestBody PlaceChainBO placeChainBO);

    /**
     * 查询连锁组织列表
     * @param dtRequest 查询对象
     * @return
     */
    @PostMapping(URL + "/list")
    GenericResponse<PagerDTO<PlaceChainBO>> page(@RequestHeader(value = "request_ticket") String requestTicket,
                                                 @RequestBody DataTablesRequest dtRequest);

    @GetMapping(URL + "/findByChainId/{chainId}")
    GenericResponse<ObjDTO<PlaceChainBO>> findByChainId(@RequestHeader(value = "request_ticket") String requestTicket,
                                                        @PathVariable String chainId);

    @PostMapping(URL + "/resetPwd")
    GenericResponse<?> resetPwd(@RequestHeader(value = "request_ticket") String requestTicket,
                                @RequestParam String chainId);

    @PostMapping(URL + "/cleanChainMemberLimit")
    GenericResponse<?> cleanChainMemberLimit(@RequestHeader(value = "request_ticket") String requestTicket,
                                @RequestParam String chainId);

    /**
     * 获取所有连锁账号
     * @return
     */
    @GetMapping(URL + "/findAllPlaceChains")
    GenericResponse<ListDTO<PlaceChainBO>> findAllPlaceChains();


    @PostMapping(URL + "/findByChainIds")
    GenericResponse<ListDTO<PlaceChainBO>> findByChainIds(@RequestBody List<String> chainIds);
}
