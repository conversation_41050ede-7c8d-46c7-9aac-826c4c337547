package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.enums.payment.PayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-07-23
 */
@Getter
@Setter
@ApiModel("租赁商品结算请求")
public class RentGoodsSettlementRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;

    @ApiModelProperty(value = "结算合计金额", required = true)
    private int totalMoney;

    @ApiModelProperty(value = "结算实际金额", required = true)
    private int realMoney;

    @ApiModelProperty(value = "结算租赁商品ID，不传代表结算全部租赁商品")
    private String rentGoodsId;

    @ApiModelProperty(value = "结算人账号ID")
    private String accountId;

    @ApiModelProperty(value = "结算人账号名称")
    private String accountName;

    @ApiModelProperty(value = "支付方式", required = true)
    private PayType payType; // 支付方式

    @ApiModelProperty(value = "付款码", required = true)
    private String payCode; // 付款码
}