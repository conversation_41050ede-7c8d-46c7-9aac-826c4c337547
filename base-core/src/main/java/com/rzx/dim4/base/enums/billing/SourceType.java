package com.rzx.dim4.base.enums.billing;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;

import java.util.*;

/**
 * 来源类型
 *
 * <AUTHOR>
 * @apiNote 新增了实例后，需要更新 {@link SourceType#map}!!! <br/>
 * 新增了第三方枚举实例后，需要更新 {@link SourceType#THIRD_SOURCE_TYPE_LIST}/!!!
 * @date 2021年9月2日 上午11:52:05
 */
public enum SourceType {
    CLIENT, // 客户端

    CASHIER, // 收银台

    WECHAT, // 微信

    ALIPAY, // 支付宝

    SYSTEM, // 系统

    MINIAPP, // 小程序

    MARKET, // 营销大师

    JWELL, // 九威

    DABAZHANG, // 大巴掌

    YISHANGWANG, // 易上网APP

    OTHER, // 其他

    MANAGEMENT, // 管理台

    PMS, // PMS电竞酒店
    LONGGUANJIA, // 龙管家

    QINGWANG, // 轻网

    RZXREALNAME, // 任子行实名

    IOT, // IOT设备/IOT小程序

    JIELA, // 杰拉

    YUNTU, // 云图电竞

    WAILIAN, // 歪脸网咖

    WANGYU, // 网鱼网咖

    QUANYOU, // 全游电竞

    WANGZHE, // 王者互娱

    WHANGYIYUN, // 网易云网咖

    DIANJING, // 电竞蜂

    YINGXING, // 银杏

    XIONGSHIYE, // 熊师爷

    DOUYIN, // 抖音
    MEITUAN, // 美团

    SHANGJITANG, // 上机堂
    ;

    private static final Map<SourceType, String> map = new EnumMap<>(SourceType.class);

    static {
        map.put(SourceType.CLIENT, "客户端");
        map.put(SourceType.CASHIER, "收银台");
        map.put(SourceType.WECHAT, "微信");
        map.put(SourceType.ALIPAY, "支付宝");
        map.put(SourceType.SYSTEM, "系统");

        map.put(SourceType.MINIAPP, "小程序");
        map.put(SourceType.MARKET, "营销大师");
        map.put(SourceType.JWELL, "九威");
        map.put(SourceType.DABAZHANG, "大巴掌");
        map.put(SourceType.YISHANGWANG, "易上网APP");

        map.put(SourceType.OTHER, "其他");
        map.put(SourceType.MANAGEMENT, "网吧管理后台");
        map.put(SourceType.PMS, "PMS电竞酒店");
        map.put(SourceType.LONGGUANJIA, "龙管家");
        map.put(SourceType.QINGWANG, "轻网");

        map.put(SourceType.RZXREALNAME, "任子行实名");
        map.put(SourceType.IOT, "IOT设备/IOT小程序");
        map.put(SourceType.JIELA, "杰拉");
        map.put(SourceType.YUNTU, "云图电竞");
        map.put(SourceType.WAILIAN, "歪脸网咖");

        map.put(SourceType.WANGYU, "网鱼网咖");
        map.put(SourceType.QUANYOU, "全游电竞");
        map.put(SourceType.WANGZHE, "王者互娱");
        map.put(SourceType.WHANGYIYUN, "网易云网咖");
        map.put(SourceType.DIANJING, "电竞蜂");
        map.put(SourceType.YINGXING, "银杏");
        map.put(SourceType.XIONGSHIYE,"熊师爷");
        map.put(SourceType.SHANGJITANG,"上机堂");
        map.put(SourceType.DOUYIN,"抖音");
        map.put(SourceType.MEITUAN,"美团");
    }

    /**
     * 获取枚举类对应的描述
     *
     * @param sourceType 枚举类
     * @return 对应描述
     */

    public static String getDesc(SourceType sourceType) {
        String s = map.get(sourceType);
        if (s == null) {
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        return s;
    }

    /**
     * 获取枚举类对应的描述
     *
     * @param sourceType 枚举类
     * @return 对应描述
     */
    public static String getDescription(SourceType sourceType) {
        return map.get(sourceType);
    }

    /**
     * 获取操作来源名称
     *
     * @param sourceType
     * @return
     * @apiNote 使用 {@link SourceType#getDesc(SourceType)} 替代
     */
    @Deprecated
    public static String getOperationName(SourceType sourceType) {
        if (sourceType.equals(SourceType.MINIAPP)) {
            return "小程序";
        } else if (sourceType.equals(SourceType.WECHAT)) {
            return "公众号";
        } else if (sourceType.equals(SourceType.YISHANGWANG)) {
            return "APP";
        } else if (sourceType.equals(SourceType.CASHIER)) {
            return "收银台";
        } else if (sourceType.equals(SourceType.CLIENT)) {
            return "客户端";
        } else if (sourceType.equals(SourceType.JWELL)) {
            return "九威";
        } else if (sourceType.equals(SourceType.MANAGEMENT)) {
            return "网吧管理后台";
        } else if (sourceType.equals(SourceType.SYSTEM)) {
            return "系统";
        } else if (sourceType.equals(SourceType.QINGWANG)) {
            return "轻网";
        } else if (sourceType.equals(SourceType.RZXREALNAME)) {
            return "任子行实名扣点";
        } else if (sourceType.equals(SourceType.IOT)) {
            return "IOT";
        } else if (sourceType.equals(SourceType.SHANGJITANG)) {
            return "上机堂";
        } else if (sourceType.equals(SourceType.DOUYIN)) {
            return "抖音";
        } else if (sourceType.equals(SourceType.MEITUAN)) {
            return "美团";
        }
        return "其他[" + sourceType.name() + "]";
    }

    /**
     * 根据字面量获取对应实例
     *
     * @param sourceTypeStr like MARKET/JWELL
     * @return
     */
    public static SourceType getByName(String sourceTypeStr) {
        for (SourceType sourceType : SourceType.values()) {
            if (sourceType.name().equals(sourceTypeStr)) {
                return sourceType;
            }
        }
        return null;
    }

    /**
     * 第三方账号对应的枚举实例列表
     */
    private static final List<SourceType> THIRD_SOURCE_TYPE_LIST = new ArrayList<>();

    static {
        THIRD_SOURCE_TYPE_LIST.add(SourceType.MARKET);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.JWELL);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.DABAZHANG);
//        THIRD_SOURCE_TYPE_LIST.add(SourceType.YISHANGWANG); // 易上网属于 V8，不属于第三方
        THIRD_SOURCE_TYPE_LIST.add(SourceType.OTHER);

        THIRD_SOURCE_TYPE_LIST.add(SourceType.PMS);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.QINGWANG);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.JIELA);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.YUNTU);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.WAILIAN);

        THIRD_SOURCE_TYPE_LIST.add(SourceType.WANGYU);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.QUANYOU);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.WANGZHE);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.WHANGYIYUN);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.DIANJING);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.YINGXING);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.XIONGSHIYE);
        THIRD_SOURCE_TYPE_LIST.add(SourceType.SHANGJITANG);
    }

    /**
     * 获取第三方账号对应的枚举实例列表
     *
     * @return
     * @apiNote 新增了第三方枚举实例后，需要更新 THIRD_SOURCE_TYPE_LIST
     */
    public static List<SourceType> getThirdSourceTypeList() {
        return THIRD_SOURCE_TYPE_LIST;
    }

    /**
     * 是否是第三方账号
     * @param sourceType
     * @return
     */
    public static boolean isThirdSourceType(SourceType sourceType) {
        return THIRD_SOURCE_TYPE_LIST.contains(sourceType);
    }

    public static boolean hasName(String name) {
        try {
            Enum.valueOf(SourceType.class, name);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
