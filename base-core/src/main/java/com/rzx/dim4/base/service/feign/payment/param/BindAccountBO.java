package com.rzx.dim4.base.service.feign.payment.param;

import com.google.gson.annotations.SerializedName;
import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/25
 **/
@EqualsAndHashCode(callSuper = false)
@Data
public class BindAccountBO extends AbstractBO {

    @SerializedName("merchant_id")
    private Integer merchantId;

    @SerializedName("store_no")
    private String storeNo;

    @SerializedName("store_name")
    private String storeName;
}
