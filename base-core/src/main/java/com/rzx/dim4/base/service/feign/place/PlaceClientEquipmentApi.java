package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.bo.place.PlaceClientEquipmentDetailBO;
import com.rzx.dim4.base.bo.place.PlaceClientEquipmentBO;
import com.rzx.dim4.base.bo.place.PlaceClientEquipmentAggregateBo;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.place.PlaceClientEquipmentApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> hwx
 * @since 2025/2/18 15:53
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceClientEquipmentApi", fallback = PlaceClientEquipmentApiHystrix.class)
public interface PlaceClientEquipmentApi {

    @GetMapping("/feign/place/placeClientEquipment/queryByPlaceIdAndEquipmentId")
    GenericResponse<ListDTO<PlaceClientEquipmentBO>> queryByPlaceIdAndClientId(@RequestParam String placeId, @RequestParam String clientId);

    /**
     * 批量删除
     *
     * @param requestTicket
     * @param placeId
     * @param equipmentIds  设备id列表
     * @return
     */
    @PostMapping("/feign/place/placeClientEquipment/batchDelete")
    GenericResponse<?> batchDelete(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam List<String> equipmentIds);

    @PostMapping("/feign/place/placeClientEquipment/batchUpdate")
    GenericResponse<?> batchUpdate(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestBody List<PlaceClientEquipmentBO> bos);

    @GetMapping("/feign/place/placeClientEquipment/detail")
    GenericResponse<ObjDTO<PlaceClientEquipmentDetailBO>> detail(@RequestParam String placeId, @RequestParam String clientId);

    /**
     * 查找PlaceClient和PlaceClientEquipment的聚合数据
     * @param placeId
     * @param equipmentType
     * @param equipmentName
     * @param areaId
     * @param clientName
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/feign/place/placeClientEquipment/pageAggregate")
    GenericResponse<PagerDTO<PlaceClientEquipmentAggregateBo>> pageAggregate(
        @RequestParam String placeId,
        @RequestParam(required = false, defaultValue = "") String equipmentType,
        @RequestParam(required = false, defaultValue = "") String equipmentName,
        @RequestParam(required = false, defaultValue = "") String areaId,
        @RequestParam(required = false, defaultValue = "") String clientName,
        @RequestParam(required = false, defaultValue = "0") int page,
        @RequestParam(required = false, defaultValue = "10") int size);
}
