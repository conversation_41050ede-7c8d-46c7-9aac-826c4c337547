package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.idauth.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.IdauthServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 认证服务 待删除，不添加新接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.IDAUTH_SERVER, contextId = "IdauthServerService", fallback = IdauthServerServiceHystrix.class)
public interface IdauthServerService {

    @GetMapping("/idauth/authRule/rules")
    public GenericResponse<PagerDTO<AuthRuleBO>> list(@RequestParam(name = "userId", required = false) String userId,
                                                      @RequestParam(name = "placeId", required = false) String placeId,
                                                      @RequestParam(name = "regionCode", required = false) String regionCode,
                                                      @RequestParam(name = "page", defaultValue = "0") int page,
                                                      @RequestParam(name = "size", defaultValue = "10") int size,
                                                      @RequestParam(name = "order", defaultValue = "desc") String order,
                                                      @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    @GetMapping("/idauth/authRule/{ruleId}")
    public GenericResponse<ObjDTO<AuthRuleBO>> getAuthRule(@PathVariable("ruleId") Long ruleId);

    @PostMapping("/idauth/authRule/save")
    public GenericResponse<ObjDTO<AuthRuleBO>> saveAuthRule(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody AuthRuleBO authRuleBO);

    @PostMapping("/idauth/authRule/delete/{authRuleId}")
    public GenericResponse<SimpleDTO> deleteAuthRule(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @PathVariable("authRuleId") Long authRuleId);

    @GetMapping("/idauth/idcard/query")
    public GenericResponse<ObjDTO<IdCardBO>> queryIdCard(@RequestParam(name = "idNumber") String idNumber);

    @GetMapping("/idauth/stats/byday")
    public GenericResponse<SimpleDTO> statsByDay(@RequestParam(name = "date") String date);

    @GetMapping("/idauth/stats/query/byday")
    public GenericResponse<PagerDTO<StatsByDayBO>> queryStatsByDay(
            @RequestParam(name = "userId", defaultValue = "") String userId,
            @RequestParam(name = "startDate", defaultValue = "") String startDate,
            @RequestParam(name = "endDate", defaultValue = "") String endDate,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    @GetMapping("/idauth/stats/query/callDetail")
    public GenericResponse<ListDTO<StatsByDayBO>> queryCallDetail(@RequestParam(name = "userId", defaultValue = "") String userId,
                                                                  @RequestParam(name = "startDate", defaultValue = "") String startDate,
                                                                  @RequestParam(name = "endDate", defaultValue = "") String endDate,
                                                                  @RequestParam(name = "authType", defaultValue = "") String authType);

    @GetMapping("/idauth/stats/query/queryCallDetailByToday")
    public GenericResponse<ListDTO<StatsByDayBO>> queryCallDetailByToday(@RequestParam(name = "userId", defaultValue = "") String userId,
                                                                         @RequestParam(name = "authType", defaultValue = "") String authType);

    @GetMapping("/idauth/stats/query/errorDetail")
    public GenericResponse<PagerDTO<StatsByDayBO>> queryErrorDetail(@RequestParam(name = "userId", defaultValue = "") String userId,
                                                                    @RequestParam(name = "startDate") String startDate,
                                                                    @RequestParam(name = "endDate") String endDate,
                                                                    @RequestParam(name = "authType", defaultValue = "") String authType,
                                                                    @RequestParam(name = "curPage", defaultValue = "0") int curPage,
                                                                    @RequestParam(name = "pageSize", defaultValue = "10") int pageSize);

    @GetMapping("/idauth/stats/query/queryErrorDetailByToday")
    public GenericResponse<PagerDTO<StatsByDayBO>> queryErrorDetailByToday(@RequestParam(name = "userId", defaultValue = "") String userId,
                                                                           @RequestParam(name = "authType", defaultValue = "") String authType,
                                                                           @RequestParam(name = "curPage", defaultValue = "0") int curPage,
                                                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize);

    // ------------------------------------实名认证服务----------------------------------------------//

    @PostMapping("/idauth/auth/3e")
    public GenericResponse<?> auth3e(@RequestHeader(value = "Authorization") String authorization,
                                     @RequestParam(name = "sign") String sign,
                                     @RequestParam(name = "idNumber") String idNumber,
                                     @RequestParam(name = "name") String name,
                                     @RequestParam(name = "facePhotoBase64") String facePhotoBase64);

    @PostMapping("/idauth/auth/2e")
    public GenericResponse<?> auth2e(@RequestHeader(value = "Authorization") String authorization,
                                     @RequestParam(name = "sign") String sign,
                                     @RequestParam(name = "idNumber") String idNumber,
                                     @RequestParam(name = "name") String name);

    @GetMapping("/idauth/auth/server/checkRealnameAuth")
    public GenericResponse<?> checkRealnameAuth(@RequestParam(name = "idNumber") String idNumber,
                                                @RequestParam(name = "mins") int mins);

    @PostMapping(value = "/idauth/auth/liveness/check", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public GenericResponse<SimpleDTO> livenessCheck(@RequestHeader(value = "Authorization") String authorization,
                                                    @RequestParam(name = "sign") String sign,
                                                    @RequestPart(value = "video") MultipartFile video);

    // ------------------------------------用户相关----------------------------------------------//
    @GetMapping("/idauth/user/{userId}")
    public GenericResponse<ObjDTO<UserBO>> getUser(@PathVariable("userId") String userId);

    @PostMapping("/idauth/user/save")
    public GenericResponse<ObjDTO<UserBO>> saveUser(@RequestHeader(value = "request_ticket") String requestTicket,
                                                    @RequestBody UserBO userBO);

    @GetMapping("/idauth/user/key/{userId}")
    public GenericResponse<ObjDTO<UserKeyBO>> getKey(@PathVariable("userId") String userId);

    @PostMapping("/idauth/user/key/reset")
    public GenericResponse<SimpleDTO> resetKey(@RequestHeader(value = "request_ticket") String requestTicket,
                                               @RequestParam(value = "userId") String userId);

    @GetMapping("/idauth/user/users/fuzzy")
    public GenericResponse<PagerDTO<UserBO>> fuzzy(@RequestParam(name = "search", defaultValue = "") String search,
                                                   @RequestParam(name = "page", defaultValue = "0") int page,
                                                   @RequestParam(name = "size", defaultValue = "10") int size,
                                                   @RequestParam(name = "order", defaultValue = "desc") String order,
                                                   @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    @GetMapping("/idauth/user/findAll")
    public GenericResponse<ListDTO<UserBO>> findUsers();

    @GetMapping("/idauth/user/access/list")
    public List<UserAccessBO> getUserAccessList(@RequestParam(name = "userId", required = true) String userId);

    @PostMapping("/idauth/user/access/save")
    public GenericResponse<SimpleDTO> saveUserAccess(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @RequestBody UserAccessBO userAccessBO);

    @PostMapping("/idauth/user/access/delete")
    public GenericResponse<SimpleDTO> deleteUserAccess(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestBody UserAccessBO userAccessBO);

    @PostMapping("/idauth/user/access/accessList/save")
    public GenericResponse<SimpleDTO> saveAccessList(@RequestHeader(value = "request_ticket") String requestTicket,
                                                     @RequestBody AccessListBO accessListBO);

    @GetMapping("/idauth/user/access/accessList")
    public GenericResponse<ObjDTO<AccessListBO>> getAccessList(@RequestParam(name = "userId", required = true) String userId);
}
