package com.rzx.dim4.base.dto;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 只有一个list类型的BO
 * 
 * <AUTHOR>
 * @date 2019年9月24日下午5:35:11
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ListDTO<T extends AbstractBO> extends AbstractDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -620992607538903725L;
	/**
	 * 数据列表
	 */
	List<T> list;

}
