package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class MiniAppOrderQueryBO {

    @ApiModelProperty(value = "场所id",required = true)
    private String placeId;
    @ApiModelProperty(value = "来源类型")
    private SourceType sourceType;
    @ApiModelProperty(value = "支付方式")
    private PayType payType;
    @ApiModelProperty(value = "订单状态，0已创建，1已支付，2已派送，3已完成，4部分退款 ,5 已退款，6已取消")
    private Integer status;
    @ApiModelProperty(value = "日期类型，1 下单时间")
    private Integer queryDateType;
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    @ApiModelProperty(value = "结束日期")
    private String endDate;
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    @ApiModelProperty(value = "机器号")
    private String clientName;
    @ApiModelProperty(value = "创建人")
    private String createrName;
    @ApiModelProperty(value = "订单类型1 商品订单，2团购订单，3网费充值订单，4包时订单，9自定义收款")
    private String orderType;
    @ApiModelProperty(value = "页数，从0开始")
    private Integer page;
    @ApiModelProperty(value = "每页条数")
    private Integer size;

}
