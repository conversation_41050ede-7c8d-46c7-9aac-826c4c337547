package com.rzx.dim4.base.service.feign.place;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.service.callback.place.PlaceChainApiHystrix;
import com.rzx.dim4.base.service.callback.place.StatisticsClientByDayApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2024年09月10日 11:35
 */
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "StatisticsClientByDayApi", fallback = StatisticsClientByDayApiHystrix.class)
public interface StatisticsClientByDayApi {

    String URL = "/feign/place/statisticsClient";


    @GetMapping(URL + "/schedulerStatistics")
    public void schedulerStatisticsPlaceClientByDay(@RequestParam(required = false,defaultValue = "1")int dayAgo);
}
