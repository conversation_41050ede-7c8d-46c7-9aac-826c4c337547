package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingServerServiceHystrix;
import com.rzx.dim4.base.service.feign.billing.param.LogOperationParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 操作记录
 * <AUTHOR>
 * @version v1.0
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "LogOperation", fallback = BillingServerServiceHystrix.class)
public interface LogOperationApi {

    String URL="/billing/admin/logOperation";

    @GetMapping(URL+"/queryLogOperationCheckoutPage")
    GenericResponse<PagerDTO<LogOperationBO>> queryLogOperationCheckoutPage(@SpringQueryMap LogOperationParam logOperationParam, Pageable pageable);
}
