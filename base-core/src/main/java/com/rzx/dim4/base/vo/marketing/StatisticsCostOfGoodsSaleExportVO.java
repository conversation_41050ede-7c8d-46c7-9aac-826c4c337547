package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR> hwx
 * @Description TODO
 * @Date 2025/2/6 9:17
 */
@Data
public class StatisticsCostOfGoodsSaleExportVO {

    @ExcelProperty(value = "商品名称", index = 0)
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "期初库存", index = 1)
    @ColumnWidth(15)
    private int openingStock;

    @ExcelProperty(value = "本期采购", index = 2)
    @ColumnWidth(15)
    private int purchase;

    @ExcelProperty(value = "本期销售", index = 3)
    @ColumnWidth(15)
    private int sales;

    @ExcelProperty(value = "盘点", index = 4)
    @ColumnWidth(15)
    private int stocktaking;

    @ExcelProperty(value = "退货", index = 5)
    @ColumnWidth(15)
    private int returns;

    @ExcelProperty(value = "报损", index = 6)
    @ColumnWidth(15)
    private int losses;

    @ExcelProperty(value = "期末库存", index = 7)
    @ColumnWidth(15)
    private int closingStock;
}
