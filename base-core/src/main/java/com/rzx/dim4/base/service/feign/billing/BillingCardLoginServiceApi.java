package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.BillingCardLoginServiceApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 计费卡登录相关逻辑
 *
 * <AUTHOR>
 * @since 2023/10/26
 **/
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "BillingCardLoginServiceApi", fallback = BillingCardLoginServiceApiHystrix.class)
public interface BillingCardLoginServiceApi {

    String URL = "/feign/billing/card/login";

    /**
     * 校验是否允许在客户端直接扫码激活登录
     *
     * @param placeId  场所id
     * @param idNumber 身份证号
     * @param cardId   计费卡号
     * @return
     * @apiNote placeId必填，idNumber/cardId 二选一
     */
    @GetMapping(URL + "/checkForbiddenClientActiveDirectly")
    GenericResponse<?> checkForbiddenClientActiveDirectly(@RequestHeader(value = "request_ticket") String requestTicket,
                                                          @RequestParam String placeId,
                                                          @RequestParam(required = false) String idNumber,
                                                          @RequestParam(required = false) String cardId);
}
