package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 营销商超相关接口
 *
 * <AUTHOR>
 * @date 2025年02月20日 14:42
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsApi", fallback = MarketingGoodsApiHystrix.class)
public interface MarketingGoodsApi {

    final String URL = "/feign/marketing/goods";

    /**
     * 查询场所下所有的商品图片信息
     */
    @GetMapping(value = URL + "/picture/all/list")
    GenericResponse<ListDTO<GoodsPictureBO>> findAllGoodsPictures(@RequestParam(name = "placeId") String placeId);

    /**
     * 获取商品类型
     *
     * @return
     */
    @PostMapping(value = URL + "/getGoodsTypeList")
    GenericResponse<ListDTO<GoodsTypeBO>> getGoodsTypeList(@RequestParam String placeId);

    /**
     * 商品列表
     *
     * @param
     * @return
     */
    @PostMapping(value = URL + "/getGoodsPageList")
    GenericResponse<PagerDTO<GoodsBO>> getGoodsPageList(@RequestParam int pageSize,
                                                        @RequestParam int pageStart,
                                                        @RequestParam(required = false, defaultValue = "") String goodsTypeId,
                                                        @RequestParam(required = false, defaultValue = "") String goodsName,
                                                        @RequestParam String placeId,
                                                        @RequestParam(required = false, defaultValue = "") String idNumber,
                                                        @RequestParam(required = false, defaultValue = "") String areaId,
                                                        @RequestParam(required = false, defaultValue = "") String showClientSwitch);

    /**
     * 商品列表
     *
     * @param
     * @return
     */
    @GetMapping(value = URL + "/getMiniAppGoodsPageList")
    GenericResponse<PagerDTO<GoodsBO>> getMiniAppGoodsPageList(@RequestParam int size,
                                                               @RequestParam int page,
                                                               @RequestParam(required = false, defaultValue = "") String goodsTypeId,
                                                               @RequestParam(required = false, defaultValue = "") String goodsName,
                                                               @RequestParam String placeId);

    //保存商品,暂时只处理了包时商品后续复用需要完善接口
    @PostMapping(value = URL + "/saveGoods")
    GenericResponse<ObjDTO<GoodsBO>> saveGoods(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody GoodsBO goodsBO);


    @PostMapping(value = URL + "/batchSaveSuppliers")
    GenericResponse<ListDTO<GoodsSuppliersBO>> batchSaveSuppliers(@RequestBody List<GoodsSuppliersBO> suppliersBOS);

    @PostMapping(value = URL + "/saveGoodsSuppliers")
    GenericResponse<ObjDTO<GoodsSuppliersBO>> saveGoodsSuppliers(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody @Validated GoodsSuppliersBO goodsSuppliersBO);

    @PostMapping(value = URL + "/findDetails")
    GenericResponse<ListDTO<GoodsBO>> querySupplyGoodsDetails(@RequestParam String placeId, @RequestParam String supplierId);


    @GetMapping(value = URL + "/findPageList")
    GenericResponse<PagerDTO<GoodsSuppliersBO>> pageGoodsSuppliers(@RequestParam(name = "placeId") String placeId,
                                                                   @RequestParam(name = "search", defaultValue = "") String search,
                                                                   @RequestParam(name = "start", defaultValue = "0") int page,
                                                                   @RequestParam(name = "size", defaultValue = "10") int size);

    @PostMapping(value = URL + "/batchSaveGoods")
    GenericResponse<ListDTO<GoodsBO>> batchSaveGoods(@RequestBody List<GoodsBO> goodsBOS);

    /**
     * 查询商品列表
     *
     * @param placeId
     * @param goodsId
     * @return
     */
    @GetMapping(value = URL + "/findByPlaceIdAndGoodsId")
    GenericResponse<ObjDTO<GoodsBO>> findByPlaceIdAndGoodsId(@RequestParam String placeId, @RequestParam String goodsId);

    @GetMapping(value = URL + "/findByPlaceIdAndGoodsIdIn")
    GenericResponse<ListDTO<GoodsBO>> findByPlaceIdAndGoodsIdIn(@RequestParam String placeId, @RequestParam List<String> goodsIds);

    @GetMapping(value = URL + "/findTopupPageListForMiniApp")
    GenericResponse<ListDTO<GoodsMiniAppBO>> findTopupPageListForMiniApp(@RequestParam(name = "placeId") String placeId,
                                                                         @RequestParam(name = "cardTypeId") String cardTypeId,
                                                                         @RequestParam(name = "idNumber") String idNumber);

    @GetMapping(value = URL + "/findTopupPageList")
    GenericResponse<ListDTO<GoodsBO>> findTopupPageList(@RequestParam(name = "placeId") String placeId,
                                                        @RequestParam(name = "cardTypeId") String cardTypeId,
                                                        @RequestParam(name = "idNumber") String idNumber,
                                                        @RequestParam(name = "areaId", required = false) String areaId,
                                                        @RequestParam(name = "sourceType") SourceType sourceType,
                                                        @RequestParam(name = "size", defaultValue = "100") int size,
                                                        @RequestParam(name = "start", defaultValue = "1") int start);

    @GetMapping(value = URL + "/miniAppFindTopupPageList")
    GenericResponse<ListDTO<GoodsBO>> miniAppFindTopupPageList(@RequestParam(name = "placeId") String placeId,
                                                               @RequestParam(name = "cardTypeId") String cardTypeId,
                                                               @RequestParam(name = "idNumber") String idNumber,
                                                               @RequestParam(name = "areaId", required = false) String areaId,
                                                               @RequestParam(name = "sourceType") SourceType sourceType,
                                                               @RequestParam(name = "size", defaultValue = "100") int size,
                                                               @RequestParam(name = "page", defaultValue = "0") int page);


    @PostMapping(URL + "/findFixGoodsPageList")
    GenericResponse<PagerDTO<GoodsBO>> pageFixGoods(@RequestBody @Validated FixGoodsQueryBO queryBO);


    @GetMapping(value = URL + "/joinChainUpdateCardTypeIds")
    void joinChainUpdateCardTypeIds(@RequestParam(name = "placeId") String placeId,
                                    @RequestParam(name = "cardTypeIds") String cardTypeIds);
}
