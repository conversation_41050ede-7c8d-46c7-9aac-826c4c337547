package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 新增入库记录请求对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ToString
public class GoodsInventoryRecordQueryRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "商品ID", required = true)
    private String goodsId;

    @ApiModelProperty(value = "变动方式:0-入库，1-上下架，2-盘点，3-调出，4-调入，5-退货，6-报损，7-销售，8-派奖，其他情况查全部")
    private String changeRecordType;

    @ApiModelProperty(value = "操作批次ID")
    private String changeRecordId;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "当前页（从0开始，异常情况重置为0）")
    private Integer page;

    @ApiModelProperty(value = "每页大小（10-100,不在此范围内，重置为10）")
    private Integer size;
}
