package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

@Getter
@Setter
@XmlRootElement(name = "rate_fix")
@XmlAccessorType(XmlAccessType.NONE)
public class BillingRulePackageXml {

    @XmlElement(name = "name")
    private String ruleName; // 规则名称

    /**
     * 类型
     * 包时段类型收银台以二进制存储分为 3 位,限制结束时间为高位,时间范围内生效为中位,具体类型为低位
     */
    @XmlElement(name = "type")
    private Byte type;

    /**
     * 费用xml解析结果 (单位元)
     */
    @XmlElement(name = "cost")
    private String costXml;

    /**
     * 时间段
     */
    @XmlElement(name = "time_length")
    private Integer timeLength;
    /**
     * 开始时间
     */
    @XmlElement(name = "rate_fix_start_time")
    private Integer startTime;
    /**
     * 结束时间
     */
    @XmlElement(name = "rate_fix_end_time")
    private Integer endTime;

    /**
     * 卡类型名称集合
     * 用于解析xml
     */
    @XmlElementWrapper(name = "rate_fix_card_names")
    @XmlElement(name = "rate_fix_card_name")
    private List<String> cardNames;

    /**
     * 区域名称集合
     * 用于解析xml
     */
    @XmlElementWrapper(name = "rate_fix_area_names")
    @XmlElement(name = "rate_fix_area_name")
    private List<String> areaNames;

}
