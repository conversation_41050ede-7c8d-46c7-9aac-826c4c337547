package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsMoveRecordApi;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsStockingApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025年02月20日 14:43
 */
@Slf4j
@Component
public class MarketingGoodsStockingApiHystrix implements MarketingGoodsStockingApi {


    @Override
    public GenericResponse<ListDTO<GoodsStocktakingBO>> saveGoodsStocktaking(String requestTicket, SaveGoodsStockingAddBO addBO) {
        log.error("接口异常，MarketingGoodsStockingApiHystrix.saveGoodsStocktaking，参数信息：addBO={}",
                new Gson().toJson(addBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsStocktakingBO>> findGoodsStocktakingDetails(String placeId, String stocktakingNum) {
        log.error("接口异常，MarketingGoodsStockingApiHystrix.findGoodsStocktakingDetails，参数信息：placeId={},stocktakingNum={}",
                placeId, stocktakingNum);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsStocktakingBO>> findPageList(String placeId, String startDate, String endDate, String storageRackId, int page, int size) {
        log.error("接口异常，MarketingGoodsStockingApiHystrix.findPageList，参数信息：placeId={},startDate={},endDate={},storageRackId={},page={},size={}",
                placeId, startDate, endDate, storageRackId, page, size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MiniAppGoodsStocktakingBO>> staticLossOrOverFlowTotal(String placeId, String startDate, String endDate, String storageRackId) {
        log.error("接口异常，MarketingGoodsStockingApiHystrix.staticLossOrOverFlowTotal，参数信息：placeId={},startDate={},endDate={},storageRackId={}",
                placeId, startDate, endDate, storageRackId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
