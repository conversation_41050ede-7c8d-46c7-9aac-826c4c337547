package com.rzx.dim4.base.service.callback.notify;

import com.rzx.dim4.base.bo.notify.region.RegionCodeBO;
import com.rzx.dim4.base.bo.notify.region.RegionCountryBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.notify.RegionServerApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/5/21
 **/
@Slf4j
@Service
public class RegionServerApiHystrix implements RegionServerApi {
    @Override
    public GenericResponse<ObjDTO<RegionCodeBO>> regionCode(String requestTicket, String countryId) {
        log.error("接口异常:::regionCode():::countryId:{}", countryId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<RegionCodeBO>> regionCodes(String requestTicket,
                                                              Set<String> countryIds) {
        log.error("接口异常:::regionCodes():::countryIds:{}", countryIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

	@Override
	public GenericResponse<ObjDTO<RegionCountryBO>> regionCountry(String requestTicket, String countryId) {
        log.error("接口异常:::regionCode():::countryId:{}", countryId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}
}
