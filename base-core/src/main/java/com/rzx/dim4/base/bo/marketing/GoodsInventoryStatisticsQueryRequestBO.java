package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 库存统计请求对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ToString
public class GoodsInventoryStatisticsQueryRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "商品名称或条形码")
    private String searchKey;

    @ApiModelProperty(value = "商品类型ID")
    private String goodsTypeId;

    @ApiModelProperty(value = "供应商ID")
    private String supplierId;

    @ApiModelProperty(value = "库存状态：0:已预警, 1:缺货, 其他情况查全部")
    private String storageNumType;
}
