package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.StorageGoodsBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingStorageGoodsApi;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> hwx
 * @since 2025/2/27 14:56
 */
@Slf4j
@Component
public class MarketingStorageGoodsApiHystrix implements MarketingStorageGoodsApi {
    @Override
    public GenericResponse<ListDTO<StorageGoodsBO>> findStorageRackByPlaceId(String placeId) {
        log.error("接口异常:::MarketingStorageGoodsApiHystrix#findStorageRackByPlaceId(placeId:::{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
