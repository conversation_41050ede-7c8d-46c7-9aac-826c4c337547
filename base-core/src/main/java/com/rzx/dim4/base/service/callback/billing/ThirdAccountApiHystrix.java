package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.billing.ThirdAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.ThirdAccountApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/3/22
 **/
@Slf4j
@Service
public class ThirdAccountApiHystrix implements ThirdAccountApi {
    @Override
    public GenericResponse<ListDTO<ThirdAccountBO>> all(String requestTicket) {
        log.error("接口异常:::all(requestTicket:::{})", requestTicket);

        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    /**
     * 只返回可用的第三方账号
     *
     * @param requestTicket
     * @return
     */
    @Override
    public GenericResponse<ListDTO<ThirdAccountBO>> listCanUse(String requestTicket) {
        log.error("接口异常:::all(requestTicket:::{})", requestTicket);

        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<ThirdAccountBO>> findByThirdAccountIds(String requestTicket, List<String> thirdAccountIds) {
        log.error("接口异常:::findByPlaceIds(requestTicket:::{},thirdAccountIds:::{})", requestTicket, thirdAccountIds);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<ThirdAccountBO>> findByPlaceId(String requestTicket, String placeId) {
        log.error("接口异常:::findByPlaceIds(requestTicket:::{},placeId:::{})", requestTicket, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
