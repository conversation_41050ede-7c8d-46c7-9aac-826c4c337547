package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.PackageTimeReserveBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveQueryBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveStatusBO;
import com.rzx.dim4.base.bo.billing.PackageTimeReserveUpdateStatusBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PackageTimeReserveApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023年10月25日 18:40
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "PackageTimeReserveApi", fallback = PackageTimeReserveApiHystrix.class)
public interface PackageTimeReserveApi {

    String URL = "/feign/packageTimeReserve";

    @GetMapping(URL + "/queryByPlaceIdAndCardId")
    GenericResponse<ObjDTO<PackageTimeReserveBO>> queryByPlaceIdAndCardId(@RequestParam String placeId, @RequestParam String cardId);

    @GetMapping(URL + "/queryByPlaceIdAndCardIdAndOrderId")
    GenericResponse<ObjDTO<PackageTimeReserveBO>> queryByPlaceIdAndCardIdAndOrderId(@RequestParam String placeId, @RequestParam String cardId, @RequestParam String orderId);

    @PostMapping(URL + "/queryPackageTimeStatus")
    GenericResponse<ObjDTO<PackageTimeReserveStatusBO>> queryPackageTimeStatus(@RequestBody PackageTimeReserveStatusBO packageTimeReserveStatusBo);

    @PostMapping(URL + "/updatePackageTimeStatus")
    GenericResponse<SimpleDTO> updatePackageTimeStatus(@RequestBody PackageTimeReserveUpdateStatusBO packageTimeReserveUpdateStatusBo);

    @PostMapping(URL + "/getBatchPackageTimeStatus")
    GenericResponse<ObjDTO<PackageTimeReserveQueryBO>> getBatchPackageTimeStatus(@RequestBody PackageTimeReserveQueryBO params);
}
