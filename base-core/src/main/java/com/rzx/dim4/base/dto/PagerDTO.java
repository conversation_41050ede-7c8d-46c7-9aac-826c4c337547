package com.rzx.dim4.base.dto;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 分页的BO
 * 
 * <AUTHOR>
 * @date 2019年10月12日上午10:26:07
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PagerDTO<T extends AbstractBO> extends ListDTO<T> implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8306829998185312252L;
	/**
	 * 总记录数
	 */
	int total;

	public PagerDTO(int total, List<T> list) {
		this.total = total;
		this.list = list;
	}
}
