package com.rzx.dim4.base.service.callback.billing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.ExchangeComputerRequestBO;
import com.rzx.dim4.base.bo.billing.GetOffComputerRequestBO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceClientsBillingApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PlaceClientsBillingApiHystrix implements PlaceClientsBillingApi {
    @Override
    public GenericResponse<SimpleDTO> exchangeComputer(ExchangeComputerRequestBO paramsBo) {
        log.info("接口异常:::PlaceClientsBillingApiHystrix.exchangeComputer({})", new G<PERSON>().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> getOffComputer(GetOffComputerRequestBO paramsBo) {
        log.info("接口异常:::PlaceClientsBillingApiHystrix.getOffComputer({})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
