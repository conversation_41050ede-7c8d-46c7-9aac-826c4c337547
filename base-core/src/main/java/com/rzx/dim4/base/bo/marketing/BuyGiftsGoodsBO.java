package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 买赠赠送商品信息表
 * <AUTHOR>
 * @date 2024年12月05日
 */
@Getter
@Setter
@ToString
public class BuyGiftsGoodsBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String buyGiftsId; // 买赠id

    private String goodsId; // 商品id

    private String goodsName; // 商品名称

    private int goodsPrice; // 赠送商品金额

    private int goodsNum; // 赠送数量

    private String goodsPic; // 赠送商品图片
    private int goodsStocksNum; // 库存数量，最小值0

}

