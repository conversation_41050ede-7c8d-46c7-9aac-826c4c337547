package com.rzx.dim4.base.service.callback.payment;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.payment.LdopayConfigApi;
import com.rzx.dim4.base.service.feign.payment.param.BindAccountBO;
import com.rzx.dim4.base.service.feign.payment.param.LdopayInfoBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/8/24
 **/
@Slf4j
@Service
public class LdopayConfigApiHystrix implements LdopayConfigApi {

    @Override
    public GenericResponse<ObjDTO<LdopayInfoBO>> queryAccount(String requestTicket, String placeId, String mobile) {
        log.error("接口异常:::queryAccount(requestTicket:::{},placeId:::{},mobile:::{})", requestTicket, placeId, mobile);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<BindAccountBO>> bind(String requestTicket, String placeId, String merUsername) {
        log.error("接口异常:::bind(requestTicket:::{},placeId:::{},merUsername:::{})", requestTicket, placeId, merUsername);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
