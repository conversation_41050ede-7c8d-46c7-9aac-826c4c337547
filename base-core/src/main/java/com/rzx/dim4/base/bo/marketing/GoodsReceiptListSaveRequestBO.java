package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 新增入库记录中的商品信息请求对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ApiModel("新增入库记录中的商品信息请求对象")
public class GoodsReceiptListSaveRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "商品id", required = true)
    private String goodsId;

    @ApiModelProperty(value = "商品数量", required = true)
    private int number;

    @ApiModelProperty(value = "发生单价", required = true)
    private int price;

    @ApiModelProperty(value = "是否赠品: 0否，1是（默认0）", required = true)
    private int isGift;
}
