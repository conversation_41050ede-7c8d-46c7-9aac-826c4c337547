package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.PlacementVO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.PlaceBizConfigApiHystrix;
import com.rzx.dim4.base.service.callback.billing.PlacementApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月10日 11:29
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "PlacementApi", fallback = PlacementApiHystrix.class)
public interface PlacementApi {

    String URL = "/feign/placement";

    @GetMapping(URL + "/findPlacementByPlaceId")
    public GenericResponse<ListDTO<PlacementVO>> findPlacementByPlaceId(@RequestParam String placeId);


}
