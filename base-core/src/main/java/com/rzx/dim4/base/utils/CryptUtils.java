package com.rzx.dim4.base.utils;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

public class CryptUtils {

    /**
     * hash 次数
     */
    public static final int CYCLES = 10;

    /**
     * 消息摘要算法名称：SHA1
     */
    public static final String DIGEST_SHA1 = "SHA1";

    /**
     * 消息摘要算法名称：MD5
     */
    public static final String DIGEST_MD5 = "MD5";

    /**
     * 简易加密
     *
     * @param bytes
     * @return
     */
    public static String generateDigestWithMD5NoSalt(byte[] bytes) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            byte[] md5Bytes = messageDigest.digest(bytes);
            StringBuffer hexValue = new StringBuffer(32);
            for (int i = 0; i < md5Bytes.length; i++) {
                int val = ((int) md5Bytes[i]) & 0xff;
                if (val < 16) {
                    hexValue.append("0");
                }
                hexValue.append(Integer.toHexString(val));
            }
            return hexValue.toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取文件hash值
     *
     * @param file
     * @return
     */
    public static String generateFileMD5(File file) throws IOException {
        byte[] bytes = Files.readAllBytes(file.toPath());
        return generateDigestWithMD5NoSalt(bytes);
    }


    /**
     * HmacSHA256加密
     *
     * @param plainText 待加密的字符串
     * @param key       加密密钥
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static byte[] encodeByHmacSHA256(String plainText, String key) throws NoSuchAlgorithmException, InvalidKeyException {
        if (StringUtils.isBlank(plainText) || StringUtils.isBlank(key)) {
            throw new ServiceException(ServiceCodes.EMPTY_DATA);
        }

        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        return mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 获取文件MD5值
     */
    public static String generateFileMD5(String filePath) {
        try {
            // 1. 校验 URL 格式
            if (!filePath.startsWith("http://") && !filePath.startsWith("https://")) {
                filePath = "http://" + filePath; // 自动补全协议头
            }
            filePath = filePath.replace("\\", "/").trim(); // 替换路径中的反斜杠

            // 2. 打开 URL 连接
            URL url = new URL(filePath);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 设置连接超时时间
            connection.setReadTimeout(30000);   // 设置读取超时时间

            // 3. 检查 HTTP 状态码
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new RuntimeException("HTTP request failed with code: " + responseCode);
            }

            // 4. 获取输入流并计算 MD5
            try (InputStream inputStream = connection.getInputStream()) {
                MessageDigest md = MessageDigest.getInstance(DIGEST_MD5);
                byte[] buffer = new byte[8192];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    md.update(buffer, 0, bytesRead); // 流式更新 MD5
                }

                byte[] digest = md.digest();
                StringBuilder hexString = new StringBuilder();
                for (byte b : digest) {
                    String hex = Integer.toHexString(0xff & b);
                    if (hex.length() == 1) {hexString.append('0');}
                    hexString.append(hex);
                }
                return hexString.toString();
            } finally {
                connection.disconnect(); // 关闭连接
            }
        } catch (Exception e) {
            System.out.println("读取" + filePath + "失败, 异常信息：" + e.getMessage());
                return null;
        }
    }
}
