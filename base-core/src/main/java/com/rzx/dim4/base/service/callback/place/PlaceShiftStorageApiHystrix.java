package com.rzx.dim4.base.service.callback.place;

import com.rzx.dim4.base.bo.place.PlaceShiftStorageBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceShiftStorageApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> hwx
 * @since 2025/3/3 9:43
 */
@Slf4j
@Service
public class PlaceShiftStorageApiHystrix implements PlaceShiftStorageApi {
    @Override
    public GenericResponse<ListDTO<PlaceShiftStorageBO>> findAllByPlaceIdAndShiftId(String placeId, String shiftId) {
        log.error("接口异常:::submit(),placeId:{},shiftId:{}", placeId, shiftId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> clearLatest(String accountId, String placeId) {
        log.error("接口异常:::clearLatest(),accountId:{},placeId:{}", accountId, placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
