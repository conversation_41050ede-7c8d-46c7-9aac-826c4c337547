package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.LogShiftBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.billing.LogShiftApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR> hwx
 * @since 2025/2/26 15:44
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "LogShiftApi", fallback = LogShiftApiHystrix.class)
public interface LogShiftApi {

    @PostMapping("/feign/billing/logShift/save")
    GenericResponse<?> save(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody LogShiftBO logShiftBo);
}
