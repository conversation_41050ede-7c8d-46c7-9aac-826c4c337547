package com.rzx.dim4.base.xml.billing;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@XmlRootElement(name = "rate_auto")
@XmlAccessorType(XmlAccessType.NONE)
@ToString
public class BillingRuleAccXml {

    @XmlElement(name = "rate_auto_rate_text")
    private String rateText; // 7*24小时格式

    @XmlElement(name = "rate_auto_area_name")
    private String areaName;

    @XmlElement(name = "rate_auto_card_type_name")
    private String cardTypeName;

}
