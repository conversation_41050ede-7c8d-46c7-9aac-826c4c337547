package com.rzx.dim4.base.service.feign.billing;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingServerServiceHystrix;
import com.rzx.dim4.base.service.feign.billing.param.LogLoginQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 *  登录操作日志
 * <AUTHOR>
 */
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "LogLoginClient", fallback = BillingServerServiceHystrix.class)
public interface LogLoginApi {

    String LOGIN_LOGIN_URL = "/billing/admin/logLogin";

    /**
     *  分页查询
     *
     * @param map  分页可以为空
     * @return
     */
    @PostMapping(value=LOGIN_LOGIN_URL + "/queryLogLoginPage")
    GenericResponse<PagerDTO<LogLoginBO>> queryLogLoginPage(@RequestBody Map<String, Object> map,
                                                            @RequestParam(name = "size", defaultValue = "10") int size,
                                                            @RequestParam(name = "page", defaultValue = "1") int page,
                                                            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                            @RequestParam(name = "order", defaultValue = "desc") String order);

    /**
     * 查询
     * @param logLoginParam
     * @return
     */
    @PostMapping(value=LOGIN_LOGIN_URL + "/queryLogLogin")
    GenericResponse<ListDTO<LogLoginBO>> queryLogLogin(@RequestParam LogLoginQuery logLoginParam);

    @GetMapping(value=LOGIN_LOGIN_URL + "/findByPlaceIdAndLoginIdIn")
    GenericResponse<ListDTO<LogLoginBO>> findByPlaceIdAndLoginIdIn(@RequestParam String placeId, @RequestParam List<String> loginIds);
    @GetMapping(value=LOGIN_LOGIN_URL + "/checkoutLogout")
    GenericResponse<?> checkoutLogout(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam List<String> cardIds);

    /**
     * 查询最新一条上机记录(包含在线、不在线)
     * @param requestTicket
     * @param placeId
     * @param idNumber
     * @return
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/findLastLoginByIdNumberAndPlaceId")
    GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceId(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String idNumber);

    /**
     * 查询已上机的一条记录
     * @param placeId
     * @param idNumber
     * @return
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNull")
    GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNull(@RequestParam String placeId, @RequestParam String idNumber);

    /**
     * 查询已结账的最新一条上机记录
     * @param placeId
     * @param idNumber
     * @return
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNotNull")
    GenericResponse<ObjDTO<LogLoginBO>> findLastLoginByIdNumberAndPlaceIdAndLogoutTimeIsNotNull(@RequestParam List<String> placeIds, @RequestParam String idNumber);


    /**
     * 查询 dayAgo 天的上机信息
     * @param placeId
     * @param dayAgo
     * @return
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/queryEverydayClientOnlineTime")
    List<Map<String, String>> queryEverydayClientOnlineTime(@RequestParam String placeId, @RequestParam int dayAgo);

    @GetMapping(value=LOGIN_LOGIN_URL + "/queryLastLoginUserByDayAgo")
    List<BillingCardBO> queryLastLoginUserByDayAgo(@RequestParam String placeId, @RequestParam int dayAgo, @RequestParam String cardTypeIds);

    /**
     * 查询上一次结账时间
     * @param placeId
     * @param cardIds
     * @return
     */
    @PostMapping(value=LOGIN_LOGIN_URL + "/queryLastLogOutTime")
    GenericResponse<ListDTO<LogLoginBO>> queryLastLogOutTime (@RequestParam String placeId, @RequestParam List<String> cardIds);

    /**
     * 查询用户最后一次上机记录
     * @param idNumber
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/queryLastLoginRecord")
    GenericResponse<ObjDTO<LogLoginBO>> queryLastLoginRecord(@RequestParam String idNumber);


    /**
     * 查询用户上机时长
     * @param idNumber
     */
    @GetMapping(value=LOGIN_LOGIN_URL + "/sumOnlineTimeByPlaceAndIdNumber")
    Integer sumOnlineTimeByPlaceAndIdNumber(@RequestParam String placeId, @RequestParam String idNumber, @RequestParam LocalDateTime startDate, @RequestParam LocalDateTime endDate);
}
