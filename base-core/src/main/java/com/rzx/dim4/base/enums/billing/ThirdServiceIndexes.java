package com.rzx.dim4.base.enums.billing;

import java.util.ArrayList;
import java.util.List;

/**
 * 第三方接口与接口序号对应枚举类
 *
 * @see com.rzx.dim4.billing.web.controller.ThirdCoreController
 */
public enum ThirdServiceIndexes {

    QueryMember(256, "第三方查询会员信息"),
    QueryOnline(257, "第三方查询用户在线信息"),
    Topup(258, "第三方充值"),
    Deduction(259, "第三方扣款"),
    QueryCardTypes(260, "第三方查询卡类型列表"),

    CreateCard(261, "第三方开卡"),
    ActivationCard(262, "第三方激活会员卡"),
    QueryPackageRule(263, "第三方查询包时规则列表"),
    PackageTime(264, "第三方执行包时"),
    Logout(265, "第三方结账下机"),

    <PERSON><PERSON>(266, "第三方登入"), //
    ClientLogin(267, "计费登陆"), //
    QueryPlaceProfile(268, "查询网吧信息"), //
    QueryClientQrCodeToken(269, "查询二维码TOKEN"), //
    QueryBillingCommonRule(270, "查询普通计费规则"), //

    CreatePaymentOrder(271, "创建充值订单"), //
    QueryPaymentOrder(272, "查询充值订单"), //
    QueryBillingCardList(273, "查询会员列表"), //
    SyncBillingCard(274, "同步会员信息"), //
    CancelActivation(275, "第三方取消激活"), //

    RealnameNotify(276, "实名通知"), //
    QueryCardAndOnline(277, "查询卡信息或在线信息"), //  卡信息、在线信息二合一
    QuerySurcharge(278, "查询最近一条附加费信息"), //
    EditPlaceProfile(279, "新增、修改场所信息"), //
    DeletePlaceArea(280, "删除一条区域信息"), //

    EditPlaceArea(281, "新增、修改区域信息"), //
    EditPlaceClient(282, "新增、修改客户端信息"), //
    QueryOnlineInfo(283, "查询场所在线信息列表"), //
    DeletePlaceClient(284, "删除一条客户端信息"), //
    QueryPlaceAreaList(285, "查询场所区域列表"), //

    CheckIfCanLoginClient(286, "校验是否可以登录客户端(上机)"), //
    QueryMemPlaceByIdNumber(287, "根据身份证ID查询所在的会员网吧ID列表"), //
    QueryUserLoginRecord(288, "查询用户上机记录"), //
    QueryClientListByPlaceId(289, "根据场所ID查询客户端列表"), //
    QueryPlaceProfilesByParam(290, "根据条件查询场所信息列表"), //

    CreateCardBySpecialIdNumber(291, "pms特殊证件开卡并激活"), //
    EditBillingCard(292, "修改会员卡信息"), //;
    QueryLogLogin(293, "第三方分页查询上下机记录"), //
    QueryLogTopup(294, "第三方分页查询充值记录"), //
    QueryTopupRules(295, "第三方查询充值规则列表"), //

    BookSeatsContext(296, "查询预约座位初始化信息"), //
    BookSeatsCreate(298, "预约座位创建"), //
    BookSeatsCancel(299, "预约座位取消"), //
    BookSeatsPageList(300, "预约座位记录分页列表"), //
    BookSeatsOrder(301, "查询预约座位订单"),

    QueryLatestLogLogin(302, "查询最近一条上下机记录"),
    QueryPageLogOperation(303, "第三方查询有限操作记录"),
    QueryClientList(304, "查询客户端列表"),
    CancellationBillingCard(305, "注销计费卡"),
    QueryActiveBillingCardList(306, "查询激活计费卡列表"),

    QueryPlaceShift(307, "根据班次查询场所统计"),
    QueryPlaceTopupRuleList(308, "查询场所充值规则列表"),
    UpdateOnlineBillingCardAccount(309, "批量修改会员卡账户"),
    QueryPackageRules(310, "查询场所包时规则信息"),
    ConvertBillingRule(311, "转换计费规则"),
    UpdateOnlineBillingCard(312, "同步单个会员卡余额"),

    QueryLogLoginByLoginId(313, "根据loginId查询上机记录信息"),

    QueryCashier(315, "查询场所下所有收银台信息"), // 314已被同步艺龙连锁会员接口占用。 
    ;
    // ===========================================================
    private final int value;
    private final String display;

    private ThirdServiceIndexes(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    /**
     * admin-web 第三方账号编辑页面接口权限控制列表数据从这里来
     *
     * @return 需要权限控制的列表
     */
    public static List<ThirdServiceIndexes> getNeedAuthorityIndexes() {
        return new ArrayList<ThirdServiceIndexes>() {
            private static final long serialVersionUID = 1L;

            {
                add(ThirdServiceIndexes.QueryMember); // 第三方查询会员信息,256
                add(ThirdServiceIndexes.QueryOnline); // 第三方查询用户在线信息,257
                add(ThirdServiceIndexes.Topup); // 第三方充值,258
                add(ThirdServiceIndexes.Deduction); // 第三方扣款,259
                add(ThirdServiceIndexes.QueryCardTypes); // 第三方查询卡类型列表,260

                add(ThirdServiceIndexes.CreateCard); // 第三方开卡,261
                add(ThirdServiceIndexes.ActivationCard); // 第三方激活会员卡，262
                add(ThirdServiceIndexes.QueryPackageRule); // 第三方查询包时规则列表，263
                add(ThirdServiceIndexes.PackageTime); // 第三方执行包时，264
                add(ThirdServiceIndexes.Logout); // 第三方结账下机，265

                add(ThirdServiceIndexes.Login); // 第三方登陆，266
                add(ThirdServiceIndexes.ClientLogin); //计费登陆，267
                add(ThirdServiceIndexes.QueryPlaceProfile); // 查询网吧信息，268
                add(ThirdServiceIndexes.QueryClientQrCodeToken); // 查询二维码token，269
                add(ThirdServiceIndexes.QueryBillingCommonRule); // 查询通用规则列表，270

                add(ThirdServiceIndexes.CreatePaymentOrder); // 创建支付订单，271
                add(ThirdServiceIndexes.QueryPaymentOrder); // 查询支付订单，272
                add(ThirdServiceIndexes.QueryBillingCardList); // 查询会员卡列表，273
                add(ThirdServiceIndexes.SyncBillingCard); // 同步会员卡信息，274
                add(ThirdServiceIndexes.CancelActivation); // 取消激活，275

                add(ThirdServiceIndexes.RealnameNotify); // 实名认证通知，276
                add(ThirdServiceIndexes.QueryCardAndOnline); // 查询会员卡和在线信息，277
                add(ThirdServiceIndexes.QuerySurcharge); // 查询附加费规则列表，278
                add(ThirdServiceIndexes.EditPlaceProfile); // 编辑场所信息，279
                add(ThirdServiceIndexes.DeletePlaceArea); // 删除场所区域，280

                add(ThirdServiceIndexes.EditPlaceArea); // 编辑场所区域，281
                add(ThirdServiceIndexes.EditPlaceClient); // 编辑场所客户信息，282
                add(ThirdServiceIndexes.QueryOnlineInfo); // 查询在线信息，283
                add(ThirdServiceIndexes.DeletePlaceClient); // 删除场所客户信息，284
                add(ThirdServiceIndexes.QueryPlaceAreaList); // 查询场所区域列表，285

                add(ThirdServiceIndexes.CheckIfCanLoginClient); // 查询场所客户是否可以登录，286
                add(ThirdServiceIndexes.QueryMemPlaceByIdNumber); // 根据会员卡号查询场所信息，287
                add(ThirdServiceIndexes.QueryUserLoginRecord); // 查询用户登录记录，288
                add(ThirdServiceIndexes.QueryClientListByPlaceId); // 查询场所客户列表，289
                add(ThirdServiceIndexes.QueryPlaceProfilesByParam); // 查询场所信息列表，290

                add(ThirdServiceIndexes.CreateCardBySpecialIdNumber); // 通过特殊会员卡号开卡，291
                add(ThirdServiceIndexes.EditBillingCard); // 编辑会员卡信息，292
                add(ThirdServiceIndexes.QueryLogLogin); // 查询登录日志，293
                add(ThirdServiceIndexes.QueryLogTopup); // 查询充值日志，294
                add(ThirdServiceIndexes.QueryTopupRules); // 查询充值规则列表，295

                add(ThirdServiceIndexes.BookSeatsContext); // 预约座位，296
                add(ThirdServiceIndexes.BookSeatsCreate); // 预约座位下单，298
                add(ThirdServiceIndexes.BookSeatsCancel); // 预约座位取消，299
                add(ThirdServiceIndexes.BookSeatsPageList); // 预约座位查询，300
                add(ThirdServiceIndexes.BookSeatsOrder); // 预约座位查询订单，301

                add(ThirdServiceIndexes.QueryLatestLogLogin); // 查询最新登录日志，302
                add(ThirdServiceIndexes.QueryPageLogOperation); // 第三方查询有限操作记录，303
                add(ThirdServiceIndexes.QueryClientList); // 查询客户端列表，304
                add(ThirdServiceIndexes.CancellationBillingCard); // 取消会员卡，305
                add(ThirdServiceIndexes.QueryActiveBillingCardList); // 查询激活会员卡列表，306

                add(ThirdServiceIndexes.QueryPlaceShift); // 根据班次查询场所统计，307
                add(ThirdServiceIndexes.QueryPlaceTopupRuleList); // 查询场所充值规则列表，308
                add(ThirdServiceIndexes.UpdateOnlineBillingCardAccount); // 更新在线会员卡账户，309
                add(ThirdServiceIndexes.QueryPackageRules); // 查询场所包时规则信息，310
                add(ThirdServiceIndexes.ConvertBillingRule); // 转换计费规则，311
                add(ThirdServiceIndexes.UpdateOnlineBillingCard); // 同步单个会员卡余额，312
                add(ThirdServiceIndexes.QueryLogLoginByLoginId); // 根据loginId查询上机记录信息,313
                add(ThirdServiceIndexes.QueryCashier); // 查询场所下所有收银台信息,315
            }
        };
    }

    /**
     * 根据索引值获取实例对象
     *
     * @param index
     * @return
     */
    public static ThirdServiceIndexes getThirdServiceIndexes(int index) {
        for (ThirdServiceIndexes thirdServiceIndexes : ThirdServiceIndexes.values()) {
            if (thirdServiceIndexes.getValue() == index) {
                return thirdServiceIndexes;
            }
        }
        return null;
    }

}
