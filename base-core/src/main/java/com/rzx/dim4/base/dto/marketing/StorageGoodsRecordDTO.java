package com.rzx.dim4.base.dto.marketing;

import java.time.LocalDateTime;

/**
 * 库存信息表
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface StorageGoodsRecordDTO {

    // 商品ID
    String getGoodsId();

    // 商品名称
    String getGoodsName();

    // 商品类型ID
    String getGoodsTypeId();

    // 商品类型名称
    String getGoodsTypeName();

    // 单位
    int getUnit();

    // 零售价 单位分
    int getUnitPrice();

    // 进货价，单位分
    int getCostPrice();

    // 商品图片
    String getGoodsPic();

    // 库存数量
    int getGoodsStocksNum();

    // 创建时间
    LocalDateTime getCreated();
}
