package com.rzx.dim4.base.service.callback;

import com.rzx.dim4.base.bo.notify.SmsLogBO;
import com.rzx.dim4.base.bo.notify.polling.*;
import com.rzx.dim4.base.bo.notify.region.RegionCityBO;
import com.rzx.dim4.base.bo.notify.region.RegionCountryBO;
import com.rzx.dim4.base.bo.notify.region.RegionProvinceBO;
import com.rzx.dim4.base.bo.notify.region.RegionTownBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Nov 19, 201910:33:28 AM
 */
@Slf4j
@Service
public class NotifyServerServiceHystrix implements NotifyServerService {

    @Override
    public Boolean send(String requestTicket, String accessKey, Map<String, String> params) {
        log.error("接口异常::: send(requestTicket:::{}, accessKey:::{}, params:::{})---", requestTicket, accessKey, params);
        return Boolean.FALSE;
    }

    @Override
    public PagerDTO<SmsLogBO> logs(Map<String, String> queryMap, int page, int size, String order,
                                   String[] orderColumns) {
        log.error("接口异常::: logs(queryMap:::{}, page:::{}, size:::{}, order:::{}, orderColumns:::{})---", queryMap, page,
                size, order, orderColumns);
        return null;
    }

    @Override
    public GenericResponse<ListDTO<RegionProvinceBO>> provinces() {
        log.error("接口异常::: provinces()");
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<RegionCityBO>> cities(String provinceId) {
        log.error("接口异常::: cities(provinceId:::{})", provinceId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<RegionCountryBO>> countries(String cityId) {
        log.error("接口异常::: countries(cityId:::{})", cityId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<RegionTownBO>> towns(String countryId) {
        log.error("接口异常::: towns(countryId:::{})", countryId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, String> findNameByCodes(String provinceId, String cityId, String countryId, String townId) {
        log.error("接口异常::: towns(provinceId:::{} cityId:::{} countryId:::{} townId:::{})", provinceId, cityId, countryId, townId);
        return new HashMap<>();
    }

    @Override
    public List<Map<String, String>> getStatProvinces() {
        log.error("接口异常::: getStatProvinces()");
        return null;
    }

    @Override
    public List<Map<String, String>> getStatCitys(String provinceCode) {
        log.error("接口异常::: getStatCitys(provinceCode:::{})", provinceCode);
        return null;
    }

    @Override
    public GenericResponse<SimpleDTO> saveStatisticsAuth(String requestTicket, String account, String provinceCode) {
        log.error("接口异常::: saveStatisticsAuth(requestTicket:::{},account:::{},provinceCode:::{})", requestTicket, account, provinceCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, String> queryStatisticsAuth(String account) {
        log.error("接口异常::: queryStatisticsAuth(account:::{})", account);
        return null;
    }

    @Override
    public List<Map<String, String>> queryStatistics(String startDate, String endDate, String bizType, String provinceCode, String cityCode, String dateType) {
        log.error("接口异常::: queryStatistics(startDate:::{},endDate:::{},bizType:::{},provinceCode:::{},cityCode:::{},dateType:::{})", startDate, endDate, bizType, provinceCode, cityCode, dateType);
        return null;
    }

    @Override
    public List<Map<String, String>> queryStatisticsBySrcId(String startDate, String endDate, String bizType, String provinceCode, String cityCode, String dateType, String srcId) {
        log.error("接口异常::: queryStatisticsBySrcId(startDate:::{},endDate:::{},bizType:::{},provinceCode:::{},cityCode:::{},dateType:::{},srcId:::{})", startDate, endDate, bizType, provinceCode, cityCode, dateType, srcId);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<ActiveBusinessBO>> pushActiveBusinessData(ActiveBusinessBO activeBusinessBO) {
        log.error("接口异常::: pushActiveBusinessData(activeBusinessBO:::{})", activeBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<RealnameBusinessBO>> pushRealnameBusinessData(RealnameBusinessBO realnameBusinessBO) {
        log.error("接口异常::: pushRealnameBusinessData(realnameBusinessBO:::{})", realnameBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<UpdateConfigBusinessBO>> pushUpdateConfigBusinessData(UpdateConfigBusinessBO updateConfigBusinessBO) {
        log.error("接口异常::: pushUpdateConfigBusinessData(updateConfigBusinessBO:::{})", updateConfigBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<ShopBuyBusinessBO>> pushShopBuyBusinessData(ShopBuyBusinessBO shopBuyBusinessBO) {
        log.error("接口异常::: pushShopBuyBusinessData(shopBuyBusinessBO:::{})", shopBuyBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<CancelActiveBusinessBO>> pushCancelActiveBusinessData(CancelActiveBusinessBO cancelActiveBusinessBO) {
        log.error("接口异常::: pushCancelActiveBusinessData(cancelActiveBusinessBO:::{})", cancelActiveBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<CreateCardBusinessBO>> pushCreateCardBusinessData(CreateCardBusinessBO createCardBusinessBO) {
        log.error("接口异常::: pushCreateCardBusinessData(createCardBusinessBO:::{})", createCardBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<LogLoginBusinessBO>> pushLoginBusinessData(LogLoginBusinessBO logLoginBusinessBO) {
        log.error("接口异常::: pushLoginBusinessData(logLoginBusinessBO:::{})", logLoginBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<PackageExchangeBusinessBO>> pushPackageExchangeBusinessData(PackageExchangeBusinessBO packageExchangeBusinessBO) {
        log.error("接口异常::: pushPackageExchangeBusinessData(packageExchangeBusinessBO:::{})", packageExchangeBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<LogoutBusinessBO>> pushLogoutBusinessData(LogoutBusinessBO logoutBusinessBO) {
        log.error("接口异常::: pushTopupAndDeductionBusinessData(logoutBusinessBO:::{})", logoutBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<TopupAndDeductionBusinessBO>> pushTopupAndDeductionBusinessData(TopupAndDeductionBusinessBO topupAndDeductionBusinessBO) {
        log.error("接口异常::: pushTopupAndDeductionBusinessData(topupAndDeductionBusinessBO:::{})", topupAndDeductionBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<PollingBO>> savePolling(String placeId, String clientId, String idNumber, BusinessType businessType) {
        log.error("接口异常::: savePolling(placeId:::{},clientId:::{}，idNumber:::{}，businessType:::{})", placeId, clientId, idNumber, businessType);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<CouponBusinessBO>> pushCouponBusinessData(CouponBusinessBO couponBusinessBO) {
        log.error("接口异常::: pushThirdExchangeBusinessData(pushCouponBusinessData:::{})", couponBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<UpdateCardTypeBusinessBO>> pushUpdateCardTypeBusinessData(UpdateCardTypeBusinessBO updateCardTypeBusinessBO) {
        log.error("接口异常::: pushThirdExchangeBusinessData(pushUpdateCardTypeBusinessData:::{})", updateCardTypeBusinessBO);
        return null;
    }

    @Override
    public GenericResponse<ObjDTO<ThirdExchangeBusinessBO>> pushThirdExchangeBusinessData(@RequestBody ThirdExchangeBusinessBO thirdExchangeBusinessBO) {
        log.error("接口异常::: pushThirdExchangeBusinessData(thirdExchangeBusinessBO:::{})", thirdExchangeBusinessBO);
        return null;
    }

}
