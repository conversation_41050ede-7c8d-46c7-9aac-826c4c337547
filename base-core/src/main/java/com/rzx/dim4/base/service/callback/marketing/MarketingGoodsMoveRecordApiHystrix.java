package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsApi;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsMoveRecordApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年02月20日 14:43
 */
@Slf4j
@Component
public class MarketingGoodsMoveRecordApiHystrix implements MarketingGoodsMoveRecordApi {


    @Override
    public GenericResponse<PagerDTO<GoodsMoveRecordBO>> findPageList(String recordType, String placeId, String startDate, String endDate, int page, int size) {
        log.error("接口异常，MarketingGoodsApiHystrix.findPageList，参数信息：recordType={}, placeId={}, startDate={}, endDate={}, page={}, size={}",
                recordType, placeId, startDate, endDate, page, size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsInventoryChangeRecordBO>> findDetail(String changeRecordId, String placeId,int recordType) {
        log.error("接口异常，MarketingGoodsApiHystrix.findDetail，参数信息：changeRecordId={}, placeId={},recordType={}",
                changeRecordId, placeId, recordType);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveGoodsMoveRecord(String requestTicket,SaveGoodsMoveRecordAddBO addBO) {
        log.error("接口异常，MarketingGoodsApiHystrix.saveGoodsMoveRecord，参数信息：addBO={}",
                new Gson().toJson(addBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }



}
