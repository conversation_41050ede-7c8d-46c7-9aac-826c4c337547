package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 新增入库记录请求对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@ToString
public class GoodsPutOnSaleRequestBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID", required = true)
    private String placeId;

    @ApiModelProperty(value = "账户ID", hidden = true)
    private String accountId;

    @ApiModelProperty(value = "账户名称", hidden = true)
    private String accountName;

    @ApiModelProperty(value = "转上架的记录列表")
    List<GoodsMoveRecordRequestBO> upRecords;
}
