package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.PlaceServerServiceHystrix;
import com.rzx.dim4.base.vo.PlaceProfileVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 场所服务 待删除，不添加新接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date Nov 19, 201910:33:08 AM
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.PLACE_SERVER, contextId = "PlaceServerService", fallback = PlaceServerServiceHystrix.class)
public interface PlaceServerService {

    /**********************************/
    /********** 场所信息相关 ************/
    /**********************************/
    @PostMapping("/api/place/profile/save")
    public GenericResponse<ObjDTO<PlaceProfileBO>> savePlaceProfile(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceProfileBO profileBo);

    @GetMapping("/api/place/profile/placeId/{placeId}")
    GenericResponse<ObjDTO<PlaceProfileBO>> findByPlaceId(@PathVariable("placeId") String placeId);

    @PostMapping("/api/place/profile/findPlaceProfiles")
    GenericResponse<ListDTO<PlaceProfileBO>> findByPlaceIds(@RequestBody List<String> placeIds);

    @PostMapping("/api/place/profile/findPlaceProfilesDistance")
    GenericResponse<ListDTO<PlaceProfileBO>> findPlaceProfilesDistance(@RequestParam List<String> placeIds,
                                                                       @RequestParam(name = "longitude") String longitude,
                                                                       @RequestParam(name = "latitude") String latitude);

    @GetMapping("/api/place/profile/identifier/{identifier}")
    public GenericResponse<ObjDTO<PlaceProfileBO>> findByIdentifier(@PathVariable String identifier);

    @PostMapping("/api/place/profiles/fuzzy")
    public GenericResponse<PagerDTO<PlaceProfileVO>> fuzzy(@RequestParam(name = "page", defaultValue = "1") int page,
                                                           @RequestParam(name = "size", defaultValue = "10") int size,
                                                           @RequestParam(name = "order", defaultValue = "desc") String order,
                                                           @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                           @RequestBody Map<String, Object> queryMap);

    @GetMapping("/api/place/profiles/list")
    GenericResponse<ListDTO<PlaceProfileBO>> list();

    @GetMapping("/api/place/nearbyProfiles")
    public GenericResponse<ListDTO<PlaceProfileBO>> nearbyProfiles(
            @RequestParam(name = "longitude", required = false) String lng,
            @RequestParam(name = "latitude", required = false) String lat);

    @GetMapping("/api/place/queryPlaceByAuditId")
    public GenericResponse<ObjDTO<PlaceProfileBO>> queryPlaceByAuditId(@RequestParam String auditId);

    @GetMapping("/api/place/queryPlaceByType")
    public GenericResponse<ListDTO<PlaceProfileBO>> queryPlaceByType(@RequestParam int type);

    @GetMapping("/api/place/queryPlaceIdsByRegionCode")
    public List<String> queryPlaceIdsByRegionCode(@RequestParam String regionCode);

    /**********************************/
    /********** 场所配置相关 ************/
    /**********************************/
    @PostMapping("/api/place/config/save")
    public GenericResponse<ObjDTO<PlaceConfigBO>> savePlaceConfig(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceConfigBO managementBo);

    @GetMapping("/api/place/config/{placeId}")
    public GenericResponse<ObjDTO<PlaceConfigBO>> findPlaceConfigByPlaceId(@PathVariable("placeId") String placeId);

    @GetMapping("/api/place/config/updateUpgradeV8Status")
    public GenericResponse updateUpgradeV8Status(@RequestParam String placeId,@RequestParam int upgradeV8Status);


    @PostMapping("/api/place/config/findPlaceConfigByPlaceIds")
    public GenericResponse<ListDTO<PlaceConfigBO>> findPlaceConfigByPlaceIds(@RequestBody List<String> placeIds);


    /**********************************/
    /********** 场所账号相关 ************/
    /**********************************/
//    @GetMapping("/api/place/account/loginName/{loginName}")
//    public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByLoginName(
//            @PathVariable("loginName") String loginName);
    @GetMapping("/api/place/account/placeId/{placeId}/accountId/{accountId}")
    public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByPlaceIdAndAccountId(
            @PathVariable("placeId") String placeId, @PathVariable("accountId") String accountId);

    @PostMapping("/api/place/account/accounts")
    public GenericResponse<ListDTO<PlaceAccountBO>> findAccountByPlaceId(@RequestParam String placeId);

    @PostMapping("/api/place/account/save")
    public GenericResponse<ObjDTO<PlaceAccountBO>> savePlaceAccount(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceAccountBO placeAccountBO);

    @PostMapping("/api/place/account/batchSave")
    public GenericResponse<ListDTO<PlaceAccountBO>> batchSavePlaceAccount(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                          @RequestBody List<PlaceAccountBO> placeAccountBOS,@RequestParam(required = false) String type);

    @PostMapping("/api/place/account/findPlaceAccountByChainIdAndAccountId")
    public GenericResponse<ObjDTO<PlaceAccountBO>> findPlaceAccountByChainIdAndAccountId(@RequestParam(required = false) String chainId,@RequestParam String accountId);

    /**
     * 小程序店长登录
     *
     * @param loginName 登录名
     * @param loginPass 登录密码
     * @return
     */
    @PostMapping("/api/place/account/counterLogin")
    public GenericResponse<ObjDTO<PlaceAccountBO>> storeManagerLogin(@RequestParam String loginName,
                                                                     @RequestParam String loginPass);

    @GetMapping("/api/place/account/findCashierAccounts")
    public GenericResponse<ListDTO<PlaceAccountBO>> findCashierAccounts(@RequestParam(name = "placeId") String placeId,
                                                                        @RequestParam(name = "flag") String flag,
                                                                        @RequestParam(name = "cashierId") String cashierId);

    /**
     * 收银员账号改为场所未删除唯一后弃用
     *
     * @apiNote 新接口如下
     * @see com.rzx.dim4.base.service.feign.place.PlaceAccountApi#loginAtCashier
     *
     * @param username
     * @param password
     * @param placeId
     * @param cashierId
     * @return
     */
//    @Deprecated
//    @PostMapping("/api/place/account/submitCheckAccount")
//    public GenericResponse<ObjDTO<PlaceAccountBO>> submitCheckAccount(@RequestParam String username,
//                                                                      @RequestParam String password,
//                                                                      @RequestParam String placeId,
//                                                                      @RequestParam String cashierId);

    /***********************************/
    /********** 代理商相关接口 ***********/
    /***********************************/
    @GetMapping("/api/place/agent/account/accounts")
    public GenericResponse<PagerDTO<PlaceAgentBO>> findPageAgentAccounts(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestParam int type,
            @RequestParam(name = "loginName", required = false) String loginName,
            @RequestParam(name = "level", required = false) String level,
            @RequestParam(name = "creater", required = false) String creater,
            @RequestParam(name = "model", required = false) String model);

    @PostMapping("/api/place/agent/account/save")
    public GenericResponse<ObjDTO<PlaceAgentBO>> savePlaceAgent(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceAgentBO placeAgentBO);

    @GetMapping("/api/place/agent/account/findByAccountId")
    public GenericResponse<ObjDTO<PlaceAgentBO>> findByAccountId(@RequestParam String accountId);

    @PostMapping("/api/place/logAgent/balance/save")
    public GenericResponse<ObjDTO<LogAgentBalanceBO>> saveLogAgentBalance(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody LogAgentBalanceBO logAgentBalanceBO);

    @PostMapping("/api/place/agent/renewal/save")
    public GenericResponse<ObjDTO<PlaceRenewalBO>> savePlaceAgentRenewal(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam(name = "loginPass", required = false) String loginPass,
            @RequestParam(name = "createFlag", required = false) String createFlag,
            @RequestParam String mobile,
            @RequestBody PlaceRenewalBO placeRenewalBO);

    @GetMapping("/api/place/agent/renewal/queryRenewalByAccountId")
    public GenericResponse<ObjDTO<PlaceRenewalBO>> queryRenewalByAccountId(@RequestParam String accountId);

    @PostMapping("/api/place/logAgent/balance/queryPageLogAgentBalance")
    public GenericResponse<PagerDTO<LogAgentBalanceBO>> queryPageLogAgentBalance(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestBody Map<String, Object> queryMap);

    @GetMapping("/api/place/logAgent/balance/queryPlaceAgentLikeLoginName")
    public GenericResponse<ListDTO<PlaceAgentBO>> queryPlaceAgentLikeLoginName(@RequestParam String loginName);

    @PostMapping("/api/place/agent/renewal/queryPageRenewalList")
    public GenericResponse<PagerDTO<LogRenewalBO>> queryPageRenewalList(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestBody Map<String, Object> queryMap);

    @PostMapping("/api/place/agent/account/bindPlaceSubmit")
    public GenericResponse<ObjDTO<PlaceAgentBO>> bindPlaceSubmit(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceAgentBO placeAgentBO);

    /***********************************/
    /********** 场所客户端相关 ***********/
    /***********************************/
    @GetMapping("/api/place/client/registered")
    public GenericResponse<ObjDTO<PlaceClientBO>> clientRegistered(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String identifier,
            @RequestParam String hostName,
            @RequestParam String ipAddr,
            @RequestParam String macAddr,
            @RequestParam String clientVersion,
            @RequestParam(name = "osVersion", required = false) String osVersion,
            @RequestParam(name = "jfVersion", required = false) String jfVersion,
            @RequestParam(name = "spVersion", required = false) String spVersion,
            @RequestParam(name = "jkVersion", required = false) String jkVersion,
            @RequestParam(name = "gxVersion", required = false) String gxVersion);

    @PostMapping("/api/place/client/batchSave")
    public GenericResponse<SimpleDTO> batchSaveClient(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestBody List<PlaceClientBO> clientBOS);

    @GetMapping("/api/place/client/queryClient")
    GenericResponse<ObjDTO<PlaceClientBO>> queryPlaceClient(@RequestParam String placeId,
                                                            @RequestParam String clientId);

    @GetMapping("/api/place/client/queryClientCount")
    public Map<String, Integer> queryClientCount(@RequestParam String placeId);

    @PostMapping("/api/place/client/queryAllClientByClientIds")
    GenericResponse<ListDTO<PlaceClientBO>> queryAllClientByClientIds(@RequestParam String placeId,
                                                                      @RequestParam List<String> clientIds);

    @GetMapping("/api/place/client/queryClientsByPlaceIdAndAreaId")
    public GenericResponse<ListDTO<PlaceClientBO>> queryClientsByPlaceIdAndAreaId(@RequestParam String placeId,
                                                                                  @RequestParam String areaId);

    @GetMapping("/api/place/client/queryClientByPlaceIdAndHostNameLike")
    public GenericResponse<ListDTO<PlaceClientBO>> findByPlaceIdAndHostNameLike(@RequestParam String placeId,
                                                                                @RequestParam String hostName);

    @GetMapping("/api/place/client/queryClientByPlaceIdAndHostName")
    public GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndHostName(@RequestParam String placeId,
                                                                           @RequestParam String hostName);

    @GetMapping("/api/place/client/queryClientByPlaceIdAndMacAddr")
    public GenericResponse<ObjDTO<PlaceClientBO>> findByPlaceIdAndMacAddr(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String macAddr);

    @GetMapping("/api/place/client/queryAllClients")
    GenericResponse<ListDTO<PlaceClientBO>> queryAllClients(@RequestParam String placeId);

    @GetMapping("/api/place/client/listClients")
    GenericResponse<PagerDTO<PlaceClientBO>> listClients(@RequestParam String placeId,
                                                         @RequestParam(name = "areaId", defaultValue = "") String areaId,
                                                         @RequestParam(name = "length", defaultValue = "10") int length,
                                                         @RequestParam(name = "start", defaultValue = "0") int start,
                                                         @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                         @RequestParam(name = "order", defaultValue = "desc") String order);

    @PostMapping("/api/place/cashier/report")
    public GenericResponse<ObjDTO<PlaceCashierBO>> saveCashier(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceCashierBO bo);


    @PostMapping("/api/place/cashier/saveCashier")
    public GenericResponse<ObjDTO<PlaceCashierBO>> saveCashier(@RequestBody PlaceCashierBO bo);


    @GetMapping("/api/place/cashier/queryAllCashiers")
    public GenericResponse<ListDTO<PlaceCashierBO>> queryAllCashiers(@RequestParam String placeId);

    /***********************************/
    /********** 网吧区域信息相关 **********/
    /***********************************/
    @PostMapping("/api/place/area/save")
    public GenericResponse<SimpleDTO> savePlaceArea(@RequestHeader(value = "request_ticket") String requestTicket,
                                                    @RequestBody PlaceAreaBO areaBo);

    @PostMapping("/api/place/area/saveBo")
    public GenericResponse<ObjDTO<PlaceAreaBO>> savePlaceAreaBo(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                @RequestBody PlaceAreaBO areaBo);


    @PostMapping("/api/place/area/batchSave")
    public GenericResponse<ListDTO<PlaceAreaBO>> batchSavePlaceArea(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                    @RequestBody List<PlaceAreaBO> placeAreaBOS);

    @GetMapping("/api/place/area/placeId/{placeId}/areaId/{areaId}")
    GenericResponse<ObjDTO<PlaceAreaBO>> findPlaceAreaByPlaceIdAndAreaId(@PathVariable("placeId") String placeId,
                                                                         @PathVariable("areaId") String areaId);

    @PostMapping("/api/place/area/findByPlaceIdAndAreaIdIn")
    public GenericResponse<ListDTO<PlaceAreaBO>> findByPlaceIdAndAreaIdIn(@RequestParam String placeId,
                                                                          @RequestParam List<String> areaIds);

    @GetMapping("/api/place/area/placeId/{placeId}")
    GenericResponse<ListDTO<PlaceAreaBO>> findPlaceAreaByPlaceId(@PathVariable("placeId") String placeId);


    @PostMapping("/api/place/area/delete")
    GenericResponse<SimpleDTO> deletePlaceArea(@RequestParam String areaId, @RequestParam String placeId);

    /***********************************/
    /********** 小程序相关接口 ***********/
    /***********************************/
    @GetMapping("/api/place/miniApp/initShiftTable")
    public GenericResponse<ListDTO<PlaceShiftBO>> initShiftTable(@RequestParam String startTime,
                                                                 @RequestParam String endTime, @RequestParam String placeId);

    @GetMapping("/api/place/miniApp/newInitShiftTable")
    public GenericResponse<ListDTO<PlaceShiftBO>> newInitShiftTable(@RequestParam String startTime,
                                                                    @RequestParam String endTime, @RequestParam String placeId);

    /***********************************/
    /********** 场所交班相关接口 **********/
    /***********************************/
    @GetMapping("/api/place/shift/findWorkingShiftByShiftId")
    public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByShiftId(@RequestParam String placeId,
                                                                           @RequestParam String shiftId);

    @GetMapping("/api/place/shift/findWorkingShiftByCashierId")
    public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingShiftByCashierId(@RequestParam String placeId,
                                                                             @RequestParam String cashierId);

    @PostMapping("/api/place/shift/save")
    public GenericResponse<ObjDTO<PlaceShiftBO>> savePlaceShift(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody PlaceShiftBO placeShiftBO);

    @GetMapping("/api/place/shift/findWorkingByAccountId")
    public GenericResponse<ObjDTO<PlaceShiftBO>> findWorkingByAccountId(@RequestParam String placeId,
                                                                        @RequestParam String accountId);

    @PostMapping("/api/place/shift/queryPageShifts")
    public GenericResponse<PagerDTO<PlaceShiftBO>> queryPageShifts(@RequestBody Map<String, String> queryMap,
                                                                   @RequestParam(name = "size", defaultValue = "10") int size,
                                                                   @RequestParam(name = "page", defaultValue = "1") int page,
                                                                   @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                   @RequestParam(name = "order", defaultValue = "desc") String order);

    @PostMapping("/api/place/findPlaceProfilePage")
    public GenericResponse<PagerDTO<PlaceProfileBO>> findPlaceProfilePage(
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestBody Map<String, String> queryMap);

    @PostMapping("/api/place/client/save")
    public GenericResponse<SimpleDTO> savePlaceClient(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestBody PlaceClientBO placeClientBO);

    @PostMapping("/api/place/client/saveClientReturnBo")
    public GenericResponse<ObjDTO<PlaceClientBO>> savePlaceClientReturnBo(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                          @RequestBody PlaceClientBO placeClientBO);

    @GetMapping("/api/place/client/queryClient")
    public GenericResponse<ObjDTO<PlaceClientBO>> findClientByPlaceIdAndClientId(@RequestParam String placeId, @RequestParam String clientId);

    @PostMapping("/api/place/client/delete")
    GenericResponse<SimpleDTO> deletePlaceClient(@RequestParam String placeId, @RequestParam String clientId);

    /**
     * 查询正在值班的班次信息(多个收银台只查询主收银台)
     *
     * @param placeId
     * @return
     */
    @GetMapping("/api/place/shift/queryWorkShifts")
    public GenericResponse<ListDTO<PlaceShiftBO>> queryWorkShifts(@RequestParam String placeId);
    
    /**
     * 根据场所id查询默认班次，优先级：1.placeConfig中配置的收银台的班次，2主收银台的班次
     *
     * @param placeId
     * @return
     */
    @GetMapping("/api/place/shift/queryDefaultWorkShift")
    public GenericResponse<ObjDTO<PlaceShiftBO>> queryDefaultWorkShift(@RequestParam String placeId);

    /**
     * 根基shiftIds 查询值班信息和收银台信息
     *
     * @param placeId
     * @return
     */
    @GetMapping("/api/place/shift/queryPlaceShiftsByPlaceIdAndShiftIds")
    public GenericResponse<ListDTO<PlaceShiftBO>> queryPlaceShiftsByPlaceIdAndShiftIds(@RequestParam String placeId, @RequestParam List<String> shiftIds);
}
