package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.PlaceChainStoresSynchronizationApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/9
 **/
@Slf4j
@Service
public class PlaceChainStoresSynchronizationApiHystrix implements PlaceChainStoresSynchronizationApi {

    @Override
    public GenericResponse<?> save(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("add 接口异常，PlaceChainStoresBO={}", placeChainStoresBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> update(String requestTicket, List<PlaceChainStoresBO> placeChainStoresBOS) {
        return null;
    }

    /**
     * @param requestTicket
     * @param placeChainStoresBO
     * @return
     * @apiNote 只限 place-server 调用
     */
    @Override
    public GenericResponse<?> exit(String requestTicket, PlaceChainStoresBO placeChainStoresBO) {
        log.error("exit 接口异常，PlaceChainStoresBO={}", placeChainStoresBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> openChainAllPlaceShareMemberPoint(String requestTicket, String chainId) {
        log.error("openChainAllPlaceShareMemberPoint 接口异常，chainId={}", chainId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
