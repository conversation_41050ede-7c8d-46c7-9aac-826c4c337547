package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.user.customer.MiniAuthContextBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingPlaceProfileApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/23
 **/
@Slf4j
@Service
public class BillingPlaceProfileApiHystrix implements BillingPlaceProfileApi {
    @Override
    public GenericResponse<ObjDTO<MiniAuthContextBO>> context(String token, String idNumber, String idName, String scanState) {
        log.error("接口异常, context(token={}, idNumber={}, idName={}, scanState={})", token, idNumber, idName, scanState);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<MiniAuthContextBO>> cashierContext(String token) {
        log.error("接口异常, context(token={})", token);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
