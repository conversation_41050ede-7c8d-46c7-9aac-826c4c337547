package com.rzx.dim4.base.service.callback.marketing;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingRealGoodsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingRealGoodsApiHystrix implements MarketingRealGoodsApi {

    @Override
    public GenericResponse<PagerDTO<BarcodeResponseBO>> findRealGoodsTemplatePage(GoodsRealTemplateRequestBo paramsBo) {
        log.error("接口异常:::MarketingRealGoodsApiHystrix.findRealGoodsTemplatePage(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveRealGoods(GoodsRealAddRequestBO paramsBo) {
        log.error("接口异常:::MarketingRealGoodsApiHystrix.saveRealGoods(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateRealGoods(GoodsRealUpdateRequestBO paramsBo) {
        log.error("接口异常:::MarketingRealGoodsApiHystrix.updateRealGoods(ParamsBo={})", new Gson().toJson(paramsBo));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> removeRealGoods(@RequestParam String placeId, @RequestParam String goodsId) {
        log.error("接口异常:::MarketingRealGoodsApiHystrix.removeRealGoods(placeId={}, goodsId={})", placeId, goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> editFixGoods(String requestTicket, SaveFixGoodsBO saveFixGoodsBO) {
        log.error("接口异常，MarketingRealGoodsApiHystrix.editFixGoods(requestTicket={},saveFixGoodsBO={})", requestTicket, new Gson().toJson(saveFixGoodsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> batchDeleteGoods(String requestTicket, String placeId, List<String> goodsIdList, PlaceAccountBO placeAccountBO) {
        log.error("接口异常，MarketingRealGoodsApiHystrix.batchDeleteGoods(requestTicket={},placeId={},goodsIdList={},placeAccountBO={},)", requestTicket, placeId, new Gson().toJson(goodsIdList), placeAccountBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> batchImport(String requestTicket, List<BarcodeResponseBO> list) {
        log.error("接口异常，MarketingRealGoodsApiHystrix.batchImport(requestTicket={},list={})", requestTicket,new Gson().toJson(list));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}