package com.rzx.dim4.base.vo.marketing;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class StaticsWordOfMouthExportVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = 1427602295138466777L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 0)
    private String goodsName;

    /**
     * 销售量
     */
    @ExcelProperty(value = "销售量", index = 1)
    private int countSale;
    /**
     * 退货量
     */
    @ExcelProperty(value = "退货量", index = 2)
    private int countRefund;
    /**
     * 退货率
     */
    @ExcelProperty(value = "退货率", index = 3)
    private double refundRate;
    /**
     * 当前评分
     */
    @ExcelProperty(value = "当前评分", index = 4)
    private int score;
    /**
     * 评论次数
     */
    @ExcelProperty(value = "评论次数", index = 5)
    private long countComment;
}
