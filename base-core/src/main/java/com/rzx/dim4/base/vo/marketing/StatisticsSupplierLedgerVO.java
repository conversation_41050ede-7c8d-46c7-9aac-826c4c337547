package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> hwx
 * @Description TODO
 * @Date 2025/2/5 11:38
 */
@Getter
@Setter
public class StatisticsSupplierLedgerVO extends AbstractBO {

    private String placeId;
    private String supplierId;
    private String supplierName;

    /**
     * 采购商品品类
     */
    private int goodsKind;

    /**
     * 采购单总额
     */
    private int money;

    /**
     * 待结算总额
     */
    private int settleMoney;
}
