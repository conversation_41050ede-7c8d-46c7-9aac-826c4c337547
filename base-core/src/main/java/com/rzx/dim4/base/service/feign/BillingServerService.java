package com.rzx.dim4.base.service.feign;

import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.billing.third.ThirdAuthorityBO;
import com.rzx.dim4.base.bo.billing.third.ThirdPlaceConfigBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.BillingServerServiceHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 计费服务，待删除，不添加新接口，新写代码不依赖这里的方法，
 * 当前内部调用按 service 分类，接口里面方法数量太多，
 * 后粒度进一步拆分，service 下按对应实体类新建接口提供 api
 * <p>
 * 已新写接口
 * billing-service：
 *
 * @see com.rzx.dim4.base.service.feign.billing.PlaceBizConfigApi
 */
@Deprecated
@Primary
@FeignClient(value = FeginConstant.BILLING_SERVER, contextId = "BillingServerService", fallback = BillingServerServiceHystrix.class)
public interface BillingServerService {

    /***********************************/
    /************** 二维码相关 ************/
    /***********************************/
    @GetMapping("/billing/admin/logQrCode/findByToken")
    public GenericResponse<ObjDTO<LogQrCodeBO>> findQrCodeByToken(@RequestParam String token);

    @PostMapping("/billing/admin/logQrCode/saveLogQrCode")
    public GenericResponse<SimpleDTO> saveLogQrCode(@RequestHeader(value = "request_ticket") String requestTicket,
                                                    @RequestBody LogQrCodeBO logQrCodeBO);

    @PostMapping("/billing/admin/logQrCode/newLogQrCode")
    public GenericResponse<SimpleDTO> newLogQrCode(@RequestHeader(value = "request_ticket") String requestTicket,
                                                   @RequestBody LogQrCodeBO logQrCodeBO);

    @PostMapping("/billing/admin/logQrCode/clearHistoryQrcode")
    public GenericResponse<SimpleDTO> clearHistoryQrcode(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestParam String placeId,
                                                         @RequestParam int qrcodeRefreshTime,
                                                         @RequestParam int qrcodeRefreshTimeCashier);

    /***********************************/
    /************** 锁屏相关 ************/
    /***********************************/
    @GetMapping("/billing/admin/wallpaper/queryOneClientWallpaper")
    public GenericResponse<ObjDTO<ClientWallpaperBO>> queryOneClientWallpaper(@RequestParam int id);

    @GetMapping("/billing/admin/wallpaper/queryClientWallpaper")
    public GenericResponse<PagerDTO<ClientWallpaperBO>> queryClientWallpaper(
            @RequestParam(name = "length", defaultValue = "10") int length,
            @RequestParam(name = "start", defaultValue = "0") int start);

    @GetMapping("/billing/admin/wallpaper/queryClientWallpaperDeliver")
    public GenericResponse<PagerDTO<ClientWallpaperDeliverBO>> queryClientWallpaperDeliver(@RequestParam String search,
                                                                                           @RequestParam(name = "length", defaultValue = "10") int length,
                                                                                           @RequestParam(name = "start", defaultValue = "0") int start);

    @PostMapping("/billing/admin/wallpaper/saveClientWallpaper")
    public GenericResponse<SimpleDTO> saveClientWallpaper(@RequestHeader(value = "request_ticket") String requestTicket,
                                                          @RequestBody ClientWallpaperBO clientWallpaperBO);

    @PostMapping("/billing/admin/wallpaper/saveClientWallpaperDeliver")
    public GenericResponse<SimpleDTO> saveClientWallpaperDeliver(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody ClientWallpaperDeliverBO clientWallpaperDeliverBO);

    @PostMapping("/billing/admin/wallpaper/saveDefaultClientWallpaperDeliver")
    public GenericResponse<SimpleDTO> saveDefaultClientWallpaperDeliver(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody ClientWallpaperDeliverBO clientWallpaperDeliverBO);

    /***********************************/
    /************** 收银台消息通知相关 ****/
    /***********************************/
    @GetMapping("/billing/admin/cashierMessage/queryPageCashierMessageNotify")
    public GenericResponse<PagerDTO<CashierMessageNotifyBO>> queryPageCashierMessageNotify(
            @RequestParam(name = "length", defaultValue = "10") int length,
            @RequestParam(name = "start", defaultValue = "0") int start);


    @PostMapping("/billing/admin/cashierMessage/saveCashierMessage")
    public GenericResponse<SimpleDTO> saveCashierMessage(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestBody CashierMessageNotifyBO cashierMessageNotifyBO);

    @GetMapping("/billing/admin/cashierMessage/queryOneCashierMessage")
    public GenericResponse<ObjDTO<CashierMessageNotifyBO>> queryOneCashierMessage(@RequestParam String notifyId);

    @PostMapping("/billing/admin/cashierMessage/saveCashierMessageDeliver")
    public GenericResponse<SimpleDTO> saveCashierMessageDeliver(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody CashierMessageNotifyDeliverBO cashierMessageNotifyDeliverBO);


    @GetMapping("/billing/admin/cashierMessage/queryPageCashierMessageDeliver")
    public GenericResponse<PagerDTO<CashierMessageNotifyDeliverBO>> queryPageCashierMessageDeliver(@RequestParam String search,
                                                                                           @RequestParam(name = "length", defaultValue = "10") int length,
                                                                                           @RequestParam(name = "start", defaultValue = "0") int start);

    /***********************************/
    /************** 统计相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/logOperation/statisticsShiftByPlaceIdAndShiftId")
    public Map<String, Integer> statisticsShiftByPlaceIdAndShiftId(@RequestParam String placeId,
                                                                   @RequestParam String shiftId);

    @PostMapping("/billing/admin/logOperation/statisticsDashboardByPlaceId")
    public Map<String, Integer> statisticsDashboardByPlaceId(@RequestBody List<String> placeIds);

    @GetMapping("/billing/admin/logOperation/sumTopupByPlaceIdGroupByCardId")
    public GenericResponse<PagerDTO<StatisticSumTopupBO>> sumTopupByPlaceIdGroupByCardId(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam String placeId,
            @RequestParam String start, @RequestParam String end, @RequestParam String desc);

    @PostMapping("/billing/admin/logOperation/statisticsTopupAndConsumptionByCardId")
    public Map<String, Integer> statisticsTopupAndConsumptionByCardId(@RequestParam String placeId,
                                                                      @RequestParam String cardId, @RequestParam String startTime, @RequestParam String endTime);

    @GetMapping("/billing/admin/logLogin/sumConsumptionByPlaceIdGroupByIdNumber")
    public GenericResponse<PagerDTO<StatisticSumConsumptionBO>> sumConsumptionByPlaceIdGroupByIdNumber(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam String placeId,
            @RequestParam String start, @RequestParam String end, @RequestParam String orderColumn);

    @PostMapping("/billing/admin/statistics/queryDashboardStatistics")
    public GenericResponse<PagerDTO<StatisticsByDayBO>> queryDashboardStatistics(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam String startDate,
            @RequestParam String endDate, @RequestParam String placeId);

    // 按月统计查询 StatisticsByDayBO 表
    @PostMapping("/billing/admin/statistics/queryMonthDashboardStatistics")
    public GenericResponse<PagerDTO<StatisticsByDayBO>> queryMonthDashboardStatistics(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam String startDate,
            @RequestParam String endDate, @RequestParam String placeId);

    @PostMapping("/billing/admin/statistics/queryAgentDashboardStatistics")
    public Map<String, Integer> queryAgentDashboardStatistics(@RequestBody List<String> placeArray);

    @PostMapping("/billing/admin/statistics/querySumOnlineNum")
    public List<Map<String, String>> querySumOnlineNum(@RequestParam String startDate, @RequestParam String endDate,
                                                       @RequestBody List<String> placeIds);

    @PostMapping("/billing/admin/statistics/queryTotalConsumptionByPlaceIds")
    public List<Map<String, String>> queryTotalConsumptionByPlaceIds(@RequestParam String startDate, @RequestParam String endDate,
                                                                     @RequestBody List<String> placeIds);

    @PostMapping("/billing/admin/statistics/sumDashboardStatistics")
    public GenericResponse<ObjDTO<StatisticsByDayBO>> sumDashboardStatistics(@RequestParam String startDate, @RequestParam String endDate,
                                                                             @RequestBody List<String> placeIds);

    /**
     * 统计本班次的总收入(剔除支出)
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    @PostMapping("/billing/admin/logOperation/statisticsShiftAllCostByPlaceIdAndShiftId")
    public Map<String, Integer> statisticsShiftAllCostByPlaceIdAndShiftId(@RequestParam String placeId,
                                                                          @RequestParam String shiftId);

    /**
     * 统计 startDate -> endDate 每一天的总收入
     *
     * @param placeIds
     * @param startDate
     * @param endDate
     * @return
     */
    @PostMapping("/billing/admin/logOperation/statisticsEveryDaySumCost")
    public GenericResponse<ListDTO<StatisticsByDayBO>> statisticsEveryDaySumCost(@RequestParam String startDate, @RequestParam String endDate, @RequestParam List<String> placeIds);


    /**
     * 查询当日的收入详情
     *
     * @param placeId
     * @return
     */
    @PostMapping("/billing/admin/logOperation/incomeToDayDetails")
    public Map<String, Integer> incomeToDayDetails(@RequestParam String placeId, @RequestParam String startTime,
                                                   @RequestParam String endTime);

    /**
     * 查询时间段内的其他收支
     *
     * @param placeId
     * @return
     */
    @GetMapping("/billing/admin/logOperation/queryOtherIncome")
    public Map<String, Integer> queryOtherIncome(@RequestParam String placeId, @RequestParam String startDateTime,
                                                 @RequestParam String endDateTime);
    /**
     * 查询班次内的其他收支
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    @GetMapping("/billing/admin/logOperation/queryOtherIncomeByShift")
    public Map<String, Integer> queryOtherIncomeByShift(@RequestParam String placeId, @RequestParam String shiftId);

    /**
     * 查询当日的收入详情列表
     *
     * @param placeId
     * @return
     */
    @PostMapping("/billing/admin/logOperation/incomeToDayDetailList")
    public List<Map<String, Object>> incomeToDayDetailList(@RequestParam String placeId, @RequestParam String startTime,
                                                           @RequestParam String endTime);

    /**
     * 查询当前班次的收入详情
     *
     * @param placeId
     * @return
     */
    @PostMapping("/billing/admin/logOperation/incomeByShiftDetails")
    public Map<String, Integer> incomeByShiftDetails(@RequestParam String placeId, @RequestParam String shiftId);

    /***********************************/
    /************* 计费卡相关 ***********/
    /***********************************/
    @PostMapping("/billing/admin/billing/cardType/create")
    public GenericResponse<ObjDTO<BillingCardTypeBO>> createBillingCardType(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody BillingCardTypeBO cardTypeBO);

    @GetMapping("/billing/admin/billing/cardType/query")
    public GenericResponse<ListDTO<BillingCardTypeBO>> findCardTypeByPlaceId(@RequestParam String placeId);

    @PostMapping("/billing/admin/billing/cardType/queryCardType")
    public GenericResponse<ObjDTO<BillingCardTypeBO>> findCardTypeByPlaceIdAndCardTypeId(@RequestParam String placeId,
                                                                                         @RequestParam String cardTypeId);

    @PostMapping("/billing/admin/billing/cardType/findCardTypeByPoints")
    public GenericResponse<?> findCardTypeByPoints(@RequestBody BillingCardBO billingCard);

    @PostMapping("/billing/admin/billing/cardType/delete")
    GenericResponse<SimpleDTO> deleteCardType(@RequestHeader(value = "request_ticket") String requestTicket,
                                              @RequestParam Long id);

    @PostMapping("/billing/admin/billing/card/batchCreate")
    public GenericResponse<ObjDTO<BillingCardBO>> batchCreateBillingCard(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody List<BillingCardBO> billingCardBOList);

    /**
     * 临时接口,用完删除！！！
     * @param requestTicket
     * @param TempWanxiangUserBOs
     * @return
     */
    @PostMapping("/billing/support/wanxiangImport/saveAllTempWanxiangUsers")
    public GenericResponse<SimpleDTO> saveAllTempWanxiangUsers(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody List<TempWanxiangUserBO> TempWanxiangUserBOs);

    @PostMapping("/billing/admin/billing/card/findBillingCardInfos")
    GenericResponse<ListDTO<BillingCardBO>> findBillingCardInfos(@RequestBody String paramJson);

    @PostMapping("/billing/admin/billing/card/queryBillingCards")
    public GenericResponse<PagerDTO<BillingCardBO>> billingCards(@RequestParam String placeId,
                                                                 @RequestParam(name = "cardTypeId", defaultValue = "") String cardTypeId,
                                                                 @RequestParam(name = "idNumber", defaultValue = "") String idNumber,
                                                                 @RequestParam(name = "idName", defaultValue = "") String idName,
                                                                 @RequestParam(name = "size", defaultValue = "10") int size,
                                                                 @RequestParam(name = "page", defaultValue = "0") int page,
                                                                 @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                 @RequestParam(name = "order", defaultValue = "desc") String order);


    @PostMapping("/billing/admin/billing/card/queryChainBillingCards")
    public GenericResponse<PagerDTO<BillingCardBO>> chainBillingCards(@RequestParam String placeId,
                                                                      @RequestParam(name = "chainCardTypeId", defaultValue = "") String chainCardTypeId,
                                                                      @RequestParam(name = "idNumber", defaultValue = "") String idNumber,
                                                                      @RequestParam(name = "chainId", defaultValue = "") String chainId,
                                                                      @RequestParam(name = "idName", defaultValue = "") String idName,
                                                                      @RequestParam(name = "size", defaultValue = "10") int size,
                                                                      @RequestParam(name = "page", defaultValue = "0") int page,
                                                                      @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                      @RequestParam(name = "order", defaultValue = "desc") String order,
                                                                      @RequestParam(value = "createCardPlaceId", required = false) String createCardPlaceId);

    @PostMapping("/billing/admin/billing/card/activation")
    public GenericResponse<ObjDTO<BillingCardBO>> billingCardActivation(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String idNumber,
            @RequestParam(required = false) ActiveType activeType,
            @RequestParam(required = false,defaultValue = "") String phoneNumber);

    @GetMapping("/billing/admin/billing/card/findByPlaceIdAndCardIds")
    public GenericResponse<ListDTO<BillingCardBO>> findByPlaceIdAndCardIds(@RequestParam String placeId,
                                                                           @RequestParam List<String> cardIds);

    @GetMapping("/billing/admin/billing/card/findByPlaceIdAndCardId")
    public GenericResponse<ObjDTO<BillingCardBO>> findByPlaceIdAndCardId(@RequestParam String placeId,
                                                                         @RequestParam String cardId);

    @GetMapping("/billing/admin/billing/card/updateCardType")
    GenericResponse<SimpleDTO> updateCardType(@RequestHeader(value = "request_ticket") String requestTicket,
                                              @RequestParam String placeId, @RequestParam String cardTypeId, @RequestParam String cardIds);

    @GetMapping("/billing/admin/billing/card/chainUpdateCardType")
    GenericResponse<SimpleDTO> chainUpdateCardType(@RequestHeader(value = "request_ticket") String requestTicket,
                                                   @RequestParam String chainCardTypeId,
                                                   @RequestParam List<String> idNumbers,
                                                   @RequestParam String chainId);

    @GetMapping("/billing/admin/billing/card/verifyUpdateCardTypeParam")
    public GenericResponse<SimpleDTO> verifyUpdateCardTypeParam(@RequestParam String placeId, @RequestParam String cardTypeId, @RequestParam String cardId);

    @GetMapping("/billing/admin/billing/card/query")
    public GenericResponse<ObjDTO<BillingCardBO>> findBillingCard(@RequestParam String placeId,
                                                                  @RequestParam String idNumber);

    @GetMapping("/billing/admin/billing/card/queryAllType")
    public GenericResponse<ObjDTO<BillingCardBO>> findBillingCardAllType(@RequestParam String placeId,
                                                                         @RequestParam String idNumber);

    @PostMapping("/billing/admin/billing/blackList/blackLists")
    public GenericResponse<PagerDTO<BillingCardBlackListBO>> blackLists(@RequestBody Map<String, String> queryMap,
                                                                        @RequestParam(name = "page", defaultValue = "0") int page,
                                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                                        @RequestParam(name = "order", defaultValue = "desc") String order,
                                                                        @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    @PostMapping("/billing/admin/billing/blackList/save")
    public GenericResponse<ObjDTO<BillingCardBlackListBO>> saveBillingCardBlackList(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody BillingCardBlackListBO billingCardBlackListBO);

    @PostMapping("/billing/admin/billing/blackList/delete")
    public GenericResponse<SimpleDTO> deleteBillingCardBlackList(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String cardId);

    /***********************************/
    /********** 场所版本相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/version/queryClientVersion")
    public GenericResponse<PagerDTO<ClientVersionBO>> queryClientVersion(@RequestParam String versionNumber,
                                                                         @RequestParam String clientType, @RequestParam(name = "length", defaultValue = "10") int length,
                                                                         @RequestParam(name = "start", defaultValue = "0") int start,
                                                                         @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                         @RequestParam(name = "order", defaultValue = "desc") String order);

    @GetMapping("/billing/admin/version/checkVersionNumber")
    public GenericResponse<SimpleDTO> checkVersionNumber(@RequestParam String versionNumber,
                                                         @RequestParam int clientType);

    @GetMapping("/billing/admin/version/checkFileMD5")
    public GenericResponse<ObjDTO<ClientVersionBO>> checkFileMD5(@RequestParam int clientType,
                                                                 @RequestParam String fileMD5);

    @PostMapping("/billing/admin/version/save")
    public GenericResponse<SimpleDTO> save(@RequestHeader(value = "request_ticket") String requestTicket,
                                           @RequestBody ClientVersionBO clientVersionBO);

    @GetMapping("/billing/admin/version/findByVersionId")
    public GenericResponse<ObjDTO<ClientVersionBO>> findByVersionId(@RequestParam String VersionId);

    @PostMapping("/billing/admin/version/findClientVersions")
    public GenericResponse<ListDTO<ClientVersionBO>> findClientVersions(@RequestParam String clientType);

    @PostMapping("/billing/admin/upgrade/queryVersionUpgrade")
    public GenericResponse<PagerDTO<ClientUpgradeBO>> queryVersionUpgrade(@RequestParam String versionNumber,
                                                                          @RequestParam String placeId, @RequestParam String clientType,
                                                                          @RequestParam(name = "length", defaultValue = "10") int length,
                                                                          @RequestParam(name = "start", defaultValue = "0") int start,
                                                                          @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                          @RequestParam(name = "order", defaultValue = "desc") String order);

    @PostMapping("/billing/admin/upgrade/findClientUpgradeByPlaceId")
    public GenericResponse<ObjDTO<ClientUpgradeBO>> findClientUpgradeByPlaceId(@RequestParam String clientType,
                                                                               @RequestParam String placeId);

    @PostMapping("/billing/admin/upgrade/findClientUpgradeByPlaceIdIn")
    public GenericResponse<ListDTO<ClientUpgradeBO>> findByClientTypeAndDeletedAndPlaceIdIn(
            @RequestParam int clientType, @RequestBody List<String> placeIds);

    @PostMapping("/billing/admin/upgrade/clientUpgradeSave")
    public GenericResponse<SimpleDTO> clientUpgradeSave(@RequestHeader(value = "request_ticket") String requestTicket,
                                                        @RequestBody ClientUpgradeBO clientUpgradeBO);

    @PostMapping("/billing/admin/upgrade/findClientUpgrades")
    public GenericResponse<ListDTO<ClientUpgradeBO>> findClientUpgrades(@RequestParam String clientType);

    @PostMapping("/billing/admin/upgrade/findClientUpgradesByVersionId")
    public GenericResponse<ListDTO<ClientUpgradeBO>> findByClientTypeAndVersionIdOrderById(@RequestParam int clientType,
                                                                                           @RequestParam String versionId);

    @PostMapping("/billing/admin/upgrade/batchUpgradeSave")
    public GenericResponse<SimpleDTO> batchUpgradeSave(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestBody List<ClientUpgradeBO> clientUpgradeBOs);

    @Deprecated
    @PostMapping("/billing/admin/place/config/savePlaceBizConfig")
    GenericResponse<SimpleDTO> savePlaceBizConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                                  @RequestBody PlaceBizConfigBO placeBizConfigBO);

    @Deprecated
    @GetMapping("/billing/admin/place/config/queryPlaceBizConfig")
    GenericResponse<ObjDTO<PlaceBizConfigBO>> queryPlaceBizConfig(@RequestParam String placeId);

    /***********************************/
    /********** 普通计费相关 ************/
    /***********************************/
    @GetMapping("/billing/admin/billing/rule/common/queryCommonRuleS")
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> queryCommonRuleS(@RequestParam String placeId,
                                                                         @RequestParam String areaId, @RequestParam String cardTypeId);

    @GetMapping("/billing/admin/billing/rule/common/queryCommonRuleByPlaceIdAndRuleId")
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> queryCommonRuleByPlaceIdAndRuleId(@RequestParam String placeId, @RequestParam String ruleId);

    @PostMapping("/billing/admin/billing/rule/common/createBillingRuleCommons")
    GenericResponse<ObjDTO<BillingRuleCommonBO>> createBillingRuleCommons(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody BillingRuleCommonBO billingRuleCommonBO);

    @PostMapping("/billing/admin/billing/rule/common/batchCreateBillingRuleCommon")
    public GenericResponse<ListDTO<BillingRuleCommonBO>> batchCreate(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody List<BillingRuleCommonBO> billingRuleCommonBOS);

    @GetMapping("/billing/admin/billing/rule/common/deleteBillingRuleCommon")
    public GenericResponse<SimpleDTO> deleteBillingRuleCommon(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String cardTypeId, @RequestParam String areaId);

    @PostMapping("/billing/admin/billing/rule/common/editBillingRuleCommonConsume")
    public GenericResponse<ObjDTO<BillingRuleCommonBO>> editBillingRuleCommonConsume(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String ruleId,
            @RequestParam int minConsume,
            @RequestParam int unitConsume,
            @RequestParam int deductionTime,
            @RequestParam int freeMinutes);


    //查询场所的最小费率和最大费率
    @PostMapping("/billing/admin/billing/rule/common/queryMinPriceAndMaxPriceByPlaceIds")
    public GenericResponse<ListDTO<PlaceProfileBO>> queryMinPriceAndMaxPriceByPlaceIds(@RequestParam List<String> placeIds);

    /***********************************/
    /********** 包时规则相关 ************/
    /***********************************/
    @GetMapping("/billing/admin/billing/rule/packageTime/queryPageBillingRulePackageTime")
    public GenericResponse<PagerDTO<BillingRulePackageTimeBO>> queryPageBillingRulePackageTime(
            @RequestParam String placeId, @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestParam(required = false) String areaId,
            @RequestParam(required = false) String cardTypeId,
            @RequestParam(required = false) SourceType sourceType,
            @RequestParam(value = "packageFlag", required = false) String packageFlag,
            @RequestParam(value = "ruleName", required = false) String ruleName);

    @GetMapping("/billing/admin/billing/rule/packageTime/queryBillingRulePackageTime")
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime(@RequestParam String placeId,
                                                                                         @RequestParam String ruleId);

    @PostMapping("/billing/admin/billing/rule/packageTime/create")
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> createBillingRulePackageTime(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody BillingRulePackageTimeBO billingRulePackageTimeBO);

    @GetMapping("/billing/admin/billing/rule/packageTime/delete")
    public GenericResponse<SimpleDTO> deleteBillingRulePackageTime(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String ruleId);

    /***********************************/
    /*********** 累计包时相关 ************/
    /***********************************/
    @GetMapping("/billing/admin/billing/rule/acc/queryPageBillingRuleAcc")
    public GenericResponse<PagerDTO<BillingRuleAccBO>> queryPageBillingRuleAcc(@RequestParam String placeId,
                                                                               @RequestParam(name = "size", defaultValue = "10") int size,
                                                                               @RequestParam(name = "page", defaultValue = "1") int page,
                                                                               @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                               @RequestParam(name = "order", defaultValue = "desc") String order);

    @GetMapping("/billing/admin/billing/rule/acc/queryBillingRuleAcc")
    public GenericResponse<ObjDTO<BillingRuleAccBO>> queryBillingRuleAcc(@RequestParam String placeId,
                                                                         @RequestParam String ruleId);

    @PostMapping("/billing/admin/billing/rule/acc/create")
    public GenericResponse<ObjDTO<BillingRuleAccBO>> createBillingRuleAcc(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody BillingRuleAccBO billingRuleAccBO);

    @GetMapping("/billing/admin/billing/rule/acc/delete")
    public GenericResponse<SimpleDTO> deleteBillingRuleAcc(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String placeId,
            @RequestParam String ruleId);

    /***********************************/
    /********** 充值规则相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/topup/rule/create")
    GenericResponse<SimpleDTO> createTopupRule(@RequestHeader(value = "request_ticket") String requestTicket,
                                               @RequestBody TopupRuleBO topupRuleBo);

    @PostMapping("/billing/admin/topup/rule/delete")
    GenericResponse<ObjDTO<TopupRuleBO>> deleteTopupRule(@RequestHeader(value = "request_ticket") String requestTicket,
                                                         @RequestBody TopupRuleBO topupRuleBo);

    @GetMapping("/billing/admin/topup/rule/queryByPlaceId")
    GenericResponse<ListDTO<TopupRuleBO>> queryTopupRuleByPlaceId(@RequestParam String placeId);

    @GetMapping("/billing/admin/topup/rule/queryByPlaceIdAndTopupRuleId")
    GenericResponse<ObjDTO<TopupRuleBO>> queryTopupRuleByPlaceIdAndTopupRuleId(@RequestParam String placeId,
                                                                               @RequestParam String topupRuleId);

    /***********************************/
    /********** 充值订单相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/logTopup/queryLogTopups")
    public GenericResponse<PagerDTO<LogTopupBO>> queryLogTopups(@RequestBody Map<String, Object> queryMap,
                                                                @RequestParam(name = "size", defaultValue = "10") int size,
                                                                @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/billing/admin/logTopup/querySumCashTopupAndPresentTopup")
    public Map<String, Integer> querySumCashTopupAndPresentTopup(@RequestBody Map<String, Object> queryMap);

    /***********************************/
    /********** 退款订单相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/logRefund/queryLogRefunds")
    public GenericResponse<PagerDTO<LogRefundBO>> queryLogRefunds(@RequestBody Map<String, Object> queryMap,
                                                                  @RequestParam(name = "size", defaultValue = "10") int size,
                                                                  @RequestParam(name = "page", defaultValue = "0") int page);

    @PostMapping("/billing/admin/logRefund/querySumCashRefundAndOnlineRefund")
    public Map<String, Integer> querySumCashRefundAndOnlineRefund(@RequestBody Map<String, Object> queryMap);

    @PostMapping("/billing/admin/logRefund/refund")
    GenericResponse<ObjDTO<PaymentRefundOrderBO>> refund(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                @RequestParam String placeId,
                                                                @RequestParam String orderId,
                                                                @RequestParam String operatorName,
                                                                @RequestParam String sourceType);

    @PostMapping("/billing/admin/logRefund/reissue")
    public GenericResponse<SimpleDTO> reissue(@RequestHeader(value = "request_ticket") String requestTicket,
                                              @RequestParam String placeId, @RequestParam String orderId);

    @PostMapping("/billing/admin/billing/card/billingCardUpdateAccount")
    public GenericResponse<?> billingCardUpdateAccount(@RequestHeader(value = "request_ticket") String requestTicket,
                                                       @RequestParam String placeId, @RequestParam String cardId, @RequestParam int cashAccount,
                                                       @RequestParam int presentAccount, @RequestParam int updateFlag, @RequestParam SourceType sourceType,
                                                       @RequestParam(required = false) String cardTypeId,@RequestParam(required = false) String orderId);

    @PostMapping("/billing/admin/billing/card/refundForInternetFeePackageTimeOrder")
    GenericResponse<SimpleDTO> refundForInternetFeePackageTimeOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                    @RequestBody BillingCardBalanceUpdateRequestBO paramsBo);


    @PostMapping("/billing/admin/billing/card/updateBillingCardAccountForRefund")
    GenericResponse<SimpleDTO> updateBillingCardAccountForRefund(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                 @RequestBody BillingCardBalanceUpdateRequestBO paramsBo);

    /***********************************/
    /********* 第三方用户调用相关 *********/
    /***********************************/

    @GetMapping("/billing/third/placeConfig/findByPlaceId")
    public GenericResponse<ObjDTO<ThirdPlaceConfigBO>> findThirdPlaceConfigByPlaceId(@RequestParam String placeId);

    @PostMapping("/billing/third/account/accounts")
    public GenericResponse<PagerDTO<ThirdAccountBO>> accounts(@RequestBody Map<String, String> queryMap,
                                                              @RequestParam(name = "page", defaultValue = "0") int page,
                                                              @RequestParam(name = "size", defaultValue = "10") int size,
                                                              @RequestParam(name = "order", defaultValue = "desc") String order,
                                                              @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns);

    @GetMapping("/billing/third/account/getThirdAccount")
    public GenericResponse<ObjDTO<ThirdAccountBO>> getThirdAccount(@RequestParam String thirdAccountId);

    @PostMapping("/billing/third/account/save")
    public GenericResponse<ObjDTO<ThirdAccountBO>> saveThirdAccount(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody ThirdAccountBO thirdAccountBO);

    @GetMapping("/billing/third/authority/findThirdAuthorityByThirdAccountId")
    public GenericResponse<ObjDTO<ThirdAuthorityBO>> findThirdAuthorityByThirdAccountId(
            @RequestParam String thirdAccountId);

    @PostMapping("/billing/third/authority/save")
    public GenericResponse<ObjDTO<ThirdAuthorityBO>> saveThirdAuthority(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody ThirdAuthorityBO thirdAuthorityBO);

    /***********************************/
    /************ 移动端相关 ************/
    /***********************************/
    @GetMapping("/billing/mobile/billing/card/queryConsumeBalance")
    public GenericResponse<ListDTO<BillingCardBO>> queryConsumeBalance(@RequestParam("idNumber") String idNumber);

    @GetMapping("/billing/mobile/billing/card/cardDetails")
    public GenericResponse<ObjDTO<BillingCardBO>> cardDetails(@RequestParam("placeId") String placeId,
                                                              @RequestParam("idNumber") String idNumber);

    @GetMapping("/billing/mobile/billing/card/queryTopupRules")
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRules(@RequestParam String placeId,
                                                                 @RequestParam String cardTypeId);

    @GetMapping("/billing/mobile/billing/card/queryTopupRulesNew")
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRulesNew(@RequestParam String placeId,
                                                                    @RequestParam String cardTypeId);

    @GetMapping("/billing/mobile/billing/card/queryTopupGift")
    public GenericResponse<ObjDTO<TopupRuleBO>> queryTopupGift(@RequestParam String placeId,
                                                               @RequestParam String idNumber, @RequestParam int amount, @RequestParam String cardTypeId);

    @PostMapping("/billing/mobile/login/clientLogin")
    public GenericResponse<ObjDTO<BillingOnlineBO>> clientLogin(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                @RequestParam("placeId") String placeId,
                                                                @RequestParam("clientId") String clientId,
                                                                @RequestParam("areaId") String areaId,
                                                                @RequestParam("idNumber") String idNumber,
                                                                @RequestParam(required = false, value = "unlockCode") String unlockCode);

    @GetMapping("/billing/mobile/billing/card/create")
    GenericResponse<SimpleDTO> weChatTopupOrderCreate(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestParam String placeId,
                                                      @RequestParam String idNumber,
                                                      @RequestParam int amount);

    @GetMapping("/billing/mobile/billing/card/createOrderPay")
    GenericResponse<ObjDTO<PaymentResultBO>> createOrderPayByAppletOfWeChat(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                            @RequestParam String placeId,
                                                                            @RequestParam String idNumber,
                                                                            @RequestParam int amount,
                                                                            @RequestParam String openId, @RequestParam(required = false) String appId);

    @PostMapping("/billing/mobile/billing/card/createBillingCard")
    GenericResponse<ObjDTO<BillingCardBO>> weChatCreateBillingCard(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam String placeId,
            @RequestParam String activeType,
            @RequestParam(required = false) String cardTypeId,
            @RequestParam(required = false,defaultValue = "") String phoneNumber);

    /**
     * @param requestTicket
     * @param orderId
     * @return
     * @see com.rzx.dim4.billing.web.controller.mobile.MobileBillingCardController#queryWechatTopupOrder(String, String)
     */
    @GetMapping("/billing/mobile/billing/card/query")
    GenericResponse<ObjDTO<PaymentOrderBO>> queryWechatTopupOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                  @RequestParam String orderId);

    @PostMapping("/billing/mobile/billing/card/queryPayList")
    GenericResponse<PagerDTO<LogTopupBO>> queryWeChatTopupOrders(@RequestBody Map<String, Object> queryMap,
                                                                 @RequestParam(name = "size", defaultValue = "10") int size,
                                                                 @RequestParam(name = "page", defaultValue = "1") int page);

    /***********************************/
    /*********** 上机记录相关 ************/
    /***********************************/
    @GetMapping("/billing/mobile/billing/online/queryFinishTop5")
    public GenericResponse<ListDTO<BillingOnlineBO>> queryFinishTop5(@RequestParam String placeId,
                                                                     @RequestParam String idNumber);

    @PostMapping("/billing/admin/billing/online/queryOnlineClientInfos")
    GenericResponse<ListDTO<BillingOnlineBO>> queryOnlineClientInfos(
            @RequestBody ListDTO<BillingOnlineBO> billingOnlineBOListDTO);

    @GetMapping("/billing/mobile/billing/online/queryBillingOnlineByIdNumber")
    public GenericResponse<ObjDTO<BillingOnlineBO>> queryBillingOnlineByIdNumber(@RequestParam("placeId") String placeId,
            @RequestParam("idNumber") String idNumber);

    @PostMapping("/billing/admin/billing/online/queryLateBillingOnlines")
    public GenericResponse<ListDTO<BillingOnlineBO>> queryLateBillingOnlines(@RequestParam String placeId,
                                                                             @RequestParam List<String> cardIds);

    @PostMapping("/billing/admin/billing/online/queryChainLateBillingOnlines")
    public GenericResponse<ObjDTO<BillingOnlineBO>> queryChainLateBillingOnlines(@RequestParam List<String> placeIds,
                                                                                 @RequestParam String idNumber);

    @PostMapping("/billing/admin/billing/online/logout")
    public GenericResponse<ObjDTO<LogLoginBO>> logout(@RequestHeader(value = "request_ticket") String requestTicket,
                                                      @RequestParam("placeId") String placeId,
                                                      @RequestParam("clientId") String clientId,
                                                      @RequestParam("cardId") String cardId,
                                                      @RequestParam("operationAccountId") String operationAccountId,
                                                      @RequestParam("operationAccountName") String operationAccountName,
                                                      @RequestParam("sourcesType") SourceType sourcesType);

    @GetMapping("/billing/admin/billing/online/findUnfinishedByPlaceIdAndClientId")
    public GenericResponse<ObjDTO<BillingOnlineBO>> findUnfinishedByPlaceIdAndClientId(@RequestParam String placeId,
                                                                                       @RequestParam String clientId);

    @PostMapping("/billing/admin/billing/online/findUnfinishedByPlaceIdAndClientIds")
    public GenericResponse<ListDTO<BillingOnlineBO>> findUnfinishedByPlaceIdAndClientIds(@RequestParam String placeId,
                                                                                         @RequestParam List<String> clientIds);

    @PostMapping("/billing/cashier/findLogOperation")
    GenericResponse<PagerDTO<LogOperationBO>> findLogOperation(@RequestBody Map<String, Object> queryMap,
                                                               @RequestParam(name = "size", defaultValue = "10") int size,
                                                               @RequestParam(name = "page", defaultValue = "1") int page,
                                                               @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                               @RequestParam(name = "order", defaultValue = "desc") String order);

    @PostMapping("/billing/cashier/querySumCostAndPresent")
    public Map<String, Integer> querySumCostAndPresent(@RequestBody Map<String, Object> queryMap);

    @GetMapping("/billing/cashier/findUnfinishedByPlaceId")
    public List<BillingOnlineBO> findUnfinishedByPlaceId(@RequestParam String placeId);

    @GetMapping("/billing/cashier/findLogLogin")
    public GenericResponse<PagerDTO<LogLoginBO>> findLogLogin(@RequestParam(value = "placeId") String placeId,
                                                              @RequestParam(value = "idNumber", defaultValue = "") String idNumber,
                                                              @RequestParam(name = "clientName", defaultValue = "") String clientName,
                                                              @RequestParam(name = "startDate", defaultValue = "") String startDate,
                                                              @RequestParam(name = "endDate", defaultValue = "") String endDate,
                                                              @RequestParam(name = "size", defaultValue = "10") int size,
                                                              @RequestParam(name = "page", defaultValue = "1") int page,
                                                              @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,

                                                              @RequestParam(name = "order", defaultValue = "desc") String order);

    /**
     * 去每日统计表中按日期查找有在线人数的网吧(除酒店)
     */
    @GetMapping("/billing/admin/billing/online/queryPlaceIdsByCountDay")
    public List<String> queryPlaceIdsByCountDay(@RequestParam String startTime, @RequestParam String endTime);

    /***********************************/
    /********** 收银台任务相关 ************/
    /***********************************/
    @PostMapping("/billing/admin/cashier/task/create")
    public GenericResponse<ObjDTO<CashierTaskBO>> createCashierTask(
            @RequestHeader(value = "request_ticket") String requestTicket, @RequestBody CashierTaskBO cashierTaskBO);

    @GetMapping("/billing/admin/cashier/task/list")
    public GenericResponse<PagerDTO<CashierTaskBO>> listCashierTask(
            @RequestParam(value = "start", defaultValue = "0") int start,
            @RequestParam(value = "length", defaultValue = "10") int length,
            @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
            @RequestParam(name = "order", defaultValue = "desc") String order,
            @RequestParam(value = "search[value]", required = false) String search);

    /***********************************/
    /********** 收银台权限相关 ***********/
    /***********************************/
    @PostMapping("/billing/admin/cashier/authority/create")
    public GenericResponse<ObjDTO<CashierAuthorityBO>> createCashierAuthority(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestBody CashierAuthorityBO cashierAuthorityBO);

//	@GetMapping("/billing/admin/cashier/authority/findCurrCashierAuthorityByPlaceId")
//	public GenericResponse<ObjDTO<CashierAuthorityBO>> findCurrCashierAuthorityByPlaceId(@RequestParam String placeId);

    @GetMapping("/billing/admin/cashier/authority/findCashierAuthorityByPlaceIdAndAccountId")
    public GenericResponse<ObjDTO<CashierAuthorityBO>> findCashierAuthorityByPlaceIdAndAccountId(
            @RequestParam String placeId, @RequestParam String accountId);

    @GetMapping("/billing/admin/cashier/authority/findNeedServiceIndexes")
    public List<ServiceIndexes> findNeedServiceIndexes();

    /***********************************/
    /********** 班次相关 ***********/
    /***********************************/
    @GetMapping("/billing/admin/logShift/saveLogShift")
    public GenericResponse<SimpleDTO> saveLogShift(@RequestParam String placeId, @RequestParam String shiftId);

    /***********************************/
    /********** 统计相关 ***********/
    /***********************************/
    @PostMapping("/billing/admin/statisticsOperation/incomeTotal")
    GenericResponse<PagerDTO<StatisticsOperationByDayBO>> incomeTotal(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam List<String> placeIds,
            @RequestParam String startTime, @RequestParam String endTime);

    @PostMapping("/billing/admin/statisticsOperation/incomeTotalByMonth")
    GenericResponse<PagerDTO<StatisticsOperationByDayBO>> incomeTotalByMonth(
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "page", defaultValue = "0") int page, @RequestParam List<String> placeIds,
            @RequestParam String startTime, @RequestParam String endTime);

    @PostMapping("/billing/admin/statisticsOperation/sumStatisticsOperationByDay")
    GenericResponse<ObjDTO<SumStatisticsOperationByDayBO>> sumStatisticsOperationByDay(@RequestParam List<String> placeIds,
                                                                                       @RequestParam String startTime, @RequestParam String endTime);

    @PostMapping("/billing/admin/statisticsOperation/sumTotalIncome")
    GenericResponse<ListDTO<StatisticsOperationByDayBO>> sumTotalIncome(@RequestParam List<String> placeIds,
                                                                        @RequestParam String startTime,
                                                                        @RequestParam String endTime,
                                                                        @RequestParam int flag);

    @PostMapping("/billing/admin/statisticsOperation/sumTotalIncomeRank")
    GenericResponse<ListDTO<StatisticsOperationByDayBO>> sumTotalIncomeRank(@RequestParam List<String> placeIds,
                                                                            @RequestParam int flag);

    /***********************************/
    /********** 附加费相关 ***********/
    /***********************************/
    @GetMapping("/billing/admin/billing/surcharge/getActiveTypes")
    public Map<String, String> getActiveTypes();

    @GetMapping("/billing/admin/billing/surcharge/querySurchargeConfigByPlaceId")
    public GenericResponse<ObjDTO<SurchargeConfigBO>> querySurchargeConfigByPlaceId(@RequestParam String placeId);

    @PostMapping("/billing/admin/billing/surcharge/saveSurchargeConfig")
    public GenericResponse<SimpleDTO> saveSurchargeConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                                          @RequestBody SurchargeConfigBO surchargeConfigBO);

    /***********************************/
    /********** 微信小程序相关 ***********/
    /***********************************/
    @PostMapping("/billing/miniApp/reversal/initReversalTable")
    public GenericResponse<ListDTO<LogOperationBO>> findLogOperationByReversalAndDateTime(@RequestParam String placeId,
                                                                                          @RequestParam String startDateTime, @RequestParam String endDateTime);

    @PostMapping("/billing/miniApp/reversal/findHistoryPlacePage")
    public GenericResponse<ListDTO<LogOperationBO>> findHistoryPlacePage(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                         @RequestParam String idNumber);

    @PostMapping("/billing/miniApp/reversal/findConsumerRanking")
    public List<Map<String, String>> findConsumerRanking(@RequestParam String placeId, @RequestParam String startDateTime,
                                                         @RequestParam String endDateTime, @RequestParam String type);

    @PostMapping("/billing/miniApp/statistics/queryStatisticsOnline")
    public GenericResponse<ListDTO<StatisticsOnlineByHourBO>> queryStatisticsOnline(@RequestParam String placeId,
                                                                                    @RequestParam String countDay);

    @PostMapping("/billing/miniApp/card/queryTemporaryCard")
    public GenericResponse<ListDTO<BillingCardBO>> queryTemporaryCard(@RequestParam String placeId);

    @PostMapping("/billing/miniApp/statistics/statisticsOnlineCard")
    public GenericResponse<ObjDTO<StatisticsOnlineCardBO>> statisticsOnlineCard(@RequestParam String placeId);

    @PostMapping("/billing/miniApp/checkout/batchLogout")
    public GenericResponse<?> batchLogout(@RequestParam String placeId, @RequestParam String clientIds,
                                          @RequestParam String cardIds, @RequestParam String loginName, @RequestParam String loginPass,
                                          @RequestParam String sourceType);

    @PostMapping("/billing/miniApp/checkout/queryPageLogout")
    public GenericResponse<PagerDTO<LogLoginBO>> queryPageLogout(@RequestParam("placeId") String placeId,
                                                                 @RequestParam(name = "startLogoutTime", defaultValue = "") String startLogoutTime,
                                                                 @RequestParam(name = "endLogoutTime", defaultValue = "") String endLogoutTime,
                                                                 @RequestParam(name = "logoutType", defaultValue = "") int logoutType,
                                                                 @RequestParam(name = "loginIds", defaultValue = "") String loginIds,
                                                                 @RequestParam(name = "size", defaultValue = "10") int size,
                                                                 @RequestParam(name = "page", defaultValue = "0") int page,
                                                                 @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                 @RequestParam(name = "order", defaultValue = "desc") String order);

    /***********************************/
    /********** Boss数据转换 ***********/
    /***********************************/
    @PostMapping("/billing/support/bossData/updateExpired")
    public GenericResponse<SimpleDTO> updateExpired(@RequestBody Map<String, Object> map);

    /***********************************/
    /********** 会员积分 ***********/
    /***********************************/
    @GetMapping("/billing/admin/billing/points/queryRewardPointsRule")
    public GenericResponse<ObjDTO<RewardPointsRuleBO>> queryRewardPointsRule(@RequestParam String placeId);

    @PostMapping("/billing/admin/billing/points/editRewardPoints")
    public GenericResponse<ObjDTO<RewardPointsRuleBO>> editRewardPoints(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                        @RequestBody RewardPointsRuleBO rewardPointsRuleBO);

    @GetMapping("/billing/admin/billing/points/queryPageExchangePointsRule")
    public GenericResponse<PagerDTO<ExchangePointsRuleBO>> queryPageExchangePointsRule(@RequestParam String placeId,
                                                                                       @RequestParam(name = "size", defaultValue = "10") int size,
                                                                                       @RequestParam(name = "page", defaultValue = "1") int page,
                                                                                       @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                                       @RequestParam(name = "order", defaultValue = "desc") String order);

    @PostMapping("/billing/admin/billing/points/editExchangePoints")
    public GenericResponse<ObjDTO<ExchangePointsRuleBO>> editExchangePoints(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                            @RequestBody ExchangePointsRuleBO exchangePointsRuleBO);

    @PostMapping("/billing/admin/billing/points/deleteExchangePoints")
    public GenericResponse<SimpleDTO> deleteExchangePoints(@RequestHeader(value = "request_ticket") String requestTicket,
                                                           @RequestParam String placeId,
                                                           @RequestParam String exchangePointsRuleId);

    @GetMapping("/billing/admin/billing/points/initExchangePointsTable")
    public GenericResponse<PagerDTO<LogPointsBO>> initExchangePointsTable(@RequestParam(name = "size", defaultValue = "10") int size,
                                                                          @RequestParam(name = "page", defaultValue = "0") int page,
                                                                          @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                          @RequestParam(name = "order", defaultValue = "desc") String order,
                                                                          @RequestParam(name = "placeId") String placeId,
                                                                          @RequestParam(value = "exchangePointsType", defaultValue = "") String exchangePointsType,
                                                                          @RequestParam(value = "idNumber", defaultValue = "") String idNumber,
                                                                          @RequestParam(name = "cardTypeId", defaultValue = "") String cardTypeId,
                                                                          @RequestParam(name = "createrName", defaultValue = "") String createrName,
                                                                          @RequestParam(name = "idName", defaultValue = "") String idName,
                                                                          @RequestParam(name = "startDate") String startDate,
                                                                          @RequestParam(name = "endDate") String endDate);

    /***********************************/
    /********** 会员数据 ***********/
    /***********************************/

    /**
     * 查询PC页面web和收银台/客户端接口查询的次数限制
     */
    @GetMapping("/billing/admin/billing/securityRequestConfig/queryInterfaceRequestNumberLimit")
    public GenericResponse<ObjDTO<SecurityRequestConfigBO>> queryInterfaceRequestNumberLimit(@RequestParam int type);


    /***********************************/
    /********** 会员数据 ***********/
    /***********************************/

    /**
     * 通过placeId查询PC页面web和收银台/客户端接口查询的次数限制
     * 每个网吧场所单独配置请求次数
     */
    @GetMapping("/billing/admin/billing/securityRequestConfig/queryInterfaceRequestNumberByPlaceId")
    public GenericResponse<ObjDTO<SecurityRequestConfigBO>> queryInterfaceRequestNumberByPlaceId(@RequestParam String placeId);

    @PostMapping("/billing/cashier/activeCard4Dim")
    GenericResponse<?> activeCard4Dim(@RequestHeader("request_ticket") String requestTicket,
                        @RequestParam String placeId,
                        @RequestParam String idNumber,
                        @RequestParam(required = false,defaultValue = "") String phoneNumber);
}