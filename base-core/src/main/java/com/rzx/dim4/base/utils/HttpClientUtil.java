package com.rzx.dim4.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpClientUtil {

	private static RequestConfig buildRequestConfig() {
		return buildRequestConfig(5000);
	}

	private static RequestConfig buildRequestConfig(int timeout) {
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(timeout)
				.setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).setRedirectsEnabled(true).build();
		return requestConfig;
	}

	public static String doGetRequest(String url) {
		return doGetRequest(url,5000);
	}

	/**
	 * 发送http GET请求，并返回http响应字符串
	 * 
	 * @param url 完整的请求url字符串
	 * @return
	 */
	public static String doGetRequest(String url,Integer connectTimeout) {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		httpGet.setConfig(buildRequestConfig(connectTimeout));
		try {
			HttpResponse httpResponse = httpClient.execute(httpGet);
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				log.info("doGetRequest请求成功");
				String responseString = EntityUtils.toString(httpResponse.getEntity());
				return responseString;
			} else {
				log.error("doGetRequest请求 [{}] 没有正常返回, status:::{}", url, httpResponse.getStatusLine());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				httpClient.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 发送POST请求
	 * 
	 * @param url
	 * @return
	 */
	public static String doPostRequest(String url) {
		return doPostRequest(url, new HashMap<String, String>());
	}

	/**
	 * 发送带参数的POST请求，
	 * 
	 * @param url
	 * @param params
	 * @return
	 */
	public static String doPostRequest(String url, Map<String, String> params) {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setConfig(buildRequestConfig());
		List<BasicNameValuePair> list = new ArrayList<BasicNameValuePair>();
		params.forEach((k, v) -> {
			list.add(new BasicNameValuePair(k, v)); // 请求参数
		});
		try {
			UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, "UTF-8");
			httpPost.setEntity(entity);
			HttpResponse httpResponse = httpClient.execute(httpPost);
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				String responseString = EntityUtils.toString(httpResponse.getEntity());
				return responseString;
			} else {
				log.error("请求 [{}] 没有正常返回, status:::{}", url, httpResponse.getStatusLine());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 发送POST字符串JSON请求
	 * 
	 * @param url
	 * @param json
	 * @return
	 */
	public static String doPostRequest(String url, String json) {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setConfig(buildRequestConfig());
		httpPost.setHeader("Content-Type", "application/json"); //
		try {
			httpPost.setEntity(new StringEntity(json, ContentType.create("application/json", "utf-8")));
			HttpResponse httpResponse = httpClient.execute(httpPost);
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				String responseString = EntityUtils.toString(httpResponse.getEntity());
				return responseString;
			} else {
				log.error("请求 [{}] 没有正常返回, status:::{}", url, httpResponse.getStatusLine());
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

}
