package com.rzx.dim4.base.service.callback.billing;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.RegcardAlgorithmApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/2/26
 **/
@Slf4j
@Service
public class RegcardAlgorithmApiHystrix implements RegcardAlgorithmApi {
    /**
     * 关闭场所注册卡开关，并发送企业微信通知
     *
     * @param requestTicket
     * @param placeConfigBO
     * @return
     */
    @Override
    public GenericResponse<SimpleDTO> closeRegcard(String requestTicket, PlaceConfigBO placeConfigBO) {
        log.error("接口异常:::closeRegcard(requestTicket:::{},placeConfigBO:::{})", requestTicket, placeConfigBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
