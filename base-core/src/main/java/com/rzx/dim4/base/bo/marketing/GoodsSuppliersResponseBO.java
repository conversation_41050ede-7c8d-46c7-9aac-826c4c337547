package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@Setter
@ApiModel(description = "供应商信息响应对象")
public class GoodsSuppliersResponseBO extends AbstractEntityBO {

    @ApiModelProperty(value = "场所ID")
    private String placeId;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称", required = true)
    private String supplierName;

    @ApiModelProperty(value = "供应商电话", required = true)
    private String supplierPhone;

    @ApiModelProperty(value = "供货品类")
    private int supplyCategory;

    @ApiModelProperty(value = "供应周期: 0现结，1挂账，2上打下，3月结，4季结，5结算方式")
    private int supplyCycle;

    @ApiModelProperty(value = "结算周期(天)")
    private int settlementCycle;

    @ApiModelProperty(value = "供应商地址")
    private String supplierAddr;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "名片正面")
    private String frontBusinessCard;

    @ApiModelProperty(value = "名片反面")
    private String reverseBusinessCard;
}