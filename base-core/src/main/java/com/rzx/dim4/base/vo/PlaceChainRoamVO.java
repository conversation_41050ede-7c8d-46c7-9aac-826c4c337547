package com.rzx.dim4.base.vo;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class PlaceChainRoamVO extends AbstractEntityBO implements Serializable {

    private static final long serialVersionUID = -2089233135236964678L;

    private String loginId;
    private String currPlaceId; // 扣费placeId
    private String roamCostPlaceId; // 漫游扣费placeId
    private String placeName; // 漫游扣费场所名称
    private int roamCashAccount; //漫游扣费本金
    private int roamPresentAccount; // 漫游扣费奖励

}
