package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.GoodsStorageRackResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsSuppliersResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsTagsResponseBO;
import com.rzx.dim4.base.bo.marketing.GoodsTypeResponseBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingCommonApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
public class MarketingCommonApiHystrix implements MarketingCommonApi {

    @Override
    public GenericResponse<ListDTO<GoodsTypeResponseBO>> findGoodsCategoryList(String placeId) {
        log.error("接口异常:::MarketingCommonApiHystrix.findGoodsCategoryList(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsSuppliersResponseBO>> findSupplierList(String placeId) {
        log.error("接口异常:::MarketingCommonApiHystrix.findSupplierList(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsTagsResponseBO>> findTagList(String placeId) {
        log.error("接口异常:::MarketingCommonApiHystrix.findTagList(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsStorageRackResponseBO>> findStorageRackList(String placeId) {
        log.error("接口异常:::MarketingCommonApiHystrix.findStorageRackList(placeId={})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}