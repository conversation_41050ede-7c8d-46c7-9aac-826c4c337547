package com.rzx.dim4.base.service.feign.marketing;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.rzx.dim4.base.bo.marketing.*;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsMoveRecordApiHystrix;
import com.rzx.dim4.base.service.callback.marketing.MarketingGoodsStockingApiHystrix;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 库存管理->上下架管理相关接口
 *
 * <AUTHOR>
 * @date 2025年02月20日 14:42
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "MarketingGoodsStockingApi", fallback = MarketingGoodsStockingApiHystrix.class)
public interface MarketingGoodsStockingApi {

    String URL = "/feign/marketing/goodsStocking";

    /**
     * 保存商品盘点
     *
     * @param addBO
     * @return
     */
    @PostMapping(URL + "/save")
    GenericResponse<ListDTO<GoodsStocktakingBO>> saveGoodsStocktaking(@RequestHeader(value = "request_ticket") String requestTicket, @RequestBody SaveGoodsStockingAddBO addBO);


    /**
     * 查询商品盘点详情
     *
     * @param placeId        场所id，必填
     * @param stocktakingNum 商品盘点编号，必填
     * @return
     */
    @GetMapping(URL + "/findDetails")
    GenericResponse<ObjDTO<GoodsStocktakingBO>> findGoodsStocktakingDetails(@RequestParam String placeId, @RequestParam String stocktakingNum);


    /**
     * 分页查询商品盘点列表
     *
     * @param placeId
     * @param startDate
     * @param endDate
     * @param storageRackId 仓库
     * @param page
     * @param size
     * @return
     */
    @GetMapping(URL + "/findPageList")
    GenericResponse<PagerDTO<GoodsStocktakingBO>> findPageList(
            @RequestParam(name = "placeId") String placeId,
            @RequestParam(value = "startDate", defaultValue = "") String startDate,
            @RequestParam(name = "endDate", defaultValue = "") String endDate,
            @RequestParam(name = "storageRackId", defaultValue = "") String storageRackId,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size);


    /**
     * 查询商品盘点时的损溢总数
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/staticLossOrOverFlowTotal")
    GenericResponse<ObjDTO<MiniAppGoodsStocktakingBO>> staticLossOrOverFlowTotal(
            @RequestParam(name = "placeId") String placeId,
            @RequestParam(value = "startDate", defaultValue = "") String startDate,
            @RequestParam(name = "endDate", defaultValue = "") String endDate,
            @RequestParam(name = "storageRackId", defaultValue = "") String storageRackId);


}
