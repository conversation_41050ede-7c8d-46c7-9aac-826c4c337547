package com.rzx.dim4.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportMemberVO {

    @ExcelProperty(value = "卡号", index = 0)
    private String loginName;

    @ExcelProperty(value = "姓名", index = 1)
    private String idName;

    @ExcelProperty(value = "会员类型", index = 2)
    private String cardTypeName;

    @ExcelProperty(value = "证件号", index = 3)
    private String idNumber;

    @ExcelProperty(value = "民族", index = 4)
    private String nation;

    @ExcelProperty(value = "住址", index = 5)
    private String address;

    @ExcelProperty(value = "电话", index = 6)
    private String mobile;

    @ExcelProperty(value = "余额(元)", index = 7)
    private String cashAccount;

    @ExcelProperty(value = "奖励(元)", index = 8)
    private String presentAccount;

    @ExcelProperty(value = "卡类型Id", index = 9)
    private String cardTypeId;

    @ExcelProperty(value = "积分", index = 10)
    private int points;

}
