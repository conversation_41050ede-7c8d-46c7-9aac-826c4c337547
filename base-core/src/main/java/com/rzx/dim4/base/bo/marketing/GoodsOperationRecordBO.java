package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年01月06日 18:09
 */
@Getter
@Setter
public class GoodsOperationRecordBO extends AbstractEntityBO {

    private Long id;
    private Long creater;
    private LocalDateTime created; // 创建时间
    private LocalDateTime updated; // 更新时间
    private int deleted;
    private String placeId; // 场所ID
    private String goodsId; // 商品信息ID，场所唯一，从120000开始递增
    private String goodsName; // 商品名称
    private int operationType; // 0上架，1下架
    private String createrName; // 操作人名称
}
