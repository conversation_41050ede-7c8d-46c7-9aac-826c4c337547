package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.marketing.DiscountCouponBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.DiscountCouponApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年08月12日 15:24
 */
@Slf4j
@Component
public class DiscountCouponApiHystrix implements DiscountCouponApi {

    @Override
    public GenericResponse<ObjDTO<DiscountCouponBO>> createOrUpdate( String requestTicket,DiscountCouponBO discountCouponBO) {
        log.error("接口异常，createOrUpdate(discountCouponBO={})", discountCouponBO.toString());
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<DiscountCouponBO>> queryByPlaceIdAndCouponId(String placeId, String couponId) {
        log.error("接口异常，queryByPlaceIdAndCouponId(placeId={}, couponId={})", placeId, couponId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> batchDelete( String requestTicket,String placeId, List<Long> ids) {
        log.error("接口异常，batchDelete(placeId={}, ids={})", placeId, ids);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<DiscountCouponBO>> findPage(String placeId, String couponTypeId, String couponName, int size, int page) {
        log.error("接口异常，findPage(placeId={}, couponTypeId={}, couponName={})", placeId, couponTypeId,couponName);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> updateStatus(String requestTicket, String placeId, String couponId, int status) {
        log.error("接口异常，updateStatus(placeId={},  couponId={}, status={})", placeId, couponId,status);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> giveCoupon(String requestTicket, String placeId, String idNumber, String couponId, String remark,SourceType sourceType, int quantity,PlaceAccountBO placeAccountBO) {
        log.error("接口异常，giveCoupon(placeId={},  idNumber={}, couponId={}, remark={}, sourceType={}, quantity={}, placeAccountBO={})", placeId, idNumber,couponId,remark,sourceType,quantity,placeAccountBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

}
