package com.rzx.dim4.base.vo.marketing;

import com.rzx.dim4.base.bo.AbstractBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class StatisticsGoodsReceiptListVO extends AbstractBO implements Serializable {
    private static final long serialVersionUID = -7705707221002121628L;

    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 上次采购价
     */
    private int prePrice;

    /**
     * 本次采购价
     */
    private int price;

    /**
     *  均价
     */
    private float avgPrice;

    /**
     * 当前采购价与均价的差值
     */
    private float diffPrice;

}
