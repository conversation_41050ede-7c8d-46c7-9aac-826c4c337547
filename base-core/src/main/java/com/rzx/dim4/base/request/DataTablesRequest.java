package com.rzx.dim4.base.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * datatables 请求查询对象
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DataTablesRequest implements Serializable {

	/**
	 * 递增查询值，每次查询需单调递增 1
	 */
	private int draw;
	/**
	 * 分页起始页，0 为第一页
	 */
	private int start;
	/**
	 * 分页尺寸
	 */
	private int length;
	/**
	 * 通用搜索框值
	 */
	private DataTablesSearch search;
	/**
	 * 列对象，筛选时使用
	 */
	private List<DataTablesColumn> columns = Collections.emptyList();
	/**
	 * 列排序对象
	 */
	private List<DataTablesOrder> order = Collections.emptyList();

	public DataTablesRequest(boolean init) {
		if (init) {
			this.draw = 1;
			this.start = 0;
			this.length = 10;
			this.search = new DataTablesSearch();
			this.columns = new ArrayList<>();
			this.order = new ArrayList<>();
		}
	}

	public DataTablesRequest(int draw, int start, int length, DataTablesSearch search) {
		this.draw = draw;
		this.start = start;
		this.length = length;
		this.search = search;
	}

	/**
	 * 根据字段名称获取筛选字段筛选值
	 *
	 * @param field 字段名称
	 * @return 筛选值
	 */
	public String getTheValueFromDtRequest(String field) {
		String valueOfGetField = "";
		for (DataTablesColumn column : columns) {
			if (field.equals(column.getData())) {
				valueOfGetField = column.getSearch().getValue();
			}
		}
		return valueOfGetField;
	}

	/**
	 * 根据字段名称设置筛选字段筛选值
	 *
	 * @param field 字段名称
	 * @param value 筛选值
	 * @return
	 */
	public boolean setTheValueOfDtRequest(String field, String value) {
		boolean result = false;
		for (DataTablesColumn column : columns) {
			if (field.equals(column.getData())) {
				column.getSearch().setValue(value);
				result = true;
				break;
			}
		}
		return result;
	}

	/**
	 * 根据字段名称设置筛选字段筛选值
	 *
	 * @param field
	 * @param value
	 * @return
	 */
	public boolean addTheValueOfDtRequest(String field, String value) {
		boolean result = false;
		for (DataTablesColumn column : columns) {
			if (field.equals(column.getData())) {
				column.getSearch().setValue(value);
				result = true;
				break;
			}
		}

		if (!result) {
			DataTablesColumn dataTablesColumn = new DataTablesColumn();
			dataTablesColumn.setData(field);
			dataTablesColumn.setSearch(new DataTablesSearch(field, false));
			columns.add(dataTablesColumn);
		}
		return true;
	}

	/**
	 * 设置通用查询字段值
	 * @param value
	 */
	public void setTheSearch(String value) {
		DataTablesSearch dataTablesSearch = new DataTablesSearch(value, false);
		this.setSearch(dataTablesSearch);
	}

	/**
	 * 获取通用查询字段值
	 * @return
	 */
	public String getTheSearch() {
		return this.getSearch().getValue();
	}
}
