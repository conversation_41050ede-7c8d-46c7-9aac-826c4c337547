package com.rzx.dim4.base.dto;

import java.io.Serializable;

import com.rzx.dim4.base.bo.AbstractBO;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 只有一个Obj类型的BO
 * 
 * <AUTHOR>
 * @date 2019年9月24日下午5:35:11
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ObjDTO<T extends AbstractBO> extends AbstractDTO implements Serializable {

	private static final long serialVersionUID = 1835189388357318231L;
	T obj;

}
