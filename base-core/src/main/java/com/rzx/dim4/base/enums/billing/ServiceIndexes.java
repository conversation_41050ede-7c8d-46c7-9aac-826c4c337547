package com.rzx.dim4.base.enums.billing;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务索引，用3位16进制表示索引序号，其中 1. 000和FFF不使用，001用作时间校对 2. 002-0FF(2-255)，预留不使用 3.
 * 100-1FF(256-511), 客户端接口，共255个接口 4. 200-2FF(512-767), 收银台接口，共255个接口
 * <p>
 * 包位置规范：
 * 1. 客户端接口的实现类位于包 com.rzx.dim4.billing.service.impl.client 中
 * 2.收银台接口的实现类位于包 com.rzx.dim4.billing.service.impl.cashier 中
 * <p>
 * 一般实现类命名规范：
 * 1. 客户端接口的实现类使用 Client + 业务关键字 + ServiceImpl，例如: 客户端登录接口: ClientLoginServiceImpl
 * 2. 收银台接口的实现类使用 Cashier + 业务关键字 + ServiceImpl，例如: 收银台登录接口: CashierLoginServiceImpl
 * <p>
 * 一般查询类命名规范：
 * 1. 客户端接口的实现类使用 Client + Query + 业务关键字 + ServiceImpl，例如:
 * 客户端查询计费卡信息接口: ClientQueryBillingCardServiceImpl
 * 2. 收银台接口的实现类使用 Cashier + Query + 业务关键字 + ServiceImpl，例如: 收银台查询注册卡数量接口:
 * CashierQueryRegcardQuantityServiceImpl
 * <p>
 * ServiceIndexes命名规范 1.
 * 与对应的实现类一一对应，索引命名时去掉类名称中的ServiceImpl，例如：ClientQueryBillingCardServiceImpl的索引名称为ClientQueryBillingCard
 *
 * <AUTHOR>
 * @Date Jun 8, 2020 10:27:44 AM
 */
public enum ServiceIndexes {

    // 001-0FF =============================================================
    ProofTime(0, "服务器时间校对"), // 0x000 时间校对

    // 100-1FF =============================================================
    ClientChangePassword(256, "客户端修改密码"), // 0x100
    ClientQueryCommonBillingRule(257, "客户端查询普通计费规则"), // 0x101
    //	ClientGenerateQRCodeNew(258, "客户端生成二维码"), // 0x102
    ClientLogin(259, "客户端登录"), // 0x103
    ClientLogout(260, "客户端登出"), // 0x104

    ClientPackageTime(261, "客户端包时"), // 0x105
    ClientQueryBillingCard(262, "客户端查询计费卡"), // 0x106
    ClientQueryLogLogin(263, "客户端查询登录记录"), // 0x107
    ClientQueryLogOperation(264, "客户端查询操作记录"), // 0x108
    ClientQueryPlaceConfig(265, "客户端查询网吧配置"), // 0x109

    ClientQueryTopupList(266, "客户端查询充值记录"), // 0x10A
    ClientQueryTopupResult(267, "客户端查询充值结果"), // 0x10B
    ClientQueryTopupRule(268, "客户端查询充值规则"), // 0x10C
    ClientQueryVersion(269, "客户端查询版本信息"), // 0x10D
    ClientRegistered(270, "客户端注册"), // 0x10E

    ClientTopupBillingCard(271, "客户端充值"), // 0x10F
    ClientVerifyIdentifier(272, "客户端认证"), // 0x110
    ClientBilling(273, "客户端计费"), // 0x111
    ClientHeartBeat(274, "客户端心跳"), // 0x112
    ClientQueryBillingOnline(275, "客户端查询在线信息"), /// 0x113

    ClientGenerateQRCode(276, "客户端生成二维码"), // 0x114
    ClientQueryBillingRulesNew(277, "客户端查询计费规则列表"), // 0x115
    ClientQueryBillingRule(278, "客户端查询计费规则"), // 0x116
    ClientReportStatus(279, "客户端上报状态"), // 0x117
    ClientQueryPackageTime(280, "客户端查询包时信息"), // 0x118

    ClientQueryWeekCommonBillingRule(281, "客户端查询7*24小时费率信息"), // 0x119
    ClientQueryLogAcc(282, "客户端查询累计包时记录"), // 0x11A
    ClientSigned(283, "客户端签到"), // 0x11B
    ClientQueryLogSigned(284, "客户端查询签到记录"), // 0x11C
    ClientQueryExchangePointsRule(285, "客户端查询积分兑换规则"), // 0x11D
    ClientExchangePoints(286, "客户端积分兑换"), // 0x11E
    ClientQueryPageLogExchangePoints(287, "客户端查询积分变更记录"), // 0x11F
    ClientPushAntiAddictionInfo(288, "防沉迷信息提交"), //0x120
    ClientQueryBillingRuleExtremum(289, "客户端查询计费规则的最大和最小值"), // 0x121
    ClientGenerateQRCodeLocalServiceImpl(290, "客户端生成二维码(本地)"), // 0x122
    ClientQueryTopupRuleNew(291, "客户端查询充值规则(新)"), // 0x123
    ClientTopupBillingCardNew(292, "客户端充值(新)"), // 0x124

    // 0x123 0x124 被占用
    ClientOnlinePackageServiceImpl(293, "客户端在线包时"), // 0x125
    ClientQueryPackageTimeNewService(294, "客户端查询包时-新"), // 0x126
    ClientQueryBookSeatsServiceImpl(295, "客户端查询是否订座中"), // 0x127
    ClientQueryWeekCommonBillingRuleByArea(296, "客户端通过区域查询7*24小时费率信息"), // 0x128
    ClientUnLockBookSeat(297, "客户端通过解锁码结束订座"), //0x129
    ClientQueryEquipment(298, "获取客户端设备信息"),  //0x12A

    ClientQueryLoginInfoService(299, "获取客户端在线信息和生成token"),  //0x12B
    ClientSubmitCall(300, "客户端呼叫服务类"), //0x12C

    ClientQueryBindMpQrcode(301, "收银台查询绑定公众号二维码"),//0x12d

    ClientCheckLogin(302, "客户端检验登入"),//0x12e
    ClientCancelForceExchange(303, "客户端取消包时，强制换机"),//0x12f

    ClientPackageExchange(304, "客户端包时跨区换机"),//0x130

    ClientQueryAuthImage(305, "客户端获取认证图片"),// 0x131


    // 200-2FF =============================================================
    CashierActivation(512, "收银台激活计费卡"), // 0x200，收银台激活计费卡
    CashierCancelPackageTime(513, "收银台取消包时"), // 0x201

    // @Deprecated // 已经被252 253 256接口替换
    // CashierCreateBillingCard(514, "收银台创建计费卡"), // 0x202，0元开卡，带钱开卡为 （589）0x24E
    CashierQueryNonIdNumber(514, "收银台查询非身份证卡数量"),  // 0x202

    CashierLogout(515, "收银台结账"), // 0x203
    CashierPackageTime(516, "收银台执行包时"), // 0x204
    CashierRegcardRegisteredByPhysical(517, "收银台实体注册卡绑定"), // 0x205
    CashierQueryBillingCard(518, "收银台查询计费卡信息"), // 0x206
    CashierQueryBillingCardType(519, "收银台查询计费卡类型"), // 0x207
    CashierQueryBillingOnline(520, "收银台查询在线信息"), // 0x208

    CashierQueryLogOperation(521, "收银台查询操作日志"), // 0x209
    CashierPurchaseRegcard(522, "收银台返回注册卡中心地址"), // 0x20A 收银台获取注册卡注册url
    CashierReportVersion(523, "收银台上报版本信息"), // 0x20B，收银台注册接口
    CashierQueryPackageRule(524, "收银台查询包时规则"), // 0x20C
    CashierQueryRegcardQuantity(525, "收银台查询注册卡数量"), // 0x20D

    CashierRegcardRegistered(526, "收银台注册卡绑定"), // 0x20E
    CashierResetPassword(527, "修改密码"), // 0x20F
    CashierReversalBillingCard(528, "收银台减钱"), // 0x210
    CashierTopupBillingCard(529, "收银台充值"), // 0x211
    CashierReportClientLostContact(530, "收银台上报失联客户端"), // 0x212

    CashierQueryPlaceConfig(531, "收银台查询场所配置"), // 0x213
    CashierQueryWhetherPackage(532, "收银台查询是否包时"), // 0x214
    CashierVerifyIdentifier(533, "收银台验证"), // 0x215
    CashierQueryTopupRule(534, "收银台查询充值规则"), // 0x216
    CashierQueryActiveBillingCard(535, "收银台查询激活卡信息"), // 0x217

    CashierQueryBillingRule(536, "收银台查询计费规则"), // 0x218 收银台查询计费规则
    CashierQueryRegcardStatus(537, "收银台查询注册卡状态"), // 0x219 查询注册卡注册状态
    CashierCancellationBillingCard(538, "收银台注销计费卡"), // 0x21A //
    CashierQueryAllClients(539, "收银台查询全部客户端"), // 0x21B 查询所有客户端
    CashierRegcardOnUnbind(540, "收银台注册卡解绑"), // 0x21C 收银台注册卡解绑

    CashierRegcardQueryPassword(541, "收银台查询注册卡密码"), // 0x21D 收银台注册卡查询密码
    CashierQueryTask(542, "收银台查询任务"), // 0x21E 收银台查询收银台任务
    CashierReportTask(543, "收银台上报任务状态"), // 0x21F 收银台上报任务状态
    CashierQueryPackageTimeReserve(544, "收银台查询预包时信息"), // 0x220 收银台查询预包时
    CashierExchange(545, "收银台换机"), // 0x221 收银台换机

    CashierModifyBillingCardType(546, "收银台修改计费卡类型"), // 0x222
    CashierQueryAuthority(547, "收银台查询权限"), // 0x223 收银台查询权限
    CashierTopupBillingCardCustomize(548, "允许修改奖励"), // 0x224, 收银台自定义充值
    CashierQueryWeekCommonBillingRule(549, "收银台查询场所下所有的7*24小时费率信息"), // 0x225
    CashierQueryWallpaperDeliver(550, "收银台查询锁屏图片"), // 0x226, 收银台查询锁屏图片

    CashierQueryCashierAccounts(551, "收银台查询收银台账号列表"), // 0x227
    CashierLogin(552, "收银台登入"), // 0x228
    CashierQueryShiftStatistics(553, "查询收银台交接班列表数据"), // 0x229
    CashierShiftSubmit(554, "收银台确认交接班"), // 0x22A
    CashierQueryLogLogin(555, "收银台查询登入日志信息"), // 0x22B

    CashierCustomPackage(556, "收银台自定义包时"), // 0x22C
    CashierTopupBillingCardPresent(557, "赠送充值"), // 22D
    CashierTopupBillingCardOldWithNew(558, "以老带新赠送"), // 22E
    CashierQueryVersionForDownload(559, "收银台查询客户端最新下载地址信息"), // 0x22F
    CashierReversalBillingCardNew(560, "收银台冲正"), // 0x230, 替换0x210

    CashierQueryLogTopup(561, "收银台查询充值记录"), // 0x231,
    CashierQueryLogReversal(562, "收银台查询退款记录"), // 0x232
    CashierQueryTopupConsumption(563, "收银台查询充值消费统计"), // 0x233
    CashierDeleteBillingCard(564, "收银台删除会员"), // 0x234

    CashierModifyProfileConfig(565, "收银台修改配置信息"), // 0x235
    CashierModifyBillingCard(566, "收银台修改用户资料"), // 0x236
    CashierCancelActivation(568, "收银台取消激活"), // 0x238
    CashierGenerateRealnameQRCode(567, "收银台实名二维码"), // 0x237
    // 238已被使用
    CashierQueryLogAcc(569, "收银台查询累计包时记录"), // 0x239
    CashierBindIdcard(570, "收银台绑定计费卡"), // 0x23A

    CashierQueryCashier(571, "收银台获取场所收银台信息"), // 0x23B
    CashierQueryPageShifts(572, "收银台分页查询交接班记录"), // 0x23C
    CashierQueryLogRefund(573, "收银台分页查询退款订单记录"), // 0x23D
    CashierQueryThirdLogOperation(574, "收银台分页查询第三方操作记录"), // 0x23E
    // 573 574 被占用
    CashierQueryExchangePointsRule(575, "收银台查询积分兑换规则"), // 0x23F
    CashierExchangePints(576, "收银台积分兑换"), // 0x240
    CashierQueryPageLogExchangePoints(577, "收银台分页查询积分变更记录"), // 0x241
    CashierQueryLogOtherIncome(578, "收银台分页查询其他收入记录信息"), // 0x242
    CashierOtherIncome(579, "收银台其他收入"), // 0x243
    CashierDeleteOtherIncome(580, "收银台删除其他收入记录信息"), // 0x244
    CashierCheckoutLogout(581, "全场结账"),  //0x245
    CashierQueryPageShiftLogOperation(582, "收银台分页查询交班页面操作记录"),  //0x246

    CashierQueryPageMember(583, "收银台分页查询场所会员信息(web页面会员列表/临时卡列表)"),  //0x247
    CashierQueryMemberStatics(584, "收银台查询卡数量、现金总额、赠送总额(web页面)"),  //0x248
    CashierQueryPageLogLogin(585, "收银台查询上下机记录(web页面)"),  //0x249
    CashierQueryPlaceChainAccount(586, "收银台查询连锁账户"), // 0x24A
    CashierQueryPageRegLog(587, "收银台查询注册卡日志(web页面)"), // 0X24B
    CashierGenerateRealnameQRCodeLocal(588, "收银台本地实名二维码"), // 0x24C
    CashierQueryVersion(589, "收银台查询客户端版本信息"), // 0x24D

    //@Deprecated // 已经被252 253 256接口替换
    //CashierCreateBillingCardWithAmount(590, "收银台带钱开卡"), // 0x24E
    CashierTopupBillingCardNoPresent(591, "收银台充值不赠送"), // 0x24F
    CashierTransferTempWanxiangUser(592, "合并万现用户"), // 0x250
    CashierQueryTempWanxiangUser(593, "查询万象用户"), // 0x251
    CashierCreateBillingCardNoPresent(594, "收银台开卡不带赠送"), // 0x252
    CashierCreateBillingCardWithCustomizeAmount(595, "收银台开卡自定义充值"), // 0x253
    CashierTopupBillingCardNew(596, "收银台充值(新)"), // 0x254
    CashierQueryTopupRuleNew(597, "收银台查询充值规则(新)"), // 0x255
    CashierCreateBillingCardWithAmountNew(598, "收银台带钱开卡(新)"), // 0x256
    CashierReversalBillingCardNewTopupRule(599, "收银台冲正(新)"), // 0x257

    CashierPackageTimeNew(600, "收银台新包时"), // 0x258
    CashierContinuePackageTime(601, "收银台续包时"), // 0x259
    CashierFuturePackageTime(602, "收银台预包时"), // 0x25A
    CashierConvertPackageTime(603, "收银台转包时"), // 0x25B

    CashierCancelPackageTimeNew(604, "收银台取消包时(新)"), // 0x25C
    CashierQueryLoginPass(605, "收银台查询会员卡密码"), // 0x25D
    CashierCreateBillingCardWithPackageTime(606, "收银台临时卡开卡包时"), // 0x25E
    CashierQueryRegcardUsed(607, "收银台查询注册卡已使用数据"), // 0x25F
    CashierQueryRegcardUnused(608, "收银台查询注册卡未使用数据"), // 0x260
    CashierQueryRegcardUsedStatistics(609, "收银台查询注册卡已使用统计数据"), // 0x261

    CashierRealnameSurcharge(610, "收银台实名扣点"), // 0x262

    CashierQueryBookSeatsPageListServiceImpl(611, "收银台查询订座记录分页列表"), // 0x263
    CashierCancelBookSeatsServiceImpl(612, "收银台取消预定座位"), // 0x264

    CashIerCreateTopupQrCode(613, "收银台创建充值二维码"), // 0x265

    CashierCreateCardQrCode(614, "收银台创建开卡二维码"), // 0x266

    CashierCancellationBillingCardNew(615, "收银台注销计费卡-新"), // 0x267

    CashierTransferTempDuDuNiuUser(616, "合并嘟嘟牛用户"), // 0x268

    CashierQueryTempDuDuNiuUser(617, "查询嘟嘟牛用户"), // 0x269

    CashierReportPlaceStatus(618, "收银台上报场所状态"), // 0x26A

    CashierQueryAuthImage(619, "收银台查询认证图片"), // 0x26B

    CashierRegcardSendVerificationCode(620, "收银台获取注册中心短信验证码"), // 0x26C

    CashierRegcardForceBind(621, "收银台强制绑定注册卡"), // 0x26D

    CashierRegcardForceBindByByPhysical(622, "收银台强制绑定注册卡（实体卡）"), // 0x26E
    CashierReportMigrateServiceImpl(623, "收银台上报网吧迁移中心成功"), // 0x26F
    CashierQueryMessageNotifyService(624, "收银台查询消息通知"), // 0x270
    CashierQueryBillingOnlineByClient(625, "收银台查询计费卡本次消费详情"), // 0x271
    CashierReportRemind(626, "收银台上报提醒服务"), // 0x272

    CashierActiveOrCreateBillingCardService(627, "收银台自动开卡或激活"), // 0x273
    CashierQueryRightWallpaperDeliverServiceImpl(628, "收银台查询右侧素材栏"), // 0x274
    CashierHandleLeaveWord(629, "收银台处理留言"),        // 0x275                                                                    //0x275
    CashierQueryGoodsPicturesService(630, "收银台查询场所下所有的商品图片"), // 0x276

    // 900-9FF =============================================================
    ThirdNonBillingClientLogin(2304, "第三方非计费登录接口"), // 0x900
    ThirdCreateBillingCardSimple(2305, "第三方简易开卡接口"), // 0x901

    // 900开头代表权限控制自定义的值 =============================================================
//	CustomPermissionsOfFullCheckout(901, "全场结账"),
    CustomPermissionsOfQueryTotalBalance(902, "查询会员余额总量"),
    CashierSimplifiedShift(903, "简版交班"),
    CashierPermissionsCreateBillingCardWithAmountNew(904, "收银台自助开卡"),

    ShopGoodsUpDown(905, "商品上下架"),
    ;

    // ===========================================================
    private final int value;
    private final String display;

    private ServiceIndexes(int value, String display) {
        this.value = value;
        this.display = display;
    }

    public int getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    /**
     * 需要权限控制的列表
     *
     * @return
     */
    public static List<ServiceIndexes> getNeedAuthorityIndexes() {
        List<ServiceIndexes> needAuthorities = new ArrayList<ServiceIndexes>() {
            private static final long serialVersionUID = 1L;

            {
                add(ServiceIndexes.CashierPurchaseRegcard); // 购买注册卡,522
                add(ServiceIndexes.CashierResetPassword); // 充值密码,527
                add(ServiceIndexes.CashierReversalBillingCard); // 减钱,528
//				add(ServiceIndexes.CashierRegcardQueryPassword); // 收银台注册卡查询密码,541    2023.8.31该条权限不再使用
                add(ServiceIndexes.CashierModifyBillingCardType); // 修改计费卡类型,546
                add(ServiceIndexes.CashierTopupBillingCardCustomize); // 自定义充值,548

                add(ServiceIndexes.CashierPackageTimeNew); // 包时(新),600
                add(ServiceIndexes.CashierContinuePackageTime); // 继续包时,601
                add(ServiceIndexes.CashierFuturePackageTime); // 提前包时,602
                add(ServiceIndexes.CashierConvertPackageTime); // 转包时,603
                add(ServiceIndexes.CashierCancelPackageTimeNew); // 取消包时(新),604

                add(ServiceIndexes.CashierCustomPackage); // 自定义包时，556

                add(ServiceIndexes.CashierTopupBillingCardPresent); // 赠送充值,557
                add(ServiceIndexes.CashierTopupBillingCardOldWithNew); // 以老带新充值,558
                add(ServiceIndexes.CashierReversalBillingCardNew); // 冲正,560
                add(ServiceIndexes.CashierDeleteBillingCard); // 删除会员，564
                add(ServiceIndexes.CashierCheckoutLogout);//全场结账，581
                add(ServiceIndexes.CustomPermissionsOfQueryTotalBalance);//查询会员余额总量，902
                add(ServiceIndexes.CashierSimplifiedShift);//简版交班，903
                add(ServiceIndexes.CashierPermissionsCreateBillingCardWithAmountNew);//收银台自助开卡接口，904
                add(ServiceIndexes.ShopGoodsUpDown);//商品上下架，905

            }
        };
        return needAuthorities;
    }

    /**
     * 根据索引值获取实例对象
     *
     * @param index
     * @return
     */
    public static ServiceIndexes getServiceIndexes(int index) {
        for (ServiceIndexes serviceIndex : ServiceIndexes.values()) {
            if (serviceIndex.getValue() == index) {
                return serviceIndex;
            }
        }
        return null;
    }

}
