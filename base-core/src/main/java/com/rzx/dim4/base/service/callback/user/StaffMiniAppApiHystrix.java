package com.rzx.dim4.base.service.callback.user;

import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.user.StaffMiniAppApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年04月24日 18:38
 */
@Slf4j
@Service
public class StaffMiniAppApiHystrix implements StaffMiniAppApi {


    @Override
    public GenericResponse<SimpleDTO> doGetUrlLink(String query, String path) {
        log.error("接口异常:::deleteExpireAuthImagesInfo，query:{}，path:{}", query,path);
        throw new ServiceException(ServiceCodes.NO_SERVICE);
    }
}
