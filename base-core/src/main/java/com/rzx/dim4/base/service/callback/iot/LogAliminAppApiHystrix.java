package com.rzx.dim4.base.service.callback.iot;

import com.rzx.dim4.base.bo.iot.LogAliminAppBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.iot.LogAliminAppApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023年12月15日 18:31
 */
@Slf4j
@Service
public class LogAliminAppApiHystrix implements LogAliminAppApi {
    @Override
    public GenericResponse<ListDTO<LogAliminAppBO>> queryStatisticsAliminApp(String queryType,  String startTime,
                                                                             String endTime, String placeType, String provinceCode, String cityCode) {
        log.error("接口异常，queryAuthConfig(queryType={},  startTime={}, endTime={}, placeType={}, provinceCode={}, cityCode={} )",
                queryType,startTime, endTime, placeType, provinceCode,cityCode);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
