package com.rzx.dim4.base.enums.place;

import com.rzx.dim4.base.enums.Convertor.AbstractEnumConverter;
import com.rzx.dim4.base.enums.Convertor.PersistEnum2DB;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/9/14
 **/
@Getter
@AllArgsConstructor
public enum MenuTypeEnum implements PersistEnum2DB<Integer> {

    DIR(1), // 目录
    MENU(2), // 菜单
    BUTTON(3), // 按钮
    INTERFACE(4); // 接口

    private final Integer type;

    @Override
    public Integer getData() {
        return type;
    }

    public static class Converter extends AbstractEnumConverter<MenuTypeEnum, Integer> {
        public Converter() {
            super(MenuTypeEnum.class);
        }
    }
}
