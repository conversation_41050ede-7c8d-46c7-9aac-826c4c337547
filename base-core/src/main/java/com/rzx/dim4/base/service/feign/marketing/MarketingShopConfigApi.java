package com.rzx.dim4.base.service.feign.marketing;

import com.rzx.dim4.base.bo.marketing.ShopConfigBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.cons.FeginConstant;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.callback.marketing.MarketingShopConfigApiHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

/**
 * 会员关怀相关接口
 *
 * <AUTHOR>
 * @date 2025年02月11日 14:26
 */
@Primary
@FeignClient(value = FeginConstant.MARKETING_SERVER, contextId = "ShopConfigApi", fallback = MarketingShopConfigApiHystrix.class)
public interface MarketingShopConfigApi {

    final String URL = "/feign/marketing/shopConfig";

    /**
     * 小程序暂停接单
     *
     * @param requestTicket
     * @param placeId
     * @param orderSwitch   暂停客户接单：默认0接单，1暂停接单
     * @return
     */
    @PostMapping(URL + "/updateOrderSwitch")
    public GenericResponse<ObjDTO<ShopConfigBO>> updateOrderSwitch(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                   @RequestParam String placeId,
                                                                   @RequestParam int orderSwitch);

    /**
     * 小程序设置库存基础设置
     *
     * @param requestTicket
     * @param placeId
     * @param whetherInventoryManage
     * @param distinguishShelfInventory
     * @param stockOutShowSwitch
     * @return
     */
    @PostMapping(URL + "/saveInventoryConfig")
    GenericResponse<ObjDTO<ShopConfigBO>> saveInventoryConfig(@RequestHeader(value = "request_ticket") String requestTicket,
                                                              @RequestBody PlaceAccountBO miniAppLoginAccount,
                                                              @RequestParam String placeId,
                                                              @RequestParam int whetherInventoryManage,
                                                              @RequestParam int distinguishShelfInventory,
                                                              @RequestParam int stockOutShowSwitch);

    /**
     * 小程序查询商超配置
     *
     * @param placeId
     * @return
     */
    @GetMapping(URL + "/findPlaceShopConfig")
    GenericResponse<ObjDTO<ShopConfigBO>> findPlaceShopConfig(@RequestParam String placeId);


    /**
     * 小程序设置商超基础配置
     *
     * @param requestTicket
     * @param placeId
     * @param accountId
     * @param customizedPayLimit
     * @return
     */
    @PostMapping(URL + "/saveCustomPayLimit")
    GenericResponse<ObjDTO<ShopConfigBO>> saveCustomPayLimit(@RequestHeader(value = "request_ticket") String requestTicket,
                                                             @RequestParam String placeId,
                                                             @RequestParam String accountId,
                                                             @RequestParam int customizedPayLimit);

    /**
     * 小程序清空商超数据
     *
     * @param requestTicket
     * @param placeId
     * @return
     */
    @PostMapping(URL + "/clearSuperMarketData")
    GenericResponse clearSuperMarketData(@RequestHeader(value = "request_ticket") String requestTicket,
                                         @RequestParam String placeId,
                                         @RequestParam String accountId);

}
