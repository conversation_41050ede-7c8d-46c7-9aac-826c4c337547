package com.rzx.dim4.base.service.callback.marketing;

import com.rzx.dim4.base.bo.user.MiniApp.PlaceIncomeBO;
import com.rzx.dim4.base.bo.user.MiniApp.PlaceShiftIncomeBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.MarketingIncomeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> hwx
 * @since 2025/2/26 12:02
 */
@Component
@Slf4j
public class MarketingIncomeApiHystrix implements MarketingIncomeApi {
    @Override
    public GenericResponse<ObjDTO<PlaceShiftIncomeBO>> currShiftIncome(@RequestParam String placeId) {
        log.error("接口异常:::currShiftIncome(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceIncomeBO>> todayIncomeTotal(String placeId) {
        log.error("接口异常:::currShiftIncome(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceIncomeBO>> monthVerifyCoupon(String placeId) {
        log.error("接口异常:::todayIncomeTotal(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PlaceIncomeBO>> monthIncomeTotal(String placeId) {
        log.error("接口异常:::monthIncomeTotal(placeId:::{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
