package com.rzx.dim4.base.bo.marketing;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 班次提现表
 * <AUTHOR>
 * @date 2025年03月31日 12:00
 */
@Getter
@Setter
public class ShiftWithdrawalBO extends AbstractEntityBO {
    private Long id;
    private Long creater;
    private LocalDateTime created; // 账号创建时间
    private LocalDateTime updated; // 账号更新时间
    private int deleted;
    private String placeId; // 场所ID

    private String placeName; // 场所名称

    private int realIncome; // 实收入 = 网费收入 + 商品收入

    private int fee; // 手续费

    private int withdrawalAmount; // 提现金额(分账金额 = 实收入-手续费)

    private String shiftIds; // 班次列表

    private int status;// 提现状态，0已创建，1提现成功，2提现失败

    private String withdrawalId; // 提现唯一ID

    private String settleOrderNo; // 龙兜提现结算单号

    private String merUsername; // 收款账号，用于进行提现和提现结果查询

    protected LocalDateTime startTime; // 提现班次开始时间

    protected LocalDateTime endTime; // 提现班次结束时间

    private int internetIncome; // 网费实收入

    private int shopIncome; // 商超实收入

    private int rentIncome; // 设备租赁实收入





}
