package com.rzx.dim4.base.service.callback;

import com.google.gson.Gson;
import com.qiniu.util.Json;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.shop.*;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.goods.*;
import com.rzx.dim4.base.service.feign.goods.api.GoodsApi;
import com.rzx.dim4.base.service.feign.goods.api.GoodsTypeApi;
import com.rzx.dim4.base.service.feign.goods.api.OrdersApi;
import com.rzx.dim4.base.service.feign.goods.query.*;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.service.feign.ShopServerService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023年1月30日 上午11:53:48
 */
@Slf4j
@Service
public class ShopServerServiceHystrix implements ShopServerService , StorageGoodsClient, SuppliersClient, DailyGoodsClient,
        LogStorageInfoClient, OrdersClient,OrdersRefundClient, GoodsTypeApi, OrdersApi, GoodsApi {

    @Override
    public GenericResponse<ObjDTO<ShopConfigBO>> queryShopConfig(String placeId) {
        log.error("接口异常:::queryShopConfig(placeId:{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> saveShopConfig(String requestTicket, ShopConfigBO shopConfigBO) {
        log.error("接口异常:::saveShopConfig(requestTicket:{},shopConfigBO:{})",requestTicket, shopConfigBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsBO>> queryGoods(GoodsQuery goodsQuery, int size, int page) {
        log.error("接口异常:::queryGoods(queryMap:{},size:{},page:{})", Json.encode(goodsQuery),size,page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsBO>> saveGoods(String requestTicket, GoodsBO goodsBO) {
        log.error("接口异常:::saveGoods(requestTicket:{},goodsBO:{})", requestTicket,goodsBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteGoods(String requestTicket, String placeId, String goodsId) {
        log.error("接口异常:::deleteGoods(requestTicket:{},placeId:{},goodsId:{})", requestTicket,placeId,goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsBO>> queryGoodsByGoodsId(String placeId, String goodsId) {
        log.error("接口异常:::queryGoodsByGoodsId(placeId:{},goodsId:{})", placeId,goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> queryGoodsByPlaceIds(List<String> placeIds) {
        log.error("接口异常:::queryGoodsByPlaceIds(placeIds:{})", placeIds);
        return new HashMap<>();
    }

    @Override
    public GenericResponse<PagerDTO<GoodsTypeBO>> queryPageGoodsType(Map<String, String> queryMap, int size, int page) {
        log.error("接口异常:::queryPageGoodsType(queryMap:{},size:{},page:{})", queryMap,size,page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsTypeBO>> saveGoodsType(String requestTicket, GoodsTypeBO goodsTypeBO) {
        log.error("接口异常:::saveGoodsType(requestTicket:{},goodsTypeBO:{})", requestTicket,goodsTypeBO);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsTypeBO>> batchSaveGoodsType(String requestTicket, List<GoodsTypeBO> goodsTypeBOs) {
        log.error("接口异常:::batchSaveGoodsType(requestTicket:{},goodsTypeBOs:{})", requestTicket,goodsTypeBOs);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> deleteGoodsType(String requestTicket, String placeId, String goodsTypeId) {
        log.error("接口异常:::deleteGoodsType(requestTicket:{},placeId:{},goodsTypeId:{})", requestTicket,placeId,goodsTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsTypeBO>> queryAllTypeByPlaceId(String placeId) {
        log.error("接口异常:::queryAllTypeByPlaceId(placeId:{})", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<GoodsTypeBO>> queryGoodsTypeByGoodsTypeId(String placeId, String goodsTypeId) {
        log.error("接口异常:::queryGoodsTypeByGoodsTypeId(placeId:{},placeId:{})", placeId, goodsTypeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<OrdersBO>> queryOrders(Map<String, String> queryMap, int size, int page) {
        log.error("接口异常:::queryOrders(queryMap:{},size:{},page:{})", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> queryStatisticsToDay(LocalDateTime startDateTime, LocalDateTime endDateTime, List<String> placeIds) {
        log.error("接口异常:::queryStatisticsToDay(startDateTime:{},endDateTime:{},placeIds:{})", startDateTime, endDateTime, placeIds);
        return new HashMap<>();
    }

    @Override
    public void statisticsGoodsSaleByDayScheduler(int dayAgo) {
        log.error("接口异常:::statisticsGoodsSaleByDayScheduler(dayAgo:::{})",dayAgo);
    }

    @Override
    public GenericResponse<PagerDTO<OrderRefundBO>> queryOrdersRefunds(Map<String, String> queryMap, int size, int page) {
        log.error("接口异常:::queryOrdersRefunds(queryMap:{},size:{},page:{})", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> refund(String placeId, String ordersId, String refundType, String shiftId, String cashierId, String cashierName, String remark, String refundGoodsJson) {
        log.error("接口异常:::refund(placeId:{},ordersId:{},refundType:{},shiftId:{},cashierId:{},cashierName:{},remark:{},refundGoodsJson:{})", placeId, ordersId, refundType, shiftId, cashierId, cashierName, remark, refundGoodsJson);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<OrderRefundGoodsBO>> getOrderRefund(String placeId, String ordersId) {
        log.error("接口异常:::getOrderRefund(placeId:{},ordersId:{})", placeId, ordersId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogGoodsStocksBO>> queryGoodsStocks(Map<String, String> queryMap, int size, int page) {
        log.error("接口异常:::queryGoodsStocks(queryMap:{},size:{},page:{})", queryMap, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<SimpleDTO> updateGoodsStocks(String placeId, String stocksChangingType, String shiftId, String cashierId, String cashierName, String remark, String stocksGoodsJson) {
        log.error("接口异常:::updateGoodsStocks(placeId:{},stocksChangingType:{},shiftId:{},cashierId:{},cashierName:{},remark:{},stocksGoodsJson:{})", placeId, stocksChangingType, shiftId, cashierId, cashierName, remark, stocksGoodsJson);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatisticsGoodsSaleByDayBO>> goodsSale(int size, int page, String placeId, String rankType, String startTime, String endTime) {
        log.error("接口异常:::goodsSale(placeId:{},rankType:{},startTime:{},endTime:{},size:{},page:{})", placeId, rankType, startTime, endTime, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StatisticsGoodsSaleByDayBO>> goodsSaleByMonth(String placeId, String startTime, String endTime) {
        log.error("接口异常:::goodsSaleByMonth(placeId:{},startTime:{},endTime:{})", placeId, startTime, endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StatisticsGoodsSaleByDayBO>> sumGoodsSaleByDate(String placeId, String startTime, String endTime) {
        log.error("接口异常:::sumGoodsSaleByDate(placeId:{},startTime:{},endTime:{})", placeId, startTime, endTime);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<StatisticsGoodsSaleByDayBO>> goodsSaleByDay(int size, int page, String placeId, String startTime, String endTime) {
        log.error("接口异常:::goodsSaleByDay(placeId:{},startTime:{},endTime:{},size:{},page:{})", placeId, startTime, endTime, size, page);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public Map<String, Integer> queryGoodsStatistics(String placeId, String shiftId) {
        log.error("接口异常:::queryGoodsStatistics(placeId:{},shiftId:{})", placeId, shiftId);
        return null;
    }


    @Override
    public GenericResponse<PagerDTO<StorageGoodsBO>> listStoragePage(StorageGoodsQuery goodsQuery, Pageable pageable) {
        log.error("接口异常:::listStoragePage", Json.encode(goodsQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<StorageGoodsBO>> listStorage(StorageGoodsQuery goodsQuery) {
        log.error("接口异常:::billingCardDeduction",Json.encode(goodsQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveStorageGoods(String requestTicket, List<StorageGoodsQuery> storageGoodsQuery) {
        log.error("接口异常:::saveStorageGoods",Json.encode(storageGoodsQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<StorageGoodsBO>> getStorage(StorageGoodsQuery goodsQuery) {
        log.error("接口异常:::getStorage",Json.encode(goodsQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> checksGoodsNumber(String requestTicket, List<GoodsParam> goodsVos) {
        log.error("接口异常:::verification",Json.encode(goodsVos));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> upDown(String placeId, String goodsId, Integer hitShelves, Integer goodsStocks,String name,String remark) {
        log.error("接口异常:::upDown",Json.encode(goodsId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> refundGoods(String requestTicket, List<StorageGoodsVo> storageGoodsQuery) {
        log.error("接口异常:::upDown",Json.encode(storageGoodsQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> saveGoodsStorageInfo(String requestTicket, StorageGoodsBO goodsInfoBO) {
        log.error("接口异常:::saveGoodsStorageInfo",Json.encode(goodsInfoBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> miniDeleteGoods(String requestTicket, String placeId, String goodsId) {
        log.error("接口异常:::miniDeleteGoods,placeId:::{},goodsId:::{}",placeId,goodsId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> deleteGoodsStorage(String placeId, String goodsId) {
        log.error("接口异常:::deleteGoodsStorage",Json.encode(goodsId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<SuppliersBO>> listSuppliers(SuppliersQuery suppliersQuery) {
        log.error("接口异常:::listSuppliers",Json.encode(suppliersQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<SuppliersBO>> listPages(SuppliersQuery suppliersQuery, Pageable pageable) {
        log.error("接口异常:::listPages",Json.encode(suppliersQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<SuppliersBO>> listSupplierNames(String placeId) {
        log.error("接口异常:::listSupplierNames",Json.encode(placeId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<?> save(String requestTicket, SuppliersBO storageGoodsBO) {
        log.error("接口异常:::save",Json.encode(storageGoodsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public void delete(String supplierId,String placeId) {
        log.error("接口异常:::deleteById",Json.encode(supplierId));

    }

    @Override
    public GenericResponse<PagerDTO<DailyGoodsBO>> dailyGoodsPage(DailyGoodsBO dailyGoodsBO) {
        log.error("接口异常:::DailyGoodsPage",Json.encode(dailyGoodsBO));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }


    @Override
    public GenericResponse<PagerDTO<GoodsStocksDetailBO>> listPage(GoodsStocksDetailQuery logStockRefundInfoQuery, Pageable pageable) {
        log.error("接口异常:::listPage",Json.encode(logStockRefundInfoQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsStocksDetailBO>> listLogStorageInfo(GoodsStocksDetailQuery logStockRefundInfoQuery) {
        log.error("接口异常:::listLogStorageInfo",Json.encode(logStockRefundInfoQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<LogStorageStocksBO>> logChangRecordPage(GoodsStocksDetailQuery logStorageInfoQuery, Pageable pageable) {
        log.error("接口异常:::logChangRecordPage",Json.encode(logStorageInfoQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<LogStorageStocksBO>> logChangRecord(GoodsStocksDetailQuery logStorageInfoQuery) {
        log.error("接口异常:::logChangRecord",Json.encode(logStorageInfoQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsStocksDetailBO>> queryStatisticsGoodsByMonth(GoodsStocksDetailQuery logStorageInfoQuery) {
        log.error("接口异常:::queryStatisticsGoodsByMonth",Json.encode(logStorageInfoQuery));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<OrdersBO>> sumOrdersTotal(Map<String, String> queryMap) {
        log.error("接口异常:::sumOrdersTotal",Json.encode(queryMap));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO> queryOrdersSum(String placeId, String startTime, String endTime) {
        log.error("接口异常:::queryOrdersSum",Json.encode(placeId));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<OrderRefundBO>> queryOrdersRefunds(Map<String, String> queryMap) {
        log.error("接口异常:::queryOrdersRefunds:{}",Json.encode(queryMap));
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ListDTO<GoodsTypeBO>> getList(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId) {
        log.error("接口异常:::goodsType#getList,placeId:{}", placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<OrdersBO>> getOrder(@RequestHeader("request_ticket") String requestTicket,
                                                      @RequestParam String orderId,
                                                      @RequestParam String placeId){
        log.error("接口异常:::payment#getOrder,orderId:{},placeId:{},", orderId,placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<GoodsBO>> getPageList(@RequestHeader("request_ticket") String requestTicket,
                                                          @RequestParam int pageSize,
                                                          @RequestParam int pageStart,
                                                          @RequestParam(required = false,defaultValue = "") String goodsTypeId,
                                                          @RequestParam(required = false,defaultValue = "") String goodsName,
                                                          @RequestParam String placeId) {
        log.error("接口异常:::goods#getPageList,pageSize:{},pageStart:{},goodsTypeId:{},goodsName:{},placeId:{}", pageSize,pageStart,goodsTypeId,goodsName,placeId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createShopOrder(@RequestHeader("request_ticket") String requestTicket,
                                                                    @RequestBody List<CartGoodsBO> cartGoodsBos,
                                                                    @RequestParam String placeId,
                                                                    @RequestParam(required = false)String clientName,
                                                                    @RequestParam(required = false)String clientId,
                                                                    @RequestParam SourceType sourceType,
                                                                    @RequestParam PayType payType,
                                                                    @RequestParam String openId,
                                                                    @RequestParam String returnUrl,
                                                                    @RequestParam String idName,
                                                                    @RequestParam String idNumber,
                                                                    @RequestParam(required = false) String cardId, String remark) {
        log.error("接口异常:::ordersApi#createShopOrder,:cart{},placeId:{}.clientName:{},clientId:{},sourceType:{},payType:{},openId:{},returnUrl:{},idName:{},idNumber:{},cardId:{},remark:{}", new Gson().toJson(cartGoodsBos),placeId,clientName,clientId,sourceType,payType,openId,returnUrl,idName,idNumber,cardId,remark);
        return null;
    }


    @Override
    public GenericResponse<ObjDTO<PaymentOrderBO>> queryPayOrder(@RequestHeader(value = "request_ticket") String requestTicket, @RequestParam String orderId) {
        log.error("接口异常:::ordersApi#queryPayOrder,orderId:{}", orderId);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }

    @Override
    public GenericResponse<PagerDTO<OrdersBO>> queryOrderList(@RequestHeader("request_ticket") String requestTicket, @RequestParam String placeId, @RequestParam String idNumber, @RequestParam int page, @RequestParam int size) {
        log.error("接口异常:::ordersApi#queryOrderList,placeId:{},idNumber:{},page:{},size:{}", placeId,idNumber,page,size);
        return new GenericResponse<>(ServiceCodes.NO_SERVICE);
    }
}
