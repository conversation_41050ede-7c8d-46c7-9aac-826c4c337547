package com.rzx.dim4.base.utils;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.exception.ServiceException;
import org.springframework.util.StringUtils;


public class PayTypeUtil {

    public static PayType checkPayType(String payCode, PayType defaultPayType) {
        //付款码
        PayType payType = defaultPayType;
        try {
            if (payCode != null) {
                System.out.println("::::::::::::::::::::::订单请求PayCode:::::::::::::::::::::::::::::::::" + payCode);

                int payCodeNum = StringUtils.isEmpty(payCode) ? 0 : Integer.parseInt(payCode.substring(0, 2));
                if (payCodeNum >= 10 && payCodeNum <= 15) {
                    // 前缀以10、11、12、13、14、15 开头，则是微信付款码
                    payType = PayType.WECHAT_SCAN;
                } else if (payCodeNum >= 25 && payCodeNum <= 30) {
                    // 前缀以25、26、27、28、29、30 开头，则是支付宝付款码
                    payType = PayType.ALIPAY_SCAN;
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw new ServiceException(ServiceCodes.PAYMENT_BAD_PAYTYPE);
        }
        return payType;
    }
}
