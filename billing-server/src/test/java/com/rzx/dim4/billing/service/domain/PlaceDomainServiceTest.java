package com.rzx.dim4.billing.service.domain;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

@Disabled("TODO")
@Slf4j
class PlaceDomainServiceTest {

    @Test
    void doContext() {

        String idNumber = "362526199103041334";

        String yearBirth = idNumber.substring(6, 10);
        String monthBirth = idNumber.substring(10, 12);
        String dayBirth = idNumber.substring(12, 14);

        log.info("yearBirth={}, monthBirth={}, dayBirth={}", yearBirth, monthBirth, dayBirth);

        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        int month = localDate.getMonth().getValue();
        int dayOfMonth = localDate.getDayOfMonth();

        log.info("year={}, month={}, day={}", year, month, dayOfMonth);

        String aa = generateZipPassword("1","44000000000001");
        log.info("aaaaa=="+aa);
        log.info("aaaaa======>"+aa.toCharArray());

        int s = three();


    }
    public static String generateZipPassword(String type,String placeId){
        LocalDate now = LocalDate.now();

        StringBuffer sb = new StringBuffer(type);
        sb.append(String.format("%04X", now.getYear()));
        sb.append(placeId);
        sb.append(String.format("%02X", now.getDayOfMonth()-1));
        sb.append(String.format("%02X", now.getMonth().getValue()));

        return sb.toString();
    }

    public  static int three (){
        int  a =  "2".equals("3") ? 1 : "3".equals("3") ? 2 : 0 ;
        log.info("ssssssss=="+a);
        return a;
    }
}