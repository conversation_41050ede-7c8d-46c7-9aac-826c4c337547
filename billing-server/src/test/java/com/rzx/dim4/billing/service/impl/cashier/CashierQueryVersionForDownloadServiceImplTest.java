package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.ClientVersionBO;
import com.rzx.dim4.base.dto.AbstractDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.ClientUpgrade;
import com.rzx.dim4.billing.entity.ClientVersion;
import com.rzx.dim4.billing.service.ClientUpgradeService;
import com.rzx.dim4.billing.service.ClientVersionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
class CashierQueryVersionForDownloadServiceImplTest {

    /**
     * 用于模拟的对象
     */
    @Mock
    private ClientVersionService clientVersionService;

    /**
     * 用于模拟的对象
     */
    @Mock
    private ClientUpgradeService clientUpgradeService;

    /**
     * 被测试的类
     */
    @InjectMocks
    private CashierQueryVersionForDownloadServiceImpl cashierQueryVersionForDownloadService;


    private static Optional<ClientUpgrade> cashierUpgradeOpt;

    private static Optional<ClientVersion> cashierVersionOpt;

    private static String placeId;
    private static String clientType;

    private static final List<String> params = new ArrayList<>(2);

    @BeforeEach
    void setUp() {

        placeId = "placeId1";
        clientType = "1";

        params.add(placeId);
        params.add(clientType);

        String versionId = "versionId1";

        ClientUpgrade clientUpgrade = new ClientUpgrade();
        clientUpgrade.setPlaceId(placeId);
        clientUpgrade.setClientType(Integer.parseInt(clientType));
        clientUpgrade.setVersionId(versionId);
        cashierUpgradeOpt = Optional.of(clientUpgrade);

        ClientVersion clientVersion = new ClientVersion();
        clientVersion.setVersionId(versionId);
        clientVersion.setVersionNumber("1.1.1");
        cashierVersionOpt = Optional.of(clientVersion);
    }

    @DisplayName("测试 获取场所客户端最新版本下载信息")
    @Test
    void doService() {
        when(clientUpgradeService.findByPlaceIdAndClientType(placeId, clientType)).thenReturn(cashierUpgradeOpt);
        when(clientVersionService.findByVersionId(cashierUpgradeOpt.get().getVersionId())).thenReturn(cashierVersionOpt);

        GenericResponse<?> response = cashierQueryVersionForDownloadService.doService(params);
        assertEquals(ServiceCodes.NO_ERROR.getCode(), response.getCode());

        AbstractDTO data = response.getData();
        ObjDTO objDTO = (ObjDTO) data;
        ClientVersionBO clientVersionBO = (ClientVersionBO) objDTO.getObj();
        assertEquals(cashierVersionOpt.get().getVersionNumber(), clientVersionBO.getVersionNumber());
    }
}