package com.rzx.dim4.billing.web.controller;

import com.google.common.hash.Hashing;
import com.rzx.dim4.billing.service.impl.cashier.*;
import com.rzx.dim4.billing.service.impl.client.ClientLoginServiceImpl;
import com.rzx.dim4.billing.service.impl.client.ClientLogoutServiceImpl;
import com.rzx.dim4.billing.service.impl.client.ClientQueryBillingCardServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * setUp 组装获取入参的原始参数，
 * 然后调用 getSignStr() 方法，获取签名字符串，即客户端请求的真是参数
 */
@Slf4j
@Disabled("单元测试不执行")
class CybercafeCoreControllerTest {

    private String placeId;

    private String identifierKey;

    private int serviceIndex = 0;

    private String[] params;

    private static final String PLACE_ID = "**************";
    private static final String IDENTIFY_KEY = "92CB1FD954FADE27";

    private static final String ID_NUMBER = "362526199303041738";
    private static final String NAME = "彭智胜";
    private static final String SHIFT_ID = "5003";

    @BeforeEach
    void setUp() {
        // 入参构造

        // 公共参数
//        placeId = "0A7FDE84C29059B6";
//        identifierKey = "**************";
        placeId = PLACE_ID;
        identifierKey = IDENTIFY_KEY;
//        interface523(); // 收银台注册
//        cashierLoginServiceImpl(); // 收银员登录
//        interface590(); // 带钱开卡

//        clientLogin();
//        clientBillingServiceImpl();
        clientLogoutServiceImpl();
//        clientQueryBillingCardServiceImpl();

//        CashierLogoutServiceImpl();
//        cashierActivationServiceImpl();
//        cashierQueryBillingOnlineServiceImpl();
//        CashierQueryShiftStatisticsServiceImpl();
//        CashierQueryPageShiftLogOperationServiceImpl();
//        cashierShiftSubmitServiceImpl();
//        clientGenerateQRCodeLocalServiceImpl();
//        cashierGenerateRealnameQRCodeLocalServiceImpl();
//        cashierCreateBillingCardWithPackageTimeServiceImpl();
        cashierCreateBillingCardWithAmountNewServiceImpl();
    }

    @Test
    void core() {
        log.info("core exec");
    }

    @DisplayName("/billing/core/cybercafe 接口参数初始化")
    @Test
    void initParam() {
        getSignStr();
    }

    private void getSignStr() {
        log.info("\nrequestBody(serviceIndex:::{}, placeId:::{}, params:::{})", serviceIndex, placeId, params);

        // 是否默认网吧
        boolean defaultPalceId = "00000000000000".equals(placeId);

        // 检查网吧ID
//        if (!defaultPalceId) {
//            GenericResponse<ObjDTO<PlaceProfileBO>> placeProfileResponse = placeServerService.findByPlaceId(placeId);
//            if (placeProfileResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
//                if (placeProfileResponse.getCode() == ServiceCodes.NOT_FOUND.getCode()) {
//                    return "网吧ID不存在";
//                }
//                if (placeProfileResponse.getCode() == ServiceCodes.NO_SERVICE.getCode()) {
//                    return "Place-Server服务调用失败";
//                }
//                return placeProfileResponse.getMessage();
//            }
//        }

        // 检查网吧识别码
//        String identifierKey = BaseConstants.REDIS_KEY_PREFIX_GET_IDENTIFIER_BY_PLACEID_ + placeId;
//        if (!defaultPalceId) {
//            boolean identifierExsit = stringRedisTemplate.hasKey(identifierKey);
//            if (!identifierExsit) {
//                return "Redis中没有网吧识别码";
//            }
//        }

        // 头部标识
        StringBuilder requestBody = new StringBuilder("4W");

        // 接口序号
        String serviceIndexHex = Integer.toHexString(serviceIndex);
        requestBody.append(StringUtils.leftPad(serviceIndexHex, 3, "0"));

        // 时间戳
        Long timestap = System.currentTimeMillis() / 1000;
        requestBody.append(timestap);

        // 场所ID
        requestBody.append(placeId);

        // 业务参数（最后一个有值入参前，参数没有值的参数都为空，用00占位）
        int indexOfLastHaveValue = getIndexOfLastHaveValue(params); // 最后一个有值的参数，都没值，为-1
        for (int index = 0; index < params.length; index++) {
            if (index < indexOfLastHaveValue || index == indexOfLastHaveValue) {
                if (StringUtils.isEmpty(params[index])) {
                    requestBody.append("00");
                } else {
                    int len = params[index].length();
                    String lenHex = Integer.toHexString(len);
                    requestBody.append(StringUtils.leftPad(lenHex, 2, "0"));
                    requestBody.append(params[index]);
                }
            }
        }

        // 签名
        if (defaultPalceId) {
            String sign = Hashing.md5().hashBytes((requestBody.toString()).getBytes()).toString().substring(8, 24);
            requestBody.append(sign.toUpperCase());
        } else {
//            String key = stringRedisTemplate.opsForValue().get(identifierKey);
            String key = identifierKey;
            String sign = Hashing.md5().hashBytes((requestBody + key).getBytes()).toString().substring(8,
                    24);
            requestBody.append(sign.toUpperCase());
        }

        log.info("\nrequestBody:::{}", requestBody.toString());
    }

    private int getIndexOfLastHaveValue(String[] params) {
        int indexOfLastHaveValue = -1;
        for (int index = 0; index < params.length; index++) {
            if (!StringUtils.isEmpty(params[index])) {
                indexOfLastHaveValue = index;
            }
        }
        return indexOfLastHaveValue;
    }

    private void cashierGenerateRealnameQRCodeLocalServiceImpl() {
        serviceIndex = 588; //收银台本地实名二维码 0x24C

//        String placeId = params.get(0); // 场所ID
//        String cashierId = params.get(1); // 收银台ID

        params = new String[1]; //除了第一个场所id外的其他参数，根据具体的接口指定大小
        params[0] = "10000256";
    }

    private void clientGenerateQRCodeLocalServiceImpl() {
        serviceIndex = 290; // 客户端生成二维码(本地), 0x122

//        String placeId = params.get(0); // 场所ID
//        String clientId = params.get(1); // 客户端ID
//        String areaId = params.get(2); // 区域Id

        params = new String[2]; //除了第一个场所id外的其他参数，根据具体的接口指定大小
        params[0] = "4020";
        params[1] = "2001";
    }

    private void cashierShiftSubmitServiceImpl() {
        serviceIndex = 554; // 十进制，十六进制为 0x22A，收银台确认交接班

//        String placeId = params.get(0);

//        String shiftId = params.get(1); // 当前班次
//        String accountId = params.get(2); // 当班人账号Id
//        String successorAccountId = params.get(3); // 交班人账号Id
//        String password = params.get(4); // 交班人密码
//        String nextShiftHandoverCashStr = params.get(5); // 预留下班金额
//        String remark = params.get(6); // 备注

        params = new String[6]; //除了第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID;
        params[1] = "********";
        params[2] = "********";
        params[3] = "Lgj@1234";
        params[4] = "100";
        params[5] = "接口测试";
    }

    /**
     * <AUTHOR>
     * @Description 收银台带钱开卡
     * @Date 14:18 2023/8/1
     * @Param
     * @see CashierCreateBillingCardWithAmountServiceImpl
     **/
    private void interface590() {
        serviceIndex = 590; // 十进制，十六进制为 0x024d，收银台带钱开卡

        params = new String[14]; //除了第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "5000";
        params[1] = "1001";
        params[2] = "xxxx";
        params[3] = "xxx";
        params[4] = "";

        params[5] = "";
        params[6] = "";
        params[7] = "-1";
        params[8] = "1";
        params[9] = "";

        params[10] = "17665373000";
        params[11] = "备注，接口测试";
        params[12] = "1000";
        params[13] = "1000";
    }

    /**
     * <AUTHOR>
     * @Description 收银台登入
     * @see CashierLoginServiceImpl
     */
    private void cashierLoginServiceImpl() {
        serviceIndex = 552; // 十进制，十六进制为 0x024d，收银台带钱开卡

//        String placeId = params.get(0); //  场所ID
//        String cashierId = params.get(1); // 收银台ID
//        String username = params.get(2); // 账号ID
//        String password = params.get(3); // 密码

        params = new String[3]; //除了第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "10000247";
        params[1] = "收银1";
        params[2] = "Lgj%1234555555555555555555555555555555555555555555555555555555555555555";
    }

    /**
     * <AUTHOR>
     * @Description 收银台注册接口
     * @see CashierReportVersionServiceImpl
     */
    private void interface523() {
        serviceIndex = 523; // 十进制，十六进制为 0x020B

//        String placeId = params.get(0);

//        String hostName = params.get(1); // 主机名
//        String ipAddr = params.get(2); // IP地址
//        String macAddr = params.get(3); // MAC地址
//        String cashierVersion = params.get(4); // 收银台版本
//        String osVersion = params.get(5); // 系统版本
//        String majorVersion = params.get(6); // 主版本
//        String updateVersion = params.get(7); // 更新版本
//        String localServerVersion = params.get(8); // 本地服务版本

        params = new String[8]; //除了第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "收银台001";
        params[1] = "*************";
        params[2] = "000C293F655E";
        params[3] = "********";
        params[4] = "";

        params[5] = "";
        params[6] = "";
        params[7] = "-1";
    }

    /**
     * 客户端登录
     * @see ClientLoginServiceImpl
     */
    private void clientLogin() {
        serviceIndex = 259; // 十进制

//        String placeId = params.get(0); // 场所ID
//        String clientId = params.get(1); // 客户端ID
//        String areaId = params.get(2); // 区域Id
//        String loginName = params.get(3); // 登录账号
//        String loginPass = params.get(4); // 登录密码

        params = new String[4]; //除了第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "4001";
        params[1] = "2001";
        params[2] = "xxx";
        params[3] = "123456";
    }

    /**
     * 收银台激活
     *
     * @see CashierActivationServiceImpl
     */
    private void cashierActivationServiceImpl() {
        serviceIndex = 512; // 十进制


//        String placeId = params.get(0);

//        String shiftId = params.get(1); // 班次ID
//        String idNumber = params.get(2);
//        String activeType = null;
//        String identification = null;
//        if (params.size() >= 4) { // 新老接口兼容
//            activeType = params.get(3); // 激活方式,传value值
//        }
//
//        if (params.size() == 5) { // 新老接口兼容
//            identification = params.get(4); // 附加费标识
//        }

        params = new String[4]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID;
        params[1] = "xxx";
        params[2] = "1";
        params[3] = "Y";
    }

    /**
     *
     * 客户端登出
     * @see ClientLogoutServiceImpl
     */
    private void clientLogoutServiceImpl() {

//        String placeId = params.get(0); // 场所ID

//        String clientId = params.get(1);// clientId
//        String cardId = params.get(2); // 卡id

        serviceIndex = 260; // 十进制
        params = new String[2]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "4001";
        params[1] = "1000178713";
    }

    private void cashierQueryBillingOnlineServiceImpl () {
        serviceIndex = 520; // 十进制
        params = new String[1]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "1000178781";
    }

    private void CashierLogoutServiceImpl() {
        serviceIndex = 515; // 十进制


//        String placeId = params.get(0); // 班次ID

//        String shiftId = params.get(1); // 收银台ID
//        String clientId = params.get(2);// clientId
//        String cardId = params.get(3); // 卡id
//        int refundAmount = 0; // 退款金额

        params = new String[3]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID;
        params[1] = "4001";
        params[2] = "1000178781";
    }

    private void CashierQueryShiftStatisticsServiceImpl() {
        //553
        serviceIndex = 553; // 十进制


//        String placeId = params.get(0);
//        String shiftId = params.get(1); // 班次ID

        params = new String[1]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID;
    }

    /**
     * 收银台分页查询交班页面操作记录
     * @see CashierQueryPageShiftLogOperationServiceImpl
     *
     */
    private void CashierQueryPageShiftLogOperationServiceImpl() {
        //553
        serviceIndex = 582; // 十进制


//        if (params.size() < 7) {
//            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
//        }
//
//        String placeId = params.get(0);
//        String shiftId = params.get(1); // 班次ID
//        String startStr = params.get(2);
//        String pageSizeStr = params.get(3);
//        String operationType = params.get(4);
//        String idName = params.get(5);
//        String idNumber = params.get(6);
//
//        int start = 0;
//        int pageSize = 100;
//
//        try {
//            start = Integer.parseInt(startStr);
//            pageSize = Integer.parseInt(pageSizeStr);
//            if (pageSize > 100) {
//                pageSize = 100;
//            }
//        } catch (Exception e) {
//            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
//        }
//
//        Map<String, Object> queryMap = new HashMap<>();
//        if (params.size() == 9) {
//            String startTime = params.get(7);
//            String endTime = params.get(8);
//
//            queryMap.put("startDate", startTime);
//            queryMap.put("endDate", endTime);
//        }

        params = new String[9]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID; //shiftId
        params[1] = "0";
        params[2] = "10";
        params[3] = "";
        params[4] = "";
        params[5] = "";
        params[6] = "";
        params[7] = "2023-08-05 15:00:00";
        params[8] = "2023-08-09 10:00:00";

//        params = new String[3]; //排除第一个场所id外的其他参数，根据具体的接口指定大小
//
//        params[0] = "5437"; //shiftId
//        params[1] = "0";
//        params[2] = "10";
    }

    /**
     *
     * 查询计费卡信息
     * @see ClientQueryBillingCardServiceImpl
     */
    private void clientQueryBillingCardServiceImpl() {
//        String placeId = params.get(0); // 场所ID
//        String idNumber = params.get(1); // 身份证号码

        serviceIndex = 262; // 十进制
        params = new String[1]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = ID_NUMBER;
    }

    /**
     * 客户端计费通知
     *
     * @see ClientBillingServiceImpl
     */
    private void clientBillingServiceImpl() {

//        String placeId = params.get(0);
//        String clientId = params.get(1);

        serviceIndex = 273; // 十进制

        params = new String[1]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = "4001";
    }

    /**
     * @see CashierCreateBillingCardWithPackageTimeServiceImpl
     */
    private void cashierCreateBillingCardWithPackageTimeServiceImpl() {
        // 获取参数
//        String placeId = params.get(0); // 场所ID
//        String shiftId = params.get(1); // 班次ID
//        String cardTypeId = params.get(2); // 卡类型ID
//        String idNumber = params.get(3); // 身份证号码
//        String name = params.get(4); // 姓名
//        String address = params.get(5); // 地址
//        String issuingAuthority = params.get(6); // 发证机关
//        String nation = params.get(7); // 民族
//        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡
//        String activeType = params.get(9); // 激活方式,传value值
//        String identification = params.get(10); // 附加费标识
//        String phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
//        String remark = params.get(12); // 备注
//        if (!org.springframework.util.StringUtils.isEmpty(remark) && remark.length() > 100) {
//            log.warn("remark length is too long, remark:{}", remark);
//            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
//        }
//
//        int cashAmount = 0;
//        String cashAmountStr = params.get(13); // 充值金额
//
//        String ruleId = params.get(14); // 包时规则ID
//        String packageType = params.get(15); // 包时类型 1:余额包时 2:现金包时，当前为固定值1

        serviceIndex = 606; // 十进制
        params = new String[15]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID; //shiftId
        params[1] = "1000";
        params[2] = ID_NUMBER;
        params[3] = NAME;
        params[4] = "";
        params[5] = "";
        params[6] = "";
        params[7] = "";
        params[8] = "1"; // 激活方式
        params[9] = "";
        params[10] = "";
        params[11] = "";
        params[12] = "3000"; // 充值金额
        params[13] = "3000"; // 包时规则ID
        params[14] = "1"; // 包时类型

    }

    /**
     * @see CashierCreateBillingCardWithAmountNewServiceImpl
     */
    private void cashierCreateBillingCardWithAmountNewServiceImpl() {

//        String placeId = params.get(0); // 场所ID
//        String shiftId = params.get(1); // 班次ID
//        String cardTypeId = params.get(2); // 卡类型ID
//        String idNumber = params.get(3); // 身份证号码
//        String name = params.get(4); // 姓名
//        String address = params.get(5); // 地址
//        String issuingAuthority = params.get(6); // 发证机关
//        String nation = params.get(7); // 民族
//        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡
//        String activeType = params.get(9); // 激活方式,传value值
//        String identification = params.get(10); // 附加费标识
//        String phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
//        String remark = params.get(12); // 备注
//        if (!org.springframework.util.StringUtils.isEmpty(remark) && remark.length() > 100) {
//            log.warn("remark length is too long, remark:{}", remark);
//            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
//        }
//
//        int cashAmount = 0;
//        int presentAmount = 0;
//        TopupRule effectedTopupRule = null;
//        String cashAmountStr = params.get(13); // 充值金额
//        String topupRuleId = params.get(14); // 充值规则ID，根据传值的充值规则id去查询充值规则获取赠送金额

        serviceIndex = 598; // 十进制
        params = new String[14]; //排除第一个场所id外的其他参数，根据具体的接口指定大小

        params[0] = SHIFT_ID; //shiftId
        params[1] = "1000";
        params[2] = ID_NUMBER;
        params[3] = NAME;
        params[4] = "";
        params[5] = "";
        params[6] = "";
        params[7] = "";
        params[8] = "1"; // 激活方式
        params[9] = "";
        params[10] = "";
        params[11] = "";
        params[12] = "3000"; // 充值金额
        params[13] = "1"; // 充值规则ID

    }
}