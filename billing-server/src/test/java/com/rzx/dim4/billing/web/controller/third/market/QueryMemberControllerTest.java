package com.rzx.dim4.billing.web.controller.third.market;

import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

class QueryMemberControllerTest {

    @Test
    void queryMember() {
        String[] params = {"thirdAccountId", "timestamp", "placeId", "idNumber"};
        Arrays.stream(params).sorted().forEach(System.out::println);
        //idNumber
        //placeId
        //thirdAccountId
        //timestamp
    }
}