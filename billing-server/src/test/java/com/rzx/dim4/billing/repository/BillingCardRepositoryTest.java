package com.rzx.dim4.billing.repository;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;


@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
//@DataJpaTest
@TestPropertySource(properties = {
        "spring.profiles.active=dev183",
})
//@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class BillingCardRepositoryTest {

    @Autowired private DataSource dataSource;
    @Autowired private JdbcTemplate jdbcTemplate;
    @Autowired private EntityManager entityManager;
    @Autowired private BillingCardRepository billingCardRepository;

    @Test
    void injectedComponentsAreNotNull(){
        assertThat(dataSource).isNotNull();
        assertThat(jdbcTemplate).isNotNull();
        assertThat(entityManager).isNotNull();
        assertThat(billingCardRepository).isNotNull();
    }


    @Test
    void findCardNumAndTotalAccount2() {

        String placeId = "**************";
        String cardTypeId = "";
        String idName =  "";
        String idNumber = "";
        String startTime = "";

        String endTime = "";
        String logoutStartTime = ""; // 上次结账开始时间
        String logoutEndTime = ""; // 上次结账结束时间
        String mobile = ""; // 手机号
        String minTotalAmount = ""; // 网费余额最小值（=现金余额+赠送余额，单位为分）

        String maxTotalAmount = ""; // 网费余额最大值（=现金余额+赠送余额，单位为分）
        String minPoints = "10"; // 积分最小值
        String maxPoints = "100"; // 积分最大值
        String remark = ""; // 备注(< 100 个字符
        List<String> cardIds = new ArrayList<>();

        Integer minTotalAmountInt = StringUtils.isEmpty(minTotalAmount) ? null : Integer.parseInt(minTotalAmount);
        Integer maxTotalAmountInt = StringUtils.isEmpty(maxTotalAmount) ? null : Integer.parseInt(maxTotalAmount);
        Integer minPointsInt = StringUtils.isEmpty(minPoints) ? null : Integer.parseInt(minPoints);
        Integer maxPointsInt = StringUtils.isEmpty(maxPoints) ? null : Integer.parseInt(maxPoints);

        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        Map<String, BigDecimal> map = billingCardRepository.findCardNumAndTotalAccount2(placeId, cardTypeId, idName, idNumber, startDateTime, endDateTime,
                mobile, minTotalAmountInt, maxTotalAmountInt, minPointsInt, maxPointsInt, remark, cardIds,null,"");

        log.info("map: {}", new Gson().toJson(map));
    }

}