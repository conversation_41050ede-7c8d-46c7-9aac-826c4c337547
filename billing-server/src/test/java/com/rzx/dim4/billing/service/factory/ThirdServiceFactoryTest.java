package com.rzx.dim4.billing.service.factory;

import com.rzx.dim4.base.enums.billing.ThirdServiceIndexes;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Set;

@Disabled("单元测试不执行")
@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {
        "spring.profiles.active=dev",
})
 class ThirdServiceFactoryTest {

    @Autowired
    private ThirdServiceFactory thirdServiceFactory;

    @Test
    void core() {
        Set<ThirdServiceIndexes> keys =
                thirdServiceFactory.getKeys();

        log.info("keys: {}", keys);

        StringBuilder sb = new StringBuilder();
        for (ThirdServiceIndexes key : keys) {
            sb.append("{").append(key.getValue()).append(", ").append(key.getDisplay()).append("},   \n");
        }
        log.info("keys: \n{}", sb.toString());
    }
}