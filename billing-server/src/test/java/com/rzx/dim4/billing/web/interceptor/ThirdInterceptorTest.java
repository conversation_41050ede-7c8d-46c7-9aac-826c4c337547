package com.rzx.dim4.billing.web.interceptor;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

class ThirdInterceptorTest {

    @Test
    void getParaMap() {

        Map<String, String> map = new HashMap<>();
        map.put("thirdAccountId","1");
        map.put("timestamp","2");
        map.put("sign","3");
        map.put("placeId","3");
        map.put("idNumber","3");

        System.out.println(map);

        Map<String, String> treemap = new TreeMap<>(map);  // 不区分大小写
        System.out.println(treemap);
    }
}