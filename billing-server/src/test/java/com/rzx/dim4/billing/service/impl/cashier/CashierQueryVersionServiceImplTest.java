package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.ClientVersionBO;
import com.rzx.dim4.base.dto.AbstractDTO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.ClientVersion;
import com.rzx.dim4.billing.service.ClientVersionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
class CashierQueryVersionServiceImplTest {

    @Mock
    private ClientVersionService clientVersionService;

    @InjectMocks
    private CashierQueryVersionServiceImpl cashierQueryVersionService;

    private static String placeId;

    private static ClientVersion clientVersion;
    private static ClientVersion cashierVersion;

    private static List<String> params;
    @BeforeAll
    static void setUp() {
        log.info("setUp......");

        placeId = "placeId1";

        clientVersion = new ClientVersion();
        clientVersion.setClientType(0);
        clientVersion.setVersionNumber("1.1.1");

        cashierVersion = new ClientVersion();
        cashierVersion.setClientType(1);
        cashierVersion.setVersionNumber("1.1.1");

        params = new ArrayList<>();
        params.add("placeId1");
    }

    @Test
    void doService() {

        when(clientVersionService.getClientVersion(placeId, "0")).thenReturn(clientVersion);
        when(clientVersionService.getClientVersion(placeId, "1")).thenReturn(cashierVersion);

        GenericResponse<?> response = cashierQueryVersionService.doService(params);
        assertEquals(response.getCode(), ServiceCodes.NO_ERROR.getCode());

        AbstractDTO data = response.getData();
        assertEquals(data.getClass(), ListDTO.class);

        ListDTO<ClientVersionBO> listDTO = (ListDTO<ClientVersionBO>) data;
        List<ClientVersionBO> list = listDTO.getList();
        assertEquals(list.size(), 2);

        ClientVersionBO clientVersionBO = list.get(0);
        assertEquals(clientVersionBO.getClientType(), 0);

        ClientVersionBO cashierVersionBO = list.get(1);
        assertEquals(cashierVersionBO.getClientType(), 1);
    }
}