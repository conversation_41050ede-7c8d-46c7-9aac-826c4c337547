package com.rzx.dim4.billing.web.controller.third.market;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

@Disabled
class PaymentOrderControllerTest {

    @Test
    void createPaymentOrder() {

        String[] params = {"thirdAccountId", "timestamp", "placeId", "cardId", "amount", "payType", "openId", "idNumber"};
        Arrays.stream(params).sorted().forEach(System.out::println);
        // amount
        //cardId
        //idNumber
        //openId
        //payType
        //placeId
        //thirdAccountId
        //timestamp
    }

}