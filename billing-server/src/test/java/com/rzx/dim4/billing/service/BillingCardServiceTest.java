package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogOperation;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.LogOperationRepository;
import com.rzx.dim4.billing.service.algorithm.UpDownUserLevelAlgorithm;
import com.rzx.dim4.billing.service.impl.cashier.CashierCancelPackageTimeNewServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.CashierQueryLogOperationServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.CashierReversalBillingCardServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.CashierTopupBillingCardNewServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

@Disabled("单元测试不执行")
@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {
        "spring.profiles.active=dev",
})
class BillingCardServiceTest {


    @Autowired
    private BillingCardRepository billingCardRepository;


    @Autowired private BillingCardService billingCardService;


    @Autowired
    UpDownUserLevelAlgorithm upDownUserLevelAlgorithm;

    @Autowired
    CashierReversalBillingCardServiceImpl cashierReversalBillingCardService;

    @Autowired
    CashierTopupBillingCardNewServiceImpl cashierTopupBillingCardNewService;

    @Autowired
    CashierCancelPackageTimeNewServiceImpl cashierCancelPackageTimeNewService;

    @Autowired
    CashierQueryLogOperationServiceImpl cashierQueryLogOperationService;

    @Autowired
    LogOperationRepository logOperationRepository;


    @Test
    void findAll() {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("placeId", "**************");
        queryMap.put("idName", "");
        queryMap.put("idNumber", "");
        queryMap.put("cardTypeId", "");
        queryMap.put("startTime", "");
        queryMap.put("endTime", "");
        queryMap.put("notTemporaryCard", "1000");
        queryMap.put("mobile", "");
        queryMap.put("minTotalAmount", "10000");
        queryMap.put("maxTotalAmount", "");
        queryMap.put("minPoints", "");
        queryMap.put("maxPoints", "");
        queryMap.put("remark", "");

        Pageable pageable = PageRequest.of(0, 10, Sort.Direction.fromString("desc"), "id");

        Page<BillingCard> billingCardPage = billingCardService.findAll(queryMap, pageable);

        log.info("billingCardPage: {}", billingCardPage);
    }

    /**
     * 查询操作记录
     */
    /**
     * 批量修改卡类型
     * */
//    @Test
//    void batchUpdateCardType() {
//        // 根据自动升降级用户等级
//        String placeId = "00000000000136";
//        String cardId = "1000292955,1000292919";
//        List<String> cardIdList = new ArrayList<>(Arrays.asList(cardId.split(",")));
//
//        // 批量修改卡类型
////        List<String> cardIdList = new ArrayList<>(Arrays.asList(cardId));
//        billingCardService.updateCardType(placeId, "1001", "vip7", cardIdList);
//    }


    /**
     * 根据积分自动升降用户等级公用方法
     * */
    @Test
    void autoUpOrDownUserLevel() {
        // 根据扣积分自动升降级用户等级
        String placeId = "00000000000136";
        String cardId = "1000292921";
        BillingCard billingCard = billingCardService.findByPlaceIdAndCardId(placeId,cardId).get();
        LogShift logShift = null;
        upDownUserLevelAlgorithm.autoUpOrDownUserLevel(billingCard,10,1, 0, SourceType.CASHIER,logShift);
    }


    /**
     * 收银台充值
     * */
    @Test
    void cashierTopup() {
        List<String> params = new ArrayList<>();
        params.add("00000000000136");
        params.add("5002");
        params.add("1000292921");
        params.add("1000");
        params.add("");
        cashierTopupBillingCardNewService.doService(params);
    }


    /**
     * 收银台取消包时
     * 看是否会进行自动升降级
     * */
    @Test
    void cashierCancelPackage() {
        List<String> params = new ArrayList<>();
//        String placeId = params.get(0); // 场所ID
//        String shiftId = params.get(1); // 班次ID
//        String cardId = params.get(2); // 身份证号码
//        String ruleId = params.get(3);//包时规则ID
//        String refundType = params.get(4);//退款方式，用户选择退现金还是退余额。1-余额退费，2-现金退费
        params.add("00000000000136");
        params.add("5002");
        params.add("1000292921");
        params.add("3000");
        params.add("1");
        cashierCancelPackageTimeNewService.doService(params);
    }


    /**
     * 收银台查询操作入职
     * 看是否会进行自动升降级
     * */
    @Test
    void cashierQueryOperation() {
        List<String> params = new ArrayList<>();
//        String placeId = params.get(0); // 场所ID
//        String shiftId = params.get(1); // 班次ID
//        String cardId = params.get(2); // 身份证号码
//        String ruleId = params.get(3);//包时规则ID
//        String refundType = params.get(4);//退款方式，用户选择退现金还是退余额。1-余额退费，2-现金退费
        params.add("00000000000136");
        params.add("5002");
        params.add("0");
        params.add("4");
        params.add("");
        params.add("2024-02-05 10:02:26");
        params.add("2024-03-06 23:02:26");
        params.add("");
        params.add("15");
//        params.add("110101199307300015");
//        params.add("");
//        params.add("");
//        params.add("");
        cashierQueryLogOperationService.doService(params);
    }

    /** 调用写操作记录的方法
     * 添加一条操作记录
     */
    @Test
    void insertLogOperation() {
        String details = "冲正本金" + 100 + "元，奖励" + 200 + "元";
        BillingCard billingCard = new BillingCard();
        billingCard.setPlaceId("**************");
        billingCard.setCardId("526332");
        billingCard.setIdNumber("360731199512011162");
        billingCard.setIdName("分表数据插入测试");
        billingCard.setCardTypeId("1001");
        billingCard.setCardTypeName("测试插入表");
        billingCard.setCashAccount(10000);
        billingCard.setPresentAccount(10000);
        for (int i = 0; i <5; i++ ) {
            int cost = 100;
            cost += i;
            addLogOperation(OperationType.TOPUP, SourceType.CASHIER, cost, 100, null, null, billingCard, null, null,
                    "", details);
        }
    }


    /**
     * 添加一条操作记录
     *
     * @param operationType 操作类型
     * @param sourceType    操作来源
     * @param cost          操作金额
     * @param present       操作赠送金额
     * @param clientId      客户端ID
     * @param lastClientId  换机是上一个客户端ID
     * @param billingCard   计费卡
     * @param loginId       登录ID
     * @param logShift      班次信息
     * @param remark        备注
     * @return
     */
    private LogOperation addLogOperation(OperationType operationType, SourceType sourceType, int cost, int present,
                                         String clientId, String lastClientId, BillingCard billingCard, String loginId, LogShift logShift,
                                         String remark, String details) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(operationType, "expenseType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        LocalDateTime now = LocalDateTime.now();
        LogOperation logOperation = new LogOperation();
        logOperation.setCreated(now.minusNanos(now.getNano())); // 不要毫秒
        logOperation.setSourceType(sourceType);
        logOperation.setOperationType(operationType);
        logOperation.setCost(cost);
        logOperation.setPresent(present);
        logOperation.setPlaceId(billingCard.getPlaceId());
        logOperation.setCardId(billingCard.getCardId());
        logOperation.setIdNumber(billingCard.getIdNumber());
        logOperation.setIdName(billingCard.getIdName());
        logOperation.setCardId(billingCard.getCardId());
        logOperation.setCardTypeId(billingCard.getCardTypeId());
        logOperation.setCardTypeName(billingCard.getCardTypeName());
        logOperation.setCashBalance(billingCard.getCashAccount());
        logOperation.setPresentBalance(billingCard.getPresentAccount());
        logOperation.setRemark(remark);
        logOperation.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        logOperation.setCreaterName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        logOperation.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logOperation.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
        logOperation.setClientId(clientId);
        logOperation.setLastClientId(lastClientId);
        logOperation.setLoginId(loginId);
        logOperation.setDetails(details);

        logOperationRepository.save(logOperation);
        return logOperation;
    }

//    @Test
//    void queryCahier() {
//        List<String> params= new ArrayList<>();
//        params.add("**************");
//        params.add("5100");
//        params.add("0");
//        params.add("4");
//        cashierQueryLogOperationService.doService(params);
//
//    }
}