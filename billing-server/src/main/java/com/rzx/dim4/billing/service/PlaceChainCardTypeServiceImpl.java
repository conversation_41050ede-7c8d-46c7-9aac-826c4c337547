package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.UpdateConfigBusinessBO;
import com.rzx.dim4.base.bo.place.PlaceChainCardTypeBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.billing.entity.BillingCardType;
import com.rzx.dim4.billing.entity.PlaceChainCardType;
import com.rzx.dim4.billing.entity.PlaceChainStores;
import com.rzx.dim4.billing.repository.BillingCardTypeRepository;
import com.rzx.dim4.billing.repository.PlaceChainCardTypeRepository;
import com.rzx.dim4.billing.repository.PlaceChainStoresRepository;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlaceChainCardTypeServiceImpl implements PlaceChainCardTypeService {

    @Autowired
    PlaceChainCardTypeRepository placeChainCardTypeRepository;

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private BillingCardTypeRepository billingCardTypeRepository;

    @Autowired
    private PlaceChainStoresRepository placeChainStoresRepository;

    @Autowired
    private NotifyServerService notifyServerService;

    public PlaceChainCardType save(PlaceChainCardType placeChainCardType) {
        return placeChainCardTypeRepository.save(placeChainCardType);
    }

    @Override
    public List<PlaceChainCardType> findByChainIdAndDeleted(String chainId, int deleted) {
        return placeChainCardTypeRepository.findByChainIdAndDeleted(chainId, deleted);
    }

    @Override
    public Optional<PlaceChainCardType> findByChainIdAndCardTypeId(String chainId, String cardTypeId) {
        return placeChainCardTypeRepository.findByChainIdAndCardTypeIdAndDeleted(chainId, cardTypeId, 0);
    }

    @Override
    public void addNewCardType(PlaceChainCardTypeBO placeChainCardTypeBO) {
        String  minPointsRequirement = String.valueOf(placeChainCardTypeBO.getMinPointsRequirement());
        if (null == placeChainCardTypeBO || StringUtils.isEmpty(placeChainCardTypeBO.getChainId())
                || StringUtils.isEmpty(placeChainCardTypeBO.getTypeName())  || placeChainCardTypeBO.getMinCreateCardAmount() <0  ||  placeChainCardTypeBO.getMinPointsRequirement() <0) {
            log.warn("创建连锁会员卡失败，入参有误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        String chainId = placeChainCardTypeBO.getChainId();
        String typeName = placeChainCardTypeBO.getTypeName().trim();
        Optional<PlaceChainCardType> placeChainCardTypeOptional =
                placeChainCardTypeRepository.findByChainIdAndTypeNameAndDeleted(chainId, typeName, 0);
        if (placeChainCardTypeOptional.isPresent()) {
            log.info("卡类型名称已被使用");
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_USED);
        }

        PlaceChainCardType placeChainCardType = new PlaceChainCardType();
        BeanUtils.copyProperties(placeChainCardTypeBO, placeChainCardType);
        placeChainCardType.setCreated(LocalDateTime.now());
        placeChainCardType.setCreater(-1L);
        if (StringUtils.isEmpty(placeChainCardTypeBO.getCardTypeId())) {
            placeChainCardType.setCardTypeId(buildCardTypeId(placeChainCardTypeBO.getChainId()));
        }

        // 同步至场所卡类型表
        billingCardTypeService.synchronizedNewCardTypeFromChain(chainId, placeChainCardType.getTypeName(), placeChainCardType.getCardTypeId(),placeChainCardType.getMinCreateCardAmount(),placeChainCardType.getMinPointsRequirement());

        placeChainCardTypeRepository.save(placeChainCardType);
    }

    /**
     * 连锁卡类型修改功能
     * auth:qdh
     * */
    @Override
    public void editCardType(PlaceChainCardTypeBO placeChainCardTypeBO) {
        if (null == placeChainCardTypeBO || StringUtils.isEmpty(placeChainCardTypeBO.getChainId())
                || StringUtils.isEmpty(placeChainCardTypeBO.getTypeName())  || placeChainCardTypeBO.getMinCreateCardAmount() <0  ||  placeChainCardTypeBO.getMinPointsRequirement() <0) {
            log.warn("编辑连锁会员卡失败，入参有误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        String chainId = placeChainCardTypeBO.getChainId();
        String typeName = placeChainCardTypeBO.getTypeName().trim();
        Optional<PlaceChainCardType> placeChainCardTypeOptional =
                placeChainCardTypeRepository.findByChainIdAndTypeNameAndDeleted(chainId, typeName, 0);
        if (!placeChainCardTypeOptional.isPresent()) {
            log.info("卡类型不存在");
            throw new ServiceException(ServiceCodes.BILLING_CHAIN_CARD_TYPE_NOT_EXIST);
        }
        // 增加”最小积分要求“和”最低开卡金额“内容
        PlaceChainCardType placeChainCardType = placeChainCardTypeOptional.get();
        placeChainCardType.setUpdated(LocalDateTime.now());
        placeChainCardType.setMinCreateCardAmount(placeChainCardTypeBO.getMinCreateCardAmount());
        placeChainCardType.setMinPointsRequirement(placeChainCardTypeBO.getMinPointsRequirement());

        // 同步至场所卡类型表
        billingCardTypeService.synchronizedNewCardTypeFromChain(chainId, placeChainCardType.getTypeName(), placeChainCardType.getCardTypeId(),placeChainCardType.getMinCreateCardAmount(),placeChainCardType.getMinPointsRequirement());

        placeChainCardTypeRepository.save(placeChainCardType);
    }


    @Override
    public GenericResponse<PagerDTO<PlaceChainCardTypeBO>> page(DataTablesRequest dtRequest) {
        String chainId = dtRequest.getTheValueFromDtRequest("chainId");
        if (StringUtils.isEmpty(chainId)) {
            log.warn("分页查询连锁会员卡失败，入参有误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("chainId", chainId);
        queryMap.put("deleted", "0");
        int page = dtRequest.getStart() / dtRequest.getLength();
        Pageable pageable = PageRequest.of(page, dtRequest.getLength(), Sort.Direction.DESC, "id");

        Page<PlaceChainCardType> placeChainCardTypePage = findAll(queryMap, pageable);
        if (null != placeChainCardTypePage) {
            List<PlaceChainCardTypeBO> placeChainCardTypeBOList = new ArrayList<>();
            placeChainCardTypePage.getContent().forEach(placeChainCardType -> {
                PlaceChainCardTypeBO placeChainCardTypeBO = new PlaceChainCardTypeBO();
                BeanUtils.copyProperties(placeChainCardType, placeChainCardTypeBO);
                placeChainCardTypeBOList.add(placeChainCardTypeBO);
            });
            return new GenericResponse<>(new PagerDTO<>((int)placeChainCardTypePage.getTotalElements(), placeChainCardTypeBOList));
        } else {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }
    }

    @Override
    public void delete(String chainId, String chainCardTypeId) {
        if (StringUtils.isEmpty(chainId) || StringUtils.isEmpty(chainCardTypeId)) {
            log.warn("删除连锁会员卡失败，入参有误");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // if id = 1000000, not allow to delete
        if (chainCardTypeId.equals("1000000")) {
            log.warn("删除连锁会员卡失败，不允许删除默认会员卡");
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_ALLOW_DELETE);
        }

        PlaceChainCardType placeChainCardType = placeChainCardTypeRepository.findByChainIdAndCardTypeIdAndDeleted(chainId, chainCardTypeId, 0)
                .orElseThrow(() -> {
                    log.info("删除连锁会员卡失败，连锁会员卡类型不存在，chainId:{}, chainCardTypeId:{}", chainId, chainCardTypeId);
                    return new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
                });

		List<PlaceChainStores> placeChainStoresList = placeChainStoresRepository.findByChainIdAndDeleted(chainId, 0);
		if (CollectionUtils.isEmpty(placeChainStoresList)) {
            log.warn("连锁下面没有场所，直接删除连锁会员卡类型");
            placeChainCardType.setDeleted(1);
			placeChainCardType.setUpdated(LocalDateTime.now());
			placeChainCardTypeRepository.save(placeChainCardType);
			return;
		}

		List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
		// 校验，对应卡类型下面的场所会员卡是否已经被使用，如果已经有相应的会员则不允许删除
        // 没有的话，把场所下面的对应会员卡类型也删除
        List<BillingCardType> billingCardTypeListFromAllPlace =
				billingCardTypeRepository.findByChainCardTypeIdAndDeletedAndPlaceIdIn(chainCardTypeId, 0, placeIds);
        if (CollectionUtils.isEmpty(billingCardTypeListFromAllPlace)) {
            placeChainCardType.setDeleted(1);
            placeChainCardType.setUpdated(LocalDateTime.now());
            placeChainCardTypeRepository.save(placeChainCardType);
            return;
        }

        // 获取场所下面会员卡下面是否有会员
        billingCardService.findByBillingCardTypes(billingCardTypeListFromAllPlace).forEach(billingCard -> {
            if (null != billingCard) {
                log.info("删除连锁会员卡失败，会员卡已经被使用，chainId:{}, cardTypeId:{}", chainId, chainCardTypeId);
                throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_USED);
            }
        });

        // 删除对应的场所会员卡类型
        // foreach billingCardTypeList set deleted=1 and updated = now
        billingCardTypeListFromAllPlace.forEach(billingCardType -> {
            billingCardType.setDeleted(1);
            billingCardType.setUpdated(LocalDateTime.now());
        });
        billingCardTypeRepository.saveAll(billingCardTypeListFromAllPlace);

        placeChainCardType.setDeleted(1);
        placeChainCardType.setUpdated(LocalDateTime.now());
        placeChainCardTypeRepository.save(placeChainCardType);
        placeIds.forEach(e->{
            // 连锁增加卡类型同步给分店时，信息同步给分店收银台
            GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(e, "", "", BusinessType.UPDATECONFIG);
            if (pollingBOGeneric.isResult()) {
                PollingBO pollingBO = pollingBOGeneric.getData().getObj();
                if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                    UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
                    updateConfigBusinessBO.setPlaceId(e);
                    updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
                    updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                    updateConfigBusinessBO.setType(1);
                    // 保存收银台业务数据
                    notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
                }
            }
        });
    }

    @Synchronized
    private String buildCardTypeId(String chainId) {
        int cardTypeId = 100002;
        Optional<PlaceChainCardType> lastCardType = placeChainCardTypeRepository.findTop1ByChainIdOrderByIdDesc(chainId);
        if (lastCardType.isPresent()) {
            cardTypeId = Integer.parseInt(lastCardType.get().getCardTypeId()) + 1;
        }
        return String.valueOf(cardTypeId);
    }

    public Page<PlaceChainCardType> findAll(Map<String, String> map, Pageable pageable) {
        return placeChainCardTypeRepository.findAll(new Specification<PlaceChainCardType>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<PlaceChainCardType> root, CriteriaQuery<?> query,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<>();

                if (map.containsKey("chainId") && !StringUtils.isEmpty(map.get("chainId"))) {
                    predicateList.add(criteriaBuilder.equal(root.get("chainId"), map.get("chainId")));
                }

                if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {
                    predicateList.add(criteriaBuilder.equal(root.get("deleted").as(Integer.class), Integer.parseInt(map.get("deleted"))));
                }

                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicateArr));
            }
        }, pageable);
    }
}
