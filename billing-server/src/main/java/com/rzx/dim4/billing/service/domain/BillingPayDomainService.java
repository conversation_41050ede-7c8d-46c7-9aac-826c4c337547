package com.rzx.dim4.billing.service.domain;

import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.enums.payment.PayType;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/18
 **/
public interface BillingPayDomainService {

    /**
     * 小程序创建支付订单
     *
     * @param placeId 场所ID
     * @param idNumber 身份证号
     * @param amount 充值金额
     * @param openId 微信openId
     * @param cardTypeId  会员卡类型ID
     * @param idName 姓名
     * @param appId 小程序appId
     * @return PaymentResultBO
     */
    PaymentResultBO doMiniCreateOrder(String placeId,
                                      String idNumber,
                                      int amount,
                                      String openId,
                                      String cardTypeId,
                                      String idName,
                                      String appId);

    /**
     * 公众号创建支付订单
     *
     * @param placeId 场所ID
     * @param idNumber 身份证号
     * @param amount 充值金额
     * @param cardTypeId 会员卡类型ID
     * @param idName 姓名
     * @return 微信拉起支付链接
     */
    PaymentResultBO doMpCreateOrder(String placeId, String idNumber, int amount, String cardTypeId, String idName,String returnUrl,String topupRuleId,String openId);

    /**
     * 第三方创建订单（有无会员卡都能充值）
     * @param thirdAccountId 第三方账户ID
     * @param placeId 场所ID
     * @param cardId 会员卡ID
     * @param cardTypeId 会员卡类型ID
     * @param idNumber 身份证号
     * @param idName 姓名
     * @param amount 充值金额
     * @param payType 支付类型
     * @param openId 微信openId
     * @return PaymentResultBO
     */
    PaymentResultBO doThirdCreateOrder(String thirdAccountId,
                                String placeId,
                                String cardId,
                                String cardTypeId,
                                String idNumber,
                                String idName,
                                String amount,
                                String payType,
                                String openId,
                                String payCode);

    /**
     * 支付成功回调
     * @param orderId 订单ID
     * @param payType 支付类型
     * @return String
     */
    String onlineTopupNotify(String orderId, PayType payType);

    /**
     * iot创建支付订单
     * @param placeId
     * @param idNumber
     * @param amount
     * @param cardTypeId
     * @param name
     * @param payType
     * @param payCode
     * @return
     */
	PaymentResultBO doIotCreateOrder(String placeId, String idNumber, int amount, String cardTypeId, String name, String payType, String payCode);

    PaymentResultBO aliAppCreateOrder(String placeId, String idNumber, int amount, String openId, String cardTypeId, String idName, String topupRuleId, String appId);
}
