package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.SurchargeConfig;
import com.rzx.dim4.billing.repository.SurchargeConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class SurchargeConfigService {

    @Autowired
    SurchargeConfigRepository surchargeConfigRepository;

    public Optional<SurchargeConfig> findByPlaceId (String placeId) {
        return surchargeConfigRepository.findByPlaceId(placeId);
    }

    public SurchargeConfig save (SurchargeConfig surchargeConfig) {
        return surchargeConfigRepository.save(surchargeConfig);
    }
}
