package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingOnline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date Jun 30, 2020 5:23:45 PM
 */
public interface BillingOnlineRepository extends JpaRepository<BillingOnline, Long>, JpaSpecificationExecutor<BillingOnline> {

    int countByPlaceIdAndCardTypeIdAndFinished(String placeId, String cardTypeId, int finish);

    int countByPlaceIdAndCardTypeIdNotAndFinished(String placeId, String cardTypeId, int finish);

    Optional<BillingOnline> findByIdNumberAndFinished(String idNumber, int finished);

    Optional<BillingOnline> findTop1ByIdNumberOrderByIdDesc(String idNumber);

    /**
     * 根据场所id和cardId查询是否有在上机
     */
    Optional<BillingOnline> findByPlaceIdAndCardIdAndFinished(String placeId, String cardId, int finished);

    Optional<BillingOnline> findByPlaceIdAndClientIdAndFinished(String placeId, String clientId, int finished);

    Optional<BillingOnline> findByPlaceIdAndIdNumberAndFinished(String placeId, String idNumber, int finished);

    Optional<BillingOnline> findTop1ByPlaceIdAndCardIdAndLoginIdOrderByIdDesc(String placeId, String cardId, String loginId);

    Optional<BillingOnline> findTop1ByPlaceIdAndClientIdAndFinishedOrderByIdDesc(String placeId, String clientId, int finished);

    // 此方法仅为处理错误数据，findByPlaceIdAndCardIdAndLoginIdAndFinishedOrderByIdDesc
    Optional<BillingOnline> findTop1ByPlaceIdInAndIdNumberAndFinishedOrderByIdDesc(List<String> placeIds, String idNumber, int finished);

    Optional<BillingOnline> findByPlaceIdAndCardIdAndLoginIdAndFinishedOrderByIdDesc(String placeId, String cardId, String loginId, int finish);

    Optional<BillingOnline> findTop1ByPlaceIdAndIdNumberAndFinishedAndDeleted(String placeId, String idNumber, int finish, int deleted);


    List<BillingOnline> findByPlaceIdAndLoginId(String placeId, String loginId);

    List<BillingOnline> findByPlaceIdInAndFinished(List<String> placeIds, int finished);

    List<BillingOnline> findByPlaceIdAndFinishedOrderByIdDesc(String placeId, int finish);

    List<BillingOnline> findByPlaceIdAndFinishedAndClientIdOrderByIdDesc(String placeId, int finished, String clientId);

    // 新逻辑，这里要替换掉
    List<BillingOnline> findByPlaceIdAndCardIdInAndFinishedOrderByIdDesc(String placeId, List<String> cardId, int finished);

    List<BillingOnline> findByPlaceIdInAndIdNumberInAndFinished(List<String> placeIds, List<String> idNumbers, int finished);

    List<BillingOnline> findByPlaceIdAndClientIdInAndFinishedOrderByIdDesc(String placeId, List<String> clientId, int finished);

    List<BillingOnline> findByPlaceIdInAndIdNumberAndFinishedOrderByIdDesc(List<String> placeIds, String idNumber, int finished);

    List<BillingOnline> findAllByPlaceIdAndCardIdAndFinishedAndDeletedOrderByIdDesc(String placeId, String cardId, int finished, int deleted);

    List<BillingOnline> findTop5ByPlaceIdAndIdNumberAndFinishedAndDeletedOrderByIdDesc(String placeId, String idNumber, int finished, int deleted);

    List<BillingOnline> findAllByPlaceIdAndDeletedAndFinishedAndIdNumberIn(String placeId, int deleted, int finished, List<String> idNumbers);

    @Modifying
    @Transactional
    @Query(value = "UPDATE billing_online SET next_time = DATE_ADD(next_time,INTERVAL :timestamp SECOND_MICROSECOND),deduction = deduction + :onceDeduction, deduction_cash = deduction_cash + :onceDeductionCash, deduction_present = deduction_present + :onceDeductionPresent  WHERE id = :id ", nativeQuery = true)
    void updateOnlineNextTime(@Param("id") Long id, @Param("timestamp") Long timestamp, @Param("onceDeduction") int onceDeduction, @Param("onceDeductionCash") int onceDeductionCash, @Param("onceDeductionPresent") int onceDeductionPresent);

    // 结账后调用该方法清掉数据
    @Transactional
    void deleteByPlaceIdAndLoginId(String placeId, String loginId);

    @Query(value = "select sum(deduction) from billing_online where login_id = ?1", nativeQuery = true)
    Integer findSumDeductionByLoginId(String loginId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE billing_online SET card_type_id = :cardTypeId, updated = now() WHERE place_id = :placeId  and card_id = :cardId", nativeQuery = true)
    int updateOnlineCardTypeId(@Param("cardTypeId") String cardTypeId, @Param("placeId") String placeId, @Param("cardId") String cardId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE billing_online SET is_invite = :isInvite, updated = now() WHERE place_id = :placeId  and login_id = :loginId", nativeQuery = true)
    int updateOnlineInviteStatus(@Param("isInvite") int isInvite, @Param("placeId") String placeId, @Param("loginId") String loginId);


    int countByPlaceIdAndFinished(String placeId, int finish);

    Optional<BillingOnline> findUnfinishedByPlaceIdAndClientIdAndFinished(String placeId, String clientId, int finished);

}
