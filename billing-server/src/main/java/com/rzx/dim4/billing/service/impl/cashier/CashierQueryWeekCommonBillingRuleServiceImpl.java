package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingRuleCommonBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingRuleCommon;
import com.rzx.dim4.billing.service.BillingRuleCommonService;
import com.rzx.dim4.billing.service.CoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 收银台查询场所下所有的7*24小时费率信息
 */
@Service
public class CashierQueryWeekCommonBillingRuleServiceImpl implements CoreService {

    @Autowired
    BillingRuleCommonService billingRuleCommonService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() != 1) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        String placeId = params.get(0); // 场所ID

        List<BillingRuleCommon> billingRuleCommons = billingRuleCommonService.findByPlaceId(placeId);
        List<BillingRuleCommonBO> billingRuleCommonBOS = billingRuleCommons.stream().map(e->{
            return e.toBO();
        }).collect(Collectors.toList());

        return new GenericResponse<>(new ListDTO<>(billingRuleCommonBOS));
    }
}
