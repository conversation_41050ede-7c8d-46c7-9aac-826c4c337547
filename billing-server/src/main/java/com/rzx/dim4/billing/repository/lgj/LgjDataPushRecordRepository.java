package com.rzx.dim4.billing.repository.lgj;

import com.rzx.dim4.billing.entity.BillingCardBlackList;
import com.rzx.dim4.billing.entity.lgj.LgjDataPushRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Optional;


public interface LgjDataPushRecordRepository extends JpaRepository<LgjDataPushRecord, Long>, JpaSpecificationExecutor<LgjDataPushRecord> {

    Optional<LgjDataPushRecord> findByPlaceId(String placeId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE `lgj_data_push_record` SET billing_card_completions =  billing_card_completions + :billingCardCompletions,combined_number = combined_number + :combinedNumber,status = :status WHERE place_id = :placeId", nativeQuery = true)
    int updateStatusAndBillingCardCompletionsAndCombinedNumberByPlaceId(@Param("billingCardCompletions") int billingCardCompletions,@Param("combinedNumber") int combinedNumber,
                                                                        @Param("placeId") String placeId,@Param("status") int status);


    @Modifying
    @Transactional
    @Query(value = "UPDATE `lgj_data_push_record` SET billing_card_total = billing_card_total + :billingCardTotal,combined_number = combined_number + :combinedNumber,status = :status WHERE place_id = :placeId", nativeQuery = true)
    int updateStatusAndBillingCardTotalAndCombinedNumberByPlaceId(@Param("billingCardTotal") int billingCardTotal,@Param("combinedNumber") int combinedNumber,
                                                                  @Param("placeId") String placeId,@Param("status") int status);

    @Modifying
    @Transactional
    @Query(value = "UPDATE `lgj_data_push_record` SET billing_card_completions =  billing_card_completions + :billingCardCompletions,combined_number = combined_number + :combinedNumber,cloud_status = :cloudStatus WHERE place_id = :placeId", nativeQuery = true)
    int updateCloudStatusAndBillingCardCompletionsAndCombinedNumberByPlaceId(@Param("billingCardCompletions") int billingCardCompletions,@Param("combinedNumber") int combinedNumber,
                                                                        @Param("placeId") String placeId,@Param("cloudStatus") int cloudStatus);


    @Modifying
    @Transactional
    @Query(value = "UPDATE `lgj_data_push_record` SET billing_card_total = billing_card_total + :billingCardTotal,combined_number = combined_number + :combinedNumber,cloud_status = :cloudStatus WHERE place_id = :placeId", nativeQuery = true)
    int updateCloudStatusAndBillingCardTotalAndCombinedNumberByPlaceId(@Param("billingCardTotal") int billingCardTotal,@Param("combinedNumber") int combinedNumber,
                                                                  @Param("placeId") String placeId,@Param("cloudStatus") int cloudStatus);

}
