package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.billing.BillingRuleCommonBO;
import com.rzx.dim4.billing.entity.BillingRuleCommon;
import com.rzx.dim4.billing.repository.BillingRuleCommonRepository;
import lombok.Synchronized;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * 
 * <AUTHOR>
 * @date 2022年1月13日 上午11:05:09
 */
@Service
public class BillingRuleCommonService {

	@Autowired
	BillingRuleCommonRepository billingRuleCommonRepository;

	/**
	 * 查询该场所下所有的7*24费率信息
	 *
	 * @param placeId
	 * @return
	 */
	public List<BillingRuleCommon> findByPlaceId(String placeId) {
		return billingRuleCommonRepository.findByPlaceIdAndDeleted(placeId, 0);
	}

	/**
	 * 查询该网吧区域下面的所有7*24费率信息
	 *
	 * @param placeId
	 * @param areaId
	 * @return
	 */
	public List<BillingRuleCommon> findByPlaceIdAndAreaId(String placeId, String areaId) {
		return billingRuleCommonRepository.findByPlaceIdAndAreaIdAndDeleted(placeId, areaId, 0);
	}

	/**
	 * 查询该场所下，某个 区域 卡类型的7*24小时费率信息
	 *
	 * @param placeId
	 * @param areaId
	 * @param cardTypeId
	 * @return
	 */
	public Optional<BillingRuleCommon> billingRuleCommons(String placeId, String areaId, String cardTypeId) {
		return billingRuleCommonRepository.findByPlaceIdAndAreaIdAndCardTypeIdAndDeleted(placeId, areaId, cardTypeId, 0);
	}

	/**
	 * 标准计费，构建ruleId，（全部为奇数）
	 *
	 * @param placeId
	 * @return
	 */
	@Synchronized
	public String builderCommonRuleId(String placeId) {
		int ruleId = 3001;
		Optional<BillingRuleCommon> lastRule = billingRuleCommonRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
		if (lastRule.isPresent()) {
			int lastRuleId = Integer.parseInt(lastRule.get().getRuleId());
			ruleId = lastRuleId + 2;
		}
		return String.valueOf(ruleId);
	}

	/**
	 * 查询该场所下，某个7*24小时费率规则信息
	 *
	 * @param placeId
	 * @param ruleId
	 * @return
	 */
	public Optional<BillingRuleCommon> findByPlaceIdAndRuleId(String placeId, String ruleId) {
		return billingRuleCommonRepository.findByPlaceIdAndRuleIdAndDeleted(placeId, ruleId, 0);
	}

	/**
	 * 保存/修改
	 *
	 * @param billingRuleCommon
	 * @return
	 */
	public BillingRuleCommon save(BillingRuleCommon billingRuleCommon) {
		return billingRuleCommonRepository.save(billingRuleCommon);
	}

	/**
	 * 批量 保存/修改
	 *
	 * @param billingRuleCommons
	 * @return
	 */
	public List<BillingRuleCommon> batchSave(List<BillingRuleCommon> billingRuleCommons) {
		return billingRuleCommonRepository.saveAll(billingRuleCommons);
	}

	public List<BillingRuleCommon> findByPlaceIdAndCardTypeIdInAndDeleted(String placeId,List<String> cardTypeIds) {
		return billingRuleCommonRepository.findByPlaceIdAndCardTypeIdInAndDeleted(placeId,cardTypeIds,0);
	}

	/**
	 * 批量 保存/修改
	 *
	 * @param billingRuleCommonBOS
	 * @return
	 */
	public List<BillingRuleCommon> batchSaveCommonBOS(List<BillingRuleCommonBO> billingRuleCommonBOS) {
		List<BillingRuleCommon> billingRuleCommons = new ArrayList<>();
		for (BillingRuleCommonBO bo : billingRuleCommonBOS) {
			// 同一区域、卡类型只有一个普通计费规则
			Optional<BillingRuleCommon> billingRuleCommonOpt =
					billingRuleCommonRepository.findByPlaceIdAndAreaIdAndCardTypeIdAndDeleted(bo.getPlaceId(), bo.getAreaId(), bo.getCardTypeId(), 0);
			if (billingRuleCommonOpt.isPresent()) {
				BillingRuleCommon billingRuleCommon = billingRuleCommonOpt.get();
				billingRuleCommon.setUpdated(LocalDateTime.now());
				billingRuleCommon.setPrices(bo.getPrices());
				billingRuleCommon.setUnitConsume(bo.getUnitConsume() <= 0 ? 100 : bo.getUnitConsume());
				billingRuleCommon.setMinConsume(Math.max(bo.getMinConsume(), 0));
				billingRuleCommon.setDeductionTime(bo.getDeductionTime() <= 0 ? 15 : bo.getUnitConsume());
				billingRuleCommonRepository.save(billingRuleCommon);
				billingRuleCommons.add(billingRuleCommon);
				continue;
			}
			BillingRuleCommon billingRuleCommon = new BillingRuleCommon();
			BeanUtils.copyProperties(bo, billingRuleCommon);
			billingRuleCommon.setCreated(LocalDateTime.now());
			billingRuleCommon.setRuleId(builderCommonRuleId(bo.getPlaceId()));
			billingRuleCommon.setUnitConsume(bo.getUnitConsume() <= 0 ? 100 : bo.getUnitConsume());
			billingRuleCommon.setMinConsume(Math.max(bo.getMinConsume(), 0));
			billingRuleCommon.setDeductionTime(bo.getDeductionTime() <= 0 ? 15 : bo.getUnitConsume());
			billingRuleCommonRepository.save(billingRuleCommon);
			billingRuleCommons.add(billingRuleCommon);
		}
		return billingRuleCommons;
	}

	/**
	 * 删除费率
	 *
	 * @param billingRuleCommon
	 */
	public void delete(BillingRuleCommon billingRuleCommon) {
		billingRuleCommonRepository.delete(billingRuleCommon);
	}

	/**
	 * 生成默认的7*24小时格式费率
	 *
	 * @return
	 */
	public String getDefaultCommonRate() {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < Calendar.DAY_OF_WEEK * (LocalTime.MAX.getHour() + 1); i++) {
			if (i % 24 == 0 && i != 0) {
				sb.deleteCharAt(sb.length() - 1);
				sb.append("_0,");
			} else {
				sb.append("0,");
			}
		}
		return sb.deleteCharAt(sb.length() - 1).toString();
	}

	/**
	 * 龙管家费率格式转新计费费率格式
	 *
	 * @param lgjPrice
	 * @return
	 */
	public String getTransformCommonPrice(String lgjPrice) {
		List<String> strings = Arrays.asList(lgjPrice.split(";"));
		StringBuilder sb = new StringBuilder();
		int count = 0;
		for (String str : strings) {
			if (!StringUtils.isEmpty(str)) {
				float rate = Float.parseFloat(str);
				int price = (int) (rate * 100);
				if (count % 24 == 0 && count != 0) {
					sb.deleteCharAt(sb.length() - 1);
					sb.append("_").append(price == -100 ? "" : price).append(",");
				} else {
					sb.append(price == -100 ? "" : price).append(",");
				}
				count++;
			}
		}
		return sb.deleteCharAt(sb.length() - 1).toString();
	}

	/** 获取场所最小最大费率方法
	 * @param priceStr 标准费率价格字符串
	 * @param most     要返回最大费率还是最小费率,min表示最小费率，max表示最大费率
	 * @return
	 */
	public int getMostPrice(String priceStr, String most) {
		if(StringUtils.isEmpty(priceStr) && !"".equals(priceStr.trim())){
			return 0;
		}
		int minPrice = 10000; // 最小费率
		int maxPrice = 0; // 最大费率
		// 处理跨天的数据，如600_600,把_替换成逗号
		priceStr = priceStr.replace("_", ",");
		List<String> prices = Arrays.asList(priceStr.split(","));
		for (String pri : prices) {
			if(StringUtils.isEmpty(pri)){
				pri = "0";
			}
			int price = Integer.parseInt(pri.trim());
			if (price > maxPrice) {
				maxPrice = price;
			}
			if (price < minPrice && price>=0) {
				minPrice = price;
			}
		}
		if ("min".equals(most)) {
			return minPrice;
		} else {
			return maxPrice;
		}
	}
}