package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingRuleAcc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年12月14日 上午10:55:21
 */
public interface BillingRuleAccRepository
		extends JpaRepository<BillingRuleAcc, Long>, JpaSpecificationExecutor<BillingRuleAcc> {

	Optional<BillingRuleAcc> findByPlaceIdAndRuleIdAndDeleted(String placeId, String ruleId, int deleted);

	Optional<BillingRuleAcc> findTop1ByPlaceIdOrderByIdDesc(String placeId);

	List<BillingRuleAcc> findByPlaceIdAndDeleted(String placeId, int deleted);

	List<BillingRuleAcc> findByPlaceIdAndDeletedOrderByIdDesc(String placeId, int deleted);

}
