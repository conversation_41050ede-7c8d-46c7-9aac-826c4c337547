package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.billing.BookSeatsBO;
import com.rzx.dim4.base.bo.billing.BookSeatsConfigBO;
import com.rzx.dim4.base.bo.billing.BookSeatsCustomerContextBO;
import com.rzx.dim4.base.bo.billing.BookSeatsQueryBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.BookSeats;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 订座服务
 *
 * <AUTHOR>
 * @since 2023/12/6
 **/
public interface BookSeatsService {
    /**
     * 获取订座配置
     *
     * @param placeId 场所ID
     * @return 订座配置
     */
    BookSeatsConfigBO getConfig(String placeId);

    /**
     * 场所订座是否开启
     *
     * @param placeId    场所ID
     * @param sourceType 订座来源
     */
    void checkBookingIsOpening(String placeId, SourceType sourceType);

    /**
     * 更新订座配置
     *
     * @param configBO 订座配置
     */
    void updateConfig(BookSeatsConfigBO configBO);

    /**
     * 获取订座列表分页信息（带筛选）
     *
     * @param queryBO 订座列表筛选条件
     * @return 订座列表
     */
    Page<BookSeatsBO> getListByPlaceId(BookSeatsQueryBO queryBO);

    /**
     * 收银台取消订座
     *
     * @param placeId     场所id
     * @param bookSeatsId 订座记录id
     * @param shiftId     收银台当班人班次id
     */
    BookSeatsBO cashierCancel(String placeId, long bookSeatsId, String shiftId);

    /**
     * 客户端查询是否被预定
     *
     * @param placeId  场所id
     * @param clientId 客户端id
     * @return 订座信息
     */
    BookSeatsBO find(String placeId, String clientId);

    /**
     * 检查用户是否没有订座记录
     *
     * @param placeId  场所id
     * @param idNumber 身份证号
     */
    void noBookingSeats(String placeId, String idNumber);

    /**
     * 客户端是否订座中
     *
     * @param placeId  场所id
     * @param clientId 客户端id
     * @return 是否订座中
     */
    boolean clientUnderBooking(String placeId, String clientId);

    /**
     * 验证解锁码，结束订座，上机
     *
     * @param placeId    场所id
     * @param clientId   客户端id
     * @param unlockCode 解锁码
     * @param idNumber   身份证号
     * @param sourceType 解锁来源
     * @apiNote 1、场所没有开启订座功能，则直接绕过；<br/>
     * 2、场所开启了订座功能，客户端不在订座中，则直接绕过；<br/>
     * 3、场所开启了订座功能，客户端正在订座中，则判断解锁码是否正确。
     */
    void finish(String placeId, String clientId, String unlockCode, String idNumber, SourceType sourceType);


    /**
     * 通过解锁码结束订座
     * @param placeId
     * @param curClientId
     * @param unlockCode
     * @param sourceType
     */
    void finishByUnlockCode(String placeId, String curClientId, String unlockCode, SourceType sourceType);

    /**
     * 用户待订座页面信息
     *
     * @param placeId  场所id
     * @param idNumber 身份证号
     * @return 待订座页面信息
     */
    BookSeatsCustomerContextBO customerContext(String placeId, String idNumber);

    /**
     * 用户执行订座
     *
     * @param bookSeatsBO 订座信息（必填字段：placeId、idNumber、clientIds、areaId、roomFlag、
     *                    duration、ruleId、price、estimatedCost）
     * @return 订座信息
     */
    BookSeatsBO customerCreate(BookSeatsBO bookSeatsBO);

    BookSeatsBO thirdAccountCreate(BookSeatsBO bookSeatsBO, ThirdAccount thirdAccount);

    /**
     * 用户取消订座
     *
     * @param bookSeatsId 订座记录id
     * @param operator    操作人id(billingCard.id/placeAccount.id)
     */
    void customerCancel(long bookSeatsId, long operator);

    /**
     * 用户端获取订座列表
     *
     * @param queryBO 订座列表筛选条件(placeId、idNumber 必填)
     * @return 订座列表
     */
    Page<BookSeatsBO> customerGetList(BookSeatsQueryBO queryBO);

    /**
     * 定时任务，订座中的订座进行扣款/结束订座
     */
    void deductionSchedule();

    /**
     * 第三方账号使用
     *
     * @param placeId
     * @param bookSeatsId
     * @param idNumber
     * @return
     */
    BookSeatsBO thirdAccountCancel(String placeId, long bookSeatsId, String idNumber, ThirdAccount thirdAccount);

    BookSeatsBO order(long id, String idNumber);

    BookSeatsBO order(long id, String idNumber, String cardId);

    void saveAll(List<BookSeats> bookSeats);
}
