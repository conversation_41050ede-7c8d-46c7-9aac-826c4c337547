package com.rzx.dim4.billing.web.controller.support;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.bo.marketing.GoodsBO;
import com.rzx.dim4.base.bo.marketing.GoodsSuppliersBO;
import com.rzx.dim4.base.bo.marketing.RentConfigBO;
import com.rzx.dim4.base.bo.place.*;
import com.rzx.dim4.base.dto.*;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsApi;
import com.rzx.dim4.base.service.feign.marketing.RentApi;
import com.rzx.dim4.base.service.feign.place.PlaceProfileApi;
import com.rzx.dim4.base.utils.BeanCoverUtil;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.HttpClientUtil;
import com.rzx.dim4.billing.bo.LgjBaseDataExportVO;
import com.rzx.dim4.billing.bo.LgjBillingCardDataExportVO;
import com.rzx.dim4.billing.bo.LgjBillingDataExportVO;
import com.rzx.dim4.billing.bo.LgjShopDataExportVO;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.sql.Time;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 从V8系统导入数据
 * <AUTHOR>
 * @date 2025年05月29日 17:57
 */

@RestController
@RequestMapping("/billing/support/import")
@Slf4j
public class LgjDataImportController {

    private static final String signKey = "olJuf50Q9Z3V6Uwh6TPA";

    private static final String billing_card_total_key = "billing:import_V8_card_";
    private static final String billing_card_import_total_key = "billing:import_V8_card_";

//    public static final String host = "http://test-server.4wgj.com";   //测试
    public static final String host = "http://www.4wgj.com";  //生产

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private PlaceProfileApi placeProfileApi;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MarketingGoodsApi marketingGoodsApi;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private BillingCardBlackListService billingCardBlackListService;

    @Autowired
    private BillingRuleCommonService billingRuleCommonService;

    @Autowired
    private BillingRuleAccService billingRuleAccService;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    private BookSeatsService bookSeatsService;

    @Autowired
    private CashierAuthorityService cashierAuthorityService;

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private SurchargeConfigService surchargeConfigService;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private LogRoomService logRoomService;

    @Autowired
    private  BillingOnlineService billingOnlineService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private RentApi rentApi;


    @PostMapping(value = "/importV8Data")
    public GenericResponse<?> importV8Data (@RequestParam("placeId") String placeId,@RequestParam String importType) {
        if(StringUtils.isEmpty(placeId) || StringUtils.isEmpty(importType)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        log.info("导入V8场所 开始 :::{} {}",placeId,importType);
        String importKey = billing_card_import_total_key+placeId;
        String key = billing_card_total_key+placeId;
        try {
            String repetitionSubmitKey = "billing:importV8Data:" + placeId +importType ;
            if (stringRedisTemplate.hasKey(repetitionSubmitKey)) {
                throw new ServiceException(ServiceCodes.FREQUENT_REQUESTS);
            }
            // 30秒内不允许重复提交
            stringRedisTemplate.opsForValue().set(repetitionSubmitKey, repetitionSubmitKey, 5, TimeUnit.SECONDS);

            stringRedisTemplate.opsForValue().set(importKey,"0",30,TimeUnit.MINUTES);
            String url = null;
            String result = null;
            JSONObject jsonObject = null;
            Map map = null;
            String sign = getSign(placeId);
            if(importType.contains("1")){
                url = host + "/billing-server/billing/support/export/exportBaseData?placeId="+ placeId +"&sign="+sign;

                result = HttpClientUtil.doGetRequest(url,15000);
                if(StringUtils.isEmpty(result)){
                    return new GenericResponse<>(ServiceCodes.OPT_ERROR);
                }

                jsonObject = JSONObject.parseObject(result);
                if(!jsonObject.get("code").toString().equals("100000") && !ObjectUtils.isEmpty(jsonObject.get("data"))){
                    return new GenericResponse<>(ServiceCodes.NOT_FOUND);
                }
                map = (Map) jsonObject.get("data");
                LgjBaseDataExportVO lgjBaseDataExportVO = JSONObject.parseObject(map.get("obj") + "", LgjBaseDataExportVO.class);
                LocalDateTime now = LocalDateTime.now();

                GenericResponse<ObjDTO<PlaceProfileBO>> placeByPlaceId = placeProfileApi.findPlaceByPlaceId(placeId);
                if(placeByPlaceId.isResult() && null != placeByPlaceId.getData().getObj()){
                    log.info("导入V8场所 :::{} ,基础信息已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceProfileBO()){
                        PlaceProfileBO placeProfileBO = BeanCoverUtil.cover(lgjBaseDataExportVO.getPlaceProfileBO(), PlaceProfileBO.class);
                        placeProfileBO.setCreated(now);
                        GenericResponse<ObjDTO<PlaceProfileBO>> save = placeProfileApi.save(placeProfileBO);
                        log.info("导入V8场所 :::{} ,基础信息结果:::{}",placeId,new Gson().toJson(save));
                    }
                }

                GenericResponse<ObjDTO<PlaceConfigBO>> configByPlaceId = placeServerService.findPlaceConfigByPlaceId(placeId);
                if(configByPlaceId.isResult() && null != configByPlaceId.getData().getObj()){
                    log.info("导入V8场所 :::{} ,配置信息已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceConfigVO()){
                        PlaceConfigBO placeConfigBO = BeanCoverUtil.cover(lgjBaseDataExportVO.getPlaceConfigVO(), PlaceConfigBO.class);
                        placeConfigBO.setCreated(now);
                        String clientRequestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                        stringRedisTemplate.opsForValue().set(clientRequestTicket, clientRequestTicket, 1, TimeUnit.MINUTES);
                        GenericResponse<ObjDTO<PlaceConfigBO>> objDTOGenericResponse = placeServerService.savePlaceConfig(clientRequestTicket, placeConfigBO);
                        log.info("导入V8场所 :::{} ,配置信息结果:::{}",placeId,new Gson().toJson(objDTOGenericResponse));
                    }
                }


                GenericResponse<ObjDTO<RentConfigBO>> rentConfigByPlaceId = rentApi.findConfigByPlaceId(placeId);
                if(rentConfigByPlaceId.isResult() && null != rentConfigByPlaceId.getData().getObj()){
                    log.info("导入V8场所 :::{} ,设备租赁配置信息已存在",placeId);
                }else{
                        String clientRequestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                        stringRedisTemplate.opsForValue().set(clientRequestTicket, clientRequestTicket, 1, TimeUnit.MINUTES);
                        GenericResponse<ObjDTO<RentConfigBO>> objDTOGenericResponse = rentApi.initRentConfig(clientRequestTicket, placeId);
                        log.info("导入V8场所 :::{} ,设备租赁配置信息结果:::{}",placeId,new Gson().toJson(objDTOGenericResponse));
                }

                GenericResponse<ListDTO<PlaceAccountBO>> accountByPlaceId = placeServerService.findAccountByPlaceId(placeId);
                if(accountByPlaceId.isResult() && accountByPlaceId.getData().getList().size() > 0){
                    log.info("导入V8场所 :::{} ,场所账户已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceAccounts() && 0 < lgjBaseDataExportVO.getPlaceAccounts().size()){
                        List<PlaceAccountBO> accountBOS = new ArrayList<>();
                        for (LgjBaseDataExportVO.PlaceAccountVO placeAccount : lgjBaseDataExportVO.getPlaceAccounts()) {
                            PlaceAccountBO placeAccountBO = BeanCoverUtil.cover(placeAccount, PlaceAccountBO.class);
                            accountBOS.add(placeAccountBO);
                        }
                        String clientRequestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                        stringRedisTemplate.opsForValue().set(clientRequestTicket, clientRequestTicket, 1, TimeUnit.MINUTES);
                        GenericResponse<ListDTO<PlaceAccountBO>> listDTOGenericResponse = placeServerService.batchSavePlaceAccount(clientRequestTicket, accountBOS,"1");
                        log.info("导入V8场所 :::{} ,场所账户信息结果:::{}",placeId,new Gson().toJson(listDTOGenericResponse));
                    }
                }

                GenericResponse<ListDTO<PlaceCashierBO>> queryAllCashiers = placeServerService.queryAllCashiers(placeId);
                if(queryAllCashiers.isResult() && queryAllCashiers.getData().getList().size() > 0){
                    log.info("导入V8场所 :::{} ,收银台信息已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceCashiers() && 0 < lgjBaseDataExportVO.getPlaceCashiers().size()){
                        for (LgjBaseDataExportVO.PlaceCashierVO placeCashier : lgjBaseDataExportVO.getPlaceCashiers()) {
                            PlaceCashierBO placeCashierBO = BeanCoverUtil.cover(placeCashier, PlaceCashierBO.class);
                            placeCashierBO.setCreated(now);
                            GenericResponse<ObjDTO<PlaceCashierBO>> objDTOGenericResponse = placeServerService.saveCashier(placeCashierBO);
                            log.info("导入V8场所 :::{} ,收银台信息结果:::{}",placeId,new Gson().toJson(objDTOGenericResponse));
                        }
                    }
                }

                GenericResponse<ListDTO<PlaceAreaBO>> placeAreaByPlaceId = placeServerService.findPlaceAreaByPlaceId(placeId);
                if(placeAreaByPlaceId.isResult() && placeAreaByPlaceId.getData().getList().size() > 0){
                    log.info("导入V8场所 :::{} ,场所区域信息已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceAreas() && 0 < lgjBaseDataExportVO.getPlaceAreas().size()){
                        List<PlaceAreaBO> areaBOS = new ArrayList<>();
                        for (LgjBaseDataExportVO.PlaceAreaVO placeArea : lgjBaseDataExportVO.getPlaceAreas()) {
                            PlaceAreaBO placeAreaBO = BeanCoverUtil.cover(placeArea,PlaceAreaBO.class);
                            placeAreaBO.setCreated(now);
                            areaBOS.add(placeAreaBO);
                        }
                        String clientRequestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                        stringRedisTemplate.opsForValue().set(clientRequestTicket, clientRequestTicket, 1, TimeUnit.MINUTES);
                        GenericResponse<ListDTO<PlaceAreaBO>> listDTOGenericResponse1 = placeServerService.batchSavePlaceArea(clientRequestTicket,areaBOS);
                        log.info("导入V8场所 :::{} ,场所区域信息结果:::{}",placeId,new Gson().toJson(listDTOGenericResponse1));
                    }
                }

                GenericResponse<ListDTO<PlaceClientBO>> queryAllClients = placeServerService.queryAllClients(placeId);
                if(queryAllClients.isResult() && queryAllClients.getData().getList().size() >0){
                    log.info("导入V8场所 :::{} ,场所客户端信息已存在",placeId);
                }else{
                    if(null != lgjBaseDataExportVO.getPlaceClients() && 0 < lgjBaseDataExportVO.getPlaceClients().size()) {
                        List<PlaceClientBO> clientBOS = new ArrayList<>();
                        for (LgjBaseDataExportVO.PlaceClientVO placeClient : lgjBaseDataExportVO.getPlaceClients()) {
                            PlaceClientBO placeClientBO = BeanCoverUtil.cover(placeClient,PlaceClientBO.class);
                            placeClientBO.setCreated(now);
                            clientBOS.add(placeClientBO);
                        }
                        String clientRequestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                        stringRedisTemplate.opsForValue().set(clientRequestTicket, clientRequestTicket, 1, TimeUnit.MINUTES);
                        GenericResponse<SimpleDTO> genericResponse = placeServerService.batchSaveClient(clientRequestTicket,clientBOS);
                        log.info("导入V8场所 :::{} ,场所客户端信息结果:::{}",placeId,new Gson().toJson(genericResponse));
                    }
                }

                log.info("导入V8场所 :::{} ,基础数据导入完毕",placeId);

                //导入场所计费数据
                url = host + "/billing-server/billing/support/export/exportBillingData?placeId="+ placeId +"&sign="+sign;

                result = HttpClientUtil.doGetRequest(url,15000);
                if(StringUtils.isEmpty(result)){
                    return new GenericResponse<>(ServiceCodes.OPT_ERROR);
                }
                jsonObject = JSONObject.parseObject(result);
                map = (Map) jsonObject.get("data");
                LgjBillingDataExportVO billingDataExportVO = JSONObject.parseObject(map.get("obj") + "", LgjBillingDataExportVO.class);
                //导入场所配置
                try {
                    if(null != billingDataExportVO.getPlaceBizConfigVO()){
                        PlaceBizConfig placeBizConfig = BeanCoverUtil.cover(billingDataExportVO.getPlaceBizConfigVO(), PlaceBizConfig.class);
                        placeBizConfig.setPlaceId(placeId);
                        placeBizConfig.setCreated(now);
                        placeBizConfig.setConstraintSubscribe(0);
                        placeBizConfig.setClientLoginMethod("0,1");
                        placeBizConfig.setCertificateClient("CASHIER,CLIENT,WECHAT,WECHATAPP");
                        placeBizConfig.setDefCardType("1000");
                        PlaceBizConfig save = placeBizConfigService.save(placeBizConfig);
                        log.info("导入V8场所 :::{} ,场所计费配置信息结果:::{}",placeId,new Gson().toJson(save));
                    }
                }catch (Exception e){
                    log.info("导入V8场所 :::{} ,场所计费配置信息已存在",placeId);
                    e.printStackTrace();
                }

                //导入附加费配置
                Optional<SurchargeConfig> byPlaceId = surchargeConfigService.findByPlaceId(placeId);
                if(byPlaceId.isPresent()){
                    log.info("导入V8场所 :::{} ,场所附加费配置信息已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getSurchargeConfigVO()){
                        SurchargeConfig surchargeConfig = BeanCoverUtil.cover(billingDataExportVO.getSurchargeConfigVO(), SurchargeConfig.class);
                        surchargeConfig.setPlaceId(placeId);
                        surchargeConfig.setCreated(now);
                        SurchargeConfig save = surchargeConfigService.save(surchargeConfig);
                        log.info("导入V8场所 :::{} ,场所附加费配置信息结果:::{}",placeId,new Gson().toJson(save));
                    }
                }


                if(null != billingDataExportVO.getBillingCardTypeVOS() && billingDataExportVO.getBillingCardTypeVOS().size() > 0){
                    List<BillingCardTypeBO> cardTypeList = new ArrayList<>();
                    for (LgjBillingDataExportVO.BillingCardTypeVO billingCardTypeVO : billingDataExportVO.getBillingCardTypeVOS()) {
                        BillingCardTypeBO billingCardType = BeanCoverUtil.cover(billingCardTypeVO, BillingCardTypeBO.class);
                        billingCardType.setPlaceId(placeId);
                        cardTypeList.add(billingCardType);
                    }
                    List<BillingCardType> cardTypeList1 = billingCardTypeService.batchSave(cardTypeList);
                    log.info("导入V8场所 :::{} ,场所卡类型配置信息结果:::{}",placeId,new Gson().toJson(cardTypeList1));
                }

                if(null != billingDataExportVO.getBillingCardBlackListVOS() && billingDataExportVO.getBillingCardBlackListVOS().size() > 0){
                    for (LgjBillingDataExportVO.BillingCardBlackListVO billingCardBlackListVO : billingDataExportVO.getBillingCardBlackListVOS()) {
                        BillingCardBlackList billingCardBlackList = BeanCoverUtil.cover(billingCardBlackListVO, BillingCardBlackList.class);
                        billingCardBlackList.setPlaceId(placeId);
                        billingCardBlackList.setCreated(now);
                        BillingCardBlackList save = billingCardBlackListService.save(billingCardBlackList);
                        log.info("导入V8场所 :::{} ,新增场所卡黑名单:::{}",placeId,new Gson().toJson(save));
                    }
                }
                List<BillingRuleCommon> byPlaceId1 = billingRuleCommonService.findByPlaceId(placeId);
                if(byPlaceId1.size() > 0){
                    log.info("导入V8场所 :::{} ,场所基础费率信息已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getBillingRuleCommonVOS() && billingDataExportVO.getBillingRuleCommonVOS().size() > 0){
                        List<LgjBillingDataExportVO.BillingRuleCommonVO> billingRuleCommonVOS = billingDataExportVO.getBillingRuleCommonVOS();
                        billingRuleCommonVOS = billingRuleCommonVOS.stream().sorted(Comparator.comparing(LgjBillingDataExportVO.BillingRuleCommonVO::getRuleId)).collect(Collectors.toList());
                        List<BillingRuleCommon> billingRuleCommons = new ArrayList<>();
                        for (LgjBillingDataExportVO.BillingRuleCommonVO billingRuleCommonVO : billingRuleCommonVOS) {
                            BillingRuleCommon billingRuleCommon = BeanCoverUtil.cover(billingRuleCommonVO, BillingRuleCommon.class);
                            billingRuleCommon.setPlaceId(placeId);
                            billingRuleCommon.setDeductionTime(15);
                            billingRuleCommon.setCreated(now);
                            billingRuleCommons.add(billingRuleCommon);
                        }
                        List<BillingRuleCommon> billingRuleCommons1 = billingRuleCommonService.batchSave(billingRuleCommons);
                        log.info("导入V8场所 :::{} ,场所基础费率信息结果:::{}",placeId,new Gson().toJson(billingRuleCommons1));
                    }
                }


//            if(null != billingDataExportVO.getBillingRuleAccVOS() && billingDataExportVO.getBillingRuleAccVOS().size() > 0){
//                for (LgjBillingDataExportVO.BillingRuleAccVO billingRuleAccVO : billingDataExportVO.getBillingRuleAccVOS()) {
//                    BillingRuleAcc billingRuleAcc = BeanCoverUtil.cover(billingRuleAccVO, BillingRuleAcc.class);
//                    billingRuleAcc.setPlaceId(placeId);
//                    billingRuleAcc.setCreated(now);
//                    BillingRuleAcc save = billingRuleAccService.save(billingRuleAcc);
//                    log.info("导入V8场所 :::{} ,新增自动累加计费规则:::{}",placeId,new Gson().toJson(save));
//                }
//            }
                List<BillingRulePackageTime> byPlaceIdOrderByIdDesc = billingRulePackageTimeService.findByPlaceIdOrderByIdDesc(placeId);
                if(byPlaceIdOrderByIdDesc.size() > 0){
                    log.info("导入V8场所 :::{} ,包时信息已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getBillingRulePackageVOS() && billingDataExportVO.getBillingRulePackageVOS().size() > 0){
                        List<LgjBillingDataExportVO.BillingRulePackageVO> billingRulePackageVOS = billingDataExportVO.getBillingRulePackageVOS();
                        billingRulePackageVOS = billingRulePackageVOS.stream().sorted(Comparator.comparing(LgjBillingDataExportVO.BillingRulePackageVO::getRuleId)).collect(Collectors.toList());

                        for (LgjBillingDataExportVO.BillingRulePackageVO billingRulePackageVO : billingRulePackageVOS) {
                            BillingRulePackageTime billingRulePackageTime = BeanCoverUtil.cover(billingRulePackageVO, BillingRulePackageTime.class);
                            billingRulePackageTime.setPlaceId(placeId);
                            billingRulePackageTime.setCreated(now);
                            billingRulePackageTime.setDurationTime((int) (billingRulePackageVO.getDurationTime() * 60)); //转换为分钟
                            billingRulePackageTime.setCouponRule(0);
                            billingRulePackageTime.setLimitDurationEndTime(Time.valueOf("00:00:00"));
                            billingRulePackageTime.setDurationRepeatedTime(0);
                            billingRulePackageTime.setSalesTimeType(1);
                            billingRulePackageTime.setCustomDate(null);
                            billingRulePackageTime.setSalesStartTime(Time.valueOf("00:00:00"));
                            billingRulePackageTime.setSalesEndTime(Time.valueOf("23:59:59"));
                            billingRulePackageTime.setLimitSalesType(1);
                            billingRulePackageTime.setLimitSalesNumber(0);
                            billingRulePackageTime.setLimitLoginBuy(0);
                            billingRulePackageTime.setRuleEndLogoutInvalidFlag(0);
                            billingRulePackageTime.setRuleEndLogoutFlag(0);
                            billingRulePackageTime.setLimitCrossAreaExchange(0);
                            billingRulePackageTime.setRulePayType("0,1,2");
                            billingRulePackageTime.setForbidden(0);
                            billingRulePackageTime.setLimitSalesTerminal("CLIENT,CASHIER,WECHAT");
                            BillingRulePackageTime save = billingRulePackageTimeService.save(billingRulePackageTime);
                            log.info("导入V8场所 :::{} ,新增包时规则:::{}",placeId,new Gson().toJson(save));

                            //保存一个goods对象
                            GoodsBO goods = new GoodsBO();
                            goods.setPackageRuleId(save.getRuleId());
                            goods.setPlaceId(save.getPlaceId());
                            goods.setGoodsName(save.getRuleName());
                            goods.setGoodsTypeId("");
                            goods.setGoodsTypeName("默认分类");
                            goods.setUnitPrice(save.getPrice());
                            goods.setUnit(9);//张
                            goods.setGoodsCategory(4);//商品类型优惠券
                            goods.setSellStatus(1);//停售
                            goods.setCycle(0);//售卖周期默认每日
                            goods.setStartTime1(0);
                            goods.setEndTime1(24);
                            goods.setShowMobileSwitch(1);
                            goods.setShowClientSwitch(1);
                            goods.setShowCashierSwitch(1);
                            goods.setOnlinePaySwitch(1);
                            goods.setShowRank(1);
                            goods.setCreated(save.getCreated());
                            goods.setCreater(save.getCreater());
                            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
                            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
                            marketingGoodsApi.saveGoods(requestTicket2,goods);
                        }
                    }
                }

                List<CashierAuthority> byPlaceId2 = cashierAuthorityService.findByPlaceId(placeId);
                if(byPlaceId2.size() > 0){
                    log.info("导入V8场所 :::{} ,收银台权限已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getCashierAuthorityVOS() && billingDataExportVO.getCashierAuthorityVOS().size() > 0){
                        for (LgjBillingDataExportVO.CashierAuthorityVO cashierAuthorityVO : billingDataExportVO.getCashierAuthorityVOS()) {
                            CashierAuthority cashierAuthority = BeanCoverUtil.cover(cashierAuthorityVO, CashierAuthority.class);
                            cashierAuthority.setPlaceId(placeId);
                            cashierAuthority.setCreated(now);
                            CashierAuthority save = cashierAuthorityService.save(cashierAuthority);
                            log.info("导入V8场所 :::{} ,新增收银台权限:::{}",placeId,new Gson().toJson(save));
                        }
                    }
                }


                if(null != billingDataExportVO.getBookSeatsVOS() && billingDataExportVO.getBookSeatsVOS().size() > 0){
                    List<BookSeats> bookSeats = new ArrayList<>();
                    for (LgjBillingDataExportVO.BookSeatsVO bookSeatsVO : billingDataExportVO.getBookSeatsVOS()) {
                        BookSeats bookSeats1 = BeanCoverUtil.cover(bookSeatsVO, BookSeats.class);
                        bookSeats1.setPlaceId(placeId);
                        bookSeats1.setCreated(now);
                        bookSeats.add(bookSeats1);
                    }
                    bookSeatsService.saveAll(bookSeats);
                    log.info("导入V8场所 :::{} ,新增订座记录:::{}",placeId,new Gson().toJson(bookSeats));
                }

                Pageable pageable = PageRequest.of(0, 1, Sort.Direction.fromString("desc"), "created");
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("placeId", placeId);
                Page<LogTopup> all = logTopupService.findAll(queryMap, pageable);
                if(all.getTotalElements() > 0){
                    log.info("导入V8场所 :::{} ,充值记录已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getLogTopupVOS() && billingDataExportVO.getLogTopupVOS().size() > 0){
                        for (LgjBillingDataExportVO.logTopupVO logTopupVO : billingDataExportVO.getLogTopupVOS()) {
                            LogTopup logTopup = BeanCoverUtil.cover(logTopupVO, LogTopup.class);
                            logTopup.setPlaceId(placeId);
                            logTopup.setCreated(now);
                            LogTopup save = logTopupService.save(logTopup);
                            log.info("导入V8场所 :::{} ,新增充值记录:::{}",placeId,new Gson().toJson(save));
                        }
                    }
                }

                List<PackageTimeReserve> byPlaceIdAndStatus = packageTimeReserveService.findByPlaceIdAndStatus(placeId);
                if(byPlaceIdAndStatus.size() > 0){
                    log.info("导入V8场所 :::{} ,预包时记录已存在",placeId);
                }else{
                    if(null != billingDataExportVO.getPackageTimeReserveVOS() && billingDataExportVO.getPackageTimeReserveVOS().size() > 0){
                        for (LgjBillingDataExportVO.PackageTimeReserveVO packageTimeReserveVO : billingDataExportVO.getPackageTimeReserveVOS()) {
                            PackageTimeReserve packageTimeReserve = BeanCoverUtil.cover(packageTimeReserveVO, PackageTimeReserve.class);
                            packageTimeReserve.setPlaceId(placeId);
                            packageTimeReserve.setCreated(now);
                            PackageTimeReserve save = packageTimeReserveService.save(packageTimeReserve);
                            log.info("导入V8场所 :::{} ,新增预包时记录:::{}",placeId,new Gson().toJson(save));
                        }
                    }
                }

//            if(null != billingDataExportVO.getLogRoomVOS() && billingDataExportVO.getLogRoomVOS().size() > 0){
//                for (LgjBillingDataExportVO.LogRoomVO logRoomVO : billingDataExportVO.getLogRoomVOS()) {
//                    LogRoom logRoom = BeanCoverUtil.cover(logRoomVO, LogRoom.class);
//                    logRoom.setPlaceId(placeId);
//                    logRoom.setCreated(now);
//                    LogRoom save = logRoomService.save(logRoom);
//                    log.info("导入V8场所 :::{} ,新增包间上机信息:::{}",placeId,new Gson().toJson(save));
//                }
//            }

//            if(null != billingDataExportVO.getBillingOnlineVOS() && billingDataExportVO.getBillingOnlineVOS().size() > 0){
//                for (LgjBillingDataExportVO.BillingOnlineVO billingOnlineVO : billingDataExportVO.getBillingOnlineVOS()) {
//                    BillingOnline billingOnline = BeanCoverUtil.cover(billingOnlineVO, BillingOnline.class);
//                    billingOnline.setPlaceId(placeId);
//                    billingOnline.setCreated(billingOnline.getBillingTime());
//                    billingOnline.setIsInvite(0);
//                    BillingOnline save = billingOnlineService.save(billingOnline);
//                    log.info("导入V8场所 :::{} ,新增在线信息:::{}",placeId,new Gson().toJson(save));
//                }
//            }

//            if(null != billingDataExportVO.getLogLoginVOS() && billingDataExportVO.getLogLoginVOS().size() > 0){
//                for (LgjBillingDataExportVO.LogLoginVO logLoginVO : billingDataExportVO.getLogLoginVOS()) {
//                    LogLogin logLogin = BeanCoverUtil.cover(logLoginVO, LogLogin.class);
//                    logLogin.setPlaceId(placeId);
//                    logLogin.setCreated(logLogin.getLoginTime());
//                    LogLogin save = logLoginService.save(logLogin);
//                    log.info("导入V8场所 :::{} ,新增上机信息:::{}",placeId,new Gson().toJson(save));
//                }
//            }
                log.info("导入V8场所 :::{} ,计费相关导入完毕",placeId);
            }else{
                log.info("导入V8场所，跳过基础信息导入 :::{}",placeId);
            }

            if(importType.contains("2")){
                //导入场所营销数据
                url = host + "/billing-server/billing/support/export/exportShopData?placeId="+ placeId +"&sign="+sign;

                result = HttpClientUtil.doGetRequest(url,15000);
                if(StringUtils.isEmpty(result)){
                    return new GenericResponse<>(ServiceCodes.OPT_ERROR);
                }
                jsonObject = JSONObject.parseObject(result);
                map = (Map) jsonObject.get("data");
                LgjShopDataExportVO lgjShopDataExportVO = JSONObject.parseObject(map.get("obj") + "", LgjShopDataExportVO.class);
                if(null != lgjShopDataExportVO.getSuppliersVOS() && 0 < lgjShopDataExportVO.getSuppliersVOS().size()) {
                    List<GoodsSuppliersBO> goodsSuppliersBOS = new ArrayList<>();
                    for (LgjShopDataExportVO.SuppliersVO suppliersVO : lgjShopDataExportVO.getSuppliersVOS()) {
                        GoodsSuppliersBO suppliersBO = new GoodsSuppliersBO();
                        suppliersBO.setPlaceId(suppliersVO.getPlaceId());
                        suppliersBO.setSupplierId(suppliersVO.getSupplierId());
                        suppliersBO.setSupplierName(suppliersVO.getSupplierName());
                        suppliersBO.setSupplierPhone(suppliersVO.getContactsPhone());
                        goodsSuppliersBOS.add(suppliersBO);
                    }
                    GenericResponse<ListDTO<GoodsSuppliersBO>> listDTOGenericResponse = marketingGoodsApi.batchSaveSuppliers(goodsSuppliersBOS);
                    log.info("导入V8场所 :::{} ,场所供应商信息结果:::{}",placeId,new Gson().toJson(listDTOGenericResponse));
                }

                if(null != lgjShopDataExportVO.getStorageGoodsVOS() && 0 < lgjShopDataExportVO.getStorageGoodsVOS().size()) {
                    List<GoodsBO> goodsBOS = new ArrayList<>();
                    for (LgjShopDataExportVO.StorageGoodsVO storageGoodsVO : lgjShopDataExportVO.getStorageGoodsVOS()) {
                        GoodsBO goodsBO = new GoodsBO();
                        goodsBO.setPlaceId(storageGoodsVO.getPlaceId());
                        goodsBO.setGoodsId(storageGoodsVO.getGoodsId());
                        goodsBO.setGoodsTypeId(storageGoodsVO.getGoodsTypeId());
                        goodsBO.setGoodsTypeName(storageGoodsVO.getGoodsTypeName());
                        goodsBO.setGoodsName(storageGoodsVO.getGoodsName());
                        goodsBO.setUnitPrice(storageGoodsVO.getUnitPrice());
                        goodsBO.setGoodsStocksNum(storageGoodsVO.getRackGoodsStocks()); //收银台库存
                        goodsBO.setMainGoodsStocksNum(storageGoodsVO.getGoodsStocks()); //仓库库存
                        goodsBO.setGoodsPic(storageGoodsVO.getGoodsPic());
                        goodsBO.setBarcode(storageGoodsVO.getGoodsBarcode());
                        goodsBOS.add(goodsBO);
                    }
                    GenericResponse<ListDTO<GoodsBO>> listDTOGenericResponse = marketingGoodsApi.batchSaveGoods(goodsBOS);
                    log.info("导入V8场所 :::{} ,场所商品信息结果:::{}",placeId,listDTOGenericResponse.getMessage());
                }
                log.info("导入V8场所 :::{} ,商超导入完毕",placeId);
            }else{
                log.info("导入V8场所，跳过商品信息导入 :::{}",placeId);
            }

            if(importType.contains("3")){
                int size = 2000;
                int total = 0;
                for (int i = 0; i < 30; i++) {
                    url = host + "/billing-server/billing/support/export/exportBillinCardData?placeId="+ placeId +"&size="+size+"&page="+i;
                    result = HttpClientUtil.doGetRequest(url);
                    if(StringUtils.isEmpty(result)){
                        log.info("导入V8场所 {} 查询会员卡信息结果为null",placeId);
                        break;
                    }
                    jsonObject = JSONObject.parseObject(result);
                    map = (Map) jsonObject.get("data");
                    List<LgjBillingCardDataExportVO> billingCardDataExportVOS = JSONObject.parseArray(map.get("list") + "", LgjBillingCardDataExportVO.class);
                    if(billingCardDataExportVOS.size() == 0){
                        log.info("导入V8场所 {} 查询会员卡信息结束，总数目为::: {}",placeId,total);
                        break;
                    }
                    List<BillingCard> billingCards = BeanCoverUtil.converList(billingCardDataExportVOS,BillingCard.class);

                    Optional<BillingCard> byPlaceIdAndIdNumber = billingCardService.findByPlaceIdAndIdNumber(placeId, billingCards.get(0).getIdNumber());
                    if(byPlaceIdAndIdNumber.isPresent()){
                        log.info("导入V8场所 {} 查询会员卡信息已存在",placeId);
                        continue;
                    }

                    billingCards.stream().forEach(it-> it.setCreated(LocalDateTime.now()));
                    billingCardService.saveAll(billingCards);
                    log.info("导入V8场所 :::{} ,成功导入数据 {}条，总共已导入: {}",placeId,billingCards.size(),total);
                    total += billingCardDataExportVOS.size();
                    String redisVal = stringRedisTemplate.opsForValue().get(key);
                    if(StringUtils.isEmpty(redisVal)){
                        log.info("导入V8场所 :::{} 会员数据合计:::{} 条",map.get("total")+"");
                        stringRedisTemplate.opsForValue().set(key,map.get("total")+"",30,TimeUnit.MINUTES);
                    }
                    stringRedisTemplate.opsForValue().set(importKey,total+"",30,TimeUnit.MINUTES);
                }
            }else{
                stringRedisTemplate.opsForValue().set(importKey,"9999",30,TimeUnit.MINUTES);
                stringRedisTemplate.opsForValue().set(key,"9999",30,TimeUnit.MINUTES);
                log.info("导入V8场所，跳过会员导入 :::{}",placeId);
            }
            log.info("导入V8场所 结束，导入成功 :::{}",placeId);
        }catch (Exception e){
            stringRedisTemplate.delete(importKey);
            stringRedisTemplate.delete(key);
            log.info("导入V8场所 结束，导入数据异常 :::{}",placeId);
            e.printStackTrace();
            return new GenericResponse<>(ServiceCodes.OPT_ERROR);
        }
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    @GetMapping("/findImportResult")
    public GenericResponse<SimpleObjDTO> findImportResult(@RequestParam String placeId){
        String key = billing_card_total_key+placeId;
        String importKey = billing_card_import_total_key+placeId;

        String importTotal = stringRedisTemplate.opsForValue().get(importKey);
        if(StringUtils.isEmpty(importTotal)){
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }else{
            String total = stringRedisTemplate.opsForValue().get(key);

            Map<String,Integer> result = new HashMap<>();
            result.put("success",Integer.valueOf(importTotal));
            if(StringUtils.isEmpty(total)){
                total = "9999";
            }
            result.put("total",Integer.valueOf(total));

            return new GenericResponse<>(new SimpleObjDTO(result));
        }
    }

    public String getSign(String placeId){
        StringBuffer toSignSB = new StringBuffer();
        toSignSB.append("placeId=").append(placeId).append("&");
        toSignSB.append(signKey);
        String toSign = DigestUtils.md5DigestAsHex(toSignSB.toString().getBytes());
        return toSign;
    }

}
