package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.bo.regcard.RegLogBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.RegcardServerService;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.LogShiftService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 收银台查询注册卡已使用数据统计接口
 *
 * 使用时间:前端默认是一个月；如果选择了班次的话，该参数是空的
 * 班次：如果选择了班次，那么查询条件就只有shiftId,而不加时间参数
 */
@Service
@Slf4j
public class CashierQueryRegcardUsedStatisticsServiceImpl implements CoreService {

	@Autowired
	RegcardServerService regcardServerService;

	@Autowired
	PlaceServerService placeServerService;

	@Autowired
	LogShiftService logShiftService;

	@Autowired
	LogOperationService logOperationService;


	@Override
	public GenericResponse<?> doService(List<String> params) {
		if (params.size() != 10 || StringUtils.isEmpty(params.get(1))) {
			return new GenericResponse<>(ServiceCodes.NULL_PARAM);
		}
		String placeId = params.get(0);//场所ID
		String cashierId = params.get(1);//收银台ID
		String cardNumber = params.get(2);// 注册卡ID
		String cardType = params.get(3);// 注册卡类型：31是1月卡；91是3月卡，对应valid_days
		String idNumber = params.get(4);// 绑定证件号
		String idName = params.get(5);//绑定姓名
		String mobile = params.get(6);// 绑定手机号
		String shiftMark = params.get(7);// 班次标识：currentShift 当班；lastShift 上一班
		String startTime = params.get(8);//开始时间
		String endTime = params.get(9);//结束时间

		DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		// 校验日期格式
		try {
			if (!StringUtils.isEmpty(startTime)) {
				LocalDateTime.parse(startTime, fmt);
			}
			if (!StringUtils.isEmpty(endTime)) {
				LocalDateTime.parse(endTime, fmt);
			}

		} catch (Exception e) {
			log.error("日期格式转换错误", e);
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		String shiftId = "";

		// 获取场所配置信息
		GenericResponse<ObjDTO<PlaceConfigBO>> respConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
		if (respConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return new GenericResponse<>(ServiceCodes.BILLING_PLACE_CONFIG_NOT_FOUND);
		}
		PlaceConfigBO placeConfigBO = respConfig.getData().getObj();
		if (placeConfigBO.getRegcard() == 0) {
			return new GenericResponse<>(ServiceCodes.REGCARD_DISABLED);
		}

		GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
		if (respProfile.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return new GenericResponse<>(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
		}
		PlaceProfileBO placeProfileBO = respProfile.getData().getObj();
		if (StringUtils.isEmpty(placeProfileBO.getAuditId())) {
			return new GenericResponse<>(ServiceCodes.REGCARD_CONFIG_ERROR);
		}
		List<RegLogBO> RegcardBOS = new ArrayList<>();

		// 如果选择了班次
		if (!StringUtils.isEmpty(shiftMark)){
			if ("currentShift".equals(shiftMark)){
				Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndCashierIdAndStatus(placeId, cashierId,0);
				if (!optLogShift.isPresent()) {
					return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
				}
				shiftId = optLogShift.get().getShiftId();
			}else {
				Optional<LogShift> optLogShift = logShiftService.findTop1ByPlaceIdAndCashierIdAndStatusOrderByIdDesc(placeId, cashierId,1);
				if (!optLogShift.isPresent()) {
					return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
				}
				shiftId = optLogShift.get().getShiftId();
			}
		}

		GenericResponse<ListDTO<RegLogBO>> regLogResponse = regcardServerService.QueryRegcardUsedByParam(placeId,cardNumber,cardType,idNumber,idName,mobile,shiftId,startTime,endTime);
		int oneMonthCardUsedNum = 0;//一月卡已使用数量（张）
		int threeMonthUsedCardNum = 0;//三月卡已使用数量（张）
		int twoYearUsedCardNum = 0; // 2年卡已使用数量(张)
		int salesAmount = 0;//销售总金额
		MemberStatsBO memberStatsBO = new MemberStatsBO();
		if (!ObjectUtils.isEmpty(regLogResponse.getData()) && regLogResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
			RegcardBOS = regLogResponse.getData().getList();
			for (RegLogBO regCardUsed : RegcardBOS) {
				if (regCardUsed.getValidDays() == 31) {
					oneMonthCardUsedNum++;
					salesAmount += regCardUsed.getPrice();
				} else if (regCardUsed.getValidDays() == 91) {
					threeMonthUsedCardNum++;
					salesAmount += regCardUsed.getPrice();
				} else if (regCardUsed.getValidDays() == 730) {
					twoYearUsedCardNum++;
					salesAmount += regCardUsed.getPrice();
				}
			}
			memberStatsBO.setUsedNum(RegcardBOS.size());
			memberStatsBO.setOneMonthCardUsedNum(oneMonthCardUsedNum);
			memberStatsBO.setThreeMonthUsedCardNum(threeMonthUsedCardNum);
			memberStatsBO.setTwoYearUsedCardNum(twoYearUsedCardNum);
			memberStatsBO.setSalesAmount(salesAmount);
			return new GenericResponse<>(new ObjDTO<>(memberStatsBO));
		}
		return new GenericResponse<>(new ObjDTO<>(new MemberStatsBO(0, 0, 0, 0, 0)));
	}

	@Getter
	@Setter
	class MemberStatsBO extends AbstractEntityBO {

		private int usedNum;//已使用数（张）
		private int oneMonthCardUsedNum;//一月卡已使用数量（张）
		private int threeMonthUsedCardNum;//三月卡已使用数量（张）
		private int twoYearUsedCardNum;//2年卡已使用数量（张）
		private int salesAmount;//销售总金额

		public MemberStatsBO() {
		}

		public MemberStatsBO(int usedNum, int oneMonthCardUsedNum, int threeMonthUsedCardNum, int twoYearUsedCardNum, int salesAmount) {
			this.usedNum = usedNum;
			this.oneMonthCardUsedNum = oneMonthCardUsedNum;
			this.threeMonthUsedCardNum = threeMonthUsedCardNum;
			this.twoYearUsedCardNum = twoYearUsedCardNum;
			this.salesAmount = salesAmount;
		}
	}
}
