package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.LogMemberSigned;
import com.rzx.dim4.billing.repository.LogMemberSignedRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 会员签到service
 */
@Service
public class LogMemberSignedService {

    @Autowired
    LogMemberSignedRepository logMemberSignedRepository;

    /**
     * 场所-卡id-签到日期 查出当月的签到记录信息
     * @param placeId
     * @param cardId
     * @param signedDate
     * @return
     */
    public Optional<LogMemberSigned> findByPlaceIdAndCardIdAndSignedDate (String placeId, String cardId, String signedDate) {
        return logMemberSignedRepository.findByPlaceIdAndCardIdAndSignedDate(placeId, cardId, signedDate);
    }

    /**
     * 场所-卡id-签到日期 查出多个签到记录信息
     * @param placeId
     * @param cardId
     * @param signedDate
     * @return
     */
    public List<LogMemberSigned> findByPlaceIdAndCardIdAndSignedDateIn (String placeId, String cardId, String signedDate) {
        if (StringUtils.isEmpty(signedDate) || StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardId)) {
            return new ArrayList<>();
        }
        List<String> signedDateList = Arrays.asList(signedDate.split(","));
        return logMemberSignedRepository.findByPlaceIdAndCardIdAndSignedDateInOrderByIdDesc(placeId,cardId,signedDateList);
    }

    /**
     * 签到记录--保存
     * @param logMemberSigned
     * @return
     */
    public LogMemberSigned save (LogMemberSigned logMemberSigned) {
        return logMemberSignedRepository.save(logMemberSigned);
    }

    /**
     * 获取当前的日期数,(2023-4)
     * @return
     */
    public String getSignedDate () {
        LocalDateTime now = LocalDateTime.now();
        StringBuilder sb = new StringBuilder();
        sb.append(now.getYear()).append("-").append(now.getMonthValue());
        return sb.toString();
    }

    /**
     * 检验当月签到是否包含当天
     * @param signedDay
     * @return
     */
    public boolean checkSigned (String signedDay) {
        int day = LocalDateTime.now().getDayOfMonth();
        if (signedDay.contains(",")) {
            List<String> list = Arrays.asList(signedDay.split(","));
            for (String str : list) {
                if (str.equals(String.valueOf(day))) {
                    return false;
                }
            }
            return true;
        }
        return signedDay.equals(String.valueOf(day)) ? false : true;
    }

    /**
     * 根据原来的签到次数-日期  获取当前最新的签到次数-日期
     * @param oldCountSignedOfLastDay
     * @return
     */
    public String getCountSignedOfLastDay (String oldCountSignedOfLastDay, String oldSignDate) {
        if (oldCountSignedOfLastDay.contains("-") && oldSignDate.contains("-")) {
            int count = Integer.parseInt(oldCountSignedOfLastDay.split("-")[0]);
            int day = Integer.parseInt(oldCountSignedOfLastDay.split("-")[1]);

            int year = Integer.parseInt(oldSignDate.split("-")[0]);
            int month = Integer.parseInt(oldSignDate.split("-")[1]);

            // 如果最近签到的天数等于昨天，那原来的次数就+1
            LocalDate oldSignedDate = LocalDate.of(year, month, day);
            if (oldSignedDate.plusDays(1).equals(LocalDate.now())) {
                count++;
            } else {
                count = 1;
            }
            StringBuilder sb = new StringBuilder();
            sb.append(count).append("-").append(LocalDateTime.now().getDayOfMonth());
            return sb.toString();
        }
        return oldCountSignedOfLastDay;
    }

    /**
     * 根据签到次数--日期  获取当前最新的签到次数
     * @param countSignedOfLastDay
     * @return
     */
    public int getSignedCount (String countSignedOfLastDay) {
        if (countSignedOfLastDay.contains("-")) {
            int count = Integer.parseInt(countSignedOfLastDay.split("-")[0]);
            int day = Integer.parseInt(countSignedOfLastDay.split("-")[1]);

            int lastDay = LocalDateTime.now().minusDays(1).getDayOfMonth(); // 昨天
            int nowDay = LocalDateTime.now().getDayOfMonth(); // 当天

            // 最近一条签到日期等于今天或者昨天,则连续签到没有中断
            if (day == nowDay || day == lastDay) {
                return count;
            }
        }
        return 0;
    }

}
