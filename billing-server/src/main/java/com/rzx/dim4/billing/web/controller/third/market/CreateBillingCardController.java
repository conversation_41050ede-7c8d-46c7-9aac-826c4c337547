package com.rzx.dim4.billing.web.controller.third.market;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.notify.polling.ActiveBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.IdAuthRequest;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.third.MarketService;
import com.rzx.dim4.billing.service.third.ThirdAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @see com.rzx.dim4.billing.service.impl.third.v2.ThirdCreateBillingCardServiceImpl#doService(List)
 */
@Slf4j
@RestController
@RequestMapping("/third/market")
public class CreateBillingCardController extends ThirdMarketParent {

    @Autowired
    private ThirdAccountService thirdAccountService;

    @Autowired
    private IBillingCardService iBillingCardService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private MarketService marketService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private PlaceServerService placeServerService;

    @Value("${dim4.host.domain}")
    private String dim4Host;

    @Autowired
    private LogActivateService logActivateService;


    /**
     * 开卡
     *
     * @param thirdAccountId   接入方账号Id
     * @param placeId          场所Id
     * @param cardTypeId       卡类型Id
     * @param idNumber         身份证号码
     * @param name             姓名
     * @param identification   扣取附加费标识
     * @param address          地址
     * @param issuingAuthority 发证机关
     * @param nation           民族
     * @param phoneNumber      手机号码
     * @param validPeriod      身份证有效期
     * @param timestamp        时间戳
     * @param sign             签名
     * @return
     */
    @PostMapping("/createCard")
    public GenericResponse<SimpleDTO> createCard(@RequestParam String thirdAccountId,
                                                 @RequestParam String placeId,
                                                 @RequestParam String cardTypeId,
                                                 @RequestParam String idNumber,
                                                 @RequestParam String name,
                                                 @RequestParam(name = "identification", required = false, defaultValue = "") String identification,
                                                 @RequestParam(name = "cashierId", required = false, defaultValue = "") String cashierId,
                                                 @RequestParam(name = "address", required = false) String address,
                                                 @RequestParam(name = "issuingAuthority", required = false) String issuingAuthority,
                                                 @RequestParam(name = "nation", required = false) String nation,
                                                 @RequestParam(name = "phoneNumber", required = false) String phoneNumber,
                                                 @RequestParam(name = "validPeriod", required = false) String validPeriod,
                                                 @RequestParam String timestamp,
                                                 @RequestParam String sign) throws Exception {

        // 必填项校验
        if (StringUtils.isEmpty(thirdAccountId) || StringUtils.isEmpty(placeId) ||
                StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(sign) ||
                StringUtils.isEmpty(cardTypeId) || StringUtils.isEmpty(idNumber) ||
                StringUtils.isEmpty(name)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        // 临时卡不能开卡
//        if ("1000".equals(cardTypeId)) {
//            return new GenericResponse<>(ServiceCodes.BILLING_TEMP_CARD_NOT_SUPPORT);
//        }

        // 获取秘钥
        Optional<ThirdAccount> thirdAccountOpt = thirdAccountService.findByThirdAccountId(thirdAccountId);
        if (!thirdAccountOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.ACCOUNT_ERROR);
        }
        ThirdAccount thirdAccount = thirdAccountOpt.get();

        // 验证身份证合法性
        boolean flag = IdNumberValidator.verificate(idNumber);
        if (!flag) {
            return new GenericResponse<>(ServiceCodes.ID_NUMBER_ERROR);
        }

        // 校验姓名、身份证号码，调用二要素接口
//        String userId = "**********"; // 固定userId，不用自动生成
//        String clientId = "4beaf0c3c69386bcfc99ad402624af99028e23f1e2e723cde9f2422e3251efd7";
//        String clientSecret = "a64ac6f63b1c914c791b0e5d0fdc5b6da065821c1f9772a033d0b9fec770f47c";
//        IdAuthRequest idAuthRequest = new IdAuthRequest(clientId, clientSecret, userId);
//
//        String authorization = idAuthRequest.getToken(dim4Host); // 调用实名认证的token
//        String response = idAuthRequest.twoElementsRequest(authorization, idNumber, name,dim4Host);
//        if (!StringUtils.isEmpty(response) && !response.contains("900000")) {
//            return new GenericResponse<>(ServiceCodes.INCONSISTENT);
//        }

        // 0 元开卡
        iBillingCardService.thirdCreateCard(placeId,
                cardTypeId,
                idNumber,
                name,
                identification,
                cashierId,
                address,
                issuingAuthority,
                nation,
                phoneNumber,
                validPeriod,
                thirdAccount,
                null);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 开卡并充值
     *
     * @param thirdAccountId   接入方账号Id
     * @param placeId          场所Id
     * @param cardTypeId       卡类型Id
     * @param idNumber         身份证号码
     * @param name             姓名
     * @param amount           充值金额
     * @param identification   扣取附加费标识
     * @param cashierId        收银员Id
     * @param address          地址
     * @param issuingAuthority 发证机关
     * @param nation           民族
     * @param phoneNumber      手机号码
     * @param validPeriod      身份证有效期
     * @param payType          支付方式
     * @param openId           微信openId
     * @param timestamp        时间戳
     * @param sign             签名
     * @return 支付链接对象
     * @throws Exception
     */
    @PostMapping("/createCardWithAmount")
    public GenericResponse<ObjDTO<PaymentResultBO>> createCardWithAmount(
            @RequestParam String thirdAccountId,
            @RequestParam String placeId,
            @RequestParam String cardTypeId,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam String amount,
            @RequestParam(name = "identification", required = false, defaultValue = "") String identification,
            @RequestParam(name = "cashierId", required = false, defaultValue = "") String cashierId,
            @RequestParam(name = "address", required = false) String address,
            @RequestParam(name = "issuingAuthority", required = false) String issuingAuthority,
            @RequestParam(name = "nation", required = false) String nation,
            @RequestParam(name = "phoneNumber", required = false) String phoneNumber,
            @RequestParam(name = "validPeriod", required = false) String validPeriod,
            @RequestParam String payType,
            @RequestParam(required = false) String openId,
            @RequestParam String timestamp,
            @RequestParam(required = false) String payCode,
            @RequestParam String sign) throws Exception {

        // 必填项校验
        if (StringUtils.isEmpty(thirdAccountId) || StringUtils.isEmpty(placeId) ||
                StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(sign) ||
                StringUtils.isEmpty(cardTypeId) || StringUtils.isEmpty(idNumber) ||
                StringUtils.isEmpty(name) || StringUtils.isEmpty(amount)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        // 临时卡不能开卡
        if ("1000".equals(cardTypeId)) {
            return new GenericResponse<>(ServiceCodes.BILLING_TEMP_CARD_NOT_SUPPORT);
        }

        // 获取秘钥
        Optional<ThirdAccount> thirdAccountOpt = thirdAccountService.findByThirdAccountId(thirdAccountId);
        if (!thirdAccountOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.ACCOUNT_ERROR);
        }
        ThirdAccount thirdAccount = thirdAccountOpt.get();

        // 验证身份证合法性
        boolean flag = IdNumberValidator.verificate(idNumber);
        if (!flag) {
            return new GenericResponse<>(ServiceCodes.ID_NUMBER_ERROR);
        }

        // 校验姓名、身份证号码，调用二要素接口
        String userId = "**********"; // 固定userId，不用自动生成
        String clientId = "4beaf0c3c69386bcfc99ad402624af99028e23f1e2e723cde9f2422e3251efd7";
        String clientSecret = "a64ac6f63b1c914c791b0e5d0fdc5b6da065821c1f9772a033d0b9fec770f47c";
        IdAuthRequest idAuthRequest = new IdAuthRequest(clientId, clientSecret, userId);

        String authorization = idAuthRequest.getToken(dim4Host); // 调用实名认证的token
        String response = idAuthRequest.twoElementsRequest(authorization, idNumber, name,dim4Host);
        if (!StringUtils.isEmpty(response) && !response.contains("900000")) {
            return new GenericResponse<>(ServiceCodes.INCONSISTENT);
        }

        PaymentResultBO paymentResultBO = marketService.createCard(placeId, cardTypeId, idNumber, name, amount, payType, openId, identification, cashierId, address, issuingAuthority, nation, phoneNumber, validPeriod, thirdAccount, payCode);


        return new GenericResponse<>(new ObjDTO<>(paymentResultBO));
    }


    /**
     * 用于pms特殊证件开卡并激活
     *
     * @param thirdAccountId   接入方账号Id
     * @param placeId          场所Id
     * @param cardTypeId       卡类型Id
     * @param idNumber         身份证号码
     * @param name             姓名
     * @param identification   扣取附加费标识
     * @param address          地址
     * @param issuingAuthority 发证机关
     * @param nation           民族
     * @param phoneNumber      手机号码
     * @param validPeriod      身份证有效期
     * @param timestamp        时间戳
     * @param sign             签名
     * @param activeTime       激活时间，特殊证件开卡时才会传的字段
     * @return
     */
    @PostMapping("/createCardBySpecialIdNumber")
    public GenericResponse<SimpleDTO> createCardBySpecialIdNumber(@RequestParam String thirdAccountId,
                                                 @RequestParam String placeId,
                                                 @RequestParam String cardTypeId,
                                                 @RequestParam String idNumber,
                                                 @RequestParam String name,
                                                 @RequestParam(name = "identification", required = false, defaultValue = "") String identification,
                                                 @RequestParam(name = "cashierId", required = false, defaultValue = "") String cashierId,
                                                 @RequestParam(name = "address", required = false) String address,
                                                 @RequestParam(name = "issuingAuthority", required = false) String issuingAuthority,
                                                 @RequestParam(name = "nation", required = false) String nation,
                                                 @RequestParam(name = "phoneNumber", required = false) String phoneNumber,
                                                 @RequestParam(name = "validPeriod", required = false) String validPeriod,
                                                 @RequestParam(name = "activeTime", required = false) String activeTime,
                                                 @RequestParam String timestamp,
                                                 @RequestParam String sign) throws Exception {

        // 必填项校验
        if (StringUtils.isEmpty(thirdAccountId) || StringUtils.isEmpty(placeId) ||
                StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(sign) ||
                StringUtils.isEmpty(cardTypeId) || StringUtils.isEmpty(idNumber) ||
                StringUtils.isEmpty(name)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }


        // 临时卡不能开卡
        if ("1000".equals(cardTypeId)) {
            return new GenericResponse<>(ServiceCodes.BILLING_TEMP_CARD_NOT_SUPPORT);
        }

        // 获取秘钥
        Optional<ThirdAccount> thirdAccountOpt = thirdAccountService.findByThirdAccountId(thirdAccountId);
        if (!thirdAccountOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.ACCOUNT_ERROR);
        }
        ThirdAccount thirdAccount = thirdAccountOpt.get();

        LocalDateTime activeDateTime = null;
        if(!StringUtils.isEmpty(activeTime) && idNumber.length() != 18){     //传了激活时间并且是非身份证激活
            activeDateTime = LocalDateTime.parse(activeTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            //特殊证件校验
            String regex ="^[a-zA-Z0-9]+$"; // 只能是数字和字母
            Pattern pattern = Pattern .compile(regex) ;
            Matcher matcher = pattern .matcher(idNumber);
            if (!matcher.matches()) {
                return new GenericResponse<>(ServiceCodes .BAD_PARAM);
            }
            PlaceBizConfig config = placeBizConfigService.findByPlaceId(placeId); // 查询网吧的非身份证配置
            if (config.getNonIdNumber() > 0) {   // 大于，允许非身份证号注册，需要判断今天已经激活的非身份证数量
                int activated = logOperationService.countTodayActivatedNonIdNumberByPlaceId(placeId);
                if (activated >= config.getNonIdNumber()) {  // 激活数量已经达到上限
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
                }
            } else if (config.getNonIdNumber() == 0){ // 等于， 不允许非身份证号注册，不允许注册非身份证号码
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
            }
        }

        Optional<BillingCard> byPlaceIdAndIdNumber = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (!byPlaceIdAndIdNumber.isPresent()) {
            //没有卡，开卡
            iBillingCardService.thirdCreateCard(placeId, cardTypeId, idNumber, name, identification, cashierId, address, issuingAuthority, nation, phoneNumber, validPeriod, thirdAccount,activeDateTime);
        }else{
            // 校验场所状态
            GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
            if (!placeConfig.isResult()) {
                return new GenericResponse<>(placeConfig.getMessage());
            }
            PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
            // 校验场所状态
            if (placeConfigBO.getStatus() != 0) {
                return new GenericResponse<>(ServiceCodes.PLACE_REALNAME_DISABLED);
            }

            //有卡，激活卡
            LocalDateTime now = LocalDateTime.now();
            BillingCard billingCard = byPlaceIdAndIdNumber.get();
            if(StringUtils.isEmpty(activeTime)){
                billingCard.setActiveTime(now);
            }else{
                billingCard.setActiveTime(activeDateTime);
            }
            billingCard.setActiveType(ActiveType.getActiveTypes(Integer.parseInt("34")));
            billingCard.setUpdated(now);
            billingCardService.save(billingCard);
//            logOperationService.addActivateCardOperation(SourceType.PMS, billingCard, null, "34");
            logActivateService.addLogActivateCard(SourceType.PMS, billingCard, null, "34");

            // 保存轮询数据
            GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, "", billingCard.getIdNumber(), BusinessType.ACTIVE);

            if (pollingBOGeneric.isResult()) {
                PollingBO pollingBO = pollingBOGeneric.getData().getObj();

                if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                    ActiveBusinessBO activeBusinessBO = new ActiveBusinessBO();
                    activeBusinessBO.setPlaceId(placeId);
                    activeBusinessBO.setIdNumber(billingCard.getIdNumber());
                    activeBusinessBO.setCreated(LocalDateTime.now().toString());
                    activeBusinessBO.setSourceType(SourceType.PMS);
                    activeBusinessBO.setBusinessType(BusinessType.ACTIVE);
                    activeBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                    activeBusinessBO.setClientId("");
                    activeBusinessBO.setCashierId(cashierId);
                    activeBusinessBO.setType(1);
                    activeBusinessBO.setOperator(SourceType.PMS.name());
                    activeBusinessBO.setActiveTime(LocalDateTime.now().toString());
                    activeBusinessBO.setIdName(billingCard.getIdName());
                    activeBusinessBO.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount());
                    activeBusinessBO.setPresentAccount(billingCard.getPresentAccount());
                    activeBusinessBO.setCardId(billingCard.getCardId());
                    BillingCardBO bo = billingCard.toBO();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    bo.setActiveTimeStr(null != bo.getActiveTime() ? formatter.format(bo.getActiveTime()) : null);
                    bo.setCreatedStr(null != bo.getCreated() ? formatter.format(bo.getCreated()) : null);
                    bo.setUpdatedStr(null != bo.getUpdated() ? formatter.format(bo.getUpdated()) : null);
                    activeBusinessBO.setBillingCardBO(bo);

                    // 保存收银台业务数据
                    notifyServerService.pushActiveBusinessData(activeBusinessBO);
                    log.info("pushActiveBusinessData:{}", new Gson().toJson(activeBusinessBO));

                }
            }
        }

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

}
