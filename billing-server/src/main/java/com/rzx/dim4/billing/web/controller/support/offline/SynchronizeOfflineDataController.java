package com.rzx.dim4.billing.web.controller.support.offline;

import com.google.common.hash.Hashing;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.*;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.FtpUtils;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.localMode.LogSynOfflineData;
import com.rzx.dim4.billing.entity.localMode.SynOfflineData;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
import com.rzx.dim4.billing.service.localMode.LogSynOfflineDataService;
import com.rzx.dim4.billing.service.localMode.SynOfflineDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 同步离线后的数据
 *
 * <AUTHOR>
 * @date 2022年8月15日 下午1:49:15
 */
@Slf4j
@RestController
@RequestMapping("/billing/support/offline/synchronize")
public class SynchronizeOfflineDataController {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    BillingOnlineService billingOnlineService;
    @Autowired
    BillingCardService billingCardService;
    @Autowired
    LogLoginService logLoginService;
    @Autowired
    LogHbService logHbService;
    @Autowired
    LogOperationService logOperationService;
    @Autowired
    LogTopupService logTopupService;
    @Autowired
    LogShiftService logShiftService;
    @Autowired
    BillingRuleCommonService billingRuleCommonService;
    @Autowired
    PackageTimeReserveService packageTimeReserveService;
    @Autowired
    SynOfflineDataService synOfflineDataService;
    @Autowired
    LogSynOfflineDataService logSynOfflineDataService;
    @Autowired
    LogRefundService logRefundService;
    @Autowired
    PlaceChainStoresService placeChainStoresService;
    @Autowired
    BillingCardTypeService billingCardTypeService;
    @Autowired
    BillingRulePackageTimeService billingRulePackageTimeService;
    @Autowired
    LogRoomService logRoomService;
    @Autowired
    BillingCardDeductionService billingCardDeductionService;
    @Autowired
    PlaceServerService placeServerService;
    @Autowired
    RewardPointsRuleService rewardPointsRuleService;
    @Autowired
    LogPointsService logPointsService;
    @Autowired
    LogActivateService logActivateService;
    DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 同步用户计费卡数据
     *
     * @param requestBody
     * @return
     */
    @PostMapping("/synOfflineData")
    public String synOfflineData(@RequestBody String requestBody) {

        log.info("synOfflineData:::" + requestBody);

        Gson gson = new Gson();
        SynRequest synRequest = gson.fromJson(requestBody, SynRequest.class);

        // 参数非空校验
        String placeId = synRequest.getPlaceId();
        String requestId = synRequest.getRequestId();
        String timestamp = synRequest.getTimestamp();
        String sign = synRequest.getSign();
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(sign) || StringUtils.isEmpty(requestId)) {
            return "params error";
        }

        // 时间戳校验
        LocalDateTime reqDatetime = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(timestamp) * 1000),
                ZoneId.systemDefault());
        LocalDateTime maxDatetime = LocalDateTime.now().plus(5, ChronoUnit.MINUTES);
        LocalDateTime minDatetime = LocalDateTime.now().minus(5, ChronoUnit.MINUTES);
        if (reqDatetime.isAfter(maxDatetime) || reqDatetime.isBefore(minDatetime)) {
            return "timestamp error";
        }

        // 签名校验
        String offlineCardsString = requestBody.substring(requestBody.indexOf("["), requestBody.lastIndexOf("]") + 1);
        String toSignString = offlineCardsString + placeId + requestId + timestamp + "SYN";
        log.info("待签名字段::::::::" + toSignString);
        @SuppressWarnings("deprecation")
        String toSign = Hashing.md5().hashBytes(toSignString.getBytes()).toString();

        if (!sign.equalsIgnoreCase(toSign)) {
            return "sign error";
        }

        List<SynOfflineCard> offlineCards = synRequest.getOfflineCards();
        if (CollectionUtils.isEmpty(offlineCards)) {
            return "success";
        }

        Optional<SynOfflineData> synOfflineDataOpt = synOfflineDataService.findByRequestId(requestId);
        if (synOfflineDataOpt.isPresent()) {
            // 中心已经处理
            log.info("本次本地上传数据中心已处理::::::::::原文件地址:::::::" + synOfflineDataOpt.get().getFileUrl());
            return "success";
        }
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String loginPass = passwordEncoder.encode("123456"); // 开卡默认密码

        // 积分开关
        int rewardPoints = 0; // 默认关闭
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (placeConfigResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
            rewardPoints = placeConfigBO.getRewardPoints();
        }
        // 遍历数据
        for (SynOfflineCard offlineCard : offlineCards) {
            BillingCard billingCard = null;
            int cashAccount = 0;
            int presentAccount = 0;
            int sumCost = 0;
            String idNumber = offlineCard.getIdNumber();
            String idName = offlineCard.getIdName();
            Map<String, String> loginMap = new HashMap<>();
            // 查询卡信息
            Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
            if (billingCardOpt.isPresent()) {
                billingCard = billingCardOpt.get();
                cashAccount = billingCard.getCashAccount();
                presentAccount = billingCard.getPresentAccount();
            }

            List<SynOfflineOperation> synOfflineOperations = offlineCard.getSynOfflineOperations(); // 离线操作记录
            for (SynOfflineOperation offlineOperation : synOfflineOperations) {
                String offlineDataId = offlineOperation.getOfflineDataId();
                String operationType = offlineOperation.getOperationType();
                LocalDateTime operationTime = LocalDateTime.parse(offlineOperation.getOperationTime(), dtFormatter);
                log.info("收到数据::::::::offlineDataId" + offlineDataId + ":::::::operationType" + operationType + "::::::operationTime" + operationTime);
                String shiftId = offlineOperation.getShiftId();
                // 先查当前业务是否已经处理
                Optional<LogSynOfflineData> logSynOfflineDataOpt = logSynOfflineDataService.findByOfflineDataId(offlineDataId);
                if (logSynOfflineDataOpt.isPresent()) {
                    // 中心已经处理，跳过
                    continue;
                }

                // 激活
                if (operationType.equals(OperationType.ACTIVATE_CARD.name())) {
                    if (billingCard == null) {
                        // 激活时，一定有卡
                        continue;
                    }
                    // 激活
                    synchronizeOfflineActivation(operationTime, shiftId, offlineDataId, billingCard, requestId, offlineOperation.getActiveType());
                    continue;
                }

                // 开卡
                if (operationType.equals(OperationType.CREATE_CARD.name())) {
                    if (billingCard == null || billingCard.getDeleted() == 1) {
                        // 开卡
                        cashAccount += offlineOperation.getCashAmount();
                        presentAccount += offlineOperation.getPresentAmount();
                        billingCard = synchronizeOfflineCreateCard(offlineDataId, offlineOperation.getCashAmount(), offlineOperation.getPresentAmount(), placeId,
                                shiftId, rewardPoints, idNumber, idName, loginPass, requestId, operationTime, offlineOperation.getActiveType());
                        continue;
                    }
                    // 充值
                    cashAccount += offlineOperation.getCashAmount();
                    presentAccount += offlineOperation.getPresentAmount();
                    billingCard = synchronizeLogTopup(billingCard, shiftId, offlineOperation.getCashAmount(),
                            offlineOperation.getPresentAmount(), offlineDataId, requestId, operationTime);
                    billingCard.setActiveTime(operationTime);
                    try {
                        billingCard.setActiveType(ActiveType.getActiveTypes(offlineOperation.getActiveType()));
                    } catch (Exception e) {
                        log.info("本地模式激活传入激活方式转换失败:::::::activeType:::::" + offlineOperation.getActiveType());
                        billingCard.setActiveType(ActiveType.MANUAL_INPUT);
                    }
                    billingCardService.save(billingCard);
                    continue;
                }

                // 充值
                if (operationType.equals(OperationType.TOPUP.name()) && billingCard != null) {
                    cashAccount += offlineOperation.getCashAmount();
                    presentAccount += offlineOperation.getPresentAmount();
                    billingCard = synchronizeLogTopup(billingCard, shiftId, offlineOperation.getCashAmount(),
                            offlineOperation.getPresentAmount(), offlineDataId, requestId, operationTime);
                    continue;
                }

                // 离线登入
                if (operationType.equals(OperationType.LOGIN.name()) && billingCard != null) {
                    sumCost += offlineOperation.getCost();
                    synchronizeLogLogin(billingCard, offlineOperation, requestId, loginMap);
                    continue;
                }

                // 转换计费规则
                if (operationType.equals(OperationType.CONVERT_BILLING_RULE.name()) && billingCard != null) {
                    synchronizeOfflineConvertRule(requestId, billingCard, offlineOperation.getRuleId(), offlineOperation.getAreaId(), operationTime, offlineDataId);
                    continue;
                }

                // 离线结账
                if (operationType.equals(OperationType.LOGOUT.name()) && billingCard != null) {
                    billingCard = synchronizeLogout(billingCard, cashAccount, presentAccount, offlineOperation, requestId, loginMap, rewardPoints);
                    if (billingCard.getDeleted() == 1 && "1000".equals(billingCard.getCardTypeId())) {
                        // 是销卡结账
                        cashAccount = 0;
                        presentAccount = 0;
                    }
                    continue;
                }

                // 中心登入,离线结账
                if ("OFFLINE_LOGOUT".equals(operationType) && billingCard != null) {
                    synchronizeOfflineLogout(billingCard, offlineOperation, requestId, rewardPoints);
                    if (billingCard.getDeleted() == 1 && "1000".equals(billingCard.getCardTypeId())) {
                        // 是销卡结账
                        cashAccount = 0;
                        presentAccount = 0;
                    }
                }
            }

            // 处理最后只有离线登入的数据,只能最后处理，不然钱会不正确
            if (loginMap.size() == 1 && billingCard != null && sumCost > 0) {

                String loginId = new ArrayList<>(loginMap.keySet()).get(0);
                Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findTop1ByPlaceIdAndCardIdAndLoginId(placeId, billingCard.getCardId(), loginId);
                if (!billingOnlineOpt.isPresent()) {
                    continue;
                }
                BillingOnline billingOnline = billingOnlineOpt.get();
                Optional<LogLogin> logLoginOpt = logLoginService.findByPlaceIdAndLoginId(placeId, loginId, billingOnline.getBillingTime());
                List<LogOperation> logOperationList = logOperationService.findByLoginIdAndOperationType(loginId,
                        OperationType.LOGIN);

                if (logLoginOpt.isPresent() && logLoginOpt.get().getLogoutTime() == null && logOperationList.size() >0) {
                    LogLogin logLogin = logLoginOpt.get();

                    // 最后还剩一次离线登入未下机的数据
                    if (cashAccount + presentAccount < sumCost) {
                        billingCard.setCashAccount(0);
                        billingCard.setPresentAccount(0);
                    } else {
                        if (cashAccount >= sumCost) {
                            billingCard.setCashAccount(cashAccount - sumCost);
                            billingCard.setPresentAccount(presentAccount);
                        } else {
                            billingCard.setCashAccount(0);
                            billingCard.setPresentAccount(presentAccount - (sumCost - cashAccount));
                        }
                    }

                    logLogin.setTotalAccount(billingCard.getTotalAccount());
                    LogOperation logOperation = logOperationList.get(0);
                    logOperation.setCashBalance(cashAccount);
                    logOperation.setPresentBalance(presentAccount);

                    try {
                        billingCardService.save(billingCard);
                        logLoginService.save(logLogin);
//                        logOperationService.save(logOperation);
                        try {
                            logOperationService.save(logOperation);
                        } catch (Exception et) {
                            logOperation.setRemark(logOperation.getRemark() + "，防御性数据1");
                            try {
                                logOperationService.save(logOperation);
                            } catch (Exception ex) {
                                logOperation.setRemark(logOperation.getRemark() + "，防御性数据2");
                                logOperationService.save(logOperation);
                            }
                        }
                    } catch (Exception e) {
                        log.info(e.getMessage());
                    }
                }
            }
        }
        log.info("本地模式上传数据处理完毕.....原数据写入文件上传至七牛云");

        try {
            BufferedWriter writer = null;
           // String avatarKey = QiniuUtils.getLocalModeDataKeyForQiniu("txt", placeId);
            String pathName = requestId + ".txt"; // 临时文件，处理完删除
            File file = new File(pathName);
            writer = new BufferedWriter(new FileWriter(file));
            writer.write(requestBody);
            writer.close();

            FileInputStream input = new FileInputStream(file);
            MockMultipartFile mockMultipartFile = new MockMultipartFile(pathName, input);

            // 上传到七牛云
//            QiniuUtils.upload(mockMultipartFile.getBytes(), avatarKey);
//            //获取七牛对象的URL
//            String url = QiniuUtils.getDownloadUrl(avatarKey);
            String url = FtpUtils.ftpUpload(mockMultipartFile, "/localMode", "");

            SynOfflineData synOfflineData = new SynOfflineData();
            synOfflineData.setRequestId(requestId);
            synOfflineData.setCreated(LocalDateTime.now());
            synOfflineData.setPlaceId(placeId);
            synOfflineData.setFileUrl(url);
            synOfflineData.setFileName(file.getName());
            synOfflineDataService.save(synOfflineData);

            // 删除临时文件,关闭流
            input.close();
            file.delete();
        } catch (Exception e) {
            log.info(e.getMessage());
            log.info("数据处理完成，文件上传七牛云失败！！！！");
        }

        return "success";
    }

    /**
     * 处理离线开卡
     *
     * @param offlineDataId
     * @param cashAmount
     * @param presentAmount
     * @param placeId
     * @param shiftId
     * @param idNumber
     * @param idName
     * @param loginPass
     * @param requestId
     * @param operationTime
     */
    private BillingCard synchronizeOfflineCreateCard(String offlineDataId, int cashAmount, int presentAmount, String placeId, String shiftId, int rewardPoints,
                                                     String idNumber, String idName, String loginPass, String requestId, LocalDateTime operationTime, int activeType) {
        // 先获取班次
        BillingCard card = null;
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId,
                shiftId, 0);
        if (!optLogShift.isPresent()) {
            return card;
        }
        LogShift logShift = optLogShift.get();

        BillingCard billingCard = new BillingCard();
        Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);
        if (billingCardOpt.isPresent() && billingCardOpt.get().getDeleted() == 0) {
            // 进入开卡逻辑，一定是当前没卡或者是已经删除的卡
            return card;
        }
        if (billingCardOpt.isPresent() && billingCardOpt.get().getDeleted() == 1) {
            // 已经删除的卡，先变为可用
            billingCard = billingCardOpt.get();
            billingCard.setDeleted(0);
            if (!optPlaceChainStores.isPresent()) {
                billingCard.setCardTypeId("1000");
                billingCard.setCardTypeName("临时卡");
            }
        }
        if (!billingCardOpt.isPresent()) {
            // 当前门店没卡
            // 当前门店没卡,查是否有连锁卡
            if (optPlaceChainStores.isPresent()) {
                // 是连锁
                PlaceChainStores placeChainStores = optPlaceChainStores.get();
               // List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(optPlaceChainStores.get().getChainId());
               // List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList()); // 获取连锁场所ID列表
                // 查询动作时，一定可以找到一张已经在连锁网吧开卡记录
                Optional<BillingCard> billingCardChainOpt = billingCardService.findByChainIdAndIdNumberAndChainCard(placeChainStores.getChainId(), idNumber, 0);
                if (billingCardChainOpt.isPresent()) {
                    BillingCard chainCard = billingCardChainOpt.get();
                    BeanUtils.copyProperties(chainCard, billingCard);
                    billingCard.setId(null);
                    billingCard.setCardId(null);
                    billingCard.setChainCard(1);
                    // 查询最新的卡类型
                    Optional<BillingCardType> billingCardTypeOpt = billingCardTypeService.findByPlaceIdAndTypeName(placeId, billingCard.getCardTypeName());
                    if (billingCardTypeOpt.isPresent()) {
                        BillingCardType billingCardType = billingCardTypeOpt.get();
                        billingCard.setCardTypeId(billingCardType.getCardTypeId());
                    }
                } else {
                    // 连锁新开卡也是默认临时卡
                    billingCard.setCardTypeId("1000");
                    billingCard.setCardTypeName("临时卡");
                    billingCard.setChainCard(1);
                }
            } else {
                // 单店
                billingCard.setCardTypeId("1000");
                billingCard.setCardTypeName("临时卡");
            }
        }

        try {
            billingCard.setActiveType(ActiveType.getActiveTypes(activeType));
        } catch (Exception e) {
            log.info("本地模式激活传入激活方式转换失败:::::::activeType:::::" + activeType);
            billingCard.setActiveType(ActiveType.MANUAL_INPUT);
        }

        int points = 0;
        if (rewardPoints == 1 && cashAmount > 0 && !"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            points = rewardPointsRuleService.getRewardPointsNum(placeId, cashAmount, ExchangePointsType.TOPUP);
        }

        billingCard.setPlaceId(placeId);
        billingCard.setIdNumber(idNumber);
        billingCard.setIdName(idName);
        billingCard.setLoginName(idNumber);
        billingCard.setLoginPass(loginPass);
        billingCard.setCashAccount(cashAmount);
        billingCard.setPresentAccount(presentAmount);
        billingCard.setPoints(points);
        billingCard.setActiveTime(operationTime);
        billingCard.setRemark("本地模式开卡");
        billingCard.setCreated(LocalDateTime.now());
        billingCard.setCreater(Long.parseLong(logShift.getLoginAccountId()));

        try {
            card = billingCardService.save(billingCard);

            // 带钱开卡  写充值记录
            if (card != null && cashAmount > 0) {
                saveTopup(card, logShift, cashAmount, operationTime,2);
                // 开启了积分兑换
                if (rewardPoints == 1 && points > 0) {
                    // 积分变更记录
                    logPointsService.addLogPointsOperation(ExchangePointsType.TOPUP, points, cashAmount, billingCard, logShift, false);
                }
            }
            saveOfflineLogOperation(operationTime, billingCard, OperationType.CREATE_CARD, cashAmount, presentAmount, "", logShift, "");
//			logOperationService.addCreateCardLogOperation(SourceType.CASHIER, billingCard, cashAmount, presentAmount, logShift);
            saveLogSynOfflineData(requestId, placeId, offlineDataId, idNumber, OperationType.CREATE_CARD, cashAmount, presentAmount,
                    shiftId, 1, operationTime);
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, placeId, offlineDataId, idNumber, OperationType.CREATE_CARD, cashAmount, presentAmount,
                    shiftId, 0, operationTime);
        }
        return card;
    }

    /**
     * 处理离线激活
     *
     * @param operationTime
     * @param card
     * @param requestId
     */
    private void synchronizeOfflineActivation(LocalDateTime operationTime, String shiftId, String offlineDataId, BillingCard card, String requestId, int activeType) {
        LogShift logShift = null;
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(card.getPlaceId(),
                shiftId, 0);
        if (!optLogShift.isPresent()) {
            return;
        }

        logShift = optLogShift.get();

        try {
            card.setActiveType(ActiveType.getActiveTypes(activeType));
        } catch (Exception e) {
            log.info("本地模式激活传入激活方式转换失败:::::::activeType:::::" + activeType);
            card.setActiveType(ActiveType.MANUAL_INPUT);
        }

        card.setActiveTime(operationTime);
        card.setUpdated(LocalDateTime.now());

        try {
            billingCardService.save(card);
            // 连锁要用总余额
            if (!"1000".equals(card.getCardTypeId()) && !"1002".equals(card.getCardTypeId())) {
                Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(card.getPlaceId(), card.getIdNumber(), 0);
                if (!newCard.isPresent()) {
                    return;
                }
                card = newCard.get();
            }
//            saveOfflineLogOperation(operationTime, card, OperationType.ACTIVATE_CARD, 0, 0, "", logShift, "");
            //离线激活操作记录由log_operation表记录改为log_activate
            saveOfflineLogActivate(operationTime, card, OperationType.ACTIVATE_CARD, logShift);

//			logOperationService.addActivateCardOperation(SourceType.CASHIER, card, logShift, "1");
            saveLogSynOfflineData(requestId, card.getPlaceId(), offlineDataId, card.getIdNumber(), OperationType.ACTIVATE_CARD, 0, 0,
                    shiftId, 1, operationTime);
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, card.getPlaceId(), offlineDataId, card.getIdNumber(), OperationType.ACTIVATE_CARD, 0, 0,
                    shiftId, 0, operationTime);
        }
    }

    /**
     * 保存本地模式充值记录
     *
     * @param billingCard
     * @param logShift
     * @param cashAmount
     * @param topupTime
     * @param topupSourceType  充值来源类型0:网费充值；2:开卡充值
     *
     */
    private void saveTopup(BillingCard billingCard, LogShift logShift, int cashAmount, LocalDateTime topupTime,int topupSourceType) {
        LogTopup logTopup = new LogTopup();
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCashAmount(cashAmount);
        logTopup.setCashBalance(billingCard.getCashAccount());
        logTopup.setClientId(null);
        logTopup.setCreated(LocalDateTime.now());
        logTopup.setCreater(null);
        logTopup.setIdName(billingCard.getIdName());
        logTopup.setIdNumber(billingCard.getIdNumber());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setLoginId(null);
        logTopup.setOrderId("CASH" + System.currentTimeMillis());
        logTopup.setOperator(StringUtils.isEmpty(logShift) ? "-1" : logShift.getLoginAccountId());
        logTopup.setOperatorName(StringUtils.isEmpty(logShift) ? "-1" : logShift.getLoginAccountName());
        logTopup.setStatus(3);
        logTopup.setPayType(PayType.CASH);
        logTopup.setPlaceId(billingCard.getPlaceId());
        logTopup.setPresentAmount(0);
        logTopup.setPresentBalance(billingCard.getPresentAccount());
        logTopup.setShiftId(logShift.getShiftId());
        logTopup.setSourceType(SourceType.CASHIER);
        logTopup.setTopupTime(topupTime);
        logTopup.setTopupRuleId(null);
        logTopup.setTopupSourceType(topupSourceType);
        logTopupService.save(logTopup);
    }

    /**
     * 保存本地模式业务数据日志
     *
     * @param requestId
     * @param placeId
     * @param offlineDataId
     * @param idNumber
     * @param operationType
     * @param cost
     * @param present
     * @param shiftId
     * @param status
     * @param operationTime
     */
    private void saveLogSynOfflineData(String requestId, String placeId, String offlineDataId, String idNumber, OperationType operationType,
                                       int cost, int present, String shiftId, int status, LocalDateTime operationTime) {
        LogSynOfflineData logSynOfflineData = new LogSynOfflineData();
        logSynOfflineData.setRequestId(requestId);
        logSynOfflineData.setPlaceId(placeId);
        logSynOfflineData.setOfflineDataId(offlineDataId);
        logSynOfflineData.setIdNumber(idNumber);
        logSynOfflineData.setOperationType(operationType);
        logSynOfflineData.setCost(cost);
        logSynOfflineData.setPresent(present);
        logSynOfflineData.setShiftId(shiftId);
        logSynOfflineData.setStatus(status);
        logSynOfflineData.setOperationTime(operationTime);
        logSynOfflineData.setCreated(LocalDateTime.now());
        logSynOfflineDataService.save(logSynOfflineData);
    }

    /**
     * 处理转换计费规则(ruleId有值为标准转包时，为空则是包时转标准)
     * @param requestId
     * @param billingCard
     * @param ruleId
     * @param areaId
     * @param operationTime
     * @param offlineDataId
     */
    private void synchronizeOfflineConvertRule (String requestId,BillingCard billingCard, String ruleId, String areaId, LocalDateTime operationTime, String offlineDataId) {
        if (billingCard == null) {
            return;
        }
        String placeId = billingCard.getPlaceId();
        String cardId = billingCard.getCardId();
        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId, cardId);
        if (!billingOnlineOpt.isPresent()) {
            return;
        }
        BillingOnline billingOnline = billingOnlineOpt.get();

        if (billingOnline.getPackageFlag() > 0) {
            // 使用完的包时规则状态变为2
            packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());
        }

        String loginId = billingOnline.getLoginId();
        if (!StringUtils.isEmpty(ruleId)) {
            if (ruleId.equals(billingOnline.getRuleId())) {
                // 中心已经转了，跳过
                return;
            }
            // 标准转包时
            List<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findByPlaceIdAndRuleId(placeId, ruleId);
            if (StringUtils.isEmpty(packageTimeReserveOpt) || packageTimeReserveOpt.size() == 0) {
                return;
            }
            PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get(0);
            Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, ruleId);
            if (!billingRulePackageTimeOpt.isPresent()) {
                return;
            }
            BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
            billingOnline.setPackagePayFlag(packageTimeReserve.getPackagePayFlag());
            billingOnline.setRuleId(ruleId);
            billingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
            billingOnline.setCommonPrice(billingRulePackageTime.getPrice());
            billingOnline.setDeduction(billingOnline.getDeduction() + billingRulePackageTime.getPrice());
            if (PackageTimeAlgorithm.getNextTime(operationTime,billingRulePackageTime).isAfter(billingOnline.getNextTime())) {
                billingOnline.setNextTime(PackageTimeAlgorithm.getNextTime(operationTime,billingRulePackageTime));
            }
            billingOnline.setRemark("本地模式标准转包时");

            packageTimeReserve.setStatus(1);
            packageTimeReserve.setUpdated(operationTime);
            packageTimeReserveService.save(packageTimeReserve);
        } else {
            // 包时转标准
            Optional<BillingRuleCommon> billingRuleCommonOpt = billingRuleCommonService.billingRuleCommons(placeId, areaId, "1000");
            if (!billingRuleCommonOpt.isPresent()) {
                return;
            }
            BillingRuleCommon billingRuleCommon = billingRuleCommonOpt.get();
            if (billingOnline.getRuleId().equals(billingRuleCommon.getRuleId())) {
                // 中心已经转了，跳过
                return;
            }
            billingOnline.setRuleId(billingRuleCommon.getRuleId());
            billingOnline.setPackageFlag(0);
            billingOnline.setCommonPrice(CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices()));
            if (operationTime.isAfter(billingOnline.getNextTime())) {
                billingOnline.setNextTime(operationTime);
            }
            billingOnline.setRemark("本地模式包时转普通扣费");
        }

        try {
            billingOnline.setUpdated(operationTime);
            billingOnlineService.save(billingOnline);
            saveOfflineLogOperation(operationTime, billingCard, OperationType.CONVERT_BILLING_RULE, 0, 0, billingOnline.getClientId(), null, loginId);
            saveLogSynOfflineData(requestId, placeId, offlineDataId, billingCard.getIdNumber(), OperationType.CONVERT_BILLING_RULE, 0, 0,
                    "", 1, operationTime);
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, placeId, offlineDataId, billingCard.getIdNumber(), OperationType.CONVERT_BILLING_RULE, 0, 0,
                    "", 0, operationTime);
        }
    }

    /**
     * 处理离线结账
     *
     * @param billingCard
     * @param offlineOperation
     * @param requestId
     * @return
     */
    private BillingCard synchronizeOfflineLogout(BillingCard billingCard, SynOfflineOperation offlineOperation, String requestId, int rewardPoints) {
        if (StringUtils.isEmpty(offlineOperation.getOperationTime()) || StringUtils.isEmpty(offlineOperation.getLoginId())) {
            return billingCard;
        }

        String loginId = offlineOperation.getLoginId();
        String placeId = billingCard.getPlaceId();
        int logoutFlag = offlineOperation.getLogoutFlag();
        int offlineBeforeTotalAccount = offlineOperation.getOfflineBeforeTotalAccount();
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findTop1ByPlaceIdAndCardIdAndLoginId(placeId,
                billingCard.getCardId(), loginId);
        if (!optBillingOnline.isPresent()) {
            return billingCard;
        }
        BillingOnline billingOnline = optBillingOnline.get();
        Optional<LogLogin> optLogLogin = logLoginService.findByPlaceIdAndLoginId(placeId, loginId, billingOnline.getBillingTime());

        List<LogOperation> logOperationList = logOperationService.findByLoginIdAndOperationType(loginId,
                OperationType.LOGIN);
        // 查询班次信息
        LogShift logShift = null;
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId,
                offlineOperation.getShiftId(), 0);
        if (optLogShift.isPresent()) {
            logShift = optLogShift.get();
        }

        int presentCost = 0; // 赠送消费金额
        int totalCost = 0;
        long onlineTime = 0; // 在线时长

        billingCard.setUpdated(LocalDateTime.now());
        if (logoutFlag == 0) {
            billingCard.setActiveTime(null);
        }
        // 查询所有会员信息
        if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            billingCard = billingCardService.save(billingCard);
            Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
            if (!newCard.isPresent()) {
                return billingCard;
            }
            billingCard = newCard.get();
            // 会员当前总余额
            int sumAccount = billingCard.getTotalAccount();
            // 算扣费差价
            int cost = sumAccount - (offlineOperation.getCashBalance() + offlineOperation.getPresentBalance());
            if (cost > 0) {
                List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, cost,loginId,0, 0);
                // 扣费
                billingCardService.billingCardDeduction(costDetails,billingCard.getPlaceId());
            }
        } else {
            billingCard.setCashAccount(offlineOperation.getCashBalance());
            billingCard.setPresentAccount(offlineOperation.getPresentBalance());
            billingCard = billingCardService.save(billingCard);
        }
//        List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);
//        if (billingCards.size() == 1) {
//            billingCard.setCashAccount(offlineOperation.getCashBalance());
//            billingCard.setPresentAccount(offlineOperation.getPresentBalance());
//        } else {
//            // 连锁会员当前总余额
//            int sumAccount = billingCardDeductionService.sumAccount(billingCards, 2);
//            // 算扣费差价
//            int cost = (offlineOperation.getCashBalance() + offlineOperation.getPresentBalance()) - sumAccount;
//            if (cost > 0) {
//                List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, cost,loginId,0);
//                // 扣费
//                billingCardService.billingCardDeduction(costDetails,billingCard.getPlaceId());
//            }
//        }

        // 判断登录信息是否完整
        if (optLogLogin.isPresent() && logOperationList.size() == 1 && optBillingOnline.isPresent()) {
            log.info("::::::登入信息完整::::::loginId:::" + loginId);
          //  BillingOnline billingOnline = optBillingOnline.get();
            LogOperation logOperationLogin = logOperationList.get(0);
            LogLogin logLogin = optLogLogin.get();
            String clientId = billingOnline.getClientId();

            LocalDateTime logoutTime = LocalDateTime.parse(offlineOperation.getOperationTime(), dtFormatter); // 登出时间

            // 可能存在换机的情况，所以无法获取准确的消费金额，通过计算登入和登出的余额差值来计算消费金额
            int loginBalance = logOperationLogin.getCashBalance() + logOperationLogin.getPresentBalance()
                    + (-logOperationLogin.getCost()); // 登入时余额
            int currBalance = offlineOperation.getCashBalance() + offlineOperation.getPresentBalance(); // 当前余额
            totalCost = loginBalance - currBalance;

            // 查询总冲正
            int sumCostTotalReversal = logOperationService.sumTotalReversalByCardIdAndDateTime(placeId, billingCard.getCardId(), logLogin.getLoginTime(),LocalDateTime.now());
            // 查询充值,这里会把现金包时的也查出来，有可能这个包时是未使用的预包时，那这一笔就不能算在消费上
            int sumCostTotal = logTopupService.sumCostCashAmountByCardIdAndDateTime(placeId, billingCard.getCardId(), logLogin.getLoginTime(), logoutTime,Arrays.asList(0,1));
            totalCost = totalCost + sumCostTotalReversal + sumCostTotal;

            // 登入之前的预包时用完之后没有算入消费里
            // 查未使用的预包时
            Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findUnusedByPlaceIdAndCardId(placeId, billingCard.getCardId());
            // 获取包时开始时间
            List<OperationType> operationTypes = new ArrayList<>();
            operationTypes.add(OperationType.ACTIVATE_CARD);
            operationTypes.add(OperationType.LOGIN);
            Optional<LogOperation> logOperationOpt = logOperationService.findTop1ByPlaceIdAndIdNumberAndOperationTypeInOrderByIdDesc(placeId, billingCard.getIdNumber(), operationTypes);
            if (logOperationOpt.isPresent()) {
                LogOperation logOperation = logOperationOpt.get();
                // 开卡、激活时间
                LocalDateTime startTime = logOperation.getCreated();
                // 查询激活、开卡到登入这段时间内包时记录，一个人只能有一条预包时
                List<OperationType> packageOperationTypes = new ArrayList<>();
                packageOperationTypes.add(OperationType.PACKAGE_TIME);
                Optional<LogOperation> packageLogOperationOpt = logOperationService.findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(placeId, billingCard.getCardId(), packageOperationTypes, startTime, logLogin.getLoginTime());
                if (packageLogOperationOpt.isPresent() && !packageTimeReserveOpt.isPresent()) {
                    // 操作了预包时,并且没有未使用的预包时,说明这个预包时已经被使用了
                    totalCost = totalCost - packageLogOperationOpt.get().getCost();
                }
            } else {
                // 如果操作记录表中没有激活和登录记录，再去log_activate表中查询激活日志记录
                Optional<LogActivate> logActivateOpt = logActivateService.findTop1ByPlaceIdAndIdNumberAndOperationTypeOrderByIdDescNoTime(placeId, billingCard.getIdNumber(), OperationType.ACTIVATE_CARD);
                if (logActivateOpt.isPresent()) {
                    LogActivate logActivate = logActivateOpt.get();
                    // 激活时间
                    LocalDateTime startTime = logActivate.getCreated();
                    // 查询激活到登入这段时间内包时记录，一个人只能有一条预包时
                    List<OperationType> packageOperationTypes = new ArrayList<>();
                    packageOperationTypes.add(OperationType.PACKAGE_TIME);
                    Optional<LogOperation> packageLogOperationOpt = logOperationService.findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(placeId, billingCard.getCardId(), packageOperationTypes, startTime, logLogin.getLoginTime());
                    if (packageLogOperationOpt.isPresent() && !packageTimeReserveOpt.isPresent()) {
                        // 操作了预包时,并且没有未使用的预包时,说明这个预包时已经被使用了
                        totalCost = totalCost - packageLogOperationOpt.get().getCost();
                    }
                }
            }

            log.info("::::::当前总消费::::::totalCost:::::" + totalCost);
            if (packageTimeReserveOpt.isPresent()) {
                // 有未使用的预包时，看是否是在本次登入结账的时间内生成的
                PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get();
                if (packageTimeReserve.getCreated().isAfter(logLogin.getLoginTime()) &&
                        packageTimeReserve.getCreated().isBefore(logoutTime) && packageTimeReserve.getPackagePayFlag() != 1) {
                    totalCost = totalCost - packageTimeReserve.getPrice();
                    log.info("::::::有预包时未使用::::::ruleId:::::" + packageTimeReserve.getRuleId() + "price::::::::" + packageTimeReserve.getPrice());
                }
            }
            Duration duration = Duration.between(logLogin.getLoginTime(), logoutTime); // 计算在线时间
            onlineTime = duration.toMinutes();
            log.info("::::::当前状态::::::finished:::::" + billingOnline.getFinished() + "logoutFlag::::::::" + logoutFlag);
            // 当前未结账，执行结账
            if (billingOnline.getFinished() == 0) {
                totalCost = totalCost == 0 ? billingOnline.getDeduction() : totalCost; // totalCost=0 本地没有消费
                billingOnline.setUpdated(logoutTime);
                billingOnline.setDeduction(totalCost);

                logLogin.setUpdated(LocalDateTime.now());
                logLogin.setConsumptionTotal(totalCost);
                logLogin.setOnlineTime(onlineTime);
                logLogin.setTotalAccount(currBalance);

                // 处理积分
                if (rewardPoints == 1 && !"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                    int points = rewardPointsRuleService.getRewardPointsNum(billingCard.getPlaceId(), totalCost, ExchangePointsType.LOGOUT);
                    if (points > 0) {
                        Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
                        if (billingCardOpt.isPresent()) {
                            BillingCard pointsCard = billingCardOpt.get();
                            pointsCard.setPoints(pointsCard.getPoints() + points);
                            billingCardService.save(pointsCard);
                            logPointsService.addLogPointsOperation(ExchangePointsType.LOGOUT, points, totalCost, pointsCard, logShift, false);
                        }
                    }
                }

                try {

                    if (logoutFlag == 0) {
                        // 主动结账结账，中心在线
                        billingOnline.setFinished(1);
                        billingOnline.setRemark("离线结账");

                        logLogin.setLogoutTime(logoutTime);
                        logLogin.setLogoutOperationCreater("");
                        logLogin.setLogoutOperationCreaterName("离线结账");
                        logLogin.setLogoutType(0);
                        logHbService.stopBilling(placeId, clientId);
                        // 添加操作记录
                        if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                            Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
                            if (!newCard.isPresent()) {
                                return billingCard;
                            }
                            billingCard = newCard.get(); // 查询最新的余额
                        }
                        saveOfflineLogOperation(logoutTime, billingCard, OperationType.LOGOUT, logLogin.getConsumptionTotal(), presentCost, clientId, logShift, logLogin.getLoginId());
                        if (billingCard.getDeleted() == 0 && "1000".equals(billingCard.getCardTypeId())) {
                            // 写退款记录
                            LogRefund logRefund = logRefundService.addLogRefund(placeId, billingCard, logShift, SourceType.CASHIER, billingCard.getCashAccount());
                            logRefundService.save(logRefund);

                            logOperationService.addCancellationLogOperation(SourceType.CASHIER, billingCard, logShift);
                            billingCard.setDeleted(1);
                            billingCard.setCashAccount(0);
                            billingCard.setPresentAccount(0);
                            billingCardService.save(billingCard);
                        }
                    }

                    // 查询是否在包间上机
                    Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, billingCard.getCardId());
                    if (logRoomOpt.isPresent()) {
                        // 是在包间上机
                        LogRoom logRoom = logRoomOpt.get();
                        logRoom.setFinishedTime(logoutTime);
                        logRoom.setFinished(1);
                        logRoomService.save(logRoom);
                    }

                    // 如果是包时结账
                    if (billingOnline.getPackageFlag() > 0) {
                        // 使用完的包时规则状态变为2
                        packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());
                    }

                    billingOnlineService.save(billingOnline);
                    logLoginService.save(logLogin);
                    saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineOperation.getOfflineDataId(), billingCard.getIdNumber(), OperationType.LOGOUT, totalCost, 0,
                            offlineOperation.getShiftId(), 1, logoutTime);

                } catch (Exception e) {
                    log.info(e.getMessage());
                    saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineOperation.getOfflineDataId(), billingCard.getIdNumber(), OperationType.LOGOUT, totalCost, 0,
                            offlineOperation.getShiftId(), 0, logoutTime);
                }

            } else { // 中心已经结账，更新数据
                logLogin.setUpdated(LocalDateTime.now());
                logLogin.setConsumptionTotal(totalCost);
                logLogin.setOnlineTime(onlineTime);
                logLogin.setTotalAccount(currBalance);
                logLogin.setLogoutType(0);

                logOperationLogin.setCost(totalCost);
                logOperationLogin.setDetails("离线结账");
                logOperationLogin.setPresent(presentCost);
                logOperationLogin.setPresentBalance(offlineOperation.getPresentBalance());
                logOperationLogin.setCashBalance(offlineOperation.getCashBalance());

                try {
                    if (logoutFlag == 1) {
                        log.info("中心已经结账:::::伪结账登入id::::" + offlineOperation.getLoginId());
                        LocalDateTime nextTime = LocalDateTime.parse(offlineOperation.getNextTime(), dtFormatter); // 下一次扣费时间
                        logLogin.setLogoutTime(null);
                        logLogin.setLogoutOperationCreater("");
                        logLogin.setLogoutOperationCreaterName("");

                        billingOnline.setFinished(0);
                        billingOnline.setNextTime(nextTime);
                        billingOnlineService.save(billingOnline);

                        // 删除中心已经生成的记录
                        logOperationService.delete(logOperationLogin);
                    } else {
//                        logOperationService.save(logOperationLogin);
                        // 处理离线结账记录补日志
                        try {
                            logOperationService.save(logOperationLogin);
                        } catch (Exception et) {
                            logOperationLogin.setRemark(logOperationLogin.getRemark() + "，防御性数据1");
                            try {
                                logOperationService.save(logOperationLogin);
                            } catch (Exception ex) {
                                logOperationLogin.setRemark(logOperationLogin.getRemark() + "，防御性数据2");
                                logOperationService.save(logOperationLogin);
                            }
                        }
                        if (billingCard.getDeleted() == 0 && "1000".equals(billingCard.getCardTypeId())) {
                            logOperationService.addCancellationLogOperation(SourceType.CASHIER, billingCard, logShift);
                            billingCard.setDeleted(1);
                            billingCard.setCashAccount(0);
                            billingCard.setPresentAccount(0);
                            billingCardService.save(billingCard);
                        }
                    }
                    log.info("中心已经结账:::::结账login::::" + logLogin.getLogoutTime() + ":::::finished:::::" + billingOnline.getFinished());
                    logLoginService.save(logLogin);
                    saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineOperation.getOfflineDataId(), billingCard.getIdNumber(), OperationType.LOGOUT, totalCost, 0,
                            offlineOperation.getShiftId(), 1, logoutTime);
                } catch (Exception e) {
                    log.info(e.getMessage());
                    saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineOperation.getOfflineDataId(), billingCard.getIdNumber(), OperationType.LOGOUT, totalCost, 0,
                            offlineOperation.getShiftId(), 0, logoutTime);
                }
            }

            // 如果是包时结账
            if (billingOnline.getPackageFlag() > 0) {
                // 使用完的包时规则状态变为2
                packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());
            }
        }
        return billingCard;
    }

    /**
     * 处理离线充值
     *
     * @param billingCard
     * @param shiftId
     * @param cashAmount
     * @param presentAmount
     * @param offlineDataId
     * @param requestId
     * @param operationTime
     * @return
     */
    private BillingCard synchronizeLogTopup(BillingCard billingCard, String shiftId, int cashAmount, int presentAmount,
                                            String offlineDataId, String requestId, LocalDateTime operationTime) {
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(billingCard.getPlaceId(),
                shiftId, 0);
        if (!optLogShift.isPresent()) {
            return billingCard;
        }
        LogShift logShift = optLogShift.get();

        try {
            billingCard = billingCardService.billingCardTopup(cashAmount, presentAmount, billingCard.getPlaceId(), billingCard.getCardId(), billingCard.getCardTypeId(), logShift,0,SourceType.CASHIER);
            saveOfflineLogOperation(operationTime, billingCard, OperationType.TOPUP, cashAmount, presentAmount, "", logShift, "");
//			logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, cashAmount, presentAmount,
//					billingCard, null, logShift, null);
            saveTopup(billingCard, logShift, cashAmount, operationTime,0);
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.TOPUP, cashAmount, presentAmount,
                    logShift.getShiftId(), 1, operationTime);
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.TOPUP, cashAmount, presentAmount,
                    logShift.getShiftId(), 0, operationTime);
        }

        Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndCardId(billingCard.getPlaceId(), billingCard.getCardId());
        return billingCardOpt.orElse(billingCard);
    }

    /**
     * 处理离线登录
     *
     * @param billingCard
     * @param offlineOperation
     * @param requestId
     * @return
     */
    private BillingCard synchronizeLogLogin(BillingCard billingCard, SynOfflineOperation offlineOperation, String requestId, Map<String, String> loginMap) {
        String placeId = billingCard.getPlaceId();
        String cardTypeId = billingCard.getCardTypeId();
        String areaId = offlineOperation.getAreaId();
        String shiftId = offlineOperation.getShiftId();
        String clientId = offlineOperation.getClientId();
        String offlineDataId = offlineOperation.getOfflineDataId();
        String offlineLoginId = offlineOperation.getOfflineLoginId();
        int cost = offlineOperation.getCost();
        String packageRuleId = offlineOperation.getRuleId();
        if (StringUtils.isEmpty(offlineOperation.getOperationTime()) || StringUtils.isEmpty(offlineOperation.getNextTime())) {
            // 登入时间、下次扣费时间 必填
            log.info("本地模式上传数据:::::登入时间、下次扣费时间存在空值");
            return billingCard;
        }
        LocalDateTime loginTime = LocalDateTime.parse(offlineOperation.getOperationTime(), dtFormatter); // 登入时间
        LocalDateTime nextTime = LocalDateTime.parse(offlineOperation.getNextTime(), dtFormatter); // 下一次扣费时间
        String ruleId = null;
        int price = 0;
        int packageFlag = 0;
        int packagePayFlag = 0;
        if (!StringUtils.isEmpty(packageRuleId)) {

            List<PackageTimeReserve> packageTimeReserves = packageTimeReserveService.findByPlaceIdAndRuleId(placeId, packageRuleId);
            if (StringUtils.isEmpty(packageTimeReserves) || packageTimeReserves.size() == 0) {
                log.info("本地模式上传数据预包时费率信息未找到:::::::::ruleId::::" + packageRuleId);
                return billingCard;
            }
            Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, packageRuleId);
            if (!billingRulePackageTimeOpt.isPresent()) {
                log.info("本地模式上传数据包时费率信息未找到:::::::::ruleId::::" + packageRuleId);
                return billingCard;
            }
            BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
            ruleId = packageRuleId;
            price = billingRulePackageTime.getPrice();
            packageFlag = billingRulePackageTime.getPackageFlag();
            packagePayFlag = packageTimeReserves.get(0).getPackagePayFlag();
            cost = cost + price; // 包时登入，消费就是包时价格 + 本地消费余额
        } else {
            // 费率校验
            Optional<BillingRuleCommon> optBillingRuleCommon = billingRuleCommonService.billingRuleCommons(placeId,
                    areaId, cardTypeId);
            if (!optBillingRuleCommon.isPresent()) {
                log.info("本地模式上传数据费率信息未找到:::::::::");
                return billingCard;
            }
            BillingRuleCommon billingRuleCommon = optBillingRuleCommon.get();
            ruleId = billingRuleCommon.getRuleId();
            price = CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices());
        }

        // 登入信息
        LogLogin logLogin = new LogLogin();
        logLogin.setPlaceId(placeId);
        logLogin.setClientId(clientId);
        logLogin.setCardId(billingCard.getCardId());
        logLogin.setIdNumber(billingCard.getIdNumber());
        logLogin.setCardTypeId(billingCard.getCardTypeId());
        logLogin.setLastClientId(null);
        logLogin.setLoginTime(loginTime);
        logLogin.setCreated(loginTime);
        logLogin.setTotalAccount(billingCard.getTotalAccount());
        logLogin.setLoginType(LoginType.PASSWORD_LOGIN);

        // 在线信息
        BillingOnline billingOnline = new BillingOnline();
        billingOnline.setDeduction(cost);
        billingOnline.setRuleId(ruleId);
        billingOnline.setPlaceId(placeId);
        billingOnline.setClientId(clientId);
        billingOnline.setAreaId(areaId);
        billingOnline.setCommonPrice(price);
        billingOnline.setIdName(billingCard.getIdName());
        billingOnline.setCardId(billingCard.getCardId());
        billingOnline.setCardTypeId(billingCard.getCardTypeId());
        billingOnline.setIdNumber(billingCard.getIdNumber());
        billingOnline.setCardTypeId(billingCard.getCardTypeId());
        billingOnline.setNextTime(nextTime);
        billingOnline.setBillingTime(loginTime);
        billingOnline.setFinished(0);
        billingOnline.setTimerFlag(0);
        billingOnline.setCreated(loginTime);
        billingOnline.setPackageFlag(packageFlag);
        billingOnline.setPackagePayFlag(packagePayFlag);
        try {
            LogLogin login = logLoginService.save(logLogin);
            billingOnline.setLoginId(login.getLoginId());
            billingOnlineService.save(billingOnline);
            //billingCardService.save(billingCard);
            // billingCard = billingCardService.billingCardReversal(cashCost, presentCost, billingCard);
            // 更新心跳日志表
            logHbService.startBilling(placeId, clientId);
            // 操作日志
            saveOfflineLogOperation(loginTime, billingCard, OperationType.LOGIN, cost, 0, clientId, null, login.getLoginId());
//			logOperationService.addLoginLogOperation(SourceType.CLIENT, clientId, billingCard, billingOnline, logShift,
//					logLogin);
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.LOGIN, cost, 0,
                    shiftId, 1, loginTime);
            if (offlineLoginId == null) {
                // 只有登入,没有结账
                loginMap.put(login.getLoginId(), login.getLoginId());
            } else {
                loginMap.put(offlineLoginId, login.getLoginId());
            }
            // 清理预包时记录
            if (!StringUtils.isEmpty(packageRuleId)) {
                packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingCard.getCardId(), packageRuleId);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.LOGIN, cost, 0,
                    shiftId, 0, loginTime);
        }
        return billingCard;
    }

    /**
     * 离线登入----离线结账
     * @param billingCard
     * @param offlineOperation
     * @param requestId
     * @param loginMap
     * @return
     */
    public BillingCard synchronizeLogout(BillingCard billingCard, int cashAccount, int presentAccount, SynOfflineOperation offlineOperation, String requestId, Map<String, String> loginMap, int rewardPoints) {
        String placeId = billingCard.getPlaceId();
        String shiftId = offlineOperation.getShiftId();
        String offlineDataId = offlineOperation.getOfflineDataId();
        int cost = offlineOperation.getCost();
        String offlineLoginId = offlineOperation.getOfflineLoginId();
        if (StringUtils.isEmpty(offlineOperation.getOperationTime())) {
            // 登入时间、下次扣费时间 必填
            log.info("本地模式上传数据:::::离线结账时间存在空值");
            return billingCard;
        }

        // 班次校验
        LogShift logShift = null;
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(billingCard.getPlaceId(),
                shiftId, 0);
        if (optLogShift.isPresent()) {
            logShift = optLogShift.get();
        }

        LocalDateTime logoutTime = LocalDateTime.parse(offlineOperation.getOperationTime(), dtFormatter); // 离线结账时间
        if (!loginMap.containsKey(offlineLoginId) || StringUtils.isEmpty(loginMap.get(offlineLoginId))) {
            log.info("本地模式上传数据:::::离线结账操作未找到对应的loginId");
            return billingCard;
        }
        String loginId = loginMap.get(offlineLoginId);
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findOnlineByPlaceIdAndCardIdAndLoginId(placeId,
                billingCard.getCardId(), loginId);
        if (!optBillingOnline.isPresent()) {
            log.info("本地模式上传数据:::::离线结账操作未找到对应的在线信息");
            return billingCard;
        }
        BillingOnline billingOnline = optBillingOnline.get();
        Optional<LogLogin> optLogLogin = logLoginService.findByPlaceIdAndLoginId(placeId, loginId, billingOnline.getBillingTime());
        if (!optLogLogin.isPresent()) {
            log.info("本地模式上传数据:::::离线结账操作未找到对应的上机记录");
            return billingCard;
        }
        LogLogin logLogin = optLogLogin.get();

        // 扣除费用
        int cashCost = 0;
        int presentCost = 0;
        if ((cashAccount + presentAccount) < cost) {
            cashCost = cashAccount;
            presentCost = presentAccount;
            cashAccount = 0;
            presentAccount = 0;
        }
        if (cashAccount >= cost) {
            cashCost = cost;
            cashAccount = cashAccount - cost;
        } else {
            cashCost = cashAccount;
            presentCost = cost - cashCost;
            cashAccount = 0;
            presentAccount = presentAccount - presentCost;
        }

        // 结账
        cost = billingOnline.getDeduction() + cost;
        billingOnline.setDeduction(cost);
        billingOnline.setFinished(1);
        billingOnline.setUpdated(logoutTime);
        billingOnline.setRemark("离线结账");

        logLogin.setLogoutTime(logoutTime);
        logLogin.setOnlineTime(Duration.between(logLogin.getLoginTime(), logoutTime).toMinutes());
        logLogin.setConsumptionTotal(cost);
        logLogin.setLogoutOperationCreater("");
        logLogin.setLogoutOperationCreaterName("离线结账");
        logLogin.setLogoutType(0);
        logLogin.setTotalAccount(cashAccount + presentAccount);

        // 积分
        int points = 0;
        if (rewardPoints == 1 && !"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            points = rewardPointsRuleService.getRewardPointsNum(billingCard.getPlaceId(), cost, ExchangePointsType.LOGOUT);
        }

        try {

            billingCard.setActiveTime(null);
            billingCard.setCashAccount(cashAccount);
            billingCard.setPresentAccount(presentAccount);
            billingCard.setPoints(billingCard.getPoints() + points);

            billingOnlineService.save(billingOnline);
            logLoginService.save(logLogin);

            // 如果是包时结账
            if (billingOnline.getPackageFlag() > 0) {
                // 使用完的包时规则状态变为2
                packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());
            }

            // 积分变更记录
            if (points > 0) {
                logPointsService.addLogPointsOperation(ExchangePointsType.LOGOUT, points, cost, billingCard, logShift, false);
            }

            if (cashCost == 0 && presentCost == 0) {
                cashCost = cost; //包时内结账的，本地消费是0 但是要把包时的费用记录
            }
            saveOfflineLogOperation(logoutTime, billingCard, OperationType.LOGOUT, cashCost, presentCost, billingOnline.getClientId(), logShift, logLogin.getLoginId());
            if (billingCard.getDeleted() == 0 && "1000".equals(billingCard.getCardTypeId())) {
                // 写退款记录
                LogRefund logRefund = logRefundService.addLogRefund(placeId, billingCard, logShift, SourceType.CASHIER, billingCard.getCashAccount());
                logRefundService.save(logRefund);

                // 写销卡记录
                logOperationService.addCancellationLogOperation(SourceType.CASHIER, billingCard, logShift);
                billingCard.setDeleted(1);
                billingCard.setCashAccount(0);
                billingCard.setPresentAccount(0);
            }

            // 查询是否在包间上机
            Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, billingCard.getCardId());
            if (logRoomOpt.isPresent()) {
                // 是在包间上机
                LogRoom logRoom = logRoomOpt.get();
                logRoom.setFinishedTime(logoutTime);
                logRoom.setFinished(1);
                logRoomService.save(logRoom);
            }

            billingCardService.save(billingCard);
            // 查询此时这个客户端中心最新的在线信息是不是同一个人,如果后处理的这条记录，stopBilling之后会有问题
            Optional<BillingOnline> onlineOptional = billingOnlineService.findTop1ByPlaceIdAndClientId(placeId, billingOnline.getClientId());
            if (!onlineOptional.isPresent() || onlineOptional.get().getCardId().equals(billingCard.getCardId())) {
                logHbService.stopBilling(placeId, billingOnline.getClientId());
            }
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.LOGOUT, cashCost, presentCost,
                    shiftId, 1, logoutTime);
        } catch (Exception e) {
            log.info(e.getMessage());
            saveLogSynOfflineData(requestId, billingCard.getPlaceId(), offlineDataId, billingCard.getIdNumber(), OperationType.LOGOUT, cashCost, presentCost,
                    shiftId, 0, logoutTime);
        }
        loginMap.remove(offlineLoginId);
        return billingCard;
    }

    /**
     * 保存操作日志记录
     *
     * @param operationTime
     * @param billingCard
     * @param operationType
     * @param cashCost
     * @param presentCost
     * @param clientId
     * @param logShift
     */
    public void saveOfflineLogOperation(LocalDateTime operationTime, BillingCard billingCard, OperationType operationType,
                                        int cashCost, int presentCost, String clientId, LogShift logShift, String loginId) {
        String details = "";
        if (operationType == OperationType.TOPUP) {
            details = "离线充值" + Double.valueOf(cashCost) / 100 + "元，奖励" + Double.valueOf(presentCost) / 100 + "元";
        } else if (operationType == OperationType.LOGIN) {
            details = "离线登入";
        } else if (operationType == OperationType.CREATE_CARD) {
            details = "离线开卡";
        } else if (operationType == OperationType.LOGOUT) {
            details = "离线结账";
        } else if (operationType == OperationType.CONVERT_BILLING_RULE) {
            details = "离线过程转换计费规则";
        }

        LogOperation log = new LogOperation();
        log.setCreated(operationTime.minusNanos(operationTime.getNano())); // 不要毫秒
        log.setSourceType(operationType == OperationType.LOGIN ? SourceType.CLIENT : SourceType.CASHIER);
        log.setOperationType(operationType);
        log.setCost(cashCost);
        log.setPresent(presentCost);
        log.setPlaceId(billingCard.getPlaceId());
        log.setCardId(billingCard.getCardId());
        log.setIdNumber(billingCard.getIdNumber());
        log.setIdName(billingCard.getIdName());
        log.setCardId(billingCard.getCardId());
        log.setCardTypeId(billingCard.getCardTypeId());
        log.setCardTypeName(billingCard.getCardTypeName());
        log.setCashBalance(billingCard.getCashAccount());
        log.setPresentBalance(billingCard.getPresentAccount());
        log.setRemark("离线同步数据");
        log.setDetails(details);
        log.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        log.setCreaterName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        log.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        log.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
        log.setClientId(clientId);
        log.setLoginId(loginId);
//        logOperationService.save(log);
        // 离线保存操作日志记录
        try {
            logOperationService.save(log);
        } catch (Exception e) {
            log.setRemark(log.getRemark()+ "，防御性数据1");
            try {
                logOperationService.save(log);
            } catch (Exception ex) {
                log.setRemark(log.getRemark()+ "，防御性数据2");
                logOperationService.save(log);
            }
        }
    }



    /**
     * 保存激活记录（改动点：由操作记录表转到激活日志表）
     *
     * @param operationTime
     * @param billingCard
     * @param operationType
     * @param logShift
     */
    public void saveOfflineLogActivate(LocalDateTime operationTime, BillingCard billingCard, OperationType operationType,
                                         LogShift logShift) {
        LogActivate log = new LogActivate();
        log.setCreated(operationTime.minusNanos(operationTime.getNano())); // 不要毫秒
        log.setSourceType(operationType == OperationType.LOGIN ? SourceType.CLIENT : SourceType.CASHIER);
        log.setOperationType(operationType);
        log.setPlaceId(billingCard.getPlaceId());
        log.setCardId(billingCard.getCardId());
        log.setIdNumber(billingCard.getIdNumber());
        log.setIdName(billingCard.getIdName());
        log.setCardId(billingCard.getCardId());
        log.setCardTypeId(billingCard.getCardTypeId());
        log.setCardTypeName(billingCard.getCardTypeName());
        log.setCashBalance(billingCard.getCashAccount());
        log.setPresentBalance(billingCard.getPresentAccount());
        log.setRemark("离线同步数据");
        log.setDetails("");
        log.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        log.setCreaterName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        log.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        log.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
        logActivateService.save(log);
        // 离线保存操作日志记录
    }


    public static void main(String[] args) {

        String request = "{\"offlineCards\":[{\"cardId\":\"20685\",\"synLogLogin\":null,\"synLogTopup\":null,\"synOfflineLogout\":{\"cashBalance\":514600,\"id\":1,\"loginId\":0,\"logoutTime\":\"2022-08-22 11:59:53\",\"presentBalance\":185900}}],\"placeId\":\"**************\",\"sign\":\"AADB3DC1B307260B54DB4D8EAAF8B74B\",\"timestamp\":**********}";

        String offlineCards = request.substring(request.indexOf("["), request.lastIndexOf("]") + 1);
        System.out.println(offlineCards);

//		Gson gson = new Gson();
//
//		SynOfflineLogout synOfflineLogout1 = new SynOfflineLogout();
//		synOfflineLogout1.setCashBalance(1000);
//		synOfflineLogout1.setPresentBalance(200);
//		synOfflineLogout1.setLoginId("**********");
//		synOfflineLogout1.setLogoutTime("2022-08-15 10:11:12");
//
//		SynLogTopup synLogTopup1 = new SynLogTopup();
//		synLogTopup1.setTopup(2000);
//		synLogTopup1.setPresent(500);
//		synLogTopup1.setShiftId("shiftId");
//		synLogTopup1.setTopupTime("2022-08-15 10:11:12");
//		SynLogTopup synLogTopup2 = new SynLogTopup();
//		synLogTopup2.setTopup(5000);
//		synLogTopup2.setPresent(1000);
//		synLogTopup2.setShiftId("shiftId");
//		synLogTopup2.setTopupTime("2022-08-15 11:23:34");
//		List<SynLogTopup> logTopups = new ArrayList<SynLogTopup>();
//		logTopups.add(synLogTopup1);
//		logTopups.add(synLogTopup2);
//
//		SynLogLogin sysLogLogin = new SynLogLogin();
//		sysLogLogin.setLoginTime("2022-08-15 09:08:07");
//		sysLogLogin.setClientId("1001");
//		sysLogLogin.setAreaId("2001");
//		sysLogLogin.setNextTime("2022-08-15 09:10:11");
//		sysLogLogin.setCost(1000);
//		sysLogLogin.setLogoutTime("2022-08-15 13:12:11");
//		List<SynLogLogin> logLogins = new ArrayList<SynLogLogin>();
//		logLogins.add(sysLogLogin);
//
//		List<SynOfflineCard> offlineCards = new ArrayList<SynOfflineCard>();
//		SynOfflineCard card = new SynOfflineCard();
//		card.setCardId("9876543210");
//		card.setSynOfflineLogout(synOfflineLogout1);
//		card.setSynLogLogin(logLogins);
//		card.setSynLogTopup(logTopups);
//		offlineCards.add(card);
//
//		SynRequest synRequest = new SynRequest();
//
//		synRequest.setPlaceId("**********");
//		synRequest.setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));
//		synRequest.setSign("32位md5");
//		synRequest.setOfflineCards(offlineCards);
//
//		System.out.println(gson.toJson(synRequest));

    }

}
