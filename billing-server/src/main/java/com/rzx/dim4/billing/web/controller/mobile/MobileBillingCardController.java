package com.rzx.dim4.billing.web.controller.mobile;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.bo.billing.TopupRuleBO;
import com.rzx.dim4.base.bo.payment.PaymentOrderBO;
import com.rzx.dim4.base.bo.payment.PaymentRequestBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.BizServer;
import com.rzx.dim4.base.enums.ClientBusinessIds;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.OrderStatus;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.impl.MobileQueryTopupRuleNewServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.CashierQueryBillingCardServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.CashierReversalBillingCardServiceImpl;
import com.rzx.dim4.billing.service.impl.client.ClientChangePasswordServiceImpl;
import com.rzx.dim4.billing.service.impl.client.ClientQueryTopupRuleNewServiceImpl;
import com.rzx.dim4.billing.service.impl.client.ClientQueryTopupRuleServiceImpl;
import com.rzx.dim4.billing.service.util.RegionChnCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/billing/mobile/billing/card")
public class MobileBillingCardController {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    ClientChangePasswordServiceImpl changePasswordServiceImpl;

    @Autowired
    CashierQueryBillingCardServiceImpl queryBillingCardService;

    @Autowired
    ClientQueryTopupRuleServiceImpl queryTopupRuleService;

    @Autowired
    ClientQueryTopupRuleNewServiceImpl queryTopupRuleNewService;

    @Autowired
    MobileQueryTopupRuleNewServiceImpl mobileTopupRuleNewService;

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    TopupRuleService topupRuleService;

    @Autowired
    CashierReversalBillingCardServiceImpl reversalBillingCardServiceImpl;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    PaymentServerService paymentServerService;


    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    LogLoginService logLoginService;

    @Autowired
    BillingOnlineService billingOnlineService;

    @Autowired
    PlaceBizConfigService placeBizConfigService;

    @Autowired
    NotifyServerService notifyServerService;

    @Autowired
    WeChatService weChatService;

    @Autowired
    RegionChnCodeUtil regionChnCodeUtil;

    @GetMapping("/queryConsumeBalance")
    public GenericResponse<ListDTO<BillingCardBO>> queryConsumeBalance(@RequestParam("idNumber") String idNumber) {
        // 1 校验参数
        if (StringUtils.isEmpty(idNumber)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        // 2 查询该身份证号所在所有网吧的卡信息(可用的)
        List<BillingCard> billingCardList = billingCardService.findByIdNumber(idNumber);
        if (CollectionUtils.isEmpty(billingCardList)) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        List<BillingCardBO> billingCardBOList = billingCardList.stream().map(e -> {
            return e.toBO();
        }).collect(Collectors.toList());
        return new GenericResponse<>(new ListDTO<>(billingCardBOList));
    }

    @PostMapping("/changePassword")
    public GenericResponse<?> changePassword(@RequestParam("placeId") String placeId,
                                             @RequestParam("idNumber") String idNumber, @RequestParam("oldPassword") String oldPassword,
                                             @RequestParam("newPassword") String newPassword) {
        // 1 封装参数
        List<String> paramList = new ArrayList<>();
        paramList.add(placeId);
        paramList.add(idNumber);
        paramList.add(oldPassword);
        paramList.add(newPassword);
        // 调用方法
        GenericResponse<?> response = changePasswordServiceImpl.doService(paramList);
        return response;
    }

    @GetMapping("/cardDetails")
    public GenericResponse<?> cardDetails(@RequestParam("placeId") String placeId,
                                          @RequestParam("idNumber") String idNumber) {
        // 1 封装参数
        List<String> paramList = new ArrayList<>();
        paramList.add(placeId);
        paramList.add(idNumber);

        // 2 调用方法
        GenericResponse<?> response = queryBillingCardService.doService(paramList);
        return response;
    }

    @GetMapping("/queryTopupRules")
    public GenericResponse<?> queryTopupRules(@RequestParam String placeId, @RequestParam String cardTypeId) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardTypeId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        List<String> paramList = new ArrayList<>();
        paramList.add(placeId);
        paramList.add(cardTypeId);
        GenericResponse<?> result = queryTopupRuleService.doService(paramList);
        return result;
    }

    /**
     * 新充值规则版本
     * 公众号和小程序在客户端扫码后获取充值规则
     */
    @GetMapping("/queryTopupRulesNew")
    public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRulesNew(@RequestParam String placeId, @RequestParam String cardTypeId) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardTypeId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        GenericResponse<ListDTO<TopupRuleBO>> result = mobileTopupRuleNewService.queryTopupRulesNew(placeId, cardTypeId);
        return result;
    }

    /**
     * 新充值规则版本
     * 公众号、小程序查询自定义金额赠送额度
     */
    @GetMapping("/queryTopupGift")
    public GenericResponse<ObjDTO<TopupRuleBO>> queryTopupGift(@RequestParam String placeId, @RequestParam String idNumber, @RequestParam int amount, @RequestParam String cardTypeId) {
        log.info("公众号、小程序 queryTopupGift,placeId:::{},idNumber:::{},amount:::{},cardTypeId:::{}", placeId, idNumber, amount, cardTypeId);
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber) || StringUtils.isEmpty(amount)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        TopupRule activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, cardTypeId, amount, 3);
        if (!ObjectUtils.isEmpty(activedRule)) {
            return new GenericResponse<>(new ObjDTO<>(activedRule.toBO()));
        }
        return new GenericResponse<>(ServiceCodes.BILLING_TOPUP_NOT_FOUND);
    }

    @GetMapping("/refund")
    public GenericResponse<?> refund(@RequestHeader(value = "request_ticket") String requestTicket,
                                     @RequestParam String placeId, @RequestParam String cardId, @RequestParam int amount) {
        if (!stringRedisTemplate.delete(requestTicket)) { //
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        if (amount <= 0) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        List<String> paramList = new ArrayList<>();
        paramList.add(placeId);
        paramList.add(cardId);
        paramList.add(String.valueOf(amount));
        GenericResponse<?> result = reversalBillingCardServiceImpl.doService(paramList);
        return result;
    }

    /**
     * 微信小程序充值，创建订单
     *
     * @param requestTicket
     * @param placeId
     * @param idNumber
     * @param amount
     * @return
     */
    @GetMapping("/createOrderPay")
    public GenericResponse<ObjDTO<PaymentResultBO>> createOrderPayByAppletOfWeChat(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String idNumber,
            @RequestParam int amount,
            @RequestParam String openId, @RequestParam(required = false) String appId) {
        log.info("createOrderPayByAppletOfWeChat, requestTicket:{}, placeId:{}, idNumber:{}, amount:{}, openId:{}, appId:{}",
                requestTicket, placeId, idNumber, amount, openId, appId);

        return weChatService.createOrderPayByAppletOfWeChat(requestTicket, placeId, idNumber, amount, openId, appId);
    }

    /**
     * 微信公众号充值，创建订单
     *
     * @param placeId  网吧Id
     * @param idNumber 身份证号/用户卡Id
     * @param amount   充值金额
     * @return 是否创建成功，及对应状态码
     */
    @GetMapping("/create")
    public GenericResponse<SimpleDTO> wechatTopupOrderCreate(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String placeId,
            @RequestParam String idNumber,
            @RequestParam int amount) {

        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber) || amount == 0) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        GenericResponse<ObjDTO<PlaceConfigBO>> responsePlaceConfig = placeServerService
                .findPlaceConfigByPlaceId(placeId);
        if (responsePlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = responsePlaceConfig.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }
        GenericResponse<ObjDTO<PlaceProfileBO>> placeProfileResponse = placeServerService.findByPlaceId(placeId);
        if (placeProfileResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.GET_PLACE_INFO_FAIL);
        }
        PlaceProfileBO placeProfileBO = placeProfileResponse.getData().getObj();
        if (placeProfileBO.getIsRegistered() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_NOT_REGISTER_PAY_AMOUNT);
        }

        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber,
                0);
        if (!optBillingCard.isPresent()) {
            return new GenericResponse<>(ServiceCodes.GET_CARD_INFO_FAIL);
        }
        BillingCard billingCard = optBillingCard.get();
        if ("1000".equals(billingCard.getCardTypeId())) {
            return new GenericResponse<>(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
        }

        // 获取充值赠送规则
        TopupRule activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(),
                amount, 3);

        // 获取场所计费配置
//        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
//        int dayNumConfig = placeBizConfig.getMemberDayNum();

        // 会员日生效次数校验
        // 20230811注释取消改功能校验
//        if (!StringUtils.isEmpty(activedRule) && activedRule.getTopupType() > 1) {
//            String memberDayNum = topupRuleService.getMemberDayNum(placeId, billingCard.getCardId());
//            if (topupRuleService.getCheckMemberDayNum(memberDayNum, dayNumConfig)) {
//                return new GenericResponse<>(ServiceCodes.BILLING_MEMBER_DAY_TOPUP_OVER);
//            }
//        }

        // 创建支付订单
        requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(amount);
        requestBO.setOrderDesc(placeProfileBO.getDisplayName() + "网费充值" + (amount / 100.00) + "元");
        requestBO.setStoreNo(placeId);
        requestBO.setPayType(PayType.WECHAT_MP.name());
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setBizType("chongzhi");
        requestBO.setIdNumber(idNumber);
        requestBO.setPlaceId(placeId);

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeId));
        requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);

        if (!response.isResult()) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();

        if (paymentResultBO == null || StringUtils.isEmpty(paymentResultBO.getPayUrl())
                || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_CREATE_FAIL);
        }

        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());
        logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
        logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
        logTopup.setCashAmount(amount);
        if (activedRule == null) {
            logTopup.setPresentAmount(0);
        } else {
            if (activedRule.getTopupMode() == 1 && amount >= activedRule.getAmount()) {
                logTopup.setPresentAmount(amount);
            } else {
                logTopup.setPresentAmount(activedRule.getPresentAmount()); // 赠送金额
            }
        }
        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);
        logTopup.setPayType(PayType.WECHAT_MP);
        logTopup.setSourceType(SourceType.WECHAT);
        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setStatus(0);
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);
        logTopup.setPayUrl(paymentResultBO.getPayUrl());

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        logTopupService.save(logTopup);

        return new GenericResponse<>(new SimpleDTO(paymentResultBO.getPayUrl()));
    }

    /**
     * @param requestTicket 校验参数
     * @param orderId       订单id
     * @return
     * @see com.rzx.dim4.base.service.feign.BillingServerService#queryWechatTopupOrder(String, String)
     */
    @GetMapping("/query")
    public GenericResponse<ObjDTO<PaymentOrderBO>> queryWechatTopupOrder(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                         @RequestParam String orderId) {
        log.info("queryWechatTopupOrder request_ticket:{}, orderId:{}", requestTicket, orderId);

        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        if (StringUtils.isEmpty(orderId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Optional<LogTopup> optLogTopup = logTopupService.findByOrderId(orderId);
        if (!optLogTopup.isPresent()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        LogTopup logTopup = optLogTopup.get();
        if (logTopup.getStatus() == 1) {
            logTopup.setStatus(2);
        }

        // 查询支付信息
        GenericResponse<ObjDTO<PaymentOrderBO>> response = paymentServerService.queryPaymentOrder(orderId);
        if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        PaymentOrderBO paymentOrderBO = response.getData().getObj();

        if (logTopup.getStatus() != 3 && paymentOrderBO.getStatus() == OrderStatus.SUCCEED.getValue()) {
            log.info("微信端充值成功，更新充值记录，logTopup:{}", new Gson().toJson(logTopup));

            billingCardService.billingCardTopupByLogTopup(logTopup);
            // TODO 针对返回结果存在优化空间
        }
        return new GenericResponse<>(new ObjDTO<>(paymentOrderBO));
    }

    /**
     * 微信端查询用户卡充值记录
     *
     * @return
     */
    @PostMapping("/queryPayList")
    public GenericResponse<PagerDTO<LogTopupBO>> queryWeChatTopupOrders(@RequestBody Map<String, Object> queryMap,
                                                                        @RequestParam(name = "size", defaultValue = "10") int size,
                                                                        @RequestParam(name = "page", defaultValue = "0") int page) {
        if (size > 100 || size < 1 || page < 0) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_QUERY);
        }
        Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "created");

        try {
            queryMap.put("deleted", "0"); // 微信端只看当前数据
            Page<LogTopup> logTopupPage = logTopupService.findAll(queryMap, pageable);
            List<LogTopupBO> logTopupBOList = logTopupPage.getContent().stream().map(LogTopup::toBO).collect(Collectors.toList());
            return new GenericResponse<>(new PagerDTO<>((int) logTopupPage.getTotalElements(), logTopupBOList));
        } catch (Exception e) {
            e.printStackTrace();
            return new GenericResponse<>(ServiceCodes.PAYMENT_BAD_QUERY);
        }
    }

    /**
     * 公众号创建会员卡
     *
     * @param requestTicket 校验
     * @param idNumber      会员卡创建信息
     * @param phoneNumber 手机号
     * @return 状态 + 成功会员卡信息
     */
    @PostMapping("/createBillingCard")
    public GenericResponse<ObjDTO<BillingCardBO>> weChatCreateBillingCard(
            @RequestHeader(value = "request_ticket") String requestTicket,
            @RequestParam String idNumber,
            @RequestParam String name,
            @RequestParam String placeId,
            @RequestParam String activeType,
            @RequestParam(required = false)String cardTypeId,
            @RequestParam(required = false,defaultValue = "") String phoneNumber) {

        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();

        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        if(StringUtils.isEmpty(cardTypeId)){
            cardTypeId = "1001";
        }
        SourceType sourceType = SourceType.WECHAT;
        if(String.valueOf(ActiveType.ALIPAY_MINI_APP.getValue()).equals(activeType)){
            sourceType = SourceType.ALIPAY;
        }
        LogShift logShift = logShiftService.getShiftId(placeId);
        String shiftId = logShift == null ? "" : logShift.getShiftId();
        BillingCardBO billingCardBO = billingCardService.commonCreateCard(placeId, shiftId, cardTypeId, idNumber, name, phoneNumber, "", "",
                "","-1",activeType, "", "", 0,0,sourceType ,LocalDateTime.now(),null,0);
        return new GenericResponse<>(new ObjDTO<>(billingCardBO));
    }
}
