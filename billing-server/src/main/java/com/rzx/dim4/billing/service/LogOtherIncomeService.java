package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.LogOtherIncome;
import com.rzx.dim4.billing.repository.LogOtherIncomeRepository;
import lombok.Synchronized;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class LogOtherIncomeService {

    @Autowired
    LogOtherIncomeRepository logOtherIncomeRepository;

    public Page<LogOtherIncome> findAll(Map<String, String> map, Pageable pageable) {
        return logOtherIncomeRepository.findAll((Specification<LogOtherIncome>) (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            Predicate p1 = cb.equal(root.get("deleted"), 0);
            andPredicateList.add(p1);

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所ID
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
            }
            // 场所IDs
            if (map.containsKey("placeIds") && !StringUtils.isEmpty(map.get("placeIds"))) { // 场所Ids
                Path<Object> path = root.get("placeId");
                String[] placeIdArr = map.get("placeIds").split(",");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> placeIds = Arrays.asList(placeIdArr);
                placeIds.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 班次ID
                andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
            }

            if (map.containsKey("createrName") && !StringUtils.isEmpty(map.get("createrName"))) { // 操作人姓名
                andPredicateList.add(cb.like(root.get("createrName"), "%" + map.get("createrName") + "%"));
            }

            if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {// 开始时间
                LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {// 结束时间
                LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                andPredicateList
                        .add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
            }
            if (map.containsKey("type") && !StringUtils.isEmpty(map.get("type"))) {// 变更类型
                andPredicateList.add(cb.equal(root.get("type").as(int.class), map.get("type")));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        }, pageable);
    }

    /**
     * 按班次-类型-统计金额
     *
     * @param placeId
     * @param shiftId
     * @param type
     * @return
     */
    public int sumIncomeByShiftId(String placeId, String shiftId, int type) {
        Integer result = logOtherIncomeRepository.sumIncomeByShiftId(placeId, shiftId, type);
        return result == null ? 0 : result;
    }

    /**
     * 构建其他收入记录id
     * @return
     */
    @Synchronized
    public synchronized String builderLogOtherIncomeId(String placeId) {
        int logOtherIncomeId = 70000;
        Optional<LogOtherIncome> lastLogOtherIncome = logOtherIncomeRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
        if (lastLogOtherIncome.isPresent()) {
            logOtherIncomeId = Integer.parseInt(lastLogOtherIncome.get().getLogOtherIncomeId()) + 1;
        }
        return String.valueOf(logOtherIncomeId);
    }

    /**
     * 保存
     * @param logOtherIncome
     * @return
     */
    public LogOtherIncome save (LogOtherIncome logOtherIncome) {
        return logOtherIncomeRepository.save(logOtherIncome);
    }

    /**
     * 查询这一条其他收入记录信息
     * @param placeId
     * @param logOtherIncomeId
     * @return
     */
    public Optional<LogOtherIncome> findByPlaceIdAndLogOtherIncomeId(String placeId, String logOtherIncomeId) {
        return logOtherIncomeRepository.findByPlaceIdAndLogOtherIncomeId(placeId, logOtherIncomeId);
    }

}
