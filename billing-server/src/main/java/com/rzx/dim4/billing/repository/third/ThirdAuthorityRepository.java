package com.rzx.dim4.billing.repository.third;

import com.rzx.dim4.billing.entity.third.ThirdAuthority;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface ThirdAuthorityRepository extends JpaRepository<ThirdAuthority, Long>, JpaSpecificationExecutor<ThirdAuthority> {

    Optional<ThirdAuthority> findByThirdAccountId (String thirdAccountId);

}
