package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.Map;

@Service
public class BossDataTransformService {

    public static final String SALT_KEY = "#rwy#cloudy#";

   // private static final String BOSS_API_URL = "http://10.10.109.136:8088/dataDistribution/open/api/barinfo/"; // 测试地址

    private static final String BOSS_API_URL = "http://api.yun.topfreeweb.net/dataDistribution/open/api/barinfo/"; // prod生产地址

    /**
     * 调用Boss接口
     * @param map
     * @return
     * @throws Exception
     */
    public String requestBoss (Map<String,Object> map,String url) throws Exception {
        String sortMap = KSort(map);
        String sign = getMD5(sortMap + SALT_KEY);
        map.put("sign",sign);

        // 组装请求参数
        Gson gson = new Gson();
        String param = gson.toJson(map);

        // 发送请求
        HttpPost httppost = new HttpPost(BOSS_API_URL+url);
        HttpEntity entity = new StringEntity(param, Consts.UTF_8);
        httppost.setEntity(entity);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = httpclient.execute(httppost);
        HttpEntity responseEntity = response.getEntity();
        String responseString = EntityUtils.toString(responseEntity);
        if (responseString == null || responseString.isEmpty()) {
            return null;
        }
        return responseString;
    }

    /**
     * 参数排序
     * @param map
     * @return
     */
    public static String KSort(Map<String, Object> map) {
        StringBuffer sb = new StringBuffer();
        map.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .forEachOrdered(x -> sb.append(x.getKey() + "=" + x.getValue() + "&"));
        return sb.toString().substring(0, sb.length() - 1);
    }

    /**
     * 生成签名
     * @param text
     * @return
     * @throws Exception
     */
    public static String getMD5(String text) throws Exception {
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] md5Bytes = messageDigest.digest(text.getBytes("UTF-8"));
        StringBuffer hexValue = new StringBuffer(32);
        for (int i = 0; i < md5Bytes.length; i++) {
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
    }

}
