package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.TempDuDuNiuUser;
import com.rzx.dim4.billing.repository.TempDuDuNiuUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TempDuDuNiuUserService {

    @Autowired
    TempDuDuNiuUserRepository tempDuDuNiuUserRepository;

    public Optional<TempDuDuNiuUser> findTop1ByPlaceId (String placeId) {
        return tempDuDuNiuUserRepository.findTop1ByPlaceIdAndDeletedOrderByIdDesc(placeId,0);
    }

    public List<TempDuDuNiuUser> findByPlaceIdAndCardIdShowAndNameShow(String placeId, String cardIdShow, String nameShow) {
        return tempDuDuNiuUserRepository.findByPlaceIdAndCardIdShowAndNameShowAndDeleted(placeId, cardIdShow, nameShow, 0);
    }

    public List<TempDuDuNiuUser> findByPlaceIdAndCardIdIn(String placeId, List<String> cardIds) {
        return tempDuDuNiuUserRepository.findByPlaceIdAndCardIdInAndDeleted(placeId, cardIds, 0);
    }

    public void saveAll(List<TempDuDuNiuUser> tempDuDuNiuUsers) {
        tempDuDuNiuUserRepository.saveAll(tempDuDuNiuUsers);
    }

}
