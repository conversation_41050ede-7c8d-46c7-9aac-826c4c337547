package com.rzx.dim4.billing.repository;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.rzx.dim4.billing.entity.SysInterface;


public interface SysInterfaceRepository extends JpaRepository<SysInterface, Long> {

	
	@Transactional
	@Modifying
	@Query(value = "update sys_interface set times = times+1, last_use_time = ?2, avg_use_time = (avg_use_time + ?2)/2, updated = now() where indexes = ?1", nativeQuery = true)
	int update(int indexes, int useTime);
	
}
