package com.rzx.dim4.billing.service.algorithm;

import java.time.LocalDateTime;
import java.util.Arrays;

import com.rzx.dim4.billing.service.LogTopupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.PlaceBizConfigService;

/**
 * 场所开启现金包时的校验
 */
@Service
public class CashPackageTimeAlgorithm {

	@Autowired
	PlaceBizConfigService placeBizConfigService;

	@Autowired
	LogTopupService logTopupService;


	public int cashPackageTimeAlgorithm(String placeId, String cardId, int price, LocalDateTime now) {
		// 查询场所计费配置信息，如果设置了现金包时，则需要做限制
		PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
		int cashPackageTime = placeBizConfig.getCashPackageTime();
		if (cashPackageTime > 0) {
			// 查询设置的现金包时分钟数的所有充值总额

			int sumCostCashIncomeByDateTime = logTopupService.sumCostCashAmountByCardIdAndDateTime(placeId, cardId, now.minusMinutes(cashPackageTime), now, Arrays.asList(0,1));
			if (sumCostCashIncomeByDateTime < price) {
				return -1;
			}
		}
		return 0;
	}
}
