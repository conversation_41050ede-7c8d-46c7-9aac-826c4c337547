package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.PlaceChainCardType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023年04月28日 下午02:38:21
 */
public interface PlaceChainCardTypeRepository extends JpaRepository<PlaceChainCardType, Long>, JpaSpecificationExecutor<PlaceChainCardType> {

    Optional<PlaceChainCardType> findTop1ByChainIdOrderByIdDesc(String chainId);

    Optional<PlaceChainCardType> findByChainIdAndTypeNameAndDeleted(String chainId, String typeName, int deleted);

    Optional<PlaceChainCardType> findByChainIdAndCardTypeIdAndDeleted(String chainId, String cardTypeId, int deleted);

    List<PlaceChainCardType> findByChainIdAndDeleted(String chainId, int deleted);


}
