package com.rzx.dim4.billing.service.third;

import com.rzx.dim4.billing.entity.third.ThirdLogLogin;
import com.rzx.dim4.billing.repository.third.ThirdLogLoginRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ThirdLogLoginService {

    @Autowired
    ThirdLogLoginRepository thirdLogLoginRepository;

    public ThirdLogLogin save (ThirdLogLogin thirdLogLogin) {
        return thirdLogLoginRepository.save(thirdLogLogin);
    }

    public Optional<ThirdLogLogin> findByPlaceIdAndLoginId(String placeId, String loginId) {
        return thirdLogLoginRepository.findByPlaceIdAndLoginId(placeId, loginId);
    }

}
