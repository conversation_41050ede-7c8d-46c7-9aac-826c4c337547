package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.AbstractEntityBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.CashPackageTimeAlgorithm;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm.getMinute;

/**
 * 收银台自定义包时
 */
@Service
@Slf4j
public class CashierCustomPackageServiceImpl implements CoreService {

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    PackageTimeReserveService packageTimeReserveService;

    @Autowired
    BillingOnlineService billingOnlineService;

    @Autowired
    BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    LogOperationService logOperationService;

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    LogLoginService logLoginService;

    @Autowired
    CashPackageTimeAlgorithm cashPackageTimeAlgorithm;

    @Autowired
    LogRoomService logRoomService;

    @Autowired
    BillingCardDeductionService billingCardDeductionService;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    PlaceBizConfigService placeBizConfigService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() < 6) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardId = params.get(2); // 计费卡ID
        String durationTimeStr = params.get(3); // 时长
        String areaIdStr = params.get(4); // 区域
        String priceStr = params.get(5); // 价格

        String packagePayType = "0";
        LocalDateTime now = LocalDateTime.now();
        if (params.size() > 6) {
            packagePayType = params.get(6); // 包时类型 1:余额包时 2:现金包时
        }

        // 校验参数
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(shiftId) || StringUtils.isEmpty(durationTimeStr)
                || StringUtils.isEmpty(areaIdStr) || StringUtils.isEmpty(priceStr)
                || StringUtils.isEmpty(cardId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        int price = 0;
        double durationTime = 0;
        try {
            price = Integer.parseInt(priceStr);
            durationTime = Double.parseDouble(durationTimeStr);
        } catch (Exception e) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 并发加锁处理
        String billingKey = billingLockAlgorithm.cardIdAcquireLock(placeId, cardId);
        if (billingKey == null) {
            // 没拿到锁
            return new GenericResponse<>(ServiceCodes.BILLING_IN_PROGRESS);
        }
        log.info("收银台自定义包时:::::::放入锁时间::::" + LocalDateTime.now());

        // 查询是否在包间上机
        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,cardId);
        if (logRoomOpt.isPresent()) {
            LogRoom logRoom = logRoomOpt.get();
            if (logRoom.getIsMaster() == 0) {
                // 副卡在包间上机 不允许操作包时
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                return new GenericResponse<>(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
            }
        }

        List<String> areaIds = Arrays.asList(areaIdStr.split(","));

        // 验证场所ID
        GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
        if (respProfile.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }

        // 校验选择的区域
        GenericResponse<ListDTO<PlaceAreaBO>> respArea = placeServerService.findPlaceAreaByPlaceId(placeId);
        if (respArea.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return respArea;
        }
        List<PlaceAreaBO> placeAreaBOS = respArea.getData().getList();
        List<String> checkAreas = placeAreaBOS.stream().map(PlaceAreaBO::getAreaId).collect(Collectors.toList());
        if (!checkAreas.containsAll(areaIds)) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_AREA_NOT_FOUND);
        }

        // 获取区域名称
        StringBuilder sb = new StringBuilder();
        areaIds.forEach(e->{
            placeAreaBOS.forEach(o->{
                if (e.equals(o.getAreaId())) {
                    sb.append(o.getAreaName()).append(",");
                }
            });
        });
        String areaNames = sb.deleteCharAt(sb.length()-1).toString();

        // 获取卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        // PackageTimeReserve中已经存在一条status为0的记录，则不允许自定义包时
        Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                .findUnusedByPlaceIdAndCardId(placeId, cardId);
        if (packageTimeReserveOpt.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_PT_REPEAT);
        }

        // 查询班次信息
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = optLogShift.get();

        // 获取扣费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(billingCard.getPlaceId());
        int deductionOrder = placeBizConfig.getDeductionOrder();

        float priceName = new BigDecimal(price / 100.00).setScale(2, RoundingMode.HALF_UP).floatValue();

        // 获取billingRulePackage 对象
        String ruleId = billingRulePackageTimeService.builderRuleId(placeId);
        BillingRulePackageTime billingRulePackageTime = new BillingRulePackageTime();
        billingRulePackageTime.setPrice(price);
        billingRulePackageTime.setCreated(now);
        billingRulePackageTime.setRuleName("自定义包时(" + priceName + "元/" + DateTimeUtils.getOnlineTimes(durationTime*60));
        billingRulePackageTime.setPackageFlag(9);
        billingRulePackageTime.setAreaIds(areaIdStr);
        billingRulePackageTime.setPlaceId(placeId);
        billingRulePackageTime.setCardTypeIds(billingCard.getCardTypeId());
        billingRulePackageTime.setRuleId(ruleId);
        billingRulePackageTime.setStartTime(-1);
        billingRulePackageTime.setEndTime(-1);
        billingRulePackageTime.setDurationTime((int)durationTime);

        // 获取nextTime
        double hours = Math.floor(durationTime);// 小时数
        int minute = getMinute(durationTime); // 分钟数
        LocalDateTime nextTime = now.plusHours(Double.valueOf(hours).longValue()).plusMinutes(Double.valueOf(minute).longValue());

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,cardId);
        if (billingOnlineOpt.isPresent()) {
            int extraDeductionCash = 0;
            int extraDeductionPresent = 0;
            BillingOnline billingOnline = billingOnlineOpt.get();

            int oldCommonPrice = billingOnline.getCommonPrice();

            // 正在包时，不允许自定义包时
            if (billingOnline.getPackageFlag() != 0) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                return new GenericResponse<>(ServiceCodes.BILLING_PT_CONFLICT);
            }
            
            if (billingOnline.getAccFlag() == 2) { // 自动包时正在生效中，不支持手动包时
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_PT_CONFLICT);
			}

            // 正在上机的区域不在选择的区域内
            if (!areaIdStr.contains(billingOnline.getAreaId())) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                return new GenericResponse<>(ServiceCodes.BILLING_PT_AREA_CONFLICT);
            }

            // 获取登录信息
            Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, cardId, billingOnline.getBillingTime());
            if (!optLogLogin.isPresent()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
            }
            LogLogin logLogin = optLogLogin.get();

            if (Integer.parseInt(packagePayType) == 2) {
                // 现金充值
                extraDeductionCash = billingRulePackageTime.getPrice();
                logTopupService.cashTopup(billingCard,logShift,billingOnline.getClientId(),logLogin.getLoginId(),billingRulePackageTime.getPrice(), packagePayType);
                logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, billingRulePackageTime.getPrice(), 0,
                        billingCard, billingOnline, logShift, logLogin, null,0,0);
            } else {
                List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);
                if (billingCardDeductionService.sumAccount(billingCards,2) < billingRulePackageTime.getPrice()) { // 余额不足
                    // 释放锁
                    billingLockAlgorithm.releaseLock(billingKey);
                    return new GenericResponse<>(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
                }
                // 获取扣费信息
                List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard,billingRulePackageTime.getPrice(),logLogin.getLoginId(),0, deductionOrder);

                // 7.扣费，先扣现金账户
                billingCardService.billingCardDeduction(costDetails,billingCard.getPlaceId());
                if (!StringUtils.isEmpty(costDetails)) {
                    extraDeductionCash = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum);
                    extraDeductionPresent = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum);
                }
            }

            // 获取当前计费卡最新信息：解决连锁门店返回不是当前门店的卡会员信息问题
            // 查询最新的卡信息(连锁逻辑要返回漫游金额)
            if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                // 查询最新的值
                Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
                if (!newOptBillingCard.isPresent()) {
                    // 释放锁
                    billingLockAlgorithm.releaseLock(billingKey);
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
                }
                billingCard = newOptBillingCard.get();
            } else {
                Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
                if (!newOptBillingCard.isPresent()) {
                    // 释放锁
                    billingLockAlgorithm.releaseLock(billingKey);
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
                }
                billingCard = newOptBillingCard.get();
            }

            // 正在标准计费，立马转包时
            billingOnline.setRuleId(ruleId);
            billingOnline.setNextTime(nextTime);
            billingOnline.setDeduction(billingOnline.getDeduction() + billingRulePackageTime.getPrice());
            billingOnline.setDeductionCash(billingOnline.getDeductionCash() + extraDeductionCash);
            billingOnline.setDeductionPresent(billingOnline.getDeductionPresent() + extraDeductionPresent);
            billingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
            billingOnline.setUpdated(now);
            billingOnline.setCommonPrice(billingRulePackageTime.getPrice());
            billingOnline.setPackagePayFlag(Integer.parseInt(packagePayType));
            billingRulePackageTimeService.save(billingRulePackageTime);
            billingOnlineService.save(billingOnline);

            logOperationService.addPackageTimeOperation(SourceType.CASHIER, 9, Integer.parseInt(packagePayType), 0, billingCard, billingOnline,
                    billingRulePackageTime, logShift, logLogin);

            logOperationService.addConvertBillingRuleOperation(SourceType.CASHIER,1, oldCommonPrice,
                    0, billingCard, billingOnline, null, billingRulePackageTime, logShift, logLogin);

            // 组装返回结果
            CustomBO customBO = new CustomBO(ruleId,billingRulePackageTime.getDurationTime(),9,price,nextTime,areaIdStr,areaNames,1);
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            log.info("收银台自定义包时成功:::::::释放锁时间::::" + LocalDateTime.now());
            return new GenericResponse<>(new ObjDTO<>(customBO));
        }

        int costTemporaryOnlineAccount = 0; // 临时卡在线余额扣除金额
        int costCashAccount = 0; // 本金包时消费
        int costPresentAccount = 0; // 奖励包时消费
        if (Integer.parseInt(packagePayType) == 2) {
            // 现金充值
            logTopupService.cashTopup(billingCard,logShift,"","",billingRulePackageTime.getPrice(), packagePayType);
            logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, billingRulePackageTime.getPrice(), 0,
                    billingCard, null, logShift, null, null,0,0);
        } else {
            List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);
            if (billingCardDeductionService.sumAccount(billingCards,2) < billingRulePackageTime.getPrice()) { // 余额不足
                return new GenericResponse<>(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }

            // 获取扣费信息
            List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard,billingRulePackageTime.getPrice(),"",0, deductionOrder);

            if (costDetails != null) {
                costTemporaryOnlineAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).sum();
                costCashAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).sum();
                costPresentAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).sum();
            }

            // 7.扣费，先扣现金账户
            billingCardService.billingCardDeduction(costDetails,billingCard.getPlaceId());
        }

        // 获取当前计费卡最新信息：解决连锁门店返回不是当前门店的卡会员信息问题
        // 查询最新的卡信息(连锁逻辑要返回漫游金额)
        if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            // 查询最新的值
            Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
            if (!newOptBillingCard.isPresent()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            billingCard = newOptBillingCard.get();
        } else {
            Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
            if (!newOptBillingCard.isPresent()) {
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            billingCard = newOptBillingCard.get();
        }


        billingRulePackageTimeService.save(billingRulePackageTime);
        // 未上机，预包时
        packageTimeReserveService.futurePackageTime(now, nextTime, billingCard, billingRulePackageTime,
                logShift, 0, Integer.parseInt(packagePayType), costTemporaryOnlineAccount, costCashAccount, costPresentAccount);

        logOperationService.addPackageTimeOperation(SourceType.CASHIER, 9, Integer.parseInt(packagePayType), 0, billingCard, null,
                billingRulePackageTime, logShift, null);

        CustomBO customBO = new CustomBO(ruleId,billingRulePackageTime.getDurationTime(),9,price,nextTime,areaIdStr,areaNames,0);
        return new GenericResponse<>(new ObjDTO<>(customBO));
    }

    @Getter
    @Setter
    class CustomBO extends AbstractEntityBO {
        private String ruleId;
        private float durationTime;
        private int packageFlag;  // 包时标识 0:普通计费  1:包时段  2:包时长  9:自定义包时
        private int price; // 包时价格
        private LocalDateTime endTime; // 包时结束时间
        private String areaIds; // 包时区域
        private String areaNames; // 区域名称
        private int preparePackageStatus; // 预包时状态  0:预包时  1:正在使用

        public CustomBO() {
        }

        public CustomBO(String ruleId, float durationTime, int packageFlag, int price, LocalDateTime endTime,String areaIds,String areaNames,int preparePackageStatus) {
            this.ruleId = ruleId;
            this.durationTime = durationTime;
            this.packageFlag = packageFlag;
            this.price = price;
            this.endTime = endTime;
            this.areaIds = areaIds;
            this.areaNames = areaNames;
            this.preparePackageStatus = preparePackageStatus;
        }
    }

}
