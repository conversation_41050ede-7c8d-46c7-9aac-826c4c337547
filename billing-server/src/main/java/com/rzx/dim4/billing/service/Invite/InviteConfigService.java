package com.rzx.dim4.billing.service.Invite;

import com.rzx.dim4.billing.entity.invite.InviteConfig;
import com.rzx.dim4.billing.repository.invite.InviteConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年04月27日 10:55
 */
@Service
@Slf4j
public class InviteConfigService {

    @Autowired
    private InviteConfigRepository inviteConfigRepository;

    public Optional<InviteConfig> findByPlaceId(String placeId){
        Optional<InviteConfig> byPlaceId = inviteConfigRepository.findByPlaceId(placeId);
        if(!byPlaceId.isPresent()){
            InviteConfig inviteConfig = new InviteConfig();
            inviteConfig.setCreated(LocalDateTime.now());
            inviteConfig.setPlaceId(placeId);
            inviteConfig.setSupportPresentSwitch(0);
            inviteConfig.setAccepterLimitNum(5);
            save(inviteConfig);
            return inviteConfigRepository.findByPlaceId(placeId);
        }
        return byPlaceId;
    }

    public InviteConfig save(InviteConfig inviteConfig){
        return inviteConfigRepository.save(inviteConfig);
    }

    public int clearCardTypeIdsByPlaceId(String placeId){
        return inviteConfigRepository.clearCardTypeIdsByPlaceId(placeId);
    }
}
