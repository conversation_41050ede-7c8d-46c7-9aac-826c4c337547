package com.rzx.dim4.billing.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.LogAcc;
import com.rzx.dim4.billing.repository.LogAccRepository;

@Service
public class LogAccService {

	@Autowired
	LogAccRepository logAccRepository;

	public LogAcc save(LogAcc logAcc) {
		return logAccRepository.save(logAcc);
	}

	public Optional<LogAcc> findByPlaceIdAndLoginId(String placeId, String loginId) {
		return logAccRepository.findFirstByPlaceIdAndLoginIdOrderByIdDesc(placeId, loginId);
	}

	public Optional<LogAcc> findByPlaceIdAndLoginIdAndFinishTimeIsNull(String placeId, String loginId) {
		return logAccRepository.findByPlaceIdAndLoginIdAndFinishTimeIsNull(placeId, loginId);
	}
}
