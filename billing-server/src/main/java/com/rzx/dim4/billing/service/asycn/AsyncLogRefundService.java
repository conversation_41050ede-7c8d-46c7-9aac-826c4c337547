package com.rzx.dim4.billing.service.asycn;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.marketing.OrdersApi;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogRefund;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.repository.LogRefundRepository;
import com.rzx.dim4.billing.service.Component.RabbitMQProvider;
import com.rzx.dim4.billing.service.Component.RefundMessage;
import com.rzx.dim4.billing.service.LogTopupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025年04月24日 10:02
 */

@Service
@Slf4j
public class AsyncLogRefundService {

    @Autowired
    private OrdersApi ordersApi;

    @Autowired
    private RabbitMQProvider rabbitMQProvider;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PaymentServerService paymentServerService;

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private LogRefundRepository logRefundRepository;

    @Async
    public void asyncSendTempCardRefund(List<LogRefund> logRefunds, BillingCard billingCard, int refundAmount, int orderType){

        // 先进行退款，退款不成功的，再发送给MQ服务处理
        List<LogRefund> needSendToMQ = new ArrayList<>();
        for (LogRefund logRefund : logRefunds) {
            int i =0;
            log.info("发起第{}笔退款开始时间：{}",i+1, DateTimeUtils.getTimeFormat(LocalDateTime.now()));
            logRefund.setUpdated(LocalDateTime.now());
            // 调用payment-server退款
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<ObjDTO<PaymentRefundOrderBO>> response =
                    paymentServerService.refundPaymentOrder(requestTicket, logRefund.getPlaceId(), logRefund.getOrderId(), logRefund.getOnlineRefund());
            //ServiceCodes.PAYMENT_REFUND_SUCC
            // ServiceCodes.PAYMENT_REFUND_ERROR

            OrdersBO ordersBO = new OrdersBO();
            ordersBO.setOrderId(logRefund.getOrderId());
            ordersBO.setStatus(5);
            ordersBO.setPlaceId(logRefund.getPlaceId());
            ordersBO.setRefundAmount(logRefund.getOnlineRefund());
            ordersBO.setRealMoney(logRefund.getOnlineRefund());
            ordersBO.setOrderType(orderType);
            log.info("调用龙兜发起充值退款结果:::{}",new Gson().toJson(response));
            if (response.getCode() == ServiceCodes.PAYMENT_REFUND_SUCC.getCode()) {
                // Handle successful refund
                PaymentRefundOrderBO paymentRefundOrderBO = response.getData().getObj();

                logRefund.setRefundOrderId(paymentRefundOrderBO.getRefundOrderId());
                logRefund.setRefundDesc(paymentRefundOrderBO.getRefundDesc());
                logRefund.setReturnMessage(paymentRefundOrderBO.getReturnMessage());
                if (paymentRefundOrderBO.getRefundStatus() == 1) {
                    logRefund.setStatus(1);
                    LogTopup logTopup = logTopupService.findByOrderId(logRefund.getOrderId())
                            .orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_LOG_TOPUP_NOT_FOUND));
                    if (logRefund.getOnlineRefund() == logTopup.getCashAmount()) {
                        logTopup.setRefundStatus(2);
                    } else {
                        logTopup.setRefundStatus(1);
                    }
                    logTopup.setUpdated(LocalDateTime.now());
                    logTopupService.save(logTopup);
                    ordersBO.setPayRefundId(paymentRefundOrderBO.getRefundOrderId());
                } else {
                    logRefund.setStatus(2);
                    needSendToMQ.add(logRefund);
                    ordersBO.setPayRefundId("");
                }
            } else {
                // Handle other response codes
                logRefund.setReturnMessage(response.getMessage());
                logRefund.setStatus(2);
                needSendToMQ.add(logRefund);
                ordersBO.setPayRefundId("");
            }
            ordersApi.saveOrder(ordersBO);
            log.info("发起第{}笔退款结束时间：{}",i+1, DateTimeUtils.getTimeFormat(LocalDateTime.now()));
            i++;
        }

        this.sendTempCardRefund(needSendToMQ);

        // 保存
        this.saveBatch(logRefunds);
        log.info("退款，placeId={},cardId={},refundAmount={},结束时间{}", billingCard.getPlaceId(), billingCard.getCardId(), refundAmount,DateTimeUtils.getTimeFormat(LocalDateTime.now()));

    }

    /**
     * 临时卡在线退款
     * @param logRefunds 生成的待退款操作对象列表
     */
    private void sendTempCardRefund(List<LogRefund> logRefunds) {
        if (CollectionUtils.isEmpty(logRefunds)) {
            return;
        }

        for (LogRefund logRefund : logRefunds) {
            if ("1000".equals(logRefund.getCardTypeId()) && logRefund.getStatus() != 1) {
                RefundMessage refundMessage = new RefundMessage(logRefund.getId());
                rabbitMQProvider.sendOneHourDelayQueue(refundMessage);
                log.info("发送给延时队列，消息内容: " + refundMessage);
            }
        }
    }

    public void saveBatch(List<LogRefund> logRefunds) {
        logRefundRepository.saveAll(logRefunds);
    }
}
