package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.BillingRuleAcc;
import com.rzx.dim4.billing.entity.LogAcc;
import com.rzx.dim4.billing.repository.BillingRuleAccRepository;
import com.rzx.dim4.billing.repository.LogAccRepository;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年1月13日 上午11:05:09
 */
@Slf4j
@Service
public class BillingRuleAccService {

	@Autowired
	BillingRuleAccRepository billingRuleAccRepository;

	@Autowired
	LogAccRepository logAccRepository;

	/**
	 * 
	 * @param placeId
	 * @return
	 */
	public List<BillingRuleAcc> findByPlaceId(String placeId) {
		return billingRuleAccRepository.findByPlaceIdAndDeletedOrderByIdDesc(placeId, 0);
	}

	/**
	 * 获取当前符合条件的累加规则
	 * 
	 * @param placeId
	 * @param cardTypeId
	 * @param areaId
	 * @return
	 */
	public BillingRuleAcc findCurrBillingRuleAcc(String placeId, String cardTypeId, String areaId, String loginId) {

		if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardTypeId) || StringUtils.isEmpty(areaId)) {
			return null;
		}

		LocalDateTime nowTime = LocalDateTime.now();
		// 查询累计记录
		Optional<LogAcc> logAccOpt = logAccRepository.findByPlaceIdAndLoginIdAndFinishTimeIsNull(placeId, loginId);
		if (logAccOpt.isPresent()) {
			// 有正在累计的
			LogAcc logAcc = logAccOpt.get();
			// 累计开始时间
			LocalDateTime start = logAcc.getCreated();
			// 计算累计结束时间
			LocalDateTime endTime = LocalDateTime.of(nowTime.toLocalDate(),
					LocalTime.of((int) logAcc.getEndTime(), Math.round(logAcc.getEndTime() % 1 * 60)));

			int hour = nowTime.getHour();
			float minus = BigDecimal.valueOf(nowTime.getMinute() / 60.0).setScale(2, RoundingMode.HALF_UP).floatValue();
			if (logAcc.getStartTime() > logAcc.getEndTime() && (hour + minus) >= logAcc.getStartTime()) {
				endTime = endTime.plusDays(1);
			}
			if ((nowTime.isAfter(start) && nowTime.isBefore(endTime)) || logAcc.getStartTime() == logAcc.getEndTime()) {
				// 还在生效的累计
				Optional<BillingRuleAcc> billingRuleAccOpt = billingRuleAccRepository.findByPlaceIdAndRuleIdAndDeleted(placeId, logAcc.getAccRuleId(), 0);
				if (billingRuleAccOpt.isPresent()) {
					BillingRuleAcc billingRuleAcc = billingRuleAccOpt.get();
					if (!billingRuleAcc.getCardTypeIds().contains(cardTypeId) || !billingRuleAcc.getAreaIds().contains(areaId)) {
						// 已经换机了，之前累计的钱保留，这条记录状态更新
						logAcc.setFinishTime(LocalDateTime.now());
						logAcc.setUpdated(LocalDateTime.now());
						logAccRepository.save(logAcc);

						BillingRuleAcc newBillingRuleAcc  = getBillingRuleAcc(placeId, cardTypeId, areaId);
						if (newBillingRuleAcc != null) {
							LogAcc newLogAcc = new LogAcc();
							newLogAcc.setAccPrice(newBillingRuleAcc.getAccPrice());
							newLogAcc.setAccRuleId(newBillingRuleAcc.getRuleId());
							newLogAcc.setCreated(nowTime);
							newLogAcc.setCurrPrice(0);
							newLogAcc.setEndTime(newBillingRuleAcc.getEndTime());
							newLogAcc.setLoginId(loginId);
							newLogAcc.setPlaceId(placeId);
							newLogAcc.setStartTime(newBillingRuleAcc.getStartTime());
							logAccRepository.save(newLogAcc);
						}
						return newBillingRuleAcc;
					}
					return billingRuleAccOpt.get();
				}
				return null;
			} else {
				// 已经失效，清空状态
				logAcc.setFinishTime(LocalDateTime.now());
				logAcc.setUpdated(LocalDateTime.now());
				logAccRepository.save(logAcc);

				BillingRuleAcc newBillingRuleAcc  = getBillingRuleAcc(placeId, cardTypeId, areaId);
				if (newBillingRuleAcc != null) {
					LogAcc newLogAcc = new LogAcc();
					newLogAcc.setAccPrice(newBillingRuleAcc.getAccPrice());
					newLogAcc.setAccRuleId(newBillingRuleAcc.getRuleId());
					newLogAcc.setCreated(nowTime);
					newLogAcc.setCurrPrice(0);
					newLogAcc.setEndTime(newBillingRuleAcc.getEndTime());
					newLogAcc.setLoginId(loginId);
					newLogAcc.setPlaceId(placeId);
					newLogAcc.setStartTime(newBillingRuleAcc.getStartTime());
					logAccRepository.save(newLogAcc);
				}
				return newBillingRuleAcc;
			}
		}
		return getBillingRuleAcc(placeId, cardTypeId, areaId);
	}

	/**
	 * 找到生效的accRule
	 * @param placeId
	 * @param cardTypeId
	 * @param areaId
	 * @return
	 */
	public BillingRuleAcc getBillingRuleAcc (String placeId, String cardTypeId, String areaId) {
		LocalDateTime nowTime = LocalDateTime.now();
		LocalDate nowDate = LocalDate.now();
		int weekend = nowTime.getDayOfWeek().getValue();
		List<BillingRuleAcc> list = billingRuleAccRepository.findByPlaceIdAndDeletedOrderByIdDesc(placeId, 0);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		for (BillingRuleAcc acc : list) {
			LocalDateTime startTime = LocalDateTime.of(nowDate,
					LocalTime.of((int) acc.getStartTime(), Math.round(acc.getStartTime() % 1 * 60)));
			LocalDateTime endTime = LocalDateTime.of(nowDate,
					LocalTime.of((int) acc.getEndTime(), Math.round(acc.getEndTime() % 1 * 60)));
			if (acc.getStartTime() > acc.getEndTime()) {
				// 跨天
				int hour = nowTime.getHour();
				float minus = BigDecimal.valueOf(nowTime.getMinute() / 60.0).setScale(2, RoundingMode.HALF_UP).floatValue();
				if ((hour + minus) < acc.getEndTime()) {
					startTime = startTime.minusDays(1);
				} else if ((hour + minus) >= acc.getStartTime()) {
					endTime = endTime.plusDays(1);
				}
			}
//			log.info("获取最新的累计包时规则2::::::startTime::::" + startTime + "::::endTime::::::" + endTime + ":::::nowTime::::::" + nowTime);
			if (acc.getAreaIds().contains(areaId) && acc.getCardTypeIds().contains(cardTypeId)) {
				boolean crossZero = false; // 跨天标识
				if (acc.getStartTime() >= acc.getEndTime()) { // 垮天
					crossZero = true;
				}
				if (nowTime.isAfter(startTime) && nowTime.isBefore(endTime)) {
					if (acc.getWeekendDisabled() == 0) { // 全天可用
						return acc;
					} else { // weekendDisabled == 1，周末禁用
						if (crossZero) { // 跨天，周五不生效
							if (weekend < 5) {
								return acc;
							}
						} else { // 没有跨天，周五生效
							if (weekend <= 5) {
								return acc;
							}
						}
					}
				}
			}
		}
		return null;
	}

	/**
	 * 分页查询包时计费
	 *
	 * @param map
	 * @param pageable
	 * @return
	 */
	public Page<BillingRuleAcc> findAll(Map<String, String> map, Pageable pageable) {
		return billingRuleAccRepository.findAll(new Specification<BillingRuleAcc>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingRuleAcc> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

				List<Predicate> predicates = new ArrayList<Predicate>();

				Predicate p1 = cb.equal(root.get("deleted"), 0);
				predicates.add(p1);

				// 场所ID
				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					Predicate predicate = cb.equal(root.get("placeId").as(String.class), map.get("placeId"));
					predicates.add(predicate);
				}

				Predicate[] p = new Predicate[predicates.size()];
				return cb.and(predicates.toArray(p));
			}
		}, pageable);
	}

	/**
	 * 查询该规则详细信息
	 * 
	 * @param placeId
	 * @param ruleId
	 * @return
	 */
	public Optional<BillingRuleAcc> findByPlaceIdAndRuleId(String placeId, String ruleId) {
		return billingRuleAccRepository.findByPlaceIdAndRuleIdAndDeleted(placeId, ruleId, 0);
	}

	/**
	 * 构建规则Id(Id为偶数则是包时规则)
	 * 
	 * @param placeId
	 * @return
	 */
	@Synchronized
	private String builderRuleId(String placeId) {
		int ruleId = 3300;
		Optional<BillingRuleAcc> lastRule = billingRuleAccRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
		if (lastRule.isPresent()) {
			ruleId = Integer.parseInt(lastRule.get().getRuleId()) + 2;
		}
		return String.valueOf(ruleId);
	}

	/**
	 * 保存
	 * 
	 * @param billingRuleAcc
	 * @return
	 */
	public BillingRuleAcc save(BillingRuleAcc billingRuleAcc) {
		if (StringUtils.isEmpty(billingRuleAcc.getRuleId())) {
			billingRuleAcc.setRuleId(builderRuleId(billingRuleAcc.getPlaceId()));
		}
		return billingRuleAccRepository.save(billingRuleAcc);
	}

	/**
	 * 删除费率
	 * 
	 * @param billingRuleAcc
	 */
	public void delete(BillingRuleAcc billingRuleAcc) {
		billingRuleAccRepository.delete(billingRuleAcc);
	}

}
