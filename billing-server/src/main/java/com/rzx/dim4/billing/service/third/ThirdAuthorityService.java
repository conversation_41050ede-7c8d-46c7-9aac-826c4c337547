package com.rzx.dim4.billing.service.third;

import com.rzx.dim4.base.enums.billing.ThirdServiceIndexes;
import com.rzx.dim4.billing.entity.third.ThirdAuthority;
import com.rzx.dim4.billing.repository.third.ThirdAuthorityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ThirdAuthorityService {

    @Autowired
    private ThirdAuthorityRepository thirdAuthorityRepository;

    public Optional<ThirdAuthority> findByThirdAccountId (String thirdAccountId) {
        return thirdAuthorityRepository.findByThirdAccountId(thirdAccountId);
    }

    public ThirdAuthority save (ThirdAuthority thirdAuthority) {
        return thirdAuthorityRepository.save(thirdAuthority);
    }

    public boolean checkAuthority(String thirdAccountId, ThirdServiceIndexes thirdServiceIndexes) {
        Optional<ThirdAuthority> thirdAuthorityOpt = thirdAuthorityRepository.findByThirdAccountId(thirdAccountId);
        if (!thirdAuthorityOpt.isPresent()) {
            return false;
        }
        ThirdAuthority thirdAuthority = thirdAuthorityOpt.get();
        return thirdAuthority.getOpAuthority().contains(String.valueOf(thirdServiceIndexes.getValue()));
    }
}
