package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingRuleCommon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年1月13日 上午11:03:59
 */
public interface BillingRuleCommonRepository extends JpaRepository<BillingRuleCommon, Long>, JpaSpecificationExecutor<BillingRuleCommon> {

	@Modifying
	@Transactional
	@Query(value = "update billing_rule_common set deleted = 1, updated = now() where place_id = :placeId and deleted = 0 and card_type_id not in ('1000', '1001', '1002')", nativeQuery = true)
	int deletedByPlaceId(String placeId);

	Optional<BillingRuleCommon> findByPlaceIdAndAreaIdAndCardTypeIdAndDeleted(String placeId, String areaId, String cardTypeId, int deleted);

	Optional<BillingRuleCommon> findTop1ByPlaceIdOrderByIdDesc(String placeId);

	Optional<BillingRuleCommon> findByPlaceIdAndRuleIdAndDeleted(String placeId, String ruleId, int deleted);

	List<BillingRuleCommon> findByPlaceIdAndDeleted(String placeId, int deleted);

	List<BillingRuleCommon> findByPlaceIdAndAreaIdAndDeleted(String placeId, String areaId, int deleted);

	List<BillingRuleCommon> findByPlaceIdAndCardTypeIdAndDeleted(String placeId, String cardTypeId, int deleted);

	List<BillingRuleCommon> findByPlaceIdAndCardTypeIdInAndDeleted(String placeId, List<String> cardTypeIds, int deleted);
}
