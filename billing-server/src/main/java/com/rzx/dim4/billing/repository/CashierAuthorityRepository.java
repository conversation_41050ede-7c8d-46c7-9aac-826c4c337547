package com.rzx.dim4.billing.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.rzx.dim4.billing.entity.CashierAuthority;

/**
 * 
 * <AUTHOR>
 * @date 2022年1月5日 上午9:40:05
 */
public interface CashierAuthorityRepository extends JpaRepository<CashierAuthority, Long> {

	Optional<CashierAuthority> findByPlaceIdAndAccountId(String placeId, String accountId);

	List<CashierAuthority> findByPlaceId(String placeId);

}
