package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.CancellationBO;
import com.rzx.dim4.base.bo.billing.LogLoginBO;
import com.rzx.dim4.base.bo.billing.StatisticSumConsumptionBO;
import com.rzx.dim4.base.bo.notify.polling.*;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.VerifyParam;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.BillingCardTypeRepository;
import com.rzx.dim4.billing.repository.LogLoginRepository;
import com.rzx.dim4.billing.service.impl.client.ClientLogoutServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzx.dim4.billing.cons.BillingConstants.ALL_LOGOUT;
import static com.rzx.dim4.billing.cons.BillingConstants.TEMP_CARD_TYPE_ID;

/**
 * <AUTHOR>
 * @date 2021年9月3日 下午5:16:37
 */
@Service
@Slf4j
public class LogLoginService {

    @Autowired
    private LogLoginRepository logLoginRepository;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private TempRecordSurchargeService tempRecordSurchargeService;

    @Autowired
    private LogRefundService logRefundService;

    @Autowired
    private BillingCardRepository billingCardRepository;

    @Autowired
    private BillingCardTypeRepository cardTypeRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private ClientLogoutServiceImpl clientLogoutServiceImpl;

    @Autowired
    private LogPlaceChainService logPlaceChainService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogLoginService logLoginService;

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    public LogLogin doLogout(String placeId, String clientId, String logoutOperationCreater,
                             String logoutOperationCreaterName, int consumptionTotal, int consumptionCashTotal, int consumptionPresentTotal, LocalDateTime loginTime, int totalAccount) {
        // 统计上网时长
        Duration duration = Duration.between(loginTime, LocalDateTime.now());
        Long onlineTime = duration.toMinutes();

        Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                clientId, loginTime);
        if (!logLoginOpt.isPresent()) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        LogLogin logLogin = logLoginOpt.get();
        logLogin.setUpdated(now);
        logLogin.setLogoutTime(now);
        logLogin.setConsumptionTotal(logLogin.getConsumptionTotal() + consumptionTotal);
        logLogin.setConsumptionCashTotal(logLogin.getConsumptionCashTotal() + consumptionCashTotal);
        logLogin.setConsumptionPresentTotal(logLogin.getConsumptionPresentTotal() + consumptionPresentTotal);
        logLogin.setLogoutOperationCreater(logoutOperationCreater);
        logLogin.setLogoutOperationCreaterName(logoutOperationCreaterName);
        logLogin.setOnlineTime(onlineTime);
        logLogin.setTotalAccount(totalAccount);
        if (ALL_LOGOUT.equals(logoutOperationCreaterName)) {
            logLogin.setLogoutType(1);
        }

        // 查询本次结账是否产生了漫游扣费
        try {
            List<String> loginIds = logPlaceChainService.queryRoamLoginId(logLogin.getLoginId());
            if (loginIds != null && loginIds.size() > 0) {
                logLogin.setChainFlag(1);
            } else {
                logLogin.setChainFlag(0); // 避免更新时已经产生的正在上机的数据，结账时赋值0，不然保存null会报错
            }
        } catch (Exception e) {
            logLogin.setChainFlag(0);
            log.info(e.getMessage());
        }

        return save(logLogin);
    }

    @Transactional
    public LogLogin save(LogLogin logLogin) {
        if (StringUtils.isEmpty(logLogin.getLoginId())) {
            logLogin.setLoginId(buildLoginId());
        }
        if (StringUtils.isEmpty(logLogin.getId())) {
            try {
                logLogin = logLoginRepository.save(logLogin);
            } catch (Exception e) {
                log.info("保存logLogin失败::::::" + e.getMessage());
                try {
                    logLogin = logLoginRepository.save(logLogin);
                } catch (Exception ex) {
                    log.info("保存logLogin第二次失败::::::" + e.getMessage());
                    logLogin = logLoginRepository.save(logLogin);
                }
            }
            return logLogin;
           // return logLoginRepository.save(logLogin);
        }
        LogLogin logLoginCopy = new LogLogin();
        BeanUtils.copyProperties(logLogin,logLoginCopy);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id",logLoginCopy.getId());
        paramMap.put("startTime",logLoginCopy.getCreated());
        paramMap.put("endTime",DateTimeUtils.monthEndTime(logLoginCopy.getCreated()));

        // 续更新的字段,nextTime和deduction不在这做更新
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("updated",LocalDateTime.now());
        updateMap.put("clientId",logLoginCopy.getClientId());
        updateMap.put("lastClientId",logLoginCopy.getLastClientId());
        updateMap.put("consumptionTotal",logLoginCopy.getConsumptionTotal());
        updateMap.put("consumptionCashTotal",logLoginCopy.getConsumptionCashTotal());
        updateMap.put("consumptionPresentTotal",logLoginCopy.getConsumptionPresentTotal());
        updateMap.put("logoutTime",logLoginCopy.getLogoutTime());
        updateMap.put("onlineTime",logLoginCopy.getOnlineTime());
        updateMap.put("totalAccount",logLoginCopy.getTotalAccount());
        updateMap.put("logoutOperationCreater",logLoginCopy.getLogoutOperationCreater());
        updateMap.put("logoutOperationCreaterName",logLoginCopy.getLogoutOperationCreaterName());
        updateMap.put("logoutType",logLoginCopy.getLogoutType());
        updateMap.put("chainFlag",logLoginCopy.getChainFlag());
        updateMap.put("activeTime",logLoginCopy.getActiveTime());
        updateMap.put("cardTypeId",logLoginCopy.getCardTypeId());
        update(entityManager, paramMap, updateMap);
        return logLoginCopy;
    }

    /**
     * 保存登入轮询数据
     *
     * @param placeId
     * @param clientId
     * @param idNumber
     */
    public void saveLoginPolling(String placeId, String clientId, String idNumber, SourceType sourceType) {
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, clientId, idNumber, BusinessType.LOGIN);

        LogLoginBusinessBO businessLogLoginBO = new LogLoginBusinessBO();
        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {

                businessLogLoginBO.setPlaceId(placeId);
                businessLogLoginBO.setClientId(clientId);
                businessLogLoginBO.setIdNumber(idNumber);
                businessLogLoginBO.setCreated(LocalDateTime.now().toString());
                businessLogLoginBO.setSourceType(sourceType);
                businessLogLoginBO.setBusinessType(BusinessType.LOGIN);
                businessLogLoginBO.setBusinessId(pollingBO.getCashierBusinessId());
                businessLogLoginBO.setType(1);
                // 保存收银台业务数据
                notifyServerService.pushLoginBusinessData(businessLogLoginBO);

                // 保存收银台业务数据
                businessLogLoginBO.setBusinessId(pollingBO.getClientBusinessId());
                businessLogLoginBO.setType(0);
                notifyServerService.pushLoginBusinessData(businessLogLoginBO);
            }
        }
    }

    /**
     * 保存包时换机轮询数据
     *
     * @param areaId
     * @param clientId
     * @param areaName
     */
    public void savePackageExchangePolling (String clientId, String areaId, String areaName, BillingCard billingCard, SourceType sourceType, BillingRulePackageTime billingRulePackageTime) {
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(billingCard.getPlaceId(), clientId, billingCard.getIdNumber(), BusinessType.PACKAGE_EXCHANGE);

        PackageExchangeBusinessBO packageExchangeBusinessBO = new PackageExchangeBusinessBO();
        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                packageExchangeBusinessBO.setPlaceId(billingCard.getPlaceId());
                packageExchangeBusinessBO.setClientId(clientId);
                packageExchangeBusinessBO.setIdNumber(billingCard.getIdNumber());
                packageExchangeBusinessBO.setCreated(LocalDateTime.now().toString());
                packageExchangeBusinessBO.setSourceType(sourceType);
                packageExchangeBusinessBO.setBusinessType(BusinessType.PACKAGE_EXCHANGE);
                packageExchangeBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                packageExchangeBusinessBO.setType(1);
                packageExchangeBusinessBO.setRuleId(billingRulePackageTime.getRuleId());
                packageExchangeBusinessBO.setRuleName(billingRulePackageTime.getRuleName());
                packageExchangeBusinessBO.setPrice(billingRulePackageTime.getPrice());
                packageExchangeBusinessBO.setStartTime(billingRulePackageTime.getStartTime());
                packageExchangeBusinessBO.setEndTime(billingRulePackageTime.getEndTime());
                packageExchangeBusinessBO.setAreaId(areaId);
                packageExchangeBusinessBO.setAreaName(areaName);
                packageExchangeBusinessBO.setCardId(billingCard.getCardId());
                packageExchangeBusinessBO.setCardTypeId(billingCard.getCardTypeId());
                packageExchangeBusinessBO.setIdName(billingCard.getIdName());
                packageExchangeBusinessBO.setTotalAccount(billingCard.getTotalAccount());
                // 保存收银台业务数据
                notifyServerService.pushPackageExchangeBusinessData(packageExchangeBusinessBO);
                // 保存收银台业务数据
                packageExchangeBusinessBO.setBusinessId(pollingBO.getClientBusinessId());
                packageExchangeBusinessBO.setType(0);
                notifyServerService.pushPackageExchangeBusinessData(packageExchangeBusinessBO);
            }
        }
    }

    /**
     * 第三方场所客户端登录时，需要把换机通知给收银台
     * @param oldPlaceClientBO
     * @param curPlaceClientBO
     * @param idNumber
     * @param sourceType
     */
    public void saveThirdExchangePolling(PlaceClientBO oldPlaceClientBO, PlaceClientBO curPlaceClientBO, String idNumber, SourceType sourceType, LocalDateTime operationTime) {
        // 先判断场所是否为第三方场所
//        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(oldPlaceClientBO.getPlaceId());
//        boolean notThirdPlaceFlag = StringUtils.isEmpty(placeBizConfig.getThirdAccountId());
//        if (notThirdPlaceFlag) {
//            log.info("不是第三方场所，不需要轮询信息");
//            return;
//        }

        // 是第三方场所，再写轮询信息
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric =
                notifyServerService.savePolling(oldPlaceClientBO.getPlaceId(), curPlaceClientBO.getClientId(), idNumber, BusinessType.EXCHANGE_CLIENT);
        ThirdExchangeBusinessBO thirdExchangeBusinessBO = new ThirdExchangeBusinessBO();
        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                thirdExchangeBusinessBO.setOperationTime(formatter.format(operationTime));
                thirdExchangeBusinessBO.setPlaceId(oldPlaceClientBO.getPlaceId());

                thirdExchangeBusinessBO.setOldClientId(oldPlaceClientBO.getClientId());
                thirdExchangeBusinessBO.setOldClientName(oldPlaceClientBO.getHostName());
                thirdExchangeBusinessBO.setOldClientIpAddr(oldPlaceClientBO.getIpAddr());

                thirdExchangeBusinessBO.setCurClientId(curPlaceClientBO.getClientId());
                thirdExchangeBusinessBO.setCurClientName(curPlaceClientBO.getHostName());
                thirdExchangeBusinessBO.setCurClientIpAddr(curPlaceClientBO.getIpAddr());

                thirdExchangeBusinessBO.setIdNumber(idNumber);
                thirdExchangeBusinessBO.setCreated(LocalDateTime.now().toString());
                thirdExchangeBusinessBO.setSourceType(sourceType);
                thirdExchangeBusinessBO.setBusinessType(BusinessType.EXCHANGE_CLIENT);
                thirdExchangeBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                thirdExchangeBusinessBO.setType(1); // 0:客户端 1:收银台
                // 保存收银台业务数据
                notifyServerService.pushThirdExchangeBusinessData(thirdExchangeBusinessBO);

                log.info("保存轮询信息成功，{}", new Gson().toJson(thirdExchangeBusinessBO));
            }
        }
    }

    public Optional<LogLogin> findOnlineByPlaceIdAndCardIdAndBillingTime (String placeId, String cardId, LocalDateTime billingTime) {
        return logLoginRepository.findByPlaceIdAndCardIdAndLogoutTimeIsNullAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, cardId, billingTime, DateTimeUtils.monthEndTime(billingTime));
//        List<LogLogin> logins = logLoginRepository.findByPlaceIdAndClientIdAndLogoutTimeIsNullOrderByIdDesc(placeId, clientId);
//        if (logins.size() > 1) {
//            for (int i = 1; logins.size() > i; i++) {
//                LogLogin logLogin = logins.get(i);
//
//                // 获取总消费、总时长
//                List<BillingOnline> onlines = billingOnlineService.findByPlaceIdAndLoginId(placeId, logLogin.getLoginId());
//                if (onlines != null) {
//                    int consumptionTotal = 0;
//                    long onlineTime = 0;
//                    for (BillingOnline online : onlines) {
//                        consumptionTotal = consumptionTotal + online.getDeduction();
//                        Duration duration = Duration.between(logLogin.getLoginTime(), StringUtils.isEmpty(online.getUpdated()) ? online.getCreated() : online.getUpdated());
//                        onlineTime = Math.max(duration.toMinutes(), onlineTime);
//                    }
//                    logLogin.setConsumptionTotal(consumptionTotal);
//                    logLogin.setOnlineTime(onlineTime);
//                }
//
//                logLogin.setLogoutTime(LocalDateTime.now());
//                logLogin.setLogoutOperationCreater("-1");
//                logLogin.setLogoutOperationCreaterName("数据异常修复");
//                logLoginRepository.save(logLogin);
//            }
//        }
//
//        List<LogLogin> logLogins = logLoginRepository.findByPlaceIdAndCardIdAndLogoutTimeIsNullOrderByIdDesc(placeId,
//                cardId);
//        if (logLogins.size() > 1) {
//            for (int i = 1; logLogins.size() > i; i++) {
//                LogLogin logLogin = logLogins.get(i);
//
//                // 获取总消费、总时长
//                List<BillingOnline> onlines = billingOnlineService.findByPlaceIdAndLoginId(placeId, logLogin.getLoginId());
//                if (onlines != null) {
//                    int consumptionTotal = 0;
//                    long onlineTime = 0;
//                    for (BillingOnline online : onlines) {
//                        consumptionTotal = consumptionTotal + online.getDeduction();
//                        Duration duration = Duration.between(logLogin.getLoginTime(), StringUtils.isEmpty(online.getUpdated()) ? online.getCreated() : online.getUpdated());
//                        onlineTime = Math.max(duration.toMinutes(), onlineTime);
//                    }
//                    logLogin.setConsumptionTotal(consumptionTotal);
//                    logLogin.setOnlineTime(onlineTime);
//                }
//
//                logLogin.setLogoutTime(LocalDateTime.now());
//                logLogin.setLogoutOperationCreater("-1");
//                logLogin.setLogoutOperationCreaterName("数据异常修复");
//                logLoginRepository.save(logLogin);
//            }
//            return Optional.of(logLogins.get(0));
//        } else if (logLogins.size() == 1) {
//            return Optional.of(logLogins.get(0));
//        }
//        return Optional.empty();
    }

    public Optional<LogLogin> findOnlineByPlaceIdAndClientId(String placeId, String clientId, LocalDateTime billingTime) {
        return logLoginRepository.findByPlaceIdAndClientIdAndLogoutTimeIsNullAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, clientId, billingTime, DateTimeUtils.monthEndTime(billingTime));
    }

    public Optional<LogLogin> findByPlaceIdAndLoginId(String placeId, String loginId, LocalDateTime billingTime) {
        return logLoginRepository.findByPlaceIdAndLoginIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, loginId, billingTime, DateTimeUtils.monthEndTime(billingTime));
    }

    /**
     * 根据loginId查询上机记录信息，目前只有第三方在调用
     * @param placeId
     * @param loginId
     * @return
     */
    public Optional<LogLogin> findLastLogLoginByLoginId (String placeId, String loginId) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findByPlaceIdAndLoginIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, loginId, now.minusMonths(1), now);
    }

    public Optional<LogLogin> findLastLoginByIdNumberAndPlaceId(String placeId, String idNumber) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findLastLoginByIdNumberAndPlaceId(placeId, idNumber, now.minusMonths(1), now);
    }

    public Optional<LogLogin> findLastLogOutByIdNumberAndPlaceId(List<String> placeIds, String idNumber) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findTop1ByPlaceIdInAndIdNumberAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeIds, idNumber, now.minusMonths(1), now);
    }

    public List<LogLogin> findByPlaceIdAndCardIdIn (String placeId, List<String> cardIds) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findByPlaceIdAndCardIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndLogoutTimeIsNotNullOrderByIdDesc(placeId, cardIds, now.minusMonths(1), now);
    }

    public List<LogLogin> findByPlaceIdAndLoginIdIn(String placeId, List<String> loginIds) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findByPlaceIdAndLoginIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, loginIds, now.minusMonths(1), now);
    }

    /**
     * 生成时间戳+随机3位的16位登录ID
     *
     * @return
     */
    public String buildLoginId() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String generateCode = Dim4StringUtils.generateCode(3);
        return timestamp + generateCode;
    }

    public Specification<LogLogin> queryParam(Map<String, Object> map) {
        return (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            // List<Predicate> orPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (map.containsKey("placeId") && !org.springframework.util.StringUtils.isEmpty(map.get("placeId"))) {// 场所
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
            }

            if (map.containsKey("logoutType") && !org.springframework.util.StringUtils.isEmpty(map.get("logoutType"))) {// 结账类型
                andPredicateList.add(cb.equal(root.get("logoutType").as(int.class), map.get("logoutType")));
            }

            if (map.containsKey("chainFlag") && !org.springframework.util.StringUtils.isEmpty(map.get("chainFlag"))) {// 是否连锁场所登入
                andPredicateList.add(cb.equal(root.get("chainFlag").as(int.class), map.get("chainFlag")));
            }

            if (map.containsKey("createdStartTime") && !org.springframework.util.StringUtils.isEmpty(map.get("createdStartTime"))) {// 操作开始时间
                LocalDateTime startTime = LocalDateTime.parse(map.get("createdStartTime").toString(), fmt);
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("createdEndTime") && !org.springframework.util.StringUtils.isEmpty(map.get("createdEndTime"))) {// 操作结束时间
                LocalDateTime endTime = LocalDateTime.parse(map.get("createdEndTime").toString(), fmt);
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
            }

            if (map.containsKey("loginTime") && !org.springframework.util.StringUtils.isEmpty(map.get("loginTime"))) {// 操作开始时间
                LocalDateTime startTime = DateTimeUtils.DateFormat(map.get("loginTime").toString());
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("loginTime").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("logoutTime") && !org.springframework.util.StringUtils.isEmpty(map.get("logoutTime"))) {// 操作结束时间
                LocalDateTime endTime = DateTimeUtils.DateFormat(map.get("logoutTime").toString());
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("logoutTime").as(LocalDateTime.class), endTime));
            }

            if (map.containsKey("workingTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("workingTime"))) {// 查询当前班次的结账记录
                LocalDateTime endTime = DateTimeUtils.DateFormat(map.get("workingTime").toString());
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("logoutTime").as(LocalDateTime.class), endTime));
            }

            if (map.containsKey("offWorkingTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("offWorkingTime"))) {// 查询当前班次的结账记录
                LocalDateTime endTime = DateTimeUtils.DateFormat(map.get("offWorkingTime").toString());
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("logoutTime").as(LocalDateTime.class), endTime));
            }

            if (map.containsKey("idNumber") && !org.springframework.util.StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                andPredicateList.add(cb.like(root.get("idNumber"), map.get("idNumber") + "%")); // 单边匹配
            }

            // idName
            if (map.containsKey("idName") && !org.springframework.util.StringUtils.isEmpty(map.get("idName"))) {
                String idName = (String) map.get("idName");
                List<BillingCard> billingCards = billingCardService.findByPlaceIdAndIdNameLike(map.get("placeId").toString(), idName);
                List<String> idNumberList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(billingCards)) {
                    idNumberList = billingCards.stream().map(BillingCard::getIdNumber).collect(Collectors.toList());
                }
                andPredicateList.add(root.get("idNumber").in(idNumberList));
            }

            // 身份证号(收银台查询)
            if (map.containsKey("cashierIdNumber") && !StringUtils.isEmpty(map.get("cashierIdNumber"))) {
                Predicate predicate = cb.like(root.get("idNumber"), map.get("cashierIdNumber") + "%");
                if ((map.get("cashierIdNumber") + "").length() == 18) {
                    predicate = cb.equal(root.get("idNumber"), map.get("cashierIdNumber"));
                }
                andPredicateList.add(predicate);
            }

            // 身份证号(收银台订单管理查询): 用这个字段查询，请同时加上placeId才会走索引
            if (map.containsKey("idNumberLike") && !StringUtils.isEmpty(map.get("idNumberLike"))) {
                if ((map.get("idNumberLike") + "").length() == 18) {
                    andPredicateList.add(cb.equal(root.get("idNumber"), map.get("idNumberLike")));
                } else {
                    andPredicateList.add(cb.like(root.get("idNumber"), "%" + map.get("idNumberLike") + "%"));
                }
            }

            if (map.containsKey("clientId") && !org.springframework.util.StringUtils.isEmpty(map.get("clientId"))) {// 客户端ID
                andPredicateList.add(cb.equal(root.get("clientId").as(String.class), map.get("clientId")));
            }

            // clientName
            if (map.containsKey("clientName") && !org.springframework.util.StringUtils.isEmpty(map.get("clientName"))) {
                String clientName = (String) map.get("clientName");

                GenericResponse<ListDTO<PlaceClientBO>> response = placeServerService.findByPlaceIdAndHostNameLike(map.get("placeId").toString(), clientName);
                if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
                    throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
                }
                List<PlaceClientBO> placeClientBOS = response.getData().getList();

                List<String> clientIds = placeClientBOS.stream().map(PlaceClientBO::getClientId).collect(Collectors.toList());
                andPredicateList.add(root.get("clientId").in(clientIds));
            }

            if (map.containsKey("cardTypeId") && !org.springframework.util.StringUtils.isEmpty(map.get("cardTypeId"))) { // 卡类型
                andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), map.get("cardTypeId")));
            }

            if (map.containsKey("logoutOperationCreater")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("logoutOperationCreater"))) { // 操作人
                andPredicateList.add(cb.equal(root.get("logoutOperationCreater").as(String.class),
                        map.get("logoutOperationCreater")));
            }

            if (map.containsKey("logoutOperationCreaterName") && !StringUtils.isEmpty(map.get("logoutOperationCreaterName"))) { // 操作人姓名
                andPredicateList.add(cb.like(root.get("logoutOperationCreaterName"), map.get("logoutOperationCreaterName") + "%")); // 单边匹配
            }

            if (map.containsKey("startLogoutTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("startLogoutTime"))) {// 结账时间
                LocalDateTime startTime = DateTimeUtils.DateFormat(map.get("startLogoutTime").toString());
                andPredicateList
                        .add(cb.greaterThanOrEqualTo(root.get("logoutTime").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("endLogoutTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("endLogoutTime"))) {// 结账时间
                LocalDateTime endTime = DateTimeUtils.DateFormat(map.get("endLogoutTime").toString());
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("logoutTime").as(LocalDateTime.class), endTime));
            }

            // cardTypeId  not in
            if (map.containsKey("cardTypeIdsNotIn") && !org.springframework.util.StringUtils.isEmpty(map.get("cardTypeIdsNotIn"))) { //
                andPredicateList.add(cb.not(root.get("cardTypeId").as(String.class).in(map.get("cardTypeIdsNotIn"))));
//				Path<Object> path = root.get("cardTypeId");
//				Predicate predicate = cb.in(path);
//				cb.not(predicate);
//				//CriteriaBuilder.In<Object> notIn = cb.in(path).not();
//				List<String> cardTypeIds = (List) map.get("cardTypeIdsNotIn");
//				//cardTypeIds.forEach(predicate::not);
//				andPredicateList.add(predicate);
            }

            if (map.containsKey("cardIds") && !org.springframework.util.StringUtils.isEmpty(map.get("cardIds"))) { // 卡Ids
                Path<Object> path = root.get("cardId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> cardIds = (List) map.get("cardIds");
                cardIds.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("clientIds") && !org.springframework.util.StringUtils.isEmpty(map.get("clientIds"))) { // 客户端Ids
                Path<Object> path = root.get("clientId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> clientIds = (List) map.get("clientIds");
                clientIds.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("placeIds") && !org.springframework.util.StringUtils.isEmpty(map.get("placeIds"))) { // placeIds
                Path<Object> path = root.get("placeId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> placeIds = (List) map.get("placeIds");
                placeIds.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("loginIds") && !org.springframework.util.StringUtils.isEmpty(map.get("loginIds"))) { // loginIds
                Path<Object> path = root.get("loginId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> loginIds = (List) map.get("loginIds");
                loginIds.forEach(in::value);
                andPredicateList.add(in);
            }


            if (map.containsKey("startLoginTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("startLoginTime"))) {// 结账时间
                LocalDateTime startTime = DateTimeUtils.DateFormat(map.get("startLoginTime").toString());
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("loginTime").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("endLoginTime")
                    && !org.springframework.util.StringUtils.isEmpty(map.get("endLoginTime"))) {// 结账时间
                LocalDateTime endTime = DateTimeUtils.DateFormat(map.get("endLoginTime").toString());
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("loginTime").as(LocalDateTime.class), endTime));
            }

            // totalAccount  余额大于
            if (map.containsKey("minTotalAccount") && !StringUtils.isEmpty(map.get("minTotalAccount"))) {
                Object minTotalAccount = map.get("minTotalAccount");
                assert minTotalAccount instanceof String;
                Integer minTotalAccountInt = Integer.parseInt((String) minTotalAccount);

                Predicate predicate = cb.greaterThanOrEqualTo(root.get("totalAccount"), minTotalAccountInt);
                andPredicateList.add(predicate);
            }

            // totalAccount 余额小于
            if (map.containsKey("maxTotalAccount") && !StringUtils.isEmpty(map.get("maxTotalAccount"))) {
                Object maxTotalAccount = map.get("maxTotalAccount");
                assert maxTotalAccount instanceof String;
                Integer maxTotalAccountInt = Integer.parseInt((String) maxTotalAccount);
                Predicate predicate = cb.lessThanOrEqualTo(root.get("totalAccount"), maxTotalAccountInt);
                andPredicateList.add(predicate);
            }


            // minConsumptionTotal 消费总额大于
            if (map.containsKey("minConsumptionTotal") && !StringUtils.isEmpty(map.get("minConsumptionTotal"))) {
                Object minConsumptionTotal = map.get("minConsumptionTotal");
                assert minConsumptionTotal instanceof String;
                Integer minConsumptionTotalInt = Integer.parseInt((String) minConsumptionTotal);

                Predicate predicate = cb.greaterThanOrEqualTo(root.get("consumptionTotal"), minConsumptionTotalInt);
                andPredicateList.add(predicate);
            }

            // minConsumptionTotal 消费总额小于
            if (map.containsKey("maxConsumptionTotal") && !StringUtils.isEmpty(map.get("maxConsumptionTotal"))) {
                Object maxConsumptionTotal = map.get("maxConsumptionTotal");
                assert maxConsumptionTotal instanceof String;
                Integer maxConsumptionTotalInt = Integer.parseInt((String) maxConsumptionTotal);
                Predicate predicate = cb.lessThanOrEqualTo(root.get("consumptionTotal"), maxConsumptionTotalInt);
                andPredicateList.add(predicate);
            }

            // 登录ids，用于收银台上机记录列表管理页面
            if (map.containsKey("loginIds") && !ObjectUtils.isEmpty(map.get("loginIds"))) {
                Path<Object> path = root.get("loginId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> idNumbers = (List) map.get("loginIds");
                idNumbers.forEach(in::value);
                andPredicateList.add(in);
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            Predicate andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));
            return cb.and(andPredicate);
        };

    }

    public List<LogLogin> findList(Map<String, Object> map) {
        if (!StringUtils.isEmpty(map) && (!map.containsKey("createdStartTime") || StringUtils.isEmpty(map.get("createdStartTime")))) {
            map.put("createdStartTime", LocalDateTime.now().minusMonths(1).toString().substring(0, 19).replaceAll("T", " "));
        }
        if (!StringUtils.isEmpty(map) && (!map.containsKey("createdEndTime") || StringUtils.isEmpty(map.get("createdEndTime")))) {
            map.put("createdEndTime", LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " "));
        }
        Specification<LogLogin> logLoginSpecification = this.queryParam(map);
        return logLoginRepository.findAll(logLoginSpecification);
    }

    /**
     * 分页查询
     *
     * @param map
     * @param pageable
     * @return
     */
    public Page<LogLogin> findAll(Map<String, Object> map, Pageable pageable) {
        if (!StringUtils.isEmpty(map) && (!map.containsKey("createdStartTime") || StringUtils.isEmpty(map.get("createdStartTime")))) {
            map.put("createdStartTime", LocalDateTime.now().minusMonths(1).toString().substring(0, 19).replaceAll("T", " "));
        }
        if (!StringUtils.isEmpty(map) && (!map.containsKey("createdEndTime") || StringUtils.isEmpty(map.get("createdEndTime")))) {
            map.put("createdEndTime", LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " "));
        }
        Specification<LogLogin> logLoginSpecification = this.queryParam(map);
        return logLoginRepository.findAll(logLoginSpecification, pageable);
    }

    /**
     * 获取消费排行统计数据
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @param pageable
     * @return
     */
    public List<StatisticSumConsumptionBO> sumConsumptionByPlaceIdGroupByIdNumber(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime,List<String> cardIds,  Pageable pageable) {
        // 查询消费排行记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        List<Map<String, String>> list = logLoginRepository.findConsumerRanking(placeId, startDateTime, endDateTime, cardIds,now.minusMonths(2), now,pageable);
        List<StatisticSumConsumptionBO> bos = new ArrayList<>();
        BigDecimal onlineTimes = BigDecimal.ZERO;
        BigDecimal consumptionTotals = BigDecimal.ZERO;
        for (Map<String, String> map : list) {
            StatisticSumConsumptionBO bo = new StatisticSumConsumptionBO();
            bo.setCardId(map.get("cardId"));
//			bo.setIdNumber(map.get("idNumber"));
            if (map.get("idNumber").length() == 18) {
                bo.setIdNumber(map.get("idNumber").substring(0, 2) + "**********" + map.get("idNumber").substring(14, 18));
            }
            // 特殊证件为8位
            else if (map.get("idNumber").length() == 8) {
                bo.setIdNumber(map.get("idNumber").substring(0, 2) + "**" + map.get("idNumber").substring(4, 8));
            } else {
                bo.setIdNumber(map.get("idNumber"));
            }
            Object obj = map.get("counts");
            BigInteger counts = obj == null ? BigInteger.ZERO : (BigInteger) obj;
            bo.setCounts(counts.intValue());
            obj = map.get("consumptionTotals");
            consumptionTotals = (obj == null ? BigDecimal.ZERO : (BigDecimal) obj);
            bo.setConsumptionTotals(consumptionTotals.intValue());
            obj = map.get("onlineTimes");
            onlineTimes = obj == null ? BigDecimal.ZERO : (BigDecimal) obj;
            bo.setOnlineTimes(onlineTimes.intValue());
            bos.add(bo);
        }
        return bos;
    }

    public Long countConsumptionByPlaceIdGroupByIdNumber(String placeId, LocalDateTime startDateTime,
                                                         LocalDateTime endDateTime,List<String> cardIds) {
        try {
            VerifyParam.checkTimeDifferenceByMonth(startDateTime,endDateTime);
        } catch (ServiceException e){
            log.info("{} 查询月份时间段大于2个月，自动调整为2个月",placeId);
            startDateTime = endDateTime.minusMonths(1);
        }
        // 查询消费排行记录，先默认查2个月
       // LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.countConsumptionByPlaceIdGroupByIdNumber(placeId, startDateTime, endDateTime,cardIds, startDateTime, endDateTime);
    }

    /**
     * 查询场所下某个结账时间以前的所有上机记录
     *
     * @param placeId
     * @param endDateTime
     * @return
     */
    public List<LogLogin> findByPlaceIdAndLogoutTime(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        try {
            VerifyParam.checkTimeDifferenceByMonth(startDateTime,endDateTime);
        } catch (ServiceException e){
            log.info("{} 查询月份时间段大于2个月，自动调整为2个月",placeId);
            startDateTime = endDateTime.minusMonths(1);
        }
        // 查询历史上机记录，先默认查2个月
       // LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findByPlaceIdAndLogoutTimeGreaterThanEqualAndLogoutTimeLessThanEqualAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, startDateTime, endDateTime, startDateTime, endDateTime);
    }

    public List<LogLogin> findByPlaceIdAndLogoutTimeInCardIds(String placeId, LocalDateTime endDateTime, List<String> cardIds) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findByPlaceIdAndLogoutTimeGreaterThanEqualAndCardIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, endDateTime, cardIds, now.minusMonths(1), now);
    }

    /**
     * 组装上机记录页面数据
     *
     * @param placeId
     * @param logLoginBOS
     */
    public void getLogLoginBOS(String placeId, List<LogLoginBO> logLoginBOS, List<PlaceClientBO> placeClients,
                               List<PlaceAreaBO> placeAreaBOS) {
        List<Map<String, String>> areaNameClientNames = new ArrayList<>();
        List<String> cardIds = logLoginBOS.stream().map(LogLoginBO::getCardId).collect(Collectors.toList());

        // 根据cardId查询卡信息 主要获取姓名、idNumber
        List<BillingCard> billingCards = billingCardRepository.findAllByPlaceIdAndCardIdIn(placeId, cardIds);

        // 查询卡类型名称
        List<String> cardTypeIds = logLoginBOS.stream().map(LogLoginBO::getCardTypeId).collect(Collectors.toList());
        List<BillingCardType> billingCardTypes = cardTypeRepository.findByPlaceIdAndDeletedAndCardTypeIdIn(placeId, 0, cardTypeIds);

        // 组装 区域-终端 姓名、卡类型名称 字段
        List<PlaceClientBO> finalPlaceClients = placeClients;
        placeAreaBOS.forEach(e -> {
            finalPlaceClients.forEach(o -> {
                if (e.getAreaId().equals(o.getAreaId())) {
                    Map<String, String> areaNameClientNameMap = new HashMap<>();
                    areaNameClientNameMap.put(o.getClientId(), e.getAreaName() + "--" + o.getHostName());
                    areaNameClientNames.add(areaNameClientNameMap);
                }
            });
        });

        logLoginBOS.forEach(e -> {
            billingCards.forEach(o -> {
                if (e.getCardId().equals(o.getCardId())) {
                    //e.setCardTypeName(o.getCardTypeName());
                    e.setIdNumber(o.getIdNumber());
                    e.setIdName(o.getIdName());
                }
            });

            billingCardTypes.forEach(o -> {
                if (e.getCardTypeId().equals(o.getCardTypeId())) {
                    e.setCardTypeName(o.getTypeName());
                }
            });

            areaNameClientNames.forEach(m -> {
                if (m.containsKey(e.getClientId())) {
                    e.setAreaNameClientName(m.get(e.getClientId()));
                }
            });

//			// 身份证加密
//			if (!StringUtils.isEmpty(e.getIdNumber())) {
//				e.setIdNumber(e.getIdNumber().substring(0,6) + "********" + e.getIdNumber().substring(14,18));
//			}

        });
    }

    public void getCardTypeName(String placeId, List<LogLoginBO> logLoginBOS) {
        List<String> cardTypeIds = logLoginBOS.stream().map(LogLoginBO::getCardTypeId).collect(Collectors.toList());
        List<BillingCardType> billingCardTypes = cardTypeRepository.findByPlaceIdAndDeletedAndCardTypeIdIn(placeId, 0, cardTypeIds);
        logLoginBOS.forEach(e -> {
            billingCardTypes.forEach(o -> {
                if (e.getCardTypeId().equals(o.getCardTypeId())) {
                    e.setCardTypeName(o.getTypeName());
                }
            });
        });
    }

    /**
     * 根据cardId，查询时间范围内的总消费
     *
     * @param placeId
     * @param cardId
     * @param logoutStartTime
     * @param logoutEndTime
     * @return
     */
    public Integer sumConsumptionByPlaceIdAndCardId(String placeId, String cardId, LocalDateTime logoutStartTime,
                                                    LocalDateTime logoutEndTime) {
        Integer result = 0;
        try {
            VerifyParam.checkTimeDifferenceByMonth(logoutStartTime,logoutEndTime);
        } catch (ServiceException e){
            log.info("{} 查询月份时间段大于2个月，自动调整为2个月",placeId);
            logoutStartTime = logoutEndTime.minusMonths(1);
        }
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        if(StringUtils.isEmpty(cardId)){
            result = logLoginRepository.sumConsumptionByPlaceId(placeId, logoutStartTime, logoutEndTime, logoutStartTime, logoutEndTime);
        }else{
            result = logLoginRepository.sumConsumptionByPlaceIdAndCardId(placeId, cardId, logoutStartTime,
                    logoutEndTime, logoutStartTime, logoutEndTime);
        }

        return null == result ? 0:result;
    }

    public void checkAntiAddiction(String placeId, String clientId, BillingCard billingCard) {
        // client:permissionLoginTime:**************-1000178703 2023-05-17 15:05:36
        // permissionLoginTimeKey=client:permissionLoginTime:findByPlaceIdAndCardId:placeId-cardId
        String permissionLoginTimeKey = "client:permissionLoginTime:findByPlaceIdAndCardId:" + placeId + "-" + billingCard.getCardId();
        Boolean hasKey = stringRedisTemplate.hasKey(permissionLoginTimeKey);
        log.info("开始防沉迷限制检测，key={}, hasKey={}", permissionLoginTimeKey, hasKey);

        if (hasKey) {
            String permissionLoginTimeStr = stringRedisTemplate.opsForValue().get(permissionLoginTimeKey);
            LocalDateTime permissionLoginTime;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            try {
                permissionLoginTime = LocalDateTime.parse(permissionLoginTimeStr, formatter);
            } catch (DateTimeParseException e) {
                log.warn("获取防沉迷时间错误");
//				return new GenericResponse<>(ServiceCodes.SYSTEM_ERROR);
                throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
            }
            LocalDateTime now = LocalDateTime.now();
            boolean notPermission = now.isBefore(permissionLoginTime);
            if (notPermission) {
                log.info("当前用户被防沉迷系统限制，暂时不允许登录");
//				return new GenericResponse<>(ServiceCodes.BILLING_ANTI_ADDICTION_NO_PERMISSION);
                throw new ServiceException(ServiceCodes.BILLING_ANTI_ADDICTION_NO_PERMISSION);
            } else {
                log.info("当前用户已超过防沉迷时间，now={}, permissionLoginTime={}", now, permissionLoginTime);
            }
        } else {
            log.info("没有查到相应的限制, placeId={}, clientId={}", placeId, clientId);
        }
    }

    /**
     * 全场结账
     *
     * @param placeId
     * @param shiftId    交班，网吧后台不用传
     * @param cardIds    卡号
     * @param sourceType
     * @return
     */
    public GenericResponse<?> checkoutLogOut(String placeId, String shiftId, List<String> cardIds, SourceType sourceType) {

        // 在线信息Online
        List<BillingOnline> unfinishedOnline;
        // 在线的cardIds
        List<String> onlineCardIds;
        // 临时卡未上机的cardIds
        List<String> tempCardIds = new ArrayList<>();

        // 获取班次信息
        LogShift logShift = logShiftService.getVerifyShift(placeId, shiftId);

        boolean flag = false;
        // 查询场所在线信息
        if (SourceType.CASHIER.equals(sourceType)) {
            // 收银台操作的，不需要结账临时卡未上机，获取场所的在线信息
            unfinishedOnline = billingOnlineService.findUnfinishedByPlaceId(placeId);

        } else {
            unfinishedOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardIds(placeId, cardIds);
            onlineCardIds = unfinishedOnline.stream().map(BillingOnline::getCardId).distinct().collect(Collectors.toList());

            // 获取临时卡未上机的cardIds
            for (String cardId : cardIds) {
                if (!onlineCardIds.contains(cardId)) {
                    tempCardIds.add(cardId);
                }
            }
        }

        // 在线的 结账
        for (BillingOnline online : unfinishedOnline) {
            String clientId = online.getClientId();
            String cardId = online.getCardId();

            //全场结账临时卡只要是带了现金就不进行处理（无论是否上机消费）
            if (TEMP_CARD_TYPE_ID.equals(online.getCardTypeId())) {
                Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
                if (!billingCardOpt.isPresent()) {
                    continue;
                }
                BillingCard billingCard = billingCardOpt.get();
                if (billingCard.getCashAccount() > 0) {
                    flag = true;
                    continue;
                }

                //校验是否存在临时卡未使用的现金预包时，如果存在则直接提示去收银台
                Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findUnusedByPlaceIdAndCardId(placeId, cardId);
                if (packageTimeReserveOpt.isPresent()) {
                    PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get();
                    if (packageTimeReserve.getPackagePayFlag() == 2  //现金支付包时
                            || (packageTimeReserve.getPackagePayFlag() == 1
                            && packageTimeReserve.getPrice() > packageTimeReserve.getCostTemporaryOnlineAccount())) { //余额支付包时并且余额中包含现金
                        flag = true;
                        continue;
                    }
                }
            }
            List<String> params = Arrays.asList(placeId, clientId, cardId, sourceType.name(), OperationType.LOGOUT_ALL.name(), shiftId);
            GenericResponse<?> respLogout = clientLogoutServiceImpl.doService(params);
            if (respLogout.isResult()) {
                if (TEMP_CARD_TYPE_ID.equals(online.getCardTypeId())) {
                    tempCardIds.add(cardId);
                }
                // 保存轮询数据
                GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, clientId, online.getIdNumber(), BusinessType.LOGOUT);
                if (pollingBOGeneric.isResult()) {
                    PollingBO pollingBO = pollingBOGeneric.getData().getObj();
                    if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                        LogoutBusinessBO businessLogoutBO = new LogoutBusinessBO();
                        businessLogoutBO.setPlaceId(placeId);
                        businessLogoutBO.setClientId(clientId);
                        businessLogoutBO.setIdNumber(online.getIdNumber());
                        businessLogoutBO.setOperator("全场结账");
                        businessLogoutBO.setSourceType(sourceType);
                        businessLogoutBO.setBusinessType(BusinessType.LOGOUT);
                        businessLogoutBO.setCreated(LocalDateTime.now().toString());
                        businessLogoutBO.setBusinessId(pollingBO.getCashierBusinessId());
                        businessLogoutBO.setType(1);
                        // 保存收银台业务数据
                        notifyServerService.pushLogoutBusinessData(businessLogoutBO);
                    }
                }
            }
        }

        // 处理临时卡未上机
        List<LogRefund> logRefunds = new ArrayList<>();
        for (String cardId : tempCardIds) {
            Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
            if (!billingCardOpt.isPresent()) {
                continue;
            }
            BillingCard billingCard = billingCardOpt.get();
            if (TEMP_CARD_TYPE_ID.equals(billingCard.getCardTypeId())) {
                //全场结账临时卡只要是带了现金就不进行处理（无论是否上机消费）
                if (billingCard.getCashAccount() > 0) {
                    flag = true;
                    continue;
                }
                // 退卡
                logOperationService.addCancellationLogOperationAll(sourceType, billingCard, logShift);
                CancellationBO cancellationBO = billingCardService.billingCardCancellationNew(billingCard);
                if (cancellationBO.getRefundCashAmount() > 0) {
                    LogRefund logRefund = logRefundService.addLogRefund(placeId, billingCard, logShift, sourceType, cancellationBO.getRefundCashAmount());
                    logRefunds.add(logRefund);
                }
                if (cancellationBO.getRefundOnlineAmount() > 0) {
                    logRefundService.addRefundTask(billingCard, cancellationBO.getRefundOnlineAmount(), sourceType, logShift);
                }
            }
        }

        // 清除附加费临时表记录
        List<TempRecordSurcharge> tempRecordSurchargeOpt = tempRecordSurchargeService.findByPlaceIdAndCardIdInAndDeleted(placeId, tempCardIds);
        if (!CollectionUtils.isEmpty(tempRecordSurchargeOpt) && tempRecordSurchargeOpt.size() > 0) {
            for (TempRecordSurcharge tempRecordSurcharge : tempRecordSurchargeOpt) {
                tempRecordSurcharge.setUpdated(LocalDateTime.now());
                tempRecordSurcharge.setDeleted(1);
            }
            tempRecordSurchargeService.saveAll(tempRecordSurchargeOpt);
        }

        if (!CollectionUtils.isEmpty(logRefunds)) {
            logRefundService.saveBatch(logRefunds);
        }
        if (flag) {
            return new GenericResponse<>(ServiceCodes.BILLING_CASHIER_LOGOUT_ALL_CARD_STATE_HINT);
        }
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 按时间-会员消费排行
     *
     * @param placeId
     * @param startDateTime
     * @return
     */
    public List<Map<String, String>> findConsumerRanking(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime,List<String> cardIds, Pageable pageable) {
        try {
            VerifyParam.checkTimeDifferenceByMonth(startDateTime,endDateTime);
        } catch (ServiceException e){
            log.info("{} 查询月份时间段大于2个月，自动调整为2个月",placeId);
            startDateTime = endDateTime.minusMonths(1);
        }
       // LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findConsumerRanking(placeId, startDateTime, endDateTime,cardIds, startDateTime, endDateTime,pageable);
    }

    /**
     * 查询最后一次结账下机记录
     *
     * @param placeId
     * @param cardId
     * @return
     */
    public Optional<LogLogin> findFirstByPlaceIdAndCardIdAndLogoutTimeIsNotNullOrderByIdDesc(String placeId, String cardId) {
        // 查询历史上机记录，先默认查2个月
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findTop1ByPlaceIdAndCardIdAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, cardId, now.minusMonths(1),now);
    }

    public List<Map<String,String>> queryEverydayClientOnlineTime(String placeId,LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return logLoginRepository.queryEverydayClientOnlineTime(placeId, startDateTime,endDateTime);
    }

    public List<LogLogin> queryLastLoginUserByDayAgo(String placeId,LocalDateTime startDateTime, LocalDateTime endDateTime,List<String> cardTypeIds) {
        return logLoginRepository.queryLastLoginUserByDayAgo(placeId,cardTypeIds,startDateTime,LocalDateTime.now(),endDateTime);
    }

    public List<String> findAllCardId(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime){
        try {
            VerifyParam.checkTimeDifferenceByMonth(startDateTime,endDateTime);
        } catch (ServiceException e){
            log.info("{} 查询月份时间段大于2个月，自动调整为2个月",placeId);
            startDateTime = endDateTime.minusMonths(1);
        }
        // 查询消费排行记录，先默认查2个月
       // LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findAllCardId(placeId,startDateTime,endDateTime, startDateTime, endDateTime);
    }

    public void update(EntityManager entityManager, Map<String, Object> parameMap, Map<String, Object> updateMap) {
        // DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        entityManager.clear();
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaUpdate<LogLogin> update = cb.createCriteriaUpdate(LogLogin.class);
        Root<LogLogin> root = update.from(LogLogin.class);

        // 条件,有id就一定有起始时间
        if (parameMap.containsKey("id") && !StringUtils.isEmpty(parameMap.get("id"))) {
            Predicate idPredicate = cb.equal(root.get("id"), parameMap.get("id"));
           // String startTimeStr = parameMap.get("startTime").toString().length() >= 19 ? parameMap.get("startTime").toString().substring(0,19).replace("T"," ") : parameMap.get("startTime").toString().substring(0,16).replace("T"," ");
           // String endTimeStr = parameMap.get("endTime").toString().length() >= 19 ? parameMap.get("endTime").toString().substring(0,19).replace("T"," ") : parameMap.get("endTime").toString().substring(0,16).replace("T"," ");
            LocalDateTime startTime = DateTimeUtils.DateFormat(parameMap.get("startTime").toString());
            LocalDateTime endTime = DateTimeUtils.DateFormat(parameMap.get("endTime").toString());
            Predicate idPredicate2 = cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime);
            Predicate idPredicate3 = cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime);
            update.where(idPredicate,idPredicate2,idPredicate3);
        }

        // 拼接的参数
        for (Map.Entry<String, Object> entry : updateMap.entrySet()) {
            update.set(root.get(entry.getKey()), entry.getValue());
        }

        entityManager.createQuery(update).executeUpdate();
    }

    public Optional<LogLogin> queryLastLoginRecord(String idNumber){
        LocalDateTime now = LocalDateTime.now();
        return logLoginRepository.findTop1ByAndIdNumberAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(idNumber, now.minusMonths(1),now);
    }

    public Integer  sumOnlineTimeByPlaceAndIdNumber(String placeId,String idNumber,LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return logLoginRepository.sumOnlineTimeByPlaceAndIdNumber(placeId,idNumber,startDateTime,endDateTime);
    }
}
