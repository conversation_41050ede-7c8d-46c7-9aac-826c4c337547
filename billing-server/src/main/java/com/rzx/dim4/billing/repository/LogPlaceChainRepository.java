package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogPlaceChain;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年5余月16日 上午15:53:49
 */
public interface LogPlaceChainRepository extends JpaRepository<LogPlaceChain, Long> {

    List<LogPlaceChain> findByCurrPlaceIdAndCardIdAndLoginId(String currPlaceId, String cardId, String loginId);

    @Query(value = "SELECT cost_place_id AS costPlaceId,"
            + "curr_place_id AS currPlaceId,"
            + "SUM(cost_cash_account) AS sumCashAccount,"
            + "SUM(cost_present_account) AS sumPresentAccount,"
            + "login_id AS loginId FROM log_place_chain "
            + "where login_id IN(:loginIds) "
            + "AND cost_place_id = curr_place_id "
            + "GROUP BY card_id,cost_place_id,curr_place_id,login_id ", nativeQuery = true)
    List<Map<String, String>> querySumCostByCurrPlace(@Param("loginIds") List<String> loginIds);

    @Query(value = "SELECT cost_place_id AS costPlaceId,"
            + "curr_place_id AS currPlaceId,"
            + "SUM(cost_cash_account) AS sumCashAccount,"
            + "SUM(cost_present_account) AS sumPresentAccount,"
            + "login_id AS loginId FROM log_place_chain "
            + "where login_id IN(:loginIds) "
            + "AND cost_place_id != curr_place_id "
            + "GROUP BY card_id,cost_place_id,curr_place_id,login_id ", nativeQuery = true)
    List<Map<String, String>> querySumCostByCostPlace(@Param("loginIds") List<String> loginIds);

    @Query(value = "SELECT login_id AS loginIds FROM log_place_chain "
            + "where login_id IN(:loginIds) "
            + "AND cost_place_id != curr_place_id "
            + "GROUP BY login_id ", nativeQuery = true)
    List<String> queryRoamLoginIds (@Param("loginIds") List<String> loginIds);

    @Query(value = "SELECT login_id AS loginIds FROM log_place_chain "
            + "where login_id = :loginId "
            + "AND cost_place_id != curr_place_id "
            + "GROUP BY login_id ", nativeQuery = true)
    List<String> queryRoamLoginId (@Param("loginId") String loginId);

}
