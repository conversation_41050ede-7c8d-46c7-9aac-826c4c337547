package com.rzx.dim4.billing.service.Component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/11/10
 **/
@Slf4j
@Component
public class RabbitTemplateEnhance implements BeanPostProcessor {

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof RabbitTemplate) {
            log.info("RabbitTemplateEnhance postProcessAfterInitialization beanName: " + beanName);
            RabbitTemplate rabbitTemplate = (RabbitTemplate) bean;
            // return 回调函数（消息 由 exchange 到 queue）
            rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
                log.info("ReturnCallback message: " + message);
                log.info("ReturnCallback replyCode: " + replyCode);
                log.info("ReturnCallback replyText: " + replyText);
                log.info("ReturnCallback exchange: " + exchange);
                log.info("ReturnCallback routingKey: " + routingKey);
            });
            // confirm 回调函数（生产者到 broker）
            rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
                log.info("ConfirmCallback correlationData: " + correlationData);
                log.info("ConfirmCallback ack: " + ack);
                log.info("ConfirmCallback cause: " + cause);
            });
            return bean;
        }
        return bean;
    }
}
