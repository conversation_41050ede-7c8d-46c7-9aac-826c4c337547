package com.rzx.dim4.billing.service.impl.cashier;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogHbService;
import com.rzx.dim4.billing.service.PlaceBizConfigService;

/**
 * 收银台上报客户端异常状态
 * 
 * <AUTHOR>
 * @date 2021年9月9日 下午2:46:55
 */
@Service
public class CashierReportClientLostContactServiceImpl implements CoreService {

	@Autowired
	LogHbService logHbService;

	@Autowired
	PlaceBizConfigService placeBizService;

	@Override
	public GenericResponse<?> doService(List<String> params) {

		if (params.size() != 3) {
			return new GenericResponse<>(ServiceCodes.NULL_PARAM);
		}

		String placeId = params.get(0);
		String clientId = params.get(1);
		String statusStr = params.get(2); // 客户端状态字段，ClientStatus 对应code

		int status = 0;
		try {
			status = Integer.parseInt(statusStr);
		} catch (NumberFormatException e) {
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		// 优先处理场所状态
		/*
		 * 101，客户端反馈的网吧实名异常，设置PlaceBizConfig.disabled == 1，此时禁止客户端注册
		 * 100，客户端反馈的网吧实名从异常中恢复，设置PlaceBizConfig.disabled == 0
		 */
		if (status == 100 || status == 101) {
			PlaceBizConfig placeBizConfig = placeBizService.findByPlaceId(placeId);
			if (status == 100) {
				placeBizConfig.setPlaceRealnameDisabled(0);
			} else if (status == 101) {
				placeBizConfig.setPlaceRealnameDisabled(1);
			}
			placeBizService.updatePlaceBizConfig(placeBizConfig);
			return new GenericResponse<>(ServiceCodes.NO_ERROR);
		}

		if (status != 5) {
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		int result = logHbService.updateLostContactStatus(placeId, clientId);
		if (result > 0) {
			return new GenericResponse<>(ServiceCodes.NO_ERROR);
		}

		return new GenericResponse<>(ServiceCodes.OPT_ERROR);

	}
}
