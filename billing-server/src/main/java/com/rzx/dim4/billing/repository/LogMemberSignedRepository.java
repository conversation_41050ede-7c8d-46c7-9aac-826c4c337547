package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogMemberSigned;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface LogMemberSignedRepository extends JpaRepository<LogMemberSigned, Long>, JpaSpecificationExecutor<LogMemberSigned> {

    Optional<LogMemberSigned> findByPlaceIdAndCardIdAndSignedDate (String placeId, String cardId, String signedDate);

    List<LogMemberSigned> findByPlaceIdAndCardIdAndSignedDateInOrderByIdDesc (String placeId, String cardId, List<String> signedDateList);

}
