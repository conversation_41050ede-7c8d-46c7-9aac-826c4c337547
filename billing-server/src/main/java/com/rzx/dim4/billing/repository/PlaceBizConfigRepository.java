package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.PlaceBizConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2021年11月12日 上午11:05:00
 */
public interface PlaceBizConfigRepository extends JpaRepository<PlaceBizConfig, Long> {

	Optional<PlaceBizConfig> findByPlaceId(String placeId);

	List<PlaceBizConfig> findByPlaceIdIn(List<String> placeIds);

	List<PlaceBizConfig> findByThirdAccountIdInAndDeleted(List<String> thirdAccountIds, int deleted);

	@Transactional
	@Modifying
	@Query(value = "update place_biz_config set temp_card_points_upgrade = 0, upgrade_user_level_flag = :upgradeUserLevelFlag,downgrade_user_level_flag = :downgradeUserLevelFlag,updated = now() where place_id = :placeId", nativeQuery = true)
	int closeBizConfigTempCardPointsUpgrade(@Param("placeId") String placeId, @Param("upgradeUserLevelFlag") int upgradeUserLevelFlag,
											@Param("downgradeUserLevelFlag") int downgradeUserLevelFlag); // 查询包时


}
