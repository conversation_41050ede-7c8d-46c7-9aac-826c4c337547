package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.TempWanxiangTopup;
import com.rzx.dim4.billing.repository.TempWanxiangTopupRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 临时充值记录
 */
@Service
public class TempWanxiangTopupService {

    @Autowired
    TempWanxiangTopupRepository tempWanxiangTopupRepository;

    public void saveAll(List list) {
        tempWanxiangTopupRepository.saveAll(list);
    }

    public List<TempWanxiangTopup> findByPlaceIdAndCardId(String placeId, String cardId) {
        return tempWanxiangTopupRepository.findByPlaceIdAndCardId(placeId, cardId);
    }


}
