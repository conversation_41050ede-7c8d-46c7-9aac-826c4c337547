package com.rzx.dim4.billing.scheduler;

import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.impl.client.ClientLogoutServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class BillingRoomScheduler {

	@Autowired
	BillingOnlineService billingOnlineService;

	@Autowired
	BillingCardService billingCardService;

	@Autowired
	ClientLogoutServiceImpl clientLogoutServiceImpl;

	@Autowired
	LogOperationService logOperationService;

	@Autowired
	LogLoginService logLoginService;

	@Autowired
	PlaceBizConfigService placeBizConfigService;

	@Autowired
	LogHbService logHbService;

	@Autowired
	LogShiftService logShiftService;

	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Autowired
	BillingRuleCommonService billingRuleCommonService;

	@Autowired
	BillingRuleAccService billingRuleAccService;

	@Autowired
	LogAccService logAccService;

	@Autowired
	LogRoomService logRoomService;

	String serverBillingRoomLockKey = "[billing]server_billing_room_lock";

	/**
	 * 每10分钟查询是否有包间区域主卡下机，副卡未下机的
	 */
	@Scheduled(initialDelay = 1000 * 30, fixedRate = 1000 * 60 * 10)
	public void serverBillingRoom() {

		log.info("中心包间查询未下机的副卡开始, 获取锁:::" + serverBillingRoomLockKey);
		boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(serverBillingRoomLockKey, "locked");
		if (locked) {
			log.info("获取成功，设置5分钟TTL");
			stringRedisTemplate.expire(serverBillingRoomLockKey, 5, TimeUnit.MINUTES);
		} else {
			log.info("获取失败，查询锁状态");
			long expired = stringRedisTemplate.getExpire(serverBillingRoomLockKey, TimeUnit.SECONDS);
			log.info("没有获得锁, 停止中心包间查询未下机任务, TTL剩余:::" + expired);
			if (expired == -1L) {
				log.info("上次TTL设置失败，重新设置5分钟TTL");
				stringRedisTemplate.expire(serverBillingRoomLockKey, 5, TimeUnit.MINUTES);
			}
			log.info("中心包间查询未下机任务结束！！！");
			return;
		}

		// 查询所有主卡下机 副卡在线的记录
		List<LogRoom> notDismountedRoom = logRoomService.queryNotDismountedRoom();

		if (CollectionUtils.isEmpty(notDismountedRoom)) {
			log.info("包间查询未下机任务查询结果:::中心没有找到包间未下机的副卡");
			log.info("释放锁:::" + serverBillingRoomLockKey);
			stringRedisTemplate.delete(serverBillingRoomLockKey);
			return; // 没有未下机的副卡
		}
		log.info("中心找到包间未下机的副卡:::" + notDismountedRoom.size());

		for (LogRoom logRoom : notDismountedRoom) { // 循环处理
			String placeId = logRoom.getPlaceId();
			String clientId = logRoom.getClientId();
			String cardId = logRoom.getCardId();

			LocalDateTime nowTime = LocalDateTime.now();

			log.info("{}-{}-{}【包间】处理逻辑......", placeId, clientId, cardId);
			List<String> loginOutParamList = new ArrayList<>();
			loginOutParamList.add(placeId);
			loginOutParamList.add(clientId);
			loginOutParamList.add(cardId);
			GenericResponse<?> repsLogout = clientLogoutServiceImpl.doService(loginOutParamList);
			if (!repsLogout.isResult()) {
				log.error("{}-{}-{}【包间】客户端跟随主卡自动结账失败，调用ClientLogout返回" + repsLogout.getCode() + "!!!", placeId,
						clientId, cardId);
				continue;
			}
			logRoom.setFinished(1);
			logRoom.setFinishedTime(nowTime);
			logRoomService.save(logRoom);
			log.info("{}-{}-{}【包间】处理逻辑结束.", placeId, clientId, cardId);
		}
		log.info("中心包间查询未下机任务结束！！！释放锁:::" + serverBillingRoomLockKey);
		stringRedisTemplate.delete(serverBillingRoomLockKey);
	}

}