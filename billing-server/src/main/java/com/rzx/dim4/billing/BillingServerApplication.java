package com.rzx.dim4.billing;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@EnableEurekaClient
@EnableScheduling
@EnableAsync(proxyTargetClass = true) // 开启异步调用
@EnableFeignClients(basePackages = "com.rzx.dim4.base.service.feign")
@ComponentScan(basePackages = { "com.rzx.dim4.billing", "com.rzx.dim4.base"})
public class BillingServerApplication {

	public static void main(String[] args) {
		SpringApplication.run(BillingServerApplication.class, args);
	}

	@Bean
	@LoadBalanced
	RestTemplate restTemplate() {
		return new RestTemplate();
	}

}