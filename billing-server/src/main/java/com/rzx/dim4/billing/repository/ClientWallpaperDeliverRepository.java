package com.rzx.dim4.billing.repository;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.rzx.dim4.billing.entity.ClientWallpaperDeliver;

public interface ClientWallpaperDeliverRepository
		extends JpaRepository<ClientWallpaperDeliver, Long>, JpaSpecificationExecutor<ClientWallpaperDeliver> {

	Optional<ClientWallpaperDeliver> findTop1ByIsDefaultOrderById(int isDefault);

	Optional<ClientWallpaperDeliver> findTop1ByStartTimeLessThanEqualAndEndTimeGreaterThanEqualAndPlaceIdsLikeAndWallpaperTypeOrderByIdDesc(
			LocalDateTime startTime, LocalDateTime endTime, String placeId,int wallpaperType);

}