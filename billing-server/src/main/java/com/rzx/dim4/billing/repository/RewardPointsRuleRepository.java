package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.RewardPointsRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface RewardPointsRuleRepository extends JpaRepository<RewardPointsRule, Long>, JpaSpecificationExecutor<RewardPointsRule> {

    Optional<RewardPointsRule> findTop1ByOrderByIdDesc();

    Optional<RewardPointsRule> findByPlaceIdAndDeleted(String placeId, int deleted);

}
