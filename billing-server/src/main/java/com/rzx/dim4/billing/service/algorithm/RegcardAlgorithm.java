package com.rzx.dim4.billing.service.algorithm;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 注册卡业务公共方法
 */
@Service
@Slf4j
public class RegcardAlgorithm {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    NotifyServerService notifyServerService;

    /**
     * 当注册卡中心服务器异常时，调用该方法自动关闭注册卡开关，发送通知信息
     *
     * @param placeConfigBO
     */
    public void closeRegcard(PlaceConfigBO placeConfigBO) {
        String placeId = placeConfigBO.getPlaceId();

        // 查询场所信息
        GenericResponse<ObjDTO<PlaceProfileBO>> genericResponse = placeServerService.findByPlaceId(placeId);
        if (!genericResponse.isResult()) {
            return;
        }
        PlaceProfileBO placeProfileBO = genericResponse.getData().getObj();

        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        placeConfigBO.setRegcard(0);
        placeConfigBO.setUpdated(LocalDateTime.now());

        try {
            placeServerService.savePlaceConfig(requestTicket, placeConfigBO);
            sendRequest(placeId, placeProfileBO.getDisplayName());
        } catch (Exception e) {
            log.info("注册卡自动关闭失败............场所编码::::::::" + placeId);
        }
    }

    /**
     * 企业微信通知数据结构
     */
    @Data
    class TemplateData {
        @NonNull
        String msgtype;
        @NonNull
        Map<String, String> markdown;
    }

    /**
     * 企业微信消息通知发送请求
     *
     * @param placeId
     * @param displayName
     */
    private void sendRequest(String placeId, String displayName) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String dateTimeStr  = formatter.format(LocalDateTime.now());

        String msg = "预警通知<font color='warning\'>注册卡中心异常！！！</font>，请相关同事注意。\n"
                + ">时间:<font color=\"info\">" + dateTimeStr + "</font> \n"
                + ">原因:<font color=\"info\">主备服务器异常</font> \n"
                + ">场所编号:<font color=\"info\">" + placeId + "</font> \n"
                + ">场所名称:<font color=\"info\">" + displayName + "</font> \n"
                + ">操作:<font color=\"info\">自动关闭单店注册卡开关</font> \n";
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("content", msg);
        msgMap.put("mentioned_list", "@all"); // 艾特全部成员
        TemplateData data = new TemplateData("markdown", msgMap);
        Gson gson = new Gson();
        String requestBody = gson.toJson(data);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<String>(requestBody, headers);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2856f23e-a9e2-465c-a035-ce512c2afa2f";
        String response = restTemplate.postForObject(url, entity, String.class);
        log.info("企业微信告警群消息[" + msg + "]发送时间[" + dateTimeStr + "]结果:::" + response);
    }

}
