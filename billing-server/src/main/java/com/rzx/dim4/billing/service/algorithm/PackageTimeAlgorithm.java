package com.rzx.dim4.billing.service.algorithm;

import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.entity.BillingRulePackageTime;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Random;

/**
 * 包时计算nextTime/endTIme
 */
public class PackageTimeAlgorithm {
	
	/**
     * 计算StartTime
     * @param localTime  当前时间作为参数且不能在这个方法里面使用LocalDateTime.now()代替，列如: 21:59:59     22:00:01  getHour()之后就是21/22,所以不能替换
     * @param billingRulePackageTime
     * @return
     */
    public static LocalDateTime getStartTime(LocalDateTime localTime, BillingRulePackageTime billingRulePackageTime) {

        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();

        Time houseTime = DateTimeUtils.floatFormatTime(start);
        int endHours = houseTime.getHours();// 小时数
        int endMinute = houseTime.getMinutes();
        int endSeconds = houseTime.getSeconds();

        // 如果选择的是0点到0点: start==end
        if (start == end) {
            end += 24;
        }

        LocalDateTime startTime;

        // 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
        LocalDateTime middleTime = localTime.minusHours(localTime.getHour()).minusMinutes(localTime.getMinute()).minusSeconds(localTime.getSecond()).minusNanos(localTime.getNano());
        // 包时段
        if (billingRulePackageTime.getPackageFlag() == 0) {
            return null;
        } else if (billingRulePackageTime.getPackageFlag() == 1) {
            if (start - end > 0) { // 跨天
                // 如果当前时间跨天
                if (localTime.getHour() < end) {
                    startTime = middleTime.minusDays(1).plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
                } else {
                    startTime = middleTime.plusHours(Double.valueOf(endHours).intValue()).plusMinutes(endMinute).plusSeconds(endSeconds);
                }
            } else {
                startTime = middleTime.plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
            }
        } else {
            // 包时长
            startTime = localTime;
        }

        return startTime;
    }

    /**
     * 计算nextTime
     * @param localTime  当前时间作为参数且不能在这个方法里面使用LocalDateTime.now()代替，列如: 21:59:59     22:00:01  getHour()之后就是21/22,所以不能替换
     * @param billingRulePackageTime
     * @return
     */
    public static LocalDateTime getNextTime(LocalDateTime localTime, BillingRulePackageTime billingRulePackageTime) {
        LocalDateTime nextTime;
        // 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
        LocalDateTime middleTime = localTime.minusHours(localTime.getHour()).minusMinutes(localTime.getMinute()).minusSeconds(localTime.getSecond()).minusNanos(localTime.getNano());

        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();


        Time houseTime = DateTimeUtils.floatFormatTime(end);

        int endHours = houseTime.getHours();// 小时数
        int endMinute = houseTime.getMinutes();
        int endSeconds = houseTime.getSeconds();

        // 如果选择的是0点到0点: start==end
        if (start == end) {
            end += 24;
        }

        // 计算开始时间是否已经跨天了
        int daysNum = (int) (localTime.toLocalDate().toEpochDay() - LocalDateTime.now().toLocalDate().toEpochDay());

        // 标准计费
        if (billingRulePackageTime.getPackageFlag() == 0) {
            return null;
        } else if (billingRulePackageTime.getPackageFlag() == 1) {
            //包时段
            if (start - end > 0) { // 跨天
                // 如果当前时间跨天

                if (localTime.getHour() >= end && daysNum == 0) {
                    nextTime = middleTime.plusDays(1).plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
                } else {
                    nextTime = middleTime.plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
                }

            } else {
                nextTime = middleTime.plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
            }
        } else {
            // 包时长
            float limitDurationEndTime = DateTimeUtils.timeFormatFloat(billingRulePackageTime.getLimitDurationEndTime()); //强制结束时间
            if ( limitDurationEndTime > 0) {

                LocalDateTime normalEndTime = localTime.plusMinutes(billingRulePackageTime.getDurationTime()); //正常结束时间
                LocalDateTime packageEndTime = LocalDateTime.of(normalEndTime.toLocalDate(), LocalTime.of(billingRulePackageTime.getLimitDurationEndTime().getHours(),
                        billingRulePackageTime.getLimitDurationEndTime().getMinutes(),billingRulePackageTime.getLimitDurationEndTime().getSeconds()));//强制结束时间

                if (packageEndTime.isBefore(normalEndTime)) {
                    nextTime = packageEndTime;
                } else {
                    nextTime = normalEndTime;
                }
            } else {
                nextTime = localTime.plusMinutes(billingRulePackageTime.getDurationTime());
            }
        }

        int hour = nextTime.getHour();
        int minute = nextTime.getMinute();
        int second = nextTime.getSecond();
        double  nowHourNum = hour + (double) minute / 60 + (double) second / 3600;
        Random random = new Random();
        int randomNumber = random.nextInt(241); // 生成0-240的随机数
        if (0 == second && (nowHourNum == 7 || nowHourNum == 8 || nowHourNum == 9)) {
            nextTime = nextTime.plusSeconds(randomNumber); // 针对7、8、9点结束的包时，nextTime随机加120以内的秒数，打散请求并发.
        }

        return nextTime;
    }

    /**
     * 获取第二次包时的开始时间
     * @param localTime
     * @param billingRulePackageTime
     * @return
     */
    public static LocalDateTime getFutureStartTime(LocalDateTime localTime, BillingRulePackageTime billingRulePackageTime) {
        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();

        Time houseTime = DateTimeUtils.floatFormatTime(start);
        int endHours = houseTime.getHours();// 小时数
        int endMinute = houseTime.getMinutes();
        int endSeconds = houseTime.getSeconds();

        // 如果选择的是0点到0点: start==end
        if (start == end) {
            end += 24;
        }

        LocalDateTime startTime;

        // 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
        LocalDateTime middleTime = localTime.minusHours(localTime.getHour()).minusMinutes(localTime.getMinute()).minusSeconds(localTime.getSecond()).minusNanos(localTime.getNano());

        if (start - end > 0) { // 跨天
            // 如果当前时间跨天
            if (localTime.getHour() < end) {
                startTime = middleTime.minusDays(1).plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
            } else {
                startTime = middleTime.plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
            }
        } else {
            startTime = middleTime.plusHours(endHours).plusMinutes(endMinute).plusSeconds(endSeconds);
        }

        return startTime;
    }

    /**
     * 计算分钟数
     * @param time
     * @return
     */
    public static int getMinute(double time){
        double timeMinute = time - Math.floor(time);

        // 转换为分钟
        timeMinute = new BigDecimal(timeMinute * 60).setScale(2, RoundingMode.HALF_UP).floatValue();

        // 取整
        timeMinute = Math.round(timeMinute);

        return Double.valueOf(timeMinute).intValue();
    }

    /**
     * float时间 转换为 LocalDateTime
     * @param billingRulePackageTime
     * @return
     */
    public static LocalDateTime getDateTime(BillingRulePackageTime billingRulePackageTime){
        LocalDateTime now = LocalDateTime.now();


        float startTime = billingRulePackageTime.getStartTime();
        float endTime = billingRulePackageTime.getEndTime();
        if (startTime > endTime && now.getHour() >= startTime) { // 跨天
            now = now.plusDays(1);
        }

        Time houseTime = DateTimeUtils.floatFormatTime(endTime);

        // 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
        LocalDateTime middleTime = now.minusHours(now.getHour()).minusMinutes(now.getMinute()).minusSeconds(now.getSecond()).minusNanos(now.getNano());

//        LocalDateTime time = middleTime.plusHours(Double.valueOf(startHours).longValue()).plusMinutes(Double.valueOf(startMinute).longValue());
        LocalDateTime time = middleTime.plusHours(houseTime.getHours()).plusMinutes(houseTime.getMinutes()).plusSeconds(houseTime.getSeconds());

        return time;

    }

    /**
     * float时间 转换为 LocalDateTime
     * @param floatTime
     * @return
     */
    public static LocalDateTime getStartTime(float floatTime){
        LocalDateTime now = LocalDateTime.now();

        Time time = DateTimeUtils.floatFormatTime(floatTime);

        // 获取中间时间，取当前时间的 年、月、日、时间初始化为00:00:00,用来替换包时开始时刻，结束时刻，如:23:00:00
        LocalDateTime middleTime = now.minusHours(now.getHour()).minusMinutes(now.getMinute()).minusSeconds(now.getSecond()).minusNanos(now.getNano());

        LocalDateTime startTime = middleTime.plusHours(time.getHours()).plusMinutes(time.getMinutes()).plusSeconds(time.getSeconds());

        return startTime;
    }


}
