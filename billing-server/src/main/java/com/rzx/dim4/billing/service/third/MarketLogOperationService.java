package com.rzx.dim4.billing.service.third;

import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.VerifyParam;
import com.rzx.dim4.billing.entity.LogOperation;
import com.rzx.dim4.billing.entity.third.MarketLogOperation;
import com.rzx.dim4.billing.repository.third.MarketLogOperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MarketLogOperationService {

    @Autowired
    MarketLogOperationRepository marketLogOperationRepository;

    /**
     * 根据orderId查询记录信息
     * @param orderId
     * @return
     */
    public Optional<MarketLogOperation> findByPlaceIdAndOrderId (String PlaceId,String orderId) {
        return marketLogOperationRepository.findByPlaceIdAndOrderId(PlaceId,orderId);
    }

    /**
     * 保存
     * @param marketLogOperation
     * @return
     */
    public MarketLogOperation save (MarketLogOperation marketLogOperation) {
        return marketLogOperationRepository.save(marketLogOperation);
    }

    public int sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(String placeId, String shiftId, SourceType sourceType) {
        Integer result = marketLogOperationRepository.sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(placeId, shiftId, sourceType);
        return result == null ? 0 : result;
    }

    public Map<String, Integer> sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeId(String placeId, String shiftId, String cardTypeId) {
        Map<String, String> map = marketLogOperationRepository.sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeId(placeId, shiftId, cardTypeId);

        Map<String,Integer> result = new HashMap<>();
        result.put("sumCash",Integer.valueOf(map.getOrDefault("sumCash","0")));
        result.put("sumPresent",Integer.valueOf(map.getOrDefault("sumPresent","0")));
        return result;
    }

    public Map<String, Integer> sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeIdNotIn(String placeId, String shiftId, List<String> cardTypeIds) {
        Map<String, String> map = marketLogOperationRepository.sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeIdNotIn(placeId, shiftId, cardTypeIds);

        Map<String,Integer> result = new HashMap<>();
        result.put("sumCash",Integer.valueOf(map.getOrDefault("sumCash","0")));
        result.put("sumPresent",Integer.valueOf(map.getOrDefault("sumPresent","0")));
        return result;
    }


    public Page<MarketLogOperation> findAll(Map<String, Object> map, Pageable pageable) {
        Specification<MarketLogOperation> specification = this.queryParams(map);
        return marketLogOperationRepository.findAll(specification, pageable);
    }


    public Specification<MarketLogOperation> queryParams(Map<String, Object> map) {

        return (root, query, cb) -> {

            List<Predicate> andPredicateList = new ArrayList<>();

            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所ID
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
            }

            if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 交班ID
                andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
            }


            if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 交班ID
                andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
            }


            if (map.containsKey("optType") && !StringUtils.isEmpty(map.get("optType"))) {// 交班ID
                andPredicateList.add(cb.equal(root.get("optType").as(Integer.class), map.get("optType")));
            }

            if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                if (map.get("idNumber").toString().length() == 18) {
                    andPredicateList.add(cb.equal(root.get("idNumber"), map.get("idNumber")));
                } else {
                    andPredicateList.add(cb.like(root.get("idNumber"), map.get("idNumber") + "%"));
                }
            }

            if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 姓名
                andPredicateList.add(cb.like(root.get("idName"), map.get("idName") + "%"));
            }

            if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {// 来源
                andPredicateList.add(cb.equal(root.get("sourceType").as(SourceType.class), SourceType.valueOf(map.get("sourceType").toString())));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];

            return cb.and(andPredicateList.toArray(andPredicateArr));
        };
    }

}
