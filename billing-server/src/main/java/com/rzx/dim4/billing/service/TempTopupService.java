package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.TempTopup;
import com.rzx.dim4.billing.repository.TempTopupRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 临时充值记录
 */
@Service
public class TempTopupService {

    @Autowired
    TempTopupRepository tempTopupRepository;

    public TempTopup save(TempTopup tempTopup) {
        return tempTopupRepository.save(tempTopup);
    }

    public Optional<TempTopup> findByPlaceIdAndIdNumberAndUsed(String placeId, String idNumber, int used) {
        return tempTopupRepository.findByPlaceIdAndIdNumberAndUsed(placeId, idNumber, used);
    }



}
