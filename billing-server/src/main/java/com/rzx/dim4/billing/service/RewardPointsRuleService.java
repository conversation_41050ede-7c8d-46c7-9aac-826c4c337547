package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.ExchangePointsType;
import com.rzx.dim4.billing.entity.RewardPointsRule;
import com.rzx.dim4.billing.repository.RewardPointsRuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
public class RewardPointsRuleService {

    @Autowired
    RewardPointsRuleRepository rewardPointsRuleRepository;

    /**
     * 保存积分赠送规则
     * @param rewardPointsRule
     * @return
     */
    public RewardPointsRule save (RewardPointsRule rewardPointsRule) {
        return rewardPointsRuleRepository.save(rewardPointsRule);
    }

    /**
     * 根据规则id查询积分赠送规则信息
     * @param placeId
     * @return
     */
    public Optional<RewardPointsRule> findByPlaceId (String placeId) {
        return rewardPointsRuleRepository.findByPlaceIdAndDeleted(placeId,0);
    }

    /**
     * 根据金额获取对应的积分赠送值
     * @param placeId
     * @param amount
     * @return
     */
    public int getRewardPointsNum (String placeId, int amount, ExchangePointsType exchangePointsType) {
        Optional<RewardPointsRule> rewardPointsRuleOpt = rewardPointsRuleRepository.findByPlaceIdAndDeleted(placeId,0);
        if (!rewardPointsRuleOpt.isPresent()) {
            return 0;
        }
        RewardPointsRule rewardPointsRule = rewardPointsRuleOpt.get();
        int pointsNum = 0;
        if (exchangePointsType.equals(ExchangePointsType.TOPUP)) {
            pointsNum = rewardPointsRule.getTopup();
        } else if (exchangePointsType.equals(ExchangePointsType.LOGOUT)) {
            pointsNum = rewardPointsRule.getConsume();
        }
        if ((amount < pointsNum) || pointsNum <= 0) {
            return 0;
        }
        return Math.round(amount/pointsNum);
    }

}
