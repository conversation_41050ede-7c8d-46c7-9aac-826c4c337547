package com.rzx.dim4.billing.service.util;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.BillingCardTypeService;
import com.rzx.dim4.billing.service.CashierAuthorityService;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.PlaceBizConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024年01月23日 11:56
 */
@Service
public class CreateCardValidator {

    @Autowired
    private BillingCardTypeService billingCardTypeService;
    @Autowired
    private CashierAuthorityService cashierAuthorityService;
    @Autowired
    private PlaceBizConfigService placeBizConfigService;
    @Autowired
    private LogOperationService logOperationService;
    @Autowired
    private PlaceServerService placeServerService;

    //校验自助开卡所需的条件，如果为自助开卡,需要校验最低开卡金额，收银员自助开卡权限，收银员开会员卡种类权限，标记开卡为自助开卡
    public void verifyAutoCreateCard(String placeId, String cardTypeId, int cashAmount, LogShift logShift) {
        // 验证自助开卡权限
        Optional<CashierAuthority> cashierAuthorityOpt = cashierAuthorityService.findByPlaceIdAndAccountId(placeId, logShift.getLoginAccountId());
        if (!cashierAuthorityOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }
        CashierAuthority cashierAuthority = cashierAuthorityOpt.get();
        if (!cashierAuthority.getOpAuthority().contains(String.valueOf(ServiceIndexes.CashierPermissionsCreateBillingCardWithAmountNew.getValue()))) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }
        //验证开会员卡种类权限
        if (!cashierAuthority.getCardTypeAuthority().contains(String.valueOf(cardTypeId))) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }
        //验证最低开卡金额
        BillingCardType billingCardType = getBillingCardType(placeId, cardTypeId);
        int minCreateCardAmount = billingCardType.getMinCreateCardAmount();
        if (cashAmount < minCreateCardAmount) { // 如果收银台传入的开卡金额小于最低开卡金额，返回开卡失败
            throw new ServiceException(ServiceCodes.BILLING_FAILED_TO_CREATE_CARD, "开卡失败，最低开卡金额为" + minCreateCardAmount / 100 + "元");
        }
    }

    //开卡-校验用户证件号或是否允许特殊证件
    public String verifyIdNumber(String placeId,String idNumber,String name){
        if (StringUtils.isEmpty(idNumber) || idNumber.length() < 6 || idNumber.length() > 20 || StringUtils.isEmpty(name) || name.length() > 50) { //
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        if (idNumber.length() != 18) { // 非大陆居民身份证
            String regex = "^[a-zA-Z0-9]+$"; // 只能是数字和字母
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(idNumber);
            if (!matcher.matches()) {
                throw new ServiceException(ServiceCodes.ID_NUMBER_ERROR);
            }
            PlaceBizConfig config = placeBizConfigService.findByPlaceId(placeId); // 查询网吧的非身份证配置
            if (config.getNonIdNumber() > 0) { // 大于0，允许非身份证号注册，需要判断今天已经激活的非身份证数量
                int activated = logOperationService.countTodayActivatedNonIdNumberByPlaceId(placeId);
                if (activated >= config.getNonIdNumber()) { // 激活数量已经达到上限
                    throw new ServiceException(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
                }
            } else if (config.getNonIdNumber() == 0) { // 等于0， 不允许非身份证号注册，不允许注册非身份证号码
                throw new ServiceException(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
            }
        } else { // 大陆居民身份证
            // 验证身份证合法性，姓名不能为空
            idNumber = idNumber.toUpperCase();
            boolean flag = IdNumberValidator.verificate(idNumber);
            if (!flag || StringUtils.isEmpty(name)) {
                throw new ServiceException(ServiceCodes.ID_NUMBER_ERROR);
            }
        }
        return idNumber;
    }

    public PlaceConfigBO verifyOnlineAuth(String placeId){
        // 校验场所是否支持在线充值
        GenericResponse<ObjDTO<PlaceConfigBO>> responsePlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (responsePlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = responsePlaceConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }
        // 检验在线充值开关
        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }
        //校验场所是否支持二维码支付
        PlaceBizConfig byPlaceId = placeBizConfigService.findByPlaceId(placeId);
        if(StringUtils.isEmpty(byPlaceId.getPayPatter()) || (!byPlaceId.getPayPatter().contains("1") && !byPlaceId.getPayPatter().contains("2"))){ //0现金支付 1二维码支付 2扫码枪支付 3上网卡支付 4内部消耗,需要有1二维码支付的权限
            throw new ServiceException(ServiceCodes.BILLING_CONFIG_QRCODE_NO_SUPPORT);
        }
        return placeConfigBO;
    }

    public void verifyCreateAuthAndMinAmount(String placeId, String cardTypeId, int cashAmount, LogShift logShift) {
        // 验证自助开卡权限
        Optional<CashierAuthority> cashierAuthorityOpt = cashierAuthorityService.findByPlaceIdAndAccountId(placeId, logShift.getLoginAccountId());
        if (!cashierAuthorityOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }
        CashierAuthority cashierAuthority = cashierAuthorityOpt.get();
        //验证开会员卡种类权限
        if (!cashierAuthority.getCardTypeAuthority().contains(String.valueOf(cardTypeId))) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }
        //验证最低开卡金额
        BillingCardType billingCardType = getBillingCardType(placeId, cardTypeId);
        int minCreateCardAmount = billingCardType.getMinCreateCardAmount();
        if (cashAmount < minCreateCardAmount) { // 如果收银台传入的开卡金额小于最低开卡金额，返回开卡失败
            throw new ServiceException(ServiceCodes.BILLING_FAILED_TO_CREATE_CARD, "开卡失败，最低开卡金额为" + minCreateCardAmount / 100 + "元");
        }
    }


    private BillingCardType getBillingCardType(String placeId, String cardTypeId) {
        // 验证卡类型ID
        Optional<BillingCardType> optBillingCardType = billingCardTypeService.findByPlaceIdAndCardTypeId(placeId, cardTypeId);
        if (!optBillingCardType.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }
        return optBillingCardType.get();
    }
}
