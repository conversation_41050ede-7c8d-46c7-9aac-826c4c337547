package com.rzx.dim4.billing.service.factory;

import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.impl.ProofTimeServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.*;
import com.rzx.dim4.billing.service.impl.cashier.webPage.CashierQueryMemberStaticsServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.webPage.CashierQueryPageLogLoginServiceImpl;
import com.rzx.dim4.billing.service.impl.cashier.webPage.CashierQueryPageMemberServiceImpl;
import com.rzx.dim4.billing.service.impl.client.*;
import com.rzx.dim4.billing.service.impl.third.ThirdCreateBillingCardSimpleServiceImpl;
import com.rzx.dim4.billing.service.impl.third.ThirdNonBillingClientLoginServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date Jun 4, 2020 1:57:12 PM
 */
@Component
public class ServiceFactory {

    @Autowired
    private ProofTimeServiceImpl proofTimeServiceImpl;

    // 收银台
    // ============================================================================================================
    @Autowired
    private CashierActivationServiceImpl cashierActivationServiceImpl;
    @Autowired
    private CashierCancelPackageTimeServiceImpl cashierCancelPackageTimeServiceImpl;
    @Autowired
    private CashierQueryNonIdNumberServiceImpl cashierQueryNonIdNumberServiceImpl;
    //@Autowired
    //private CashierCreateBillingCardWithAmountServiceImpl cashierCreateBillingCardWithAmountServiceImpl;
    @Autowired
    private CashierCreateBillingCardWithAmountNewServiceImpl cashierCreateBillingCardWithAmountNewServiceImpl;
    @Autowired
    private CashierLogoutServiceImpl cashierLogoutServiceImpl;
    @Autowired
    private CashierPackageTimeServiceImpl cashierPackageTimeServiceImpl;
    @Autowired
    private CashierQueryBillingCardServiceImpl cashierQueryBillingCardServiceImpl;
    @Autowired
    private CashierQueryBillingCardTypeServiceImpl cashierQueryBillingCardTypeServiceImpl;
    @Autowired
    private CashierQueryBillingOnlineServiceImpl cashierQueryBillingOnlineServiceImpl;
    @Autowired
    private CashierQueryLogOperationServiceImpl cashierQueryLogOperationServiceImpl;
    @Autowired
    private CashierQueryPackageRuleServiceImpl cashierQueryPackageRuleServiceImpl;
    @Autowired
    private CashierQueryRegcardQuantityServiceImpl cashierQueryRegcardQuantityServiceImpl;
    @Autowired
    private CashierRegcardRegisteredServiceImpl cashierRegcardRegisteredServiceImpl;
    @Autowired
    private CashierRegcardRegisteredByPhysicalServiceImpl cashierRegcardRegisteredByPhysicalServiceImpl;
    @Autowired
    private CashierQueryRegcardStatusServiceImpl cashierQueryRegcardStatusServiceImpl;
    @Autowired
    private CashierReportVersionServiceImpl cashierReportVersionServiceImpl;
    @Autowired
    private CashierResetPasswordServiceImpl cashierResetPasswordServiceImpl;
    @Autowired
    private CashierRegcardOnUnbindServiceImpl cashierRegcardOnUnbindServiceImpl;
    @Autowired
    private CashierReversalBillingCardServiceImpl cashierReversalBillingCardServiceImpl;
    @Autowired
    private CashierTopupBillingCardServiceImpl cashierTopupBillingCardServiceImpl;
    @Autowired
    private CashierTopupBillingCardNewServiceImpl cashierTopupBillingCardNewServiceImpl;
    @Autowired
    private CashierReportClientLostContactServiceImpl cashierReportClientLostContactServiceImpl;
    @Autowired
    private CashierQueryPlaceConfigServiceImpl cashierQueryPlaceConfigServiceImpl;
    @Autowired
    private CashierQueryWhetherPackageServiceImpl cashierQueryWhetherPackageServiceImpl;
    @Autowired
    private CashierVerifyIdentifierServiceImpl cashierVerifyIdentifierServiceImpl;
    @Autowired
    private CashierQueryTopupRuleServiceImpl cashierQueryTopupRuleServiceImpl;
    @Autowired
    private CashierQueryTopupRuleNewServiceImpl cashierQueryTopupRuleNewServiceImpl;
    @Autowired
    private CashierQueryActiveBillingCardServiceImpl cashierQueryActiveBillingCardServiceImpl;
    @Autowired
    private CashierCancellationBillingCardServiceImpl cashierCancellationBillingCardServiceImpl;
    @Autowired
    private CashierCancellationBillingCardNewServiceImpl cashierCancellationBillingCardNewServiceImpl;
    @Autowired
    private CashierQueryAllClientsServiceImpl cashierQueryAllClientsServiceImpl;
    @Autowired
    private CashierQueryBillingRuleServiceImpl cashierQueryBillingRuleServiceImpl;
    @Autowired
    private CashierQueryTaskServiceImpl cashierQueryTaskServiceImpl;
    @Autowired
    private CashierReportTaskServiceImpl cashierReportTaskServiceImpl;
    @Autowired
    private CashierQueryPackageTimeReserveServiceImpl cashierQueryPackageTimeReserveServiceImpl;
    @Autowired
    private CashierExchangeServiceImpl cashierExchangeServiceImpl;
    @Autowired
    private CashierQueryAuthorityServiceImpl cashierQueryAuthorityServiceImpl;
    @Autowired
    private CashierTopupBillingCardCustomizeServiceImpl cashierTopupBillingCardCustomizeServiceImpl;
    @Autowired
    private CashierModifyBillingCardTypeServiceImpl cashierModifyBillingCardTypeServiceImpl;
    @Autowired
    private CashierQueryWeekCommonBillingRuleServiceImpl cashierQueryWeekCommonBillingRuleServiceImpl;
    @Autowired
    private CashierQueryWallpaperDeliverServiceImpl cashierQueryWallpaperDeliverServiceImpl;
    @Autowired
    private CashierQueryRightWallpaperDeliverServiceImpl cashierQueryRightWallpaperDeliverServiceImpl;
    @Autowired
    private CashierQueryCashierAccountsServiceImpl cashierQueryCashierAccountsServiceImpl;
    @Autowired
    private CashierLoginServiceImpl cashierLoginServiceImpl;
    @Autowired
    private CashierQueryShiftStatisticsServiceImpl cashierQueryShiftStatisticsServiceImpl;
    @Autowired
    private CashierShiftSubmitServiceImpl cashierShiftSubmitServiceImpl;
    @Autowired
    private CashierQueryLogLoginServiceImpl cashierQueryLogLoginServiceImpl;
    @Autowired
    private CashierQueryVersionForDownloadServiceImpl cashierQueryVersionForDownloadServiceImpl;
    @Autowired
    private CashierQueryVersionServiceImpl cashierQueryVersionServiceImpl;
    @Autowired
    private CashierTopupBillingCardPresentServiceImpl cashierTopupBillingCardPresentServiceImpl;
    @Autowired
    private CashierTopupBillingCardOldWithNewServiceImpl cashierTopupBillingCardOldWithNewServiceImpl;
    @Autowired
    private CashierCustomPackageServiceImpl cashierCustomPackageServiceImpl;
    @Autowired
    private CashierReversalBillingCardNewServiceImpl cashierReversalBillingCardNewServiceImpl;
    @Autowired
    private CashierReversalBillingCardNewTopupRuleServiceImpl cashierReversalBillingCardNewTopupRuleServiceImpl;
    @Autowired
    private CashierQueryLogReversalServiceImpl cashierQueryLogReversalServiceImpl;
    @Autowired
    private CashierQueryLogTopupServiceImpl cashierQueryLogTopupServiceImpl;
    @Autowired
    private CashierQueryTopupConsumptionServiceImpl cashierQueryTopupConsumptionServiceImpl;
    @Autowired
    private CashierDeleteBillingCardServiceImpl cashierDeleteBillingCardServiceImpl;
    @Autowired
    private CashierModifyProfileConfigServiceImpl cashierModifyProfileConfigServiceImpl;
    @Autowired
    private CashierModifyBillingCardServiceImpl cashierModifyBillingCardServiceImpl;
    @Autowired
    private CashierCancelActivationServiceImpl cashierCancelActivationServiceImpl;
    @Autowired
    private CashierGenerateRealnameQRCodeServiceImpl cashierGenerateRealnameQRCodeServiceImpl;
    @Autowired
    private CashierQueryLogAccServiceImpl cashierQueryLogAccServiceImpl;
    @Autowired
    private CashierBindIdcardServiceImpl cashierBindIdcardServiceImpl;
    @Autowired
    private CashierQueryCashierServiceImpl cashierQueryCashierServiceImpl;
    @Autowired
    private CashierQueryPageShiftsServiceImpl cashierQueryPageShiftsServiceImpl;
    @Autowired
    private CashierQueryLogRefundServiceImpl cashierQueryLogRefundServiceImpl;
    @Autowired
    private CashierQueryThirdLogOperationServiceImpl cashierQueryThirdLogOperationServiceImpl;
    @Autowired
    private CashierExchangePointsServiceImpl cashierExchangePointsServiceImpl;
    @Autowired
    private CashierQueryExchangePointsRuleServiceImpl cashierQueryExchangePointsRuleServiceImpl;
    @Autowired
    private CashierQueryPageLogPointsServiceImpl cashierQueryPageLogPointsServiceImpl;
    @Autowired
    private CashierQueryLogOtherIncomeServiceImpl cashierQueryLogOtherIncomeServiceImpl;
    @Autowired
    private CashierOtherIncomeServiceImpl cashierOtherIncomeServiceImpl;
    @Autowired
    private CashierDeleteOtherIncomeServiceImpl cashierDeleteOtherIncomeServiceImpl;
    @Autowired
    private CashierQueryPageShiftLogOperationServiceImpl cashierQueryPageShiftLogOperationServiceImpl;
    @Autowired
    private CashierCheckoutLogoutServiceImpl cashierCheckoutLogoutServiceImpl;
    @Autowired
    private CashierQueryPageMemberServiceImpl cashierQueryPageMemberServiceImpl;
    @Autowired
    private CashierQueryMemberStaticsServiceImpl cashierQueryMemberStaticsServiceImpl;
    @Autowired
    private CashierQueryPageLogLoginServiceImpl cashierQueryPageLogLoginServiceImpl;
    @Autowired
    private CashierQueryPlaceChainAccountServiceImpl cashierQueryPlaceChainAccountServiceImpl;
    @Autowired
    private CashierGenerateRealnameQRCodeLocalServiceImpl cashierGenerateRealnameQRCodeLocalServiceImpl;
    //新接口
    @Autowired
    private CashierQueryPageRegLogServiceImpl cashierQueryPageRegLogServiceImpl;
    @Autowired
    private CashierTopupBillingCardNoPresentServiceImpl cashierTopupBillingCardNoPresentServiceImpl;
    @Autowired
    private CashierTransferTempWanxiangUserServiceImpl cashierTransferTempWanxiangUserServiceImpl;
    @Autowired
    private CashierQueryTempWanxiangUserServiceImpl cashierQueryTempWanxiangUserServiceImpl;
    @Autowired
    private CashierCreateBillingCardNoPresentAmountServiceImpl cashierCreateBillingCardNoPresentAmountServiceImpl;
    @Autowired
    private CashierCreateBillingCardWithCustomizeAmountServiceImpl cashierCreateBillingCardWithCustomizeAmountServiceImpl;
    @Autowired
    private CashierPackageTimeNewServiceImpl cashierPackageTimeNewServiceImpl;
    @Autowired
    private CashierContinuePackageTimeServiceImpl cashierContinuePackageTimeServiceImpl;
    @Autowired
    private CashierFuturePackageTimeServiceImpl cashierFuturePackageTimeServiceImpl;
    @Autowired
    private CashierConvertPackageTimeServiceImpl cashierConvertPackageTimeServiceImpl;
    @Autowired
    private CashierCancelPackageTimeNewServiceImpl cashierCancelPackageTimeNewServiceImpl;
    @Autowired
    private CashierQueryLoginPassServiceImpl cashierQueryLoginPassService;
    @Autowired
    private CashierCreateBillingCardWithPackageTimeServiceImpl cashierCreateBillingCardWithPackageTimeService;
    @Autowired
    private CashierQueryRegcardUsedServiceImpl cashierQueryRegcardUsedService;
    @Autowired
    private CashierQueryRegcardUnusedServiceImpl cashierQueryRegcardUnusedService;
    @Autowired
    private CashierQueryRegcardUsedStatisticsServiceImpl cashierQueryRegcardUsedStatisticsService;
    @Autowired
    private CashierQueryBookSeatsPageListServiceImpl cashierQueryBookSeatsPageListService;
    @Autowired
    private CashierCancelBookSeatsServiceImpl cashierCancelBookSeatsService;
    @Autowired
    private CashierRealnameSurchargeServiceImpl cashierRealnameSurchargeService;
    @Autowired
    private CashierQueryTempDuDuNiuUserServiceImpl cashierQueryTempDuDuNiuUserServiceImpl;
    @Autowired
    private CashierTransferTempDuDuNiuUserServiceImpl cashierTransferTempDuDuNiuUserServiceImpl;
    @Autowired
    private CashierReportPlaceStatusServiceImpl cashierReportPlaceStatusServiceImpl;
    @Autowired
    private CashierQueryMessageNotifyServiceImpl cashierQueryMessageNotifyServiceImpl;

    @Autowired
    private CashierActiveOrCreateBillingCardServiceImpl cashierActiveOrCreateBillingCardService;

    @Autowired
    private CashIerCreateTopupQrCodeImpl cashIerCreateTopupQrCodeImpl;
    @Autowired
    private CashierCreateCardQrCodeImpl cashierCreateCardQrCodeImpl;
    @Autowired
    private CashierQueryAuthImageServiceImpl cashierQueryAuthImageServiceImpl;
    @Autowired
    private CashierRegcardSendVerificationCodeImpl cashierRegcardGetVerificationCodeImpl;
    @Autowired
    private CashierRegcardForceBindImpl cashierRegcardForceBindImpl;
    @Autowired
    private CashierRegcardForceBindByByPhysicalImpl cashierRegcardForceBindByByPhysicalImpl;
    @Autowired
    private CashierReportMigrateServiceImpl cashierReportMigrateService;
    @Autowired
    private CashierQueryBillingOnlineByClientServiceImpl cashierQueryBillingOnlineByClientService;
    @Autowired
    protected CashierReportRemindServiceImpl cashierReportRemindService;
    @Autowired
    protected CashierHandleLeaveWordServiceImpl cashierHandleLeaveWordService;
    @Autowired
    protected CashierQueryGoodsPicturesServiceImpl cashierQueryGoodsPicturesService;

    // 客户端
    // ============================================================================================================
    @Autowired
    private ClientChangePasswordServiceImpl clientChangePasswordServiceImpl;
    @Autowired
    private ClientQueryCommonBillingRuleServiceImpl clientQueryCommonBillingRuleServiceImpl;
    @Autowired
    private ClientGenerateQRCodeServiceImpl clientGenerateQRCodeServiceImpl;
    @Autowired
    private ClientLoginServiceImpl clientLoginServiceImpl;
    @Autowired
    private ClientLogoutServiceImpl clientLogoutServiceImpl;
    @Autowired
    private ClientPackageTimeServiceImpl clientPackageTimeServiceImpl;
    @Autowired
    private ClientQueryBillingCardServiceImpl clientQueryBillingCardServiceImpl;
    @Autowired
    private ClientQueryBillingRuleServiceImpl clientQueryBillingRuleServiceImpl;
    @Autowired
    private ClientQueryLogLoginServiceImpl clientQueryLogLoginServiceImpl;
    @Autowired
    private ClientQueryPackageBillingRulesServiceImpl clientQueryBillingRulesNewServiceImpl;
    @Autowired
    private ClientQueryTopupListServiceImpl clientQueryTopupListServiceImpl;
    @Autowired
    private ClientQueryTopupResultServiceImpl clientQueryTopupResultServiceImpl;
    @Autowired
    private ClientQueryVersionServiceImpl clientQueryVersionServiceImpl;
    @Autowired
    private ClientQueryLogOperationServiceImpl clientQueryLogOperationServiceImpl;
    @Autowired
    private ClientQueryPlaceConfigServiceImpl clientQueryPlaceConfigServiceImpl;
    @Autowired
    private ClientQueryTopupRuleServiceImpl clientQueryTopupRuleServiceImpl;
    @Autowired
    private ClientQueryTopupRuleNewServiceImpl clientQueryTopupRuleNewServiceImpl;
    @Autowired
    private ClientRegisteredServiceImpl clientRegisteredServiceImpl;
    @Autowired
    private ClientTopupBillingCardServiceImpl clientTopupBillingCardServiceImpl;
    @Autowired
    private ClientTopupBillingCardNewServiceImpl clientTopupBillingCardNewServiceImpl;
    @Autowired
    private ClientVerifyIdentifierServiceImpl clientVerifyIdentifierServiceImpl;
    @Autowired
    private ClientHeartBeatServiceImpl clientHeartBeatServiceImpl;
    @Autowired
    private ClientBillingServiceImpl clientBillingServiceImpl;
    @Autowired
    private ClientQueryBillingOnlineServiceImpl clientQueryBillingOnlineServiceImpl;
    @Autowired
    private ClientReportStatusServiceImpl clientReportStatusServiceImpl;
    @Autowired
    private CashierPurchaseRegcardServiceImpl cashierPurchaseRegcardServiceImpl;
    @Autowired
    private CashierRegcardQueryPasswordServiceImpl cashierRegcardQueryPasswordServiceImpl;
    @Autowired
    private ClientQueryPackageTimeServiceImpl clientQueryPackageTimeServiceImpl;
    @Autowired
    private ClientQueryWeekCommonBillingRuleServiceImpl clientQueryWeekCommonBillingRuleServiceImpl;
    @Autowired
    private ClientQueryLogAccServiceImpl clientQueryLogAccServiceImpl;
    @Autowired
    private ClientExchangePointsServiceImpl clientExchangePointsServiceImpl;
    @Autowired
    private ClientQueryExchangePointsRuleServiceImpl clientQueryExchangePointsRuleServiceImpl;
    @Autowired
    private ClientQueryLogSignedServiceImpl clientQueryLogSignedServiceImpl;
    @Autowired
    private ClientSignedServiceImpl clientSignedServiceImpl;
    @Autowired
    private ClientQueryPageLogPointsServiceImpl clientQueryPageLogPointsServiceImpl;
    @Autowired
    private ClientQueryBillingRuleExtremumServiceImpl clientQueryBillingRuleExtremumServiceImpl;
    @Autowired
    private ClientPushAntiAddictionInfoImpl clientPushAntiAddictionInfoImpl;
    @Autowired
    private ClientGenerateQRCodeLocalServiceImpl clientGenerateQRCodeLocalServiceImpl;
    @Autowired
    private ClientOnlinePackageServiceImpl clientOnlinePackageServiceImpl;
    @Autowired
    private ClientQueryPackageTimeNewServiceImpl clientQueryPackageTimeNewService;
    @Autowired
    private ClientQueryBookSeatsServiceImpl clientQueryBookSeatsService;
    @Autowired
    private ClientQueryWeekCommonBillingRuleByAreaServiceImpl clientQueryWeekCommonBillingRuleByAreaService;
    @Autowired
    private ClientUnLockBookSeatServiceImpl clientUnLockBookSeatServiceImpl;
    @Autowired
    private ClientQueryEquipmentServiceImpl clientQueryEquipmentServiceImpl;
    @Autowired
    private ClientQueryLoginInfoServiceImpl clientQueryLoginInfoService;
    @Autowired
    protected ClientSubmitCallServiceImpl clientSubmitCallService;
    @Autowired
    protected ClientQueryBindMpQrcode clientQueryBindMpQrcode;
    @Autowired
    protected ClientCancelForceExchangeServiceImpl clientCancelForceExchangeService;
    @Autowired
    protected ClientCheckLoginServiceImpl clientCheckLoginService;
    @Autowired
    protected ClientPackageExchangeServiceImpl clientPackageExchangeService;
    @Autowired
    private ClientQueryAuthImageServiceImpl clientQueryAuthImageService;

    // 第三方
    // ============================================================================================================
    @Autowired
    private ThirdNonBillingClientLoginServiceImpl thirdNonBillingClientLoginServiceImpl;
    @Autowired
    private ThirdCreateBillingCardSimpleServiceImpl thirdCreateBillingCardSimpleServiceImpl;

    // getService
    // =======================================================================================================
    public CoreService getService(ServiceIndexes serviceIndex) {
        if (serviceIndex == null) {
            return null;
        }
        switch (serviceIndex) {
            // == init ==
            case ProofTime:
                return proofTimeServiceImpl;
            // ===client===
            case ClientChangePassword:
                return clientChangePasswordServiceImpl;
            case ClientQueryCommonBillingRule:
                return clientQueryCommonBillingRuleServiceImpl;
            case ClientGenerateQRCode:
                return clientGenerateQRCodeServiceImpl;
            case ClientLogin:
                return clientLoginServiceImpl;
            case ClientLogout:
                return clientLogoutServiceImpl;

            case ClientPackageTime:
                return clientPackageTimeServiceImpl;
            case ClientQueryBillingCard:
                return clientQueryBillingCardServiceImpl;
            case ClientQueryBillingRule:
                return clientQueryBillingRuleServiceImpl;
            case ClientQueryLogLogin:
                return clientQueryLogLoginServiceImpl;
            case ClientQueryBillingRulesNew:
                return clientQueryBillingRulesNewServiceImpl;
            case ClientQueryLogOperation:
                return clientQueryLogOperationServiceImpl;
            case ClientQueryPlaceConfig:
                return clientQueryPlaceConfigServiceImpl;

            case ClientQueryTopupList:
                return clientQueryTopupListServiceImpl;
            case ClientQueryTopupResult:
                return clientQueryTopupResultServiceImpl;
            case ClientQueryTopupRule:
                return clientQueryTopupRuleServiceImpl;
            case ClientQueryTopupRuleNew:
                return clientQueryTopupRuleNewServiceImpl;
            case ClientQueryVersion:
                return clientQueryVersionServiceImpl;
            case ClientRegistered:
                return clientRegisteredServiceImpl;

            case ClientTopupBillingCard:
                return clientTopupBillingCardServiceImpl;
            case ClientTopupBillingCardNew:
                return clientTopupBillingCardNewServiceImpl;
            case ClientVerifyIdentifier:
                return clientVerifyIdentifierServiceImpl;
            case ClientHeartBeat:
                return clientHeartBeatServiceImpl;
            case ClientBilling:
                return clientBillingServiceImpl;
            case ClientQueryBillingOnline:
                return clientQueryBillingOnlineServiceImpl;
            case ClientQueryBillingRuleExtremum:
                return clientQueryBillingRuleExtremumServiceImpl;

            case ClientReportStatus:
                return clientReportStatusServiceImpl;
            case ClientQueryPackageTime:
                return clientQueryPackageTimeServiceImpl;
            case ClientQueryWeekCommonBillingRule:
                return clientQueryWeekCommonBillingRuleServiceImpl;
            case ClientQueryLogAcc:
                return clientQueryLogAccServiceImpl;
            case ClientQueryExchangePointsRule:
                return clientQueryExchangePointsRuleServiceImpl;
            case ClientExchangePoints:
                return clientExchangePointsServiceImpl;
            case ClientSigned:
                return clientSignedServiceImpl;
            case ClientQueryLogSigned:
                return clientQueryLogSignedServiceImpl;
            case ClientQueryPageLogExchangePoints:
                return clientQueryPageLogPointsServiceImpl;
            case ClientPushAntiAddictionInfo:
                return clientPushAntiAddictionInfoImpl;
            case ClientGenerateQRCodeLocalServiceImpl:
                return clientGenerateQRCodeLocalServiceImpl;
            case ClientOnlinePackageServiceImpl:
                return clientOnlinePackageServiceImpl;
            case ClientQueryPackageTimeNewService:
                return clientQueryPackageTimeNewService;
            case ClientQueryBookSeatsServiceImpl:
                return clientQueryBookSeatsService;
            case ClientQueryWeekCommonBillingRuleByArea:
                return clientQueryWeekCommonBillingRuleByAreaService;
            case ClientUnLockBookSeat:
                return clientUnLockBookSeatServiceImpl;
            case ClientQueryEquipment:
                return clientQueryEquipmentServiceImpl;
            case ClientQueryLoginInfoService:
                return clientQueryLoginInfoService;
            case ClientSubmitCall:
                return clientSubmitCallService;
            case ClientQueryBindMpQrcode:
                return clientQueryBindMpQrcode;
            case ClientCancelForceExchange:
                return clientCancelForceExchangeService;
            case ClientCheckLogin:
                return clientCheckLoginService;
            case ClientPackageExchange:
                return clientPackageExchangeService;
            case ClientQueryAuthImage:
                return clientQueryAuthImageService;

            // === third ===
            case ThirdNonBillingClientLogin:
                return thirdNonBillingClientLoginServiceImpl;
            case ThirdCreateBillingCardSimple:
                return thirdCreateBillingCardSimpleServiceImpl;
            // ===cashier===
            case CashierActivation:
                return cashierActivationServiceImpl;
            case CashierCancelPackageTime:
                return cashierCancelPackageTimeServiceImpl;
            case CashierQueryNonIdNumber:
                return cashierQueryNonIdNumberServiceImpl;
            case CashierCreateBillingCardWithAmountNew:
                return cashierCreateBillingCardWithAmountNewServiceImpl;
            case CashierLogout:
                return cashierLogoutServiceImpl;
            case CashierPackageTime:
                return cashierPackageTimeServiceImpl;

            case CashierRegcardRegisteredByPhysical:
                return cashierRegcardRegisteredByPhysicalServiceImpl;
            case CashierQueryBillingCard:
                return cashierQueryBillingCardServiceImpl;
            case CashierQueryBillingCardType:
                return cashierQueryBillingCardTypeServiceImpl;
            case CashierQueryBillingOnline:
                return cashierQueryBillingOnlineServiceImpl;
            case CashierQueryLogOperation:
                return cashierQueryLogOperationServiceImpl;

            case CashierQueryPackageRule:
                return cashierQueryPackageRuleServiceImpl;
            case CashierQueryRegcardQuantity:
                return cashierQueryRegcardQuantityServiceImpl;
            case CashierRegcardRegistered:
                return cashierRegcardRegisteredServiceImpl;
            case CashierReportVersion:
                return cashierReportVersionServiceImpl;

            case CashierResetPassword:
                return cashierResetPasswordServiceImpl;
            case CashierReversalBillingCard:
                return cashierReversalBillingCardServiceImpl;
            case CashierTopupBillingCard:
                return cashierTopupBillingCardServiceImpl;
            case CashierTopupBillingCardNew:
                return cashierTopupBillingCardNewServiceImpl;
            case CashierReportClientLostContact:
                return cashierReportClientLostContactServiceImpl;
            case CashierQueryPlaceConfig:
                return cashierQueryPlaceConfigServiceImpl;

            case CashierQueryWhetherPackage:
                return cashierQueryWhetherPackageServiceImpl;
            case CashierVerifyIdentifier:
                return cashierVerifyIdentifierServiceImpl;
            case CashierQueryTopupRule:
                return cashierQueryTopupRuleServiceImpl;
            case CashierQueryTopupRuleNew:
                return cashierQueryTopupRuleNewServiceImpl;
            case CashierQueryActiveBillingCard:
                return cashierQueryActiveBillingCardServiceImpl;

            case CashierQueryRegcardStatus:
                return cashierQueryRegcardStatusServiceImpl;
            case CashierCancellationBillingCard:
                return cashierCancellationBillingCardServiceImpl;
            case CashierCancellationBillingCardNew:
                return cashierCancellationBillingCardNewServiceImpl;
            case CashierQueryAllClients:
                return cashierQueryAllClientsServiceImpl;
            case CashierPurchaseRegcard:
                return cashierPurchaseRegcardServiceImpl;
            case CashierQueryBillingRule:
                return cashierQueryBillingRuleServiceImpl;

            case CashierRegcardOnUnbind:
                return cashierRegcardOnUnbindServiceImpl;
            case CashierRegcardQueryPassword:
                return cashierRegcardQueryPasswordServiceImpl;
            case CashierQueryTask:
                return cashierQueryTaskServiceImpl;
            case CashierReportTask:
                return cashierReportTaskServiceImpl;
            case CashierQueryPackageTimeReserve:
                return cashierQueryPackageTimeReserveServiceImpl;

            case CashierExchange:
                return cashierExchangeServiceImpl;
            case CashierQueryAuthority:
                return cashierQueryAuthorityServiceImpl;
            case CashierTopupBillingCardCustomize:
                return cashierTopupBillingCardCustomizeServiceImpl;
            case CashierModifyBillingCardType:
                return cashierModifyBillingCardTypeServiceImpl;
            case CashierQueryWallpaperDeliver:
                return cashierQueryWallpaperDeliverServiceImpl;
            case CashierQueryRightWallpaperDeliverServiceImpl:
                return cashierQueryRightWallpaperDeliverServiceImpl;

            case CashierQueryWeekCommonBillingRule:
                return cashierQueryWeekCommonBillingRuleServiceImpl;
            case CashierQueryCashierAccounts:
                return cashierQueryCashierAccountsServiceImpl;
            case CashierLogin:
                return cashierLoginServiceImpl;
            case CashierQueryShiftStatistics:
                return cashierQueryShiftStatisticsServiceImpl;
            case CashierShiftSubmit:
                return cashierShiftSubmitServiceImpl;

            case CashierQueryLogLogin:
                return cashierQueryLogLoginServiceImpl;
            case CashierTopupBillingCardPresent:
                return cashierTopupBillingCardPresentServiceImpl;
            case CashierTopupBillingCardOldWithNew:
                return cashierTopupBillingCardOldWithNewServiceImpl;
            case CashierCustomPackage:
                return cashierCustomPackageServiceImpl;
            case CashierReversalBillingCardNew:
                return cashierReversalBillingCardNewServiceImpl;
            case CashierReversalBillingCardNewTopupRule:
                return cashierReversalBillingCardNewTopupRuleServiceImpl;

            case CashierQueryLogTopup:
                return cashierQueryLogTopupServiceImpl;
            case CashierQueryLogReversal:
                return cashierQueryLogReversalServiceImpl;
            case CashierQueryVersionForDownload:
                return cashierQueryVersionForDownloadServiceImpl;
            case CashierQueryVersion:
                return cashierQueryVersionServiceImpl;
            case CashierQueryTopupConsumption:
                return cashierQueryTopupConsumptionServiceImpl;
            case CashierDeleteBillingCard:
                return cashierDeleteBillingCardServiceImpl;

            case CashierModifyProfileConfig:
                return cashierModifyProfileConfigServiceImpl;
            case CashierModifyBillingCard:
                return cashierModifyBillingCardServiceImpl;
            case CashierCancelActivation:
                return cashierCancelActivationServiceImpl;
            case CashierGenerateRealnameQRCode:
                return cashierGenerateRealnameQRCodeServiceImpl;
            case CashierQueryLogAcc:
                return cashierQueryLogAccServiceImpl;
            case CashierBindIdcard:
                return cashierBindIdcardServiceImpl;
            case CashierQueryCashier:
                return cashierQueryCashierServiceImpl;
            case CashierQueryPageShifts:
                return cashierQueryPageShiftsServiceImpl;
            case CashierQueryLogRefund:
                return cashierQueryLogRefundServiceImpl;
            case CashierQueryThirdLogOperation:
                return cashierQueryThirdLogOperationServiceImpl;
            case CashierQueryExchangePointsRule:
                return cashierQueryExchangePointsRuleServiceImpl;
            case CashierExchangePints:
                return cashierExchangePointsServiceImpl;
            case CashierQueryPageLogExchangePoints:
                return cashierQueryPageLogPointsServiceImpl;
            case CashierQueryLogOtherIncome:
                return cashierQueryLogOtherIncomeServiceImpl;
            case CashierOtherIncome:
                return cashierOtherIncomeServiceImpl;
            case CashierDeleteOtherIncome:
                return cashierDeleteOtherIncomeServiceImpl;
            case CashierQueryPageShiftLogOperation:
                return cashierQueryPageShiftLogOperationServiceImpl;
            case CashierCheckoutLogout:
                return cashierCheckoutLogoutServiceImpl;
            case CashierQueryPageMember:
                return cashierQueryPageMemberServiceImpl;
            case CashierQueryMemberStatics:
                return cashierQueryMemberStaticsServiceImpl;
            case CashierQueryPageLogLogin:
                return cashierQueryPageLogLoginServiceImpl;
            case CashierQueryPlaceChainAccount:
                return cashierQueryPlaceChainAccountServiceImpl;
            case CashierGenerateRealnameQRCodeLocal:
                return cashierGenerateRealnameQRCodeLocalServiceImpl;
            case CashierQueryPageRegLog:
                return cashierQueryPageRegLogServiceImpl;
            case CashierTopupBillingCardNoPresent:
                return cashierTopupBillingCardNoPresentServiceImpl;
            case CashierTransferTempWanxiangUser:
                return cashierTransferTempWanxiangUserServiceImpl;
            case CashierQueryTempWanxiangUser:
                return cashierQueryTempWanxiangUserServiceImpl;
            case CashierCreateBillingCardNoPresent:
                return cashierCreateBillingCardNoPresentAmountServiceImpl;
            case CashierCreateBillingCardWithCustomizeAmount:
                return cashierCreateBillingCardWithCustomizeAmountServiceImpl;
            case CashierPackageTimeNew:
                return cashierPackageTimeNewServiceImpl;
            case CashierContinuePackageTime:
                return cashierContinuePackageTimeServiceImpl;
            case CashierFuturePackageTime:
                return cashierFuturePackageTimeServiceImpl;
            case CashierConvertPackageTime:
                return cashierConvertPackageTimeServiceImpl;
            case CashierCancelPackageTimeNew:
                return cashierCancelPackageTimeNewServiceImpl;
            case CashierQueryLoginPass:
                return cashierQueryLoginPassService;
            case CashierCreateBillingCardWithPackageTime:
                return cashierCreateBillingCardWithPackageTimeService;
            case CashierQueryRegcardUsed:
                return cashierQueryRegcardUsedService;
            case CashierQueryRegcardUnused:
                return cashierQueryRegcardUnusedService;
            case CashierQueryRegcardUsedStatistics:
                return cashierQueryRegcardUsedStatisticsService;
            case CashierQueryBookSeatsPageListServiceImpl:
                return cashierQueryBookSeatsPageListService;
            case CashierCancelBookSeatsServiceImpl:
                return cashierCancelBookSeatsService;
            case CashierRealnameSurcharge:
                return cashierRealnameSurchargeService;
            case CashIerCreateTopupQrCode:
                return cashIerCreateTopupQrCodeImpl;
            case CashierCreateCardQrCode:
                return cashierCreateCardQrCodeImpl;
            case CashierQueryTempDuDuNiuUser:
                return cashierQueryTempDuDuNiuUserServiceImpl;
            case CashierTransferTempDuDuNiuUser:
                return cashierTransferTempDuDuNiuUserServiceImpl;
            case CashierReportPlaceStatus:
                return cashierReportPlaceStatusServiceImpl;
            case CashierQueryAuthImage:
                return cashierQueryAuthImageServiceImpl;
            case CashierRegcardSendVerificationCode:
                return cashierRegcardGetVerificationCodeImpl;
            case CashierRegcardForceBind:
                return cashierRegcardForceBindImpl;
            case CashierRegcardForceBindByByPhysical:
                return cashierRegcardForceBindByByPhysicalImpl;
            case CashierReportMigrateServiceImpl:
                return cashierReportMigrateService;
            case CashierQueryMessageNotifyService:
                return cashierQueryMessageNotifyServiceImpl;
            case CashierQueryBillingOnlineByClient:
                return cashierQueryBillingOnlineByClientService;
            case CashierActiveOrCreateBillingCardService:
                return cashierActiveOrCreateBillingCardService;
            case CashierReportRemind:
                return cashierReportRemindService;
            case CashierQueryGoodsPicturesService:
                return cashierQueryGoodsPicturesService;
            case CashierHandleLeaveWord:
                return cashierHandleLeaveWordService;
            default:
                return null;
        }
    }
}