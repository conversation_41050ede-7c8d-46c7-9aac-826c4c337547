package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogQrCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年10月11日 上午11:55:20
 */
public interface LogQrCodeRepository extends JpaRepository<LogQrCode, Long> {

	Optional<LogQrCode> findByToken(String token);

	Optional<LogQrCode> findByTokenAndUsedAndDeadlineGreaterThanEqual(String token, int used, LocalDateTime now);

	//新增一个接口，二维码如果被使用不提示二维码信息未找到
	Optional<LogQrCode> findByTokenAndDeadlineGreaterThanEqual(String token,  LocalDateTime now);

	Optional<LogQrCode> findFirstByPlaceIdAndClientIdAndAreaIdOrderByIdDesc(String placeId, String clientId,
			String areaId);

	Optional<LogQrCode> findFirstByPlaceIdAndClientIdAndAreaIdAndDeadlineOrderByCreatedDesc(String placeId, String clientId,
																					   String areaId, LocalDateTime deadline);
	Optional<LogQrCode> findFirstByPlaceIdAndCashierIdAndDeadlineOrderByIdDesc(String placeId, String cashierId, LocalDateTime deadline);

	Optional<LogQrCode> findFirstByPlaceIdAndClientIdAndAreaIdAndDeadlineNotOrderByIdDesc(String placeId, String cashierId, String areaId, LocalDateTime deadline);

	@Modifying
	@Transactional
	@Query(value = "UPDATE log_qrcode SET deadline = NOW() WHERE place_id = :placeId AND deadline > NOW() AND IF(1= :type,cashier_id IS NOT NULL,client_id IS NOT NULL)", nativeQuery = true)
	int clearHistoryQrcode(@Param("placeId") String placeId , @Param("type") int type);

}
