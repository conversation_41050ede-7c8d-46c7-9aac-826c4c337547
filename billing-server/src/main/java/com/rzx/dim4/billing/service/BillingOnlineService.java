package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.service.feign.billing.param.BillingOnlineParam;
import com.rzx.dim4.billing.entity.BillingOnline;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/16
 **/
public interface BillingOnlineService {

    void updateOnlineNextTime(Long id, Long timestamp, int onceDeduction, int onceDeductionCash, int onceDeductionPresent);

    Boolean haveOnline(String placeId);

    int countByPlaceIdAndCardTypeIdAndFinished(String placeId, String cardTypeId, int finish);

    int countByPlaceIdAndCardTypeIdNotAndFinished(String placeId, String cardTypeId, int finish);

    BillingOnline save(BillingOnline billingOnline);

    Optional<BillingOnline> findUnfinishedByIdNumber(String idNumber);

    /**
     * 查询当前证件号最后一次上机的网吧
     *
     * @param idNumber
     * @return
     */
    Optional<BillingOnline> findTop1ByIdNumberOrderByIdDesc(String idNumber);

    Optional<BillingOnline> findByPlaceIdAndClientId(String placeId, String clientId);

    Optional<BillingOnline> findTop1ByPlaceIdAndClientId(String placeId, String clientId);

    Optional<BillingOnline> findByPlaceIdAndIdNumber(String placeId, String idNumber);

    Optional<BillingOnline> findUnfinishedByPlaceIdAndCardId(String placeId, String cardId);

    Optional<BillingOnline> findUnfinishedByPlaceIdAndClientId(String placeId, String clientId);

    Optional<BillingOnline> findTop1ByPlaceIdAndCardIdAndLoginId(String placeId, String cardId, String loginId);

    Optional<BillingOnline> findUnfinishedByPlaceIdAndIdNumber(String placeId, String idNumber);

    Optional<BillingOnline> findOnlineByPlaceIdAndCardIdAndLoginId(String placeId, String cardId, String loginId);

    Optional<BillingOnline> findTop1FinishedByPlaceIdsAndIdNumber(List<String> placeId, String idNumber, int finish);

    List<BillingOnline> findUnfinishedByPlaceId(String placeId);

    /**
     * 查询多个场所的在线信息
     *
     * @param placeIds
     * @return
     */
    List<BillingOnline> findByPlaceIdInAndFinished(List<String> placeIds);

    List<BillingOnline> findByList(BillingOnlineParam billingOnlineParam);

    List<BillingOnline> findByPlaceIdAndLoginId(String placeId, String loginId);

    List<BillingOnline> findTop5ByPlaceIdAndIdNumber(String placeId, String idNumber);

    List<BillingOnline> findFinishedByPlaceIdAndCardIds(String placeId, List<String> cardIds);

    List<BillingOnline> findUnfinishedByPlaceIdAndCardIds(String placeId, List<String> cardIds);

    List<BillingOnline> findUnfinishedByPlaceIdAndClientIds(String placeId, List<String> clientIds);

    List<BillingOnline> findFinishedByPlaceIdsAndIdNumber(List<String> placeId, String idNumber, int finish);

    List<BillingOnline> findByPlaceIdInAndIdNumberInAndFinished(List<String> placeIds, List<String> idNumbers);

    Page<BillingOnline> findAll(Map<String, Object> map, Pageable pageable);

    Page<BillingOnline> findByPage(BillingOnlineParam billingOnlineParam, Pageable pageable);

    void deleteByPlaceIdAndLoginId(String placeId, String loginId);

    Integer findSumDeductionByLoginId(String loginId);

    int updateOnlineCardTypeId(String cardTypeId,String placeId,String cardId);

    // 统计网吧当前在线人数
    int countByPlaceIdAndFinished(String placeId, int finish);

    int updateOnlineInviteStatus(int isInvite,String placeId, String loginId);

    //获取在线信息
    Optional<BillingOnline> findUnfinishedByPlaceIdAndClientIdAndFinished(String placeId, String clientId);

    List<BillingOnline> findAll(Map<String, Object> map);

}
