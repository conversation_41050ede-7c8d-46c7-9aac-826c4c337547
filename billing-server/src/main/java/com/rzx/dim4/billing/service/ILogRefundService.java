package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogShift;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/1/31
 **/
public interface ILogRefundService {

    /**
     * 临时卡在线退款操作
     * @param billingCard 计费卡信息
     * @param refundAmount 待退款金额
     * @param sourceType 来源
     * @param logShift 对应收银员信息
     */
    void addRefundTask(BillingCard billingCard, int refundAmount, SourceType sourceType, LogShift logShift);

    void addPackageTimeReserveRefundTask(BillingCard billingCard, int refundAmount, SourceType sourceType, LogShift logShift ,String ruleId);

}
