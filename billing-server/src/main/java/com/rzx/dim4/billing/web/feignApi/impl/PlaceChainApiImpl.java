package com.rzx.dim4.billing.web.feignApi.impl;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.admin.JoinPlaceToChainBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.billing.BillingPlaceChainApi;
import com.rzx.dim4.billing.service.PlaceChainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/4/16
 **/
@Slf4j
@RestController
public class PlaceChainApiImpl extends BillingBaseApi implements BillingPlaceChainApi {

    @Autowired
    private PlaceChainService placeChainService;

    @Override
    @PostMapping(URL + "/placeJoinChain")
    public GenericResponse<?> placeJoinChain(@RequestHeader(value = "request_ticket") String requestTicket,
                                             @RequestBody JoinPlaceToChainBO joinPlaceToChainBO) {
        checkoutRequestTicket(requestTicket);

        log.info("placeJoinChain 接口入参: {}", new Gson().toJson(joinPlaceToChainBO));
//        placeChainService.placeJoinChain(joinPlaceToChainBO);

        try {
            placeChainService.checkParamsBeforeJoinChain(joinPlaceToChainBO);
        } catch (ServiceException e) {
            log.info("placeJoinChain 接口异常, checkParamsBeforeJoinChain: {}", e.getDetailedMsg());
            return new GenericResponse<>(e.getErrorCode(), e.getDetailedMsg());
        }

        placeChainService.doJoinChain(joinPlaceToChainBO);

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }
}
