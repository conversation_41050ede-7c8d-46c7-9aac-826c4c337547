package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.RegcardServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogShiftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 收银台注册卡(实体卡)注册接口
 */
@Service
@Slf4j
public class CashierRegcardRegisteredByPhysicalServiceImpl implements CoreService {

    @Autowired
    RegcardServerService regcardServerService;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    LogShiftService logShiftService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() != 5) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0);
        String shiftId = params.get(1); // 班次ID
        String idNumber = params.get(2).toUpperCase();
        String mobile = params.get(3);
        String cardNumber = params.get(4);

        // 验证身份证合法性
        Boolean flag = IdNumberValidator.verificate(idNumber);
        if (!flag) {
            return new GenericResponse<>(ServiceCodes.ID_NUMBER_ERROR);
        }

        // 查询班次信息
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }

        // 获取场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> respConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (respConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.BILLING_PLACE_CONFIG_NOT_FOUND);
        }
        PlaceConfigBO placeConfigBO = respConfig.getData().getObj();
        if (placeConfigBO.getRegcard() == 0) {
            return new GenericResponse<>(ServiceCodes.REGCARD_DISABLED);
        }

        // 获取场所信息
        GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
        if (respProfile.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }
        PlaceProfileBO placeProfileBO = respProfile.getData().getObj();
        if (StringUtils.isEmpty(placeProfileBO.getAuditId())) {
            return new GenericResponse<>(ServiceCodes.REGCARD_CONFIG_ERROR);
        }

        String authId = placeProfileBO.getAuditId();
        String areaId = placeProfileBO.getAreaId();
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<?> respOnActive = regcardServerService.OnRegistByPhysicalCard(requestTicket, placeId, authId, areaId, idNumber, mobile, cardNumber);
        log.info("注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());

        return respOnActive;
    }
}
