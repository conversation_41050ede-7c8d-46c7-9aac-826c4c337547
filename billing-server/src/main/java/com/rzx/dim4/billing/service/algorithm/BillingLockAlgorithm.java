package com.rzx.dim4.billing.service.algorithm;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.rzx.dim4.billing.cons.BillingConstants.*;

/**
 * 并发锁公共方法
 */
@Service
@Slf4j
public class BillingLockAlgorithm {

    @Autowired
    RedissonClient redissonClient;

    /**
     * 扣费加锁
     * @param placeId
     * @param clientId
     * @return
     */
    public String acquireLock (String placeId, String clientId) {

        // 扣费并发锁的key
        String billingKey = BILLING_SIMULTANEOUSLY_LOCK + "_" + placeId + "_" + clientId;

        // 获取锁对象
        RLock rLock = redissonClient.getLock(billingKey);

        /**
         * 尝试获取锁
         * waitTimeout 尝试获取锁的最大等待时间，超过这个值，则认为获取锁失败
         * leaseTime   锁的持有时间,超过这个时间锁会自动失效（值应设置为大于业务处理的时间，确保在锁有效期内业务能处理完）
         */
        try {
            boolean res = rLock.tryLock(20, 4000, TimeUnit.MILLISECONDS);
            if (res) {
                log.info("redission获取锁成功::::::::::::::::::{}" , rLock.getName());
                return billingKey;
            }
            return null;
        } catch (Exception e) {
            log.info("获取锁失败.......{}_{}" , placeId , clientId);
            return null;
        }
    }

    /**
     * 扣费业务根据cardId加锁
     * @param placeId
     * @param cardId
     * @return
     */
    public String cardIdAcquireLock (String placeId, String cardId) {

        // 扣费并发锁的key
        String cardIdKey = BILLING_CARD_ID_SIMULTANEOUSLY_LOCK + "_" + placeId + "_" + cardId;

        // 获取锁对象
        RLock rLock = redissonClient.getLock(cardIdKey);

        /**
         * 尝试获取锁
         * waitTimeout 尝试获取锁的最大等待时间，超过这个值，则认为获取锁失败
         * leaseTime   锁的持有时间,超过这个时间锁会自动失效（值应设置为大于业务处理的时间，确保在锁有效期内业务能处理完）
         */
        try {
            boolean res = rLock.tryLock(20, 4000, TimeUnit.MILLISECONDS);
            if (res) {
                log.info("redission获取锁成功:::{}" , rLock.getName());
                return cardIdKey;
            }
            return null;
        } catch (Exception e) {
            log.info("获取锁失败.......{}_{}" , placeId , cardIdKey);
            return null;
        }
    }

    /**
     * 请客上网扣费业务根据cardId加锁
     * @param placeId
     * @param cardId
     * @return
     */
    public String inviteCardIdAcquireLock (String placeId, String cardId) {

        // 扣费并发锁的key
        String cardIdKey = BILLING_CARD_ID_SIMULTANEOUSLY_LOCK + "_" + placeId + "_" + cardId;

        // 获取锁对象
        RLock rLock = redissonClient.getLock(cardIdKey);

        /**
         * 尝试获取锁
         * waitTimeout 尝试获取锁的最大等待时间，超过这个值，则认为获取锁失败
         * leaseTime   锁的持有时间,超过这个时间锁会自动失效（值应设置为大于业务处理的时间，确保在锁有效期内业务能处理完）
         */
        try {
            boolean res = rLock.tryLock(20, 1000, TimeUnit.MILLISECONDS);
            if (res) {
                log.info("redission获取锁成功:::{}" , rLock.getName());
                return cardIdKey;
            }
            return null;
        } catch (Exception e) {
            log.info("获取锁失败.......{}_{}" , placeId , cardIdKey);
            return null;
        }
    }

    /**
     * 包间区域加锁
     * @param placeId
     * @param areaId
     * @return
     */
    public String areaAcquireLock (String placeId, String areaId) {

        // 扣费并发锁的key
        String areaKey = BILLING_AREA_SIMULTANEOUSLY_LOCK + "_" + placeId + "_" + areaId;

        // 获取锁对象
        RLock rLock = redissonClient.getLock(areaKey);

        /**
         * 尝试获取锁
         * waitTimeout 尝试获取锁的最大等待时间，超过这个值，则认为获取锁失败
         * leaseTime   锁的持有时间,超过这个时间锁会自动失效（值应设置为大于业务处理的时间，确保在锁有效期内业务能处理完）
         */
        try {
            boolean res = rLock.tryLock(20, 4000, TimeUnit.MILLISECONDS);
            if (res) {
                log.info("redission获取锁成功{}" , rLock.getName());
                return areaKey;
            }
            return null;
        } catch (Exception e) {
            log.info("获取锁失败.......{}_{}" , placeId , areaId);
            return null;
        }
    }

    /**
     * 锁释放
     * @param billingKey
     */
    public void releaseLock (String billingKey) {
        if (billingKey != null) {
            RLock rLock = redissonClient.getLock(billingKey);
            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                //释放锁（解锁）
                log.info("redission释放锁成功:::::::::{}" , rLock.getName());
                rLock.unlock();
            }
        }
    }

}
