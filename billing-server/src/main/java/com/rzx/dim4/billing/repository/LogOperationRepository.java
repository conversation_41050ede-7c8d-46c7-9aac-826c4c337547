package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.LogOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface LogOperationRepository extends JpaRepository<LogOperation, Long> , JpaSpecificationExecutor<LogOperation> {

	List<LogOperation> findByLoginIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String loginId, OperationType operationType, LocalDateTime startTime, LocalDateTime endTime);

	Page<LogOperation> findAll(Specification<LogOperation> consumingRecordsSpecification, Pageable pageable);

	Optional<LogOperation> findTop1ByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, OperationType operationType, LocalDateTime startTime, LocalDateTime endTime);

	List<LogOperation> findByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, OperationType operationType, LocalDateTime startTime, LocalDateTime endTime);

	Optional<LogOperation> findTop1ByPlaceIdAndIdNumberAndOperationTypeInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String idNumber, List<OperationType> operationTypes, LocalDateTime startTime, LocalDateTime endTime);

	Optional<LogOperation> findTop1ByPlaceIdAndCardIdAndOperationTypeInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, List<OperationType> operationTypes, LocalDateTime startTime, LocalDateTime endTime);

	@Query(value = "select count(1) from log_operation where place_id = ?1 and created >= ?2 and created <= ?3 and operation_type in ('CREATE_CARD','ACTIVATE_CARD') and length(id_number) <> 18", nativeQuery = true)
	Integer countActivatedNonIdNumberByPlaceId(String placeId, LocalDateTime todayStart,LocalDateTime todayEnd);


	/**
	 * 按班次-赠送收入
	 *
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select sum(lo.present) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType='PRESENT' and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostCashIncomeOfPresentByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-卡类型-附加费总次数
	 *
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select count(lo.id) from log_operation lo where lo.place_id=?1 and IF(?3 !='1000',lo.card_type_id !='1000',lo.card_type_id ='1000') and lo.operation_type='SURCHARGE' and lo.shift_id = ?2 and lo.created >= ?4 and lo.created <= ?5", nativeQuery = true)
	Integer countSurchargeByShiftIdAndCardTypeId(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-卡类型-附加费总金额
	 *
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select sum(lo.cost + lo.present) from log_operation lo where lo.place_id=?1 and IF(?3 !='1000',lo.card_type_id !='1000',lo.card_type_id ='1000') and lo.operation_type='SURCHARGE' and lo.shift_id = ?2 and lo.created >= ?4 and lo.created <= ?5", nativeQuery = true)
	Integer sumSurchargeByShiftIdAndCardTypeId(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-冲正
	 * 
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType='REVERSAL' and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostTotalReversalByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-新开会员卡-数量
	 * 
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select count(lo.id) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType='CREATE_CARD' and lo.cardTypeId<>'1000' and lo.created >= ?3 and lo.created <= ?4")
	Integer countCreateMemberCardByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-新开临时卡-数量
	 * 
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select count(lo.id) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType='CREATE_CARD' and lo.cardTypeId='1000' and lo.created >= ?3 and lo.created <= ?4")
	Integer countCreateTemporaryCardByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-会员卡-消费金额
	 * 
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType ='LOGOUT' and lo.cardTypeId<>'1000' and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostMemberCardConsumptionByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次-临时卡-消费金额
	 * 
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.shiftId=?2 and lo.operationType ='LOGOUT' and lo.cardTypeId='1000' and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostTemporaryCardConsumptionByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-新开临时卡-数量
	 * 
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select count(lo.id) from LogOperation lo where lo.placeId in (?1) and lo.operationType='CREATE_CARD' and lo.cardTypeId='1000' and lo.created >= ?2 and lo.created <= ?3")
	Integer countCreateTemporaryCardByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-新开会员卡-数量
	 * 
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select count(lo.id) from LogOperation lo where lo.placeId in (?1) and lo.operationType='CREATE_CARD' and lo.cardTypeId<>'1000' and lo.created >= ?2 and lo.created <= ?3")
	Integer countCreateMemberCardByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);



	/**
	 * 按时间-总冲正
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId in (?1) and lo.operationType='REVERSAL' and lo.sourceType='CASHIER' and lo.created >= ?2 and lo.created <= ?3")
	Integer sumCostTotalReversalByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-附加费
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost+lo.present) from LogOperation lo where lo.placeId=?1 and lo.operationType='SURCHARGE' and lo.cardId = ?2 and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostSurchargeByDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-商品总购买
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.operationType='BUY' and lo.cardId = ?2 and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostShopCashByDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-商品总退款
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.operationType='REFUND' and lo.cardId = ?2 and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostShopRefundByDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);


	/**
	 * 按时间-冲正-数据列表
	 *
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select   `id`,\n" +
			"  `created`,\n" +
			"  `creater`,\n" +
			"  `creater_name`,\n" +
			"  `deleted`,\n" +
			"  `updated`,\n" +
			"  `card_id`,\n" +
			"  `card_type_id`,\n" +
			"  `card_type_name`,\n" +
			"  `cash_balance`,\n" +
			"  `client_id`,\n" +
			"  `cost`,\n" +
			"  `details`,\n" +
			"  `id_name`,\n" +
			"  `id_number`,\n" +
			"  `login_id`,\n" +
			"  `last_client_id`,\n" +
			"  `operation_type`,\n" +
			"  `present`,\n" +
			"  `place_id`,\n" +
			"  `present_balance`,\n" +
			"  `remark`,\n" +
			"  `shift_id`,\n" +
			"  `source_type`,\n" +
			"  `cashier_id`,\n" +
			"  `online_account` from log_operation lo where lo.place_id= ?1 and lo.operation_type='REVERSAL' and lo.created >= ?2 and lo.created <= ?3 order by lo.created desc;", nativeQuery = true)
	List<LogOperation> findLogOperationByReversalAndDateTime(String placeId, LocalDateTime startDateTime,
			LocalDateTime endDateTime);

	/**
	 * 按时间-卡-总冲正
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost) from LogOperation lo where lo.placeId=?1 and lo.operationType='REVERSAL' and lo.sourceType='CASHIER' and lo.cardId = ?2 and lo.created >= ?3 and lo.created <= ?4")
	Integer sumCostTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-总冲正(本金+奖励)
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query("select sum(lo.cost + lo.present) from LogOperation lo where lo.placeId=?1 and lo.operationType='REVERSAL' and lo.sourceType='CASHIER' and lo.cardId = ?2 and lo.created >= ?3 and lo.created <= ?4")
	Integer sumTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);


	/**
	 * 按卡Id分组统计充值 赠送
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select a.card_id as cardId, sum(cost) as costs, sum(present) as presents from log_operation a  where a.place_id = ?1 and a.created > ?2 and a.created < ?3 and card_id in (?4) and operation_type in ('TOPUP', 'PRESENT', 'CREATE_CARD') and a.card_type_id not in('1000','1002') and cost > 0 group by a.card_id ", nativeQuery = true)
	List<Map<String, String>> sumTopupByPlaceIdGroupByCardId(String placeId, LocalDateTime startDateTime,LocalDateTime endDateTime,List<String> cardIds, Pageable pageable);


	/**
	 * 查询有充值记录的会员卡号
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select a.card_id as cardId from log_operation a  where a.place_id = ?1 and a.created > ?2 and a.created < ?3 and operation_type in ('TOPUP', 'PRESENT', 'CREATE_CARD') and a.card_type_id not in('1000','1002') group by a.card_id ", nativeQuery = true)
	List<String> findCardIdsTopupHistoryByPlaceId(String placeId, LocalDateTime startDateTime,LocalDateTime endDateTime);

	/**
	 * 按卡Id分组统计充值次数
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "SELECT a.card_id AS cardId, COUNT(a.id) AS counts FROM log_operation a  WHERE  a.place_id = ?1 AND a.created > ?2 AND a.created < ?3 AND a.card_id IN (?4) AND (a.operation_type='TOPUP'  OR (a.operation_type='CREATE_CARD' AND a.cost > 0)) AND a.card_type_id NOT IN('1000','1002')  GROUP BY a.card_id ", nativeQuery = true)
	List<Map<String, String>> queryTopupCountByPlaceIdAndCardIdIn( String placeId, LocalDateTime startDateTime,LocalDateTime endDateTime,List<String> cardIds);
	
	@Query(value = "select count(DISTINCT(a.card_id)) from log_operation a where  a.place_id = ?1 and a.created > ?2 and a.created < ?3 and card_id in (?4) and operation_type in ('TOPUP', 'PRESENT' ,'CREATE_CARD') and a.card_type_id not in('1000','1002') ", nativeQuery = true)
	Long countTopupByPlaceIdGroupByCardId(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime,List<String> cardIds);




	@Query(value = "SELECT SUM(cost) AS sumCost,\n" + "SUM(present) AS sumPresent\n" + "FROM\n" + "log_operation\n"
			+ "WHERE\n" + "place_id = :placeId \n"
			+ "AND created >= :startDate AND created <= :endDate \n"
			+ "AND if(:idNumber = '' OR :idNumber is null ,1=1, id_number = :idNumber) \n"
			+ "AND if(:cardTypeId = '' OR :cardTypeId is null ,1=1, card_type_id = :cardTypeId) \n"
			+ "AND if(:createrName = '' OR :createrName is null ,1=1, creater_name like :createrName) \n"
			+ "AND if(:idName = '' OR :idName is null ,1=1, id_name like :idName) \n"
			+ "AND (coalesce(:sourceTypes,null) is null or source_type in (:sourceTypes)) \n"
			+ "AND (coalesce(:operationTypeArr,null) is null or operation_type in (:operationTypeArr)) \n", nativeQuery = true)
	Map<String, String> querySumCostAndPresent(@Param("placeId") String placeId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("idNumber") String idNumber,
											   @Param("cardTypeId") String cardTypeId, @Param("createrName") String createrName,@Param("idName") String idName,@Param("sourceTypes") List<String> sourceTypes,
											   @Param("operationTypeArr") List<String> operationTypeArr);

	/**
	 * 查询历史上机记录门店
	 */
	@Query(value = "SELECT place_id as placeId,count(place_id)  FROM log_operation WHERE  id_number = ?1  AND operation_type = 'LOGIN'  and created >= ?2 and created <= ?3 GROUP BY place_id ORDER BY count(place_id)  DESC limit 99", nativeQuery = true)
	List<Map<String, String>>  findHistoryPlacePage(String idNumber, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 查询班次的开卡数据
	 * */
	List<LogOperation> findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndOperationType(String placeId,LocalDateTime startTime, LocalDateTime endTime, OperationType operationType);

	Optional<LogOperation> findTop1ByPlaceIdAndIdNumberAndDeletedAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId,String idNumber,int deleted,OperationType operationType,LocalDateTime start,LocalDateTime end);

	Optional<LogOperation> findTop1ByPlaceIdAndIdNumberAndLoginIdAndDeletedAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String idNubmer, String loginId, int deleted, OperationType operationType,LocalDateTime start,LocalDateTime end);

}
