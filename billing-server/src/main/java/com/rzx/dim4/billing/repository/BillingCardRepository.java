package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.dto.billiing.BillingCardUserDTO;
import com.rzx.dim4.billing.entity.BillingCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date Jun 19, 2020 11:36:58 AM
 */
public interface BillingCardRepository extends JpaRepository<BillingCard, Long>, JpaSpecificationExecutor<BillingCard> {

	Optional<BillingCard> findTop1ByOrderByIdDesc();

	Optional<BillingCard> findByPlaceIdAndIdNumber(String placeId, String idNumber);

	Optional<BillingCard> findByPlaceIdAndIdcardNum(String placeId, String idcardNum);

	Optional<BillingCard> findByPlaceIdAndCardIdAndDeleted(String placeId, String cardId, int deleted);

	//	@QueryHints(value = {@QueryHints(name = "javax.persistence.cache.storeMode",value = "REFRESH")})
	Optional<BillingCard> findByPlaceIdAndIdNumberAndDeleted(String placeId, String idNumber, int deleted);

	Optional<BillingCard> findByPlaceIdAndLoginNameAndDeleted(String placeId, String loginName, int deleted);

	List<BillingCard> findByIdNumberAndDeleted(String idNumber, int deleted);

	Optional<BillingCard> findByChainIdAndIdNumberAndChainCardAndDeletedAndCardTypeIdNotIn(String chainId, String idNumber, int chainCard, int deleted, List<String> cardTypeIds);

	List<BillingCard> findByPlaceIdAndIdNameLike(String placeId, String idName);

	List<BillingCard> findByPlaceIdInAndIdNameLikeAndDeleted(List<String> placeIds, String idName, int deleted);

	Optional<BillingCard> findByPlaceIdAndIdNumberLikeAndDeleted(String placeId, String idNumber, int deleted);

	List<BillingCard> findByPlaceIdAndDeletedAndIdNumberLike(String placeId, int deleted, String idNumber);

	List<BillingCard> findAllByPlaceIdAndDeletedAndIdNumberIsLike(String placeId, int deleted, String idNumber);


	List<BillingCard> findAllByPlaceIdAndCardIdIn(String placeId, List<String> cardIds);

	List<BillingCard> findAllByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);

	List<BillingCard> findByPlaceIdInAndIdNumberAndDeleted(List<String> placeIds, String idNumber, int deleted);

	List<BillingCard> findByChainIdAndIdNumberAndDeletedAndCardTypeIdNotIn(String chainId, String idNumber, int deleted, List<String> cardTypeIds);

	List<BillingCard> findByChainIdAndIdNumberAndCardTypeIdInAndDeleted(String chainId, String idNumber, List<String> cardTypeId, int deleted);

	List<BillingCard> findByPlaceIdAndIdNumberInAndDeleted(String placeId, List<String> idNumbers, int deleted);

	List<BillingCard> findByPlaceIdAndCardTypeIdAndDeletedOrderByIdDesc(String placeId, String cardTypeId, int deleted);

	List<BillingCard> findByChainIdAndIdNumberInAndDeleted(String chainId, List<String> idNumbers, int deleted);

	List<BillingCard> findByPlaceIdInAndIdNumberInAndDeleted(List<String> placeIds, List<String> idNumbers, int deleted);

	/**
	 * 这里只有艺龙转换会员时调用
	 *
	 * @param placeIds
	 * @param idNumbers
	 * @param chainCard
	 * @param deleted
	 * @return
	 */
	List<BillingCard> findByPlaceIdInAndIdNumberInAndChainCardAndDeleted(List<String> placeIds, List<String> idNumbers, int chainCard, int deleted);

//	@Query(value = "select card_id from billing_card where id_number = ?1 and deleted = 0", nativeQuery = true)
//	List<String> findCardIdByIdNumber(String idNumber);

	@Query(value = "select * from billing_card where place_id = ?1 and deleted = 0 and active_time > ?2 order by active_time asc;", nativeQuery = true)
	List<BillingCard> findForCashierUserView(String placeId, LocalDateTime activeTime);

	List<BillingCard> findByPlaceIdAndChainCardAndDeleted(String placeId, int chainCard, int deleted);

//	List<BillingCard> findByPlaceIdInAndDeletedAndIdNumber(List<String> placeIds, int deleted,String idNumber);

	List<BillingCard> findByChainIdAndIdNumberAndDeleted(String chainId, String idNumber, int deleted);

	@Query(value = "select distinct id_number as idNumber, id_name as idName from billing_card where id_number in (:idNumbers)", nativeQuery = true)
	List<BillingCardUserDTO> findByIdNumbers(@Param("idNumbers") List<String> idNumbers);

	/**
	 * 临时卡-未消费总额(在线余额不用统计，结账时会自动退)
	 */
	@Query("select sum(bc.cashAccount )  from BillingCard bc where bc.placeId=?1 and bc.deleted= 0 and bc.cardTypeId='1000'")
	Integer sumBalanceTemporaryCardCashIncomeByPlaceId(String placeId);


	/**
	 * 统计临时卡/非临时卡 卡数量、现金总余额、赠送总余额
	 */
	@Query(value = "select count(id) cardNum,SUM(cash_account) as cashTotal,SUM(present_account) as presentTotal from billing_card where place_id = :placeId " +
			"AND if(:cardTypeId = '1000' ,card_type_id = '1000', if(:cardTypeId = '' or :cardTypeId = null ,card_type_id != '1000',card_type_id = :cardTypeId)) " +
			"AND if(:idName != '' or :idName != null ,id_name like :idName, 1 = 1) " +
			"AND if(:idNumber != '' or :idNumber != null ,id_number like :idNumber, 1 = 1) " +
			"AND (coalesce(:startTime,null) is null or created >= :startTime) " +
			"AND (coalesce(:endTime,null) is null or created <= :endTime) " +
			"AND (coalesce(:cardIds,null) is null or card_id in (:cardIds)) " +
			"AND deleted=0;", nativeQuery = true)
	Map<String, Integer> findCardNumAndTotalAccount(@Param("placeId") String placeId,
													@Param("cardTypeId") String cardTypeId,
													@Param("idName") String idName,
													@Param("idNumber") String idNumber,
													@Param("startTime") LocalDateTime startTime,
													@Param("endTime") LocalDateTime endTime,
													@Param("cardIds") List<String> cardIds);

	@Query(value = "select count(id) cardNum,(SUM(cash_account) + SUM(temporary_online_account)) as cashTotal,SUM(present_account) as presentTotal from billing_card where place_id = :placeId " +
			"AND if(:cardTypeId = '1000' ,card_type_id = '1000', if(:cardTypeId = '' or :cardTypeId = null ,card_type_id != '1000',card_type_id = :cardTypeId)) " +
			"AND if(:idName != '' or :idName != null ,id_name like :idName, 1 = 1) " +
			"AND if(:idNumber != '' or :idNumber != null ,id_number like :idNumber, 1 = 1) " +
			"AND if(:mobile != '' or :mobile != null ,phone_number like :mobile, 1 = 1) " +
			"AND if(:minTotalAmount != '' or :minTotalAmount != null, (cash_account + present_account) >= :minTotalAmount, 1 = 1) " +
			"AND if(:maxTotalAmount != '' or :maxTotalAmount != null, (cash_account + present_account) <= :maxTotalAmount, 1 = 1) " +
			"AND if(:minPoints != '' or :minPoints != null, points >= :minPoints, 1 = 1) " +
			"AND if(:maxPoints != '' or :maxPoints != null, points <= :maxPoints, 1 = 1) " +
			"AND if(:remark != '' or :remark != null, remark like :remark, 1 = 1) " +
			"AND (coalesce(:startTime, null) is null or created >= :startTime) " +
			"AND (coalesce(:endTime, null) is null or created <= :endTime) " +
			"AND (coalesce(:cardIds, null) is null or card_id in (:cardIds)) " +
			"AND (coalesce(:idNumbers, null) is null or id_number in (:idNumbers)) " +
			"AND if(:idcardNum != '' or :idcardNum != null, id_number like :idcardNum, 1 = 1) " +
			"AND deleted=0;", nativeQuery = true)
	Map<String, BigDecimal> findCardNumAndTotalAccount2(@Param("placeId") String placeId,
														@Param("cardTypeId") String cardTypeId,
														@Param("idName") String idName,
														@Param("idNumber") String idNumber,
														@Param("startTime") LocalDateTime startTime,
														@Param("endTime") LocalDateTime endTime,

														@Param("mobile") String mobile,
														@Param("minTotalAmount") Integer minTotalAmount,
														@Param("maxTotalAmount") Integer maxTotalAmount,
														@Param("minPoints") Integer minPoints,
														@Param("maxPoints") Integer maxPoints,
														@Param("remark") String remark,

														@Param("cardIds") List<String> cardIds,
														@Param("idNumbers") List<String> idNumbers,
														@Param("idcardNum") String idcardNum);

	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card_type_id = :chainCardTypeId, card_type_id = :cardTypeId, card_type_name = :cardTypeName, cash_account = :cashAccount, temporary_online_account=0, chain_card = :chainCard, updated = now() WHERE place_id = :placeId and card_id in (:cardIds)", nativeQuery = true)
	int updateCardTypeByCardId(@Param("chainCardTypeId") String chainCardTypeId, @Param("cardTypeId") String cardTypeId, @Param("cardTypeName") String cardTypeName,
							   @Param("placeId") String placeId, @Param("cardIds") List<String> cardIds, @Param("cashAccount") int cashAccount, @Param("chainCard") int chainCard);

	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card_type_id = :chainCardTypeId, card_type_id = :cardTypeId, card_type_name = :cardTypeName, updated = now() WHERE place_id = :placeId and card_id = :cardId ", nativeQuery = true)
	int updateChainCardTypeByCardId(@Param("chainCardTypeId") String chainCardTypeId, @Param("cardTypeId") String cardTypeId, @Param("cardTypeName") String cardTypeName,
									@Param("placeId") String placeId, @Param("cardId") String cardId);

	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card_type_id = :chainCardTypeId, card_type_id = :cardTypeId, card_type_name = :cardTypeName, cash_account = :cashAccount, temporary_online_account=0, chain_card = :chainCard, points = :points, updated = now() WHERE place_id = :placeId and card_id = :cardId", nativeQuery = true)
	int tempUpgradeCardType(@Param("chainCardTypeId") String chainCardTypeId, @Param("cardTypeId") String cardTypeId,
							@Param("cardTypeName") String cardTypeName, @Param("placeId") String placeId,
							@Param("cardId") String cardId, @Param("cashAccount") int cashAccount,
							@Param("chainCard") int chainCard, @Param("points") int points);

	/**
	 * 修改卡类型如果是将低卡类型修改为高等级卡类型，需要补全最小积分要求
	 */
	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card_type_id = :chainCardTypeId, card_type_id = :cardTypeId, card_type_name = :cardTypeName, points = :minPointsRequirement, updated = now() WHERE place_id = :placeId and card_id = :cardId", nativeQuery = true)
	int updateCardTypeByCardIdParam(@Param("chainCardTypeId") String chainCardTypeId, @Param("cardTypeId") String cardTypeId, @Param("cardTypeName") String cardTypeName, @Param("minPointsRequirement") int minPointsRequirement,
									@Param("placeId") String placeId, @Param("cardId") String cardId);

	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET cash_account = cash_account - :cashAccount, present_account = present_account - :presentAccount, temporary_online_account = temporary_online_account - :temporaryOnlineAccount, updated = now() WHERE place_id = :placeId and card_id = :cardId", nativeQuery = true)
	int updateBillingCardAccount(@Param("placeId") String placeId, @Param("cardId") String cardId,
								 @Param("cashAccount") int cashAccount, @Param("presentAccount") int presentAccount, @Param("temporaryOnlineAccount") int temporaryOnlineAccount);

	@Modifying(clearAutomatically = true, flushAutomatically = true)
	@Transactional
	@Query(value = "UPDATE billing_card SET points = points + :points, updated = now() WHERE place_id = :placeId and card_id = :cardId", nativeQuery = true)
	int updateBillingCardPoints(@Param("placeId") String placeId, @Param("cardId") String cardId, @Param("points") int points);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET card_type_name = :cardTypeName, updated = now() WHERE place_id = :placeId and card_type_id = :cardTypeId and card_id IN (:cardIds)", nativeQuery = true)
	int updateCardTypeName(@Param("cardTypeName") String cardTypeName, @Param("placeId") String placeId,
						   @Param("cardTypeId") String cardTypeId, @Param("cardIds") List<String> cardIds);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET card_type_name = :cardTypeName, card_type_id = :cardTypeId, updated = now() WHERE place_id = :placeId  and card_id = :cardId", nativeQuery = true)
	int updateCardTypeWithTopup(@Param("cardTypeName") String cardTypeName, @Param("cardTypeId") String cardTypeId, @Param("placeId") String placeId, @Param("cardId") String cardId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET card_type_name = :cardTypeName, card_type_id = :cardTypeId, updated = now(),cash_account = cash_account + temporary_online_account,temporary_online_account =0  WHERE place_id = :placeId  and card_id = :cardId", nativeQuery = true)
	int updateTempCardTypeWithTopup(@Param("cardTypeName") String cardTypeName, @Param("cardTypeId") String cardTypeId, @Param("placeId") String placeId, @Param("cardId") String cardId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card = 0, chain_id = null, chain_card_type_id = null, updated = now() WHERE place_id = :placeId", nativeQuery = true)
	int updateChainToZeroCardByPlaceId(@Param("placeId") String placeId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_card = 0, updated = now() WHERE card_id IN (:cardIds) and chain_id = :chainId and deleted= 0 ", nativeQuery = true)
	int updateChainToZeroCardByChainId(@Param("cardIds") List<String> cardIds, @Param("chainId") String chainId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET card_type_name = :typeName, chain_id = :chainId, chain_card_type_id = :chainCardTypeId, updated = now() WHERE place_id = :placeId and card_type_id = :cardTypeId and card_type_name != :typeName", nativeQuery = true)
	int updateSpecialCardType(@Param("placeId") String placeId, @Param("cardTypeId") String specialPlaceCardTypeId, @Param("typeName") String specialChainCardTypeName, @Param("chainId") String chainId, @Param("chainCardTypeId") String chainCardTypeId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET card_type_id = :cardTypeId, card_type_name = :typeName, chain_card = 0, chain_id = :chainId, chain_card_type_id = :chainCardTypeId, updated = now() WHERE place_id = :placeId and card_type_id = :oldCardTypeId", nativeQuery = true)
	int updateCardType(String placeId, String oldCardTypeId, String cardTypeId, String typeName, String chainId, String chainCardTypeId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card bct, (SELECT DISTINCT bct3.id_number,\n" +
			"                                          bctt.chain_card_type_id,\n" +
			"                                          bctt2.card_type_id,\n" +
			"                                          bctt2.type_name,\n" +
			"                                          bctt2.place_id\n" +
			"                          FROM billing_card bct3,\n" +
			"                               billing_card_type bctt,\n" +
			"                               billing_card_type bctt2\n" +
			"                          WHERE bct3.chain_card = 0\n" +
			"                            AND bct3.place_id IN (:placeIds)\n" +
			"                            AND bct3.deleted = 0\n" +
			"                            AND bct3.place_id = bctt.place_id\n" +
			"                            AND bct3.card_type_id = bctt.card_type_id\n" +
			"                            AND bctt.deleted = 0\n" +
			"                            AND bctt.chain_card_type_id = bctt2.chain_card_type_id\n" +
			"                            AND bctt2.deleted = 0\n" +
			"                            AND bctt2.place_id = :placeId) AS nt\n" +
			"SET bct.card_type_id   = nt.card_type_id,\n" +
			"    bct.card_type_name = nt.type_name,\n" +
			"    bct.chain_card     = 1,\n" +
			"    bct.updated        = NOW()\n" +
			"WHERE bct.id_number = nt.id_number\n" +
			"  AND bct.place_id = nt.place_id\n" +
			"  AND bct.deleted = 0\n" +
			"  AND bct.card_type_id != '1000'\n" +
			"  AND bct.card_type_id != '1002'\n" +
			"  AND bct.place_id = :placeId", nativeQuery = true)
	int unionCardType(@Param("placeIds") List<String> placeIds, @Param("placeId") String placeId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET deleted = 1,cash_account = 0,present_account = 0,points = 0, updated = now() WHERE place_id in (:placeIds) and id_number in (:idNumbers)", nativeQuery = true)
	int updateCardByPlaceIdsAndIdNumbers(@Param("placeIds") List<String> placeIds, @Param("idNumbers") List<String> idNumbers);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET active_time = null, updated = now(), deleted = IF(:deleted = 1,1,:deleted)  WHERE place_id = :placeId and card_id = :cardId", nativeQuery = true)
	int billingCardLogout(@Param("placeId") String placeId, @Param("cardId") String cardId, @Param("deleted") int deleted);

	/**
	 * 艺龙查询会员列表
	 *
	 * @param chainId
	 * @param chainCardTypeId
	 * @param idNumber
	 * @param idName
	 * @param start
	 * @param end
	 * @return
	 */
	@Query(value = "select *from billing_card where chain_id = (:chainId)  and IF(:chainCardTypeId != '' or :chainCardTypeId != null ,chain_card_type_id = :chainCardTypeId, card_type_id not in ('1000','1002')) and IF(:idNumber != '' or :idNumber != null ,id_number = :idNumber, 1 = 1) and IF(:idName != '' or :idName != null ,id_name = :idName, 1 = 1) and IF(:placeId != '' or :placeId != null ,place_id = :placeId, 1 = 1) and chain_card = 0 and deleted = 0 limit :start,:end", nativeQuery = true)
	List<BillingCard> findByChainCardMemberAndDeleted(@Param("chainId") String chainId, @Param("chainCardTypeId") String chainCardTypeId, @Param("idNumber") String idNumber, @Param("placeId") String placeId, @Param("idName") String idName, @Param("start") int start, @Param("end") int end);

	@Query(value = "select *from billing_card where place_id = :placeId and chain_id = (:chainId)  and IF(:chainCardTypeId != '' or :chainCardTypeId != null ,chain_card_type_id = :chainCardTypeId, card_type_id not in ('1000','1002')) and IF(:idNumber != '' or :idNumber != null ,id_number = :idNumber, 1 = 1) and IF(:idName != '' or :idName != null ,id_name = :idName, 1 = 1)  and chain_card = 0 and deleted = 0 limit :start,:end", nativeQuery = true)
	List<BillingCard> findByChainCardMemberAndDeleted2(@Param("chainId") String chainId, @Param("chainCardTypeId") String chainCardTypeId, @Param("idNumber") String idNumber, @Param("placeId") String placeId, @Param("idName") String idName, @Param("start") int start, @Param("end") int end);


	List<BillingCard> findByPlaceIdAndDeletedAndChainIdIsNull(String placeId, int deleted);

	List<BillingCard> findByPlaceIdAndDeletedAndCardTypeIdNotIn(String placeId, int deleted, List<String> cardTypeIds);

	@Modifying
	@Transactional
	@Query(value = "UPDATE billing_card SET chain_id = :chainId, chain_card_type_id = :chainCardTypeId  WHERE place_id = :placeId and card_id in (:cardIds)", nativeQuery = true)
	int updateChainMember(@Param("placeId") String placeId, @Param("cardIds") List<String> cardIds, @Param("chainId") String chainId, @Param("chainCardTypeId") String chainCardTypeId);

	List<BillingCard> findAllByChainIdAndChainCardAndPlaceIdNotInAndIdNumberInAndDeleted(String chainId, int chainCard, List<String> placeIds, List<String> idNumbers, int deleted);

	List<BillingCard> findByChainIdAndChainCardAndPlaceIdNotAndIdNumberInAndDeletedAndCardTypeIdNotIn(String chainId, int chainCard, String placeId, List<String> idNumbers, int deleted, List<String> cardTypeIds);

//	@Modifying
//	@Transactional
//	@Query(value = "UPDATE billing_card SET cash_account = 0  WHERE chain_id = :chainId and id_number = :idNumber", nativeQuery = true)
//	int clearChainCashAccount(@Param("chainId") String chainId, @Param("idNumber") String idNumber);
//
//	@Modifying
//	@Transactional
//	@Query(value = "UPDATE billing_card SET present_account = 0  WHERE chain_id = :chainId and id_number = :idNumber", nativeQuery = true)
//	int clearChainPresentAccount(@Param("chainId") String chainId, @Param("idNumber") String idNumber);
//
//	@Modifying
//	@Transactional
//	@Query(value = "UPDATE billing_card SET card_type_id = :cardTypeId , login_pass = :password ,id_name = :idName, phone_number = :phoneNumber ,chain_card_type_id = :chainCardTypeId WHERE chain_id = :chainId and id_number = :idNumber", nativeQuery = true)
//	int updateChainThirdCard(@Param("chainId") String chainId, @Param("idNumber") String idNumber, @Param("cardTypeId") String cardTypeId, @Param("password") String password,
//							 @Param("idName") String idName, @Param("phoneNumber") String phoneNumber, @Param("chainCardTypeId") String chainCardTypeId);

	List<BillingCard> findByPlaceIdAndCardIdInAndDeleted(String placeId,List<String> cardIds,int deleted);

}