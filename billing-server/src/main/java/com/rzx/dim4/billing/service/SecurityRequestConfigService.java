package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.SecurityRequestConfig;
import com.rzx.dim4.billing.repository.SecurityRequestConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class SecurityRequestConfigService {

    @Autowired
    SecurityRequestConfigRepository securityRequestConfigRepository;

    public Optional<SecurityRequestConfig> findByType (int type) {
        return securityRequestConfigRepository.findByType(type);
    }

    public Optional<SecurityRequestConfig> findByTypeAndPlaceId (String placeId) {
        return securityRequestConfigRepository.findByPlaceIdAndDeleted(placeId,0);
    }

    public List<SecurityRequestConfig> findByPlaceIdIn(List<String> placeIds) {
        return securityRequestConfigRepository.findByPlaceIdIn(placeIds);
    }

    /**
     * 查询所有记录
     */
    public List<SecurityRequestConfig> findAll() {
        return securityRequestConfigRepository.findAll();
    }


}
