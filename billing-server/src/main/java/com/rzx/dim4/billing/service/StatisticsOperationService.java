package com.rzx.dim4.billing.service;

import com.alibaba.excel.util.CollectionUtils;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.StatisticsOperationByDayBO;
import com.rzx.dim4.base.bo.billing.SumStatisticsOperationByDayBO;
import com.rzx.dim4.billing.repository.StatisticsOperationByDayRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StatisticsOperationService {

    @Autowired
    StatisticsOperationByDayRepository statisticsOperationByDayRepository;

    /**
     * 分页查询
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @param pageable
     * @return
     */
    public List<StatisticsOperationByDayBO> queryStatisticsOperation(List<String> placeIds, String startDateTime,
                                                                     String endDateTime, Pageable pageable) {
        List<Map<String, String>> list = statisticsOperationByDayRepository.queryStatisticsOperation(startDateTime,
                endDateTime, placeIds, pageable);
        List<StatisticsOperationByDayBO> bos = new ArrayList<>();
        for (Map<String, String> map : list) {
            StatisticsOperationByDayBO bo = new StatisticsOperationByDayBO();
            bo.setPlaceId(map.get("placeId"));
            bo.setCountDay(map.get("countDay"));
            Object obj = map.get("sumMemberTopupIncome");
            bo.setSumMemberTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumTemporaryTopupIncome");
            bo.setSumTemporaryTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumClientTopupIncome");
            bo.setSumClientTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCashTopupIncome");
            bo.setSumCashTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCashierOnlineTopupIncome");
            bo.setSumCashierOnlineTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumMpTopupIncome");
            bo.setSumMpTopupIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumPresentIncome");
            bo.setSumPresentIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumTotalIncome");
            bo.setSumTotalIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumTotalReversal");
            bo.setSumTotalReversal(obj == null ? 0 : (Integer) obj);
            obj = map.get("createMemberCardNum");
            bo.setCreateMemberCardNum(obj == null ? 0 : (Integer) obj);
            obj = map.get("createTemporaryCardNum");
            bo.setCreateTemporaryCardNum(obj == null ? 0 : (Integer) obj);
            obj = map.get("countReversal");
            bo.setCountReversal(obj == null ? 0 : (Integer) obj);
            obj = map.get("countTopup");
            bo.setCountTopup(obj == null ? 0 : (Integer) obj);
            obj = map.get("countRefund");
            bo.setCountRefund(obj == null ? 0 : (Integer) obj);
            obj = map.get("countPackage");
            bo.setCountPackage(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumPackageTotal");
            bo.setSumPackageTotal(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCashOutIncome");
            bo.setSumCashOutIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumOnlineOutIncome");
            bo.setSumOnlineOutIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumPresentTupupTotal");
            bo.setSumPresentTupupTotal(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumPresentReversalTotal");
            bo.setSumPresentReversalTotal(obj == null ? 0 : (Integer) obj);
            obj = map.get("otherIncome");
            bo.setOtherIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("otherOutcome");
            bo.setOtherOutcome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumAppTopupIncome");
            bo.setSumAppTopupIncome(obj == null ? 0 : (Integer) obj);
            bos.add(bo);
        }
        return bos;
    }

    /**
     * 查询所有数据合计
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public SumStatisticsOperationByDayBO allQueryStatisticsOperation(List<String> placeIds, String startDateTime,
                                                                     String endDateTime) {
        Map<String, String> map = statisticsOperationByDayRepository.allQueryStatisticsOperation(startDateTime,
                endDateTime, placeIds);

        SumStatisticsOperationByDayBO bo = new SumStatisticsOperationByDayBO();

        if (Objects.isNull(map)) {
            return bo;
        }

        Object obj = map.get("sumMemberTopupIncome");
        bo.setSumMemberTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumTemporaryTopupIncome");
        bo.setSumTemporaryTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumClientTopupIncome");
        bo.setSumClientTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCashTopupIncome");
        bo.setSumCashTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCashierOnlineTopupIncome");
        bo.setSumCashierOnlineTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumMpTopupIncome");
        bo.setSumMpTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumPresentIncome");
        bo.setSumPresentIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumTotalIncome");
        bo.setSumTotalIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumTotalReversal");
        bo.setSumTotalReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCreateMemberCardNum");
        bo.setSumCreateMemberCardNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCreateTemporaryCardNum");
        bo.setSumCreateTemporaryCardNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCountReversal");
        bo.setSumCountReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCountTopup");
        bo.setSumCountTopup(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCountRefund");
        bo.setSumCountRefund(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCountPackage");
        bo.setSumCountPackage(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumPackageTotal");
        bo.setSumPackageTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCashOutIncome");
        bo.setSumCashOutIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOnlineOutIncome");
        bo.setSumOnlineOutIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumPresentTupupTotal");
        bo.setSumPresentTupupTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumPresentReversalTotal");
        bo.setSumPresentReversalTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOtherIncome");
        bo.setSumOtherIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOtherOutcome");
        bo.setSumOtherOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumAppTopupIncome");
        bo.setSumAppTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        return bo;
    }

    public int countStatisticsOperation(List<String> placeIds, String startDateTime,
                                        String endDateTime) {
        return statisticsOperationByDayRepository.countStatisticsOperation(startDateTime, endDateTime, placeIds);
    }

    /**
     * 获取场所纯收入统计
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @param flag          0:按天  1:按月
     * @return
     */
    public List<StatisticsOperationByDayBO> querySumTotalIncome(List<String> placeIds, String startDateTime,
                                                                String endDateTime, int flag) {
        List<Map<String, String>> list = new ArrayList<>();
        if (flag == 0) {
            list = statisticsOperationByDayRepository.querySumTotalIncomeByDay(startDateTime,
                    endDateTime, placeIds);
        } else {
            list = statisticsOperationByDayRepository.querySumTotalIncomeByMonth(startDateTime,
                    endDateTime, placeIds);
        }
        List<StatisticsOperationByDayBO> bos = new ArrayList<>();
        for (Map<String, String> map : list) {
            StatisticsOperationByDayBO bo = new StatisticsOperationByDayBO();
            bo.setCountDay(map.get("countDay"));
            Object obj = map.get("sumTotalIncome");
            bo.setSumTotalIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            bos.add(bo);
        }
        return bos;
    }

    /**
     * 连锁首页--收入排行
     *
     * @param placeIds
     * @param flag     0:本周  1:本月 2:本季度
     * @return
     */
    public List<StatisticsOperationByDayBO> querySumTotalIncomeRank(List<String> placeIds, int flag) {
        log.info("querySumTotalIncomeRank placeIds:{}, flag:{}", placeIds, flag);

        List<Map<String, String>> mapList;
        String startDateTime;
        String endDateTime;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.now();

        if (flag == 0) {
            // 本周
            startDateTime = formatter.format(localDate.minus(7, ChronoUnit.DAYS));
            endDateTime = formatter.format(localDate.minus(1, ChronoUnit.DAYS));

        } else if (flag == 1) {
            // 本月
            startDateTime = formatter.format(localDate.minus(30, ChronoUnit.DAYS));
            endDateTime = formatter.format(localDate.minus(1, ChronoUnit.DAYS));
        } else {
            // 本季度
            startDateTime = formatter.format(localDate.minus(90, ChronoUnit.DAYS));
            endDateTime = formatter.format(localDate.minus(1, ChronoUnit.DAYS));
        }

        log.info("querySumTotalIncomeRank startDateTime:{}, endDateTime:{}", startDateTime, endDateTime);
        mapList = statisticsOperationByDayRepository.querySumTotalIncomeRank(startDateTime, endDateTime, placeIds);

        List<StatisticsOperationByDayBO> bos = new ArrayList<>();

        Map<String, Integer> placeSum = new HashMap<>();
        boolean notEmpty = !CollectionUtils.isEmpty(mapList);
        if (notEmpty) {
            placeSum = mapList.stream()
                    .collect(Collectors.toMap(map -> map.get("placeId"),
                            map -> new BigDecimal(((Object) map.get("sumTotalIncome")).toString()).intValue(), Integer::sum));
        }

        for (String placeId : placeIds) {
            StatisticsOperationByDayBO bo = new StatisticsOperationByDayBO();
            bo.setPlaceId(placeId);
            if (notEmpty && placeSum.containsKey(placeId)) {
                Integer sum = placeSum.get(placeId);
                bo.setSumTotalIncome(sum);
            } else {
                bo.setSumTotalIncome(0);
            }
            bos.add(bo);
        }

        bos.sort(Comparator.comparing(StatisticsOperationByDayBO::getSumTotalIncome).reversed());
        log.info("连锁首页--收入排行:{}", new Gson().toJson(bos));
        return bos;
    }

    /**
     * 分页查询月统计
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @param pageable
     * @return
     */
    public List<StatisticsOperationByDayBO> queryStatisticsOperationByMonth(List<String> placeIds, String startDateTime,
                                                                     String endDateTime, Pageable pageable) {
        List<Map<String, String>> list = statisticsOperationByDayRepository.queryStatisticsOperationByMonth(startDateTime,
                endDateTime, placeIds, pageable);
        List<StatisticsOperationByDayBO> bos = new ArrayList<>();
        for (Map<String, String> map : list) {
            StatisticsOperationByDayBO bo = new StatisticsOperationByDayBO();
            bo.setPlaceId(map.get("placeId"));
            bo.setCountDay(map.get("countDay"));
            Object obj = map.get("sumMemberTopupIncome");
            bo.setSumMemberTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumTemporaryTopupIncome");
            bo.setSumTemporaryTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumClientTopupIncome");
            bo.setSumClientTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCashTopupIncome");
            bo.setSumCashTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCashierOnlineTopupIncome");
            bo.setSumCashierOnlineTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumMpTopupIncome");
            bo.setSumMpTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPresentIncome");
            bo.setSumPresentIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumTotalIncome");
            bo.setSumTotalIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumTotalReversal");
            bo.setSumTotalReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("createMemberCardNum");
            bo.setCreateMemberCardNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("createTemporaryCardNum");
            bo.setCreateTemporaryCardNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("countReversal");
            bo.setCountReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("countTopup");
            bo.setCountTopup(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("countRefund");
            bo.setCountRefund(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("countPackage");
            bo.setCountPackage(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPackageTotal");
            bo.setSumPackageTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCashOutIncome");
            bo.setSumCashOutIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumOnlineOutIncome");
            bo.setSumOnlineOutIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPresentTupupTotal");
            bo.setSumPresentTupupTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPresentReversalTotal");
            bo.setSumPresentReversalTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("otherIncome");
            bo.setOtherIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("otherOutcome");
            bo.setOtherOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumAppTopupIncome");
            bo.setSumAppTopupIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            bos.add(bo);
        }
        return bos;
    }

    public int countStatisticsOperationByMonth(List<String> placeIds, String startDateTime,
                                        String endDateTime) {
        return statisticsOperationByDayRepository.countStatisticsOperationByMonth(startDateTime, endDateTime, placeIds);
    }
}
