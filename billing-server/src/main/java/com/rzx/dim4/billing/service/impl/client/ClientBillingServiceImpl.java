package com.rzx.dim4.billing.service.impl.client;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BillingOnlineBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.ReceiveCouponDetailApi;
import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.invite.InviteConfig;
import com.rzx.dim4.billing.entity.invite.InviteOnline;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.Invite.InviteConfigService;
import com.rzx.dim4.billing.service.Invite.InviteOnlineService;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
import com.rzx.dim4.billing.service.impl.hotel.HotelBillingServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;

import static com.rzx.dim4.billing.cons.BillingConstants.NEXT_TIME_PLUS;

/**
 * 客户端计费接口
 *
 * <AUTHOR>
 * @date 2021-08-05 17:07
 */
@Service
@Slf4j
public class ClientBillingServiceImpl implements CoreService {

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private LogHbService logHbService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private PackageTimeReserveService cashierTempPackageTimeService;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    private BillingRuleCommonService billingRuleCommonService;

    @Autowired
    private BillingRuleAccService billingRuleAccService;

    @Autowired
    private LogAccService logAccService;

    @Autowired
    private HotelBillingServiceImpl hotelBillingServiceImpl;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private LogRoomService logRoomService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    WechatMessageApi wechatMessageApi;

    @Autowired
    private ReceiveCouponDetailApi receiveCouponDetailApi;

    @Autowired
    BalanceDetailsAlgorithm balanceDetailsAlgorithm;

    @Autowired
    InviteOnlineService inviteOnlineService;

    @Autowired
    InviteConfigService inviteConfigService;

    @Autowired
    PlacementService placementService;

    private ThreadLocal<Boolean> packageTimeEndTL = ThreadLocal.withInitial(() -> false); // 包时结束标识
    private ThreadLocal<PackageTimeReserve> packageTimeReserveTL = ThreadLocal.withInitial(() -> null); // 收银台包时记录
    private ThreadLocal<Integer> isMasterTL = ThreadLocal.withInitial(() -> 1); // 是否是主卡

    private ThreadLocal<Integer> costCash = ThreadLocal.withInitial(() -> 0); // 本金扣费总额

    private ThreadLocal<Integer> costPresent = ThreadLocal.withInitial(() -> 0); // 奖励扣费总额

    /**
     * 计费接口
     * <p>
     * 该接口为客户端频繁调用，如非十分必要，不要在此添加逻辑
     *
     * @param params
     * @return GenericResponse
     */
    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() != 2) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0);
        String clientId = params.get(1);

        // 获取网吧配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        if (placeBizConfig.getBillingType() == 0) {
            List<String> p = new ArrayList<String>();
            p.add(placeId);
            p.add(clientId);
            return hotelBillingServiceImpl.doService(p);
        }

        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime nextTime = LocalDateTime.now();
        this.packageTimeEndTL.set(false);
        this.packageTimeReserveTL.set(null);
        this.isMasterTL.set(1);
        this.costCash.set(0); // 本金扣费总额
        this.costPresent.set(0); // 奖励扣费总额
        LogRoom logRoom = null;
        InviteOnline inviteOnline = null;

        // 并发加锁处理
        String billingKey = billingLockAlgorithm.acquireLock(placeId, clientId);
        if (billingKey == null) {
            // 没拿到锁
            return new GenericResponse<>(ServiceCodes.BILLING_IN_PROGRESS);
        }

        // 获取正在计费的BillingOnline
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndClientId(placeId,
                clientId);
        if (!optBillingOnline.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_ONLINE_NOT_FOUND); // 未找到该客户端在线计费信息,退出本次心跳计费
        }
        BillingOnline online = optBillingOnline.get();
        // 并发加锁处理
        String cardIdKey = billingLockAlgorithm.cardIdAcquireLock(placeId, online.getCardId());
        if (cardIdKey == null) {
            // 没拿到锁
            return new GenericResponse<>(ServiceCodes.BILLING_IN_PROGRESS);
        }

        // 先拿到锁，再执行查询，避免并发时另一方锁执行完成，这边查询到的是修改前的值
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndClientId(placeId,
                clientId);
        if (!billingOnlineOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_ONLINE_NOT_FOUND); // 未找到该客户端在线计费信息,退出本次心跳计费
        }
        BillingOnline billingOnline = billingOnlineOpt.get();

        // 优先获取计费卡信息
        Optional<BillingCard> optBillingCard  = billingCardService.findByPlaceIdAndCardId(placeId,billingOnline.getCardId());
        if (!optBillingCard.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND); // 未找到该计费卡信息,退出本次心跳计费
        }
        BillingCard billingCard = optBillingCard.get(); //这个billingCard的金额会参与扣费，所以不能直接查询连锁会员卡总金额
        BillingOnlineBO bo = billingOnline.toBO();

        BillingCardBO viewBillingCard = billingCard.toBO();  //这个BO返回用于页面展示总金额,需要查询连锁漫游金额给前端
        if(!billingOnline.getCardTypeId().equals("1000") && !billingOnline.getCardTypeId().equals("1002")){
            optBillingCard =  billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId,billingOnline.getIdNumber(),0);
            viewBillingCard = optBillingCard.get().toBO();
        }
        int userCouponCount = receiveCouponDetailApi.findUserCouponCount(placeId, billingOnline.getIdNumber());
        bo.setUserCouponCount(userCouponCount);
        bo.setPresentAccount(viewBillingCard.getPresentAccount());
        bo.setCashAccount(viewBillingCard.getCashAccount()); //线上金额+现金金额
        bo.setTemporaryOnlineAccount(viewBillingCard.getTemporaryOnlineAccount());
        bo.setPoints(viewBillingCard.getPoints());
        bo.setNation(viewBillingCard.getNation());
        bo.setPhoneNumber(viewBillingCard.getPhoneNumber());
        bo.setCardTypeName(viewBillingCard.getCardTypeName());
        bo.setRemark(viewBillingCard.getRemark());
        bo.setActiveType(billingCard.getActiveType());
        bo.setActiveTypeCode(null != billingCard.getActiveType() ? billingCard.getActiveType().getValue() : null);
        bo.setValidPeriod(viewBillingCard.getValidPeriod());
        bo.setIssuingAuthority(viewBillingCard.getIssuingAuthority());
        bo.setAddress(viewBillingCard.getAddress());
        bo.setActiveTime(viewBillingCard.getActiveTime());
        bo.setIdcardNum(viewBillingCard.getIdcardNum());

        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, billingOnline.getCardId());
        if (logRoomOpt.isPresent()) {
            // 如果是在包间上机，查询主次卡信息
            logRoom = logRoomOpt.get();
            this.isMasterTL.set(logRoom.getIsMaster());
        }

        if (billingOnline.getIsInvite() > 0) {
            Optional<InviteOnline> inviteOnlineOpt = inviteOnlineService.findByPlaceIdAndIdNumberAndStatus(placeId, billingOnline.getIdNumber(), 1);
            if (inviteOnlineOpt.isPresent()) {
                bo.setInviteCode(inviteOnlineOpt.get().getInviteCode());
                inviteOnline = inviteOnlineOpt.get();
            }
        }

        if (nowTime.isBefore(billingOnline.getNextTime())) { // 没到心跳时间直接返回
            bo.setIsMaster(this.isMasterTL.get());

            //查询总消费金额
            Integer sumDeduction = billingOnlineService.findSumDeductionByLoginId(bo.getLoginId());
            bo.setDeduction(sumDeduction);
            if(bo.getPackageFlag() > 0){
                //查询包时开始时间
                List<OperationType> operationTypes = new ArrayList<>();
                operationTypes.add(OperationType.CONVERT_BILLING_RULE);
                Optional<LogOperation> logOperationOptional = logOperationService.findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(bo.getPlaceId(), bo.getCardId(), operationTypes, bo.getBillingTime(), LocalDateTime.now());
                if(logOperationOptional.isPresent()){
                    bo.setPackageStartDate(logOperationOptional.get().getCreated());
                }else{
                    bo.setPackageStartDate(bo.getBillingTime());
                }
            }

            //查询是否有预包时，如果预包时的开始时间已达到则把nextTime时间改为当前时间，让下面逻辑执行预包时
            BillingRulePackageTime billingRulePackageTime = getPackageTimeBillingRule(billingOnline);
            if (null != billingRulePackageTime) {
                billingOnline.setNextTime(nowTime);
            }else{
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                billingLockAlgorithm.releaseLock(cardIdKey);
                return new GenericResponse<>(new ObjDTO<>(bo));
            }

        }

        // 以下都是 billingOnline.getNextTime() 已经到了，现在要更新 nextTime 或其他逻辑

        // 工作卡,直接返回
        if ("1002".equals(billingOnline.getCardTypeId()) || this.isMasterTL.get() == 0) {
            if (nowTime.isBefore(billingOnline.getNextTime())) { // 没到心跳时间直接返回
                bo.setIsMaster(this.isMasterTL.get());

                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                billingLockAlgorithm.releaseLock(cardIdKey);
                return new GenericResponse<>(new ObjDTO<>(bo));
            }
            // 工作卡和酒店不会判断预包时的信息
            billingOnline.setNextTime(billingOnline.getNextTime().plusMinutes(NEXT_TIME_PLUS));
            billingOnline.setUpdated(nowTime);
            billingOnlineService.save(billingOnline);

            bo.setNextTime(billingOnline.getNextTime());
            bo.setUpdated(billingOnline.getUpdated());
            bo.setIsMaster(this.isMasterTL.get());

            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            billingLockAlgorithm.releaseLock(cardIdKey);
            return new GenericResponse<>(new ObjDTO<>(bo));
        }



        Optional<BillingRuleCommon> optCommonBillingRule = billingRuleCommonService.billingRuleCommons(billingOnline.getPlaceId(),
                billingOnline.getAreaId(), billingOnline.getCardTypeId());
        if (!optCommonBillingRule.isPresent()) {
            log.info("***计费规则未找到***-->areaId：" + billingOnline.getAreaId() + "cardTypeId---->>>>" + billingOnline.getCardTypeId());

            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            billingLockAlgorithm.releaseLock(cardIdKey);
            return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }

        // 查询登录信息
        Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, billingOnline.getCardId(), billingOnline.getBillingTime());
        if (!optLogLogin.isPresent()) {

            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            billingLockAlgorithm.releaseLock(cardIdKey);
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
        }
        LogLogin logLogin = optLogLogin.get();

        BillingRuleCommon execBillingRuleCommon = optCommonBillingRule.get(); // 默认获取并使用普通计费
        int unitConsume = execBillingRuleCommon.getUnitConsume(); // 获取单位扣费
        int minConsume = execBillingRuleCommon.getMinConsume();// 获取最低消费
        int deductionTime = execBillingRuleCommon.getDeductionTime(); // 获取扣费时间
        // 获取此时的费率价格
        int price = CommonRuleAlgorithm.getPrice(execBillingRuleCommon.getPrices());
        if (price < 0) {
            log.info("***计费规则限制上机***-->areaId：{},cardTypeId:{},price:{}", billingOnline.getAreaId(), billingOnline.getCardTypeId(), price);
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            billingLockAlgorithm.releaseLock(cardIdKey);
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_LIMIT_LOGIN);
        }
        if (deductionTime == 15 || deductionTime == 30 || deductionTime == 60) {
            // 目前只支持这三个
            double deductionHours = (double) deductionTime / 60;
            unitConsume = (int)(price * deductionHours); // 一次扣的钱
        }

        boolean needDeductMinimumSpending = isNeedDeductMinimumSpending(placeBizConfig, billingOnline, logLogin);

        BillingRulePackageTime oldPackageTimeBillingRule = null; // 用于包时转标准,包时转包时
        if (billingOnline.getPackageFlag() > 0) {
            // 如果当前是包时在线
            // 查询正在使用的包时规则
            Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService
                    .findByPlaceIdAndRuleId(billingOnline.getPlaceId(), billingOnline.getRuleId());
            if (billingRulePackageTimeOpt.isPresent()) {
                oldPackageTimeBillingRule = billingRulePackageTimeOpt.get();
            }
        }
        // 获取包时计费
        BillingRulePackageTime packageTimeBillingRule = getPackageTimeBillingRule(billingOnline);

        // 心跳时间没有到达next_time，不处理直接返回，如果packageTimeReserve!=null,则说明需要转包时
        if (this.packageTimeReserveTL.get() == null && nowTime.isBefore(billingOnline.getNextTime())) {
            bo.setIsMaster(this.isMasterTL.get());

            //查询总消费金额
            Integer sumDeduction = billingOnlineService.findSumDeductionByLoginId(bo.getLoginId());
            bo.setDeduction(sumDeduction);
            if(bo.getPackageFlag() > 0){
                //查询包时开始时间
                List<OperationType> operationTypes = new ArrayList<>();
                operationTypes.add(OperationType.CONVERT_BILLING_RULE);
                Optional<LogOperation> logOperationOptional = logOperationService.findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(bo.getPlaceId(), bo.getCardId(), operationTypes, bo.getBillingTime(), LocalDateTime.now());
                if(logOperationOptional.isPresent()){
                    bo.setPackageStartDate(logOperationOptional.get().getCreated());
                }else{
                    bo.setPackageStartDate(bo.getBillingTime());
                }
            }
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            billingLockAlgorithm.releaseLock(cardIdKey);
            return new GenericResponse<>(new ObjDTO<>(bo));
        }

        int onceDeduction = 0;
        BillingCard inviteCard = null;
        // 有包时费率信息，获取nextTime(请客上网被邀请人除外)
        if (packageTimeBillingRule != null && billingOnline.getIsInvite() != 2) {

            if (packageTimeBillingRule.getPackageFlag() == 2) {
                // 包时长不限制结束时间
                LocalDateTime futureNextTime = PackageTimeAlgorithm.getNextTime(LocalDateTime.now(), packageTimeBillingRule);
                LocalDateTime endTime = PackageTimeAlgorithm.getDateTime(packageTimeBillingRule);
                if (packageTimeBillingRule.getLimitEndTime() == 1) {
                    if (futureNextTime.isBefore(endTime)) {
                        nextTime = futureNextTime;
                    } else {
                        nextTime = this.packageTimeReserveTL.get().getEndTime();
                    }
                } else {
                    nextTime = futureNextTime;
                }
            } else {
                nextTime = this.packageTimeReserveTL.get().getEndTime();
            }

            // 扣费
            // onceDeduction = billingDeduction(billingCard, packageTimeBillingRule.getPrice(),logLogin.getLoginId());
            onceDeduction = packageTimeBillingRule.getPrice(); // 已经提前扣了
            this.costCash.set(onceDeduction);
            billingOnline.setRuleId(packageTimeBillingRule.getRuleId());
            billingOnline.setPackageFlag(packageTimeBillingRule.getPackageFlag());
            billingOnline.setCommonPrice(packageTimeBillingRule.getPrice());
            billingOnline.setAccFlag(0);
            billingOnline.setPackagePayFlag(this.packageTimeReserveTL.get().getPackagePayFlag());
        } else {
            // 普通计费
			// 包时结账，转标准计费
			if (billingOnline.getPackageFlag() > 0) {
				packageTimeEndTL.set(true);
				// 使用完的包时规则状态变为2
				cashierTempPackageTimeService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());

				// 如果场所配置了包时结束后结账，这里就调用结账(只对包时段有效)
                if (null != oldPackageTimeBillingRule && oldPackageTimeBillingRule.getRuleEndLogoutFlag() == 1 && billingOnline.getPackageFlag() > 0) {
					// 结账
					log.info(":::::设置包时段结束后结账:::触发成功，执行结账");
					return this.logout(logLogin, billingOnline, billingCard, logShiftService.getShiftId(placeId), logRoom,  billingKey, billingOnline.getCardId(), placeBizConfig.getClientQrCodeAuth(),placeBizConfig.getCashierQrCodeAuth(), inviteOnline == null ? "" : inviteOnline.getInviteCode());
				}
			}

            int oncePrice = unitConsume; // 本次扣费
            // 如果是首次扣费，同时上机时扣最低消费打开情况下，取扣费金额大的
            if (needDeductMinimumSpending && minConsume > unitConsume) {
                oncePrice = minConsume;
                log.info("oncePrice:{}", oncePrice);
            }

            // 继续执行普通费率，计算nextTime
            List<BillingCard> billingCards = null;
            int supportPresentSwitch = 0;
            if (billingOnline.getIsInvite() == 2 && inviteOnline != null) {
                inviteCard = inviteOnlineService.getInviteCard(placeId, inviteOnline.getInviteCode());
                billingCards = billingCardDeductionService.getChainBillingCard(inviteCard);
                Optional<InviteConfig> inviteConfigOpt = inviteConfigService.findByPlaceId(placeId);
                if (inviteConfigOpt.isPresent()) {
                    supportPresentSwitch = inviteConfigOpt.get().getSupportPresentSwitch();
                }
            } else {
                billingCards = billingCardDeductionService.getChainBillingCard(billingCard);
            }
            int totalAccount = 0;
            if (supportPresentSwitch == 1) {
                // 只扣本金
                totalAccount = billingCardDeductionService.sumAccount(billingCards, 0);
            } else {
                totalAccount = billingCardDeductionService.sumAccount(billingCards, 2);
            }
            // 账户余额不足单位扣费时，扣除账户剩余金额
            int balanceFlag = 0; // 余额是否足够扣完一次扣费的标识
            if (totalAccount > 0 && totalAccount < oncePrice) {
                oncePrice = totalAccount;
                balanceFlag= 1;
            }

            // 包时结账，转标准计费
            if (packageTimeBillingRule == null && billingOnline.getPackageFlag() > 0) {
                packageTimeEndTL.set(true);
                // 使用完的包时规则状态变为2
                cashierTempPackageTimeService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, billingOnline.getCardId(), billingOnline.getRuleId());
            }

            if (price == 0) {
                nextTime = billingOnline.getNextTime().plusMinutes(NEXT_TIME_PLUS);
            } else if ((deductionTime == 15 || deductionTime == 30 || deductionTime == 60) && balanceFlag == 0 && (!needDeductMinimumSpending || unitConsume > minConsume)) {
                nextTime = billingOnline.getNextTime().plusMinutes(deductionTime);
            } else {
                float extend = ((float) oncePrice / price) * 60;
                int minutes = (int) extend;
                float seconds = (extend - minutes) * 60;
                nextTime = billingOnline.getNextTime().plusMinutes(minutes).plusSeconds((int) seconds);
            }

            // 这段代码是解决累计包时多扣钱转换为时间的问题。
            if (billingOnline.getPackageFlag() == 0 && billingOnline.getAccFlag() == 1 && billingOnline.getIsInvite() != 2) {
                Optional<LogAcc> optLogAcc = logAccService.findByPlaceIdAndLoginId(placeId, billingOnline.getLoginId());
                LogAcc logAcc = optLogAcc.orElse(null);
                if (logAcc.getCurrPrice() + oncePrice >= logAcc.getAccPrice()) {
                    oncePrice = logAcc.getAccPrice() - logAcc.getCurrPrice();
                }
            }
            // 扣费
            if (billingOnline.getIsInvite() == 2 && inviteOnline != null && inviteCard != null) {
                onceDeduction = price == 0 ? 0 : billingDeduction(inviteCard, oncePrice, logLogin.getLoginId(), supportPresentSwitch == 1 ? 2 : placeBizConfig.getDeductionOrder());
            } else {
                onceDeduction = price == 0 ? 0 : billingDeduction(billingCard, oncePrice, logLogin.getLoginId(), placeBizConfig.getDeductionOrder());
            }
            billingOnline.setRuleId(execBillingRuleCommon.getRuleId());
            billingOnline.setPackageFlag(0);
            billingOnline.setCommonPrice(price);
        }

        if (onceDeduction < 0) { // 余额不足下机
            if (billingOnline.getIsInvite() > 0 && inviteOnline != null) {
                inviteOnline.setUpdated(LocalDateTime.now());
                inviteOnline.setStatus(2);
                inviteOnlineService.save(inviteOnline);
            }
            return this.logout(logLogin, billingOnline, billingCard, null, logRoom,  billingKey, billingOnline.getCardId(),
                    placeBizConfig.getClientQrCodeAuth(),placeBizConfig.getCashierQrCodeAuth(), inviteOnline == null ? "" : inviteOnline.getInviteCode());
        }

        // 处理累计包时业务,请客上网被邀请人跳过
        if (billingOnline.getIsInvite() != 2) {
            BillingRuleAcc billingRuleAcc = billingRuleAccService.findCurrBillingRuleAcc(placeId,
                    billingOnline.getCardTypeId(), billingOnline.getAreaId(), billingOnline.getLoginId()); // 获得当前生肖的累计包时规则
            log.info("billingRuleAcc::::为空???,accFlag::::::,{},{}",billingRuleAcc==null,billingOnline.getAccFlag());
            if (billingRuleAcc == null && billingOnline.getAccFlag() > 0) { // 累计包时结束，修改标识，不做任何处理
                billingOnline.setAccFlag(0);
            } else if (billingOnline.getPackageFlag() == 0 && billingOnline.getAccFlag() == 1) { // 正在累计中
                Optional<LogAcc> optLogAcc = logAccService.findByPlaceIdAndLoginId(placeId, billingOnline.getLoginId());
                LogAcc logAcc = optLogAcc.orElse(null);

                if (logAcc.getCurrPrice() + onceDeduction >= logAcc.getAccPrice()) { // 达到累计包时（扣费）标准
                    // 计算此时累计包时nextTime
                    LocalDateTime accNextTime = LocalDateTime.of(LocalDate.now(),
                            LocalTime.of((int) logAcc.getEndTime(), Math.round(logAcc.getEndTime() % 1 * 60)));
                    log.info("accNextTime:{}", accNextTime);
                    logAcc.setUpdated(nowTime);
                    logAcc.setFinishTime(nowTime);
                    logAcc.setCurrPrice(logAcc.getCurrPrice() + onceDeduction);
                    logAccService.save(logAcc);

                    if ((logAcc.getEndTime() < logAcc.getStartTime()) && accNextTime.isBefore(nowTime)) {
                        accNextTime = accNextTime.plusDays(1);
                    }
                    nextTime = accNextTime;

                    int hour = nextTime.getHour();
                    int minute = nextTime.getMinute();
                    int second = nextTime.getSecond();
                    double nowHourNum = hour + (double) minute / 60 + (double) second / 3600;
                    Random random = new Random();
                    int randomNumber = random.nextInt(241); // 生成0-240的随机数
                    if (nowHourNum == 7 || nowHourNum == 8 || nowHourNum == 9) {
                        nextTime = nextTime.plusSeconds(randomNumber); // 针对7、8、9点结束的包时，nextTime随机加120以内的秒数，打散请求并发.
                    }

                    billingOnline.setAccFlag(2);
                    logOperationService.addAccOperation(SourceType.CLIENT, billingCard, null, logAcc);
                } else { // 没达到扣费标准
                    logAcc.setUpdated(nowTime);
                    logAcc.setCurrPrice(logAcc.getCurrPrice() + onceDeduction);
                    logAccService.save(logAcc);
                }
            } else if (billingOnline.getPackageFlag() == 0 && billingRuleAcc != null && (billingOnline.getAccFlag() == 0 || billingOnline.getAccFlag() == 2)) { // 开始累计
                Optional<LogAcc> optLogAcc = logAccService.findByPlaceIdAndLoginId(placeId, billingOnline.getLoginId());
                if (optLogAcc.isPresent() && StringUtils.isEmpty(optLogAcc.get().getFinishTime())) {
                    LogAcc logAcc = optLogAcc.get();
                    logAcc.setUpdated(nowTime);
                    logAcc.setCurrPrice(logAcc.getCurrPrice() + onceDeduction);
                    logAccService.save(logAcc);
                    if (logAcc.getCurrPrice() >= logAcc.getAccPrice()) {
                        logAcc.setFinishTime(nowTime);
                        billingOnline.setAccFlag(2); // 设置online中的acc标识
                    } else {
                        billingOnline.setAccFlag(1); // 设置online中的acc标识
                    }
                } else {
                    LogAcc logAcc = new LogAcc();
                    logAcc.setAccPrice(billingRuleAcc.getAccPrice());
                    logAcc.setAccRuleId(billingRuleAcc.getRuleId());
                    logAcc.setCreated(nowTime);
                    logAcc.setCurrPrice(onceDeduction);
                    logAcc.setEndTime(billingRuleAcc.getEndTime());
                    logAcc.setLoginId(billingOnline.getLoginId());
                    logAcc.setPlaceId(placeId);
                    logAcc.setStartTime(billingRuleAcc.getStartTime());
                    logAccService.save(logAcc);
                    if (logAcc.getCurrPrice() >= logAcc.getAccPrice()) {
                        logAcc.setFinishTime(nowTime);
                        billingOnline.setAccFlag(2); // 设置online中的acc标识

                        LocalDateTime accNextTime = LocalDateTime.of(LocalDate.now(),
                                LocalTime.of((int) logAcc.getEndTime(), Math.round(logAcc.getEndTime() % 1 * 60)));
                        log.info("accNextTime:{}", accNextTime);
                        if ((logAcc.getEndTime() < logAcc.getStartTime()) && accNextTime.isBefore(nowTime)) {
                            accNextTime = accNextTime.plusDays(1);
                        }
                        nextTime = accNextTime;

                        int hour = nextTime.getHour();
                        int minute = nextTime.getMinute();
                        int second = nextTime.getSecond();
                        double nowHourNum = hour + (double) minute / 60 + (double) second / 3600;
                        Random random = new Random();
                        int randomNumber = random.nextInt(241); // 生成0-240的随机数
                        if (nowHourNum == 7 || nowHourNum == 8 || nowHourNum == 9) {
                            nextTime = nextTime.plusSeconds(randomNumber); // 针对7、8、9点结束的包时，nextTime随机加120以内的秒数，打散请求并发.
                        }

                        logOperationService.addAccOperation(SourceType.CLIENT, billingCard, null, logAcc);
                    } else {
                        billingOnline.setAccFlag(1); // 设置online中的acc标识
                    }
                }
            }
        }

        billingOnline.setUpdated(nowTime);
        billingOnline.setTimerFlag(0);
        billingOnlineService.save(billingOnline);

        // 设置下次上机时间
        billingOnlineService.updateOnlineNextTime(billingOnline.getId(), Duration.between(billingOnline.getNextTime(), nextTime).toNanos() / 1000, onceDeduction, this.costCash.get(), this.costPresent.get());

        // 如果是转包时，设置状态为已使用
        if (this.packageTimeReserveTL.get() != null) {
            PackageTimeReserve packageTimeReserve = this.packageTimeReserveTL.get();
            packageTimeReserve.setUpdated(nowTime);
            packageTimeReserve.setStatus(1);
            cashierTempPackageTimeService.save(packageTimeReserve);
        }

        if (billingOnline.getPackageFlag() > 0) {
            if (oldPackageTimeBillingRule != null) {
                // 包时转包时
                logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM, 3, 0,
                        0, billingCard, billingOnline, oldPackageTimeBillingRule, packageTimeBillingRule, null, logLogin);
            } else {
                // 标准转包时
                logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM, 1, CommonRuleAlgorithm.getPrice(execBillingRuleCommon.getPrices()),
                        0, billingCard, billingOnline, null, packageTimeBillingRule, null, logLogin);
            }
//			logOperationService.addBeginPackageTimeCardOperation(SourceType.SYSTEM, billingCard, billingOnline,
//					packageTimeBillingRule, null, logLogin); // 包时开始记录
        } else if (billingOnline.getPackageFlag() == 0 && this.packageTimeEndTL.get() && oldPackageTimeBillingRule != null) {
            logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM, 2, 0,
                    CommonRuleAlgorithm.getPrice(execBillingRuleCommon.getPrices()), billingCard, billingOnline, oldPackageTimeBillingRule, null, null, logLogin);
//			logOperationService.addEndPackageTimeCardOperation(SourceType.SYSTEM, billingCard, billingOnline,
//					execBillingRuleCommon, null, logLogin); // 包时结束记录
        } else {
            // 普通计费，不记录
        }

        Optional<BillingCard> newOptBillingCard = null;
        if(billingOnline.getCardTypeId().equals("1000") || billingOnline.getCardTypeId().equals("1002")){
            newOptBillingCard = billingCardService.findByPlaceIdAndCardId(placeId,billingOnline.getCardId());
        }else{
            newOptBillingCard =  billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId,billingOnline.getIdNumber(),0);
        }
        billingCard = newOptBillingCard.get();//获取最新的billingCard信息
        BillingOnlineBO billingOnlineBO = billingOnline.toBO();
        billingOnlineBO.setPresentAccount(billingCard.getPresentAccount());
        billingOnlineBO.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount()); //线上金额+现金金额
        billingOnlineBO.setTemporaryOnlineAccount(billingCard.getTemporaryOnlineAccount());
        billingOnlineBO.setPoints(billingCard.getPoints());
        billingOnlineBO.setNation(billingCard.getNation());
        billingOnlineBO.setPhoneNumber(billingCard.getPhoneNumber());
        billingOnlineBO.setCardTypeName(billingCard.getCardTypeName());
        billingOnlineBO.setRemark(billingCard.getRemark());
        billingOnlineBO.setActiveType(billingCard.getActiveType());
        billingOnlineBO.setActiveTypeCode(null != billingCard.getActiveType() ? billingCard.getActiveType().getValue() : null);
        billingOnlineBO.setValidPeriod(billingCard.getValidPeriod());
        billingOnlineBO.setIssuingAuthority(billingCard.getIssuingAuthority());
        billingOnlineBO.setAddress(billingCard.getAddress());
        billingOnlineBO.setActiveTime(billingCard.getActiveTime());
        billingOnlineBO.setIsMaster(this.isMasterTL.get());
        billingOnlineBO.setNextTime(nextTime);
        billingOnlineBO.setDeduction(billingOnlineBO.getDeduction() + onceDeduction + logLogin.getConsumptionTotal());
        billingOnlineBO.setDeductionCash(billingOnlineBO.getDeductionCash() + this.costCash.get());
        billingOnlineBO.setDeductionPresent(billingOnlineBO.getDeductionPresent() + this.costPresent.get());
        billingOnlineBO.setIdcardNum(billingCard.getIdcardNum());

        // 保存记录列表
        if (billingOnline.getIsInvite() > 0 && inviteOnline != null) {
            billingOnlineBO.setInviteCode(inviteOnline.getInviteCode());
        }
        if(billingOnlineBO.getPackageFlag() == 0){
            if (billingOnline.getIsInvite() == 2 && inviteCard!= null) {
                if(!"1000".equals(inviteCard.getCardTypeId()) && !"1002".equals(inviteCard.getCardTypeId())){
                   Optional<BillingCard> billingCardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, inviteCard.getIdNumber(), 0);
                   if (billingCardOpt.isPresent()) {
                       inviteCard = billingCardOpt.get();
                   }
                }
                balanceDetailsAlgorithm.saveBalanceDetails(nowTime,inviteCard,clientId,0,0,this.costCash.get(),this.costPresent.get(),
                        "被邀请人上机扣除",0,0, inviteOnline.getInviteCode());
            } else {
                String inviteCode = billingOnline.getIsInvite() == 1 ?inviteOnline.getInviteCode() : null;
                balanceDetailsAlgorithm.saveBalanceDetails(nowTime,billingCard,clientId,0,0,this.costCash.get(),this.costPresent.get(), "" +
                        "上机扣除",0,0, inviteCode);
            }
        }

        GenericResponse<?> response = new GenericResponse<>(new ObjDTO<>(billingOnlineBO));
        // 释放锁
        billingLockAlgorithm.releaseLock(billingKey);
        billingLockAlgorithm.releaseLock(cardIdKey);
        return response;

    }

    /**
     * 本次需要首先扣除最低消费
     *
     * 什么地方记录了一次上机行为，从上机到下机。LogLogin 表？是的，消费总额 consumptionTotal，在换机时，下机时会更新。
     * 什么地方记录了上机的费用使用情况。billingOnline 表？在一台机子上的一次上机行为，的消费情况。
     * 扣款记录详情记录在哪里？LogOperation 表？是的。
     *
     * @param placeBizConfig
     * @param billingOnline
     * @return
     */
    private boolean isNeedDeductMinimumSpending(PlaceBizConfig placeBizConfig, BillingOnline billingOnline, LogLogin logLogin) {
        int deductMinimumSpendingUponLogin = placeBizConfig.getDeductMinimumSpendingUponLogin(); // 是否上机时扣最低消费


        String curClientId = billingOnline.getClientId();
        boolean exchangeMachine = !StringUtils.isEmpty(logLogin.getLastClientId()) && !logLogin.getLastClientId().equals(curClientId);
        boolean firstDeduction = getFirstDeduction(billingOnline, exchangeMachine, logLogin.getConsumptionTotal());

        boolean needDeductMinimumSpending = deductMinimumSpendingUponLogin == 1 && firstDeduction;

        log.info("deductMinimumSpendingUponLogin:::{}, firstDeduction:::{},  本次是否需要首先扣除最低消费:::{}",
                deductMinimumSpendingUponLogin, firstDeduction, needDeductMinimumSpending);
        return needDeductMinimumSpending;
    }

    /**
     * 是否是首次扣费(计算上网时间的上网费用)
     *
     * @param billingOnline 当前上机在线情况对象
     * @param exchangeMachine 是否换机
     * @param consumptionTotalBeforeEx 换机前总消费
     * @return
     */
    private boolean getFirstDeduction(BillingOnline billingOnline, boolean exchangeMachine, int consumptionTotalBeforeEx) {
        // 如果没换机，则用 billingOnline.deduction 对象就能计算出是否是首次扣费；
        // 如果换机了，则 logLogin.consumptionTotal 记录了换机前的总消费，当前消费则是 logLogin.consumptionTotal + billingOnline.deduction

        boolean firstDeduction; // 是否是首次扣款
        int curDeduction;// 累计扣款总额
        if (exchangeMachine) {
            curDeduction = consumptionTotalBeforeEx + billingOnline.getDeduction();
        } else {
            curDeduction = billingOnline.getDeduction();
        }

        // 查询是否有附加费
        String loginId = billingOnline.getLoginId();
        List<LogOperation> logOperations = logOperationService.findByLoginIdAndOperationType(loginId, OperationType.SURCHARGE);

        if (CollectionUtils.isEmpty(logOperations)) {
            // 没有附加费
            firstDeduction = curDeduction == 0;
        } else {
            // 有附加费
            int cost = logOperations.stream().map(LogOperation::getCost).mapToInt(Math::abs).sum();
            firstDeduction = curDeduction == Math.abs(cost);
        }
        return firstDeduction;
    }

    /**
     * 结账下机
     *
     * @param logLogin
     * @param billingOnline
     * @param billingCard
     * @param logShift
     * @return
     */
    private GenericResponse<?> logout(LogLogin logLogin, BillingOnline billingOnline, BillingCard billingCard,
                                      LogShift logShift, LogRoom logRoom, String billingKey, String cardIdKey,
                                      String clientQrCodeAuth,String cashierQrCodeAuth, String inviteCode) {

        // 结束Online
        billingOnline.setFinished(1);
        billingOnline.setUpdated(LocalDateTime.now());
        // 更新心跳记录表和登录登出表
        logHbService.stopBilling(billingOnline.getPlaceId(), billingOnline.getClientId()); // 更新心跳表

        // 更新登录日志
        logLogin = logLoginService.doLogout(billingOnline.getPlaceId(), billingOnline.getClientId(),
                billingCard.getCardId(), "系统结账", billingOnline.getDeduction(),
                billingOnline.getDeductionCash(),billingOnline.getDeductionPresent(),
                logLogin.getLoginTime(), billingCard.getTotalAccount()); // 更新登录登出表

        // 记录上机记录相关的消费记录
        logOperationService.addLogoutLogOperation(SourceType.SYSTEM, billingCard, logShift, logLogin);

        // 查询预包时记录
        Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findUnusedByPlaceIdAndCardId(billingOnline.getPlaceId(), billingOnline.getCardId());

        // 更新BillingCard
//		billingCard.setUpdated(LocalDateTime.now());
//		billingCard.setActiveTime(null);
        int deleted = 0;
        if ("1000".equals(billingCard.getCardTypeId())) {// 处理临时卡下机
            if (!packageTimeReserveOpt.isPresent() && billingCard.getTotalAccount() <= 0) {
                // 临时卡没有预包时记录,销卡
                billingOnline.setDeleted(1);
                deleted = 1;
                logOperationService.addCancellationLogOperation(SourceType.SYSTEM, billingCard, logShift);
            }
        }
        billingCardService.billingCardLogout(billingCard.getPlaceId(), billingCard.getCardId(), deleted);
        //  billingOnlineService.save(billingOnline);
        // 系统结账后赠送积分
//        billingCardService.updateRewardPoints(billingCard, billingOnline.getDeduction(), logShift, 0,SourceType.SYSTEM);

        // 清理预包时记录
//		packageTimeReserveService.updateInvalidationByPlaceIdAndCardId(billingOnline.getPlaceId(),
//				billingCard.getCardId());

        if(!billingOnline.getCardTypeId().equals("1000") && !billingOnline.getCardTypeId().equals("1002")){
            Optional<BillingCard> optBillingCard =  billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(billingOnline.getPlaceId(),billingOnline.getIdNumber(),0);
            billingCard = optBillingCard.get();//获取最新的billingCard信息
        }
        BillingOnlineBO billingOnlineBO = billingOnline.toBO();
        billingOnlineBO.setPresentAccount(billingCard.getPresentAccount());
        billingOnlineBO.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount()); //线上金额+现金金额
        billingOnlineBO.setTemporaryOnlineAccount(billingCard.getTemporaryOnlineAccount());
        billingOnlineBO.setPoints(billingCard.getPoints());
        billingOnlineBO.setNation(billingCard.getNation());
        billingOnlineBO.setPhoneNumber(billingCard.getPhoneNumber());
        billingOnlineBO.setCardTypeName(billingCard.getCardTypeName());
        billingOnlineBO.setRemark(billingCard.getRemark());
        billingOnlineBO.setActiveType(billingCard.getActiveType());
        billingOnlineBO.setActiveTypeCode(null != billingCard.getActiveType() ? billingCard.getActiveType().getValue() : null);
        billingOnlineBO.setValidPeriod(billingCard.getValidPeriod());
        billingOnlineBO.setIssuingAuthority(billingCard.getIssuingAuthority());
        billingOnlineBO.setAddress(billingCard.getAddress());
        billingOnlineBO.setIdcardNum(billingCard.getIdcardNum());
        billingOnlineBO.setInviteCode(inviteCode);
        // 更新包间记录
        if (logRoom != null) {
            logRoom.setFinishedTime(LocalDateTime.now());
            logRoom.setFinished(1);
            logRoomService.save(logRoom);
            billingOnlineBO.setIsMaster(logRoom.getIsMaster());
        }

        // 清除在线信息
        billingOnlineService.deleteByPlaceIdAndLoginId(billingCard.getPlaceId(), logLogin.getLoginId());

        //发送微信公众号模板通知
        wechatMessageApi.sendComputerCheckOut(clientQrCodeAuth,cashierQrCodeAuth,billingOnline.getIdNumber(), DateTimeUtils.getTimeFormat(logLogin.getLoginTime()),
                DateTimeUtils.getTimeFormat(logLogin.getLogoutTime()),
//                billingOnline.getPackageFlag() == 0 ? (billingOnline.getDeduction() + extraDeduction): billingOnline.getDeduction(), //是否包时
                logLogin.getConsumptionTotal(),
                billingCard.getTotalAccount());

        //查询总消费金额
        Integer sumDeduction = billingOnlineService.findSumDeductionByLoginId(billingOnlineBO.getLoginId());
        billingOnlineBO.setDeduction(sumDeduction);
        if(billingOnlineBO.getPackageFlag() > 0){
            //查询包时开始时间
            List<OperationType> operationTypes = new ArrayList<>();
            operationTypes.add(OperationType.CONVERT_BILLING_RULE);
            Optional<LogOperation> logOperationOptional = logOperationService.findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(billingOnlineBO.getPlaceId(), billingOnlineBO.getCardId(), operationTypes, billingOnlineBO.getBillingTime(), LocalDateTime.now());
            if(logOperationOptional.isPresent()){
                billingOnlineBO.setPackageStartDate(logOperationOptional.get().getCreated());
            }else{
                billingOnlineBO.setPackageStartDate(billingOnlineBO.getBillingTime());
            }
        }
        placementService.refreshPlacementDate(billingOnline.getPlaceId(),billingOnline.getClientId(),"2",billingOnline.getIdName(),billingOnline.getIdNumber(),billingOnline.getCardId());
        // 释放锁
        billingLockAlgorithm.releaseLock(billingKey);
        billingLockAlgorithm.releaseLock(cardIdKey);
        return new GenericResponse<>(new ObjDTO<>(billingOnlineBO));
    }

    /**
     * 对BillingCard进行登录扣费
     *
     * @param billingCard
     * @param oncePrice   一次扣费金额
     * @param loginId     登入id
     * @param deductionOrder   扣费顺序 0:优先本金  1:优先奖励
     * @return
     */
    private int billingDeduction(BillingCard billingCard, int oncePrice, String loginId, int deductionOrder) {

        // 获取扣费信息
        List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, oncePrice, loginId, 0, deductionOrder);
        if (costDetails == null) {
            return -1; // 余额不足
        }

        // 扣费
        BillingCard card = billingCardService.billingCardDeduction(costDetails, billingCard.getPlaceId());
        if (card == null) {
            return -1; // 余额不足
        }
        if (!StringUtils.isEmpty(costDetails)) {
            this.costCash.set(costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum));
            this.costPresent.set(costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum));
        }
        return oncePrice;
    }

    /**
     * 在计费过程中检查后是否需要使用新的计费规则
     *
     * @param billingOnline 当前计费信息
     */
    private BillingRulePackageTime getPackageTimeBillingRule(BillingOnline billingOnline) {
        Optional<PackageTimeReserve> cashierTempPackageTimeOpt = cashierTempPackageTimeService
                .findUnusedByPlaceIdAndCardId(billingOnline.getPlaceId(), billingOnline.getCardId());
        if (cashierTempPackageTimeOpt.isPresent()) {
            PackageTimeReserve cashierTempPackageTime = cashierTempPackageTimeOpt.get();
            if (cashierTempPackageTime.getAreaIds().contains(billingOnline.getAreaId())) {
                Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService
                        .findByPlaceIdAndRuleId(billingOnline.getPlaceId(), cashierTempPackageTime.getRuleId());
                if (billingRulePackageTimeOpt.isPresent()) {
                    if (LocalDateTime.now().isAfter(cashierTempPackageTime.getStartTime())
                            && LocalDateTime.now().isBefore(cashierTempPackageTime.getEndTime())) {
                        this.packageTimeReserveTL.set(cashierTempPackageTime);
                        return billingRulePackageTimeOpt.get();
                    }
                }
            }
        } else {
            this.packageTimeReserveTL.set(null);
        }
        return null;
    }
}
