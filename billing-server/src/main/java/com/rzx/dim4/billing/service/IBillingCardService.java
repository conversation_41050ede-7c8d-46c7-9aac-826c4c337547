package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.CancellationBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.entity.third.ThirdAccount;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/17
 **/
public interface IBillingCardService {

    /**
     * 收银台开卡（带钱）
     *
     * @param placeId          门店id
     * @param shiftId          班次id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param phoneNumber      手机号
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeType       激活类型
     * @param identification   证件类型
     * @param remark           备注
     * @param cashAmount       现金金额
     * @param presentAmount    赠送金额
     * @param amountFlag       是否带钱开卡
     * @return BillingCardBO
     */
    BillingCardBO cashierCreateCard(String placeId,
                                    String shiftId,
                                    String cardTypeId,
                                    String idNumber,
                                    String name,
                                    String phoneNumber,
                                    String address,
                                    String issuingAuthority,
                                    String nation,
                                    String validPeriod,
                                    String activeType,
                                    String identification,
                                    String remark,
                                    int cashAmount,
                                    int presentAmount,
                                    boolean amountFlag,
                                    int presentAmortized);

    /**
     * 第三方开卡
     *
     * @param placeId          门店id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param identification   证件类型
     * @param cashierId        收银台id
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param phoneNumber      手机号
     * @param validPeriod      有效期
     * @param thirdAccount     第三方账户
     * @return BillingCardBO
     */
    BillingCardBO thirdCreateCard(String placeId,
                                  String cardTypeId,
                                  String idNumber,
                                  String name,
                                  String identification,
                                  String cashierId,
                                  String address,
                                  String issuingAuthority,
                                  String nation,
                                  String phoneNumber,
                                  String validPeriod,
                                  ThirdAccount thirdAccount,
                                  LocalDateTime activeTime);


    BillingCardBO autoCreateCardWhenTopup(LogTopup logTopup, BillingCardBO billingCardBO, String identification);

    /**
     * 只能用来获取会员卡信息，不能用来更新会员卡信息！！！！！！！
     * 因为假如场所加入了连锁，则此处获得的卡信息，比如金额是聚合所有连锁场所的，而不是单个场所，如果保存至单个场所则金额错误
     *
     * @param placeId  场所id
     * @param idNumber 会员卡号
     * @return 会员卡信息
     * @apiNote 假如用户在当前场所有会员卡，且为临时卡/工作卡，则获取临时卡/工作卡；
     * 假如没有，则判断是否加入连锁，如果加入了连锁，则
     */
    BillingCardBO getOnlyReadOverSystem(String placeId, String idNumber);

    /**
     * 只能用来获取会员卡信息，不能用来更新会员卡信息！！！！！！！
     * 因为假如场所加入了连锁，则此处获得的卡信息，比如金额是聚合所有连锁场所的，而不是单个场所，如果保存至单个场所则金额错误
     *
     * @param placeId  场所id
     * @param idNumber 会员卡号
     * @return 会员卡信息
     * @apiNote 假如用户在当前场所有会员卡，且为临时卡/工作卡，则获取临时卡/工作卡；
     * 假如没有，则判断是否加入连锁，如果加入了连锁，则
     */
    BillingCardBO getOnlyReadOverSystem(String placeId, String idNumber, String idName);

    Optional<BillingCard> findByPlaceIdAndIdNumberAndNotDeleted(String placeId, String idNumber);

    BillingCard billingCardDeduction(List<PlaceChainBillingCardCostDetail> costDetails, String currPlaceId);

    /**
     * 收银台开临时卡（带包时）
     *
     * @param placeId          门店id
     * @param shiftId          班次id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param phoneNumber      手机号
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeType       激活类型
     * @param identification   证件类型
     * @param remark           备注
     * @param cashAmount       现金金额
     * @param presentAmount    赠送金额
     * @param amountFlag       是否带钱开卡
     * @param packageRuleId    包时规则id
     * @param packageType      包时类型 1:余额包时 2:现金包时，当前为固定值1
     * @return
     */
    BillingCardBO cashierCreateTempCardWithPackageTime(String placeId,
                                                       String shiftId,
                                                       String cardTypeId,
                                                       String idNumber,
                                                       String name,
                                                       String phoneNumber,
                                                       String address,
                                                       String issuingAuthority,
                                                       String nation,
                                                       String validPeriod,
                                                       String activeType,
                                                       String identification,
                                                       String remark,
                                                       int cashAmount,
                                                       int presentAmount,
                                                       boolean amountFlag,
                                                       String packageRuleId,
                                                       int packageType);

    /**
     * 注销临时卡
     *
     * @param placeId
     * @param cardId
     * @param shiftId
     * @param refundType
     * @param sourceType
     * @return
     * @apiNote doCancellationTempBillingCard 两个方法类似，就入参不一样，一个传 cardId，一个传 billingCard
     */
    CancellationBO doCancellationTempBillingCard(String placeId, String cardId, String shiftId, String refundType, SourceType sourceType);

    /**
     * 注销临时卡
     *
     * @param shiftId
     * @param refundType
     * @param sourceType
     * @param billingCard
     * @return
     * @apiNote doCancellationTempBillingCard 两个方法类似，就入参不一样，一个传 cardId，一个传 billingCard
     */
    CancellationBO doCancellationTempBillingCard(String shiftId, String refundType, SourceType sourceType, BillingCard billingCard);

    /**
     * 查询当前场所下面的指定会员卡
     *
     * @param placeId   场所 id
     * @param idNumbers 身份证号列表
     * @return 会员卡列表
     */
    List<BillingCard> findByPlaceIdAndIdNumbers(String placeId, List<String> idNumbers);

    /**
     * 删除会员卡
     *
     * @param placeId
     * @param idNumber
     * @param shiftId
     * @param remark
     * @param sourceType
     */
    void doDeleteMembershipCard(String placeId, String idNumber, String shiftId, String remark, SourceType sourceType);

    void saveAll(List<BillingCard> billingCards);
}
