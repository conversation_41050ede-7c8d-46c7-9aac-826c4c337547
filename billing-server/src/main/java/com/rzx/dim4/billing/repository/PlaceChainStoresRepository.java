package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.PlaceChainStores;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023年04月28日 下午02:38:21
 */
public interface PlaceChainStoresRepository extends JpaRepository<PlaceChainStores, Long>, JpaSpecificationExecutor<PlaceChainStores> {

    Optional<PlaceChainStores> findByPlaceId(String placeId);

    Optional<PlaceChainStores> findByPlaceIdAndDeleted(String placeId, int deleted);

    List<PlaceChainStores> findByChainIdAndDeleted(String chainId, int deleted);

    PlaceChainStores findByChainIdAndPlaceIdAndDeleted(String chainId, String placeId, int deleted);

    List<PlaceChainStores> findByDeleted (int deleted);

    @Transactional
    @Modifying
    @Query(value = "update place_chain_stores set share_member_point = 1, updated = now() where chain_id = :chainId and deleted = 0", nativeQuery = true)
    int openChainAllPlaceShareMemberPoint(@Param("chainId") String chainId); // 查询包时
}
