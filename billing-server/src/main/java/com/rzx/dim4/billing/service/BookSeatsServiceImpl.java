package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.bo.notify.polling.BookSeatsBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.place.PlaceAccountBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.FinishType;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.notify.BusinessDataApi;
import com.rzx.dim4.base.service.feign.place.PlaceAccountApi;
import com.rzx.dim4.base.service.feign.place.PlaceClientApi;
import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.repository.BillingRuleCommonRepository;
import com.rzx.dim4.billing.repository.BookSeatsRepository;
import com.rzx.dim4.billing.repository.PlaceBizConfigRepository;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzx.dim4.base.enums.billing.FinishType.EXPIRED;
import static com.rzx.dim4.base.enums.billing.SourceType.SYSTEM;
import static com.rzx.dim4.base.enums.billing.SourceType.WECHAT;
import static com.rzx.dim4.billing.cons.BillingConstants.BILLING_SIMULTANEOUSLY_LOCK;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/7
 **/
@Slf4j
@Service
public class BookSeatsServiceImpl implements BookSeatsService {

    @Autowired
    private BookSeatsRepository bookSeatsRepository;

    @Autowired
    private PlaceBizConfigRepository placeBizConfigRepository;

    @Autowired
    private PlaceClientApi placeClientApi;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BillingRuleCommonRepository billingRuleCommonRepository;

    @Autowired
    private IBillingCardService billingCardService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private BusinessDataApi businessDataApi;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private PlaceAccountApi placeAccountApi;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    private WechatMessageApi wechatMessageApi;

    @Autowired
    private BalanceDetailsAlgorithm balanceDetailsAlgorithm;

    @Autowired
    private PlacementService placementService;

    @Autowired
    private BillingCardService cardService;

    private final int unitTimes = 15; // 扣钱时间单位，15分钟扣一次

    /**
     * 获取订座配置
     *
     * @param placeId 场所ID
     * @return 订座配置
     */
    @Override
    public BookSeatsConfigBO getConfig(String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);

        return getBookSeatsConfig(placeBizConfig);
    }

    /**
     * 二级开关开了才是真开了；
     * 二级开关只有在一级开关开了情况下开了才是正常，不然就是不正常
     *
     * @param placeId    场所ID
     * @param sourceType 订座来源
     */
    @Override
    public void checkBookingIsOpening(String placeId, SourceType sourceType) {
        if (StringUtils.isEmpty(placeId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
        Optional<PlaceBizConfig> placeBizConfigOptional = placeBizConfigRepository.findByPlaceId(placeId);
        PlaceBizConfig placeBizConfig = placeBizConfigOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BAD_PARAM));

        // 二级开关
        int bookSeatsFlagV8 = placeBizConfig.getBookSeatsFlagV8();
        int bookSeatsFlagThird = placeBizConfig.getBookSeatsFlagThird();
        String thirdAccountId = placeBizConfig.getThirdAccountId();

        // 一级开关
        boolean placeOpenBookSeatsFlag = placeBizConfig.getBookSeatsFlag() == 1;
        if (!placeOpenBookSeatsFlag) {
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_IS_NOT_OPEN);
        }

        // 一级开了，没有绑定第三方，V8使用一级开关的打开，可以订座
        if (StringUtils.isEmpty(thirdAccountId)) {
            // do nothing
        } else {
            // 一级开了，有绑定第三方
            // 是否是第三方来源
            boolean thirdSourceTypeFlag = SourceType.isThirdSourceType(sourceType);
            if (thirdSourceTypeFlag) {
                // 第三方是否打开
                if (bookSeatsFlagThird == 0) {
                    throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_IS_NOT_OPEN);
                }
            } else {
                // 有绑定第三方，则 V8开启，需判断二级开关
                if (bookSeatsFlagV8 == 0) {
                    throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_IS_NOT_OPEN);
                }
            }
        }
    }


    /**
     * 组装 BookSeatsConfigBO
     *
     * @param placeBizConfig 场所配置
     * @return BookSeatsConfigBO
     */
    private BookSeatsConfigBO getBookSeatsConfig(PlaceBizConfig placeBizConfig) {
        BookSeatsConfigBO bookSeatsConfigBO = new BookSeatsConfigBO();

        bookSeatsConfigBO.setPlaceId(placeBizConfig.getPlaceId());
        bookSeatsConfigBO.setBookSeatsFlag(placeBizConfig.getBookSeatsFlag());
        bookSeatsConfigBO.setBookSeatsFlagV8(placeBizConfig.getBookSeatsFlagV8());
        bookSeatsConfigBO.setBookSeatsFlagThird(placeBizConfig.getBookSeatsFlagThird());
        bookSeatsConfigBO.setBookSeatsFreeTime(placeBizConfig.getBookSeatsFreeTime());
        // 如果场所配置第三方账号字段有值，则为第三方场所
        bookSeatsConfigBO.setPlaceInThirdParty(StringUtils.isEmpty(placeBizConfig.getThirdAccountId()) ? 0 : 1);
        // 组装客户端列表
        List<PlaceClientBO> placeClientBOS = getFullInfoPlaceClientBOS(placeBizConfig.getPlaceId(), null);
        List<BookSeatsClientBO> bookSeatsClientBOS = placeClientBOS.stream().map(t -> {
                    BookSeatsClientBO bookSeatsClientBO = new BookSeatsClientBO();
                    BeanUtils.copyProperties(t, bookSeatsClientBO);
                    return bookSeatsClientBO;
                }).sorted(Comparator.comparing(BookSeatsClientBO::getHostName))
                .sorted(Comparator.comparing(BookSeatsClientBO::getAreaId))
                .collect(Collectors.toList());

        String cannotBookClientIds = placeBizConfig.getCannotBookClientIds();
        if (!StringUtils.isEmpty(cannotBookClientIds)) {
            String[] clientIdArr = cannotBookClientIds.split(",");
            List<String> clientIds = Arrays.asList(clientIdArr);
            bookSeatsConfigBO.setCannotBookClientIds(clientIds);

            // 设置不可用状态
            bookSeatsClientBOS.forEach(t -> {
                if (clientIds.contains(t.getClientId())) {
                    t.setStatus(2);
                }
            });
        }
        bookSeatsConfigBO.setBookSeatsClientBOs(bookSeatsClientBOS);
        return bookSeatsConfigBO;
    }

    /**
     * 客户端列表（补充了 areaName/isRoom）
     *
     * @param placeId   场所id
     * @param clientIds 客户端id（可选）
     * @return 客户端列表
     */
    private List<PlaceClientBO> getFullInfoPlaceClientBOS(String placeId, List<String> clientIds) {
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<ListDTO<PlaceClientBO>> response = placeClientApi.getFullInfo(requestTicket, placeId, clientIds);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }

        return response.getData().getList();
    }

    /**
     * 更新订座配置
     *
     * @param configBO 订座配置
     */
    @Override
    public void updateConfig(BookSeatsConfigBO configBO) {
        if (StringUtils.isEmpty(configBO.getPlaceId())) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<PlaceBizConfig> placeBizConfigOptional = placeBizConfigRepository.findByPlaceId(configBO.getPlaceId());
        PlaceBizConfig placeBizConfig = placeBizConfigOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BAD_PARAM));
        log.info("the old placeBizConfig : {}", new Gson().toJson(placeBizConfig));

        placeBizConfig.setBookSeatsFlag(configBO.getBookSeatsFlag());
        // 订座配置一级开关关闭时，二级都关闭
        if (configBO.getBookSeatsFlag() == 0) {
            placeBizConfig.setBookSeatsFlagV8(0);
            placeBizConfig.setBookSeatsFlagThird(0);
        } else {
            placeBizConfig.setBookSeatsFlagV8(configBO.getBookSeatsFlagV8());
            placeBizConfig.setBookSeatsFlagThird(configBO.getBookSeatsFlagThird());
        }
        // 订座配置二级只要开了一个，一级开关就开启
        if (configBO.getBookSeatsFlagV8() != 0 || configBO.getBookSeatsFlagThird() != 0) {
            placeBizConfig.setBookSeatsFlag(1);
        }
        placeBizConfig.setBookSeatsFreeTime(configBO.getBookSeatsFreeTime());
        placeBizConfig.setCannotBookClientIds(StringUtils.isEmpty(configBO.getCannotBookClientIds()) ? null : String.join(",", configBO.getCannotBookClientIds()));

        placeBizConfig.setUpdated(LocalDateTime.now());
        PlaceBizConfig save = placeBizConfigRepository.save(placeBizConfig);
        log.info("the new placeBizConfig : {}", new Gson().toJson(save));
    }

    /**
     * 获取订座列表（带筛选）
     *
     * @param queryBO 订座列表筛选条件（placdId 必填）
     * @return 订座列表
     */
    public Page<BookSeatsBO> getList(BookSeatsQueryBO queryBO) {
        Pageable pageable = PageRequest.of(queryBO.getPage(),
                queryBO.getPageSize() == 0 ? 10 : queryBO.getPageSize(),
                Sort.Direction.fromString("desc"),
                "id");

        Page<BookSeats> bookSeatsPage = this.findAll(queryBO, pageable);

        List<BookSeats> bookSeatsList = bookSeatsPage.getContent();
        List<BookSeatsBO> bookSeatsBOList = bookSeatsList.stream().map(t -> {
            BookSeatsBO bookSeatsBO = t.toBO();
            // 塞客户端列表详情
            String clientIdsStr = t.getClientIds();
            String[] clientIdArr = clientIdsStr.split(",");
            List<String> clients = Arrays.asList(clientIdArr);
            List<PlaceClientBO> placeClientBOS = getFullInfoPlaceClientBOS(t.getPlaceId(), clients);
            bookSeatsBO.setClientList(placeClientBOS);
            return bookSeatsBO;
        }).collect(Collectors.toList());

        return new PageImpl<>(bookSeatsBOList, pageable, bookSeatsPage.getTotalElements());
    }

    @Override
    public Page<BookSeatsBO> getListByPlaceId(BookSeatsQueryBO queryBO) {
        // placeId
        if (StringUtils.isEmpty(queryBO.getPlaceId())) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        return this.getList(queryBO);
    }

    @Override
    @Transactional
    public BookSeatsBO cashierCancel(String placeId, long bookSeatsId, String shiftId) {
        LogShift logShift = getLogShift(placeId, shiftId);
        Long accountBOId = getAccountBOId(placeId, logShift);

        BookSeats bookSeats = this.cancel(bookSeatsId, accountBOId, FinishType.CASHIER_CANCEL);

        saveOperationLog(bookSeats, SourceType.CASHIER, logShift);
        return bookSeats.toBO();
    }

    private LogShift getLogShift(String placeId, String shiftId) {
        // 查询班次信息
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        return optLogShift.get();
    }

    private Long getAccountBOId(String placeId, LogShift logShift) {
        String accountId = logShift.getAccountId();
        int accountType = logShift.getAccountType();
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        GenericResponse<ObjDTO<PlaceAccountBO>> response = placeAccountApi.getCashier(requestTicket, placeId, accountType, accountId);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.info("placeAccountApi.get error: {}", response);
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
        PlaceAccountBO placeAccountBO = response.getData().getObj();
        return placeAccountBO.getId();
    }

    private Page<BookSeats> findAll(BookSeatsQueryBO queryBO, Pageable pageable) {
        return bookSeatsRepository.findAll((Specification<BookSeats>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            // placeId，
            if (!StringUtils.isEmpty(queryBO.getPlaceId())) {
                list.add(criteriaBuilder.equal(root.get("placeId"), queryBO.getPlaceId()));
            }

            switch (queryBO.getQueryStatus()) {
                case 0:
                    break;
                case 1:
                    list.add(criteriaBuilder.equal(root.get("status"), 0));
                    break;
                case 2:
                    list.add(criteriaBuilder.equal(root.get("status"), 1));
                    break;
            }

            if (Objects.nonNull(queryBO.getStartTime())) {
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("startTime"), queryBO.getStartTime()));
            }
            if (Objects.nonNull(queryBO.getQueryStartTimeRight())) {
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("startTime"), queryBO.getQueryStartTimeRight()));
            }

            if (!StringUtils.isEmpty(queryBO.getIdName())) {
                list.add(criteriaBuilder.like(root.get("idName"), "%" + queryBO.getIdName() + "%"));
            }

            if (!StringUtils.isEmpty(queryBO.getIdNumber())) {
                list.add(criteriaBuilder.equal(root.get("idNumber"), queryBO.getIdNumber()));
            }

            if (!StringUtils.isEmpty(queryBO.getIdcardNum())) {
                list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("idcardNum"), "%" + queryBO.getIdcardNum() + "%"), criteriaBuilder.like(root.get("idNumber"), "%" + queryBO.getIdcardNum() + "%")));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        }, pageable);
    }

    /**
     * 取消订座
     *
     * @param bookSeatsId 订座记录id
     * @param operator    操作人id(billingCard.id/placeAccount.id)
     * @param finishType  结束类型(FinishType)
     * @see FinishType
     */
    public BookSeats cancel(long bookSeatsId, long operator, FinishType finishType) {
        if (0 == bookSeatsId || 0 == operator) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        BookSeats bookSeats = bookSeatsRepository.findById(bookSeatsId)
                .orElseThrow(() -> new ServiceException(ServiceCodes.BAD_PARAM));
        if (bookSeats.getStatus() == 0) {
            LocalDateTime now = LocalDateTime.now();
            bookSeats.setStatus(1);
            bookSeats.setUpdater(operator);
            bookSeats.setFinishType(finishType);
            bookSeats.setActualEndTime(now);
            bookSeats.setUpdated(now);

            placementService.refreshPlacementDate(bookSeats.getPlaceId(),bookSeats.getClientIds(),"5",bookSeats.getIdName(),bookSeats.getIdNumber(),bookSeats.getCardId());

            return bookSeatsRepository.save(bookSeats);
        } else {
            log.info("订座记录早就已经被取消");
            throw new ServiceException(ServiceCodes.OPT_REPEAT);
        }
    }

    /**
     * 客户端查询是否被预定
     *
     * @param placeId  场所id
     * @param clientId 客户端id
     * @return 订座信息
     */
    @Override
    public BookSeatsBO find(String placeId, String clientId) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(clientId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        List<BookSeats> bookSeatsList = bookSeatsRepository.findByPlaceIdAndDeletedAndStatus(placeId, 0, 0);
        if (CollectionUtils.isEmpty(bookSeatsList)) {
            return null;
        }

        for (BookSeats bookSeats : bookSeatsList) {
            if (bookSeats.getClientIds().contains(clientId)) {
                return bookSeats.toBO();
            }
        }
        return null;
    }

    @Override
    public void noBookingSeats(String placeId, String idNumber) {
        log.info("noBookingSeats, placeId:{}, idNumber:{}", placeId, idNumber);

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        List<BookSeats> bookSeatsList = bookSeatsRepository.findAllByPlaceIdAndStatusAndIdNumber(placeId, 0, idNumber);
        if (CollectionUtils.isEmpty(bookSeatsList)) {
            return;
        }

        if (bookSeatsList.size() > 1) {
            log.warn("数据异常，多条订座中数据，placeId:{}, idNumber:{}, bookSeatsList:{}", placeId, idNumber, new Gson().toJson(bookSeatsList));
            throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
        }

        log.info("有一条正在订座的记录，placeId:{}, idNumber:{}", placeId, idNumber);
        throw new ServiceException(ServiceCodes.BILLING_CUSTOMER_ON_BOOKING);
    }

    @Override
    public boolean clientUnderBooking(String placeId, String clientId) {
        BookSeatsBO bookSeatsBO = this.find(placeId, clientId);
        return bookSeatsBO != null;
    }

    /**
     * 用户待订座页面信息
     *
     * @param placeId  场所id
     * @param idNumber 身份证号
     * @return 待订座页面信息
     */
    @Override
    public BookSeatsCustomerContextBO customerContext(String placeId, String idNumber) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 获取会员卡信息
        BillingCardBO billingCardBOOnlyReadOverSystem = billingCardService.getOnlyReadOverSystem(placeId, idNumber);
        String cardTypeId = billingCardBOOnlyReadOverSystem.getCardTypeId();
        int account = billingCardBOOnlyReadOverSystem.getCashAccount() + billingCardBOOnlyReadOverSystem.getPresentAccount();

        BookSeatsConfigBO bookSeatsConfigBO = getBookSeatsConfigBO(placeId, cardTypeId);

        updateConfigForClientStatusIfUsing(bookSeatsConfigBO);

        return BookSeatsCustomerContextBO.init(bookSeatsConfigBO, account);
    }

    /**
     * 更新当前已经在订座中的客户端
     *
     * @param bookSeatsConfigBO 订座配置
     * @apiNote 注意此处 bookSeatsConfigBO.getBookSeatsClientBOs() 的引用不能改变，不然最新状态无法从本方法传出去
     */
    private void updateConfigForClientStatusIfUsing(BookSeatsConfigBO bookSeatsConfigBO) {
        String placeId = bookSeatsConfigBO.getPlaceId();
        List<BookSeatsClientBO> bookSeatsClientBOs = bookSeatsConfigBO.getBookSeatsClientBOs();
        // 正在订座中的客户端
        List<BookSeats> bookSeatsList = bookSeatsRepository.findByPlaceIdAndDeletedAndStatus(placeId, 0, 0);

        // 正在上机的客户端
        List<BillingOnline> billingOnlineList = billingOnlineService.findUnfinishedByPlaceId(placeId);

        if (CollectionUtils.isEmpty(bookSeatsClientBOs)) {
            return;
        }

        // 更新客户端状态
        bookSeatsClientBOs.forEach(bookSeatsClientBO -> {
            if (!CollectionUtils.isEmpty(bookSeatsList)) {
                bookSeatsList.forEach(bookSeats -> {
                    if (bookSeats.getClientIds().contains(bookSeatsClientBO.getClientId())) {
                        bookSeatsClientBO.setStatus(1);
                    }
                });
            }

            if (!CollectionUtils.isEmpty(billingOnlineList)) {
                billingOnlineList.forEach(billingOnline -> {
                    if (billingOnline.getClientId().equals(bookSeatsClientBO.getClientId())) {
                        bookSeatsClientBO.setStatus(1);
                    }
                    if (bookSeatsClientBO.getIsRoom() == 1 && billingOnline.getAreaId().equals(bookSeatsClientBO.getAreaId())) {
                        bookSeatsClientBO.setStatus(1);
                    }
                });
            }
        });
    }

    private BookSeatsConfigBO getBookSeatsConfigBO(String placeId, String cardTypeId) {
        BookSeatsConfigBO bookSeatsConfigBO = this.getConfig(placeId);
        List<BookSeatsClientBO> bookSeatsClientBOs = bookSeatsConfigBO.getBookSeatsClientBOs();

        // 获取当前卡类型在当前场所下的所有区域的标准计费规则
        List<BillingRuleCommon> billingRuleCommons = billingRuleCommonRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
        // 获取各个区域，当前计费卡标准费率
        Map<String, BillingRuleCommon> areaRuleMap = billingRuleCommons.stream().collect(Collectors.toMap(BillingRuleCommon::getAreaId, t -> t));

        // 设置客户端当前订座时的费率
        bookSeatsClientBOs.forEach(bookSeatsClientBO -> {
            if (areaRuleMap.containsKey(bookSeatsClientBO.getAreaId())) {
                BillingRuleCommon billingRuleCommon = areaRuleMap.get(bookSeatsClientBO.getAreaId());
                bookSeatsClientBO.setRuleId(billingRuleCommon.getRuleId());
                int price = CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices());
                bookSeatsClientBO.setPrice(price);
            }
        });

        bookSeatsConfigBO.setBookSeatsClientBOs(bookSeatsClientBOs);

        // 组装区域列表，去重。先把 BookSeatsAreaBO 对象基本属性填完，然后填充客户端列表
        List<BookSeatsAreaBO> bookSeatsAreaBOS = new ArrayList<>();
        bookSeatsClientBOs.stream()
                .collect(Collectors.toMap(PlaceClientBO::getAreaId, PlaceClientBO::getAreaName, (t, u) -> t))
                .forEach((k, v) -> {
                    BookSeatsAreaBO bookSeatsAreaBO = new BookSeatsAreaBO();
                    bookSeatsAreaBO.setAreaId(k);
                    bookSeatsAreaBO.setAreaName(v);

                    // 每项区域内有的客户端
                    List<BookSeatsClientBO> bookSeatsClientBOs1 = new ArrayList<>();
                    bookSeatsClientBOs.forEach(t -> {
                        if (t.getAreaId().equals(k)) {
//                            log.info("price:{}", t.getPrice());
//                            log.info("room:{}", t.getIsRoom());
                            bookSeatsAreaBO.setPrice(t.getPrice());
                            bookSeatsAreaBO.setIsRoom(t.getIsRoom());
                            bookSeatsClientBOs1.add(t);
                        }
                    });

                    bookSeatsAreaBO.setBookSeatsClientBOs(bookSeatsClientBOs1);
                    bookSeatsAreaBOS.add(bookSeatsAreaBO);
                });

        List<BookSeatsAreaBO> bookSeatsAreaBOS2 = bookSeatsAreaBOS.stream()
                .sorted(Comparator.comparing(BookSeatsAreaBO::getAreaId)).collect(Collectors.toList());
        bookSeatsConfigBO.setBookSeatsAreaBOs(bookSeatsAreaBOS2);

        return bookSeatsConfigBO;
    }

    /**
     * 用户执行订座
     *
     * @param bookSeatsBO 订座信息（必填字段：placeId、idNumber、clientIds、duration）
     * @return 订座信息
     */
    @Override
    @Transactional
    public BookSeatsBO customerCreate(BookSeatsBO bookSeatsBO) {
        return this.create(bookSeatsBO, SourceType.WECHAT);
    }

    @Override
    @Transactional
    public BookSeatsBO thirdAccountCreate(BookSeatsBO bookSeatsBO, ThirdAccount thirdAccount) {
        if (null == thirdAccount || null == thirdAccount.getSourceType()) {
            log.error("thirdAccountCancel error, 第三方账号为空，或没有绑定 SourceType，thirdAccount={}",
                    new Gson().toJson(thirdAccount));
            throw new ServiceException(ServiceCodes.ACCOUNT_NO_BOUND);
        }

        return this.create(bookSeatsBO, thirdAccount.getSourceType());
    }

    public BookSeatsBO create(BookSeatsBO bookSeatsBO, SourceType sourceType) {

        this.checkBookingIsOpening(bookSeatsBO.getPlaceId(), sourceType);

        // 加并发锁
        String clientIdsStr = bookSeatsBO.getClientIds();
        List<String> clientIds = Arrays.asList(clientIdsStr.split(","));
        for (String clientId : clientIds) {
            String billingKey = billingLockAlgorithm.acquireLock(bookSeatsBO.getPlaceId(), clientId);
            if (billingKey == null) {
                // 没拿到锁
                throw new ServiceException(ServiceCodes.BILLING_IN_PROGRESS);
            }
        }

        // 校验必要信息
        verifyNecessaryInformationAndUpdateFeilds(bookSeatsBO);

        BookSeats bookSeats = init(bookSeatsBO);

        // 然后，扣第一次钱
        deductionUnitCost(bookSeats);

        // 生成解锁码
        bookSeats.setUnlockCode(createUnlockCode(bookSeats.getPlaceId()));

        BookSeats save = bookSeatsRepository.save(bookSeats);

        // 配置轮询通知收银台
        sendNotifyToCashier(save, sourceType, BusinessType.BOOK_SEATS);

        saveOperationLog(save, sourceType);

        // 释放锁
        for (String clientId : clientIds) {
            String billingKey = BILLING_SIMULTANEOUSLY_LOCK + "_" + bookSeats.getPlaceId() + "_" + clientId;
            billingLockAlgorithm.releaseLock(billingKey);
        }

        GenericResponse<ObjDTO<PlaceProfileBO>> placeId = placeServerService.findByPlaceId(save.getPlaceId());
        GenericResponse<ListDTO<PlaceClientBO>> listDTOGenericResponse = placeServerService.queryAllClientByClientIds(save.getPlaceId(), Arrays.asList(save.getClientIds().split(",")));
        if(placeId.isResult() && listDTOGenericResponse.isResult()){
            String clientName = listDTOGenericResponse.getData().getList().stream().map(PlaceClientBO::getHostName).collect(Collectors.joining("、"));
            //发送订座成功模通知
            wechatMessageApi.sendBookSeatsSuccessMessage(bookSeats.getPlaceId(),save.getIdNumber(),placeId.getData().getObj().getDisplayName(),
                    clientName,save.getStartTime(), save.getStartTime().plusMinutes(save.getDuration()),save.getUnlockCode());
        }


        placementService.refreshPlacementDate(bookSeats.getPlaceId(),bookSeats.getClientIds(),"4",bookSeats.getIdName(),bookSeats.getIdNumber(),bookSeats.getCardId());

        return save.toBO();
    }

    private static BookSeats init(BookSeatsBO bookSeatsBO) {
        BookSeats bookSeats = new BookSeats();
        BeanUtils.copyProperties(bookSeatsBO, bookSeats);
        bookSeats.setStatus(0);
        return bookSeats;
    }

    private void sendNotifyToCashier(BookSeats bookSeats, SourceType sourceType, BusinessType businessType) {
        GenericResponse<ObjDTO<PollingBO>> response = notifyServerService.savePolling(bookSeats.getPlaceId(), bookSeats.getClientIds().split(",")[0], bookSeats.getIdNumber(), businessType);

        if (response.isResult()) {
            PollingBO polling = response.getData().getObj();
            BookSeatsBusinessBO bookSeatsBusinessBO = new BookSeatsBusinessBO();
            bookSeatsBusinessBO.setBusinessId(polling.getCashierBusinessId());
            bookSeatsBusinessBO.setPlaceId(bookSeats.getPlaceId());
            bookSeatsBusinessBO.setClientId(bookSeats.getClientIds().split(",")[0]);
            bookSeatsBusinessBO.setIdNumber(bookSeats.getIdNumber());
            bookSeatsBusinessBO.setCreated(LocalDateTime.now().toString());
            bookSeatsBusinessBO.setSourceType(sourceType);
            bookSeatsBusinessBO.setBusinessType(businessType);

            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<ObjDTO<BookSeatsBusinessBO>> response1 = businessDataApi.pushBookSeats(requestTicket, bookSeatsBusinessBO);
            if (!response1.isResult()) {
                log.warn("businessDataApi.pushBookSeats error: {}", response1.getCode() + " " + response1.getMessage());
            }
        } else {
            log.warn("notifyServerService.savePolling error: {}", response.getCode() + " " + response.getMessage());
        }
    }

    @Override
    @Transactional
    public void customerCancel(long bookSeatsId, long operator) {
        BookSeats bookSeats = this.cancel(bookSeatsId, operator, FinishType.USER_CANCEL);

        saveOperationLog(bookSeats, SourceType.WECHAT);
        // 配置轮询通知收银台
        sendNotifyToCashier(bookSeats, WECHAT, BusinessType.BOOK_SEATS_END);
    }

    /**
     * 用户端获取订座列表
     *
     * @param queryBO 订座列表筛选条件(placeId、idNumber 必填)
     * @return 订座列表
     */
    @Override
    public Page<BookSeatsBO> customerGetList(BookSeatsQueryBO queryBO) {
        if (queryBO == null || StringUtils.isEmpty(queryBO.getIdNumber())) {
            log.info("BookSeatsQueryBO is null or idNumber is null");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        return this.getList(queryBO);
    }

    /**
     * 定时任务，订座中的订座进行扣款/结束订座
     */
    @Override
    @Transactional
    public void deductionSchedule() {
        // 获取订座中的 bookSeats 数据
        List<BookSeats> bookSeatsList = bookSeatsRepository.findAllByDeletedAndStatus(0, 0);
        if (CollectionUtils.isEmpty(bookSeatsList)) {
            log.info("bookSeatsList is empty");
            return;
        }
        log.info("bookSeatsList size: {}", bookSeatsList.size());

        // 再进行扣钱
        for (BookSeats bookSeats : bookSeatsList) {
            LocalDateTime startTime = bookSeats.getStartTime();
            LocalDateTime now = LocalDateTime.now();

            // 是否到截止时间
            LocalDateTime endTime = startTime.plusMinutes(bookSeats.getDuration());
            if (now.isEqual(endTime) || now.isAfter(endTime)) {
                // 到了订座截止时间，结束本次订座
                bookSeats.setStatus(1);
                bookSeats.setActualEndTime(now);
                bookSeats.setFinishType(EXPIRED);
                bookSeats.setUpdated(now);
                bookSeats.setUpdater(-1L);
                BookSeats save = bookSeatsRepository.save(bookSeats);
                // 配置轮询通知收银台
                sendNotifyToCashier(save, SYSTEM, BusinessType.BOOK_SEATS_END);

                log.info("订座时间已到，结束本次订座，id={}, placeId={}, idNumber={}", bookSeats.getId(), bookSeats.getPlaceId(), bookSeats.getIdNumber());

                saveOperationLog(save, SourceType.SYSTEM);

                GenericResponse<ObjDTO<PlaceProfileBO>> placeId = placeServerService.findByPlaceId(save.getPlaceId());
                GenericResponse<ListDTO<PlaceClientBO>> listDTOGenericResponse = placeServerService.queryAllClientByClientIds(save.getPlaceId(), Arrays.asList(save.getClientIds().split(",")));
                if(placeId.isResult() && listDTOGenericResponse.isResult()){
                    String clientName = listDTOGenericResponse.getData().getList().stream().map(PlaceClientBO::getHostName).collect(Collectors.joining("、"));
                    //发送订座取消模通知
                    wechatMessageApi.sendBookSeatsCancelMessage(save.getPlaceId(),save.getIdNumber(),placeId.getData().getObj().getDisplayName(),
                            clientName,save.getStartTime(), now);
                }
                placementService.refreshPlacementDate(bookSeats.getPlaceId(),bookSeats.getClientIds(),"5",bookSeats.getIdName(),bookSeats.getIdNumber(),bookSeats.getCardId());
                continue;
            }

            // 没有到截止时间，同时费率为 0，则不扣钱
            int price = bookSeats.getPrice();
            if (0 == price) {
                log.info("费率为 0，不扣钱，id={}, placeId={}, idNumber={}", bookSeats.getId(), bookSeats.getPlaceId(), bookSeats.getIdNumber());
                bookSeats.setUpdated(LocalDateTime.now());
                bookSeats.setUpdater(-1L);
                bookSeatsRepository.save(bookSeats);
                continue;
            }

            // 获取已经扣了多少钱，算出是第几次扣
            int actualCost = bookSeats.getActualCost();
            int unitCost = getUnitCost(bookSeats); // 可能为 0
            int countForDeduction = actualCost / unitCost; // 扣了几次

            LocalDateTime nextDeductionTime = startTime.plusMinutes((long) countForDeduction * unitTimes);
            if (now.isBefore(nextDeductionTime)) {
                // 未到下次计费时间
                log.info("未到下次计费时间，id={}, placeId={}, idNumber={}", bookSeats.getId(), bookSeats.getPlaceId(), bookSeats.getIdNumber());
                continue;
            }

            log.info("到了下一次计费时间，id={}, placeId={}, idNumber={}", bookSeats.getId(), bookSeats.getPlaceId(), bookSeats.getIdNumber());
            // 到了下一次扣费时间
            String placeId = bookSeats.getPlaceId();
            String idNumber = bookSeats.getIdNumber();
            BillingCardBO billingCardBOOnlyReadOverSystem = billingCardService.getOnlyReadOverSystem(placeId, idNumber);
            // 计费卡里的钱
            int accountOfBillingCard = billingCardBOOnlyReadOverSystem.getCashAccount() + billingCardBOOnlyReadOverSystem.getPresentAccount();

            if (accountOfBillingCard < unitCost) {
                log.info("unitCost={}, billingCard={}", unitCost, new Gson().toJson(billingCardBOOnlyReadOverSystem));
                // 余额不足，结束订座
                bookSeats.setStatus(1);
                bookSeats.setActualEndTime(now);
                bookSeats.setFinishType(FinishType.NOT_ENOUGH_BALANCE);
                bookSeats.setUpdated(now);
                bookSeats.setUpdater(-1L);
                BookSeats save = bookSeatsRepository.save(bookSeats);
                // 配置轮询通知收银台
                sendNotifyToCashier(save, SYSTEM, BusinessType.BOOK_SEATS_END);

                log.info("余额不足，结束订座，id={}, placeId={}, idNumber={}", bookSeats.getId(), placeId, idNumber);

                saveOperationLog(save, SourceType.SYSTEM);

                continue;
            }

            // 扣钱
            deductionUnitCost(bookSeats);
            bookSeats.setUpdated(now);
            bookSeats.setUpdater(-1L);
            bookSeatsRepository.save(bookSeats);
            log.info("扣钱成功，id={}, placeId={}, idNumber={}", bookSeats.getId(), placeId, idNumber);
        }
    }

    @Override
    @Transactional
    public BookSeatsBO thirdAccountCancel(String placeId, long bookSeatsId, String idNumber, ThirdAccount thirdAccount) {
        BillingCardBO billingCardBOOnlyReadOverSystem = billingCardService.getOnlyReadOverSystem(placeId, idNumber);
        Long operatorId = billingCardBOOnlyReadOverSystem.getId();

        BookSeats bookSeats = this.cancel(bookSeatsId, operatorId, FinishType.USER_CANCEL);

        if (null == thirdAccount || null == thirdAccount.getSourceType()) {
            log.error("thirdAccountCancel error, 第三方账号为空，或没有绑定 SourceType，thirdAccount={}",
                    new Gson().toJson(thirdAccount));
            throw new ServiceException(ServiceCodes.ACCOUNT_NO_BOUND);
        }

        saveOperationLog(bookSeats, thirdAccount.getSourceType());
        // 配置轮询通知收银台
        sendNotifyToCashier(bookSeats, thirdAccount.getSourceType(), BusinessType.BOOK_SEATS_END);

        return bookSeats.toBO();
    }

    @Override
    public BookSeatsBO order(long id, String idNumber) {
        if (id == 0 || StringUtils.isEmpty(idNumber)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<BookSeats> bookSeatsOptional = bookSeatsRepository.findByIdAndIdNumber(id, idNumber);
        return bookSeatsOptional.map(t -> {
            BookSeatsBO bookSeatsBO = t.toBO();
            // 塞客户端列表详情
            String clientIdsStr = t.getClientIds();
            String[] clientIdArr = clientIdsStr.split(",");
            List<String> clients = Arrays.asList(clientIdArr);
            List<PlaceClientBO> placeClientBOS = getFullInfoPlaceClientBOS(t.getPlaceId(), clients);
            bookSeatsBO.setClientList(placeClientBOS);
            return bookSeatsBO;
        }).orElse(null);
    }

    @Override
    public BookSeatsBO order(long id, String idNumber, String cardId) {
        if (id == 0 || (StringUtils.isEmpty(idNumber) && StringUtils.isEmpty(cardId))) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (!StringUtils.isEmpty(idNumber)) {
            return order(id, idNumber);
        } else {
            Optional<BookSeats> bookSeatsOptional = bookSeatsRepository.findByIdAndCardId(id, cardId);
            return bookSeatsOptional.map(t -> {
                BookSeatsBO bookSeatsBO = t.toBO();
                // 塞客户端列表详情
                String clientIdsStr = t.getClientIds();
                String[] clientIdArr = clientIdsStr.split(",");
                List<String> clients = Arrays.asList(clientIdArr);
                List<PlaceClientBO> placeClientBOS = getFullInfoPlaceClientBOS(t.getPlaceId(), clients);
                bookSeatsBO.setClientList(placeClientBOS);
                return bookSeatsBO;
            }).orElse(null);
        }
    }

    private void saveOperationLog(BookSeats save, SourceType sourceType, LogShift logShift) {
        LogOperation logOperation = new LogOperation();
        OperationType operationType;

        String detail = "订座";
        String shiftId = null;
        String cashierId = null;
        int cashBalance = 0;
        int presentBalance = 0;

        if (save.getStatus() == 0) {
            if (save.getFinishType() == null) {
                // 订座创建
                operationType = OperationType.CREATE_BOOK_SEATS;
                detail += "开始，" + save.getPrice() / 100 + "元/小时";
            } else {
                log.warn("记录异常，status=0 时，finishType 必须为空，此时不为空，bookSeats={}", new Gson().toJson(save));
                throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
            }
        } else if (save.getStatus() == 1) {
            // 订座结束
            operationType = OperationType.END_BOOK_SEATS;

            switch (save.getFinishType()) {
                case EXPIRED:
                    detail += (FinishType.EXPIRED.getDesc() + "，共" + save.getActualCost() / 100 + "元");
                    break;
                case USER_LOGIN:
                    detail += (FinishType.USER_LOGIN.getDesc() + "，共" + save.getActualCost() / 100 + "元");
                    break;
                case NOT_ENOUGH_BALANCE:
                    detail += (FinishType.NOT_ENOUGH_BALANCE.getDesc() + "，共" + save.getActualCost() / 100 + "元");
                    break;
                case CASHIER_CANCEL:
                    detail += (FinishType.CASHIER_CANCEL.getDesc() + "，共" + save.getActualCost() / 100 + "元");
                    shiftId = logShift.getShiftId();
                    cashierId = logShift.getCashierId();
                    break;
                case USER_CANCEL:
                    detail += (FinishType.USER_CANCEL.getDesc() + "，共" + save.getActualCost() / 100 + "元");
                    break;
                default:
                    break;
            }

            Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(save.getPlaceId(), save.getIdNumber());
            BillingCard billingCard = billingCardOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND));
            cashBalance = billingCard.getCashAccount();
            presentBalance = billingCard.getPresentAccount();
        } else {
            log.warn("bookSeats status 异常，只能为0或1，此时为{}，bookSeats={}", save.getStatus(), new Gson().toJson(save));
            throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
        }

        logOperation.setPlaceId(save.getPlaceId());
        logOperation.setCardId(save.getCardId());
        logOperation.setCardTypeId(save.getCardTypeId());
        logOperation.setCardTypeName(save.getCardTypeName());
        logOperation.setIdNumber(save.getIdNumber());
        logOperation.setIdName(save.getIdName());
        logOperation.setShiftId(shiftId);
        logOperation.setCreaterName(save.getIdName());
        logOperation.setLoginId(null);
        logOperation.setClientId(null);
        logOperation.setLastClientId(null);
        logOperation.setOperationType(operationType);
        logOperation.setSourceType(sourceType);

        logOperation.setCost(save.getActualCost());
        logOperation.setPresent(0);
        logOperation.setCashBalance(cashBalance);
        logOperation.setPresentBalance(presentBalance);

        logOperation.setRemark(null);
        logOperation.setDetails(detail);
        logOperation.setCashierId(cashierId);

        logOperation.setCreated(LocalDateTime.now());
//        logOperationService.save(logOperation);
        try {
            logOperationService.save(logOperation);
        } catch (Exception e) {
            logOperation.setRemark(logOperation.getRemark() + "，防御性数据1");
            try {
                logOperationService.save(logOperation);
            } catch (Exception ex) {
                logOperation.setRemark(logOperation.getRemark() + "，防御性数据2");
                logOperationService.save(logOperation);
            }
        }
    }

    private void saveOperationLog(BookSeats save, SourceType sourceType) {
        this.saveOperationLog(save, sourceType, null);
    }

    /**
     * 验证解锁码，结束订座，上机
     *
     * @param placeId    场所id
     * @param unlockCode 解锁码
     * @param idNumber   身份证号
     * @param sourceType 解锁来源
     * @apiNote 1、场所没有开启订座功能，则直接绕过；<br/>
     * 2、场所开启了订座功能，客户端不在订座中，则直接绕过；<br/>
     * 3、场所开启了订座功能，客户端正在订座中，则判断解锁码是否正确。
     */
    @Override
    public void finish(String placeId, String clientId, String unlockCode, String idNumber, SourceType sourceType) {
        if (StringUtils.isEmpty(placeId) || sourceType == null) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 查询场所是否开启订座功能
        Optional<PlaceBizConfig> placeBizConfigOptional = placeBizConfigRepository.findByPlaceId(placeId);
        PlaceBizConfig placeBizConfig = placeBizConfigOptional.orElseThrow(() -> {
            log.warn("placeBizConfig is null, placeId={}", placeId);
            return new ServiceException(ServiceCodes.BAD_PARAM);
        });

        int bookSeatsFlag = placeBizConfig.getBookSeatsFlag();
        if (bookSeatsFlag == 0) {
            log.info("订座功能未开启，placeId={}", placeId);
            return;
        }

        // 当前客户端是否处于订座中
        List<BookSeats> bookSeatsList = bookSeatsRepository.findByPlaceIdAndDeletedAndStatus(placeId, 0, 0);
        if (CollectionUtils.isEmpty(bookSeatsList)) {
            return;
        }

        // 当前客户端对应的订座中的记录
        List<BookSeats> seatsList = bookSeatsList.stream()
                .filter(BookSeats -> BookSeats.getClientIds().contains(clientId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(seatsList)) {
            // 客户端不在订座中，直接返回
            return;
        }
        if (seatsList.size() > 1) {
            log.warn("当前客户端有多条正在订座记录，placeId={}, clientId={}", placeId, clientId);
            throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
        }
        BookSeats bookSeats = seatsList.get(0);

        // 客户端正在订座中
        log.info("客户端正在订座中，placeId={}, clientId={}", placeId, clientId);
        // 判断解锁码
        if (!bookSeats.getUnlockCode().equals(unlockCode)) {
            log.info("解锁码不正确，placeId={}, clientId={}, unlockCode={}", placeId, clientId, unlockCode);
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_UNLOCK_CODE_WRONG);
        }

        // 解除订座
        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
        BillingCard billingCard = billingCardOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND));

        LocalDateTime now = LocalDateTime.now();
        bookSeats.setUpdated(now);
        bookSeats.setFinishType(FinishType.USER_LOGIN);
        bookSeats.setActualEndTime(now);
        bookSeats.setUpdater(billingCard.getId());
        bookSeats.setUnlockCardId(billingCard.getCardId());
        bookSeats.setUnlockIdNumber(billingCard.getIdNumber());
        bookSeats.setUnlockIdName(billingCard.getIdName());
        bookSeats.setStatus(1);
        BookSeats save = bookSeatsRepository.save(bookSeats);

        saveOperationLog(save, sourceType);

        // 配置轮询通知收银台
        // 除了客户端/收银台，其他来源咨询收银台开发人员是否需要发送轮询通知
        if (SourceType.WECHAT == sourceType) {
            sendNotifyToCashier(save, sourceType, BusinessType.BOOK_SEATS_END);
        }
    }

    /**
     * 通过解锁码结束订座
     *
     * @param placeId    场所id
     * @param unlockCode 解锁码
     * @param sourceType 解锁来源
     * @apiNote 1、场所没有开启订座功能，则直接绕过；<br/>
     * 2、场所开启了订座功能，客户端不在订座中，则直接绕过；<br/>
     * 3、场所开启了订座功能，客户端正在订座中，则判断解锁码是否正确。
     */
    @Override
    public void finishByUnlockCode(String placeId, String clientId, String unlockCode, SourceType sourceType) {
        if (StringUtils.isEmpty(placeId) || sourceType == null || StringUtils.isEmpty(unlockCode) || StringUtils.isEmpty(clientId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 查询场所是否开启订座功能
        Optional<PlaceBizConfig> placeBizConfigOptional = placeBizConfigRepository.findByPlaceId(placeId);
        PlaceBizConfig placeBizConfig = placeBizConfigOptional.orElseThrow(() -> {
            log.warn("placeBizConfig is null, placeId={}", placeId);
            return new ServiceException(ServiceCodes.BAD_PARAM);
        });

        int bookSeatsFlag = placeBizConfig.getBookSeatsFlag();
        if (bookSeatsFlag == 0) {
            log.info("订座功能未开启，placeId={}", placeId);
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_IS_NOT_OPEN);
        }

        // 当前客户端是否处于订座中
        List<BookSeats> bookSeatsList = bookSeatsRepository.findByPlaceIdAndDeletedAndStatus(placeId, 0, 0);
        if (CollectionUtils.isEmpty(bookSeatsList)) {
            log.info("当前客户端没有正在订座记录，placeId={}, clientId={}", placeId, clientId);
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_NO_REPETITION);
        }

        // 当前客户端对应的订座中的记录
        List<BookSeats> seatsList = bookSeatsList.stream()
                .filter(bs -> containClientId(bs,clientId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(seatsList)) {
            log.info("客户端没有正在订座记录，placeId={}, clientId={}", placeId, clientId);
            // 客户端不在订座中
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_NO_REPETITION);
        }
        if (seatsList.size() > 1) {
            log.warn("当前客户端有多条正在订座记录，placeId={}, clientId={}", placeId, clientId);
            throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
        }
        BookSeats bookSeats = seatsList.get(0);

        // 客户端正在订座中
        log.info("客户端正在订座中，placeId={}, clientId={}", placeId, clientId);
        // 判断解锁码
        if (!bookSeats.getUnlockCode().equals(unlockCode)) {
            log.info("解锁码不正确，placeId={}, clientId={}, unlockCode={}", placeId, clientId, unlockCode);
            throw new ServiceException(ServiceCodes.BILLING_BOOK_SEATS_UNLOCK_CODE_WRONG);
        }

        // 解除订座
        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, bookSeats.getIdNumber());
        BillingCard billingCard = billingCardOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND));

        LocalDateTime now = LocalDateTime.now();
        bookSeats.setUpdated(now);
        bookSeats.setFinishType(FinishType.USER_LOGIN);
        bookSeats.setActualEndTime(now);
        bookSeats.setUpdater(billingCard.getId());
        bookSeats.setUnlockCardId(billingCard.getCardId());
        bookSeats.setUnlockIdNumber(billingCard.getIdNumber());
        bookSeats.setUnlockIdName(billingCard.getIdName());
        bookSeats.setStatus(1);
        BookSeats save = bookSeatsRepository.save(bookSeats);

        saveOperationLog(save, sourceType);

        // 配置轮询通知收银台
        // 除了客户端/收银台，其他来源咨询收银台开发人员是否需要发送轮询通知
        if (SourceType.WECHAT == sourceType) {
            sendNotifyToCashier(save, sourceType, BusinessType.BOOK_SEATS_END);
        }
    }

    /**
     * 判断当前订座信息是否包含当前客户端
     * @param bookSeats
     * @param clientId
     * @return
     */
    private boolean containClientId(BookSeats bookSeats, String clientId){
        String clientIds = bookSeats.getClientIds();
        if (org.apache.commons.lang3.StringUtils.isBlank(clientIds)) {
            return false;
        }

        return Arrays.asList(clientIds.split(",")).contains(clientId);
    }

    /**
     * 扣除订座第一次的钱
     *
     * @param bookSeats 订座信息
     * @apiNote bookSeatsBO 更新 actualCost、creater
     */
    private void deductionUnitCost(BookSeats bookSeats) {
        //findByPlaceIdAndIdNumberAndNotDeleted
        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(bookSeats.getPlaceId(), bookSeats.getIdNumber());
        BillingCard billingCard = billingCardOptional.orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND));

        // 获取网吧配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(billingCard.getPlaceId());

        // 第一次扣费，设置创建人
        if (bookSeats.getActualCost() == 0) {
            bookSeats.setCreater(billingCard.getId());

            LocalDateTime startTime = bookSeats.getStartTime();
            if (null != startTime && LocalDateTime.now().isBefore(startTime) && bookSeats.getFreeTime() > 0) {
                log.info("订座时间未到，不扣费");
                return;
            }
        }

        int unitCost = getUnitCost(bookSeats);
        // 获取扣费细节
        List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, unitCost, null, 0, placeBizConfig.getDeductionOrder());
        // 扣费
        billingCardService.billingCardDeduction(costDetails, billingCard.getPlaceId());

        if (!ObjectUtils.isEmpty(costDetails)) {
            int cost = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream()
                    .mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum);
            int present = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum);

            String description = "网费-订座[" + bookSeats.getClientIds() + "]-生效人id:" + billingCard.getCardId();
            if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                // 查询最新的值
                Optional<BillingCard> newOptBillingCard = cardService.findByPlaceIdAndIdNumberAndNotDeleted(billingCard.getPlaceId(), billingCard.getIdNumber(), 0);
                if (!newOptBillingCard.isPresent()) {
                    log.info("{}-{}-{} 订座未找到连锁卡信息", billingCard.getPlaceId(), bookSeats.getClientIds(), billingCard.getCardId());
                    return;
                }
                billingCard = newOptBillingCard.get();
            }
            balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(),billingCard,bookSeats.getClientIds(),7,0,cost,present, description,0,0, null);
        }




        bookSeats.setActualCost(bookSeats.getActualCost() + unitCost);
    }

    private int getUnitCost(BookSeats bookSeats) {
        return bookSeats.getPrice() / (60 / unitTimes);
    }

    /**
     * 校验订座信息
     *
     * @param bookSeatsBO 订座信息
     * @apiNote 校验的字段：placeId、idNumber、clientIds、duration；
     * <p></p>
     * bookSeatsBO 更新 setCreated、setStartTime、setIdName、setCardId、setIdcardNum、
     * setCardTypeId、setCardTypeName、setFreeTime、setEndTime、areaId、
     * roomFlag、ruleId、price、estimatedCost。
     */
    private void verifyNecessaryInformationAndUpdateFeilds(BookSeatsBO bookSeatsBO) {
        // 需要校验的字段
        String placeIdFromCustomer = bookSeatsBO.getPlaceId();
        String idNumberFromCustomer = bookSeatsBO.getIdNumber();
        String clientIdsStrFromCustomer = bookSeatsBO.getClientIds();
        int durationFromCustomer = bookSeatsBO.getDuration();

        // 校验场所是否支持在线充值
        GenericResponse<ObjDTO<PlaceConfigBO>> responsePlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeIdFromCustomer);
        if (responsePlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = responsePlaceConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        // 校验身份信息
        BillingCardBO billingCardBOOnlyReadOverSystem =
                billingCardService.getOnlyReadOverSystem(placeIdFromCustomer, idNumberFromCustomer);
        if (billingCardBOOnlyReadOverSystem == null) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        // 临时卡/工作卡不支持订座
        if ("1000".equals(billingCardBOOnlyReadOverSystem.getCardTypeId())
                || "1002".equals(billingCardBOOnlyReadOverSystem.getCardTypeId())) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
        }

        bookSeatsBO.setIdName(billingCardBOOnlyReadOverSystem.getIdName());
        bookSeatsBO.setCardId(billingCardBOOnlyReadOverSystem.getCardId());
        bookSeatsBO.setIdcardNum(billingCardBOOnlyReadOverSystem.getIdcardNum());
        bookSeatsBO.setCardTypeId(billingCardBOOnlyReadOverSystem.getCardTypeId());
        bookSeatsBO.setCardTypeName(billingCardBOOnlyReadOverSystem.getCardTypeName());

        // 校验是否是一个座位/多个是否是同一区域且为包间
        if (StringUtils.isEmpty(clientIdsStrFromCustomer)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
        String[] clientIdArr = clientIdsStrFromCustomer.split(",");
        String cardTypeId = billingCardBOOnlyReadOverSystem.getCardTypeId();

        // 订座配置
        BookSeatsConfigBO bookSeatsConfigBO = getBookSeatsConfigBO(placeIdFromCustomer, cardTypeId);
        LocalDateTime now = LocalDateTime.now();
        int freeTime = bookSeatsConfigBO.getBookSeatsFreeTime();
        bookSeatsBO.setCreated(now);
        bookSeatsBO.setStartTime(now.plusMinutes(freeTime));
        bookSeatsBO.setFreeTime(freeTime);

        List<String> cannotBookClientIds = bookSeatsConfigBO.getCannotBookClientIds();
        List<BookSeatsClientBO> bookSeatsClientBOs = bookSeatsConfigBO.getBookSeatsClientBOs();
        String ruleId;
        int price;
        if (clientIdArr.length > 1) {
            // 包间校验，检查客户端是否都在同一区域
            Arrays.stream(clientIdArr).forEach(clientId -> {
                if (CollectionUtils.isEmpty(bookSeatsClientBOs)
                        || (!CollectionUtils.isEmpty(cannotBookClientIds) && cannotBookClientIds.contains(clientId))) {
                    log.info("客户端 {} 不可订座", clientId);
                    throw new ServiceException(ServiceCodes.BILLING_CURRENT_CLIENT_NOT_ALLOW_BOOK_SEATS);
                }
            });

            // 入参包含的区域
            List<String> areas = bookSeatsClientBOs.stream()
                    .filter(bookSeatsClientBO -> Arrays.asList(clientIdArr).contains(bookSeatsClientBO.getClientId()))
                    .map(PlaceClientBO::getAreaId).distinct().collect(Collectors.toList());

            Map<String, BookSeatsClientBO> areaSeatsClientBOMap = bookSeatsClientBOs.stream()
                    .filter(bookSeatsClientBO -> Arrays.asList(clientIdArr).contains(bookSeatsClientBO.getClientId()))
                    .collect(Collectors.toMap(PlaceClientBO::getAreaId, bookSeatsClientBO -> bookSeatsClientBO, (bo1, bo2) -> bo1));

            if (areas.size() > 1) {
                log.info("客户端不在一个包间，不能订座");
                throw new ServiceException(ServiceCodes.BAD_PARAM);
            } else if (areas.size() == 1) {
                bookSeatsBO.setAreaId(areas.get(0));
                bookSeatsBO.setAreaName(areaSeatsClientBOMap.get(areas.get(0)).getAreaName());
            } else {
                log.info("包间区域校验异常");
                throw new ServiceException(ServiceCodes.BAD_PARAM);
            }

            BookSeatsClientBO bookSeatsClientBO = areaSeatsClientBOMap.get(areas.get(0));
            if (bookSeatsClientBO.getIsRoom() != 1) {
                log.info("包间标识错误");
                throw new ServiceException(ServiceCodes.BAD_PARAM);
            } else {
                bookSeatsBO.setRoomFlag(1);
            }

            ruleId = bookSeatsClientBO.getRuleId();
            price = bookSeatsClientBO.getPrice();
        } else if (clientIdArr.length == 1) {
            // 单个客户端
            String clientId = clientIdArr[0];
            log.info("clientId:{}", clientId);
            log.info("bookSeatsClientBOs:{}", new Gson().toJson(bookSeatsClientBOs));
            log.info("cannotBookClientIds:{}", new Gson().toJson(cannotBookClientIds));

            if (CollectionUtils.isEmpty(bookSeatsClientBOs)
                    || (!CollectionUtils.isEmpty(cannotBookClientIds) && cannotBookClientIds.contains(clientId))) {
                log.info("客户端 {} 不可订座", clientId);
                throw new ServiceException(ServiceCodes.BILLING_CURRENT_CLIENT_NOT_ALLOW_BOOK_SEATS);
            }

            // 不在客户端列表中，或对应客户端数量异常
            List<BookSeatsClientBO> bookSeatsClientBOS =
                    bookSeatsClientBOs.stream()
                            .filter(bookSeatsClientBO -> bookSeatsClientBO.getClientId().equals(clientId))
                            .collect(Collectors.toList());
            if (bookSeatsClientBOS.size() != 1) {
                log.info("客户端异常");
                throw new ServiceException(ServiceCodes.BILLING_CURRENT_CLIENT_NOT_ALLOW_BOOK_SEATS);
            }

            BookSeatsClientBO bookSeatsClientBO = bookSeatsClientBOS.get(0);
            bookSeatsBO.setAreaId(bookSeatsClientBO.getAreaId());
            bookSeatsBO.setAreaName(bookSeatsClientBO.getAreaName());
            ruleId = bookSeatsClientBO.getRuleId();
            price = bookSeatsClientBO.getPrice();
            if (bookSeatsClientBO.getIsRoom() == 1) {
                log.info("包间标识错误");
                throw new ServiceException(ServiceCodes.BILLING_CURRENT_CLIENT_NOT_ALLOW_BOOK_SEATS);
            } else {
                bookSeatsBO.setRoomFlag(0);
            }
        } else {
            log.info("客户端id错误");
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        // 校验客户端是否正在订座
        List<BookSeats> bookSeatsList = bookSeatsRepository.findByPlaceIdAndDeletedAndStatus(placeIdFromCustomer, 0, 0);
        if (!CollectionUtils.isEmpty(bookSeatsList)) {
            for (BookSeats bookSeats : bookSeatsList) {
                if (bookSeats.getClientIds().contains(clientIdArr[0])) {
                    log.info("客户端 {} 正在订座中", clientIdArr[0]);
                    throw new ServiceException(ServiceCodes.BILLING_CLIENT_ON_BOOKING);
                }

                if (bookSeats.getIdNumber().equals(idNumberFromCustomer)) {
                    log.info("当前用户已预定座位，不能重复订座，placeId: {}, clientIds: {}, idNumber:{}，", placeIdFromCustomer, clientIdArr, idNumberFromCustomer);
                    throw new ServiceException(ServiceCodes.BILLING_CUSTOMER_ON_BOOKING);
                }
            }
        }

        //校验客户端是否正在上机
        List<BillingOnline> billingOnlineList = billingOnlineService.findUnfinishedByPlaceId(placeIdFromCustomer);
        if (!CollectionUtils.isEmpty(billingOnlineList)) {
            List<String> clientIds = Arrays.asList(clientIdArr);
            for (BillingOnline billingOnline : billingOnlineList) {
                if (clientIds.contains(billingOnline.getClientId())) {
                    log.info("客户端 {} 正在上机中", billingOnline.getClientId());
                    throw new ServiceException(ServiceCodes.BILLING_CLIENT_IS_ONLINE);
                }
            }
        }

        // 校验开始时间
        // 校验订座时间是否是 0.5h/1h/1.5h/2h/2.5h/3h 这几个档位
        // create arrayList for 30 60 90 120 150 180
        int[] durations = {30, 60, 90, 120, 150, 180};
        boolean noneMatch = Arrays.stream(durations).noneMatch(d -> d == durationFromCustomer);
        if (noneMatch) {
            log.info("订座时长错误");
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        // 校验价格
        if (price < 0) {
            log.info("price不合法, code:{}, msg:{}",
                    ServiceCodes.BILLING_CUR_AREA_NO_RIGHT_BOOKING.getCode(), ServiceCodes.BILLING_CUR_AREA_NO_RIGHT_BOOKING.getMessage());
            throw new ServiceException(ServiceCodes.BILLING_CUR_AREA_NO_RIGHT_BOOKING);
        } else {
            // price 大于0 或 等于0
            bookSeatsBO.setPrice(price);
        }

        if (StringUtils.isEmpty(ruleId)) {
            log.info("客户端对应的计费ruleId为空");
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        } else {
            bookSeatsBO.setRuleId(ruleId);
        }

        // 校验金额是否够当前包时
        int willCost = price * durationFromCustomer / 60;
        bookSeatsBO.setEstimatedCost(willCost);
        int account = billingCardBOOnlyReadOverSystem.getCashAccount() + billingCardBOOnlyReadOverSystem.getPresentAccount();
        if (willCost > account) {
            log.info("账户金额不够，willCost={},account={}", willCost, account);
            throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
        }
    }

    /**
     * 生成当前场所可用的解锁码
     *
     * @param placeId 场所 Id
     * @return 解锁码
     * @apiNote 当前场所正在进行的订座中记录唯一
     */
    private String createUnlockCode(String placeId) {
        String generateCode;
        int curPlaceUsingCount;
        do {
            generateCode = Dim4StringUtils.generateCode(4);
            curPlaceUsingCount = bookSeatsRepository.countByPlaceIdAndUnlockCodeAndDeletedAndStatus(placeId, generateCode, 0, 0);
        } while (curPlaceUsingCount > 0);
        return generateCode;
    }

    public void saveAll(List<BookSeats> bookSeats){
        bookSeatsRepository.saveAll(bookSeats);
    }
}
