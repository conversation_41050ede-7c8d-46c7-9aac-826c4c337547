package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.CashierMessageNotify;
import com.rzx.dim4.billing.entity.CashierMessageNotifyDeliver;
import com.rzx.dim4.billing.repository.CashierMessageNotifyDeliverRepository;
import com.rzx.dim4.billing.repository.CashierMessageNotifyRepository;
import lombok.Synchronized;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class CashierMessageNotifyService {

    @Autowired
    private CashierMessageNotifyRepository cashierMessageNotifyRepository;

    public void saveCashierMessageNotify (CashierMessageNotify cashierMessageNotify) {
        if (StringUtils.isEmpty(cashierMessageNotify.getNotifyId())) {
            cashierMessageNotify.setNotifyId(builderNotifyId());
        }
        cashierMessageNotify.setUpdated(LocalDateTime.now());
        cashierMessageNotifyRepository.save(cashierMessageNotify);
    }

    public Optional<CashierMessageNotify> findByNotifyId (String notifyId) {
        return cashierMessageNotifyRepository.findByNotifyId(notifyId);
    }

    public Page<CashierMessageNotify> findAll(Pageable pageable) {
        return cashierMessageNotifyRepository.findByOrderByIdDesc(pageable);
    }

    public List<CashierMessageNotify> findByNotifyIdInOrderByIdDesc (List<String> notifyIds) {
        return cashierMessageNotifyRepository.findByNotifyIdInOrderByIdDesc(notifyIds);
    }

    /**
     * 构建notifyId
     *
     * @return
     */
    @Synchronized
    public String builderNotifyId() {
        int notifyId = 10000;
        Optional<CashierMessageNotify> lastCashierMessage =  cashierMessageNotifyRepository.findTop1ByOrderByIdDesc();
        if (lastCashierMessage.isPresent()) {
            int lastNotifyId = Integer.parseInt(lastCashierMessage.get().getNotifyId());
            notifyId = lastNotifyId + 1;
        }
        return String.valueOf(notifyId);
    }

}
