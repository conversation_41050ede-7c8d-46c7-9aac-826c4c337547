package com.rzx.dim4.billing.repository.third;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.third.MarketLogOperation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MarketLogOperationRepository extends JpaRepository<MarketLogOperation, Long>, JpaSpecificationExecutor<MarketLogOperation> {

    Optional<MarketLogOperation> findByPlaceIdAndOrderId (String placeId,String orderId);


    @Query(value = "select sum(lo.cash_amount) from market_log_operation lo where lo.place_id=?1 AND opt_type = 0  and lo.shift_id=?2  and lo.source_type=?3", nativeQuery = true)
    Integer sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(String placeId, String shiftId, SourceType sourceType);

    @Query(value = "select IFNULL(sum(lo.cash_amount),'0') sumCash,IFNULL(sum(lo.present_amount),'0') sumPresent from market_log_operation lo where lo.place_id=?1 AND opt_type = 0  and lo.shift_id=?2 and lo.card_type_id not in (?3)", nativeQuery = true)
    Map<String,String> sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeIdNotIn(String placeId, String shiftId, List<String> cardTypeIds);

    @Query(value = "select IFNULL(sum(lo.cash_amount),'0') sumCash,IFNULL(sum(lo.present_amount),'0') sumPresent from market_log_operation lo where lo.place_id=?1 AND opt_type = 0  and lo.shift_id=?2 and lo.card_type_id=?3", nativeQuery = true)
    Map<String,String> sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeId(String placeId, String shiftId,String cardTypeId);


}
