package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.IBillingCardService;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.PlaceBizConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 收银台临时卡开卡&包时（不管是收银台是立即包时还是预包时，都是调预包时逻辑）
 *
 * @see com.rzx.dim4.base.enums.billing.ServiceIndexes
 * @see com.rzx.dim4.billing.service.factory.ServiceFactory
 * <AUTHOR>
 * @since 2023/11/28
 **/
@Slf4j
@Service
public class CashierCreateBillingCardWithPackageTimeServiceImpl implements CoreService {

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogOperationService logOperationService;
    @Autowired
    private IBillingCardService iBillingCardService;
    @Autowired
    private PlaceServerService placeServerService;

    @Override
    public GenericResponse<?> doService(List<String> params) {
        // 必填：0.场所ID , 1.卡类型ID，2.收银台ID 3.身份证号码，4.姓名，5.地址，6.签发机关，7.民族, 8.有效期
        if (params.size() < 16) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 获取参数
        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID

        String cardTypeId = params.get(2); // 卡类型ID
        String idNumber = params.get(3); // 身份证号码
        String name = params.get(4); // 姓名
        String address = params.get(5); // 地址
        String issuingAuthority = params.get(6); // 发证机关
        String nation = params.get(7); // 民族
        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡
        String activeType = params.get(9); // 激活方式,传value值
        String identification = params.get(10); // 附加费标识
        String phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
        String remark = params.get(12); // 备注
        if (!StringUtils.isEmpty(remark) && remark.length() > 100) {
            log.warn("remark length is too long, remark:{}", remark);
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        int cashAmount = 0;
        String cashAmountStr = params.get(13); // 充值金额

        String ruleId = params.get(14); // 包时规则ID
        String packageType = params.get(15); // 包时类型 1:余额包时 2:现金包时，当前为固定值1
        try {
            cashAmount = Integer.parseInt(cashAmountStr);
        } catch (NumberFormatException nfe) {
            return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
        }

        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (!placeConfig.isResult()) {
            return placeConfig;
        }
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        /*
         * 补充idNumber不为18位大陆居民身份证号的情况
         */
        if (StringUtils.isEmpty(idNumber) || idNumber.length() < 6 || idNumber.length() > 20 || StringUtils.isEmpty(name) || name.length() > 50) { //
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if (idNumber.length() != 18) { // 非大陆居民身份证，
            String regex = "^[a-zA-Z0-9]+$"; // 只能是数字和字母
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(idNumber);
            if (!matcher.matches()) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
            PlaceBizConfig config = placeBizConfigService.findByPlaceId(placeId); // 查询网吧的非身份证配置
            if (config.getNonIdNumber() > 0) { // 大于0，允许非身份证号注册，需要判断今天已经激活的非身份证数量
                int activated = logOperationService.countTodayActivatedNonIdNumberByPlaceId(placeId);
                if (activated >= config.getNonIdNumber()) { // 激活数量已经达到上限
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
                }
            } else if (config.getNonIdNumber() == 0) { // 等于0， 不允许非身份证号注册，不允许注册非身份证号码
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
            }
        } else { // 大陆居民身份证
            // 验证身份证合法性，姓名不能为空
            idNumber = idNumber.toUpperCase();
            boolean flag = IdNumberValidator.verificate(idNumber);
            if (!flag || StringUtils.isEmpty(name)) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
        }

        BillingCardBO billingCardBO = iBillingCardService.cashierCreateTempCardWithPackageTime(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                cashAmount,
                0,
                true,
                ruleId,
                Integer.parseInt(packageType));

        return new GenericResponse<>(new ObjDTO<>(billingCardBO));
    }
}
