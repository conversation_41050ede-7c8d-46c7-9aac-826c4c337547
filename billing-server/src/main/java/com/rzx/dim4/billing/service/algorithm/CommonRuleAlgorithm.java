package com.rzx.dim4.billing.service.algorithm;

import java.time.LocalDateTime;

import com.rzx.dim4.base.utils.CommonRuleAlgorithmCore;
import org.apache.commons.lang.StringUtils;

/**
 * 普通费率 相关公共转换方法
 */
public class CommonRuleAlgorithm extends CommonRuleAlgorithmCore {

	/**
	 * 根据prices获取当天当前小时的费率价格 (返回的是分)
	 *
	 * @param prices
	 * @return
	 */
	public static int getPrice(String prices) {
		// 星期 7*24数据
		String[] weekData = prices.split("_");

		// 获取当前星期数
		int weekNum = LocalDateTime.now().getDayOfWeek().getValue();

		// 取出当前星期数的 7*24费率信息
		String priceStr = weekData[weekNum - 1];

		// 获取当天当前小时的费率
		int nowHour = LocalDateTime.now().getHour();
		String[] priceArray = priceStr.split(",", -1);
		String price = priceArray[nowHour];

		if (StringUtils.isEmpty(price)) {
			return 0;
		}

		try {
			return Integer.parseInt(price);
		} catch (Exception e) {
			return 0;
		}
	}
}
