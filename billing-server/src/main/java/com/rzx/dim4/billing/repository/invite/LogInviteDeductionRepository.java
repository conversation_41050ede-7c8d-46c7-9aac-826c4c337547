package com.rzx.dim4.billing.repository.invite;

import com.rzx.dim4.billing.entity.invite.LogInviteDeduction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年04月27日 14:36
 */
public interface LogInviteDeductionRepository extends JpaRepository<LogInviteDeduction, Long>, JpaSpecificationExecutor<LogInviteDeduction> {

    List<LogInviteDeduction> findByPlaceIdAndInviteCode(String placeId, String inviteCode);
}
