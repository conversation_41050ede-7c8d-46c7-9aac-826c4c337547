package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.PlaceChainStores;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 计费卡扣费service
 */
@Slf4j
@Service
public class BillingCardDeductionService {

    @Autowired
    BillingCardRepository billingCardRepository;

    @Autowired
    PlaceChainStoresService placeChainStoresService;

    @Autowired
    BillingCardService billingCardService;

    /**
     * 扣费、修改卡余额
     *
     * @param placeId
     * @param cardId
     * @param cashAccount
     * @param presentAccount
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BillingCard updateBillingCardAccount(String placeId, String cardId, int cashAccount, int presentAccount, int temporaryOnlineAccount) {
        billingCardRepository.updateBillingCardAccount(placeId, cardId, cashAccount, presentAccount, temporaryOnlineAccount);
        Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
        return billingCardOpt.orElse(null);
    }

    /**
     * 积分变动
     * @param placeId
     * @param cardId
     * @param points
     */
    @Transactional
    public void updateBillingCardPoints (String placeId, String cardId, int points) {
        billingCardRepository.updateBillingCardPoints(placeId, cardId, points);
    }

    /**
     * 根据当前登入的计费卡获取所有的计费卡信息
     *
     * @param billingCard
     * @return
     */
    public List<BillingCard> getChainBillingCard(BillingCard billingCard) {
        List<BillingCard> result = new ArrayList<>();
        if ("1000".equals(billingCard.getCardTypeId()) || "1002".equals(billingCard.getCardTypeId())) {
            result.add(billingCard);
            return result;
        }
        Optional<BillingCard> chainCardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(billingCard.getPlaceId(), billingCard.getIdNumber(), 0);
        if (chainCardOpt.isPresent()) {
            result.add(chainCardOpt.get());
            return result;
        }
//        List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findPlaceChainStoresListInSamePlaceChainByPlaceId(billingCard.getPlaceId());
//        if (CollectionUtils.isEmpty(placeChainStoresList)) {
//            result.add(billingCard);
//            return result;
//        }
//        List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
//        // 获取该用户在连锁的所有计费卡
//        List<BillingCard> billingCards = billingCardService.findByPlaceIdInAndIdNumberAndCardTypeIdNotIn(placeIds, billingCard.getIdNumber());
        return result;
    }

    /**
     * 根据标识符，计算出对应的总金额
     *
     * @param billingCards
     * @param flag         默认统计总余额  0:统计现金 1:统计奖励 2:统计总余额
     * @return
     */
    public int sumAccount(List<BillingCard> billingCards, int flag) {
        if (StringUtils.isEmpty(billingCards) || billingCards.size() < 1) {
            return 0;
        }
        if (flag == 0) {
            // 如果是临时卡的话，统计现金需要加上临时卡线上账户余额
            if ("1000".equals(billingCards.get(0).getCardTypeId())) {
                return billingCards.stream().mapToInt(BillingCard::getCashAccount).reduce(0, Integer::sum) + billingCards.stream().mapToInt(BillingCard::getTemporaryOnlineAccount).reduce(0, Integer::sum);
            }
            return billingCards.stream().mapToInt(BillingCard::getCashAccount).reduce(0, Integer::sum);
        } else if (flag == 1) {
            return billingCards.stream().mapToInt(BillingCard::getPresentAccount).reduce(0, Integer::sum);
        }
        return billingCards.stream().mapToInt(BillingCard::getTotalAccount).reduce(0, Integer::sum);
    }

    /**
     * 获取扣费信息
     *
     * @param billingCard     当前所在场所的计费卡
     * @param oncePrice       需扣费金额
     * @param loginId         登入id
     * @param deductionSource 0:包时、心跳、登录接口  1:中心定时器
     * @param deductionOrder  扣费顺序 0:优先本金  1:优先奖励  2:只扣本金  3:只扣奖励
     * @return
     */
    public List<PlaceChainBillingCardCostDetail> getChainBillingCardCostDetails(BillingCard billingCard, int oncePrice, String loginId, int deductionSource, int deductionOrder) {

        List<PlaceChainBillingCardCostDetail> result = new ArrayList<>();
        int deductionCash = 0; // 待扣本金
        int deductionPresent = 0; // 待扣奖励
        int deductionTemporaryOnlineAccount = 0; // 待扣在线余额
        String chainId = billingCard.getChainId();

        // 只扣当前场所会员卡余额情况
        if (StringUtils.isEmpty(chainId) || "1000".equals(billingCard.getCardTypeId())) {
            // 不是连锁,或者是临时卡
            if (billingCard.getTotalAccount() <= 0) {
                return null; // 余额不足
            }

            int cashAccount = billingCard.getCashAccount(); // 现金账户
            int presentAccount = billingCard.getPresentAccount(); // 奖励账户
            int temporaryOnlineAccount = billingCard.getTemporaryOnlineAccount(); // 临时卡在线账户
            // 校验余额是否足够
            if (billingCard.getTotalAccount() < oncePrice) {
                if (deductionSource == 1) {
                    // 定时器这里不够一次扣费,账户清零，再上6分钟(或者余额低于最低消费)
                    PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), cashAccount, billingCard.getPresentAccount(), billingCard.getTemporaryOnlineAccount());
                    result.add(placeChainBillingCardCostDetail);
                    return result;
                }
                return null; // 余额不足
            }

            // 余额足够，优先扣本金
            if (deductionOrder == 0) {
                if (cashAccount >= oncePrice) {
                    deductionCash = oncePrice;
                } else if (cashAccount + temporaryOnlineAccount >= oncePrice) {
                    deductionCash = cashAccount;
                    deductionTemporaryOnlineAccount = oncePrice - deductionCash;
                } else {
                    deductionCash = cashAccount;
                    deductionTemporaryOnlineAccount = temporaryOnlineAccount;
                    deductionPresent = oncePrice - deductionCash - deductionTemporaryOnlineAccount;
                }
            } else if (deductionOrder == 1) {
                // 优先扣奖励
                if (presentAccount >= oncePrice) {
                    deductionPresent = oncePrice;
                } else if (cashAccount + presentAccount >= oncePrice) {
                    deductionPresent = presentAccount;
                    deductionCash = oncePrice - deductionPresent;
                } else {
                    deductionPresent = presentAccount;
                    deductionCash = cashAccount;
                    deductionTemporaryOnlineAccount = oncePrice - deductionCash - deductionPresent;
                }
            } else if (deductionOrder == 2) {
                // 只扣本金
                if (cashAccount >= oncePrice) {
                    deductionCash = oncePrice;
                } else if (cashAccount + temporaryOnlineAccount >= oncePrice) {
                    deductionCash = cashAccount;
                    deductionTemporaryOnlineAccount = oncePrice - deductionCash;
                } else {
                    // 余额不足
                    return null;
                }
            } else {
                // 只扣奖励
                if (presentAccount >= oncePrice) {
                    deductionPresent = oncePrice;
                } else {
                    // 余额不足
                    return null;
                }
            }
            PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), deductionCash, deductionPresent, deductionTemporaryOnlineAccount);
            result.add(placeChainBillingCardCostDetail);
            return result;
        }

        // 获取连锁总余额
        List<BillingCard> billingCards = getChainBillingCard(billingCard);

        // 获取该用户在连锁的所有计费卡
        List<BillingCard> billingCardList = billingCardRepository.findByChainIdAndIdNumberAndDeleted(chainId, billingCard.getIdNumber(), 0);

        // 连锁网吧，优先扣登入场所的
        int cashAccount = billingCard.getCashAccount(); // 现金账户
        int presentAccount = billingCard.getPresentAccount(); // 赠送账户

        // 获取连锁配置
        List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(chainId);

        // 只扣本金
        if (deductionOrder == 2) {
            // 先判断总本金是否足够
            if (sumAccount(billingCards,0) < oncePrice) {
                return null;
            }
            // 当前门店本金不够
            if (cashAccount < oncePrice) {
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), cashAccount, 0, 0);

                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                // 如果都是0，则不需要更改这张卡
                if (placeChainBillingCardCostDetail.getCostCash() > 0 || placeChainBillingCardCostDetail.getCostPresent() > 0) {
                    result.add(placeChainBillingCardCostDetail);
                }
               // result.add(placeChainBillingCardCostDetail);

                // 剩余扣费余额
                oncePrice = oncePrice - cashAccount;
            } else {
                // 当前门店本金足够
                deductionCash = oncePrice;
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), deductionCash, deductionPresent, 0);
                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                result.add(placeChainBillingCardCostDetail);
                return result;
            }
            return getChainShareCost(result, placeChainStoresList, billingCardList, billingCards, deductionOrder, oncePrice, deductionSource, loginId, billingCard.getPlaceId());
        } else if (deductionOrder == 3) {
            // 先判断总奖励是否足够
            if (sumAccount(billingCards,1) < oncePrice) {
                return null;
            }
            // 只扣奖励
            if (presentAccount < oncePrice) {
                // 当前门店奖励不够
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), 0, presentAccount, 0);

                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                // 如果都是0，则不需要更改这张卡
                if (placeChainBillingCardCostDetail.getCostCash() > 0 || placeChainBillingCardCostDetail.getCostPresent() > 0) {
                    result.add(placeChainBillingCardCostDetail);
                }
               // result.add(placeChainBillingCardCostDetail);

                // 剩余扣费余额
                oncePrice = oncePrice - presentAccount;
            } else {
                // 当前门店奖励足够
                deductionPresent = oncePrice;
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), deductionCash, deductionPresent, 0);
                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                result.add(placeChainBillingCardCostDetail);
                return result;
            }
            return getChainShareCost(result, placeChainStoresList, billingCardList, billingCards, deductionOrder, oncePrice, deductionSource, loginId, billingCard.getPlaceId());
        } else if (deductionOrder == 1) {
            // 优先奖励
            if (presentAccount < oncePrice) {
                // 当前门店奖励不够
                int cashCost = 0;
                if (sumAccount(billingCards,1) < oncePrice) {
                    // 连锁所有的卡奖励也不够
                    int waitDeductionCash = oncePrice - sumAccount(billingCards,1);
                    if (cashAccount < waitDeductionCash) {
                        cashCost = cashAccount;
                    } else {
                        cashCost = waitDeductionCash;
                    }
                    oncePrice = oncePrice - cashCost;
                }
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), cashCost, presentAccount, 0);

                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                // 如果都是0，则不需要更改这张卡
                if (placeChainBillingCardCostDetail.getCostCash() > 0 || placeChainBillingCardCostDetail.getCostPresent() > 0) {
                    result.add(placeChainBillingCardCostDetail);
                }
               // result.add(placeChainBillingCardCostDetail);

                // 剩余扣费余额
                oncePrice = oncePrice - presentAccount;
            } else {
                // 当前门店奖励足够
                deductionPresent = oncePrice;
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), deductionCash, deductionPresent, 0);
                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                result.add(placeChainBillingCardCostDetail);
                return result;
            }
            return getChainShareCost(result, placeChainStoresList, billingCardList, billingCards, deductionOrder, oncePrice, deductionSource, loginId, billingCard.getPlaceId());
        } else {
            // 优先本金
            if (cashAccount < oncePrice) {
                // 当前门店本金不够
                int presentCost = 0;
                if (sumAccount(billingCards,0) < oncePrice) {
                    // 连锁所有的卡本金也不够
                    int waitDeductionPresent = oncePrice - sumAccount(billingCards,0);
                    if (presentAccount < waitDeductionPresent) {
                        presentCost = presentAccount;
                    } else {
                        presentCost = waitDeductionPresent;
                    }
                    oncePrice = oncePrice - presentCost;
                }
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), cashAccount, presentCost, 0);

                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                // 如果都是0，则不需要更改这张卡
                if (placeChainBillingCardCostDetail.getCostCash() > 0 || placeChainBillingCardCostDetail.getCostPresent() > 0) {
                    result.add(placeChainBillingCardCostDetail);
                }
                // result.add(placeChainBillingCardCostDetail);

                // 剩余扣费余额
                oncePrice = oncePrice - cashAccount;
            } else {
                // 当前门店本金足够
                deductionCash = oncePrice;
                PlaceChainBillingCardCostDetail placeChainBillingCardCostDetail = new PlaceChainBillingCardCostDetail(billingCard.getPlaceId(), billingCard.getCardId(), deductionCash, deductionPresent, 0);
                placeChainBillingCardCostDetail.setChainId(chainId);
                placeChainBillingCardCostDetail.setLoginId(loginId);
                result.add(placeChainBillingCardCostDetail);
                return result;
            }
            return getChainShareCost(result, placeChainStoresList, billingCardList, billingCards, deductionOrder, oncePrice, deductionSource, loginId, billingCard.getPlaceId());
        }
    }

    /**
     * 连锁扣费明细
     * @param result 扣费明细列表
     * @param placeChainStoresList 漫游配置信息
     * @param billingCardList 会员卡列表
     * @param billingCards 当前用户连锁总余额信息
     * @param deductionOrder 扣费顺序
     * @param oncePrice 一次扣费
     * @param deductionSource 扣费来源，区分定时器、111、其他
     * @param loginId 登入id
     * @param placeId 场所编号
     * @return
     */
    public List<PlaceChainBillingCardCostDetail> getChainShareCost (List<PlaceChainBillingCardCostDetail> result,
                                                                    List<PlaceChainStores> placeChainStoresList,
                                                                    List<BillingCard> billingCardList,
                                                                    List<BillingCard> billingCards,
                                                                    int deductionOrder,
                                                                    int oncePrice,
                                                                    int deductionSource,
                                                                    String loginId,
                                                                    String placeId) {
        billingCardList = billingCardList.stream().filter(e -> !placeId.equals(e.getPlaceId())).collect(Collectors.toList());
        if (billingCards.isEmpty()) {
            // 没有连锁卡
            return null;
        }

        // 对余额排序
        billingCardList = billingCardList.stream().sorted(Comparator.comparing(BillingCard::getTotalAccount)).collect(Collectors.toList());

        int waitDeductionCash = 0;
        int waitDeductionPresent = 0;
        // 处理剩余待扣漫游奖励
        if (deductionOrder == 1) {
            int presentAccount = 0;
            for (BillingCard bc : billingCardList) {
                for (PlaceChainStores pcs : placeChainStoresList) {
                    if (pcs.getPlaceId().equals(bc.getPlaceId())) {
                        if (pcs.getSharePresentAccount() == 1) {
                            presentAccount = presentAccount + bc.getPresentAccount();
                        }
                    }
                }
            }
            if (presentAccount < oncePrice) {
                waitDeductionCash = oncePrice - presentAccount;
            }
        }
        // 处理剩余待扣漫游本金
        if (deductionOrder == 0) {
            int cashAccount = 0;
            for (BillingCard bc : billingCardList) {
                for (PlaceChainStores pcs : placeChainStoresList) {
                    if (pcs.getPlaceId().equals(bc.getPlaceId())) {
                        if (pcs.getShareCashAccount() == 1) {
                            cashAccount = cashAccount + (bc.getCashAccount() + bc.getTemporaryOnlineAccount());
                        }
                    }
                }
            }
            if (cashAccount < oncePrice) {
                waitDeductionPresent = oncePrice - cashAccount;
            }
        }
        for (BillingCard card : billingCardList) {
            for (PlaceChainStores chainStores : placeChainStoresList) {
                if (card.getPlaceId().equals(chainStores.getPlaceId())) {
                    PlaceChainBillingCardCostDetail chainBillingCardCostDetail = new PlaceChainBillingCardCostDetail();
                    chainBillingCardCostDetail.setPlaceId(card.getPlaceId());
                    chainBillingCardCostDetail.setCardId(card.getCardId());
                    chainBillingCardCostDetail.setChainId(chainStores.getChainId());
                    chainBillingCardCostDetail.setLoginId(loginId);
                    if (deductionOrder == 2) {
                        // 只扣本金
                        if (chainStores.getShareCashAccount() == 1) {
                            // 本金漫游
                            if (card.getCashAccount() >= oncePrice) {
                                chainBillingCardCostDetail.setCostCash(oncePrice);
                                chainBillingCardCostDetail.setCostPresent(0);
                                result.add(chainBillingCardCostDetail);
                                return result;
                            }
                            // 当前本金不够
                            chainBillingCardCostDetail.setCostCash(card.getCashAccount());
                            oncePrice = oncePrice - card.getCashAccount(); // 剩余扣费金额
                        }
                        // 如果都是0，则不需要更改这张卡
                        if (chainBillingCardCostDetail.getCostCash() > 0 || chainBillingCardCostDetail.getCostPresent() > 0) {
                            result.add(chainBillingCardCostDetail);
                        }
                        break;
                    } else if (deductionOrder == 3) {
                        // 只扣奖励
                        if (chainStores.getSharePresentAccount() == 1) {
                            // 奖励漫游漫游
                            if (card.getPresentAccount() >= oncePrice) {
                                chainBillingCardCostDetail.setCostPresent(oncePrice);
                                result.add(chainBillingCardCostDetail);
                                return result;
                            }
                            // 当前奖励不够
                            chainBillingCardCostDetail.setCostPresent(card.getPresentAccount());
                            oncePrice = oncePrice - card.getPresentAccount(); // 剩余扣费金额
                        }
                        // 如果都是0，则不需要更改这张卡
                        if (chainBillingCardCostDetail.getCostCash() > 0 || chainBillingCardCostDetail.getCostPresent() > 0) {
                            result.add(chainBillingCardCostDetail);
                        }
                        break;
                    } else if (deductionOrder == 1) {
                        // 优先扣奖励
                        if (chainStores.getShareCashAccount() == 1 && waitDeductionCash > 0) {
                            // 本金漫游
                            if (card.getCashAccount() >= waitDeductionCash) {
                                chainBillingCardCostDetail.setCostCash(waitDeductionCash);
                                oncePrice = oncePrice - waitDeductionCash;
                                waitDeductionCash = 0;
                            } else {
                                // 当前本金不够
                                chainBillingCardCostDetail.setCostCash(card.getCashAccount());
                                oncePrice = oncePrice - card.getCashAccount();
                                waitDeductionCash = waitDeductionCash - card.getCashAccount(); // 剩余扣费金额
                            }
                        }

                        if (chainStores.getSharePresentAccount() == 1) {
                            // 奖励漫游漫游
                            if (card.getPresentAccount() >= oncePrice) {
                                chainBillingCardCostDetail.setCostPresent(oncePrice);
                                result.add(chainBillingCardCostDetail);
                                return result;
                            }
                            // 当前奖励不够
                            chainBillingCardCostDetail.setCostPresent(card.getPresentAccount());
                            oncePrice = oncePrice - card.getPresentAccount(); // 剩余扣费金额
                        }
                        // 如果都是0，则不需要更改这张卡
                        if (chainBillingCardCostDetail.getCostCash() > 0 || chainBillingCardCostDetail.getCostPresent() > 0) {
                            result.add(chainBillingCardCostDetail);
                        }
                        break;
                    } else {
                        // 优先扣本金
                        if (chainStores.getSharePresentAccount() == 1 && waitDeductionPresent > 0) {
                            // 奖励漫游
                            if (card.getPresentAccount() >= waitDeductionPresent) {
                                chainBillingCardCostDetail.setCostPresent(waitDeductionPresent);
                                oncePrice = oncePrice - waitDeductionPresent;
                                waitDeductionPresent = 0;
                            } else {
                                // 当前奖励不够
                                chainBillingCardCostDetail.setCostPresent(card.getPresentAccount());
                                oncePrice = oncePrice - card.getPresentAccount();
                                waitDeductionPresent = waitDeductionPresent - card.getPresentAccount(); // 剩余扣费金额
                            }
                        }

                        if (chainStores.getShareCashAccount() == 1) {
                            // 本金漫游
                            if (card.getCashAccount() >= oncePrice) {
                                chainBillingCardCostDetail.setCostCash(oncePrice);
                                result.add(chainBillingCardCostDetail);
                                return result;
                            }
                            // 当前奖励不够
                            chainBillingCardCostDetail.setCostCash(card.getCashAccount());
                            oncePrice = oncePrice - card.getCashAccount(); // 剩余扣费金额
                        }
                        // 如果都是0，则不需要更改这张卡
                        if (chainBillingCardCostDetail.getCostCash() > 0 || chainBillingCardCostDetail.getCostPresent() > 0) {
                            result.add(chainBillingCardCostDetail);
                        }
                        break;
                    }

                }
            }
        }

        // 待扣已扣完
        if (waitDeductionCash == 0 && waitDeductionPresent == 0 && oncePrice == 0 && !result.isEmpty()) {
            return result;
        }

        // 余额足够，在循环内部就已经return了，如果能走完循环，则说话余额不足,直接返回null.(定时器除外)
        if (deductionSource == 1 && (sumAccount(billingCards, 2) > 0 || !result.isEmpty())) {
            // 定时器这里不够一次扣费,账户清零，再上6分钟
            return result;
        }
        return null;
    }

}
