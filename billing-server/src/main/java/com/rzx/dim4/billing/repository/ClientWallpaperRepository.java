package com.rzx.dim4.billing.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.rzx.dim4.billing.entity.ClientWallpaper;

public interface ClientWallpaperRepository
		extends JpaRepository<ClientWallpaper, Long>, JpaSpecificationExecutor<ClientWallpaper> {

	Page<ClientWallpaper> findByOrderByIdDesc(Pageable pageable);

}