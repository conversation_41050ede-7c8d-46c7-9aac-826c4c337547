package com.rzx.dim4.billing.service.Invite;

import com.rzx.dim4.billing.entity.invite.LogInviteDeduction;
import com.rzx.dim4.billing.repository.invite.LogInviteDeductionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年04月27日 14:38
 */
@Slf4j
@Service
public class LogInviteDeductionService {

    @Autowired
    private LogInviteDeductionRepository inviteDeductionDetailRepository;

    public LogInviteDeduction save(LogInviteDeduction inviteDeductionDetail){
        return inviteDeductionDetailRepository.save(inviteDeductionDetail);
    }

    public List<LogInviteDeduction> findByPlaceIdAndInviteCode(String placeId, String inviteCode){
        return inviteDeductionDetailRepository.findByPlaceIdAndInviteCode(placeId, inviteCode);
    }
}
