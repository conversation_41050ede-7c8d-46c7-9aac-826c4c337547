package com.rzx.dim4.billing.service.Component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/11/9
 **/
@Slf4j
@Service
public class RabbitMQProvider {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * @see com.rzx.dim4.billing.service.Component.RabbitMQConsumer#receiveRefundDelayOneHour
     * @param msg
     */
    public void sendOneHourDelayQueue(RefundMessage msg) {
        // 投递的目标交换机
        String exchange = RabbitMQConfig.DELAY_EXCHANGE_B;
        // 路由键（通过路由键发送到哪个队列）
        String routeKey = RabbitMQConfig.DELAY_ROUTING_B;

        log.info("发送给延时队列 DELAY_EXCHANGE_B，消息内容: " + msg);

        rabbitTemplate.convertAndSend(exchange, routeKey, msg);
    }

    /**
     * @see com.rzx.dim4.billing.service.Component.RabbitMQConsumer#receiveRefundDelaySixHour
     * @param msg
     */
    public void sendSixHourDelayQueue(RefundMessage msg) {
        // 投递的目标交换机
        String exchange = RabbitMQConfig.DELAY_EXCHANGE_C;
        // 路由键（通过路由键发送到哪个队列）
        String routeKey = RabbitMQConfig.DELAY_ROUTING_C;

        log.info("发送给延时队列 DELAY_EXCHANGE_C，消息内容: " + msg);

        rabbitTemplate.convertAndSend(exchange, routeKey, msg);
    }
}
