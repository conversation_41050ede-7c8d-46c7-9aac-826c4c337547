package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingCardBlackList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface BillingCardBlackListRepository extends JpaRepository<BillingCardBlackList, Long>, JpaSpecificationExecutor<BillingCardBlackList> {

    Optional<BillingCardBlackList> findByPlaceIdAndCardId(String placeId, String cardId);
}
