package com.rzx.dim4.billing.service;

import org.springframework.stereotype.Service;

import com.rzx.dim4.base.utils.QiniuUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class QiniuAsyncService {

	public void upload(byte[] file, String key) {
		long now = System.currentTimeMillis();
		try {
			boolean result = QiniuUtils.upload(file, key);
			log.info("七牛异步上传" + (result ? "成功" : "失败") + "[" + key + "], 耗时:::" + (System.currentTimeMillis() - now));

		} catch (Exception e) {
			log.error("七牛异步上传异常,{}", e);
		}

	}

}