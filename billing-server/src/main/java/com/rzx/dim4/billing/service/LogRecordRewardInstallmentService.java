package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogRecordRewardInstallment;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.entity.TopupRule;
import com.rzx.dim4.billing.repository.LogQrCodeRepository;
import com.rzx.dim4.billing.repository.LogRecordRewardInstallmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024年05月30日 11:01
 */
@Slf4j
@Service
public class LogRecordRewardInstallmentService {

    @Autowired
    private LogRecordRewardInstallmentRepository logRecordRewardInstallmentRepository;

    @Autowired
    private TopupRuleService topupRuleService;


    public LogRecordRewardInstallment save(LogRecordRewardInstallment logRecordRewardInstallment) {
        if(ObjectUtils.isEmpty(logRecordRewardInstallment.getCreated())){
            logRecordRewardInstallment.setCreated(LocalDateTime.now());
        }
        return logRecordRewardInstallmentRepository.save(logRecordRewardInstallment);
    }


    /**
     * 将所有剩余赠送次数为0、状态为1 的任务状态改为已完成
     * @return
     */
    public int clearCompletedTasks( ) {
        return logRecordRewardInstallmentRepository.clearCompletedTasks();
    }

    /**
     * 修改单个场所单个用户的所有赠送数据状态
     * @return
     */
    public int updateStatusByPlaceIdAndIdNumber(int status,String placeId,String idNumber) {
        return logRecordRewardInstallmentRepository.updateStatusByPlaceIdAndIdNumber(status,placeId,idNumber);
    }


    /**
     * 根据id列表修改状态和剩余赠送次数
     * @return
     */
    public int updateStatusAndOncePresentAmortizedByIds(List<Long> ids) {
        return logRecordRewardInstallmentRepository.updateStatusAndOncePresentAmortizedByIds(ids);
    }


    /**
     * 根据状态查询所有未完成赠送记录
     * @param deleted
     * @param status
     * @return
     */
    public List<LogRecordRewardInstallment> findByDeletedAndStatus(int deleted,int status) {

        return logRecordRewardInstallmentRepository.findByDeletedAndStatus(deleted,status);
    }




    /**
     * 根据订单id查询分期赠送信息
     * @param orderId
     * @return
     */
    public Optional<LogRecordRewardInstallment> findByTopupOrderId(String orderId) {

        return logRecordRewardInstallmentRepository.findByTopupOrderIdAndDeleted(orderId,0);
    }



    /**
     * 根据id逻辑删除数据
     * @param id
     * @return
     */
    public int deletedById(Long id) {

        return logRecordRewardInstallmentRepository.deletedById(id);
    }


    /**
     * 退款时直接删除分期
     * @param orderId
     * @return
     */
    public int deletedByOrderId(String orderId) {

        return logRecordRewardInstallmentRepository.deletedByOrderId(orderId);
    }


    /**
     * 获取单次赠送金额
     * @param topupRule 充值规则
     * @return 返回单次赠送金额
     */
    public int getInstallmentAmount(TopupRule topupRule,int cashAmount){

        //如果没有奖励并且非冲多少送多少，直接返回0
        if(topupRule.getPresentAmount() <= 0 && topupRule.getTopupMode() != 1){
            return 0;
        }
        //判断如果是冲多少送多少,需要充值金额达到最低起送金额
        if(topupRule.getTopupMode() == 1 && cashAmount < topupRule.getAmount()){
            return 0;
        }
        //计算赠送金额
        int presentAmount = 0;
        if (topupRule.getPresentAmount() == 0 && topupRule.getTopupMode() == 1) {
            presentAmount = cashAmount;
        } else {
            presentAmount = topupRule.getPresentAmount();
        }

        //如果没有没有分期，直接返回赠送金额
        if(topupRule.getPresentAmortized() == 0){
            return presentAmount;
        }
        log.info("计算奖励单次分期赠送金额，场所：{}，充值规则：{}，充值金额：{},赠送金额：{}，分期次数：{}",topupRule.getPlaceId(),topupRule.getTopupRuleId(),cashAmount,
                topupRule.getPresentAmount(),topupRule.getPresentAmortized());

        //计算单次赠送金额（总赠送金额/分期次数），单位分（不保留小数点后位数字）
        int oncePresentAmount = presentAmount / topupRule.getPresentAmortized();

        return oncePresentAmount;
    }

    /**
     * 分期赠送-获取分期赠送对象（计算分期赠送单期的金额）
     * @param topupRule 充值规则
     * @param idNumber 会员证件信息
     * @param cashAmount 充值金额
     * @return 返回本次分期赠送对象
     */
    public LogRecordRewardInstallment topupRewardInstallment(TopupRule topupRule, String idNumber,int cashAmount){

        LogRecordRewardInstallment logRecordRewardInstallment = new LogRecordRewardInstallment();

        //如果没有奖励并且非冲多少送多少，直接返回0
        if(topupRule.getPresentAmount() <= 0 && topupRule.getTopupMode() != 1){
            logRecordRewardInstallment.setSumPresentAmount(0);
            logRecordRewardInstallment.setOncePresentAmount(0);
            return logRecordRewardInstallment;
        }
        //计算赠送金额
        int presentAmount = 0;
        if (topupRule.getPresentAmount() == 0 && topupRule.getTopupMode() == 1) {
            presentAmount = cashAmount;
        } else {
            presentAmount = topupRule.getPresentAmount();
        }

        //如果没有没有分期，直接返回赠送金额
        if(topupRule.getPresentAmortized() == 0){
            logRecordRewardInstallment.setSumPresentAmount(presentAmount);
            logRecordRewardInstallment.setOncePresentAmount(presentAmount);
            return logRecordRewardInstallment;
        }
        log.info("充值分期赠送金额，场所：{}，用户：{}，充值规则：{}，充值金额：{},赠送金额：{}，分期次数：{}",topupRule.getPlaceId(),idNumber,topupRule.getTopupRuleId(),cashAmount,
                topupRule.getPresentAmount(),topupRule.getPresentAmortized());

        //计算单次赠送金额（总赠送金额/分期次数），单位分（不保留小数点后位数字）
        int oncePresentAmount = presentAmount / topupRule.getPresentAmortized();

        logRecordRewardInstallment.setRuleId(topupRule.getTopupRuleId());
        logRecordRewardInstallment.setPlaceId(topupRule.getPlaceId());
        logRecordRewardInstallment.setIdNumber(idNumber);
        logRecordRewardInstallment.setSumPresentAmount(presentAmount); //总赠送金额
        logRecordRewardInstallment.setSumPresentAmortized(topupRule.getPresentAmortized()); //	总分期次数
        logRecordRewardInstallment.setOncePresentAmount(oncePresentAmount); // 单次赠送金额
        logRecordRewardInstallment.setOncePresentAmortized(topupRule.getPresentAmortized() - 1);//	剩余分期次数

        return logRecordRewardInstallment;
    }

    /**
     *
     * @param logTopup
     * @return
     */
    public LogRecordRewardInstallment addTopupRewardInstallment(LogTopup logTopup){
        //查询充值规则判断是否为分期赠送
        int presentAmortized = 0;
        LogRecordRewardInstallment logRecordRewardInstallment = new LogRecordRewardInstallment();
        logRecordRewardInstallment.setSumPresentAmount(logTopup.getPresentAmount());
        logRecordRewardInstallment.setOncePresentAmount(logTopup.getPresentAmount());
        logRecordRewardInstallment.setSumPresentAmortized(0);
        if(!StringUtils.isEmpty(logTopup.getTopupRuleId())){
            Optional<TopupRule> byPlaceIdAndTopupRuleId = topupRuleService.findByPlaceIdAndTopupRuleId(logTopup.getPlaceId(), logTopup.getTopupRuleId());
            if(byPlaceIdAndTopupRuleId.isPresent() && byPlaceIdAndTopupRuleId.get().getPresentAmortized() > 0){
                TopupRule topupRule = byPlaceIdAndTopupRuleId.get();
                presentAmortized =  topupRule.getPresentAmortized();
                log.info("根据充值记录写入分期赠送记录，充值记录id：{}，分期次数：{}",logTopup.getIdName(),presentAmortized);
                //写入分期赠送记录
                logRecordRewardInstallment.setRuleId(topupRule.getTopupRuleId());
                logRecordRewardInstallment.setPlaceId(topupRule.getPlaceId());
                logRecordRewardInstallment.setIdNumber(logTopup.getIdNumber());
                logRecordRewardInstallment.setTopupOrderId(logTopup.getOrderId());
                logRecordRewardInstallment.setStatus(1);

                if ( topupRule.getTopupMode() == 1) {
                    logRecordRewardInstallment.setSumPresentAmount(logTopup.getCashAmount()); //冲多少送多少总赠送金额
                } else {
                    logRecordRewardInstallment.setSumPresentAmount(topupRule.getPresentAmount()); //总赠送金额
                }

                logRecordRewardInstallment.setSumPresentAmortized(presentAmortized); //	总分期次数
                logRecordRewardInstallment.setOncePresentAmount(logTopup.getPresentAmount()); // 单次赠送金额
                logRecordRewardInstallment.setOncePresentAmortized(topupRule.getPresentAmortized() - 1);//	剩余分期次数
                save(logRecordRewardInstallment);
            }
        }
        return logRecordRewardInstallment;
    }

    /**
     * 会员删除时清除未赠送的奖励
     * @param placeIds 场所id列表
     * @param idNumber 证件号
     */
    public int deleteRewardInstallmentByPlaceIds(List<String> placeIds,String idNumber){
        return logRecordRewardInstallmentRepository.deleteRewardInstallment(placeIds,idNumber);
    }


}
