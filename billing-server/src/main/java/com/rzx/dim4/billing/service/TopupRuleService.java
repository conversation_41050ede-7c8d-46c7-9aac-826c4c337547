package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.TopupRuleBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.UpdateConfigBusinessBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.billing.entity.BillingCardType;
import com.rzx.dim4.billing.entity.TopupRule;
import com.rzx.dim4.billing.repository.BillingCardTypeRepository;
import com.rzx.dim4.billing.repository.TopupRuleRepository;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzx.dim4.billing.cons.BillingConstants.MEMBER_DAY;

/**
 * 充值规则
 *
 * <AUTHOR>
 * @date 2021年9月3日 下午2:44:48
 */
@Slf4j
@Service
public class TopupRuleService {

    @Autowired
    TopupRuleRepository topupRuleRepository;

    @Autowired
    BillingCardTypeRepository billingCardTypeRepository;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    NotifyServerService notifyServerService;

    /**
     * 查询所有充值规则
     */
    public List<TopupRule> findByPlaceId(String placeId) {
        return topupRuleRepository.findByPlaceIdAndDeleted(placeId, 0);
    }

    public List<TopupRule> findByPlaceIdAndCardTypeId(String placeId, String cardTypeId) {
        return topupRuleRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
    }

    public Optional<TopupRule> findByPlaceIdAndTopupRuleId(String placeId, String topupRuleId) {
        return topupRuleRepository.findByPlaceIdAndTopupRuleIdAndDeleted(placeId, topupRuleId, 0);
    }

    public ServiceCodes save(TopupRuleBO newRule) {
        ServiceCodes errCode = validateMemberDayEdit(newRule);
        if (errCode != null) {
            return errCode;
        }
        ServiceCodes codes = saveMemberDayTopupRule(newRule);
        if (codes == ServiceCodes.NO_ERROR) {
            GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(newRule.getPlaceId(), "", "", BusinessType.UPDATECONFIG);
            if (pollingBOGeneric.isResult()) {
                PollingBO pollingBO = pollingBOGeneric.getData().getObj();
                if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                    UpdateConfigBusinessBO bo = new UpdateConfigBusinessBO();
                    bo.setPlaceId(newRule.getPlaceId());
                    bo.setBusinessType(BusinessType.UPDATECONFIG);
                    bo.setBusinessId(pollingBO.getCashierBusinessId());
                    bo.setType(1);
                    // 保存收银台业务数据
                    notifyServerService.pushUpdateConfigBusinessData(bo);
                }
            }
        }
        return codes;
    }


    /**
     * <pre>
     *  func: 会员日/充多少送多少 新增/更新 校验
     *  pre:
     *  	校验1: 违反【充值金额-赠送金额】
     *  	校验2: 违反【充值金额 + 卡类型 + 日期】
     * </pre>
     *
     * @param newRule 输入规则
     * @return 错误码
     */
    private ServiceCodes validateMemberDayEdit(TopupRuleBO newRule) {

        String placeId = newRule.getPlaceId();
        String topupRuleId = newRule.getTopupRuleId();
        int amount = newRule.getAmount();
        int presentAmount = newRule.getPresentAmount();
        int topupMode = newRule.getTopupMode();//根据充值模式去判断怎么去重

        // 新增
        if (StringUtils.isEmpty(topupRuleId)) {
            Gson gson = new Gson();
            boolean amountExisted = amountPresentAmountExisted(newRule);
            boolean dateExisted = amountCardTypeDateExisted(newRule, null);

            // {校验1失败, 校验2成功} -> 校验成功
            return validateRule(amountExisted, dateExisted, topupMode);
        }
        // 修改
        if (!StringUtils.isEmpty(topupRuleId)) {
            Optional<TopupRule> optSelfRule = topupRuleRepository.findByPlaceIdAndTopupRuleIdAndDeleted(placeId, topupRuleId, 0);
            Optional<TopupRule> optNewAmountRule =
                    topupRuleRepository.findTop1ByPlaceIdAndAmountAndPresentAmountAndDeleted(
                            placeId, amount, presentAmount, 0);
            boolean newAmountExisted = optNewAmountRule.isPresent();
            assert optSelfRule.isPresent();
            TopupRule selfRule = optSelfRule.get();

            Pair<Integer, Integer> newAmountPair = Pair.of(amount, presentAmount);
            Pair<Integer, Integer> oldAmountPair = Pair.of(selfRule.getAmount(), selfRule.getPresentAmount());
            boolean changeAmount = !newAmountPair.equals(oldAmountPair);

            boolean amountExisted = changeAmount && newAmountExisted;
            boolean dateExisted = amountCardTypeDateExisted(newRule, selfRule);

            return validateRule(amountExisted, dateExisted, topupMode);
        }
        return ServiceCodes.BAD_PARAM;
    }

    /**
     * <pre>
     *  func: 校验 新增/修改的充值规则
     *  desc:
     *        {校验1失败, 校验2成功} -> 校验成功
     *        {校验1失败, 校验2失败} -> 校验失败
     *        {校验1成功, 校验2成功} -> 校验失败
     *        {校验1成功, 校验2成功} -> 校验成功
     * 	pre:
     * 		校验1: 违反【充值金额-赠送金额】
     * 		校验2: 违反【充值金额 + 卡类型 + 日期】
     * </pre>
     *
     * @param amountExisted 金额已存在
     * @param dateExisted   具体【卡类型-充值金额-日期】规则已存在
     * @return 校验结果，null 为校验成功
     */
    private ServiceCodes validateRule(boolean amountExisted, boolean dateExisted, int topUpMode) {
        // {校验1失败, 校验2成功} -> 校验成功
        if (amountExisted && !dateExisted) {
            return null;
        }
        // {校验1失败, 校验2失败} -> 校验失败
        if (amountExisted) {
            return topUpMode == 0 ? ServiceCodes.BILLING_TOPUP_MODE0_EXISTED
                    : ServiceCodes.BILLING_TOPUP_MODE1_EXISTED;
        }
        // {校验1成功, 校验2失败} -> 校验失败
        if (dateExisted) {
            return topUpMode == 0 ? ServiceCodes.BILLING_TOPUP_DATE_AMOUNT_CARD_TYPE_INVALID
                    : ServiceCodes.BILLING_TOPUP_DATE_AMOUNT_CARD_TYPE_INVALID;
        }
        // {校验1成功, 校验2成功} -> 校验成功
        return null;
    }

    private boolean amountPresentAmountExisted(TopupRuleBO newRule) {
        Optional<TopupRule> optNewRuleAmount = topupRuleRepository.
                findTop1ByPlaceIdAndAmountAndPresentAmountAndDeleted(
                        newRule.getPlaceId(),
                        newRule.getAmount(),
                        newRule.getPresentAmount(),
                        0);
        return optNewRuleAmount.isPresent();
    }

    private ServiceCodes saveMemberDayTopupRule(TopupRuleBO inputRule) {

        String placeId = inputRule.getPlaceId();
        String topupRuleId = inputRule.getTopupRuleId();

        // 更新 -> 先删除原有 MemberDayRow 记录
        deleteMemberDayTopupRule(placeId, topupRuleId, inputRule.getCardTypeIds());
//        deleteMemberDayTopupRule(placeId, topupRuleId);
        List<String> cardIds = Arrays.asList(inputRule.getCardTypeIds().split("\\s*,\\s*"));
        cardIds = cardIds.stream().distinct().collect(Collectors.toList());
        int topupRuleIdInt = Integer.parseInt(builderTopupRuleId(placeId));

        // cardIds -> cardId-Name map
        List<BillingCardType> cardTypeList = billingCardTypeRepository
                .findByPlaceIdAndDeletedAndCardTypeIdIn(placeId, 0, cardIds);
        Map<String, String> cardTypeId2NameMap = cardTypeList.stream().collect(
                Collectors.toMap(BillingCardType::getCardTypeId, BillingCardType::getTypeName));
        if (cardTypeId2NameMap.size() != cardIds.size()) {
            return ServiceCodes.BAD_PARAM;
        }

        int topupType = 1;
        if (inputRule.getTopupMode() == 1) {
            topupType = 3; //新老版本数据兼容
        }
        // 循环cardIds -> 生成数据库记录
        List<TopupRule> newTopupRules = new ArrayList<>(cardIds.size());
        for (String cardTypeId : cardIds) {
            TopupRule newTopupRule = new TopupRule();
            newTopupRule.setPlaceId(placeId);
            newTopupRule.setCardTypeId(cardTypeId);
            newTopupRule.setAmount(inputRule.getAmount());
            newTopupRule.setPresentAmount(inputRule.getPresentAmount());
            newTopupRule.setEffectiveDays(inputRule.getEffectiveDays());
            newTopupRule.setMobileUsage(inputRule.getMobileUsage());
            newTopupRule.setClientUsage(inputRule.getClientUsage());
            newTopupRule.setCashierUsage(inputRule.getCashierUsage()); //
            newTopupRule.setCashierFixed(inputRule.getCashierFixed()); // 当前版本，会员日充值规则不支持收银台固定金额赠送
            newTopupRule.setCreated(LocalDateTime.now());
            newTopupRule.setCardTypeName(cardTypeId2NameMap.get(cardTypeId));
            newTopupRule.setTopupRuleId(String.valueOf(topupRuleIdInt));
            newTopupRule.setTopupType(topupType);
            newTopupRule.setTopupMode(inputRule.getTopupMode());
            newTopupRule.setStatus(inputRule.getStatus());
            newTopupRule.setEffectiveWeekDays(inputRule.getEffectiveWeekDays());
            newTopupRule.setEffectiveMode(inputRule.getEffectiveMode());
            newTopupRule.setNewCardReward(inputRule.getNewCardReward());
            newTopupRule.setMemberRecharge(inputRule.getMemberRecharge());
            newTopupRule.setPresentAmortized(inputRule.getPresentAmortized());

            //20230826:如果是工作卡的卡类型充值规则数据，默认写删除标记，避免龙管家数据转换的时候增加“工作卡”卡类型充值规则数据
            if ("1002".equals(cardTypeId)) {
                newTopupRule.setDeleted(1);
            }
            topupRuleIdInt++;
            newTopupRules.add(newTopupRule);
        }
        topupRuleRepository.saveAll(newTopupRules);
        return ServiceCodes.NO_ERROR;
    }

    public ServiceCodes delete(TopupRuleBO deleteRule) {
        //如果是单个卡类型且日期没有则适用以下逻辑
//        topupRuleRepository.deleteById(deleteRule.getId());
//        return ServiceCodes.NO_ERROR;

        return deleteMemberDayTopupRule(deleteRule.getPlaceId(),
                deleteRule.getTopupRuleId(), deleteRule.getCardTypeIds());
    }

    /**
     * 删除会员日 MemberDayRow 记录。包含【会员日】、【充多少送多少】
     *
     * @param placeId     场所id
     * @param topupRuleId 充值规则id
     * @return 删除成功/失败 错误码
     */
    private ServiceCodes deleteMemberDayTopupRule(String placeId, String topupRuleId, String cardTypeIds) {

        // 将 原有 topupRuleId 对应的 MemberDayRow记录 即 [充值金额、赠送金额、生效日期] 规则列表 全部删除
        if (!StringUtils.isEmpty(topupRuleId)) {
//            List<String> cardIds = Arrays.asList(cardTypeIds.split("\\s*,\\s*"));
//            cardIds = cardIds.stream().distinct().collect(Collectors.toList());
            Optional<TopupRule> optRule = topupRuleRepository.findByPlaceIdAndTopupRuleIdAndDeleted(placeId, topupRuleId, 0);
            if (optRule.isPresent()) {
                TopupRule oldRule = optRule.get();
                int amount = oldRule.getAmount();
                int presentAmount = oldRule.getPresentAmount();
                String effectiveDays = oldRule.getEffectiveDays();
                String effectiveWeekDays = oldRule.getEffectiveWeekDays();
                int effectiveMode = oldRule.getEffectiveMode();
                int cashierUsage = oldRule.getCashierUsage();
                int clientUsage = oldRule.getClientUsage();
                int mobileUsage = oldRule.getMobileUsage();
                //循环卡类型列表，删除各个卡类型下的相同充值金额、赠送金额、生效日期规则列表
//                    cardIds.forEach(e->{
                // 获取 MemberDayRow记录
                List<TopupRule> oldRules = new ArrayList<>();
                // 根据不同的生效日期方式查询不同的数据记录
                if (effectiveMode == 0) {
                    oldRules = topupRuleRepository.findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndCashierUsageAndClientUsageAndMobileUsageAndDeleted(
                            placeId, amount, presentAmount, effectiveDays, cashierUsage, clientUsage, mobileUsage, 0);
                } else {
                    oldRules = topupRuleRepository.findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndCashierUsageAndClientUsageAndMobileUsageAndDeleted(
                            placeId, amount, presentAmount, effectiveWeekDays, cashierUsage, clientUsage, mobileUsage, 0);
                }

                topupRuleRepository.deleteAll(oldRules);
//                    });

                return ServiceCodes.NO_ERROR;
            }

        }
        return ServiceCodes.BAD_PARAM;
    }

    private ServiceCodes deleteMemberDayTopupRule2(String placeId, String topupRuleId) {

        // 将 原有 topupRuleId 对应的 MemberDayRow记录 即 [充值金额、赠送金额、生效日期] 规则列表 全部删除
        if (!StringUtils.isEmpty(topupRuleId)) {
            Optional<TopupRule> optRule = topupRuleRepository.findByPlaceIdAndTopupRuleIdAndDeleted(placeId, topupRuleId, 0);
            if (optRule.isPresent()) {
                TopupRule oldRule = optRule.get();
                int amount = oldRule.getAmount();
                int presentAmount = oldRule.getPresentAmount();
                String effectiveDays = oldRule.getEffectiveDays();
                String effectiveWeekDays = oldRule.getEffectiveWeekDays();
                int effectiveMode = oldRule.getEffectiveMode();

                // 获取 MemberDayRow记录
                List<TopupRule> oldRules = new ArrayList<>();
                // 根据不同的生效日期方式查询不同的数据记录
                if (effectiveMode == 0) {
                    oldRules = topupRuleRepository.findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndDeleted(
                            placeId, amount, presentAmount, effectiveDays, 0);
                } else {
                    oldRules = topupRuleRepository.findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndDeleted(
                            placeId, amount, presentAmount, effectiveWeekDays, 0);
                }

                topupRuleRepository.deleteAll(oldRules);
                return ServiceCodes.NO_ERROR;
            }
        }
        return ServiceCodes.BAD_PARAM;
    }

    /**
     * 计算会员日生效次数
     *
     * @param placeId
     * @param cardId
     * @param presentAmount
     * @param effectedTopupRule
     */
    public void updateMemberDayNum(String placeId, String cardId, int presentAmount, TopupRule effectedTopupRule) {
        if ((effectedTopupRule == null) && presentAmount > 0) {
            // 生效的会员日规则，计算次数并设置时间
            LocalDateTime now = LocalDateTime.now();
            String key = MEMBER_DAY + "_" + placeId + "_" + cardId;
            Duration duration = Duration.between(now, now.with(LocalTime.MAX));
            long diff = duration.toMillis() / 1000;
            stringRedisTemplate.opsForValue().increment(key, 1); // 如果没有则新增key，值为1，有则自增.
            stringRedisTemplate.expire(key, diff, TimeUnit.SECONDS);
        }
    }

    /**
     * 获取会员日生效次数
     *
     * @param placeId
     * @param cardId
     * @return
     */
    public String getMemberDayNum(String placeId, String cardId) {
        String key = MEMBER_DAY + "_" + placeId + "_" + cardId;
        if (stringRedisTemplate.hasKey(key)) {
            return stringRedisTemplate.opsForValue().get(key);
        }
        return null;
    }

    /**
     * 会员日生效次数获取充值规则
     *
     * @param memberDayNum
     * @param memberDayNum
     * @return
     */
    public Boolean getCheckMemberDayNum(String memberDayNum, int dayNumConfig) {
        // 空的就是首次充值
        if (StringUtils.isEmpty(memberDayNum)) {
            return false;
        }

        if (dayNumConfig > 0 && Integer.parseInt(memberDayNum) >= dayNumConfig) {
            return true;
        }

        return false;
    }

    /**
     * 通过充值金额匹配出符合条件的充值赠送规则，先查询是否有会员日充赠规则，无则查询普通充赠规则
     *
     * @param placeId    场所id
     * @param cardTypeId cardTypeId
     * @param amount     充值金额
     * @param type       充值场景：1：收银台，2：客户端，3：移动端
     * @return 匹配的充值赠送规则
     * 1、根据充值的金额去匹配，如果金额相等则直接返回
     * 2、如果金额不相等，则按充值金额从小到大的顺序去匹配，找到第一个充值金额大于规则的金额且后面没有小于充值金额的规则，返回该规则
     */
    public TopupRule getEffectedTopupRuleByTopupAmountNew(String placeId, String cardTypeId, int amount, int type) {

        //首先根据type类型获取所有小于等于充值金额的计费规则，按照充值金额从大到小，赠送金额从小到大排序
        List<TopupRule> allTypeRules = findTopupRulesNew(placeId, cardTypeId, type, amount);
        if (type == 1) { // 来次收银台的充值需要先判断是否是固定金额赠送，如果是直接返回
            for (int i = 0; i < allTypeRules.size(); i++) { // 只从普通计费规则中查找
                TopupRule topupRule = allTypeRules.get(i);
                if (topupRule.getCashierUsage() == 1) {
                    //如果是固定金额赠送且收银台场景应用已设置，只针对普通计费type为1的规则进行判断,,,,,现在不区分充值规则类型123了，这个判断条件可以去掉？
                    if (topupRule.getCashierFixed() == 1 && topupRule.getTopupMode() == 0) {
                        if (topupRule.getAmount() == amount) { // 如果是固定金额赠送，且充值金额与规则金额相等，同时符合当前日期则返回
                            String effectiveDays = "";
                            String todayDate = "";
                            if (topupRule.getEffectiveMode() == 0) {
                                effectiveDays = topupRule.getEffectiveDays();
                                todayDate = String.valueOf(LocalDateTime.now().getDayOfMonth());
                            } else {
                                //获取当前日期是星期几
                                Calendar cal = Calendar.getInstance();
                                cal.setTime(new Date());
                                int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
                                // 对DAY_OF_WEEK值为1是星期天的情况进行处理
                                if (weekIndex == 0) {
                                    weekIndex = 7;
                                }
                                effectiveDays = topupRule.getEffectiveWeekDays();
                                todayDate = String.valueOf(weekIndex);
                            }
                            List<String> effectiveDayList = convertCommaStr2List(effectiveDays);
                            if (effectiveDayList.contains(todayDate)) {
                                return topupRule;
                            }
                        }
                    }
                }
            }
        }

        // 判定充赠规则优先级: 1: 会员日，充多少送多少(topupType=3) 2: 会员日，充值赠送(topupType=2) 3: 普通，充值赠送(topupType=1)
        List<TopupRule> todayMatchRules = getTodayMatchTopupRules(allTypeRules);
        // {充赠规则列表，当前充值金额} -> 当前充值金额对应的赠送规则
        for (int i = 0; i < todayMatchRules.size(); i++) {
            TopupRule topupRule = todayMatchRules.get(i);
            if (amount >= topupRule.getAmount() && topupRule.getCashierFixed() == 0) {
                return topupRule;
            }
        }
        return null;
    }


    public TopupRule getEffectedTopupRuleByTopupAmount(String placeId, String cardTypeId, int amount, int isFromCashier) {

        List<TopupRule> allTypeRules = findTopupRules(placeId, cardTypeId, 1);

        List<TopupRule> type3Rules = new ArrayList<>();
        List<TopupRule> type2Rules = new ArrayList<>();
        List<TopupRule> type1Rules = new ArrayList<>();

        allTypeRules.forEach(rule -> {
            if (rule.getTopupType() == 1) {
                type1Rules.add(rule);
            } else if (rule.getTopupType() == 2) {
                type2Rules.add(rule);
            } else if (rule.getTopupType() == 3) {
                type3Rules.add(rule);
            }
        });

        if (isFromCashier == 1) { // 来次收银台的充值需要先判断是否是固定金额赠送，如果是直接返回
            for (int i = 0; i < type1Rules.size(); i++) { // 只从普通计费规则中查找
                TopupRule topupRule = type1Rules.get(i);
                if (topupRule.getCashierFixed() == 1) {
                    if (topupRule.getAmount() == amount) { // 如果是固定金额赠送，且充值金额与规则金额相等，则返回
                        return topupRule;
                    } else { // 否则从普通计费规则中删除该规则，防止干扰后面计算逻辑
                        type1Rules.remove(i);
                        i--;
                    }
                }
            }
        } else {
            for (int i = 0; i < allTypeRules.size(); i++) {
                TopupRule topupRule = allTypeRules.get(i);
                if (topupRule.getCashierFixed() == 1) {
                    allTypeRules.remove(i);
                    i--;
                }
            }
        }

        // 判定充赠规则优先级: 1: 会员日，充多少送多少(topupType=3) 2: 会员日，充值赠送(topupType=2) 3: 普通，充值赠送(topupType=1)
        List<TopupRule> todayMemberDayRules = getTodayMemberDayTopupRules(type3Rules);
        if (CollectionUtils.isEmpty(todayMemberDayRules)) {
            todayMemberDayRules = getTodayMemberDayTopupRules(type2Rules);
        }
        if (CollectionUtils.isEmpty(todayMemberDayRules)) {
            todayMemberDayRules = type1Rules;
        }
        todayMemberDayRules.sort(Comparator.comparing(TopupRule::getAmount));

        // {充赠规则列表，当前充值金额} -> 当前充值金额对应的赠送规则
        for (int i = 0; i < todayMemberDayRules.size(); i++) {
            TopupRule topupRule = todayMemberDayRules.get(i);
            if (amount >= todayMemberDayRules.get(i).getAmount() && todayMemberDayRules.get(i).getCashierFixed() == 0
                    && ((i + 1 == todayMemberDayRules.size()) || (amount < todayMemberDayRules.get(i + 1).getAmount()))) {
                return todayMemberDayRules.get(i);
            }
        }
        return null;
    }

    /**
     * {类型2 会员日充赠规则列表 || 类型3 会员日充赠规则列表} -> 今日生效的 充赠规则列表
     *
     * @param memberDayRules {类型2 会员日充赠规则 || 类型3 会员日充赠规则}
     * @return 今日生效的 类型2 或 类型3 充赠规则列表
     */
    public List<TopupRule> getTodayMemberDayTopupRules(List<TopupRule> memberDayRules) {
        if (CollectionUtils.isEmpty(memberDayRules)) {
            return Collections.emptyList();
        }
        List<TopupRule> todayRules = new ArrayList<>();
        for (TopupRule rule : memberDayRules) {
            String effectiveDays = rule.getEffectiveDays();
            List<String> effectiveDayList = convertCommaStr2List(effectiveDays);
            String todayDate = String.valueOf(LocalDateTime.now().getDayOfMonth());
            if (effectiveDayList.contains(todayDate)) {
                todayRules.add(rule);
            }
        }
        return todayRules;
    }

    /**
     * 今日生效的 充赠规则列表
     * 新版本只看日期是否匹配，只要日期匹配都返回
     *
     * @param allRules {只有两种模式的充值规则}
     * @return 今日生效的充赠规则列表
     */
    public List<TopupRule> getTodayMatchTopupRules(List<TopupRule> allRules) {
        if (CollectionUtils.isEmpty(allRules)) {
            return Collections.emptyList();
        }
        List<TopupRule> todayRules = new ArrayList<>();
        for (TopupRule rule : allRules) {
            //这里需要区分日期生效模式(生效模式：0-按月（默认全选），1-按周（默认全选）)
            String effectiveDays = "";
            String todayDate = "";
            if (rule.getEffectiveMode() == 0) {
                effectiveDays = rule.getEffectiveDays();
                todayDate = String.valueOf(LocalDateTime.now().getDayOfMonth());
            } else {
                //获取当前日期是星期几
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date());
                int weekIndex = cal.get(Calendar.DAY_OF_WEEK) - 1;
                // 对DAY_OF_WEEK值为1是星期天的情况进行处理
                if (weekIndex == 0) {
                    weekIndex = 7;
                }
                effectiveDays = rule.getEffectiveWeekDays();
                todayDate = String.valueOf(weekIndex);
            }
            List<String> effectiveDayList = convertCommaStr2List(effectiveDays);
            if (effectiveDayList.contains(todayDate)) {
                todayRules.add(rule);
            }
        }
        return todayRules;
    }

    //新版本使用查询所有充值规则列表，根据传入的参数showAll代表对应的适用场景
    //showAll：1代表查询收银台，2代表查询客户端，3代表查询移动端
    public List<TopupRule> findTopupRulesNews(String placeId, String cardTypeId, int showAll) {
        if (showAll == 1) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndCashierUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(placeId, cardTypeId, 1, 0, 1);
        } else if (showAll == 2) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndClientUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(placeId, cardTypeId, 1, 0, 1);

        } else if (showAll == 3) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndMobileUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(placeId, cardTypeId, 1, 0, 1);
        }
        return Collections.emptyList();
    }

    //老版本使用所有
    public List<TopupRule> findTopupRules(String placeId, String cardTypeId, int showAll) {
        if (showAll == 1) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
        }
        return topupRuleRepository.findByPlaceIdAndCardTypeIdAndLimitClientShowAndDeleted(placeId, cardTypeId, showAll, 0);
    }

    public List<TopupRule> findTopupRulesNew(String placeId, String cardTypeId, int type, int amount) {
        if (type == 1) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndCashierUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(placeId, cardTypeId, 1, 0, 1, 1, amount);
        }
        //如果传值为2，代表是去查询客户端生效的规则
        else if (type == 2) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndClientUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(placeId, cardTypeId, 1, 0, 1, 1, amount);
        }
        //如果传值为3，代表是去查询移动端生效的规则
        else if (type == 3) {
            return topupRuleRepository.findByPlaceIdAndCardTypeIdAndMobileUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(placeId, cardTypeId, 1, 0, 1, 1, amount);
        }
        return null;
    }

    public List<TopupRule> findAllByPlaceId(String placeId) {
        return topupRuleRepository.findByPlaceIdAndDeletedOrderByIdDesc(placeId, 0);
    }

    //根据不同场景查询所有充值规则：1收银台；2客户端；3移动端
    public List<TopupRule> findAllByPlaceIdWithType(String placeId, int type) {
        if (type == 1) {
            return topupRuleRepository.findByPlaceIdAndCashierUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(placeId, 1, 0, 1);
        }
        //默认返回
        return topupRuleRepository.findByPlaceIdAndDeletedOrderByIdDesc(placeId, 0);
    }

    /**
     * 检查 新规则 【卡类型-充值金额-日期】(去除自身)规则对集合 是否与已有记录相同，相同则存在冲突
     *
     * @param newRule  输入规则
     * @param selfRule 已有规则
     * @return 新规则 【卡类型-充值金额-日期】(去除自身)后，规则已在数据中中存在
     */
    private boolean amountCardTypeDateExisted(TopupRuleBO newRule, TopupRule selfRule) {
        Gson gson = new Gson();
        List<TopupRule> existedRules = topupRuleRepository
                .findByPlaceIdAndAmountAndPresentAmountAndDeletedAndEffectiveModeAndCardTypeIdIn(
                        newRule.getPlaceId(),
                        newRule.getAmount(),
                        newRule.getPresentAmount(),
                        0,
                        newRule.getEffectiveMode(),
                        convertCommaStr2List(newRule.getCardTypeIds()));

        if (CollectionUtils.isEmpty(existedRules)) {
            return false;
        }

        List<TopupRule> oldRules = new ArrayList<>();
        if (selfRule != null) {
            if (selfRule.getEffectiveMode() == 0) {
                oldRules = topupRuleRepository
                        .findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndDeleted(
                                selfRule.getPlaceId(),
                                selfRule.getAmount(),
                                selfRule.getPresentAmount(),
                                selfRule.getEffectiveDays(),
                                0
                        );
            } else {
                oldRules = topupRuleRepository
                        .findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndDeleted(
                                selfRule.getPlaceId(),
                                selfRule.getAmount(),
                                selfRule.getPresentAmount(),
                                selfRule.getEffectiveWeekDays(),
                                0
                        );
            }
        }

        // {已存在规则列表, 自身规则(修改规则时)} -> 已存在、自身【卡类型-日期】规则对集合
        Set<Pair<String, String>> existedCard2daysPairSet = new HashSet<>();
        Set<Pair<String, String>> selfCard2daysPairSet = new HashSet<>();
        for (TopupRule rule : existedRules) {
            existedCard2daysPairSet.addAll(getCard2dayPairSet(rule));
        }
        for (TopupRule rule : oldRules) {
            selfCard2daysPairSet.addAll(getCard2dayPairSet(rule));
        }

        String cardTypeIds = newRule.getCardTypeIds();
        String newRuleEffectiveDays = "";
        if (newRule.getEffectiveMode() == 0) {
            newRuleEffectiveDays = newRule.getEffectiveDays();
        } else {
            newRuleEffectiveDays = newRule.getEffectiveWeekDays();
        }
        List<String> newRuleCardIds = convertCommaStr2List(cardTypeIds);
        List<String> newRuleDays = convertCommaStr2List(newRuleEffectiveDays);

        // {新规则卡类型 * 新规则日期 减去 自身【卡类型-日期】规则对集合} -> 新【卡类型-日期】充值规则对集合
        List<Pair<String, String>> newCard2DayPairList = new ArrayList<>();
        for (String newRuleCardId : newRuleCardIds) {
            for (String newRuleDay : newRuleDays) {
                Pair<String, String> newRuleCard2DayPair = Pair.of(newRuleCardId, newRuleDay);
                newCard2DayPairList.add(newRuleCard2DayPair);
            }
        }
        newCard2DayPairList.removeAll(selfCard2daysPairSet);

        // {新规则对集合 去掉 自身规则对集合 后，与已存在规则元素相同} -> 存在冲突
        for (Pair<String, String> newRuleCard2DayPair : newCard2DayPairList) {
            if (existedCard2daysPairSet.contains(newRuleCard2DayPair)) {
                return true;
            }
        }
        return false;
    }

    private Set<Pair<String, String>> getCard2dayPairSet(TopupRule oneExistedRule) {
        Set<Pair<String, String>> card2dayPairSet = new HashSet<>();
        String cardTypeId = oneExistedRule.getCardTypeId();
        List<String> thisRuleDays = convertCommaStr2List(oneExistedRule.getEffectiveDays());
        for (String day : thisRuleDays) {
            card2dayPairSet.add(Pair.of(cardTypeId, day));
        }
        return card2dayPairSet;
    }

    /**
     * 字符串转List
     *
     * @param str
     * @return
     */
    private List<String> convertCommaStr2List(String str) {
        return new ArrayList<>(Arrays.asList(str.split("\\s*,\\s*")));
    }

    @Synchronized
    private String builderTopupRuleId(String placeId) {
        int topupRuleId = 4000;
        Optional<TopupRule> lastRuleOptional = topupRuleRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
        if (lastRuleOptional.isPresent()) {
            topupRuleId = Integer.parseInt(lastRuleOptional.get().getTopupRuleId()) + 1;
        }
        return String.valueOf(topupRuleId);
    }

}
