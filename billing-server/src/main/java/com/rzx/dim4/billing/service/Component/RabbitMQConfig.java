package com.rzx.dim4.billing.service.Component;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;


/**
 * 延时队列：延时 xx 秒。一个完整的延时队列，需要两组队列：一个是普通（设置了有效期的）队列，一个是死信队列，组合而成。
 * <p>
 * 注意：队列消息有效期设置字段x-message-ttl，单位：毫秒，假如一开始设置了一个有效期，启动了，现在要修改，无法直接在当前队列上更新。
 * 如果代码中修改了已存在队列的有效期，启动时会报错，新设置的值不会生效。
 * 解决方法：只能重新创建一个新的队列。
 *
 * <AUTHOR>
 * @see RabbitMQProvider 生产者
 * @see RabbitMQConsumer 消费者
 * <p>
 * 参考文档：https://www.cnblogs.com/xmf3628/p/********.html?spm=a2c6h.********.article-detail.9.6a1cf1890mlVRA
 * @since 2023/11/9
 **/
@Configuration
public class RabbitMQConfig {


    // 普通队列配置
    public static final String DELAY_EXCHANGE_B = "exchange.delay.B";
    public static final String DELAY_QUEUE_B = "queue.delay.B";
    public static final String DELAY_ROUTING_B = "routing.delay.B";
    public static final int DELAY_TIME_B = 1000 * 60 * 60; // 60分钟

    // 死信队列配置
    public static final String DLX_EXCHANGE_B = "exchange.dlx.B";
    public static final String DLX_QUEUE_ORDER_B = "queue.dlx.order.B";
    public static final String DLX_ROUTING_ORDER_B = "routing.dlx.order.B";

    // 普通队列配置
    public static final String DELAY_EXCHANGE_C = "exchange.delay.C";
    public static final String DELAY_QUEUE_C = "queue.delay.C";
    public static final String DELAY_ROUTING_C = "routing.delay.C";
    public static final int DELAY_TIME_C = 1000 * 60 * 60 * 6; // 6小时

    // 死信队列配置
    public static final String DLX_EXCHANGE_C = "exchange.dlx.C";
    public static final String DLX_QUEUE_ORDER_C = "queue.dlx.order.C";
    public static final String DLX_ROUTING_ORDER_C = "routing.dlx.order.C";

    // 死信延时队列B start//

    /**
     * 配置延时交换机
     *
     * @return DirectExchange
     */
    @Bean
    public DirectExchange delayExchangeB() {
        return new DirectExchange(DELAY_EXCHANGE_B);
    }

    /**
     * 配置延时队列
     *
     * @return Queue
     */
    @Bean
    public Queue delayQueueB() {
        // 设置死信发送至 dlx.exchange 交换机，设置路由键为 routing.dlx.payment.order
//        String dlxExchangeName = "dlx.exchange";
//        String routingKey = "routing.dlx.A";

        Map<String, Object> args = new HashMap<>();
        // 设置队列的延时属性， 60秒
        args.put("x-message-ttl", DELAY_TIME_B);
        args.put("x-dead-letter-exchange", DLX_EXCHANGE_B);
//        args.put("x-dead-letter-routing-key", DLX_ROUTING);
        args.put("x-dead-letter-routing-key", DLX_ROUTING_ORDER_B);
        return new Queue(DELAY_QUEUE_B, true, false, false, args);
    }

    /**
     * 绑定延时交换机和队列
     *
     * @return Binding
     */
    @Bean
    public Binding bindingDelayExchangeB() {
        return BindingBuilder.bind(delayQueueB()).to(delayExchangeB()).with(DELAY_ROUTING_B);
    }

    /**
     * 配置死信交换机
     *
     * @return DirectExchange
     */
    @Bean
    public DirectExchange dlxExchangeB() {
        return new DirectExchange(DLX_EXCHANGE_B);
    }

    /**
     * 配置死信队列
     *
     * @return Queue
     */
    @Bean
    public Queue dlxQueueOrderB() {
        return new Queue(DLX_QUEUE_ORDER_B, true, false, false);
    }

    /**
     * 绑定
     *
     * @return Binding
     */
    @Bean
    public Binding bindingDlxExchangeB() {
        return BindingBuilder.bind(dlxQueueOrderB()).to(dlxExchangeB()).with(DLX_ROUTING_ORDER_B);
    }
    // 死信延时队列B end//

    // 死信延时队列C start//

    /**
     * 配置延时交换机
     *
     * @return DirectExchange
     */
    @Bean
    public DirectExchange delayExchangeC() {
        return new DirectExchange(DELAY_EXCHANGE_C);
    }

    /**
     * 配置延时队列
     *
     * @return Queue
     */
    @Bean
    public Queue delayQueueC() {
        // 设置死信发送至 dlx.exchange 交换机，设置路由键为 routing.dlx.payment.order
//        String dlxExchangeName = "dlx.exchange";
//        String routingKey = "routing.dlx.A";

        Map<String, Object> args = new HashMap<>();
        // 设置队列的延时属性， 60秒
        args.put("x-message-ttl", DELAY_TIME_C);
        args.put("x-dead-letter-exchange", DLX_EXCHANGE_C);
//        args.put("x-dead-letter-routing-key", DLX_ROUTING);
        args.put("x-dead-letter-routing-key", DLX_ROUTING_ORDER_C);
        return new Queue(DELAY_QUEUE_C, true, false, false, args);
    }

    /**
     * 绑定延时交换机和队列
     *
     * @return Binding
     */
    @Bean
    public Binding bindingDelayExchangeC() {
        return BindingBuilder.bind(delayQueueC()).to(delayExchangeC()).with(DELAY_ROUTING_C);
    }

    /**
     * 配置死信交换机
     *
     * @return DirectExchange
     */
    @Bean
    public DirectExchange dlxExchangeC() {
        return new DirectExchange(DLX_EXCHANGE_C);
    }

    /**
     * 配置死信队列
     *
     * @return Queue
     */
    @Bean
    public Queue dlxQueueOrderC() {
        return new Queue(DLX_QUEUE_ORDER_C, true, false, false);
    }

    /**
     * 绑定
     *
     * @return Binding
     */
    @Bean
    public Binding bindingDlxExchangeC() {
        return BindingBuilder.bind(dlxQueueOrderC()).to(dlxExchangeC()).with(DLX_ROUTING_ORDER_C);
    }
    // 死信延时队列C end//
}
