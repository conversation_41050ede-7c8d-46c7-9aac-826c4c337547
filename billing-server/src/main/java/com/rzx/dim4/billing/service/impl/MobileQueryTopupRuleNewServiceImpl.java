package com.rzx.dim4.billing.service.impl;

import com.rzx.dim4.base.bo.billing.TopupRuleBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.TopupRule;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.TopupRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 查询计费卡充值规则
 * 
 * <AUTHOR>
 * @date Jul 27, 2020 3:57:51 PM
 */
@Service
public class MobileQueryTopupRuleNewServiceImpl{

	@Autowired
	TopupRuleService topupRuleService;


	public GenericResponse<ListDTO<TopupRuleBO>> queryTopupRulesNew(String placeId,  String cardTypeId) {

		List<TopupRule> allTypeRules = topupRuleService.findTopupRulesNews(placeId, cardTypeId, 3);
		// 对相同的充值金额和赠送金额进行去重
		List<TopupRule> rules = new ArrayList<>();
		rules = allTypeRules.stream().collect(
				Collectors.collectingAndThen(
						Collectors.toCollection(
//								() -> new TreeSet<>(Comparator.comparing(e -> e.getAmount()  + "#" + e.getPresentAmount()))), ArrayList::new));
								() -> new TreeSet<>(Comparator.comparing(e -> e.getAmount()  + "#" + e.getPresentAmount()   + "#" + e.getCardTypeId()  + "#" + e.getEffectiveMode()  + "#" + e.getTopupType() + "#" + e.getNewCardReward()))), ArrayList::new));

//		//判断过滤符合日期的充值规则列表
		List<TopupRule> todayMatchRules = topupRuleService.getTodayMatchTopupRules(rules);
		// 再次去重，过滤掉不同充值类型有相同的充值金额和赠送金额数据，解决老数据下同时有充值类型为1和2的在同一天生效的数据
		todayMatchRules = todayMatchRules.stream().collect(
				Collectors.collectingAndThen(
						Collectors.toCollection(
								() -> new TreeSet<>(Comparator.comparing(e -> e.getAmount()  + "#" + e.getPresentAmount()   + "#" + e.getCardTypeId()  + "#" + e.getNewCardReward()))), ArrayList::new));

		return new GenericResponse<>(new ListDTO<>(convert2VO(todayMatchRules)));
	}

	private List<TopupRuleBO> convert2VO(List<TopupRule> rules) {
		return rules.stream().map(TopupRule::toBO).sorted(
				Comparator.comparing(TopupRuleBO::getAmount).thenComparing(TopupRuleBO::getPresentAmount)).collect(Collectors.toList());
	}
//	private List<TopupRuleBO> convert2VO(List<TopupRule> rules) {
//		return rules.stream().map(TopupRule::toBO).collect(Collectors.toList());
//	}
}
