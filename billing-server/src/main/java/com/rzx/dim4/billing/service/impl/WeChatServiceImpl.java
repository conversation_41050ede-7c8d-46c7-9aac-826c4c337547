package com.rzx.dim4.billing.service.impl;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.payment.PaymentRequestBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.BizServer;
import com.rzx.dim4.base.enums.ClientBusinessIds;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.util.RegionChnCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023年05月25日 11:30
 */
@Service
@Slf4j
public class WeChatServiceImpl implements WeChatService {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    TopupRuleService topupRuleService;

    @Autowired
    PlaceBizConfigService placeBizConfigService;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    PaymentServerService paymentServerService;

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    BillingOnlineService billingOnlineService;

    @Autowired
    LogLoginService logLoginService;

    @Autowired
    RegionChnCodeUtil regionChnCodeUtil;

    @Override
    public GenericResponse<ObjDTO<PaymentResultBO>> createOrderPayByAppletOfWeChat(
            String requestTicket, String placeId, String idNumber, int amount, String openId, String appId) {
        log.info("createOrderPayByAppletOfWeChat");

        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber) || amount == 0) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        GenericResponse<ObjDTO<PlaceConfigBO>> responsePlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeId);

        if (responsePlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = responsePlaceConfig.getData().getObj();
        if (placeConfigBO.getOnlineTopup() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }
        GenericResponse<ObjDTO<PlaceProfileBO>> placeProfileResponse = placeServerService.findByPlaceId(placeId);
        if (placeProfileResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.GET_PLACE_INFO_FAIL);
        }
        PlaceProfileBO placeProfileBO = placeProfileResponse.getData().getObj();
        if (placeProfileBO.getIsRegistered() == 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_NOT_REGISTER_PAY_AMOUNT);
        }
        //查询卡类型，现在每种价格只支持设置一种卡类型
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
        if (!optBillingCard.isPresent()) {
            return new GenericResponse<>(ServiceCodes.GET_CARD_INFO_FAIL);
        }
        BillingCard billingCard = optBillingCard.get();
        if ("1000".equals(billingCard.getCardTypeId())) {
            return new GenericResponse<>(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
        }
        // 获取充值赠送规则
        TopupRule activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(), amount, 3);
        // 获取场所计费配置
//        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
//        int dayNumConfig = placeBizConfig.getMemberDayNum();
        // 会员日生效次数校验
        // 20230811注释取消改功能校验
//        if (!StringUtils.isEmpty(activedRule) && activedRule.getTopupType() > 1) {
//            String memberDayNum = topupRuleService.getMemberDayNum(placeId, billingCard.getCardId());
//            if (topupRuleService.getCheckMemberDayNum(memberDayNum, dayNumConfig)) {
//                return new GenericResponse<>(ServiceCodes.BILLING_MEMBER_DAY_TOPUP_OVER);
//            }
//        }

        // 创建支付订单
        requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(amount);
        requestBO.setOrderDesc(placeProfileBO.getDisplayName() + "网费充值" + (amount / 100.00) + "元");
        requestBO.setStoreNo(placeId);
        requestBO.setOpenId(openId);
        requestBO.setPayType(PayType.WECHAT_MINIAPP.name());
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setBizType("chongzhi");
        requestBO.setIdNumber(idNumber);
        requestBO.setPlaceId(placeId);
        requestBO.setPayAppId(appId);

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));
        requestBO.setBusinessId(ClientBusinessIds.WECHAT_MINI_APP.getCode());

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);
        if (!response.isResult()) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();

        // || StringUtils.isEmpty(paymentResultBO.getPayUrl())  //小程序支付不需要payUrl
        if (paymentResultBO == null || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            return new GenericResponse<>(ServiceCodes.PAYMENT_CREATE_FAIL);
        }


        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());
        logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
        logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
        logTopup.setCashAmount(amount);

        if (activedRule == null) {
            logTopup.setPresentAmount(0);
        } else {
            if (activedRule.getTopupMode() == 1 && amount >= activedRule.getAmount()) {
                logTopup.setPresentAmount(amount);
            } else {
                logTopup.setPresentAmount(activedRule.getPresentAmount()); // 赠送金额
            }
        }
        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);
        logTopup.setPayType(PayType.WECHAT_MINIAPP);
        logTopup.setSourceType(SourceType.MINIAPP);
        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setStatus(0);
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);
        logTopup.setPayUrl(paymentResultBO.getPayUrl());

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        log.info("保存充值日志, ={}", new Gson().toJson(logTopup));

        logTopupService.save(logTopup);

        return new GenericResponse<>(new ObjDTO<>(paymentResultBO));
    }
}
