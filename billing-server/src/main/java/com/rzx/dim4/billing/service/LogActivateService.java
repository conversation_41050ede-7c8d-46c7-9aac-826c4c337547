package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogActivate;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.repository.LogActivateRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class LogActivateService {

    @Autowired
    LogActivateRepository logActivateRepository;

    public LogActivate save(LogActivate logActivate) {
        return logActivateRepository.save(logActivate);
    }

    private LogActivate addLogActivate(OperationType operationType,
                                            SourceType sourceType,
                                            BillingCard billingCard,
                                            LogShift logShift,
                                            String remark,
                                            String details) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(operationType, "expenseType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        LocalDateTime now = LocalDateTime.now();
        LogActivate logActivate = new LogActivate();
        logActivate.setCreated(now.minusNanos(now.getNano())); // 不要毫秒
        logActivate.setSourceType(sourceType);
        logActivate.setOperationType(operationType);
        logActivate.setPlaceId(billingCard.getPlaceId());
        logActivate.setCardId(billingCard.getCardId());
        logActivate.setIdNumber(billingCard.getIdNumber());
        logActivate.setIdName(billingCard.getIdName());
        logActivate.setCardId(billingCard.getCardId());
        logActivate.setCardTypeId(billingCard.getCardTypeId());
        logActivate.setCardTypeName(billingCard.getCardTypeName());
        // 前端线上余额是cashBalance + presentBalance
        if (billingCard.getTemporaryOnlineAccount() > 0) {
            logActivate.setCashBalance(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount());
        } else {
            logActivate.setCashBalance(billingCard.getCashAccount());
        }
        logActivate.setPresentBalance(billingCard.getPresentAccount());
        logActivate.setOnlineAccount(billingCard.getTemporaryOnlineAccount());
        logActivate.setRemark(remark);
        logActivate.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        logActivate.setCreaterName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        logActivate.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logActivate.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
        logActivate.setDetails(details);

        logActivateRepository.save(logActivate);
        return logActivate;
    }

    public Page<LogActivate> findAll(Map<String, String> map, Pageable pageable) {
        return logActivateRepository.findAll(new Specification<LogActivate>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<LogActivate> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> andPredicateList = new ArrayList<>();
                List<Predicate> orPredicateList = new ArrayList<>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 网吧id
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
                }
                // 用户卡id
                if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {
                    andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
                }
                // 用户卡身份证号
                if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
                    andPredicateList.add(cb.equal(root.get("idNumber").as(String.class), map.get("idNumber")));
                }
                // 班次
                if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {
                    andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
                }
                // 操作开始时间
                if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                    LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
                    andPredicateList.add(cb.greaterThanOrEqualTo(root.get("refundTime").as(LocalDateTime.class), startTime));
                }
                // 操作结束时间
                if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                    LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
                    andPredicateList.add(cb.lessThanOrEqualTo(root.get("refundTime").as(LocalDateTime.class), endTime));
                }

                Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
                if (orPredicateList.size() == 0) {
                    return cb.and(andPredicateList.toArray(andPredicateArr));
                } else {
                    Predicate[] orPredicateArr = new Predicate[orPredicateList.size()];
                    Predicate andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));
                    Predicate orPredicate = cb.or(orPredicateList.toArray(orPredicateArr));
                    return cb.and(andPredicate, orPredicate);
                }
            }
        }, pageable);
    }



    /**
     * 操作记录 - 激活
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param activeType  激活方式
     */
    public void addLogActivateCard(SourceType sourceType, BillingCard billingCard, LogShift logShift,
                                         String activeType) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");

        String desc = "";
        String details = activeType != null
                ? Objects.requireNonNull(ActiveType.getActiveTypes(Integer.parseInt(activeType))).getDisplay() + "激活"
                : "刷身份证/手输身份证";

        addLogActivate(OperationType.ACTIVATE_CARD, sourceType, billingCard, logShift, desc,
                details);
    }


    /**
     * 操作记录 - 取消激活
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addCancelLogActivate(SourceType sourceType, BillingCard billingCard, LogShift logShift) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");

        String desc = "";
        String details = "取消激活";

        addLogActivate(OperationType.CANCEL_ACTIVATE, sourceType, billingCard, logShift, desc,
                details);
    }


    /**
     * 查询最近一条包时的实际开始时间--id卡
     *
     * @param placeId
     * @param cardId
     * @param operationType
     * @return
     */
    public Optional<LogActivate> findTop1ByPlaceIdAndCardIdAndOperationType(String placeId, String cardId,
                                                                             OperationType operationType) {
        LocalDateTime startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        LocalDateTime endDateTime = LocalDateTime.now();
        return logActivateRepository.findTop1ByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, cardId,
                operationType, startDateTime, endDateTime);
    }


    /**
     * 查询最近一条激活日志表的数据
     * @param placeId
     * @param idNumber
     * @param operationType
     * @param startDateTime
     * @return
     */
    public Optional<LogActivate> findTop1ByPlaceIdAndIdNumberAndOperationTypeOrderByIdDesc(String placeId, String idNumber,
                                                                                              OperationType operationType, LocalDateTime startDateTime) {
        LocalDateTime endDateTime = LocalDateTime.now();
        return logActivateRepository.findTop1ByPlaceIdAndIdNumberAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber,
                operationType, startDateTime, endDateTime);
    }

    /**
     * 通过激活日志表，处理离线结账查询最近一条包时的实际开始时间--证件号
     * 和findTop1ByPlaceIdAndIdNumberAndOperationTypeOrderByIdDesc方法的区别是，参数不传时间
     * @param placeId
     * @param idNumber
     * @return
     */
    public Optional<LogActivate> findTop1ByPlaceIdAndIdNumberAndOperationTypeOrderByIdDescNoTime(String placeId, String idNumber,
                                                                                              OperationType operationType) {
        LocalDateTime  startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        LocalDateTime endDateTime = LocalDateTime.now();
        return logActivateRepository.findTop1ByPlaceIdAndIdNumberAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber,
                operationType, startDateTime, endDateTime);
    }
}
