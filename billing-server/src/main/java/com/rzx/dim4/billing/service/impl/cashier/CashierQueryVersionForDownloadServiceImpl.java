package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.ClientUpgrade;
import com.rzx.dim4.billing.entity.ClientVersion;
import com.rzx.dim4.billing.service.ClientUpgradeService;
import com.rzx.dim4.billing.service.ClientVersionService;
import com.rzx.dim4.billing.service.CoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 获取场所客户端最新版本下载信息
 */
@Service
public class CashierQueryVersionForDownloadServiceImpl implements CoreService {

    @Autowired
    private ClientVersionService clientVersionService;

    @Autowired
    private ClientUpgradeService clientUpgradeService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() != 2) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        String placeId = params.get(0);
        String clientType = params.get(1);  //类型：0客户端、1收银台

        //根据客户端类型查询该网吧最新的版本信息
        Optional<ClientUpgrade> cashierUpgradeOpt = clientUpgradeService.findByPlaceIdAndClientType(placeId, clientType);
        if (!cashierUpgradeOpt.isPresent()) {
            return new GenericResponse(ServiceCodes.BILLING_UPDATE_VERSION_NOT_FOUND);
        }
        ClientUpgrade cashierUpgrade = cashierUpgradeOpt.get();
        Optional<ClientVersion> cashierVersionOpt = clientVersionService.findByVersionId(cashierUpgrade.getVersionId());
        if (!cashierVersionOpt.isPresent()) {
            return new GenericResponse(ServiceCodes.BILLING_UPDATE_VERSION_NOT_FOUND);
        }
        ClientVersion cashierVersion = cashierVersionOpt.get();

        return new GenericResponse<>(new ObjDTO<>(cashierVersion.toBO()));
    }
}
