package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.BillingCardBlackList;
import com.rzx.dim4.billing.repository.BillingCardBlackListRepository;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class BillingCardBlackListService {

    @Autowired
    BillingCardBlackListRepository billingCardBlackListRepository;

    public BillingCardBlackList save (BillingCardBlackList billingCardBlackList) {
        return billingCardBlackListRepository.save(billingCardBlackList);
    }

    public Optional<BillingCardBlackList> findByPlaceIdAndCardId (String placeId, String cardId) {
        return billingCardBlackListRepository.findByPlaceIdAndCardId(placeId,cardId);
    }

    public void delete (BillingCardBlackList billingCardBlackList) {
        billingCardBlackListRepository.delete(billingCardBlackList);
    }

    public Page<BillingCardBlackList> findAll(Map<String, String> map, Pageable pageable) {
        return billingCardBlackListRepository.findAll(new Specification<BillingCardBlackList>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<BillingCardBlackList> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> andPredicateList = new ArrayList<>();
                // 场所
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
                }

                // 身份证号码
                if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
                    andPredicateList.add(cb.equal(root.get("idNumber").as(String.class), map.get("idNumber")));
                }

                // 姓名
                if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 是否查询在线订单
                    andPredicateList.add(cb.like(root.get("idName"), "%" + map.get("idName") + "%"));
                }

                Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
                return cb.and(andPredicateList.toArray(andPredicateArr));
            }
        }, pageable);

    }

}
