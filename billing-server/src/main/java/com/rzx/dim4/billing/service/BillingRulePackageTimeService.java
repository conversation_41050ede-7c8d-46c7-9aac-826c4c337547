package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.bo.user.MiniApp.InternetFeePackageSearchBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.entity.BillingRulePackageTime;
import com.rzx.dim4.billing.entity.LogOperation;
import com.rzx.dim4.billing.entity.LogRoom;
import com.rzx.dim4.billing.entity.invite.InviteOnline;
import com.rzx.dim4.billing.repository.BillingRulePackageTimeRepository;
import com.rzx.dim4.billing.repository.LogOperationRepository;
import com.rzx.dim4.billing.service.Invite.InviteOnlineService;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.sql.Time;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.rzx.dim4.base.utils.DateTimeUtils.*;

/**
 * 
 * <AUTHOR>
 * @date 2022年1月13日 上午11:05:09
 */
@Service
@Slf4j
public class BillingRulePackageTimeService {

	@Autowired
	BillingRulePackageTimeRepository billingRulePackageTimeRepository;

	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Autowired
	LogOperationRepository logOperationRepository;

	@Autowired
	LogRoomService logRoomService;

	@Autowired
	InviteOnlineService inviteOnlineService;

	/**
	 * 分页查询包时计费
	 *
	 * @param map
	 * @param pageable
	 * @return
	 */
	public Page<BillingRulePackageTime> findAll(Map<String, String> map, Pageable pageable) {
		return billingRulePackageTimeRepository.findAll(new Specification<BillingRulePackageTime>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingRulePackageTime> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

				List<Predicate> predicates = new ArrayList<Predicate>();

				Predicate p1 = cb.equal(root.get("deleted"), 0);
				Predicate p2 = cb.notEqual(root.get("packageFlag"), 9);
				Predicate p3 = cb.equal(root.get("couponRule"), 0);
				predicates.add(p1);
				predicates.add(p2);
				predicates.add(p3);

				// 场所ID
				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					Predicate predicate = cb.equal(root.get("placeId").as(String.class), map.get("placeId"));
					predicates.add(predicate);
				}

				// 包时类型
				if (map.containsKey("packageFlag") && !StringUtils.isEmpty(map.get("packageFlag"))) {
					Predicate predicate = cb.equal(root.get("packageFlag").as(String.class), map.get("packageFlag"));
					predicates.add(predicate);
				}
				if (map.containsKey("forbidden") && !StringUtils.isEmpty(map.get("forbidden"))) {
					Predicate predicate = cb.equal(root.get("forbidden").as(String.class), map.get("forbidden"));
					predicates.add(predicate);
				}
				if (map.containsKey("packageFlag") && !StringUtils.isEmpty(map.get("packageFlag"))) {
					Predicate predicate = cb.equal(root.get("packageFlag").as(String.class), map.get("packageFlag"));
					predicates.add(predicate);
				}

				if (map.containsKey("areaId") && !StringUtils.isEmpty(map.get("areaId"))) {
					Predicate predicate = cb.like(root.get("areaIds").as(String.class), "%" + map.get("areaId") + "%");
					predicates.add(predicate);
				}

				if (map.containsKey("ruleName") && !StringUtils.isEmpty(map.get("ruleName"))) {
					Predicate predicate = cb.like(root.get("ruleName").as(String.class), "%" + map.get("ruleName") + "%");
					predicates.add(predicate);
				}

				if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {
					Predicate predicate = cb.like(root.get("cardTypeIds").as(String.class), "%" + map.get("cardTypeId") + "%");
					predicates.add(predicate);
				}

				if (map.containsKey("limitSalesTerminal") && !StringUtils.isEmpty(map.get("limitSalesTerminal"))) {
					Predicate predicate = cb.like(root.get("limitSalesTerminal").as(String.class), "%" + map.get("limitSalesTerminal") + "%");
					predicates.add(predicate);
				}

				Predicate[] p = new Predicate[predicates.size()];
				return cb.and(predicates.toArray(p));
			}
		}, pageable);
	}

	/**
	 * 查询该规则详细信息
	 * @param placeId
	 * @param ruleId
	 * @return
	 */
	public Optional<BillingRulePackageTime> findByPlaceIdAndRuleId(String placeId, String ruleId) {
		return billingRulePackageTimeRepository.findByPlaceIdAndRuleIdAndDeleted(placeId, ruleId, 0);
	}

	/**
	 * 查询包时规则
	 */
	public List<BillingRulePackageTime> findPackageRule(InternetFeePackageSearchBO paramsBo) {
		Specification<BillingRulePackageTime> specification = new Specification<BillingRulePackageTime>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingRulePackageTime> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
				List<Predicate> predicates = new ArrayList<>();

				predicates.add(cb.equal(root.get("deleted").as(int.class), 0));
				predicates.add(cb.equal(root.get("forbidden").as(int.class), 0));
				predicates.add(cb.equal(root.get("placeId").as(String.class), paramsBo.getPlaceId()));

				if (paramsBo.getPackageFlag() != null) {
					predicates.add(cb.equal(root.get("packageFlag").as(int.class), paramsBo.getPackageFlag()));
				}
				predicates.add(cb.equal(root.get("limitClientShow"), "0"));

				query.where(cb.and(predicates.toArray(new Predicate[0])));
				query.orderBy(cb.desc(root.get("packageFlag").as(int.class)), cb.asc(root.get("startTime").as(int.class)), cb.asc(root.get("endTime").as(int.class)));
				return query.getRestriction();
			}
		};
		return billingRulePackageTimeRepository.findAll(specification);
	}

	/**
	 * 查询包时规则
	 * @param map
	 * @return
	 */
	public List<BillingRulePackageTime> findPackageRuleByPlaceIdAndAreaIdAndCardTypeId(Map<String, String> map) {
		Specification<BillingRulePackageTime> specification = new Specification<BillingRulePackageTime>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingRulePackageTime> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
				List<Predicate> predicates = new ArrayList<Predicate>();

				Predicate p1 = cb.equal(root.get("deleted"), 0);
				predicates.add(p1);
				if (!map.containsKey("ruleId")) {
					//过滤掉优惠券的包时规则
					predicates.add(cb.equal(root.get("couponRule"), 0));
				}

				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					predicates.add(cb.equal(root.get("placeId"), map.get("placeId")));
				}
				if (map.containsKey("areaId") && !StringUtils.isEmpty(map.get("areaId"))) {
					predicates.add(cb.like(root.get("areaIds"), "%" + map.get("areaId") + "%"));
				}
				if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {
					predicates.add(cb.like(root.get("cardTypeIds"), "%" + map.get("cardTypeId") + "%"));
				}
				if (map.containsKey("limitClientShow") && !StringUtils.isEmpty(map.get("limitClientShow"))) {
					predicates.add(cb.equal(root.get("limitClientShow"), map.get("limitClientShow")));
				}

				query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
				query.orderBy(cb.desc(root.get("packageFlag").as(int.class)),
						cb.asc(root.get("startTime").as(int.class)),
						cb.asc(root.get("endTime").as(int.class)));
				return query.getRestriction();
			}
		};
		return billingRulePackageTimeRepository.findAll(specification);
	}


	/**
	 * 构建规则Id(Id为偶数则是包时规则)
	 * @param placeId
	 * @return
	 */
	@Synchronized
	public String builderRuleId(String placeId) {
		int ruleId = 3000;
		Optional<BillingRulePackageTime> lastRule = billingRulePackageTimeRepository.findTop1ByPlaceIdOrderByIdDesc(placeId);
		if (lastRule.isPresent()) {
			ruleId = Integer.parseInt(lastRule.get().getRuleId()) + 2;
		}
		return String.valueOf(ruleId);
	}

	/**
	 * 保存
	 * @param billingRulePackageTime
	 * @return
	 */
	public BillingRulePackageTime save(BillingRulePackageTime billingRulePackageTime){
		return billingRulePackageTimeRepository.save(billingRulePackageTime);
	}

	/**
	 * 批量 保存/修改
	 * @param billingRulePackageTimes
	 * @return
	 */
	public List<BillingRulePackageTime> batchSave (List<BillingRulePackageTime> billingRulePackageTimes){
		return billingRulePackageTimeRepository.saveAll(billingRulePackageTimes);
	}

	/**
	 * 删除费率
	 * @param billingRulePackageTime
	 */
	public void delete(BillingRulePackageTime billingRulePackageTime) {
		billingRulePackageTimeRepository.delete(billingRulePackageTime);
	}

	public List<BillingRulePackageTime> findByPlaceIdOrderByIdDesc (String placeId) {
		return billingRulePackageTimeRepository.findByPlaceIdAndDeletedAndCouponRuleOrderByIdDesc(placeId, 0,0);
	}

	public List<BillingRulePackageTime> findByPlaceIdAndAreaIdOrderByIdDesc (String placeId, String areaId) {
		return billingRulePackageTimeRepository.findByPlaceIdAndAreaIdsLikeAndDeletedAndCouponRuleOrderByIdDesc(placeId,  areaId,0,0);
	}

	public List<BillingRulePackageTime> findByPlaceIdAndRuleIdInAndDeleted (String placeId,List<String> ruleIds) {
		return billingRulePackageTimeRepository.findByPlaceIdAndRuleIdInAndDeleted(placeId, ruleIds,0);
	}

	public Long deleteByPlaceId (String placeId) {
		return billingRulePackageTimeRepository.deleteByPlaceId(placeId);
	}

	public int updateForbiddenByPlaceIdAndRuleId(String placeId, String ruleId, String forbidden){
		return billingRulePackageTimeRepository.updateForbiddenByPlaceIdAndRuleId(placeId,ruleId,forbidden);
	}

	/**
	 * 包时校验参数
	 * @param requestTicket
	 */
	public void checkPackageTimeParam (String requestTicket,
									   String placeId,
									   String cardId,
									   String ruleId,
									   String sourceTypeStr) {

		if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket))) {
			log.info("checkPackageTimeParam 防重复请求拦截：redis 没有相应的 requestTicket={}，请求被拦截", requestTicket);
			throw new ServiceException(ServiceCodes.NO_TICKET);
		}

		if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardId) || StringUtils.isEmpty(ruleId) || StringUtils.isEmpty(sourceTypeStr)) {
			log.info("checkPackageTimeParam 必要参数为空;{},{},{},{}", placeId, cardId, ruleId, sourceTypeStr);
			throw new ServiceException(ServiceCodes.NULL_PARAM);
		}

		// 请客上网被邀请人不支持
		Optional<InviteOnline> inviteOnlineOpt = inviteOnlineService.findByPlaceIdAndCardIdAndTypeAndStatusNot(placeId, cardId, 1, 2);
		if (inviteOnlineOpt.isPresent()) {
			log.info("checkPackageTimeParam 被邀请人不支持包时;{},{},{}", placeId, cardId, inviteOnlineOpt.get().getInviteCode());
			throw new ServiceException(ServiceCodes.BILLING_INVITE_PACKAGE_NOT_SUPPORT);
		}

		try {
			// 将字符串转换为 int 类型
			int numberRuleId = Integer.parseInt(ruleId);
			// 判断是否为偶数
			if (numberRuleId % 2 != 0) {
				log.info("checkPackageTimeParam ruleId 不是包时规则类型值;{}",  ruleId);
				throw new ServiceException(ServiceCodes.BAD_PARAM);
			}
		} catch (NumberFormatException e) {
			log.info("checkPackageTimeParam ruleId 参数格式错误;{}",  ruleId);
			throw new ServiceException(ServiceCodes.BAD_PARAM);
		}

		// 校验来源
		if (!SourceType.hasName(sourceTypeStr)) {
			log.info("checkPackageTimeParam sourceTypeStr 参数格式错误;{}",  sourceTypeStr);
			throw new ServiceException(ServiceCodes.BAD_PARAM);
		}
	}

	/**
	 * 校验生效卡类型、销售终端、销售时间段
	 * @param billingRulePackageTime
	 * @param cardTypeId
	 * @param sourceTypeStr
	 */
	public void checkPackageTimeAvailable (BillingRulePackageTime billingRulePackageTime,
										   String cardTypeId,
										   String sourceTypeStr) {

		// 校验卡类型
		if (!billingRulePackageTime.getCardTypeIds().contains(cardTypeId)) {
			log.info("checkPackageTimeAvailable 包时规则不支持该卡类型;{}",  cardTypeId);
			throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
		}

		// 校验销售终端
		if (!billingRulePackageTime.getLimitSalesTerminal().contains(sourceTypeStr)) {
			log.info("checkPackageTimeAvailable 包时规则不支持该卡终端;{}",  sourceTypeStr);
			throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
		}

		// 校验销售时间
		int salesTimeType = billingRulePackageTime.getSalesTimeType();
		Time salesStartTime = billingRulePackageTime.getSalesStartTime();
		Time salesEndTime = billingRulePackageTime.getSalesEndTime();
		String customDate = billingRulePackageTime.getCustomDate();
		// 不限制或者为每日,只需判断时间段是否包含
		if (0 == salesTimeType || 1 == salesTimeType) {
			return;
//			if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
//				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内;start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
//				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
//			}
		} else if (2 == salesTimeType) {
			// 每周
			LocalDate currentDate = LocalDate.now();
			// 获取星期数（1-7，1 表示星期一）
			DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
			int weekdayNumber = dayOfWeek.getValue();
			if (StringUtils.isEmpty(customDate) || !customDate.contains(String.valueOf(weekdayNumber))) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在周日期内);customDate{},weekdayNumber{}",  customDate, weekdayNumber);
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
			if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(周);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
		} else if (3 == salesTimeType) {
			// 每月
			LocalDate currentDate = LocalDate.now();
			// 获取当前日期是本月的第几天
			int dayOfMonth = currentDate.getDayOfMonth();
			if (StringUtils.isEmpty(customDate) || !customDate.contains(String.valueOf(dayOfMonth))) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在月日期内);customDate{}, dayOfMonth{}",  customDate, dayOfMonth);
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
			if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(月);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
		} else {
			// 自定义时间段
			if (StringUtils.isEmpty(customDate) || !customDate.contains("_")) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在自定义日期内);customDate{}",  customDate);
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
			if (!checkCustomDateIsInDateTimeRange(customDate)) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(不在自定义日期内);customDate{}",  customDate);
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
			if (!checkIsInDateTimeRange(salesStartTime, salesEndTime)) {
				log.info("checkPackageTimeAvailable 当前时间不在可销售时间内(周);start{},end{},current{}",  salesStartTime.toString(), salesEndTime.toString(), LocalDateTime.now());
				throw new ServiceException(ServiceCodes.MARKET_CURRENT_NOT_WITHIN_SALES_PERIOD);
			}
 		}

		// 校验包时类型,非包时段、包时长就报错
		if (billingRulePackageTime.getPackageFlag() != 1 && billingRulePackageTime.getPackageFlag() != 2) {
			log.info("checkPackageTimeAvailable 包时类型错误;packageFlag{}",  billingRulePackageTime.getPackageFlag());
			throw new ServiceException(ServiceCodes.MARKET_PACKAGE_RULE_TYPE_ERROR);
		}
	}

	/**
	 * 校验销售次数
	 * @param limitSalesType 销售限制类型 0:不限制 1:每天 2:每周 3:每月
	 * @param limitSalesNumber 限制销售次数  1,2,3.....次
	 * @param ruleId 包时规则id
	 * @param placeId 场所编码
	 * @param cardId  计费卡id
	 */
	public void checkSalesNumbers (int limitSalesType,
								   int limitSalesNumber,
								   String ruleId,
								   String placeId,
								   String cardId) {
		if (limitSalesType == 0) {
			// 不限制
			return;
		}
		if (limitSalesNumber == 0) {
			// 不限制
			return;
		}
		if (limitSalesType > 3) {
			log.info("checkSalesNumbers 限制次数类型错误;limitSalesType{}",  limitSalesType);
			throw new ServiceException(ServiceCodes.MARKET_LIMIT_SALES_TYPE_ERROR);
		}
		// 先获取要查询操作记录的起始时间
		LocalDateTime startTime = null;
		LocalDateTime endTime = null;
		if (limitSalesType == 1) {
			// 每天
			startTime = LocalDate.now().atStartOfDay();
			endTime = LocalDateTime.now();
		} else if (limitSalesType == 2) {
			// 每周
			startTime = weekStartTime();
			endTime = weekEndTime();
		} else if (limitSalesType == 3) {
			// 每月
			startTime = monthStartTime();
			endTime = monthEndTime(LocalDateTime.now());
		}
		// 查询用户包时的购买次数
		List<LogOperation> packageNumbers = logOperationRepository.findByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, cardId, OperationType.PACKAGE_TIME, startTime, endTime);
		int count = (int) packageNumbers.stream().filter(e -> e.getRemark().equals(ruleId)).count();
		if (count >= limitSalesNumber) {
			log.info("checkSalesNumbers 包时次数超限制;ruleId{},设置的次数limitSalesNumber{},已使用的次数count{}",  ruleId, limitSalesNumber, count);
			throw new ServiceException(ServiceCodes.MARKET_LIMIT_SALES_TYPE_ERROR);
		}

	}

	/**
	 * 校验区域是否匹配、是否包间副卡
	 * @param placeId
	 * @param cardId
	 * @param onlineAreaId
	 * @param areaIds
	 */
	public void checkPackageArea (String placeId, String cardId, String onlineAreaId, String areaIds) {
		// 校验区域是否匹配
		if (!areaIds.contains(onlineAreaId)) {
			log.info("checkPackageArea 包时区域不匹配;areaIds{},当前所在区域,onlineAreaId{}",  areaIds, onlineAreaId);
			throw new ServiceException(ServiceCodes.BILLING_PT_AREA_CONFLICT);
		}

		// 查询是否在包间上机
		Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,cardId);
		if (logRoomOpt.isPresent()) {
			LogRoom logRoom = logRoomOpt.get();
			if (logRoom.getIsMaster() == 0) {
				// 副卡在包间上机 不允许操作包时
				log.info("checkPackageArea 包间副卡不允许操作包时;areaIds{},当前所在区域,onlineAreaId{}",  areaIds, onlineAreaId);
				throw new ServiceException(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
			}
		}

	}

	/**
	 * 当前时间是否在某个时间段内
	 * @param salesStartTime
	 * @param salesEndTime
	 * @return
	 */
	private boolean checkIsInDateTimeRange (Time salesStartTime, Time salesEndTime) {
		LocalTime start = salesStartTime.toLocalTime();
		LocalTime end = salesEndTime.toLocalTime();
		// 获取当前时间
		LocalTime current = LocalTime.now();
		return isInDateTimeRange(current, start, end);
	}

	/**
	 * 校验当前日期是否在自定义日期内
	 * @param customDate
	 * @return
	 */
	private boolean checkCustomDateIsInDateTimeRange (String customDate) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		// 分割字符串获取起始日期和结束日期
		String[] parts = customDate.split("_");
		LocalDate startDate = LocalDate.parse(parts[0], formatter);
		LocalDate endDate = LocalDate.parse(parts[1], formatter);
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();
		// 判断当前日期是否在起始日期和结束日期之间
		return!currentDate.isBefore(startDate) &&!currentDate.isAfter(endDate);
	}

	public static boolean verifyPackageTimeCycle(BillingRulePackageTimeBO packageTimeBO, LocalDateTime now){
		if(packageTimeBO.getSalesTimeType() == 0){
			return true;
		}
		//获取当前时间点（float格式）
		float hours = now.getHour();
		int minute = now.getMinute();
		int second = now.getSecond();
		hours += minute / 60.0f;
		hours += second/ 3600.0f;

		float startTime = DateTimeUtils.timeFormatFloat(Time.valueOf(packageTimeBO.getSalesStartTime()));
		float endTime = DateTimeUtils.timeFormatFloat(Time.valueOf(packageTimeBO.getSalesEndTime()));

		if (startTime <= endTime ) {
			// 非跨天情况：直接判断区间
			if((hours <= startTime || hours >= endTime)){
				return false;
			}
		}else{
			// 跨天情况
			if(hours > endTime ){
				if(hours <= startTime){
					return false;
				}
			}
		}
		//校验商品售卖周期
		if(packageTimeBO.getSalesTimeType() == 0){
			//不限制
			return true;
		}else if(packageTimeBO.getSalesTimeType() == 1){
			//校验每日售卖时间
			return true;
		}else if(packageTimeBO.getSalesTimeType() == 2){
			//每周
			int week = now.getDayOfWeek().getValue();
			if (packageTimeBO.getCustomDate().contains(week + "")) {
				return true;
			}
			return false;
		}else if(packageTimeBO.getSalesTimeType() == 3){
			//每月
			int dayOfMonth = now.getDayOfMonth();

			if(Arrays.asList(packageTimeBO.getCustomDate().split(",")).contains(dayOfMonth+"")){
				return true;
			}
			return false;
		}else if(packageTimeBO.getSalesTimeType() == 4) {
			//指定日期
			LocalDate localDate = now.toLocalDate();
			String[] dateArr = packageTimeBO.getCustomDate().split("_");
			if(localDate.compareTo(LocalDate.parse(dateArr[0])) < 0){
				return false;
			}
			if(localDate.compareTo(LocalDate.parse(dateArr[1])) > 0){
				return false;
			}
			return true;
		}else {
			return false;
		}
	}

}
