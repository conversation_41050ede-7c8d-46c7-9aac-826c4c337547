package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.AbstractBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.cons.BaseConstants;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.LogQrCode;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogQrCodeService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收银台本地生成实名二维码
 *
 * <AUTHOR>
 * @date 2023年07月10日 14：46：07
 */
@Service
public class CashierGenerateRealnameQRCodeLocalServiceImpl implements CoreService {

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private LogQrCodeService logQrCodeService;

    @Value("${weChat.server}")
    private String weChatServer;

    @Override
    public GenericResponse<?> doService(List<String> params) {
        // 1 检验参数
        if (params.size() != 2) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        String placeId = params.get(0); // 场所ID
        String cashierId = params.get(1); // 收银台ID
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cashierId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        // 2 查询场所配置信息信息
        GenericResponse<ObjDTO<PlaceConfigBO>> respPlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (respPlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return new GenericResponse<>(ServiceCodes.NO_SERVICE);
        }
        PlaceConfigBO placeConfigBO = respPlaceConfig.getData().getObj();
        // 网吧是否开启微信登入
        if (placeConfigBO.getCashierRealnameQrcode() == 0) {
            return new GenericResponse<>(ServiceCodes.BILLING_CASHIER_QR_CODE_NOT_SUPPORT);
        }

        LocalDateTime deadline;
        if (placeConfigBO.getQrcodeRefreshTimeCashier() <= 0) { // 长期不刷新
            deadline = BaseConstants.DEAD_LINE;

            Optional<LogQrCode> logQrCodeOptional = logQrCodeService.findLastLogQrCode(placeId, cashierId, deadline);
            // 已有数据，更新，返回
            if (logQrCodeOptional.isPresent()) {
                LogQrCode logQrCode = logQrCodeOptional.get();
                logQrCode.setUpdated(LocalDateTime.now());
                logQrCodeService.save(logQrCode);

                QRCodeBO ret = getQrCodeBO(logQrCode.getToken(), deadline);
                return new GenericResponse<>(new ObjDTO<>(ret));
            }
        } else {
            deadline = LocalDateTime.now().plusSeconds(placeConfigBO.getQrcodeRefreshTimeCashier()); // 客户端调接口时减去3秒
        }

        // 没有数据，新增，返回
        LogQrCode logQrCode = new LogQrCode();
        logQrCode.setPlaceId(placeId);
        logQrCode.setCashierId(cashierId);
        logQrCode.setDeadline(deadline);
        logQrCode.setUsed(0);
        String qrCodeToken = Dim4StringUtils.getUUIDWithoutHyphen() + System.currentTimeMillis() / 1000;
        logQrCode.setToken(qrCodeToken);
        logQrCode.setCreated(LocalDateTime.now());
        logQrCodeService.save(logQrCode);

        QRCodeBO ret = getQrCodeBO(qrCodeToken, deadline);
        return new GenericResponse<>(new ObjDTO<>(ret));
    }

    private QRCodeBO getQrCodeBO(String qrCodeToken, LocalDateTime deadline) {
        String qrCodeContent = weChatServer + "/user-server/user/wechat/mp/page/token?token=" + qrCodeToken + "&from=cashier";
        QRCodeBO ret = new QRCodeBO(); // 返回数据
        ret.setQrcodeContent(qrCodeContent);
        ret.setDeadline(deadline);
        return ret;
    }

    @Getter
    @Setter
    public static class QRCodeBO extends AbstractBO implements Serializable {
        /**
         *
         */
        private static final long serialVersionUID = 1L;
        String qrcodeContent;
        LocalDateTime deadline;
    }

}
