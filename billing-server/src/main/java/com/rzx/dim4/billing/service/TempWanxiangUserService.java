package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.TempWanxiangUser;
import com.rzx.dim4.billing.repository.TempWanxiangUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 临时用户记录
 */
@Service
public class TempWanxiangUserService {

    @Autowired
    TempWanxiangUserRepository tempWanxiangUserRepository;

    public TempWanxiangUser save(TempWanxiangUser user) {
        return tempWanxiangUserRepository.save(user);
    }

    public void saveAll(List list) {
        tempWanxiangUserRepository.saveAll(list);
    }

    public Optional<TempWanxiangUser> findTop1ByPlaceId(String placeId) {
        return tempWanxiangUserRepository.findTop1ByPlaceIdAndDeletedOrderByIdDesc(placeId,0);
    }

    public List<TempWanxiangUser> findByPlaceIdAndCardIdShowAndNameShow(String placeId, String cardIdShow, String heapCanUse) {
        return tempWanxiangUserRepository.findByPlaceIdAndCardIdShowAndNameShowAndDeleted(placeId, cardIdShow, heapCanUse, 0);
    }

    public List<TempWanxiangUser> findByPlaceIdAndCardIdIn(String placeId, List<String> cardIds) {
        return tempWanxiangUserRepository.findByPlaceIdAndCardIdInAndDeleted(placeId, cardIds, 0);
    }


}
