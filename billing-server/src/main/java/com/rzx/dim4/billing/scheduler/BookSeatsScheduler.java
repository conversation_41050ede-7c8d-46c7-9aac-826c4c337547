package com.rzx.dim4.billing.scheduler;

import com.rzx.dim4.billing.service.BookSeatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 订座定时任务
 *
 * <AUTHOR>
 * @since 2023/12/11
 **/
@Slf4j
@Component
public class BookSeatsScheduler {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BookSeatsService bookSeatsService;

    String bookSeatsLockKey = "billing:lock:bookSeats_lock";

    @Scheduled(initialDelay = 1000 * 30, fixedRate = 1000 * 60 * 5)
    public void bookSeats() {

        log.info("中心订座计费开始, 获取锁:::" + bookSeatsLockKey);
        boolean locked = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(bookSeatsLockKey, "locked"));
        if (locked) {
            log.info("获取成功，设置5分钟TTL");
            stringRedisTemplate.expire(bookSeatsLockKey, 5, TimeUnit.MINUTES);
        } else {
            log.info("获取失败，查询锁状态");
            long expired = stringRedisTemplate.getExpire(bookSeatsLockKey, TimeUnit.SECONDS);
            log.info("没有获得锁, 停止中心订座计费查询未下机任务, TTL剩余:::" + expired);
            if (expired == -1L) {
                log.info("上次TTL设置失败，重新设置5分钟TTL");
                stringRedisTemplate.expire(bookSeatsLockKey, 5, TimeUnit.MINUTES);
            }
            log.info("中心订座计费任务结束！！！");
            return;
        }

        bookSeatsService.deductionSchedule();

        log.info("中心订座计费结束！！！释放锁:::" + bookSeatsLockKey);
        stringRedisTemplate.delete(bookSeatsLockKey);
    }
}
