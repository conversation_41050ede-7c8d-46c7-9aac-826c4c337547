package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.marketing.OrdersApi;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogRefund;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.repository.LogRefundRepository;
import com.rzx.dim4.billing.service.Component.RabbitMQProvider;
import com.rzx.dim4.billing.service.Component.RefundMessage;
import com.rzx.dim4.billing.service.asycn.AsyncLogRefundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.rzx.dim4.billing.cons.BillingConstants.TEMP_CARD_TYPE_ID;

@Slf4j
@Service
public class LogRefundService implements ILogRefundService {

    @Autowired
    private LogRefundRepository logRefundRepository;

    @Autowired
    private LogTopupService logTopupService;


    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private AsyncLogRefundService asyncLogRefundService;
    @Autowired
    private LogRefundService logRefundService;


    public LogRefund save(LogRefund logRefund) {
        return logRefundRepository.save(logRefund);
    }

    public LogRefund getById(Long id) {
        return logRefundRepository.findById(id).orElseThrow(() -> new ServiceException(ServiceCodes.BAD_PARAM));
    }

    /**
     * 分页查询
     *
     * @param map
     * @param pageable
     * @return
     */
    public Page<LogRefund> findAll(Map<String, Object> map, Pageable pageable) {
        return logRefundRepository.findAll(new Specification<LogRefund>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<LogRefund> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> andPredicateList = new ArrayList<Predicate>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                if (map.containsKey("refundOrderId") && !StringUtils.isEmpty(map.get("refundOrderId"))) {// 订单ID
                    andPredicateList.add(cb.like(root.get("refundOrderId").as(String.class), "%" + map.get("refundOrderId") + "%"));
                }
                if (map.containsKey("ldorderid") && !StringUtils.isEmpty(map.get("ldorderid"))) {// 龙兜订单ID
                    andPredicateList.add(cb.like(root.get("ldorderid").as(String.class), "%" + map.get("ldorderid") + "%"));
                }
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所ID
                    andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
                }
                if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {// 卡类型ID
                    andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), map.get("cardTypeId")));
                }
                if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {// 卡ID
                    andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
                }
                if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 交班ID
                    andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
                }
                if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                    andPredicateList.add(cb.like(root.get("idNumber"), map.get("idNumber") + "%"));
                }
                if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 姓名
                    andPredicateList.add(cb.like(root.get("idName"), map.get("idName") + "%"));
                }
                if (map.containsKey("operatorName") && !StringUtils.isEmpty(map.get("operatorName"))) { // 操作人姓名
                    andPredicateList.add(cb.like(root.get("operatorName"), "%" + map.get("operatorName") + "%"));
                }
                if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {// 操作开始时间
                    LocalDateTime startTime = LocalDateTime.parse(map.get("startDate").toString(), fmt);
                    andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }
                if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(map.get("endDate").toString(), fmt);
                    andPredicateList
                            .add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {// 来源
                    andPredicateList.add(cb.equal(root.get("sourceType").as(String.class), map.get("sourceType")));
                }
                if (map.containsKey("sourceTypes") && !StringUtils.isEmpty(map.get("sourceTypes"))) {// 来源
                    andPredicateList.add(root.get("sourceType").as(String.class).in(map.get("sourceTypes")));
                }

                if (map.containsKey("cardIds") && !org.springframework.util.StringUtils.isEmpty(map.get("cardIds"))) { // 卡Ids
                    Path<Object> path = root.get("cardId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) map.get("cardIds");
                    cardIds.forEach(in::value);
                    andPredicateList.add(in);
                }

                Predicate[] p = new Predicate[andPredicateList.size()];
                return cb.and(andPredicateList.toArray(p));
            }
        }, pageable);
    }

    /**
     * 带筛选条件的统计退款余额
     *
     * @param placeId
     * @param startDate
     * @param endDate
     * @param idNumber
     * @param cardTypeId
     * @param operatorName
     * @param idName
     * @param refundOrderId
     * @param sourceType
     * @return
     */
    public Map<String, Integer> querySumCashRefundAndOnlineRefund(String placeId, String startDate, String endDate, String idNumber, String cardTypeId,
                                                                  String operatorName, String idName, String refundOrderId, String ldorderid, String sourceType) {
        Map<String, Integer> result = new HashMap<>();
        Map<String, String> map = logRefundRepository.querySumCashRefundAndOnlineRefund(placeId, startDate, endDate, idNumber, cardTypeId, operatorName, idName, refundOrderId, ldorderid, sourceType);
        for (String key : map.keySet()) {
            Object obj = map.get(key);
            if (StringUtils.isEmpty(obj)) {
                result.put(key, 0);
            } else {
                result.put(key, new BigDecimal(obj.toString()).intValue());
            }
        }
        return result;
    }

    /**
     * 查询时间段内该场所的所有退款记录
     *
     * @param placeId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<LogRefund> queryLogRefundByTime(String placeId, LocalDateTime startTime, LocalDateTime endTime) {
        return logRefundRepository.findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, startTime, endTime);
    }

    /**
     * 查询该场所--当前班次的所有退款记录
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public List<LogRefund> findByPlaceIdAndShiftId(String placeId, String shiftId) {
        return logRefundRepository.findByPlaceIdAndShiftId(placeId, shiftId);
    }

    /**
     * 按班次-线上总退款
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostOnlineRefundByShiftId(String placeId, String shiftId) {
        Integer result = logRefundRepository.sumCostOnlineRefundByShiftId(placeId, shiftId);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-来源-线上总退款
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostOnlineRefundByShiftIdAndSource(String placeId, String shiftId, SourceType sourceType) {
        Integer result = logRefundRepository.sumCostOnlineRefundByShiftIdAndSource(placeId, shiftId, sourceType);
        return result == null ? 0 : result;
    }

    /**
     * 按场所--卡号--时间段-现金总退款
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCashRefundByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                                LocalDateTime endDateTime) {
        Integer result = logRefundRepository.sumCashRefundByCardIdAndDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按场所--卡号--时间段-线上总退款
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumOnlineRefundByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                                  LocalDateTime endDateTime) {
        Integer result = logRefundRepository.sumOnlineRefundByCardIdAndDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按场所--卡号--时间段-现金总退款
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCashRefundByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                                  LocalDateTime endDateTime) {
        Integer result = logRefundRepository.sumCashRefundByPlaceIdsAndDateTime(placeIds, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按场所--时间段-线上总退款
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumOnlineRefundByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                                    LocalDateTime endDateTime) {
        Integer result = logRefundRepository.sumOnlineRefundByPlaceIdsAndDateTime(placeIds, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按场所-班次-卡类型-退款方式 查询总退款额（线上+线下）
     *
     * @return 返回负数
     */
    public int sumRefundAmountByPlaceIdAndShiftIdAndCardTypeIdAndRefundType(String placeId,String shiftId,String cardTypeId) {
        Integer result = logRefundRepository.sumRefundAmountByPlaceIdAndShiftIdAndCardTypeIdAndRefundType(placeId, shiftId,cardTypeId);
        return result == null ? 0 : -result;
    }

    /**
     * 按班次查询现金总退款
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostCashRefundByShiftId(String placeId,String shiftId){

        Integer result = logRefundRepository.sumCostCashRefundByShiftId(placeId, shiftId);
        return result == null ? 0 : result;
    }

    /**
     * 按班次查询现金总退款(区分退款类型)
     * @param placeId
     * @param shiftId
     * @return 注意返回的是负数
     */
    public int sumCostCashRefundByShiftIdAndRefundType(String placeId,String shiftId){

        Integer result = logRefundRepository.sumCostCashRefundByShiftIdAndRefundType(placeId, shiftId);
        return result == null ? 0 : -result;
    }

    /**
     * 按班次查询总退款(区分退款类型)
     * @param placeId
     * @param shiftId
     * @return 注意返回的是负数
     */
    public int sumTotalRefundByShiftIdAndRefundType(String placeId,String shiftId){

        Integer result = logRefundRepository.sumTotalRefundByShiftIdAndRefundType(placeId, shiftId);
        return result == null ? 0 : -result;
    }

    public LogRefund addLogRefund(String placeId, BillingCard billingCard, LogShift logShift, SourceType sourceType, int refundAmount) {

        LogRefund logRefund = new LogRefund();
        logRefund.setPlaceId(placeId);
        logRefund.setCardId(billingCard.getCardId());
        logRefund.setIdNumber(billingCard.getIdNumber());
        logRefund.setIdName(billingCard.getIdName());
        logRefund.setCardTypeId(billingCard.getCardTypeId());
        logRefund.setCardTypeName(billingCard.getCardTypeName());

        logRefund.setCashRefund(refundAmount);

        logRefund.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        logRefund.setOperatorName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        logRefund.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logRefund.setSourceType(sourceType);
        logRefund.setCreated(LocalDateTime.now());
        logRefund.setStatus(1);
        return logRefund;
    }

    public LogRefund addLogRefundOnline(String placeId, BillingCard billingCard, LogShift logShift, SourceType sourceType, int refundAmount) {
        LogRefund logRefund = new LogRefund();
        logRefund.setPlaceId(placeId);
        logRefund.setCardId(billingCard.getCardId());
        logRefund.setIdNumber(billingCard.getIdNumber());
        logRefund.setIdName(billingCard.getIdName());
        logRefund.setCardTypeId(billingCard.getCardTypeId());
        logRefund.setCardTypeName(billingCard.getCardTypeName());

        logRefund.setOnlineRefund(refundAmount);

        logRefund.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        logRefund.setOperatorName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        logRefund.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logRefund.setSourceType(sourceType);
        logRefund.setCreated(LocalDateTime.now());
        logRefund.setStatus(0);
        return logRefund;
    }

    public void saveBatch(List<LogRefund> logRefunds) {
        logRefundRepository.saveAll(logRefunds);
    }

    /**
     * 临时卡在线退款操作
     * @param billingCard 计费卡信息
     * @param refundAmount 待退款金额
     * @param sourceType 来源
     * @param logShift 对应收银员信息
     */
    @Override
    public void addRefundTask(BillingCard billingCard, int refundAmount, SourceType sourceType, LogShift logShift) {
        log.info("开始结账退款，placeId={},cardId={},refundAmount={},开始时间", billingCard.getPlaceId(), billingCard.getCardId(), refundAmount,DateTimeUtils.getTimeFormat(LocalDateTime.now()));

        if (!TEMP_CARD_TYPE_ID.equals(billingCard.getCardTypeId())) {
            log.error("只有临时卡有结账退在线账户款");
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
        }

        if (null == logShift) {
//            throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
            logShift = logShiftService.getShiftId(billingCard.getPlaceId()); //还是存在为null的可能
        }

        if (refundAmount == 0) {
            return;
        }

        // 循环调用查询还没有退款的充值订单，时间由近至远，然后扣除需要退款的金额，直到退款为0
        List<LogRefund> logRefunds = new ArrayList<>();
        List<Long> haveUsedLogtopupIds = new ArrayList<>();

        //精确匹配充值退款金额订单,没有匹配到后再去循环查找订单退款
        Optional<LogTopup> logTopupOptional = logTopupService.findLatestOneCanRefund(billingCard, haveUsedLogtopupIds,refundAmount);
        boolean logTopupFlag = logTopupOptional.isPresent();

        // 正常是找到对应的充值订单，refundAmount减为0退出循环，如果中途订单不够那就是在线账户的钱没有足够的充值订单去退，是系统异常，需人工接入
        while (refundAmount > 0) {
            if(!logTopupFlag){
                log.info("临时卡查询充值对应金额记录没有找到，去找最近一笔充值订单=============");
                logTopupOptional = logTopupService.findLatestOneCanRefund(billingCard, haveUsedLogtopupIds,null);
            }

            LogTopup logTopup = logTopupOptional.orElseThrow(() -> {
                log.warn("临时卡没有可以退款的充值订单，placeId={},cardId={}", billingCard.getPlaceId(), billingCard.getCardId());
                return new ServiceException(ServiceCodes.BILLING_TOPUP_CAN_USE_NOT_FOUND);
            });

            if(logTopup.getRefundStatus() == 2){
                log.info("订单id={}，已退款，无法再次退款 ", logTopup.getId());
                haveUsedLogtopupIds.add(logTopup.getId());
                continue;
            }

            LogRefund logRefund = new LogRefund();
            logRefund.setPlaceId(billingCard.getPlaceId());
            logRefund.setCardId(billingCard.getCardId());
            logRefund.setIdName(billingCard.getIdName());
            logRefund.setIdNumber(billingCard.getIdNumber());
            logRefund.setCardTypeId(billingCard.getCardTypeId());
            logRefund.setCardTypeName(billingCard.getCardTypeName());
            logRefund.setSourceType(sourceType);
            int temporaryOnlineAmount = logTopup.getCashAmount();
            if (refundAmount > temporaryOnlineAmount) {
                logRefund.setOnlineRefund(temporaryOnlineAmount);
                refundAmount -= temporaryOnlineAmount;
            } else {
                logRefund.setOnlineRefund(refundAmount);
                refundAmount = 0;
            }
            logRefund.setOrderId(logTopup.getOrderId());
            logRefund.setLdorderid(logTopup.getLdOrderId());
            if (sourceType == SourceType.CASHIER) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(ObjectUtils.isEmpty(logShift) ? null:logShift.getLoginAccountName());
                logRefund.setCreater(ObjectUtils.isEmpty(logShift) ? null:Long.parseLong(logShift.getLoginAccountId()));
            } else if (sourceType == SourceType.CLIENT || sourceType == SourceType.WECHAT ) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(billingCard.getIdName());
                logRefund.setCreater(billingCard.getId());
            } else if (sourceType ==  SourceType.MINIAPP) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName("四维网管小程序");
//                logRefund.setCreater(billingCard.getId());
            } else if (sourceType ==  SourceType.ALIPAY) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(billingCard.getIdName());
                logRefund.setCreater(billingCard.getId());
            } else {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_SOURCE_ERROR);
            }

            logRefund.setCreated(LocalDateTime.now());

            logRefund.setStatus(0);
            LogRefund save = logRefundService.save(logRefund);
            logRefunds.add(save);

            log.info("临时卡已使用充值订单={}，id为{}，退款金额为{}", new Gson().toJson(logTopup),logTopup.getId(), logRefund.getOnlineRefund());
            haveUsedLogtopupIds.add(logTopup.getId());
        }
        asyncLogRefundService.asyncSendTempCardRefund(logRefunds,billingCard,refundAmount,3);

    }

    @Override
    public void addPackageTimeReserveRefundTask(BillingCard billingCard, int refundAmount, SourceType sourceType, LogShift logShift, String ruleId) {
        log.info("开始预包时退款，placeId={},cardId={},refundAmount={},包时规则id={},开始时间={}", billingCard.getPlaceId(), billingCard.getCardId(), refundAmount,ruleId,DateTimeUtils.getTimeFormat(LocalDateTime.now()));

        if (refundAmount == 0) {
            return;
        }
        if (null == logShift) {
            logShift = logShiftService.getShiftId(billingCard.getPlaceId()); //还是存在为null的可能
        }

        // 循环调用查询还没有退款的充值订单，时间由近至远，然后扣除需要退款的金额，直到退款为0
        List<LogRefund> logRefunds = new ArrayList<>();
        List<Long> haveUsedLogtopupIds = new ArrayList<>();

        //精确匹配充值退款金额订单,没有匹配到后再去循环查找订单退款
        Optional<LogTopup> logTopupOptional = logTopupService.findLatestOneCanRefund(billingCard, haveUsedLogtopupIds,refundAmount);
        boolean logTopupFlag = logTopupOptional.isPresent();

        // 正常是找到对应的充值订单，refundAmount减为0退出循环，如果中途订单不够那就是在线账户的钱没有足够的充值订单去退，是系统异常，需人工接入
        while (refundAmount > 0) {
            if(!logTopupFlag){
                log.info("预包时查询充值对应金额记录没有找到，去找最近一笔充值订单=============");
                logTopupOptional = logTopupService.findLatestOneCanRefund(billingCard, haveUsedLogtopupIds,null);
            }

            LogTopup logTopup = logTopupOptional.orElseThrow(() -> {
                log.warn("没有可以退款的充值订单，placeId={},cardId={}", billingCard.getPlaceId(), billingCard.getCardId());
                return new ServiceException(ServiceCodes.BILLING_TOPUP_CAN_USE_NOT_FOUND);
            });

            if(logTopup.getRefundStatus() == 2){
                log.info("订单id={}，已退款，无法再次退款 ", logTopup.getId());
                haveUsedLogtopupIds.add(logTopup.getId());
                continue;
            }

            LogRefund logRefund = new LogRefund();
            logRefund.setPlaceId(billingCard.getPlaceId());
            logRefund.setCardId(billingCard.getCardId());
            logRefund.setIdName(billingCard.getIdName());
            logRefund.setIdNumber(billingCard.getIdNumber());
            logRefund.setCardTypeId(billingCard.getCardTypeId());
            logRefund.setCardTypeName(billingCard.getCardTypeName());
            logRefund.setSourceType(sourceType);
            int temporaryOnlineAmount = logTopup.getCashAmount();
            if (refundAmount > temporaryOnlineAmount) {
                logRefund.setOnlineRefund(temporaryOnlineAmount);
                refundAmount -= temporaryOnlineAmount;
            } else {
                logRefund.setOnlineRefund(refundAmount);
                refundAmount = 0;
            }
            logRefund.setOrderId(logTopup.getOrderId());
            logRefund.setLdorderid(logTopup.getLdOrderId());
            if (sourceType == SourceType.CASHIER) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(ObjectUtils.isEmpty(logShift) ? null:logShift.getLoginAccountName());
                logRefund.setCreater(ObjectUtils.isEmpty(logShift) ? null:Long.parseLong(logShift.getLoginAccountId()));
            } else if (sourceType == SourceType.CLIENT || sourceType == SourceType.WECHAT ) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(billingCard.getIdName());
                logRefund.setCreater(billingCard.getId());
            } else if (sourceType ==  SourceType.MINIAPP) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName("四维网管小程序");
//                logRefund.setCreater(billingCard.getId());
            } else if (sourceType ==  SourceType.ALIPAY) {
                logRefund.setShiftId(ObjectUtils.isEmpty(logShift) ? null:logShift.getShiftId());
                logRefund.setOperatorName(billingCard.getIdName());
                logRefund.setCreater(billingCard.getId());
            } else {
                throw new ServiceException(ServiceCodes.SHOP_GOODS_SOURCE_ERROR);
            }

            logRefund.setCreated(LocalDateTime.now());

            logRefund.setStatus(0);
            LogRefund save = logRefundService.save(logRefund);
            logRefunds.add(save);

            log.info("预包时已使用充值订单={}，id为{}，退款金额为{}", new Gson().toJson(logTopup), logTopup.getId(), logRefund.getOnlineRefund());
            haveUsedLogtopupIds.add(logTopup.getId());
        }
        asyncLogRefundService. asyncSendTempCardRefund(logRefunds,billingCard,refundAmount,4);
    }



}
