package com.rzx.dim4.billing.service.Component;

import com.rabbitmq.client.Channel;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.LogRefund;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.service.LogRefundService;
import com.rzx.dim4.billing.service.LogTopupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/11/9
 **/
@Slf4j
@Service
public class RabbitMQConsumer {

    @Autowired private RabbitMQProvider rabbitMQProvider;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PaymentServerService paymentServerService;

    @Autowired
    private LogRefundService logRefundService;

    @Autowired
    private LogTopupService logTopupService;

    /**
     * 接收到消息，然后调用退款接口，
     * 退款成功则更新 logRefund 表状态，
     * 失败则重试次数+1，然后重新放到6h的队列中等待下次执行
     *
     * @param message
     * @param channel
     * @param tag
     * @throws IOException
     */
    @RabbitListener(queues = RabbitMQConfig.DLX_QUEUE_ORDER_B, ackMode = "MANUAL")
    public void receiveRefundDelayOneHour(@Payload RefundMessage message,
                                         Channel channel,
                                         @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("DLX_QUEUE_ORDER_B message={},tag={} ", message, tag);

        doRefundWork(message, true);
    }

    private void doRefundWork(RefundMessage message, boolean oneHourFlag) {
        int retryCount = message.getRetryCount();
        long logRefundId = message.getLogRefundId();

        boolean checkRetryCount = oneHourFlag ? (retryCount != 0) : (retryCount < 1 || retryCount > 12);
        if (checkRetryCount) {
            log.info("重试次数不合法，logRefundId={}, retryCount={} ", logRefundId, retryCount);
            return;
        }

        LogRefund logRefund = logRefundService.getById(logRefundId);
        if (logRefund.getStatus() != 0) {
            log.info("当前退款记录不是未退款状态，logRefundId={}, status={} ", logRefundId, logRefund.getStatus());
            return;
        }

        // 调用payment-server退款
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        GenericResponse<ObjDTO<PaymentRefundOrderBO>> response =
                paymentServerService.refundPaymentOrder(requestTicket, logRefund.getPlaceId(), logRefund.getOrderId(), logRefund.getOnlineRefund());

        if (response.getCode() == ServiceCodes.PAYMENT_REFUND_SUCC.getCode()) {
            // Handle successful refund
            PaymentRefundOrderBO paymentRefundOrderBO = response.getData().getObj();
            logRefund.setRefundOrderId(paymentRefundOrderBO.getRefundOrderId());
            logRefund.setRefundDesc(paymentRefundOrderBO.getRefundDesc());
            logRefund.setReturnMessage(paymentRefundOrderBO.getReturnMessage());
            // 退款进度1已退款, 2未退款, 3处理中, 4退款失败。2/4需要重试
            if (paymentRefundOrderBO.getRefundStatus() == 1) {
                logRefund.setStatus(1);
                LogTopup logTopup = logTopupService.findByOrderId(logRefund.getOrderId())
                        .orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_LOG_TOPUP_NOT_FOUND));
                if (logRefund.getOnlineRefund() == logTopup.getCashAmount()) {
                    logTopup.setRefundStatus(2);
                } else {
                    logTopup.setRefundStatus(1);
                }
                logTopup.setUpdated(LocalDateTime.now());
                logTopupService.save(logTopup);
            } else if (paymentRefundOrderBO.getRefundStatus() == 3) {
                logRefund.setStatus(2);
            } else {
                message.setRetryCount(retryCount + 1);
                rabbitMQProvider.sendSixHourDelayQueue(message);
            }
        } else {
            // Handle other response codes
            message.setRetryCount(retryCount + 1);
            rabbitMQProvider.sendSixHourDelayQueue(message);
            logRefund.setReturnMessage(response.getMessage());
        }
        logRefund.setUpdated(LocalDateTime.now());
        logRefundService.save(logRefund);
    }

    @RabbitListener(queues = RabbitMQConfig.DLX_QUEUE_ORDER_C, ackMode = "MANUAL")
    public void receiveRefundDelaySixHour(@Payload RefundMessage message,
                                         Channel channel,
                                         @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("DLX_QUEUE_ORDER_C message={},tag={} ", message, tag);

        doRefundWork(message, false);
    }
}
