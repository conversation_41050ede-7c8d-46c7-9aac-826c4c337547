package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogShift;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021年8月30日 下午6:40:17
 */
public interface LogShiftRepository extends JpaRepository<LogShift, Long> {

    Optional<LogShift> findTop1ByPlaceIdOrderByIdDesc(String placeId);

    Optional<LogShift> findByPlaceIdAndShiftIdAndStatus(String placeId, String shiftId, int status);

    Optional<LogShift> findByPlaceIdAndCashierIdAndStatus(String placeId, String cashierId, int status);

    Optional<LogShift> findTop1ByPlaceIdAndCashierIdOrderByIdDesc(String placeId, String cashierId);

    Optional<LogShift> findTop1ByPlaceIdAndCashierIdAndStatusOrderByIdDesc(String placeId, String cashierId, int status);

}
