package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.domain.PackageTimeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收银台续包时
 */
@Slf4j
@Service
public class CashierContinuePackageTimeServiceImpl implements CoreService {

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private LogRoomService logRoomService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    private CashierAuthorityService cashierAuthorityService;

    @Autowired
    BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    PlaceBizConfigService placeBizConfigService;

    @Autowired
    PackageTimeDomainService packageTimeDomainService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (CollectionUtils.isEmpty(params) || params.size() < 5) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0); // 班次ID
        String shiftId = params.get(1); // 收银台ID
        String cardId = params.get(2); // 计费卡ID
        String ruleId = params.get(3); // 包时规则ID
        String packageType = params.get(4); // 包时类型 1:余额包时 2:现金包时

        // 查询班次
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = optLogShift.get();

        // 验证权限
        Optional<CashierAuthority> cashierAuthorityOpt = cashierAuthorityService.findByPlaceIdAndAccountId(placeId, logShift.getLoginAccountId());
        if (!cashierAuthorityOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }

        CashierAuthority cashierAuthority = cashierAuthorityOpt.get();
        if (!cashierAuthority.getOpAuthority().contains(String.valueOf(ServiceIndexes.CashierContinuePackageTime.getValue()))) {
            throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
        }

        // 获取卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        // 并发加锁处理
        String billingKey = billingLockAlgorithm.acquireLock(placeId, billingCard.getCardId());
        if (billingKey == null) {
            // 没拿到锁
            return new GenericResponse<>(ServiceCodes.BILLING_IN_PROGRESS);
        }
        log.info("收银台续包时:::::::放入锁时间::::" + LocalDateTime.now());

        // 查询是否在包间上机
        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,cardId);
        if (logRoomOpt.isPresent()) {
            LogRoom logRoom = logRoomOpt.get();
            if (logRoom.getIsMaster() == 0) {
                // 副卡在包间上机 不允许操作包时
                return new GenericResponse<>(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
            }
        }

        // 2.查询当前上机状态
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
                cardId);
        if (!optBillingOnline.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_ONLINE_NOT_FOUND); // 未找到该客户端在线计费信息
        }
        BillingOnline billingOnline = optBillingOnline.get();
        if (billingOnline.getPackageFlag() < 1) {
            return new GenericResponse<>(ServiceCodes.BILLING_COMMON_ONLINE_NOT_CONTINUE_PACKAGE); // 不是正在包时的不能操作续包时
        }

        // 获取当前的包时规则
        Optional<BillingRulePackageTime> oldBillingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, billingOnline.getRuleId());
        if (!oldBillingRulePackageTimeOpt.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        BillingRulePackageTime oldBillingRulePackageTime = oldBillingRulePackageTimeOpt.get();

        // 查询登录信息
        Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, billingOnline.getCardId(), billingOnline.getBillingTime());
        if (!optLogLogin.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
        }
        LogLogin logLogin = optLogLogin.get();

        packageTimeDomainService.continuePackageTime(placeId, ruleId, billingKey, billingOnline, billingCard, packageType, logLogin, logShift, oldBillingRulePackageTime);

        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }
}

