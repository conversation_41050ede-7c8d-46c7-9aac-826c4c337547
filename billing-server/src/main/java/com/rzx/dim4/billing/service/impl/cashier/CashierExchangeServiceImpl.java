package com.rzx.dim4.billing.service.impl.cashier;

import com.alibaba.fastjson.JSON;
import com.rzx.dim4.base.bo.billing.BillingOnlineBO;
import com.rzx.dim4.base.bo.billing.BillingRuleCommonBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.invite.InviteOnline;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.Invite.InviteOnlineService;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收银台换机
 *
 * <AUTHOR>
 * @date 2021年12月8日 下午3:50:48
 */
@Service
@Slf4j
public class CashierExchangeServiceImpl implements CoreService {

	@Autowired
	BillingCardService billingCardService;

	@Autowired
	BillingOnlineService billingOnlineService;

	@Autowired
	LogOperationService logOperationService;

	@Autowired
	LogShiftService logShiftService;

	@Autowired
	LogLoginService logLoginService;

	@Autowired
	LogHbService logHbService;

	@Autowired
	BillingRuleCommonService billingRuleCommonService;

	@Autowired
	BillingRulePackageTimeService billingRulePackageTimeService;

	@Autowired
	PlaceServerService placeServerService;

	@Autowired
	LogRoomService logRoomService;

	@Autowired
	LogAccService logAccService;

	@Autowired
	BillingRuleAccService billingRuleAccService;

	@Autowired
	BillingLockAlgorithm billingLockAlgorithm;

	@Autowired
	PlaceBizConfigService placeBizConfigService;

	@Autowired
	BalanceDetailsAlgorithm balanceDetailsAlgorithm;

	@Autowired
	private BillingCardDeductionService billingCardDeductionService;

	@Autowired
	private InviteOnlineService inviteOnlineService;

	@Autowired
	private PlacementService placementService;

	private ThreadLocal<Integer> costCash = ThreadLocal.withInitial(() -> 0); // 本金扣费总额

	private ThreadLocal<Integer> costPresent = ThreadLocal.withInitial(() -> 0); // 奖励扣费总额


	@Override
	public GenericResponse<?> doService(List<String> params) {

		if (params.size() != 6) {
			return new GenericResponse<>(ServiceCodes.NULL_PARAM);
		}

		String placeId = params.get(0);
		String shiftId = params.get(1); // 班次ID
		String currAreaId = params.get(2);
		String currClientId = params.get(3);
		String newAreaId = params.get(4);
		String newClientId = params.get(5);

		LocalDateTime nowTime = LocalDateTime.now();

		// 获取网吧计费配置
		PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);

		// 查询当前的在线信息
		Optional<BillingOnline> currBillingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndClientId(placeId,
				currClientId);
		if (!currBillingOnlineOpt.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_ONLINE_NOT_FOUND);
		}
		BillingOnline currBillingOnline = currBillingOnlineOpt.get();

		// 查询即将换机客户端在线信息，如果已经在线则不能换机
		Optional<BillingOnline> newBillingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndClientId(placeId,
				newClientId);
		if (newBillingOnlineOpt.isPresent()) {
			if(currAreaId.equals(newAreaId) && currClientId.equals(newClientId)){
				log.warn("在线换机禁止本机换本机：{}",params.toString());
				return new GenericResponse<>(ServiceCodes.ILLEGAL_PARAM);
			}
			BillingOnline newBillingOnline = newBillingOnlineOpt.get();
			//在线换机到在线
			return onlineToOnline(currBillingOnline,newBillingOnline,placeId,shiftId,currAreaId,currClientId,newAreaId,newClientId,nowTime, placeBizConfig.getExchangeUpdateRate());
		}

		// 并发加锁处理
		String billingKey = billingLockAlgorithm.cardIdAcquireLock(placeId, currBillingOnline.getCardId());
		if (billingKey == null) {
			// 没拿到锁
			return new GenericResponse<>(ServiceCodes.BILLING_IN_PROGRESS);
		}
		log.info("收银台结账:::::::放入锁时间::::" + LocalDateTime.now());

		GenericResponse<ObjDTO<PlaceAreaBO>> respCurrArea = placeServerService.findPlaceAreaByPlaceIdAndAreaId(placeId,
				currAreaId);
		if (respCurrArea.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			// 释放锁
			billingLockAlgorithm.releaseLock(billingKey);
			return respCurrArea;
		}

		// 校验包间换机
		GenericResponse<ObjDTO<PlaceAreaBO>> respNewArea = placeServerService.findPlaceAreaByPlaceIdAndAreaId(placeId,
				newAreaId);
		if (respNewArea.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			// 释放锁
			billingLockAlgorithm.releaseLock(billingKey);
			return respNewArea;
		}
		PlaceAreaBO currAreaBO = respCurrArea.getData().getObj();
		PlaceAreaBO newAreaBO = respNewArea.getData().getObj();

		if (currAreaBO.getIsRoom() == 1 || newAreaBO.getIsRoom() == 1) {
			// 正在上机的区域和要换机的区域 存在一个在包间区域则不能跨区换机
			if (!currAreaId.equals(newAreaId)) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_ROOM_AREA_NOT_SUPPORT_EXCHANGE);
			}
		}

		// 如果当前是包时，则不能夸区域换机
		if (currBillingOnline.getPackageFlag() > 0) {

			// 查询包时费率
			Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, currBillingOnline.getRuleId());
			if (!billingRulePackageTimeOpt.isPresent()) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
			}
			BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
			if (!billingRulePackageTime.getAreaIds().contains(newAreaId)) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_PT_AREA_ERROR);
			}
		}

		// 查询当前计费卡信息
		Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId,
				currBillingOnline.getCardId());
		if (!optBillingCard.isPresent()) {
			// 释放锁
			billingLockAlgorithm.releaseLock(billingKey);
			return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND); // 账号不存在
		}
		BillingCard billingCard = optBillingCard.get();

		// 查询班次信息
		Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
		if (!optLogShift.isPresent()) {
			// 释放锁
			billingLockAlgorithm.releaseLock(billingKey);
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
		}
		LogShift logShift = optLogShift.get();

		// 查询登录信息
		Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, currBillingOnline.getCardId(), currBillingOnline.getBillingTime());
		if (!optLogLogin.isPresent()) {
			// 释放锁
			billingLockAlgorithm.releaseLock(billingKey);
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
		}
		LogLogin logLogin = optLogLogin.get();

		// 查询新换机所在区域的费率信息
		BillingRuleCommon newBillingRuleCommon = null;
		int price = currBillingOnline.getCommonPrice();
		LocalDateTime nextTime = currBillingOnline.getNextTime();
		int accFlag = currBillingOnline.getAccFlag();
		if (currBillingOnline.getPackageFlag() == 0 && (!newAreaId.equals(currAreaId))) {

			Optional<BillingRuleCommon> billingRuleCommonOpt = billingRuleCommonService.billingRuleCommons(placeId,newAreaId,currBillingOnline.getCardTypeId());
			if (!billingRuleCommonOpt.isPresent()) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
			}
			newBillingRuleCommon = billingRuleCommonOpt.get();

			if (CommonRuleAlgorithm.getPrice(newBillingRuleCommon.getPrices()) < 0) {
				log.info("***换机区域的计费规则不可用***-->areaId：" + newAreaId + "cardTypeId---->>>>" + billingCard.getCardTypeId());
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_AVAILABLE);
			}
			
			// 获取新区域的计费费率
			price = CommonRuleAlgorithm.getPrice(newBillingRuleCommon.getPrices());

			// 查询累计记录
			Optional<LogAcc> logAccOpt = logAccService.findByPlaceIdAndLoginId(placeId, logLogin.getLoginId());
			if (logAccOpt.isPresent()) {
				// 还有正在进行的累计包时,看是否跨规则换机
				LogAcc logAcc = logAccOpt.get();
				Optional<BillingRuleAcc> billingRuleAccOpt = billingRuleAccService.findByPlaceIdAndRuleId(placeId, logAcc.getAccRuleId());
				if (billingRuleAccOpt.isPresent() && !billingRuleAccOpt.get().getAreaIds().contains(newAreaId) && currBillingOnline.getAccFlag() == 2) {
					nextTime = LocalDateTime.now();
					accFlag = 0;
				}
			}
		}

		// 查询最新的卡信息(连锁逻辑要返回漫游金额)
		BillingCard card;
		if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
			// 查询最新的值
			Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
			if (!newOptBillingCard.isPresent()) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
			}

			card = newOptBillingCard.get();
		} else {
			Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
			if (!newOptBillingCard.isPresent()) {
				// 释放锁
				billingLockAlgorithm.releaseLock(billingKey);
				return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
			}
			card = newOptBillingCard.get();
		}
		// 如果上机用户不在包时，且换机后两个区域费率不一致，需要计算是否补差价，写收银台换机网费余额明细
		int unitConsumeDifference = 0; // 要补差的钱
		if (currBillingOnline.getCommonPrice() != price && currBillingOnline.getPackageFlag() == 0) {
			// 换机立即扣费率。这里改成换机补差，不立即111，然后写网费余额明细
			int unitConsume = newBillingRuleCommon.getUnitConsume(); // 换机后的区域单位扣费
			int deductionTime = newBillingRuleCommon.getDeductionTime(); // 换机后的区域扣费时间，15 30 60
//			int price = CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices()); // 换机后当前区域网费价格

			int CommonPriceDifference = price - currBillingOnline.getCommonPrice(); // 两个区域费率差
			if (CommonPriceDifference > 0) {
				// 查询换机前的计费规则
				Optional<BillingRuleCommon> billingRuleCommonOldOpt = billingRuleCommonService.billingRuleCommons(placeId,
						currBillingOnline.getAreaId(), billingCard.getCardTypeId());
				if (!billingRuleCommonOldOpt.isPresent()) {
					return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
				}
				BillingRuleCommon billingRuleCommonOld = billingRuleCommonOldOpt.get();
				int deductionTimeOld = billingRuleCommonOld.getDeductionTime(); // 换机前 的区域扣费时间，15 30 60
				int unitConsumeOld = billingRuleCommonOld.getUnitConsume(); // 换机前 的区域单位扣费
//				int priceOld = CommonRuleAlgorithm.getPrice(billingRuleCommonOld.getPrices()); // 换机前当前区域网费价格,也可以用在线数据的
//				int priceOld = currBillingOnline.getCommonPrice();
				// 换机后的扣费时间
				if (deductionTime == 15 || deductionTime == 30 || deductionTime == 60) {
					// 目前只支持这三个
					double deductionHours = (double) deductionTime / 60;
					unitConsume = (int) (price * deductionHours); // 一次扣的钱
				}
				// 换机前的扣费时间
				if (deductionTimeOld == 15 || deductionTimeOld == 30 || deductionTimeOld == 60) {
					// 目前只支持这三个
					double deductionHoursOld = (double) deductionTimeOld / 60;
					unitConsumeOld = (int) (currBillingOnline.getCommonPrice() * deductionHoursOld); // 一次扣的钱
				}
				// 换机前后的扣费时长相等,直接补上相差的金额
				if (deductionTime == deductionTimeOld) {
					unitConsumeDifference = Math.max(unitConsume - unitConsumeOld, 0); // 一次扣费多少钱的换机差异
					log.info("收银台换机后单位扣费差unitConsumeDifference={},unitConsume={},unitConsumeOld={}", unitConsumeDifference, unitConsume, unitConsumeOld);
				} else {
					// 如果是从长扣费时间（换机前）换到短时间（换机后），如果换机前扣的钱不够还击后的单位扣费，则需要补差价
					// 如果是从短扣费时长换到长扣费时长，则需要看是否需要补差价，补完差价后等下次扣费时间重新计算换机后的费率。计算换机后的总费用（这里按依旧按照旧的扣费单位时间，只需要把换机后相同时间的扣费金额算出来）
					double originalCost = (currBillingOnline.getCommonPrice() / 60) * deductionTimeOld; // 计算原始费用
					// 计算换机后的总费用（这里假设后续使用时间也是30分钟）
					double NewCost = (price / 60) * deductionTimeOld;
					// 补差价：新换机要补扣多少钱，如果大于0是从低换到高价区域
					double additionalCharge = NewCost - originalCost;
					if (additionalCharge > 0) {
						unitConsumeDifference = (int) additionalCharge;
					}
				}
				// 如果换机前后单位扣费不一致
				if (unitConsumeDifference > 0) {
					InviteOnline inviteOnline = null;
					BillingCard inviteCard = null;
					int onceDeduction = 0;
					// 普通计费换机补差
					if (currBillingOnline.getIsInvite() == 2) {
						Optional<InviteOnline> inviteOnlineOpt = inviteOnlineService.findByPlaceIdAndIdNumberAndStatus(placeId, billingCard.getIdNumber(), 1);
						if (inviteOnlineOpt.isPresent()) {
							inviteOnline = inviteOnlineOpt.get();
							inviteCard = inviteOnlineService.getInviteCard(placeId, inviteOnline.getInviteCode());
						}
						if (inviteCard != null) {
							onceDeduction = billingDeductionOnExchange(inviteCard, unitConsumeDifference, placeBizConfig, logLogin.getLoginId(), newClientId);
						}
					} else {
						onceDeduction = billingDeductionOnExchange(billingCard, unitConsumeDifference, placeBizConfig, logLogin.getLoginId(), newClientId);
					}

					log.info("收银台换机后onceDeduction==============={}",onceDeduction);
					if (onceDeduction < 0) {
						log.warn("收银台换机补差价余额不足：：：placeId={}",placeId);
						return new GenericResponse<>(ServiceCodes.BILLING_INSUFFICIENT_BALANCE); // 换机补差余额不足释放锁返回操作失败
					}
					// 扣费后重新获取卡信息
					if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
						// 查询最新的值
						Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
						if (!newOptBillingCard.isPresent()) {
							// 释放锁
							billingLockAlgorithm.releaseLock(billingKey);
							return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
						}

						card = newOptBillingCard.get();
					} else {
						Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
						if (!newOptBillingCard.isPresent()) {
							// 释放锁
							billingLockAlgorithm.releaseLock(billingKey);
							return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
						}
						card = newOptBillingCard.get();
					}

					// 用重新获取的卡信息补差价后的钱
					logLogin.setTotalAccount(card.getTotalAccount());
					// 换机补差扣费完成后，写一条网费余额明细
					if (currBillingOnline.getIsInvite() == 2 && inviteOnline != null && inviteCard != null) {
						// 获取请客人最新的会员余额信息
						if (!"1000".equals(inviteCard.getCardTypeId()) && !"1002".equals(inviteCard.getCardTypeId())) {
							// 查询最新的值
							Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, inviteCard.getIdNumber(), 0);
							if (!newOptBillingCard.isPresent()) {
								// 释放锁
								billingLockAlgorithm.releaseLock(billingKey);
								return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
							}

							inviteCard = newOptBillingCard.get();
						} else {
							Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, inviteCard.getIdNumber());
							if (!newOptBillingCard.isPresent()) {
								// 释放锁
								billingLockAlgorithm.releaseLock(billingKey);
								return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
							}
							inviteCard = newOptBillingCard.get();
						}
						balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(),inviteCard,"",5,0,this.costCash.get(),this.costPresent.get(), "被邀请人换机补差",0,0, inviteOnline.getInviteCode());
					} else {
						log.info("收银台换机后costCash==============={}，costPresent:::::{}",this.costCash.get(),this.costPresent.get());
						balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(),card,newClientId,5,0,this.costCash.get(),this.costPresent.get(), "换机补差",0,0, null);
					}
				}
			}
		}

		// 结束当前BillingOnline的计费
		currBillingOnline.setFinished(1);
		currBillingOnline.setTimerFlag(0);
		currBillingOnline.setUpdated(nowTime);
		billingOnlineService.save(currBillingOnline);

		// 写入新的客户端记录
		BillingOnline newBillingOnline = new BillingOnline();
		newBillingOnline.setPlaceId(placeId);
		newBillingOnline.setClientId(newClientId);
		newBillingOnline.setAreaId(newAreaId);
		newBillingOnline.setCardId(currBillingOnline.getCardId());
		newBillingOnline.setRuleId(newBillingRuleCommon == null ? currBillingOnline.getRuleId() : newBillingRuleCommon.getRuleId());
		newBillingOnline.setIdNumber(currBillingOnline.getIdNumber());
		newBillingOnline.setIdName(currBillingOnline.getIdName());
		newBillingOnline.setCardTypeId(currBillingOnline.getCardTypeId());
		newBillingOnline.setDeduction(unitConsumeDifference);
		newBillingOnline.setDeductionCash(this.costCash.get());
		newBillingOnline.setDeductionPresent(this.costPresent.get());
		newBillingOnline.setLoginId(currBillingOnline.getLoginId());
		newBillingOnline.setNextTime(nextTime);
		newBillingOnline.setBillingTime(currBillingOnline.getBillingTime());
		newBillingOnline.setFinished(0);
		newBillingOnline.setTimerFlag(0);
		newBillingOnline.setCreated(nowTime);
		newBillingOnline.setPackageFlag(currBillingOnline.getPackageFlag());
		newBillingOnline.setCommonPrice(newBillingRuleCommon == null ? currBillingOnline.getCommonPrice() : price);
		newBillingOnline.setAccFlag(accFlag);
		newBillingOnline.setPackagePayFlag(currBillingOnline.getPackagePayFlag());
		newBillingOnline.setIsInvite(currBillingOnline.getIsInvite());
		billingOnlineService.save(newBillingOnline);

		// 更新登录信息
		logLogin.setClientId(newClientId);
		logLogin.setLastClientId(currBillingOnline.getClientId());
		logLogin.setConsumptionTotal(logLogin.getConsumptionTotal() + currBillingOnline.getDeduction());
		logLogin.setConsumptionCashTotal(logLogin.getConsumptionCashTotal() + currBillingOnline.getDeductionCash());
		logLogin.setConsumptionPresentTotal(logLogin.getConsumptionPresentTotal() + currBillingOnline.getDeductionPresent());
		logLogin.setTotalAccount(billingCard.getTotalAccount());
		logLoginService.save(logLogin);

		//
		logHbService.stopBilling(placeId, currClientId);

		//
		Optional<LogHb> optLogHb = logHbService.findByPlaceIdAndClientId(placeId, newClientId);
		if (optLogHb.isPresent()) {
			logHbService.startBilling(placeId, newClientId);
		} else {
			LogHb logHb = new LogHb();
			logHb.setPlaceId(placeId);
			logHb.setClientId(newClientId);
			logHb.setBillingFlag(1);
			logHb.setCreated(nowTime);
			logHb.setHbTime(nowTime);
			logHbService.save(logHb);
		}

		logOperationService.addExchangeClientOperation(SourceType.CASHIER, card, currBillingOnline,
				newBillingOnline, logShift, logLogin);

		placementService.checkRefreshPlacementDate(currBillingOnline.getPlaceId(),currBillingOnline.getClientId()+","+newBillingOnline.getClientId(),"6",
				currBillingOnline.getIdName(),currBillingOnline.getIdNumber(),currBillingOnline.getCardId());

		if (currBillingOnline.getCommonPrice() != newBillingOnline.getCommonPrice()) {
			logOperationService.addConvertBillingRuleOperation(SourceType.CASHIER,0, currBillingOnline.getCommonPrice(), newBillingOnline.getCommonPrice(), billingCard, newBillingOnline, null, null, logShift, logLogin);
		}

		BillingOnlineBO billingOnlineBO = newBillingOnline.toBO();
		billingOnlineBO.setLastOnlineClientId(currClientId);

		//包间换机，更新包间记录表
		Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,billingCard.getCardId());
		if (logRoomOpt.isPresent()) {
			LogRoom logRoom = logRoomOpt.get();
			logRoom.setClientId(newClientId);
			logRoom.setUpdated(LocalDateTime.now());
			logRoomService.save(logRoom);
		}
		// 释放锁
		billingLockAlgorithm.releaseLock(billingKey);
		return new GenericResponse<>(new ObjDTO<>(billingOnlineBO));
	}


	public GenericResponse<?> onlineToOnline(BillingOnline currBillingOnline,BillingOnline newBillingOnline,
											 String placeId,String shiftId,String currAreaId,
											 String currClientId,String newAreaId,String newClientId,LocalDateTime nowTime,int exchangeUpdateRate){
		//查询源区域信息
		GenericResponse<ObjDTO<PlaceAreaBO>> respCurrArea = getArea(placeId,currAreaId);
		if (respCurrArea.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return respCurrArea;
		}
		//查询目标区域信息
		GenericResponse<ObjDTO<PlaceAreaBO>> respNewArea = getArea(placeId,newAreaId);
		if (respNewArea.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return respNewArea;
		}
		PlaceAreaBO currAreaBO = respCurrArea.getData().getObj();
		PlaceAreaBO newAreaBO = respNewArea.getData().getObj();


		//正在上机的区域和要换机的区域 存在一个在包间区域则不能跨区换机
		if (currAreaBO.getIsRoom() == 1 || newAreaBO.getIsRoom() == 1 && (!currAreaId.equals(newAreaId))) {
			return new GenericResponse<>(ServiceCodes.BILLING_ROOM_AREA_NOT_SUPPORT_EXCHANGE);
		}


		// 1,校验两个人在对方区域是否有费率 packageFlag=0 拿ruleId去查BillingRuleCommon packageFlag>0 查BiLlingRulePackageTime，检查当前的ruleId(针对包时),是否在对方区域生效
		// 如果当前是包时,查询是否存在计费规则，判断计费规则中是否包含源区域和目标区域
		// -----> 源客户端换至目标校验
		GenericResponse<ObjDTO<BillingRuleCommonBO>> ruleResponse = verifyPackageFlag(placeId, currBillingOnline, newAreaId, currAreaId);
		if (ruleResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return ruleResponse;
		}
		BillingRuleCommonBO newBillingRuleCommon = ruleResponse.getData().getObj();
		int newPrice = null == newBillingRuleCommon ? currBillingOnline.getCommonPrice() : CommonRuleAlgorithm.getPrice(newBillingRuleCommon.getPrices());
		log.info("newBillingRuleCommon:{},newPrice{}",newBillingRuleCommon,newPrice);
		// -----> 目标客户端换至源客户端校验
		GenericResponse<ObjDTO<BillingRuleCommonBO>> oldRuleResponse = verifyPackageFlag(placeId, newBillingOnline, currAreaId, newAreaId);
		if (oldRuleResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			return oldRuleResponse;
		}
		BillingRuleCommonBO currBillingRuleCommon = oldRuleResponse.getData().getObj();
		int currPrice = null == currBillingRuleCommon ? newBillingOnline.getCommonPrice() : CommonRuleAlgorithm.getPrice(currBillingRuleCommon.getPrices());
		log.info("currBillingRuleCommon:{},currPrice{}",currBillingRuleCommon,currPrice);



		// 查询班次信息
		Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
		if (!optLogShift.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
		}
		LogShift logShift = optLogShift.get();



		//------------------- 查询源上机信息
		// 查询当前计费卡信息
		Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId,
				currBillingOnline.getCardId());
		if (!optBillingCard.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND); // 账号不存在
		}
		BillingCard currBillingCard = optBillingCard.get();
		// 查询登录信息
		Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, currBillingOnline.getCardId(), currBillingOnline.getBillingTime());
		if (!optLogLogin.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
		}
		LogLogin currLogLogin = optLogLogin.get();

		//------------------- 查询目标上机信息
		// 2.查询两个人的Log_login,
		// 查询当前计费卡信息
		optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId,
				newBillingOnline.getCardId());
		if (!optBillingCard.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND); // 账号不存在
		}
		BillingCard newBillingCard = optBillingCard.get();
		// 查询登录信息
		optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, newBillingOnline.getCardId(), newBillingOnline.getBillingTime());
		if (!optLogLogin.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
		}
		LogLogin newLogLogin = optLogLogin.get();



		// 3.开始互换 online.setclientId() Login表clientId lastclientId
		// 结束原有的BillingOnline的计费
		currBillingOnline.setFinished(1);
		currBillingOnline.setTimerFlag(0);
		currBillingOnline.setUpdated(nowTime);
		billingOnlineService.save(currBillingOnline);
		newBillingOnline.setFinished(1);
		newBillingOnline.setTimerFlag(0);
		newBillingOnline.setUpdated(nowTime);
		billingOnlineService.save(newBillingOnline);
		//写入新的BillingOnline
		//源 -> 新
		updateOnlineAndLogin(nowTime,placeId,
				newClientId,newAreaId,currBillingOnline,newBillingRuleCommon,newPrice,
				currLogLogin,currBillingCard,logShift,currBillingCard, exchangeUpdateRate);

		//新 -> 源
		updateOnlineAndLogin(nowTime,placeId,
				currClientId,currAreaId,newBillingOnline,currBillingRuleCommon,currPrice,
				newLogLogin,newBillingCard,logShift,newBillingCard, exchangeUpdateRate);

		// 4.写换机记录
		//添加 curr To new 换机日志
//		addChangeClientOperation(placeId,currBillingCard,currBillingOnline,newBillingOnline,
//				logShift,currLogLogin,newClientId);

		//添加 new To curr 换机日志
//		addChangeClientOperation(placeId,newBillingCard,newBillingOnline,currBillingOnline,
//				logShift,newLogLogin,currClientId);

		//返回的数据
		BillingOnlineBO billingOnlineBO = newBillingOnline.toBO();
		billingOnlineBO.setLastOnlineClientId(currClientId);

		placementService.checkRefreshPlacementDate(currBillingOnline.getPlaceId(),currBillingOnline.getClientId()+","+newBillingOnline.getClientId(),"6",
				currBillingOnline.getIdName(),currBillingOnline.getIdNumber(),currBillingOnline.getCardId());
		return new GenericResponse<>(new ObjDTO<>(billingOnlineBO));
	}


	private GenericResponse<ObjDTO<PlaceAreaBO>> getArea(String placeId,String areaId){
		return placeServerService.findPlaceAreaByPlaceIdAndAreaId(placeId,areaId);
	}

	private GenericResponse<ObjDTO<BillingRuleCommonBO>> verifyPackageFlag(String placeId, BillingOnline billingOnline, String newAreaId, String currAreaId){
		BillingRuleCommonBO billingRuleCommonBO = null;
		if (billingOnline.getPackageFlag() > 0) {
			// 查询包时费率
			Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, billingOnline.getRuleId());
			if (!billingRulePackageTimeOpt.isPresent()) {
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
			}
			BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
			if (!billingRulePackageTime.getAreaIds().contains(newAreaId)) {
				return new GenericResponse<>(ServiceCodes.BILLING_PT_AREA_ERROR);
			}
		}
		//如果是普通计费，查询新换机所在区域的费率信息
		if (billingOnline.getPackageFlag() == 0 && (!newAreaId.equals(currAreaId))) {
			Optional<BillingRuleCommon> billingRuleCommonOpt = billingRuleCommonService.billingRuleCommons(placeId,newAreaId,billingOnline.getCardTypeId());
			if (!billingRuleCommonOpt.isPresent()) {
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_FOUND);
			}
			billingRuleCommonBO = billingRuleCommonOpt.get().toBO();
			if (CommonRuleAlgorithm.getPrice(billingRuleCommonBO.getPrices()) < 0) {
				log.info("***verifyPackageFlag 换机区域的计费规则不可用***-->areaId：" + newAreaId + "cardTypeId---->>>>" + billingOnline.getCardTypeId());
				return new GenericResponse<>(ServiceCodes.BILLING_RULE_NOT_AVAILABLE);
			}
		}
		return new GenericResponse<>(new ObjDTO<>(billingRuleCommonBO));
	}

	private GenericResponse<?> updateOnlineAndLogin(LocalDateTime nowTime, String placeId,String newClientId,
													String newAreaId,BillingOnline currBillingOnline,
													BillingRuleCommonBO newBillingRuleCommon, int newPrice,
													LogLogin currLogLogin,BillingCard billingCard,LogShift logShift,
													BillingCard currBillingCard, int exchangeUpdateRate){
		//写入新的BillingOnline
		BillingOnline toNewBillingOnline = new BillingOnline();
		toNewBillingOnline.setPlaceId(placeId);
		toNewBillingOnline.setClientId(newClientId);
		toNewBillingOnline.setAreaId(newAreaId);
		toNewBillingOnline.setCardId(currBillingOnline.getCardId());
		toNewBillingOnline.setRuleId(newBillingRuleCommon == null ? currBillingOnline.getRuleId() : newBillingRuleCommon.getRuleId());
		toNewBillingOnline.setIdNumber(currBillingOnline.getIdNumber());
		toNewBillingOnline.setIdName(currBillingOnline.getIdName());
		toNewBillingOnline.setCardTypeId(currBillingOnline.getCardTypeId());
		toNewBillingOnline.setDeduction(0);
		toNewBillingOnline.setLoginId(currBillingOnline.getLoginId());
		if (exchangeUpdateRate == 1 && (currBillingOnline.getCommonPrice() != newPrice) && currBillingOnline.getPackageFlag() == 0) {
			// 换机立即扣费率,只针对标准计费
			toNewBillingOnline.setNextTime(nowTime);
		} else {
			toNewBillingOnline.setNextTime(currBillingOnline.getNextTime());
		}
		toNewBillingOnline.setBillingTime(currBillingOnline.getBillingTime());
		toNewBillingOnline.setFinished(0);
		toNewBillingOnline.setTimerFlag(0);
		toNewBillingOnline.setCreated(nowTime);
		toNewBillingOnline.setPackageFlag(currBillingOnline.getPackageFlag());
		toNewBillingOnline.setPackagePayFlag(currBillingOnline.getPackagePayFlag());
		toNewBillingOnline.setCommonPrice(newBillingRuleCommon == null ? currBillingOnline.getCommonPrice() : newPrice);
		toNewBillingOnline.setAccFlag(currBillingOnline.getAccFlag());
		billingOnlineService.save(toNewBillingOnline);

		// 更新登录信息
		currLogLogin.setClientId(newClientId);
		currLogLogin.setLastClientId(currBillingOnline.getClientId());
		currLogLogin.setConsumptionTotal(currLogLogin.getConsumptionTotal() + currBillingOnline.getDeduction());
		currLogLogin.setConsumptionCashTotal(currLogLogin.getConsumptionCashTotal() + currBillingOnline.getDeductionCash());
		currLogLogin.setConsumptionPresentTotal(currLogLogin.getConsumptionPresentTotal() + currBillingOnline.getDeductionPresent());
		currLogLogin.setTotalAccount(billingCard.getTotalAccount());
		logLoginService.save(currLogLogin);

		//添加 curr To new 换机日志
		return addChangeClientOperation(placeId,currBillingCard,currBillingOnline,toNewBillingOnline,
				logShift,currLogLogin,newClientId);
	}

	private GenericResponse<?> addChangeClientOperation(String placeId,BillingCard billingCard,
														BillingOnline currBillingOnline, BillingOnline newBillingOnline,
														LogShift logShift,LogLogin logLogin,String clientId){
		BillingCard currCard;
		if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
			// 查询最新的值
			Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
			if (!newOptBillingCard.isPresent()) {
				return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
			}
			currCard = newOptBillingCard.get();
		} else {
			Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber());
			if (!newOptBillingCard.isPresent()) {
				return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
			}
			currCard = newOptBillingCard.get();
		}
		logOperationService.addExchangeClientOperation(SourceType.CASHIER, currCard, currBillingOnline,
				newBillingOnline, logShift, logLogin);
		placementService.onlineToOnlineRefreshPlacementDate(currBillingOnline.getPlaceId(),
				currBillingOnline.getClientId()+","+newBillingOnline.getClientId(),"7",
				currBillingOnline.getIdName(),currBillingOnline.getIdNumber(),currBillingOnline.getCardId(),
				newBillingOnline.getIdName(), newBillingOnline.getIdNumber(), newBillingOnline.getCardId());

		if (currBillingOnline.getCommonPrice() != newBillingOnline.getCommonPrice()) {
			logOperationService.addConvertBillingRuleOperation(SourceType.CASHIER,0, currBillingOnline.getCommonPrice(), newBillingOnline.getCommonPrice(), billingCard, newBillingOnline, null, null, logShift, logLogin);
		}

		//包间换机，更新包间记录表
		Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,billingCard.getCardId());
		if (logRoomOpt.isPresent()) {
			LogRoom logRoom = logRoomOpt.get();
			logRoom.setClientId(clientId);
			logRoom.setUpdated(LocalDateTime.now());
			logRoomService.save(logRoom);
		}
		return new GenericResponse<>(ServiceCodes.NO_SERVICE);
	}

	/**
	 * 普通计费换机补差方法
	 * @param billingCard   会员卡
	 * @param oncePrice 本次扣费金额
	 * @param placeBizConfig 场所配置
	 * @param loginId   登录ID
	 * @param newClientId  换机后的客户端ID
	 * @return
	 */
	private int billingDeductionOnExchange(BillingCard billingCard, int oncePrice, PlaceBizConfig placeBizConfig, String loginId, String newClientId) {
		if (placeBizConfig.getBillingType() == 0 || "1002".equals(billingCard.getCardTypeId())) { // 网吧不扣费(或者是工作卡)
			return 0;
		}
		log.info("收银台换机时查看billingCard：：：："+ JSON.toJSONString(billingCard));
		// 根据登入卡 获取所有卡
		List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);
		int totalAccount = billingCardDeductionService.sumAccount(billingCards, 2);

		// 校验余额是否足够
		if (totalAccount < oncePrice) { // 余额小于单位扣费
			log.info("收银台换机补差时余额不足oncePrice={},loginId={},cardId={}",oncePrice,loginId, billingCard.getCardId());
			return -1; // 余额不足
		}
		List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, oncePrice, loginId, 0, placeBizConfig.getDeductionOrder());
		log.info("收银台换机扣除的：：：：：：：：：：：：：costDetails={}",JSON.toJSONString(costDetails));
		// 扣费，先扣现金账户
		billingCardService.billingCardDeduction(costDetails, billingCard.getPlaceId());
		if (!ObjectUtils.isEmpty(costDetails)) {
			this.costCash.set(this.costCash.get() + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum));
			this.costPresent.set(this.costPresent.get() + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum));
		}
		return oncePrice;
	}
}
