package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.LogTopupBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收银台现金充值的冲正(新充值规则)
 * CashierReversalBillingCardNewTopupRule(599, "收银台冲正(新)"), // 0x257
 *
 * <AUTHOR>
 * @date 2023年8月
 */
@Service
@Slf4j
public class CashierReversalBillingCardNewTopupRuleServiceImpl implements CoreService {

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    LogOperationService logOperationService;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    LogReversalService logReversalService;

    @Autowired
    TopupRuleService topupRuleService;

    @Autowired
    private LogRecordRewardInstallmentService logRecordRewardInstallmentService;

    @Override
    public GenericResponse<ObjDTO<LogTopupBO>> doService(List<String> params) {

        // 检查参数
        if (params.size() != 6) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        String placeId = params.get(0);
        String shiftId = params.get(1);
        String cardId = params.get(2);
        String topupOrderId = params.get(3); // 充值订单ID
        String fixCashAmountStr = params.get(4); // 修正后的充值金额
//		String fixPresentAmountStr = params.get(5); // 修正后的奖励金额
        String remark = params.get(5); // 原因

        LocalDateTime now = LocalDateTime.now();

        // 处理金额
        int fixCashAmount = 0; // 修正后的充值金额
        int fixPresentAmount = 0; // 修正后的奖励金额
        try {
            fixCashAmount = Integer.parseInt(fixCashAmountStr);
//			fixPresentAmount = Integer.parseInt(fixPresentAmountStr);
        } catch (NumberFormatException nfe) {
            return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
        }
        if (fixCashAmount < 0 || fixCashAmount > 1000000) { // 范围 0-1万元
            return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
        }
//		if (fixPresentAmount < 0 || fixPresentAmount > 100000) { // 范围 0-1千元
//			return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
//		}

        // 查询卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        // 查询当班信息
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift shift = optLogShift.get();

        // 查询充值订单，获取原充值金额
        Optional<LogTopup> optLogTopup = logTopupService.findByPlaceIdAndOrderId(placeId, topupOrderId);
        if (!optLogTopup.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_TOPUP_NOT_FOUND);
        }
        LogTopup logTopup = optLogTopup.get();
        if (logTopup.getPayType() != PayType.CASH && logTopup.getRefundStatus() == 2) { // 退款状态为2表示已经全额退款
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_TOPUP_NOT_FOUND);
        }

        // 包时订单不支持冲正撤销
        if (logTopup.getTopupSourceType() == 1) {
            return new GenericResponse<>(ServiceCodes.BILLING_PACKAGE_ORDER_NOT_SUPPORT_REVERSAL);
        }

        int topupCashAmount = logTopup.getCashAmount();
        int topupPresentAmount = logTopup.getPresentAmount();
        //判断是否是分期赠送订单，如果是分期赠送则需要根据分期赠送计算已赠送金额
        Optional<LogRecordRewardInstallment> logRecordRewardInstallmentOptional = logRecordRewardInstallmentService.findByTopupOrderId(topupOrderId);
        if(logRecordRewardInstallmentOptional.isPresent()){
            LogRecordRewardInstallment logRecordRewardInstallment = logRecordRewardInstallmentOptional.get();
            int numberRecordReward = logRecordRewardInstallment.getSumPresentAmortized() - logRecordRewardInstallment.getOncePresentAmortized(); //已赠送次数
            topupPresentAmount = numberRecordReward * logRecordRewardInstallment.getOncePresentAmount(); //已赠送金额
            log.info("分期赠送订单冲正,总赠送次数：{}，剩余赠送次数：{}，已赠送次数：{}，总赠送金额：{}，已赠送金额：{}，单次赠送金额：{}",logRecordRewardInstallment.getSumPresentAmortized(),logRecordRewardInstallment.getOncePresentAmortized(),
                    numberRecordReward,logRecordRewardInstallment.getSumPresentAmount(),topupPresentAmount,logRecordRewardInstallment.getOncePresentAmount());
        }

        // 查询已经退款金额
        int refundedCashAmount = 0; // 现金已退款金额
        int refundedPresentAmount = 0; // 奖励已退款金额
        if (logTopup.getRefundStatus() == 1) { // 订单已经发生退款，查询退款总额
            List<LogReversal> logReversals = logReversalService.findByPlaceIdAndTopupOrderId(placeId, topupOrderId);
            for (LogReversal refund : logReversals) { // 计算已经退款的金额
                refundedCashAmount += refund.getCashReversal();
                refundedPresentAmount += refund.getPresentReversal();
            }
        }

        // 计算按照新充值金额使用的充值规则
        TopupRule effectedTopupRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId,
                billingCard.getCardTypeId(), fixCashAmount, 1);
        if (effectedTopupRule != null) {
//            if (effectedTopupRule.getTopupMode() == 1) {
//                fixPresentAmount = fixCashAmount; // 充多少，送多少
//            } else {
//                fixPresentAmount = effectedTopupRule.getPresentAmount();
//            }
            fixPresentAmount = logRecordRewardInstallmentService.getInstallmentAmount(effectedTopupRule,fixCashAmount);
        }

        // 检测退款金额是否超出
        if (fixCashAmount + refundedCashAmount > topupCashAmount
                || fixPresentAmount + refundedPresentAmount > topupPresentAmount) {
            return new GenericResponse<>(ServiceCodes.BILLING_REFUND_TO_REVOKE);
        }

        // 计算冲正金额
        int cashRefund = topupCashAmount - refundedCashAmount - fixCashAmount;
        int presentRefund = topupPresentAmount - refundedPresentAmount - fixPresentAmount;
        if (cashRefund > billingCard.getCashAccount() || presentRefund > billingCard.getPresentAccount()) {
            return new GenericResponse<>(ServiceCodes.BILLING_REFUND_TO_REVOKE);
        }

        if (cashRefund + refundedCashAmount == topupCashAmount
                && presentRefund + refundedPresentAmount == topupPresentAmount) {
            logTopup.setRefundStatus(2);
        } else {
            logTopup.setRefundStatus(1);
        }
        logTopup.setUpdated(now);
        logTopupService.save(logTopup);
        //根据订单id删除分期赠送内容，然后写入新的金额分期赠送
        Optional<LogRecordRewardInstallment> byTopupOrderId = logRecordRewardInstallmentService.findByTopupOrderId(logTopup.getOrderId());
        if(byTopupOrderId.isPresent()){
            //删除原来的数据
            logRecordRewardInstallmentService.deletedById(byTopupOrderId.get().getId());
            if(logTopup.getRefundStatus() == 1 && effectedTopupRule != null){
                logTopup.setTopupRuleId(effectedTopupRule.getTopupRuleId());
                logTopup.setPresentAmount(fixPresentAmount);
                //写入新的分期赠送
                logRecordRewardInstallmentService.addTopupRewardInstallment(logTopup);
            }

        }

        billingCardService.billingCardReversal(cashRefund, presentRefund, billingCard);

        LogReversal logReversal = new LogReversal();
        logReversal.setCardId(cardId);
        logReversal.setCashReversal(cashRefund);
        logReversal.setCreated(now);
        logReversal.setIdName(billingCard.getIdName());
        logReversal.setIdNumber(billingCard.getIdNumber());
        logReversal.setOperator(shift.getLoginAccountId());
        logReversal.setPlaceId(placeId);
        logReversal.setPresentReversal(presentRefund);
//		logReversal.setRefundTime(now);
        logReversal.setRemark(remark);
//		logReversal.setRefundType(PayType.CASH);
//		logReversal.setSourceType(SourceType.CASHIER);
        logReversal.setShiftId(shift.getShiftId());
        logReversal.setTopupOrderId(topupOrderId);
        logReversal.setReversalType(0);
        logReversalService.save(logReversal);

        logOperationService.addReversalLogOperation(SourceType.CASHIER, cashRefund, presentRefund, billingCard, shift,
                remark);

        // 返回结果
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }
}
