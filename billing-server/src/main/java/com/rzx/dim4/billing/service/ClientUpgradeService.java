package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.ClientUpgrade;
import com.rzx.dim4.billing.repository.ClientUpgradeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

@Service
public class ClientUpgradeService {

	@Autowired
	ClientUpgradeRepository clientUpgradeRepository;

	/**
	 * 根据客户端类型查询该网吧下的最新下发的版本记录
	 * 
	 * @param placeId
	 * @param clientType
	 * @return
	 */
	public Optional<ClientUpgrade> findByPlaceIdAndClientType(String placeId, String clientType) {
		return clientUpgradeRepository.findByPlaceIdAndClientTypeAndDeleted(placeId, Integer.parseInt(clientType), 0);
	}

	/**
	 *
	 * @param clientType
	 * @param placeIds
	 * @return
	 */
	public List<ClientUpgrade> findByClientTypeAndDeletedAndPlaceIdIn (int clientType,List<String> placeIds) {
		return clientUpgradeRepository.findByClientTypeAndDeletedAndPlaceIdIn(clientType,0,placeIds);
	}

	/**
	 * 根据客户端类型查询所有最新下发的版本记录
	 * 
	 * @param clientType
	 * @return
	 */
	public List<ClientUpgrade> findByClientType(String clientType) {
		return clientUpgradeRepository.findByClientTypeOrderById(Integer.parseInt(clientType));
	}

	/**
	 * 根据客户端类型,版本号查询所有网吧最新下发的版本记录
	 * 
	 * @param clientType
	 * @return
	 */
	public List<ClientUpgrade> findByClientTypeAndVersionIdOrderById(int clientType, String versionId) {
		return clientUpgradeRepository.findByClientTypeAndVersionIdOrderById(clientType, versionId);
	}

	/**
	 * 保存升级记录
	 * 
	 * @param clientUpgrade
	 * @return
	 */
	public ClientUpgrade clientUpgradeSave(ClientUpgrade clientUpgrade) {
		return clientUpgradeRepository.save(clientUpgrade);
	}

	/**
	 * 批量保存升级记录
	 * 
	 * @param clientUpgrades
	 * @return
	 */
	public List<ClientUpgrade> batchUpgradeSave(List<ClientUpgrade> clientUpgrades) {
		return clientUpgradeRepository.saveAll(clientUpgrades);
	}

	/**
	 * 升级列表分页查询
	 * 
	 * @param versionId
	 * @param placeId
	 * @param clientType
	 * @param pageable
	 * @return
	 */
	public Page<ClientUpgrade> findAllClientUpgrade(String versionId, String placeId, int clientType,
			Pageable pageable) {
		if (StringUtils.isEmpty(versionId) && StringUtils.isEmpty(placeId)) {
			return clientUpgradeRepository.findByClientTypeAndDeletedOrderByIdDesc(clientType, 0, pageable);
		} else if (!StringUtils.isEmpty(versionId) && StringUtils.isEmpty(placeId)) {
			return clientUpgradeRepository.findByClientTypeAndVersionIdAndDeletedOrderByIdDesc(clientType, versionId, 0,
					pageable);
		} else if (!StringUtils.isEmpty(placeId) && StringUtils.isEmpty(versionId)) {
			return clientUpgradeRepository.findByClientTypeAndPlaceIdAndDeletedOrderByIdDesc(clientType, placeId, 0,
					pageable);
		}
		return clientUpgradeRepository.findByClientTypeAndPlaceIdAndVersionIdAndDeletedOrderByIdDesc(clientType,
				placeId, versionId, 0, pageable);
	}
}
