package com.rzx.dim4.billing.repository.third;

import com.rzx.dim4.billing.entity.third.ThirdAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ThirdAccountRepository extends JpaRepository<ThirdAccount, Long> , JpaSpecificationExecutor<ThirdAccount> {

    Optional<ThirdAccount> findByThirdAccountId (String thirdAccountId);

    Optional<ThirdAccount> findTop1ByOrderByIdDesc();

    List<ThirdAccount> findByThirdAccountIdInAndStatusAndDeleted(List<String> thirdAccountIds, int status, int deleted);
}
