package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogLogin;
import feign.Param;
import org.apache.zookeeper.Login;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date Jun 28, 2020 11:47:56 AM
 */
public interface LogLoginRepository extends JpaRepository<LogLogin, Long>, JpaSpecificationExecutor<LogLogin> {

    Optional<LogLogin> findByPlaceIdAndCardIdAndLogoutTimeIsNullAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	List<LogLogin> findByPlaceIdAndCardIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndLogoutTimeIsNotNullOrderByIdDesc(String placeId, List<String> cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

    Optional<LogLogin> findByPlaceIdAndClientIdAndLogoutTimeIsNullAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, String clientId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	Optional<LogLogin> findTop1ByPlaceIdInAndIdNumberAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(List<String> placeIds, String idNumber, LocalDateTime startDateTime, LocalDateTime endDateTime);

    Optional<LogLogin> findByPlaceIdAndLoginIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, String loginId, LocalDateTime startDateTime, LocalDateTime endDateTime);

    List<LogLogin> findByPlaceIdAndLogoutTimeGreaterThanEqualAndLogoutTimeLessThanEqualAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId,
                                                                                                                                                      LocalDateTime logoutStartTime,
                                                                                                                                                      LocalDateTime logoutEndTime,
                                                                                                                                                      LocalDateTime startTime,
                                                                                                                                                      LocalDateTime endTime);

    List<LogLogin> findByPlaceIdAndLogoutTimeGreaterThanEqualAndCardIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId,
                                                                                                                                       LocalDateTime logoutEndTime,
                                                                                                                                       List<String> cardIds,
                                                                                                                                       LocalDateTime startTime,
                                                                                                                                       LocalDateTime endTime);

    @Query(value = "SELECT SUM(consumption_total) as  sumConsumptionTotal from log_login where place_id = ?1 and card_id=?2 and logout_time >= ?3 and logout_time <= ?4 AND created >= ?5 AND created <= ?6", nativeQuery = true)
    Integer sumConsumptionByPlaceIdAndCardId(String placeId, String cardId, LocalDateTime logoutStartTime, LocalDateTime logoutEndTime, LocalDateTime startDateTime, LocalDateTime endDateTime);

    @Query(value = "SELECT SUM(consumption_total) as  sumConsumptionTotal from log_login where place_id = ?1 and logout_time >= ?2 and logout_time <= ?3 AND created >= ?4 AND created <= ?5", nativeQuery = true)
    Integer sumConsumptionByPlaceId(String placeId, LocalDateTime logoutStartTime, LocalDateTime logoutEndTime, LocalDateTime startDateTime, LocalDateTime endDateTime);

    List<LogLogin> findByPlaceIdAndLoginIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, List<String> loginIds, LocalDateTime startDateTime, LocalDateTime endDateTime);


    @Query(value = "SELECT * FROM `log_login` WHERE place_id = ?1 AND id_number =  ?2 AND created >= ?3 AND created <= ?4  ORDER BY logout_time DESC LIMIT 1", nativeQuery = true)
    Optional<LogLogin> findLastLoginByIdNumberAndPlaceId(String placeId, String idNumber, LocalDateTime startDateTime, LocalDateTime endDateTime);

	@Query(value = "SELECT ll.id_number idNumber, " +
					"ll.card_id cardId," +
					"count( ll.id ) counts, " +
					"sum( ll.consumption_total ) consumptionTotals," +
					"sum( ll.online_time ) onlineTimes " +
					"FROM `log_login` ll " +
					"WHERE ll.place_id = ?1 " +
					"AND ll.card_id in (?4) " +
					"AND ll.logout_time >= ?2 " +
					"AND ll.logout_time <= ?3 " +
					"AND ll.card_type_id NOT IN ( '1000', '1002' ) " +
				//	"AND ll.logout_time IS NOT NULL " +
					"AND ll.deleted = 0 " +
					"AND ll.created >= ?5 " +
					"AND ll.created <= ?6 " +
					"GROUP BY ll.id_number,ll.card_id " , nativeQuery = true)
	List<Map<String, String>> findConsumerRanking(String placeId, LocalDateTime logoutStartTime, LocalDateTime logoutEndTime,List<String> cardIds, LocalDateTime startDateTime, LocalDateTime endDateTime,  Pageable pageable);


	@Query(value = "SELECT  ll.card_id cardId "+
					"FROM `log_login` ll " +
					"WHERE ll.place_id = ?1 " +
					"AND ll.logout_time >= ?2 " +
					"AND ll.logout_time <= ?3 " +
					"AND ll.card_type_id NOT IN ( '1000', '1002' ) " +
				//	"AND ll.logout_time IS NOT NULL " +
					"AND ll.deleted = 0 " +
					"AND ll.created >= ?4 " +
					"AND ll.created <= ?5 " +
					"GROUP BY ll.id_number,ll.card_id " , nativeQuery = true)
	List<String> findAllCardId(String placeId, LocalDateTime logoutStartTime, LocalDateTime logoutEndTime, LocalDateTime startDateTime, LocalDateTime endDateTime);


	@Query(value = "select count(1) from (SELECT id_number "+
			"FROM `log_login` ll " +
			"WHERE ll.place_id = ?1 " +
			"AND ll.card_id in (?4) " +
			"AND ll.logout_time >= ?2 " +
			"AND ll.logout_time <= ?3 " +
			"AND ll.card_type_id NOT IN ( '1000', '1002' ) " +
		//	"AND ll.logout_time IS NOT NULL " +
			"AND ll.deleted = 0 " +
			"AND ll.created >= ?5 " +
			"AND ll.created <= ?6 " +
			"GROUP BY ll.id_number,ll.card_id ) a" , nativeQuery = true)
	Long countConsumptionByPlaceIdGroupByIdNumber(String placeId, LocalDateTime logoutStartTime, LocalDateTime logoutEndTime,List<String> cardIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

    Optional<LogLogin> findTop1ByPlaceIdAndCardIdAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);


	@Query(value = "SELECT COUNT(id) sumOnlineVisits,SUM(online_time) sumOnlineTime ,client_id clientId FROM log_login WHERE place_id = ?1 AND created >= ?2 AND created <=?3 GROUP BY client_id", nativeQuery = true)
	List<Map<String,String>> queryEverydayClientOnlineTime(String placeId,LocalDateTime startDateTime, LocalDateTime endDateTime);

//	@Query(value = "SELECT created,id_number,card_type_id,card_id,MAX(logout_time) logTime " +
//			"FROM log_login WHERE place_id = ?1 " +
//			"AND card_type_id IN(?2) " +
//			"AND created >= ?3 " +
//			"AND created <= now() " +
//			"GROUP BY id_number  " +
//			"HAVING logTime >= ?3 AND logTime <= ?4  ", nativeQuery = true)
	@Query(value = "" +
			"SELECT   `id`,\n" +
			"  `created`,\n" +
			"  `creater`,\n" +
			"  `deleted`,\n" +
			"  `updated`,\n" +
			"  `card_id`,\n" +
			"  `card_type_id`,\n" +
			"  `client_id`,\n" +
			"  `consumption_total`,\n" +
			"  `last_client_id`,\n" +
			"  `id_number`,\n" +
			"  `login_id`,\n" +
			"  `login_time`,\n" +
			"  `login_type`,\n" +
			"  `logout_operation_creater`,\n" +
			"  `logout_operation_creater_name`,\n" +
			"  `logout_type`,\n" +
			"  `online_time`,\n" +
			"  `place_id`,\n" +
			"  `total_account`,\n" +
			"  `active_time`,\n" +
			"  `chain_flag`,\n" +
			"  `consumption_cash_total`,\n" +
			"  `consumption_present_total` ,MAX(logout_time) logout_time " +
			"FROM log_login " +
			"WHERE place_id = ?1  " +
			"AND card_type_id IN(?2)  " +
			"AND created >= ?3 " +
			"AND created <= ?4 " +
			"GROUP BY id_number ", nativeQuery = true)
	List<LogLogin> queryLastLoginUserByDayAgo(String placeId, List<String> cardTypeIds, LocalDateTime startDateTime,LocalDateTime now, LocalDateTime endDateTime);



	Optional<LogLogin> findTop1ByAndIdNumberAndLogoutTimeIsNotNullAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc( String idNumber, LocalDateTime startDateTime, LocalDateTime endDateTime);

	@Query(value = "SELECT SUM(l.online_time) FROM log_login l " +
			"WHERE l.place_id = :placeId AND l.id_number = :idNumber AND l.created >= :startDateTime AND l.created <= :endDateTime", nativeQuery = true)
	Integer  sumOnlineTimeByPlaceAndIdNumber(String placeId,String idNumber, LocalDateTime startDateTime, LocalDateTime endDateTime);

}
