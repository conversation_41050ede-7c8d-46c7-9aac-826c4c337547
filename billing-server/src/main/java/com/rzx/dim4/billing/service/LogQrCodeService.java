package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.cons.BaseConstants;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.LogQrCode;
import com.rzx.dim4.billing.repository.LogQrCodeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class LogQrCodeService {

	@Autowired
	LogQrCodeRepository logQrCodeRepository;

	@Autowired
	private PlaceServerService placeServerService;

	@Autowired
	private LogQrCodeService logQrCodeService;

	public LogQrCode save(LogQrCode logQrCode) {
		return logQrCodeRepository.save(logQrCode);
	}

	public Optional<LogQrCode> findByToken(String token) {
		return logQrCodeRepository.findByToken(token);
	}

	public Optional<LogQrCode> findEffectiveQrCodeByToken(String token) {
		return logQrCodeRepository.findByTokenAndDeadlineGreaterThanEqual(token,  LocalDateTime.now());
	}

	public Optional<LogQrCode> findLastLogQrCode(String placeId, String clientId, String areaId) {
		return logQrCodeRepository.findFirstByPlaceIdAndClientIdAndAreaIdOrderByIdDesc(placeId, clientId, areaId);
	}

	/**
	 * 获取客户端二维码最新生成记录
	 * @param placeId
	 * @param clientId
	 * @param areaId
	 * @return
	 */
	public Optional<LogQrCode> findLastClientLogQrCode(String placeId, String clientId, String areaId) {
		// 查询场所配置信息信息
		GenericResponse<ObjDTO<PlaceConfigBO>> respPlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
		if (respPlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
			log.error("获取场所配置失败");
			throw new ServiceException(ServiceCodes.getByCode(respPlaceConfig.getCode()));
		}

		PlaceConfigBO placeConfigBO = respPlaceConfig.getData().getObj();
		LocalDateTime deadline = BaseConstants.DEAD_LINE;

		if (placeConfigBO.getQrcodeRefreshTime() <= 0) { // 长期不刷新
			// 获取长期
			return logQrCodeRepository.findFirstByPlaceIdAndClientIdAndAreaIdAndDeadlineOrderByCreatedDesc(placeId, clientId, areaId, deadline);
		} else {
			// 获取短期
			return logQrCodeRepository.findFirstByPlaceIdAndClientIdAndAreaIdAndDeadlineNotOrderByIdDesc(placeId, clientId, areaId, deadline);
		}
	}


	public Optional<LogQrCode> findLastLogQrCode(String placeId, String clientId, String areaId, LocalDateTime deadline) {
		return logQrCodeRepository.findFirstByPlaceIdAndClientIdAndAreaIdAndDeadlineOrderByCreatedDesc(placeId, clientId, areaId, deadline);
	}

	public Optional<LogQrCode> findLastLogQrCode(String placeId, String cashierId, LocalDateTime deadline) {
		return logQrCodeRepository.findFirstByPlaceIdAndCashierIdAndDeadlineOrderByIdDesc(placeId, cashierId, deadline);
	}

	public int clearHistoryQrcode(String placeId, int type) {
		return logQrCodeRepository.clearHistoryQrcode(placeId, type);
	}
	/**
	 * 根据传进来的二维码实体，获取它的场所Id，区域ID、客户端ID,重新生成二维码
	 * */
	public void newCreatedQrcode(LogQrCode logQrCode) {
		LogQrCode logQrCodeNew = new LogQrCode();
		LocalDateTime deadline = BaseConstants.DEAD_LINE;//默认一个远的时间
		logQrCodeNew.setPlaceId(logQrCode.getPlaceId());
		logQrCodeNew.setClientId(logQrCode.getClientId());
		logQrCodeNew.setAreaId(logQrCode.getAreaId());
		logQrCodeNew.setDeadline(deadline);
		logQrCodeNew.setUsed(0);
		String qrCodeToken = Dim4StringUtils.getUUIDWithoutHyphen() + System.currentTimeMillis() / 1000;
		logQrCodeNew.setToken(qrCodeToken);
		logQrCodeNew.setCreated(LocalDateTime.now());
		logQrCodeService.save(logQrCodeNew);
	}
}
