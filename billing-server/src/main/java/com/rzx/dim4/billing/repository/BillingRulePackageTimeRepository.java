package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingRulePackageTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022年1月13日 上午11:04:23
 */
public interface BillingRulePackageTimeRepository
        extends JpaRepository<BillingRulePackageTime, Long>, JpaSpecificationExecutor<BillingRulePackageTime> {

    Optional<BillingRulePackageTime> findByPlaceIdAndRuleIdAndDeleted(String placeId, String rule, int deleted);

    Optional<BillingRulePackageTime> findTop1ByPlaceIdOrderByIdDesc(String placeId);

    List<BillingRulePackageTime> findByPlaceIdAndDeleted(String placeId, int deleted);

    List<BillingRulePackageTime> findByPlaceIdAndDeletedAndCouponRuleOrderByIdDesc(String placeId, int deleted,int couponRule);

    List<BillingRulePackageTime> findByPlaceIdAndRuleIdInAndDeleted(String placeId,List<String> ruleIds,int deleted);

    List<BillingRulePackageTime> findByPlaceIdAndAreaIdsLikeAndDeletedAndCouponRuleOrderByIdDesc(String placeId, String areaId, int deleted,int couponRule);

    @Transactional
    Long deleteByPlaceId(String placeId);

    @Transactional
    @Modifying
    @Query(value = "update billing_rule_package_time set forbidden = :forbidden, updated = now() where place_id = :placeId and rule_id = :ruleId ", nativeQuery = true)
    int updateForbiddenByPlaceIdAndRuleId(@Param("placeId")String placeId,@Param("ruleId") String ruleId,@Param("forbidden") String forbidden);

    @Transactional
    @Modifying
    @Query(value = "update billing_rule_package_time set card_type_id = :cardTypeIds, updated = now() where place_id = :placeId ", nativeQuery = true)
    int updateCardTypeIdsByPlaceId(@Param("placeId")String placeId,@Param("cardTypeIds") String cardTypeIds);
}
