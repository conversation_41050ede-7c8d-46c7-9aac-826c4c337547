package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BookSeats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/12/6
 **/
public interface BookSeatsRepository extends JpaRepository<BookSeats, Long>, JpaSpecificationExecutor<BookSeats> {

    Optional<BookSeats> findByIdAndIdNumber(long id, String idNumber);

    Optional<BookSeats> findByIdAndCardId(long id, String cardId);

    int countByPlaceIdAndUnlockCodeAndDeletedAndStatus(String placeId, String unlockCode, int deleted, int status);


    List<BookSeats> findAllByDeletedAndStatus(int deleted, int status);

    List<BookSeats> findByPlaceIdAndDeletedAndStatus(String placeId, int deleted, int status);

    List<BookSeats> findAllByPlaceIdAndStatusAndIdNumber(String placeId, int status, String idNumber);

    List<BookSeats> findAllByPlaceIdAndUnlockCodeAndDeletedAndStatus(String placeId, String unlockCode, int deleted, int status);
}
