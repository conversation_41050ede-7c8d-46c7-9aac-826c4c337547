package com.rzx.dim4.billing.repository.invite;

import com.rzx.dim4.billing.entity.invite.InviteOnline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年04月27日 13:39
 */
public interface InviteOnlineRepository  extends JpaRepository<InviteOnline, Long>, JpaSpecificationExecutor<InviteOnline> {

    Optional<InviteOnline> findTop1ByPlaceIdAndTypeOrderByIdDesc(String placeId,int type);

    List<InviteOnline> findByPlaceIdAndIdNumberAndStatusAndDeleted(String placeId,String idNumber,int status,int deleted);

    List<InviteOnline> findByPlaceIdAndInviteCodeOrderByIdAsc(String placeId,String inviteCode);

    Optional<InviteOnline> findByPlaceIdAndIdNumberAndStatus (String placeId, String idNumber, int status);

    Optional<InviteOnline> findByPlaceIdAndInviteCodeAndTypeAndStatusNot (String placeId, String inviteCode, int type, int status);

    Optional<InviteOnline> findByPlaceIdAndCardIdAndTypeAndStatusNot (String placeId, String cardId, int type, int status);

    /**
     * 查询所有请客人下机 被请客人在线的记录
     * @return
     */
    @Query(value = "SELECT c.*from invite_online c,(" +
            "SELECT a.* FROM invite_online a,(SELECT DISTINCT place_id,invite_code FROM invite_online where  type = 1 and status != 2 GROUP BY place_id,invite_code)b \n" +
            "WHERE\n" +
            "a.type = 0 \n" +
            "AND a.status = 2 \n" +
            "AND a.place_id = b.place_id \n" +
            "AND a.invite_code = b.invite_code)d where c.type = 1  AND c.status != 2  AND c.place_id = d.place_id  AND c.invite_code = d.invite_code", nativeQuery = true)
    List<InviteOnline> queryNotDismountedInvite();

    List<InviteOnline> findByPlaceIdAndInviteCodeAndType(String placeId,String inviteCode,int type);

    List<InviteOnline> findByPlaceIdAndIdNumberAndInviteCodeAndStatusNot (String placeId, String idNumber, String inviteCode,int status);

    @Modifying
    @Transactional
    @Query(value = "UPDATE invite_online SET status =2 ,updated = now() WHERE place_id = ?1 and invite_code= ?2 ", nativeQuery = true)
    int clearInviteOnline(String placeId,String inviteCode);

    List<InviteOnline> findByPlaceIdAndIdNumberAndStatusNot (String placeId, String idNumber, int status);
}
