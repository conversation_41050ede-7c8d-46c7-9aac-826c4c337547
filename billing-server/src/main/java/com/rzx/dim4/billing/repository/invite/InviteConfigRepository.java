package com.rzx.dim4.billing.repository.invite;

import com.rzx.dim4.billing.entity.invite.InviteConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年04月27日 10:52
 */
public interface InviteConfigRepository extends JpaRepository<InviteConfig, Long>, JpaSpecificationExecutor<InviteConfig> {

    Optional<InviteConfig> findByPlaceId(String placeId);

    @Transactional
    @Modifying
    @Query(value = "update invite_config set accepter_card_type_ids = null, updated = now() where place_id = :placeId ", nativeQuery = true)
    int clearCardTypeIdsByPlaceId(@Param("placeId")String placeId);
}
