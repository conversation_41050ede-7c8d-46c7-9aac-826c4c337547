package com.rzx.dim4.billing.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.stereotype.Service;

import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.service.CoreService;

/**
 * 获取服务器时间
 * 
 * <AUTHOR>
 * @date Jun 4, 2020 2:12:08 PM
 */
@Service
public class ProofTimeServiceImpl implements CoreService {
	@Override
	public GenericResponse<?> doService(List<String> params) {
		DateTimeFormatter dtf = DateTimeFormatter.ISO_DATE_TIME;
		LocalDateTime now = LocalDateTime.now();
		return new GenericResponse<SimpleDTO>(new SimpleDTO(dtf.format(now)));
	}
}
