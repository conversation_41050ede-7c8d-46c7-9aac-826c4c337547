package com.rzx.dim4.billing.service.Invite;

import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.invite.InviteOnline;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.invite.InviteOnlineRepository;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年04月27日 13:46
 */
@Slf4j
@Service
public class InviteOnlineService {

    @Autowired
    private InviteOnlineRepository inviteOnlineRepository;

    @Autowired
    private BillingCardRepository billingCardRepository;

    public InviteOnline save(InviteOnline inviteOnline){
        if (StringUtils.isEmpty(inviteOnline.getInviteCode())) {
            inviteOnline.setInviteCode(getNextCode(inviteOnline.getPlaceId()));
        }
        return inviteOnlineRepository.save(inviteOnline);
    }

    @Synchronized
    private String getNextCode(String placeId) {
        int code = 1001;
        Optional<InviteOnline> inviteOnlineOptional= inviteOnlineRepository.findTop1ByPlaceIdAndTypeOrderByIdDesc(placeId,0);
        if (inviteOnlineOptional.isPresent()) {
            code = Integer.parseInt(inviteOnlineOptional.get().getInviteCode()) + 1;
        }
        return String.valueOf(code);
    }

    public void deletedById(Long id){
        inviteOnlineRepository.deleteById(id);
    }

    public Optional<InviteOnline> findById(Long id){
        return inviteOnlineRepository.findById(id);
    }

    public List<InviteOnline> findByPlaceIdAndIdNumberAndStatusAndDeleted(String placeId,String idNumber,int status){
        return inviteOnlineRepository.findByPlaceIdAndIdNumberAndStatusAndDeleted(placeId, idNumber, status, 0);
    }

    public List<InviteOnline> findByPlaceIdAndInviteCode(String placeId,String inviteCode){
        return inviteOnlineRepository.findByPlaceIdAndInviteCodeOrderByIdAsc(placeId,inviteCode);
    }


    /**
     * 查询用户未失效的请客上网信息，已失效的就不是Optional了。
     * @param placeId
     * @param idNumber
     * @param status
     * @return
     */
    public Optional<InviteOnline> findByPlaceIdAndIdNumberAndStatus (String placeId, String idNumber,int status){
        return inviteOnlineRepository.findByPlaceIdAndIdNumberAndStatus (placeId, idNumber, status);
    }

    /**
     * 根据请客信息获取请客人信息
     * @param placeId
     * @param inviteCode
     * @param status
     * @return
     */
    public Optional<InviteOnline> findByPlaceIdAndInviteCodeAndStatusNot (String placeId, String inviteCode, int status){
        return inviteOnlineRepository.findByPlaceIdAndInviteCodeAndTypeAndStatusNot(placeId, inviteCode, 0, status);
    }

    /**
     * 根据请客信息获取请客人会员卡信息
     * @param placeId
     * @param inviteCode
     * @return
     */
    public BillingCard getInviteCard (String placeId, String inviteCode) {
        Optional<InviteOnline> inviteOnlineOpt = findByPlaceIdAndInviteCodeAndStatusNot(placeId, inviteCode, 2);
        if (!inviteOnlineOpt.isPresent()) {
            return null;
        }
        InviteOnline inviteOnline = inviteOnlineOpt.get();
        String inviteIdNumber = inviteOnline.getIdNumber();
        Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndIdNumber(placeId, inviteIdNumber);
        if (!billingCardOpt.isPresent()) {
            return null;
        }
        return billingCardOpt.get();
    }

    /**
     * 根据cardId获取请客上网信息
     * @param placeId
     * @param cardId
     * @param type
     * @param status
     * @return
     */
    public Optional<InviteOnline> findByPlaceIdAndCardIdAndTypeAndStatusNot (String placeId, String cardId, int type, int status) {
        return inviteOnlineRepository.findByPlaceIdAndCardIdAndTypeAndStatusNot(placeId, cardId, type, status);
    }

    public  List<InviteOnline> findByPlaceIdAndInviteCodeAndType(String placeId,String inviteCode,int type){
        return inviteOnlineRepository.findByPlaceIdAndInviteCodeAndType(placeId,inviteCode,type);
    }

    public Page<InviteOnline> findAll(Map<String, Object> param, Pageable pageable) {
        return inviteOnlineRepository.findAll(new Specification<InviteOnline>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<InviteOnline> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicates = new ArrayList<Predicate>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = cb.equal(root.get("deleted"), 0);
                predicates.add(p1);

                // 场所ID
                if (param.containsKey("placeId") && !StringUtils.isEmpty(param.get("placeId"))) {
                    Predicate predicate = cb.equal(root.get("placeId").as(String.class), param.get("placeId"));
                    predicates.add(predicate);
                }
                if (param.containsKey("type") && !StringUtils.isEmpty(param.get("type"))) {
                    Predicate predicate = cb.equal(root.get("type").as(String.class), param.get("type"));
                    predicates.add(predicate);
                }
                if (param.containsKey("idNumber") && !StringUtils.isEmpty(param.get("idNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), "%" + param.get("idNumber") + "%");
                    predicates.add(predicate);
                }
                if (param.containsKey("startTime") && !org.springframework.util.StringUtils.isEmpty(param.get("startTime"))) {// 开卡时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(param.get("startTime")), fmt);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }
                if (param.containsKey("endTime") && !org.springframework.util.StringUtils.isEmpty(param.get("endTime"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(param.get("endTime")), fmt);
                    predicates.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                Predicate[] p = new Predicate[predicates.size()];
                return cb.and(predicates.toArray(p));
            }
        }, pageable);
    }

    /**
     * 查询所有请客人下机 被请客人在线的记录
     * @return
     */
    public List<InviteOnline> queryNotDismountedInvite () {
        return inviteOnlineRepository.queryNotDismountedInvite();
    }

    public List<InviteOnline> findByPlaceIdAndIdNumberAndInviteCodeAndStatusNot (String placeId, String idNumber,String inviteCode, int status){
        return inviteOnlineRepository.findByPlaceIdAndIdNumberAndInviteCodeAndStatusNot(placeId,idNumber,inviteCode,status);
    }

    public int clearInviteOnline(String placeId,String inviteCode){
        return inviteOnlineRepository.clearInviteOnline(placeId,inviteCode);
    }

    public List<InviteOnline> findByPlaceIdAndIdNumberAndStatusNot(String placeId, String idNumber,int status){
        return inviteOnlineRepository.findByPlaceIdAndIdNumberAndStatusNot(placeId,idNumber,status);
    }
}
