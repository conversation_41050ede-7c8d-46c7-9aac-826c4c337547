package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ExchangePointsType;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogPoints;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.PlaceChainStores;
import com.rzx.dim4.billing.repository.LogPointsRepository;
import com.rzx.dim4.billing.repository.PlaceChainStoresRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LogPointsService {

    @Autowired
    LogPointsRepository logPointsRepository;

    @Autowired
    PlaceChainStoresRepository placeChainStoresRepository;

    @Autowired
    BillingCardService billingCardService;

    /**
     * 添加一条积分变更记录
     *
     * @param exchangePointsType 变更类型
     * @param cost               总消费/充值/兑换网费
     * @param exchangePoints     变更点数
     * @param billingCard        计费卡信息
     * @param logShift           班次信息
     * @return
     */
    public LogPoints addLogPointsOperation(ExchangePointsType exchangePointsType,
                                           int exchangePoints,
                                           int cost,
                                           BillingCard billingCard,
                                           LogShift logShift,
                                           Boolean roamFlag) {
        Assert.notNull(exchangePointsType, "exchangePointsType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");

        String details = "";
        if (exchangePointsType.equals(ExchangePointsType.SIGNED)) {
            details = "签到赠送:" + exchangePoints + "积分";
        } else if (exchangePointsType.equals(ExchangePointsType.TOPUP)) {
            details = "充值网费:" + cost / 100 + "元";
        } else if (exchangePointsType.equals(ExchangePointsType.LOGOUT)) {
            details = "本次上机消费:" + cost / 100 + "元";
        } else if (exchangePointsType.equals(ExchangePointsType.REVERSAL)) {
            details = "积分调整("+ logShift.getAccountName()+ ")";
        } else {
            if (roamFlag) {
                details = "漫游积分兑换:" + cost / 100 + "元网费";
            } else {
                details = "兑换:" + cost / 100 + "元网费";
            }
            exchangePoints = -exchangePoints;
        }

        LocalDateTime now = LocalDateTime.now();
        LogPoints log = new LogPoints();
        log.setCreated(now.minusNanos(now.getNano())); // 不要毫秒
        log.setExchangePointsType(exchangePointsType);
        log.setExchangePoints(exchangePoints);
        log.setTotalPoints(billingCard.getPoints());
        log.setPlaceId(billingCard.getPlaceId());
        log.setCardId(billingCard.getCardId());
        log.setIdNumber(billingCard.getIdNumber());
        log.setIdName(billingCard.getIdName());
       // log.setCardId(billingCard.getCardId());
        log.setCardTypeId(billingCard.getCardTypeId());
        log.setCardTypeName(billingCard.getCardTypeName());
        log.setDetails(details);
        log.setCreaterName(StringUtils.isEmpty(logShift) ? "用户" : logShift.getLoginAccountName());
        log.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logPointsRepository.save(log);
        return log;
    }

    public Page<LogPoints> findAll(Map<String, Object> map, Pageable pageable) {
        return logPointsRepository.findAll((Specification<LogPoints>) (root, query, cb) -> {
            List<Predicate> andPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所ID
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
            }

            if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {// 卡ID
                andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
            }

            if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {// 卡类型id
                andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), map.get("cardTypeId")));
            }

            if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                andPredicateList.add(cb.like(root.get("idNumber"), "%" + map.get("idNumber") + "%"));
            }
            if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 姓名
                andPredicateList.add(cb.like(root.get("idName"), "%" + map.get("idName") + "%"));
            }

            if (map.containsKey("createrName") && !StringUtils.isEmpty(map.get("createrName"))) { // 操作人姓名
                andPredicateList.add(cb.like(root.get("createrName"), "%" + map.get("createrName") + "%"));
            }

            if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {// 开始时间
                LocalDateTime startTime = LocalDateTime.parse(map.get("startDate").toString(), fmt);
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {// 结束时间
                LocalDateTime endTime = LocalDateTime.parse(map.get("endDate").toString(), fmt);
                andPredicateList
                        .add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
            }
            if (map.containsKey("exchangePointsType") && !StringUtils.isEmpty(map.get("exchangePointsType"))) {// 变更类型
                andPredicateList.add(cb.equal(root.get("exchangePointsType").as(String.class), map.get("exchangePointsType")));
            }
            if (map.containsKey("exchangePointsTypeArr") && !StringUtils.isEmpty(map.get("exchangePointsTypeArr"))) {// 变更类型
                andPredicateList.add(root.get("exchangePointsType").as(String.class).in(map.get("exchangePointsTypeArr")));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            return cb.and(andPredicateList.toArray(andPredicateArr));
        }, pageable);
    }

    /**
     * 查询变更类型的次数
     *
     * @param placeId
     * @param exchangePointsType
     * @param cardId
     * @return
     */
    public int countSignedNum(String placeId, ExchangePointsType exchangePointsType, String cardId) {
        Integer result = logPointsRepository.countSignedNum(placeId, exchangePointsType, cardId);
        return result == null ? 0 : result;
    }

    /**
     * 漫游积分兑换
     *
     * @param points
     * @param exchangePresent
     * @param billingCard
     * @param logShift
     * @return
     */
    public ServiceCodes roamExchangePoints(int points, int exchangePresent, BillingCard billingCard, LogShift logShift) {

        // 查询是否是连锁
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresRepository.findByPlaceIdAndDeleted(billingCard.getPlaceId(), 0);
        if (optPlaceChainStores.isPresent()) {
            int exchangePoints = points;
            Optional<BillingCard> cardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(billingCard.getPlaceId(), billingCard.getIdNumber(), 0);
            if (!cardOpt.isPresent()) {
                return ServiceCodes.BILLING_CARD_NOT_FOUND;
            }

            // 漫游总积分也不足
            if (cardOpt.get().getPoints() < points) {
                return ServiceCodes.BILLING_POINTS_NOT_ENOUGH;
            }

            // 优先扣当前卡
            Boolean roamFlag = false; // 是否扣漫游积分
            if (billingCard.getPoints() >= points) {
                billingCard.setPoints(billingCard.getPoints() - points);
                points = 0;
            } else if (billingCard.getPoints() >= 0) {
                points = points - billingCard.getPoints();
                billingCard.setPoints(0);
                roamFlag = true;
            }


            billingCard.setPresentAccount(billingCard.getPresentAccount() + exchangePresent);
            billingCard.setUpdated(LocalDateTime.now());

            if (roamFlag) {
                List<PlaceChainStores> placeChainStoresList = placeChainStoresRepository.findByChainIdAndDeleted(optPlaceChainStores.get().getChainId(), 0);
               // List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).filter(e -> !e.equals(billingCard.getPlaceId())).collect(Collectors.toList()); // 获取连锁场所ID列表
                // 查询所有漫游卡信息
                List<BillingCard> billingCards = billingCardService.findByChainIdAndIdNumberByMember(optPlaceChainStores.get().getChainId(), billingCard.getIdNumber());
                billingCards = billingCards.stream().filter(e -> !e.getPlaceId().equals(billingCard.getPlaceId())).collect(Collectors.toList());
                for (BillingCard card : billingCards) {
                    if (points > 0) {
                        for (PlaceChainStores pcs : placeChainStoresList) {
                            if (card.getPlaceId().equals(pcs.getPlaceId()) && pcs.getShareMemberPoint() == 1) {
                                if (card.getPoints() >= points) {
                                    card.setPoints(card.getPoints() - points);
                                    card.setUpdated(LocalDateTime.now());
                                    billingCardService.save(card);
                                    points = 0;
                                    break;
                                }
                                if (card.getPoints() > 0 && card.getPoints() < points) {
                                    points = points - card.getPoints();
                                    card.setPoints(0);
                                    card.setUpdated(LocalDateTime.now());
                                    billingCardService.save(card);
                                }
                            }
                        }
                    }
                }
            }

            // 漫游入库
            try {
                billingCardService.save(billingCard);
                // 查询最新的值
                Optional<BillingCard> newcardOpt = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(billingCard.getPlaceId(), billingCard.getIdNumber(), 0);
                if (!newcardOpt.isPresent()) {
                    return ServiceCodes.BILLING_CARD_NOT_FOUND;
                }
                addLogPointsOperation(ExchangePointsType.EXCHANGE, exchangePoints, exchangePresent, newcardOpt.get(), logShift, roamFlag);
            } catch (Exception e) {
                log.info(e.getMessage());
                return ServiceCodes.OPT_ERROR;
            }
        } else {
            // 单店
            // 可用积分校验
            if (billingCard.getPoints() < points) {
                return ServiceCodes.BILLING_POINTS_NOT_ENOUGH;
            }

            // 积分兑换网费
            billingCard.setPoints(billingCard.getPoints() - points);
            billingCard.setPresentAccount(billingCard.getPresentAccount() + exchangePresent);
            billingCard.setUpdated(LocalDateTime.now());

            try {
                billingCardService.save(billingCard);
                addLogPointsOperation(ExchangePointsType.EXCHANGE, points, exchangePresent, billingCard, logShift, false);
            } catch (Exception e) {
                log.info(e.getMessage());
                return ServiceCodes.OPT_ERROR;
            }
        }
        return ServiceCodes.NO_ERROR;
    }
}
