package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.TopupRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date Jul 13, 2020 3:12:26 PM
 */
public interface TopupRuleRepository extends JpaRepository<TopupRule, Long> {

	@Modifying
	@Transactional
	@Query(value = "update topup_rule set deleted = 1, updated = now() where place_id = :placeId and card_type_id != '1001' and deleted = 0", nativeQuery = true)
	int deleteTopupRulesByPlaceId(String placeId);

	Optional<TopupRule> findTop1ByPlaceIdOrderByIdDesc(String placeId);

	Optional<TopupRule> findByPlaceIdAndTopupRuleIdAndDeleted(String placeId, String topupRuleId, int deleted);

	List<TopupRule> findByPlaceIdAndDeleted(String placeId, int deleted);
	List<TopupRule> findByPlaceIdAndTopupTypeAndDeleted(String placeId, int topupType, int deleted);

	List<TopupRule> findByPlaceIdAndTopupTypeAndCardTypeIdAndDeleted(String placeId, int topupType, String cardTypeId, int deleted);

	List<TopupRule> findByPlaceIdAndCardTypeIdAndLimitClientShowAndDeleted(String placeId, String cardTypeId, int showAll, int deleted);



	List<TopupRule> findByPlaceIdAndCardTypeIdAndClientUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(String placeId, String cardTypeId, int clientUsage, int deleted,int status,int memberRecharge,int amount);

	List<TopupRule> findByPlaceIdAndCardTypeIdAndCashierUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(String placeId, String cardTypeId, int cashierUsage, int deleted,int status,int memberRecharge,int amount);

	List<TopupRule> findByPlaceIdAndCardTypeIdAndMobileUsageAndDeletedAndStatusAndMemberRechargeAndAmountLessThanEqualOrderByAmountDescPresentAmount(String placeId, String cardTypeId, int mobileUsage, int deleted,int status,int memberRecharge,int amount);

	// （含卡类型）查询所有收银台可用的充值规则，按充值金额和赠送金额升序排序
	List<TopupRule> findByPlaceIdAndCardTypeIdAndCashierUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(String placeId, String cardTypeId, int cashierUsage, int deleted,int status);

	// （含卡类型）查询所有客户端可用的充值规则，按充值金额和赠送金额升序排序
	List<TopupRule> findByPlaceIdAndCardTypeIdAndClientUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(String placeId, String cardTypeId, int clientUsage, int deleted,int status);

	// （含卡类型）查询所有移动端可用的充值规则，按充值金额和赠送金额升序排序
	List<TopupRule> findByPlaceIdAndCardTypeIdAndMobileUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(String placeId, String cardTypeId, int mobileUsage, int deleted,int status);

	//不含卡类型:获取收银台的充值规则，按充值金额和赠送金额升序排序
	List<TopupRule> findByPlaceIdAndCashierUsageAndDeletedAndStatusOrderByAmountAscPresentAmount(String placeId, int cashierUsage, int deleted,int status);



	List<TopupRule> findByPlaceIdAndCardTypeIdAndDeleted(String placeId, String cardTypeId, int deleted);

	List<TopupRule> findByPlaceIdOrderByIdDesc(String placeId);

	List<TopupRule> findByPlaceIdAndDeletedOrderByIdDesc(String placeId, int deleted);

	Optional<TopupRule> findTop1ByPlaceIdAndTopupTypeAndAmountAndPresentAmountAndDeleted(
			String placeId, int topupType, int amount, int presentAmount, int deleted);

	List<TopupRule> findByPlaceIdAndTopupTypeAndAmountAndPresentAmountAndEffectiveDaysAndDeleted(
			String placeId, int topupType, int amount, int presentAmount, String effectiveDays, int deleted);

	List<TopupRule> findByPlaceIdAndTopupTypeAndAmountAndDeletedAndCardTypeIdIn(
			String placeId, int topupType, int amount, int deleted, List<String> cardTypeIds);

	Optional<TopupRule> findTop1ByPlaceIdAndAmountAndPresentAmountAndDeleted(
			String placeId,int amount, int presentAmount, int deleted);

	// 根据充值金额、赠送金额、生效日期（每月）模式下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveDays, int deleted);

	// 根据充值金额、赠送金额、生效日期（每周）模式下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveWeekDays, int deleted);

	// 根据充值金额、赠送金额、生效日期（每月）模式、卡类型下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndCardTypeIdAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveDays, String  cardTypeId,int deleted);

	// 根据充值金额、赠送金额、生效日期（每周）模式、卡类型下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndCardTypeIdAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveWeekDays, String  cardTypeId,int deleted);

	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndDeletedAndEffectiveModeAndCardTypeIdIn(
			String placeId, int amount,int presentAmount, int deleted, int effectiveMode,List<String> cardTypeIds);

	//根据充值金额、赠送金额、生效日期（每月）模式、卡类型、全部使用场景下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveDaysAndCashierUsageAndClientUsageAndMobileUsageAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveDays,int cashierUsage, int clientUsage,int mobileUsage,int deleted);

	// 根据充值金额、赠送金额、生效日期（每周）模式、卡类型、全部场景下查询数据
	List<TopupRule> findByPlaceIdAndAmountAndPresentAmountAndEffectiveWeekDaysAndCashierUsageAndClientUsageAndMobileUsageAndDeleted(
			String placeId, int amount, int presentAmount, String effectiveWeekDays, int cashierUsage, int clientUsage,int mobileUsage,int deleted);
}
