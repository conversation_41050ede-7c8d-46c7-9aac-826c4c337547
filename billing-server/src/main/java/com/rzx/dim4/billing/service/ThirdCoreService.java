package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.web.controller.ThirdCoreController;
import java.util.List;

/**
 * 第三方请求接口
 *
 * @see ThirdCoreController 相关文档看类文档
 * <AUTHOR>
 * @since 2024/1/16
 **/
public interface ThirdCoreService {

    /**
     * 第三方执行业务
     * @param params 业务参数
     * @return 业务执行结果
     */
    GenericResponse<?> doService(List<String> params);
}
