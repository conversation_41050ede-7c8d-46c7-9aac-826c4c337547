package com.rzx.dim4.billing.service.third;

import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.BillingCardType;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.service.BillingCardTypeService;
import com.rzx.dim4.billing.service.domain.BillingPayDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/20
 **/
@Slf4j
@Service
public class MarketServiceImpl implements MarketService {

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private BillingPayDomainService billingPayDomainService;

    @Autowired
    private PlaceServerService placeServerService;

    @Override
    public PaymentResultBO createCard(String placeId, String cardTypeId, String idNumber, String name, String amount, String payType, String openId, String identification, String cashierId, String address, String issuingAuthority, String nation, String phoneNumber, String validPeriod, ThirdAccount thirdAccount, String payCode) {

        int amountInt;
        try {
            amountInt = Integer.parseInt(amount);
        } catch (NumberFormatException e) {
            log.error("amount is not a number");
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }

        // 金额必须大于0
        if (amountInt < 0 || amountInt == 0) {
            log.error("amount is less than zero");
            throw new ServiceException(ServiceCodes.PAYMENT_BAD_AMOUNT);
        }

        // 校验场所状态
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (!placeConfig.isResult()) {
            throw new ServiceException(placeConfig.getMessage());
        }
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        // 先下单，支付成功回调后开卡
        BillingCardType billingCardType = billingCardTypeService.getBillingCardType(placeId, cardTypeId);
        int minCreateCardAmount = billingCardType.getMinCreateCardAmount();
        if (amountInt < minCreateCardAmount) {
            log.error("amount is less than minCreateCardAmount = {}", minCreateCardAmount);
            throw new ServiceException(ServiceCodes.PAYMENT_BAD_AMOUNT, "当前无法开卡，开卡金额小于最小开卡金额" + minCreateCardAmount / 100 + "元");
        }

        return billingPayDomainService.doThirdCreateOrder(thirdAccount.getThirdAccountId(),
                placeId,
                "",
                cardTypeId,
                idNumber,
                name,
                amount,
                payType,
                openId,
                payCode);
    }
}
