package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.cons.BillingConstants;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.util.CreateCardValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 收银台带钱开卡-无送充值(新充值规则)
 *
 * <AUTHOR>
 * @since 2023/8
 **/
@Slf4j
@Service
public class CashierCreateBillingCardNoPresentAmountServiceImpl implements CoreService {

    @Autowired
    private IBillingCardService iBillingCardService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private CreateCardValidator createCardValidator;

    @Override
    public GenericResponse<ObjDTO<BillingCardBO>> doService(List<String> params) {

        // 必填：0.场所ID , 1.卡类型ID，2.收银台ID 3.身份证号码，4.姓名，5.地址，6.签发机关，7.民族, 8.有效期
        if (params.size() > 15) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 获取参数
        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardTypeId = params.get(2); // 卡类型ID
        String idNumber = params.get(3); // 身份证号码
        String name = params.get(4); // 姓名
        String address = params.get(5); // 地址
        String issuingAuthority = params.get(6); // 发证机关
        String nation = params.get(7); // 民族
        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡
        String activeType = params.get(9); // 激活方式,传value值
        String identification = params.get(10); // 附加费标识
        String phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
        String remark = params.get(12); // 备注
        if (!StringUtils.isEmpty(remark) && remark.length() > 100) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        int cashAmount;
        String cashAmountStr = params.get(13); // 充值金额
        // 处理充金额和赠送金额
        try {
            cashAmount = Integer.parseInt(cashAmountStr);
        } catch (NumberFormatException nfe) {
            return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
        }
        String autoCreateCard = null;
        if(params.size()>=15){
            autoCreateCard = params.get(14);
        }
        
        /*
         * 补充idNumber不为18位大陆居民身份证号的情况
         */
        if (StringUtils.isEmpty(idNumber) || idNumber.length() < 6 || idNumber.length() > 20 || StringUtils.isEmpty(name) || name.length() > 50) { //
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if (idNumber.length() != 18) { // 非大陆居民身份证
            String regex = "^[a-zA-Z0-9]+$"; // 只能是数字和字母
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(idNumber);
            if (!matcher.matches()) {
                return new GenericResponse<>(ServiceCodes.ID_NUMBER_ERROR);
            }
            PlaceBizConfig config = placeBizConfigService.findByPlaceId(placeId); // 查询网吧的非身份证配置
            if (config.getNonIdNumber() > 0) { // 大于0，允许非身份证号注册，需要判断今天已经激活的非身份证数量
                int activated = logOperationService.countTodayActivatedNonIdNumberByPlaceId(placeId);
                if (activated >= config.getNonIdNumber()) { // 激活数量已经达到上限
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
                }
            } else if (config.getNonIdNumber() == 0) { // 等于0， 不允许非身份证号注册，不允许注册非身份证号码
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
            }
        } else { // 大陆居民身份证
            // 验证身份证合法性，姓名不能为空
            idNumber = idNumber.toUpperCase();
            boolean flag = IdNumberValidator.verificate(idNumber);
            if (!flag || StringUtils.isEmpty(name)) {
                return new GenericResponse<>(ServiceCodes.ID_NUMBER_ERROR);
            }
        }

        // 校验班次
        Optional<LogShift> logShiftOpt = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!logShiftOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = logShiftOpt.get();

        //判断是否为自助开卡
        if(!StringUtils.isEmpty(autoCreateCard) && "autoCreateCard".equals(autoCreateCard)){
            //校验自助开卡
            createCardValidator.verifyAutoCreateCard(placeId,cardTypeId,cashAmount,logShift);
            remark = BillingConstants.AUTO_CARD_IDENT;
        }

        LocalDateTime now = LocalDateTime.now();

        BillingCardBO billingCardBO = new BillingCardBO();
        billingCardBO.setPlaceId(placeId);
        billingCardBO.setCardTypeId(cardTypeId);
        billingCardBO.setIdName(name);
        billingCardBO.setIdNumber(idNumber);
        billingCardBO.setActiveType(activeType);
        billingCardBO.setAddress(address);
        billingCardBO.setLoginName(idNumber);
        billingCardBO.setNation(nation);
        billingCardBO.setIssuingAuthority(issuingAuthority);
        billingCardBO.setPhoneNumber(phoneNumber);
        billingCardBO.setRemark(remark);
        billingCardBO.setValidPeriod(StringUtils.isEmpty(validPeriod) ? "-1" : validPeriod);


        LogTopup logTopup = new LogTopup();
        logTopup.setCashAmount(cashAmount);
        logTopup.setPresentAmount(0);
        logTopup.setCreated(now);
        logTopup.setCreater(null);
        logTopup.setIdName(name);
        logTopup.setIdNumber(idNumber);
        logTopup.setCardTypeId(cardTypeId);
        logTopup.setOrderId("CASH" + System.currentTimeMillis());
        logTopup.setOperator(logShift.getLoginAccountId());
        logTopup.setOperatorName(logShift.getLoginAccountName());
        logTopup.setShiftId(shiftId);
        logTopup.setStatus(2);
        logTopup.setPayType(PayType.CASH);
        logTopup.setPlaceId(placeId);
        logTopup.setSourceType(SourceType.CASHIER);
        logTopup.setTopupTime(now);
        logTopup.setTopupRuleId(null);

        billingCardBO = iBillingCardService.autoCreateCardWhenTopup(logTopup, billingCardBO, identification);

        return new GenericResponse<>(new ObjDTO<>(billingCardBO));
    }
}