package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.repository.LogShiftRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021年8月30日 下午6:41:51
 */
@Slf4j
@Service
public class LogShiftService {

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    LogShiftRepository logShiftRepository;

    public LogShift save(LogShift logShift) {
        return logShiftRepository.save(logShift);
    }

    public Optional<LogShift> findByPlaceIdAndShiftIdAndStatus(String placeId, String shiftId, int status) {
        return logShiftRepository.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, status);
    }

    public Optional<LogShift> findByPlaceIdAndCashierIdAndStatus(String placeId, String cashierId, int status) {
        return logShiftRepository.findByPlaceIdAndCashierIdAndStatus(placeId, cashierId, status);
    }

    public Optional<LogShift> findTop1ByPlaceIdAndCashierIdAndStatusOrderByIdDesc(String placeId, String cashierId, int status) {
        return logShiftRepository.findTop1ByPlaceIdAndCashierIdAndStatusOrderByIdDesc(placeId, cashierId, status);
    }

    // 组装交接班商超统计数据
    public void getShopStatistics(PlaceShiftBO placeShiftBO, Map<String, Integer> map) {
        if (!org.springframework.util.StringUtils.isEmpty(map)) {

            // 商超现金收入
            if (map.containsKey("shopCashTotal")) {
                placeShiftBO.setShopCashTotal(map.get("shopCashTotal"));
            }

            // 商超现金退款
            if (map.containsKey("shopCashRefund")) {
                placeShiftBO.setShopCashRefund(map.get("shopCashRefund"));
            }

            // 商超线上收入
            if (map.containsKey("shopOnlineTotal")) {
                placeShiftBO.setShopOnlineTotal(map.get("shopOnlineTotal"));
            }

            // 商超线上退款
            if (map.containsKey("shopOnlineRefund")) {
                placeShiftBO.setShopOnlineRefund(map.get("shopOnlineRefund"));
            }

            // 商超卡扣
            if (map.containsKey("shopCardTotal")) {
                placeShiftBO.setShopCardTotal(map.get("shopCardTotal"));
            }

            // 商超内部损耗
            if (map.containsKey("shopLossTotal")) {
                placeShiftBO.setShopLossTotal(map.get("shopLossTotal"));
            }

            // 商品销售数量
            if (map.containsKey("countGoodSale")) {
                placeShiftBO.setCountGoodSale(map.get("countGoodSale"));
            }

            // 商品退款数量
            if (map.containsKey("countGoodRefund")) {
                placeShiftBO.setCountGoodRefund(map.get("countGoodRefund"));
            }

            // 计算现金纯收入
            placeShiftBO.setTotalCashIncome(placeShiftBO.getTotalCashIncome() + placeShiftBO.getShopCashTotal() - placeShiftBO.getShopCashRefund());
            placeShiftBO.setTotalOnlineIncome(placeShiftBO.getTotalOnlineIncome() + placeShiftBO.getShopOnlineTotal() - placeShiftBO.getShopOnlineRefund());
        }
    }

    /**
     * 根据shiftId获取班次信息或者placeId获取对应的logShift
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public LogShift getVerifyShift(String placeId, String shiftId) {
        // 查询班次信息
        if (!StringUtils.isEmpty(shiftId)) {
            Optional<LogShift> optLogShift = logShiftRepository.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
            if (optLogShift.isPresent()) {
                return optLogShift.get();
            }
        }
        LogShift shiftId1 = getShiftId(placeId);
        return Optional.ofNullable(shiftId1).orElse(null);
    }

    /**
     * 根据场所配置的交班是否纳入在线收入 获取对应的logShift
     *
     * @param placeId
     * @return
     */
    public LogShift getShiftId(String placeId) {
        GenericResponse<ObjDTO<PlaceConfigBO>> response = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return null;
        }
        PlaceConfigBO placeConfigBO = response.getData().getObj();
        String cashierId = placeConfigBO.getShiftOnlineIncome();

        // 查询此时的班次信息
        log.info("getShiftId, placeId:{},cashierId:{}", placeId, cashierId);
        Optional<LogShift> logShiftOpt = logShiftRepository.findByPlaceIdAndCashierIdAndStatus(placeId, cashierId, 0);
        if (!logShiftOpt.isPresent()) {
            return null;
        }
        return logShiftOpt.get();
    }

}
