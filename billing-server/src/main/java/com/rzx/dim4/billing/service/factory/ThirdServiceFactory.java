package com.rzx.dim4.billing.service.factory;

import com.rzx.dim4.base.enums.billing.ThirdServiceIndexes;
import com.rzx.dim4.billing.service.ThirdCoreService;
import com.rzx.dim4.billing.service.impl.third.v2.*;
import com.rzx.dim4.billing.web.controller.ThirdCoreController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

/**
 * 收银台第三方接口策略工厂
 *
 * <AUTHOR>
 * @see ThirdCoreController 相关文档看类文档
 * @since 2024/1/16
 **/
@Component
public class ThirdServiceFactory {

    @Autowired
    private ThirdQueryMemberServiceImpl thirdQueryMemberService;

    @Autowired
    private ThirdTopupBillingCardServiceImpl thirdTopupBillingCardService;

    @Autowired
    private ThirdEditBillingCardServiceImpl thirdEditBillingCardService;

    @Autowired
    private ThirdDeductionBillingCardServiceImpl thirdDeductionBillingCardService;

    @Autowired
    private ThirdLogoutBillingCardServiceImpl thirdLogoutBillingCardService;

    @Autowired
    private ThirdActivationBillingCardServiceImpl thirdActivationBillingCardService;

    @Autowired
    private ThirdClientLoginServiceImpl thirdClientLoginService;

    @Autowired
    private ThirdQueryBillingOnlineServiceImpl thirdQueryBillingOnlineService;

    @Autowired
    private ThirdQueryBillingCardTypesServiceImpl thirdQueryBillingCardTypesService;

    @Autowired
    private ThirdCreateBillingCardServiceImpl thirdCreateBillingCardService;

    @Autowired
    private ThirdQueryPlaceOnlineServiceImpl thirdQueryPlaceOnlineService;

    @Autowired
    private ThirdQueryLatestLogLoginServiceImpl thirdQueryLatestLogLoginService;

    @Autowired
    private ThirdBookSeatContextServiceImpl thirdBookSeatContextService;

    @Autowired
    private ThirdBookSeatCreateOrderServiceImpl thirdBookSeatCreateOrderService;

    @Autowired
    private ThirdBookSeatCancelServiceImpl thirdBookSeatCancelService;

    @Autowired
    private ThirdBookSeatQueryPageServiceImpl thirdBookSeatQueryPageService;

    @Autowired
    private ThirdBookSeatQueryOrderServiceImpl thirdBookSeatQueryOrderService;

    @Autowired
    private ThirdQueryLogOperationListServiceImpl thirdQueryPageLogOperationService;

    @Autowired
    private ThirdQueryClientListServiceImpl thirdQueryClientListService;

    @Autowired
    private ThirdCancellationBillingCardServiceImpl thirdCancellationBillingCardService;

    @Autowired
    private ThirdCancelActivationBillingCardServiceImpl thirdCancelActivationBillingCardService;

    @Autowired
    private ThirdQueryActiveBillingCardServiceImpl thirdQueryActiveBillingCardService;

    @Autowired
    private ThirdQueryPlaceShiftServiceImpl thirdQueryPlaceShiftService;

    @Autowired
    private ThirdQueryPlaceTopupRuleListImpl thirdQueryPlaceTopupRuleListService;

    @Autowired
    private ThirdQueryPackageRuleServiceImpl thirdQueryPackageRuleService;

    @Autowired
    private ThirdConvertBillingRuleServiceImpl thirdConvertBillingRuleService;

    @Autowired
    private ThirdUpdateOnlineBillingCardServiceImpl thirdUpdateOnlineBillingCardService;

    @Autowired
    private ThirdQueryLogLoginByLoginIdServiceImpl thirdQueryLogLoginByLoginIdService;

    @Autowired
    private ThirdQueryCashierServiceImpl thirdQueryCashierService;

    private static final Map<ThirdServiceIndexes, ThirdCoreService> coreServiceMap = new EnumMap<>(ThirdServiceIndexes.class);

    public ThirdCoreService getService(ThirdServiceIndexes thirdServiceIndexes) {
        // 双重判断单例模式
        if (CollectionUtils.isEmpty(coreServiceMap)) {
            synchronized (ThirdServiceFactory.class) {
                if (CollectionUtils.isEmpty(coreServiceMap)) {
                    coreServiceMap.put(ThirdServiceIndexes.QueryMember, thirdQueryMemberService);
                    coreServiceMap.put(ThirdServiceIndexes.Topup, thirdTopupBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.EditBillingCard, thirdEditBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.Deduction, thirdDeductionBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.Logout, thirdLogoutBillingCardService);

                    coreServiceMap.put(ThirdServiceIndexes.ActivationCard, thirdActivationBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.ClientLogin, thirdClientLoginService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryOnline, thirdQueryBillingOnlineService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryCardTypes, thirdQueryBillingCardTypesService);
                    coreServiceMap.put(ThirdServiceIndexes.CreateCard, thirdCreateBillingCardService);

                    coreServiceMap.put(ThirdServiceIndexes.QueryOnlineInfo, thirdQueryPlaceOnlineService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryLatestLogLogin, thirdQueryLatestLogLoginService);
                    coreServiceMap.put(ThirdServiceIndexes.BookSeatsContext, thirdBookSeatContextService);
                    coreServiceMap.put(ThirdServiceIndexes.BookSeatsCreate, thirdBookSeatCreateOrderService);
                    coreServiceMap.put(ThirdServiceIndexes.BookSeatsCancel, thirdBookSeatCancelService);

                    coreServiceMap.put(ThirdServiceIndexes.BookSeatsPageList, thirdBookSeatQueryPageService);
                    coreServiceMap.put(ThirdServiceIndexes.BookSeatsOrder, thirdBookSeatQueryOrderService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryPageLogOperation, thirdQueryPageLogOperationService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryClientList, thirdQueryClientListService);
                    coreServiceMap.put(ThirdServiceIndexes.CancellationBillingCard, thirdCancellationBillingCardService);

                    coreServiceMap.put(ThirdServiceIndexes.CancelActivation, thirdCancelActivationBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryActiveBillingCardList, thirdQueryActiveBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryPlaceShift, thirdQueryPlaceShiftService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryPlaceTopupRuleList, thirdQueryPlaceTopupRuleListService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryPackageRules, thirdQueryPackageRuleService);
                    coreServiceMap.put(ThirdServiceIndexes.ConvertBillingRule, thirdConvertBillingRuleService);
                    coreServiceMap.put(ThirdServiceIndexes.UpdateOnlineBillingCard, thirdUpdateOnlineBillingCardService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryLogLoginByLoginId, thirdQueryLogLoginByLoginIdService);
                    coreServiceMap.put(ThirdServiceIndexes.QueryCashier, thirdQueryCashierService);
                }
            }
        }

        return coreServiceMap.get(thirdServiceIndexes);
    }

    /**
     * 内部测试使用
     *
     * @return
     */
    public Set<ThirdServiceIndexes> getKeys() {
        getService(null);
        return coreServiceMap.keySet();
    }
}
