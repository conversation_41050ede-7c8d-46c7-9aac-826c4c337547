package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.vo.PlaceChainRoamOrderVO;
import com.rzx.dim4.base.vo.PlaceChainRoamVO;
import com.rzx.dim4.billing.entity.LogPlaceChain;
import com.rzx.dim4.billing.repository.LogPlaceChainRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年5月16日 上午15:54:33
 */
@Service
public class LogPlaceChainService {

    @Autowired
    LogPlaceChainRepository logPlaceChainRepository;

    public List<LogPlaceChain> findByCurrPlaceIdAndCardIdAndLoginId(String currPlaceId, String cardId, String loginId) {
        return logPlaceChainRepository.findByCurrPlaceIdAndCardIdAndLoginId(currPlaceId, cardId, loginId);
    }

    public LogPlaceChain save(LogPlaceChain logPlaceChain) {
        return logPlaceChainRepository.save(logPlaceChain);
    }

    /**
     * 查询连锁漫游订单详情
     * @param loginIds
     * @return
     */
    public List<PlaceChainRoamOrderVO> findChainRoamList (List<String> loginIds) {

        // 只展示有漫游订单的登入id
        List<String> roamLoginIds = queryRoamLoginIds(loginIds);

        // 漫游扣费详情
        List<Map<String, String>> costPlaceMap = logPlaceChainRepository.querySumCostByCostPlace(roamLoginIds);

        // 登入场所扣费详情
        List<Map<String, String>> currPlaceMap = logPlaceChainRepository.querySumCostByCurrPlace(roamLoginIds);

        List<PlaceChainRoamOrderVO> logPlaceChains = new ArrayList<>();

        // 组装当前场所扣费信息
        for (Map<String, String> map : currPlaceMap) {
            PlaceChainRoamOrderVO placeChainRoamOrderVO = new PlaceChainRoamOrderVO();
            placeChainRoamOrderVO.setLoginId(map.get("loginId"));
            placeChainRoamOrderVO.setCurrPlaceId(map.get("currPlaceId"));
            Object obj = map.get("sumCashAccount");
            placeChainRoamOrderVO.setSumCurrCashAccount(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPresentAccount");
            placeChainRoamOrderVO.setSumCurrPresentAccount(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            placeChainRoamOrderVO.setSumCurrTotalAccount(placeChainRoamOrderVO.getSumCurrCashAccount() + placeChainRoamOrderVO.getSumCurrPresentAccount());
            logPlaceChains.add(placeChainRoamOrderVO);
        }

        // 漫游扣费信息
        List<PlaceChainRoamVO> placeChainRoamVOS = new ArrayList<>();
        for (Map<String, String> map : costPlaceMap) {
            PlaceChainRoamVO placeChainRoamVO = new PlaceChainRoamVO();
            placeChainRoamVO.setLoginId(map.get("loginId"));
            placeChainRoamVO.setRoamCostPlaceId(map.get("costPlaceId"));
            placeChainRoamVO.setCurrPlaceId(map.get("currPlaceId"));
            Object obj = map.get("sumCashAccount");
            placeChainRoamVO.setRoamCashAccount(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumPresentAccount");
            placeChainRoamVO.setRoamPresentAccount(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            placeChainRoamVOS.add(placeChainRoamVO);
        }
        Map<String, List<PlaceChainRoamVO>> roamMap = placeChainRoamVOS.stream().collect(Collectors.groupingBy(PlaceChainRoamVO::getLoginId));

        List<String> currLoginIds = logPlaceChains.stream().map(PlaceChainRoamOrderVO::getLoginId).collect(Collectors.toList());
        for (String key : roamMap.keySet()) {
            if (currLoginIds.contains(key)) {
                for (PlaceChainRoamOrderVO vo : logPlaceChains) {
                    if (roamMap.containsKey(vo.getLoginId())) {
                        List<PlaceChainRoamVO> placeChainRoamVOS1 = roamMap.get(vo.getLoginId());
                        vo.setPlaceChainRoamVOS(placeChainRoamVOS1);
                        vo.setRoamCashAccount(placeChainRoamVOS1.stream().mapToInt(PlaceChainRoamVO::getRoamCashAccount).reduce(0,Integer::sum));
                        vo.setRoamPresentAccount(placeChainRoamVOS1.stream().mapToInt(PlaceChainRoamVO::getRoamPresentAccount).reduce(0,Integer::sum));
                    } else {
                        // 只扣上机门店
                        vo.setPlaceChainRoamVOS(new ArrayList<>());
                        vo.setRoamCashAccount(0);
                        vo.setRoamPresentAccount(0);
                    }
                }
            } else {
                // 只扣漫游
                PlaceChainRoamOrderVO placeChainRoamOrderVO = new PlaceChainRoamOrderVO();
                placeChainRoamOrderVO.setCurrPlaceId(roamMap.get(key).get(0).getCurrPlaceId());
                placeChainRoamOrderVO.setLoginId(key);
                placeChainRoamOrderVO.setRoamCashAccount(roamMap.get(key).stream().mapToInt(PlaceChainRoamVO::getRoamCashAccount).reduce(0,Integer::sum));
                placeChainRoamOrderVO.setRoamPresentAccount(roamMap.get(key).stream().mapToInt(PlaceChainRoamVO::getRoamPresentAccount).reduce(0,Integer::sum));
                placeChainRoamOrderVO.setPlaceChainRoamVOS(roamMap.get(key));
                logPlaceChains.add(placeChainRoamOrderVO);
            }
        }
        return logPlaceChains;
    }

    /**
     * 查询漫游订单的登入id
     * @param loginIds
     * @return
     */
    public List<String> queryRoamLoginIds (List<String> loginIds) {
        return logPlaceChainRepository.queryRoamLoginIds(loginIds);
    }

    /**
     * 获取分页数据
     * @param loginId
     * @return
     */
    public List<String> queryRoamLoginId (String loginId) {
        return logPlaceChainRepository.queryRoamLoginId(loginId);
    }

}
