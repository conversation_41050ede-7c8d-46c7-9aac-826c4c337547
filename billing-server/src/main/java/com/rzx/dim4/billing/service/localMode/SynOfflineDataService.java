package com.rzx.dim4.billing.service.localMode;

import com.rzx.dim4.billing.entity.localMode.SynOfflineData;
import com.rzx.dim4.billing.repository.localMode.SynOfflineDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class SynOfflineDataService {

    @Autowired
    SynOfflineDataRepository synOfflineDataRepository;

    public Optional<SynOfflineData> findByRequestId (String requestId) {
        return synOfflineDataRepository.findByRequestId(requestId);
    }

    public SynOfflineData save (SynOfflineData synOfflineData) {
        return synOfflineDataRepository.save(synOfflineData);
    }
}
