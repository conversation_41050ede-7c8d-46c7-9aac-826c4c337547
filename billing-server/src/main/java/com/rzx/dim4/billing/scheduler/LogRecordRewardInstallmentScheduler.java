package com.rzx.dim4.billing.scheduler;

import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogRecordRewardInstallment;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.service.BillingCardDeductionService;
import com.rzx.dim4.billing.service.BillingCardService;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.LogRecordRewardInstallmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用于每月一号分期赠送金额发放
 * <AUTHOR>
 * @date 2024年05月31日 15:04
 */
@Slf4j
@Component
public class LogRecordRewardInstallmentScheduler {

    @Autowired
    private LogRecordRewardInstallmentService logRecordRewardInstallmentService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final String LOG_RECORD_REWARD_INSTALLMENT_LOCK_KEY = "billing:log_Record_Reward_Installment_redis_lock";



//    @Scheduled(cron = "0 0/5 * * * ? ") //每10分钟执行一次
    @Scheduled(cron = "0 0 0 1 * ? ") //每月一号零点零分
    public void cleanMemberNumber() {
        log.info("每月一号赠送金额开始派送" );
        boolean locked = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(LOG_RECORD_REWARD_INSTALLMENT_LOCK_KEY, "locked"));
        if (locked) {
            log.info("赠送金额开始派送锁获取成功，设置5分钟TTL");
            stringRedisTemplate.expire(LOG_RECORD_REWARD_INSTALLMENT_LOCK_KEY, 5, TimeUnit.MINUTES);
            //修改所有状态为1 但是剩余赠送次数未0的数据
            logRecordRewardInstallmentService.clearCompletedTasks();
            //查询待赠送金额信息
            List<LogRecordRewardInstallment> allDatas = logRecordRewardInstallmentService.findByDeletedAndStatus(0, 1);
            if(CollectionUtils.isEmpty(allDatas)){
                log.info("未查询到尚未赠送完的充赠金额！");
                return;
            }
            //数据根据场所id+证件号进行分组处理
            Map<String, List<LogRecordRewardInstallment>> groupMap = allDatas.stream().collect(Collectors.groupingBy(it -> it.getPlaceId()+"_"+it.getIdNumber()));
            groupMap.forEach((k,list)->{
                log.info("--=========="+k);
                String[] split = k.split("_");
                String placeId = split[0];
                String idNumber = split[1];

                //查询当前班次
                LogShift shift = new LogShift();
                GenericResponse<ObjDTO<PlaceShiftBO>> listDTOGenericResponse = placeServerService.queryDefaultWorkShift(placeId);
                if(listDTOGenericResponse.isResult() && !ObjectUtils.isEmpty(listDTOGenericResponse.getData().getObj())){
                    PlaceShiftBO placeShiftBO = listDTOGenericResponse.getData().getObj();
                    shift.setLoginAccountName("系统");
                    shift.setShiftId(placeShiftBO.getShiftId());
                    shift.setLoginAccountId(placeShiftBO.getOnDutyAccountId());
                    shift.setCashierId(placeShiftBO.getCashierId());
                }

                if(StringUtils.isEmpty(shift.getLoginAccountId())){
                    shift.setLoginAccountName("系统");
                    shift.setLoginAccountId("-1");
                }

                Optional<BillingCard> byPlaceIdAndIdNumberAndNotDeleted = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
                if(byPlaceIdAndIdNumberAndNotDeleted.isPresent()){
                    int sumAmount = list.stream().mapToInt(it -> it.getOncePresentAmount()).sum();
                    BillingCard billingCard = byPlaceIdAndIdNumberAndNotDeleted.get();

                    log.info("场所：{}下的用户：{},卡号：{}，分期奖励金额：{} 分",placeId,idNumber,billingCard.getCardId(),sumAmount);

                    //修改赠送金额
                    billingCardDeductionService.updateBillingCardAccount(placeId,billingCard.getCardId(),0,-sumAmount,0);

                    //修改剩余赠送次数
                    List<Long> ids = list.stream().map(it->it.getId()).collect(Collectors.toList());
                    log.info("具体赠送列表id为：{}",ids);

                    logRecordRewardInstallmentService.updateStatusAndOncePresentAmortizedByIds(ids);

                    //写入操作记录
                    logOperationService.addRewardInstallmentLogOperation(sumAmount,billingCard,shift);

                }else{
                    log.info("场所：{}下的用户：{}计费卡不存在，清除所有未完成分期赠送金额");
                    logRecordRewardInstallmentService.updateStatusByPlaceIdAndIdNumber(0,placeId,idNumber);
                }

            });


        } else {
            log.info("每月一号赠送金额开始派送锁获取失败，查询锁状态");
            long expired = stringRedisTemplate.getExpire(LOG_RECORD_REWARD_INSTALLMENT_LOCK_KEY, TimeUnit.SECONDS);
            log.info("获取赠送金额派送锁状态：" + expired);
            return;
        }
    }

}
