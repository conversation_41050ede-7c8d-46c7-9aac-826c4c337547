package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.PackageTimeReserve;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface PackageTimeReserveRepository extends JpaRepository<PackageTimeReserve, Long> {

    Optional<PackageTimeReserve> findByPlaceIdAndCardIdAndStatus(String placeId, String cardId, int status);

    Optional<PackageTimeReserve> findByPlaceIdAndCardIdAndOrderId(String placeId, String cardId, String orderId);

    Optional<PackageTimeReserve> findTop1ByPlaceIdAndIdNumberAndStatusOrderByIdDesc(String placeId, String idNumber, int status);

    Optional<PackageTimeReserve> findTop1ByPlaceIdAndCardIdAndEndTimeGreaterThanOrderByIdDesc(String placeId, String cardId, LocalDateTime dateTime);

    List<PackageTimeReserve> findByPlaceIdAndStatus(String placeId, int status);

    List<PackageTimeReserve> findByPlaceIdAndRuleIdAndStatus(String placeId, String ruleId, int status);

    List<PackageTimeReserve> findByPlaceIdInAndIdNumberInAndStatus(List<String> placeIds, List<String> idNumbers, int status);

    PackageTimeReserve findTop1ByPlaceIdAndCardIdAndIdNumberAndRuleIdOrderByIdDesc(String placeId, String cardId, String idNumber, String ruleId);

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where status = 0 and end_time < now()", nativeQuery = true)
    int updateUnusedCashierTempPackageTimeInvalidation(); // 查询包时

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where place_id = ?1 and card_id = ?2 and status = 0", nativeQuery = true)
    int updateInvaildByPlaceIdAndCardId(String placeId, String cardId); // 取消包时使用

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = :status, updated = now() where place_id = :placeId and order_id = :orderId ", nativeQuery = true)
    int updateInvalidationByPlaceIdAndOrderId(@Param("placeId") String placeId, @Param("orderId") String orderId, @Param("status") int status); // 更改状态

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where place_id = ?1 and card_id = ?2 and (status = 0 or status = 1)", nativeQuery = true)
    int updateInvalidationByPlaceIdAndCardId(String placeId, String cardId); // 结账,销卡,激活未上机

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where place_id = ?1 and card_id = ?2 and rule_id = ?3 and (status = 0 or status = 1)", nativeQuery = true)
    int updateInvalidationByPlaceIdAndCardIdAndRuleId(String placeId, String cardId, String ruleId);

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where place_id = ?1 and card_id = ?2  and status = 1", nativeQuery = true)
    int clearPackageTimeReserveByPlaceIdAndCardId(String placeId, String cardId);

    @Transactional
    @Modifying
    @Query(value = "update package_time_reserve set status = 2, updated = now() where place_id = ?1 and card_id = ?2 and order_id = ?3 and (status = 0 or status = 1)", nativeQuery = true)
    int updateInvalidationByPlaceIdAndCardIdAndOrderId(String placeId, String cardId, String orderId);

    @Query(value = "SELECT sum(IFNULL(price,0)) as totalMoney FROM package_time_reserve WHERE place_id = :placeId  " +
            " AND deleted = 0 AND package_pay_flag = :packagePayFlag AND created >= :startTime AND created <= :endTime  ", nativeQuery = true)
    Integer sumPackageTime(@Param("placeId") String placeId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("packagePayFlag") Integer packagePayFlag);

    @Query(value = "SELECT sum(IFNULL(price,0)) as totalMoney FROM package_time_reserve WHERE place_id = :placeId  " +
            " AND deleted = 0 AND (package_pay_flag=1 or package_pay_flag=2) AND created >= :startTime AND created <= :endTime  ", nativeQuery = true)
    Integer sumPackageTime(@Param("placeId") String placeId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    @Query(value = "SELECT count(1) FROM package_time_reserve WHERE place_id = :placeId  " +
            " AND deleted = 0 AND (package_pay_flag=1 or package_pay_flag=2) AND created >= :startTime AND created <= :endTime  ", nativeQuery = true)
    Integer countPackageTime(@Param("placeId") String placeId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
