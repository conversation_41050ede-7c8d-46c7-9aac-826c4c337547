package com.rzx.dim4.billing.entity;

import com.rzx.dim4.base.bo.billing.LogQrCodeBO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 二维码生成记录
 * 
 * <AUTHOR>
 * @date 2022年10月11日 上午11:00:28
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "log_qrcode", indexes = { @Index(name = "idx_log_qrcode_place_id", columnList = "place_id"),
		@Index(name = "idx_log_qrcode_token", columnList = "token") })
public class LogQrCode extends BaseEntity {

	private static final long serialVersionUID = -1498509903748432429L;

	@Column(name = "token", length = 100, nullable = false, updatable = false, unique = true)
	private String token; // TOKEN

	@Column(name = "uuid", length = 100)
	private String uuid;

	@Column(name = "place_id", length = 14, nullable = false, updatable = false)
	private String placeId; // 场所ID

	@Column(name = "client_id", length = 10)
	private String clientId; // 客户端ID

	@Column(name = "area_id", length = 10)
	private String areaId; // 区域ID

	@Column(name = "cashier_id", length = 10)
	private String cashierId; // 收银台ID

	@Column(name = "deadline", nullable = false)
	private LocalDateTime deadline; // 二维码有效截止时间

	@Column(name = "used", nullable = false)
	private int used; // 是否已使用

	@Column(name = "params", length = 500)
	private String params; // 也其他业务参数

	public LogQrCodeBO toBO() {
		LogQrCodeBO bo = new LogQrCodeBO();
		BeanUtils.copyProperties(this, bo);
		return bo;
	}

	public LogQrCode(LogQrCodeBO bo) {
		BeanUtils.copyProperties(bo, this);
	}

}
