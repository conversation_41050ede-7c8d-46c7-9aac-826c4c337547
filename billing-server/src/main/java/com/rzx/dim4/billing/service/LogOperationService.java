package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.billing.CancellationBO;
import com.rzx.dim4.base.bo.billing.StatisticSumTopupBO;
import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.enums.billing.RefundType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.VerifyParam;
import com.rzx.dim4.billing.cons.BillingConstants;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.LogOperationRepository;
import com.rzx.dim4.billing.repository.LogRefundRepository;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import com.rzx.dim4.billing.service.impl.third.v2.ThirdQueryLogOperationListServiceImpl;
import com.rzx.dim4.billing.service.third.MarketLogOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzx.dim4.base.cons.BaseConstants.PLACE_EXCHANGE_OPERATION;

/**
 * <AUTHOR>
 * @date 2021年9月6日 下午5:28:11
 */
@Slf4j
@Service
public class LogOperationService {

    @Autowired
    LogOperationRepository logOperationRepository;

    @Autowired
    BillingCardRepository billingCardRepository;

    @Autowired
    LogRefundRepository logRefundRepository;

    @Autowired
    LogRefundService logRefundService;

    @Autowired
    LogOtherIncomeService logOtherIncomeService;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private MarketLogOperationService marketLogOperationService;

    @Autowired
    private EntityManager entityManager;
    DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public LogOperation save(LogOperation logOperation) {
        return logOperationRepository.save(logOperation);
    }

    public void delete(LogOperation logOperation) {
        logOperationRepository.delete(logOperation);
    }

    public List<LogOperation> findByLoginIdAndOperationType(String loginId, OperationType operationType) {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = LocalDateTime.now();
        // 如果操作类型传来的是附加费，则查询开始时间是本月；否则默认三个月前
        if (OperationType.SURCHARGE.equals(operationType)) {
            startDateTime = DateTimeUtils.monthStartTime();
        } else {
//            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月，会查3张表
            LocalTime currentTime = LocalTime.now();//获取当前时间
            int hour = currentTime.getHour();//获取当前是一天的几点
            // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
            if (hour > 18 && hour < 22) {
                startDateTime = DateTimeUtils.monthStartTime();//本月第一天
            } else {
                startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
            }
        }
        return logOperationRepository.findByLoginIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqual(loginId, operationType, startDateTime, endDateTime);
    }

    public int countTodayActivatedNonIdNumberByPlaceId(String placeId) {
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        // 分表jdbc上线后，需增加截至时间否则报错
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        return logOperationRepository.countActivatedNonIdNumberByPlaceId(placeId, todayStart, todayEnd);
    }

    /**
     * 查询最近一条包时的实际开始时间--id卡
     *
     * @param placeId
     * @param cardId
     * @param operationType
     * @return
     */
    public Optional<LogOperation> findTop1ByPlaceIdAndCardIdAndOperationType(String placeId, String cardId,
                                                                             OperationType operationType) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        return logOperationRepository.findTop1ByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, cardId,
                operationType, startDateTime, endDateTime);
    }

    /**
     * 查询最近一条包时的实际开始时间--证件号
     *
     * @param placeId
     * @param idNumber
     * @param operationTypes
     * @return
     */
    public Optional<LogOperation> findTop1ByPlaceIdAndIdNumberAndOperationTypeInOrderByIdDesc(String placeId, String idNumber,
                                                                                              List<OperationType> operationTypes) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        return logOperationRepository.findTop1ByPlaceIdAndIdNumberAndOperationTypeInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber,
                operationTypes, startDateTime, endDateTime);
    }

    /**
     *
     * @param placeId
     * @param idNumber
     * @param operationTypes
     * @return
     */
    public Optional<LogOperation> findTop1ByPlaceIdAndIdNumberAndOperationTypeInOrderByIdDesc(String placeId, String idNumber,
                                                                                              List<OperationType> operationTypes,LocalDateTime startDateTime) {
        LocalDateTime endDateTime = LocalDateTime.now();
        return logOperationRepository.findTop1ByPlaceIdAndIdNumberAndOperationTypeInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber,
                operationTypes, startDateTime, endDateTime);
    }

    /**
     * 查询最近一条包时的实际开始时间--证件号-起始时间
     *
     * @param placeId
     * @param operationTypes
     * @param startTime
     * @param endTime
     * @return
     */
    public Optional<LogOperation> findTop1ByPlaceIdAndCardIdAndOperationTypeByTime(String placeId, String cardId,
                                                                                   List<OperationType> operationTypes,
                                                                                   LocalDateTime startTime, LocalDateTime endTime) {
        return logOperationRepository.findTop1ByPlaceIdAndCardIdAndOperationTypeInAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, cardId,
                operationTypes, startTime, endTime);
    }

    /**
     * 操作记录 - 根据积分自动升降级用户等级
     *
     * @param sourceType          来源
     * @param amount              充值/消费金额
     * @param billingCard         计费卡信息
     * @param amountType          金额类型:0-充值，1-消费(结账消费时的积分赠送),2-积分调整（冲正时amount代表积分）,3-积分兑换，4-取消包时
     * @param points              amountType=2/3时，代表积分，其他时候为0
     * @param upOrDown            升级或者降级:0-充值，1-消费(结账消费时的积分赠送)
     * @param currentCardTypeName 当前会员卡类型名称
     * @param matchCardTypeName   升降级匹配的会员卡类型名称
     */
    public void addGradeUserLevelLogOperation(SourceType sourceType, int amount, BillingCard billingCard, int amountType, int points, int upOrDown, String currentCardTypeName, String matchCardTypeName, LogShift logShift) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        OperationType operationType = OperationType.UP_GRADE_USER_LEVEL;

        // details举例：根据积分自动升级用户等级：充值/消费了 60 元 从普通会员卡升级到了 钻石会员卡。
        String amountTypeStr = "充值";
        if (amountType == 1) {
            amountTypeStr = "结账消费";
        } else if (amountType == 2) {
            amountTypeStr = "积分调整";
        } else if (amountType == 3) {
            amountTypeStr = "积分兑换";
        } else if (amountType == 4) {
            amountTypeStr = "取消包时";
        }

        String upOrDownStr = "升级";
        if (upOrDown == 1) {
            upOrDownStr = "降级";
            operationType = OperationType.DOWN_GRADE_USER_LEVEL;
        }
        String details = "";

        if (amountType == 2 || amountType == 3) {
            details = "根据积分自动" + upOrDownStr + "用户等级，" + amountTypeStr + points + "分，会员卡类型从'" + currentCardTypeName + "'" + upOrDownStr + "成'" + matchCardTypeName + "'";
        } else {
            details = "根据积分自动" + upOrDownStr + "用户等级，" + amountTypeStr + Double.valueOf(amount) / 100 + "元，会员卡类型从'" + currentCardTypeName + "'" + upOrDownStr + "成'" + matchCardTypeName + "'";
        }
        addLogOperation(operationType, sourceType, amount, 0, null, null, billingCard, null, logShift,
                null, details);
    }


    /**
     * 操作记录 - 换机
     *
     * @param sourceType        来源
     * @param billingCard       计费卡信息
     * @param lastBillingOnline 换机前在线信息
     * @param currBillingOnline 换机后在线信息
     * @param logShift          班次信息
     * @param logLogin          登录信息
     */
    public void addExchangeClientOperation(SourceType sourceType, BillingCard billingCard,
                                           BillingOnline lastBillingOnline, BillingOnline currBillingOnline, LogShift logShift, LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(lastBillingOnline, "lastBillingOnline 不能为空");
        Assert.notNull(currBillingOnline, "currBillingOnline 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");

        String desc = "";

        int price = currBillingOnline.getCommonPrice();

        String details = lastBillingOnline.getClientId() + PLACE_EXCHANGE_OPERATION + currBillingOnline.getClientId();
        if (currBillingOnline.getAccFlag() == 2) {
            details += "(自动包时生效中)";
        } else {
            details += "(￥" + Double.valueOf(price) / 100 + "元/小时)";
        }
        // 换机时记录上一台机器的消费总额到cost
        addLogOperation(OperationType.EXCHANGE, sourceType, (-lastBillingOnline.getDeduction()), 0,
                currBillingOnline.getClientId(), lastBillingOnline.getClientId(), billingCard, logLogin.getLoginId(),
                logShift, desc, details);
    }

    /**
     * 操作记录 - 结束包时
     *
     * @param sourceType        来源
     * @param billingCard       计费卡信息
     * @param billingOnline     在线信息
     * @param billingRuleCommon 包时结束后的计费规则
     * @param logShift          班次信息
     * @param logLogin          登录信息
     */
    public void addEndPackageTimeCardOperation(SourceType sourceType, BillingCard billingCard,
                                               BillingOnline billingOnline, BillingRuleCommon billingRuleCommon, LogShift logShift, LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(billingOnline, "billingOnline 不能为空");
        Assert.notNull(billingRuleCommon, "billingRule 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");

        String desc = "";

        int price = CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices());
        String details = "包时转普通计费(￥" + Double.valueOf(price) / 100 + "元/小时)";

        addLogOperation(OperationType.END_PACKAGE_TIME, sourceType, 0, 0, billingOnline.getClientId(),
                billingOnline.getClientId(), billingCard, logLogin.getLoginId(), logShift, desc, details);
    }

    /**
     * 操作记录 - 取消包时
     *
     * @param sourceType     来源
     * @param billingCard    计费卡信息
     * @param packageFlag    取消包时标识
     * @param packagePayFlag 支付方式
     * @param logShift       班次信息
     * @param logLogin       登录信息
     * @param usedFlag       已使用标识
     */
    public void addCancelPackageTimeOperation(SourceType sourceType, BillingCard billingCard, int packageFlag, int packagePayFlag,
                                              BillingOnline billingOnline, LogShift logShift, LogLogin logLogin, int refundTypeInt, int usedFlag) {

        Assert.notNull(sourceType, "sourceType 不能为空");

        String packageTypeName = packageFlag == 1 ? "包时段" : packageFlag == 2 ? "包时长" : "自定义包时";
        String packagePayName = packagePayFlag == 1 ? "余额包时" : packagePayFlag == 2 ? "现金包时" : "在线支付包时";
        String desc = "";
        String details = "取消" + packageTypeName + "(" + packagePayName + ")";
        if (usedFlag == 0) {
            details = details + "，退款方式:" + (refundTypeInt == 1 ? "退余额" : "退现金");
        }
        addLogOperation(OperationType.CANCEL_PACKAGE_TIME, sourceType, 0, 0, StringUtils.isEmpty(billingOnline) ? "" : billingOnline.getClientId(),
                StringUtils.isEmpty(billingOnline) ? "" : billingOnline.getClientId(), billingCard, StringUtils.isEmpty(logLogin) ? "" : logLogin.getLoginId(), logShift, desc, details);
    }

    /**
     * 操作记录 - 开始包时
     *
     * @param sourceType             来源
     * @param billingCard            计费卡信息
     * @param billingOnline          在线信息
     * @param billingRulePackageTime 计费规则
     * @param logShift               班次信息
     * @param logLogin               登录信息
     */
    public void addBeginPackageTimeCardOperation(SourceType sourceType, BillingCard billingCard,
                                                 BillingOnline billingOnline, BillingRulePackageTime billingRulePackageTime, LogShift logShift,
                                                 LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(billingOnline, "billingOnline 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");
        Assert.notNull(billingRulePackageTime, "billingRule 不能为空");

        String desc = "";

        int amount = -billingRulePackageTime.getPrice();

        String details = "";
        if (billingRulePackageTime.getPackageFlag() == 1) {

            float startTime = billingRulePackageTime.getStartTime();
            float endTime = billingRulePackageTime.getEndTime();

            // 如果选择的是0点到0点: startTime==endTime
            if (startTime == endTime) {
                endTime += 24;
            }

            float diff = new BigDecimal((endTime - startTime)).setScale(2, RoundingMode.HALF_UP).floatValue();

            float middle = endTime < startTime ? (diff + 24) : diff;
            details = "包时段(￥" + Double.valueOf(billingRulePackageTime.getPrice()) / 100 + "，" + middle + "小时)";
        } else if (billingRulePackageTime.getPackageFlag() == 2) {
            details = "包时长(￥" + Double.valueOf(billingRulePackageTime.getPrice()) / 100 + "，" + billingRulePackageTime.getDurationTime()
                    + "小时)";
        } else if (billingRulePackageTime.getPackageFlag() == 9) {
            details = "自定义包时(￥" + Double.valueOf(billingRulePackageTime.getPrice()) / 100 + "，"
                    + billingRulePackageTime.getDurationTime() + "小时)";
        }

        addLogOperation(OperationType.BEGIN_PACKAGE_TIME, sourceType, amount, 0, billingOnline.getClientId(),
                billingOnline.getClientId(), billingCard, logLogin.getLoginId(), logShift, desc, details);

    }

    /**
     * 操作记录 - 转换计费规则
     *
     * @param sourceType                来源
     * @param convertType               执行类型， 0:标准转标准 1:标准转包时 2:包时转标准 3:包时转包时
     * @param oldCommonPrice            转换前标准扣费价格
     * @param newCommonPrice            转换后标准扣费价格
     * @param billingCard               计费卡信息
     * @param billingOnline             在线信息
     * @param oldBillingRulePackageTime 转换前包时规则
     * @param newBillingRulePackageTime 转换后包时规则
     * @param logShift                  班次信息
     * @param logLogin                  登录信息
     */
    public void addConvertBillingRuleOperation(SourceType sourceType, int convertType, int oldCommonPrice, int newCommonPrice, BillingCard billingCard,
                                               BillingOnline billingOnline, BillingRulePackageTime oldBillingRulePackageTime, BillingRulePackageTime newBillingRulePackageTime, LogShift logShift,
                                               LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        // Assert.notNull(billingOnline, "billingOnline 不能为空");
        // Assert.notNull(logLogin, "logLogin 不能为空");
        // Assert.notNull(billingRulePackageTime, "billingRulePackageTime 不能为空");

        String desc = "";
        String details = "";
        int amount = 0;
        if (convertType == 0) {
            details = "普通计费【" + Double.valueOf(oldCommonPrice) / 100 + "元/小时】转普通计费【" + Double.valueOf(newCommonPrice) / 100 + "元/小时】";
            amount = -newCommonPrice;
        } else if (convertType == 1) {
            String packageRuleName = newBillingRulePackageTime.getPackageFlag() == 1 ? "包时段" : newBillingRulePackageTime.getPackageFlag() == 2 ? "包时长" : "自定义包时";
            details = "普通计费【" + Double.valueOf(oldCommonPrice) / 100 + "元/小时】转" + packageRuleName + "【" + newBillingRulePackageTime.getRuleName() + "】";
            amount = -newBillingRulePackageTime.getPrice();
        } else if (convertType == 2) {
            String packageRuleName = oldBillingRulePackageTime.getPackageFlag() == 1 ? "包时段" : oldBillingRulePackageTime.getPackageFlag() == 2 ? "包时长" : "自定义包时";
            details = packageRuleName + "【" + oldBillingRulePackageTime.getRuleName() + "】" + "转普通计费【" + Double.valueOf(newCommonPrice) / 100 + "元/小时】";
            amount = -newCommonPrice;
        } else if (convertType == 3) {
            String oldPackageRuleName = oldBillingRulePackageTime.getPackageFlag() == 1 ? "包时段" : oldBillingRulePackageTime.getPackageFlag() == 2 ? "包时长" : "自定义包时";
            String newPackageRuleName = newBillingRulePackageTime.getPackageFlag() == 1 ? "包时段" : newBillingRulePackageTime.getPackageFlag() == 2 ? "包时长" : "自定义包时";
            details = oldPackageRuleName + "【" + oldBillingRulePackageTime.getRuleName() + "】" + "转" + newPackageRuleName + "【" + newBillingRulePackageTime.getRuleName() + "】";
            amount = -newBillingRulePackageTime.getPrice();
        }

        addLogOperation(OperationType.CONVERT_BILLING_RULE, sourceType, amount, 0, billingOnline == null ? "" : billingOnline.getClientId(),
                billingOnline == null ? "" : billingOnline.getClientId(), billingCard, logLogin == null ? "" : logLogin.getLoginId(), logShift, desc, details);

    }

    /**
     * 操作记录 - 包时
     *
     * @param sourceType             来源
     * @param executeType            执行类型， 0:准点包时  1:预包时   2:续包时  3:转包时  9:自定义包时
     * @param packagePayType         支付类型  1.余额支付包时 2.现金支付包时 3.在线支付包时 4.优惠券
     * @param middlePrice            转包时差价
     * @param billingCard            计费卡信息
     * @param billingOnline          在线信息
     * @param billingRulePackageTime 计费规则
     * @param logShift               班次信息
     * @param logLogin               登录信息
     */
    public void addPackageTimeOperation(SourceType sourceType, int executeType, int packagePayType, int middlePrice, BillingCard billingCard,
                                        BillingOnline billingOnline, BillingRulePackageTime billingRulePackageTime, LogShift logShift,
                                        LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        // Assert.notNull(billingOnline, "billingOnline 不能为空");
        // Assert.notNull(logLogin, "logLogin 不能为空");
        Assert.notNull(billingRulePackageTime, "billingRulePackageTime 不能为空");

        String desc = billingRulePackageTime.getRuleId(); // 包时的操作记录，备注这里记当时操作包时的规则id，为了统计这个包时的次数
        String details = "";
        int amount = -billingRulePackageTime.getPrice();
        if (executeType == 0) {
            details = "准点包时,";
        } else if (executeType == 1) {
            details = "预包时,";
        } else if (executeType == 2) {
            details = "续包时,";
        } else if (executeType == 3) {
            details = "转包时,";
            amount = -middlePrice;
            details += "补差价:" + (Double.valueOf(middlePrice) / 100) + "元";
        } else if (executeType == 9) {
            details = "自定义包时,";
        }

        if (billingRulePackageTime.getPackageFlag() == 1) {
            details += "包时段," + "[" + billingRulePackageTime.getRuleName() + "],支付方式:";
        } else if (billingRulePackageTime.getPackageFlag() == 2 || billingRulePackageTime.getPackageFlag() == 9) {
            details += "包时长," + "[" + billingRulePackageTime.getRuleName() + "],支付方式:";
        }

        if (packagePayType == 1) {
            details += "余额支付";
        } else if (packagePayType == 2) {
            details += "现金支付";
        } else if (packagePayType == 3) {
            details += "在线支付";
        } else {
            details += "优惠券核销";
        }

        addLogOperation(OperationType.PACKAGE_TIME, sourceType, amount, 0, billingOnline == null ? "" : billingOnline.getClientId(),
                billingOnline == null ? "" : billingOnline.getClientId(), billingCard, logLogin == null ? "" : logLogin.getLoginId(), logShift, desc, details);

    }

    /**
     * 操作记录 - 激活
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param activeType  激活方式
     */
    public void addActivateCardOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift,
                                         String activeType) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = activeType != null
                ? Objects.requireNonNull(ActiveType.getActiveTypes(Integer.parseInt(activeType))).getDisplay() + "激活"
                : "刷身份证/手输身份证";

        addLogOperation(OperationType.ACTIVATE_CARD, sourceType, 0, 0, null, null, billingCard, null, logShift, desc,
                details);
    }

    /**
     * 操作记录 - 退款
     *
     * @param sourceType  来源
     * @param refundType  退款业务
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addRefundOperation(SourceType sourceType, RefundType refundType, BillingCard billingCard, int cost, int present, int temporaryOnline, LogShift logShift) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = "";
        String sourceName = SourceType.getDesc(sourceType);
        if (refundType.equals(RefundType.TOPUP)) {
            // 充值退款
            desc = "用于网费充值退款";
            details = "在线支付退款,本金:" + ((double) cost / 100) + "元;奖励" + ((double) present / 100) + "元,操作来源:" + sourceName;
        } else if (refundType.equals(RefundType.CANCELLATION)) {
            // 销卡退款
            desc = "用于临时卡销卡退款";
            details = "现金支付退款,本金:" + ((double) cost / 100) + "元;奖励" + ((double) present / 100) + "元";
            if ("1000".equals(billingCard.getCardTypeId())) {
                details += "，在线账户：" + (double) billingCard.getTemporaryOnlineAccount() / 100 + "元";
            }
            details += ",操作来源:" + sourceName;

        } else if (refundType.equals(RefundType.SHOP)) {
            // 商超卡扣退款
            desc = "用于商超卡扣退款";
            details = "商品退款(卡扣),本金:" + ((double) cost / 100) + "元;奖励" + ((double) present / 100);
        } else if (refundType.equals(RefundType.CANCEL_PACKAGE_TIME)) {
            // 取消包时
            desc = "用于取消包时退款";
            if ("1000".equals(billingCard.getCardTypeId())) {
                details = "取消包时退款,本金:" + ((double) cost / 100) + "元;线上" + ((double) temporaryOnline / 100);
            } else {
                details = "取消包时退款,本金:" + ((double) cost / 100) + "元;奖励" + ((double) present / 100);
            }
        }

        addLogOperation(OperationType.REFUND, sourceType, cost, present, null, null, billingCard, null, logShift, desc, details);
    }

    public void addInternetGiftLogOperationForMiniApp(BillingCard billingCard, int presentAmount) {

        String remark = "小程序赠送网费";
        String amount = BigDecimal.valueOf(presentAmount).divide(java.math.BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP) + "元";
        String details = String.format("赠送网费的金额: %s, 操作来源: 小程序%s", amount, SourceType.MINIAPP.name());

        log.info("小程序赠送网费 LogOperation=(placeId={}, cardId={}, details={})", billingCard.getPlaceId(), billingCard.getCardId(), details);
        addLogOperation(OperationType.INTERNET_GIFT, SourceType.MINIAPP, 0, presentAmount, SpecialPlaceClients.MINIAPP.getClientId(), SpecialPlaceClients.MINIAPP.getClientId(), billingCard, null, null, remark, details);
    }

    public void addRefundOperationNew(SourceType sourceType, RefundType refundType, BillingCard billingCard, CancellationBO cancellationBO, LogShift logShift) {
        log.info("addRefundOperationNew,sourceType={},billingCard={},cancellationBO={},logShift={}", sourceType, billingCard, cancellationBO, logShift);

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = "";
        String sourceName = SourceType.getDesc(sourceType);
        if (refundType.equals(RefundType.TOPUP)) {
            // 充值退款
            desc = "用于网费充值退款";
            details = "在线支付退款,本金:" + ((double) cancellationBO.getRefundCashAmount() / 100) + "元;奖励" + ((double) cancellationBO.getPresent() / 100) + "元,操作来源:" + sourceName;
        } else if (refundType.equals(RefundType.CANCELLATION)) {
            // 销卡退款
            desc = "用于临时卡销卡退款";
            details = "现金支付退款,本金:" + ((double) cancellationBO.getRefundCashAmount() / 100) + "元;奖励" + ((double) cancellationBO.getPresent() / 100) + "元";
            if ("1000".equals(billingCard.getCardTypeId())) {
                details += "，在线账户：" + (double) cancellationBO.getRefundOnlineAmount() / 100 + "元";
            }
            details += ",操作来源:" + sourceName;

        } else if (refundType.equals(RefundType.SHOP)) {
            // 商超卡扣退款
            desc = "用于商超卡扣退款";
            details = "商品退款(卡扣),本金:" + ((double) cancellationBO.getRefundCashAmount() / 100) + "元;奖励" + ((double) cancellationBO.getPresent() / 100);
        } else if (refundType.equals(RefundType.CANCEL_PACKAGE_TIME)) {
            // 取消包时
            desc = "用于取消包时退款";
            details = "取消包时退款,本金:" + ((double) cancellationBO.getRefundCashAmount() / 100) + "元;奖励" + ((double) cancellationBO.getPresent() / 100);
        }

        addLogOperationNew(OperationType.REFUND,
                sourceType,
                cancellationBO.getRefundCashAmount(),
                cancellationBO.getPresent(),
                cancellationBO.getRefundOnlineAmount(),
                null,
                null,
                billingCard,
                null,
                logShift,
                desc,
                details);
    }

    /**
     * 操作记录 - 购物
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     */
    public void addBuyOperation(SourceType sourceType, BillingCard billingCard, int cost, int present) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = "商品购买(卡扣),消耗本金:" + (Double.valueOf(cost) / 100) + "元;消耗奖励" + (Double.valueOf(present) / 100);

        addLogOperation(OperationType.BUY, sourceType, cost, present, null, null, billingCard, null, null, desc,
                details);
    }

    public void addBuyTopupOperation(SourceType sourceType, BillingCard billingCard, int cost, int present) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details =  "购买虚拟商品";

        details +="充值网费" + Double.valueOf(-cost) / 100 + "元，奖励" + Double.valueOf(-present) / 100 + "元";

        addLogOperation(OperationType.TOPUP, sourceType, cost, present, null, null, billingCard, null, null, desc,
                details);
    }

    /**
     * 操作记录 - 累计包时
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param logAcc      激活方式
     */
    public void addAccOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift, LogAcc logAcc) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(logAcc, "logAcc 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";

        String details = "完成累计包时，" + (int) logAcc.getStartTime() + "时" + Math.round(logAcc.getStartTime() % 1 * 60) + "分 - "
                + (int) logAcc.getEndTime() + "时" + Math.round(logAcc.getEndTime() % 1 * 60) + "分，"
                + logAcc.getAccPrice() / 100.0 + "元";

        addLogOperation(OperationType.ACC, sourceType, 0, 0, null, null, billingCard, null, logShift, desc, details);
    }

    /**
     * 操作记录 - 取消激活
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addCancelActivateOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = "取消激活";

        addLogOperation(OperationType.CANCEL_ACTIVATE, sourceType, 0, 0, null, null, billingCard, null, logShift, desc,
                details);
    }

    /**
     * 操作记录 - 附加费
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addSurchargeOperation(SourceType sourceType, int amount, int present, String clientId,
                                      BillingCard billingCard, LogShift logShift, String loginId) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        String detailsSourceType = "激活上机附加费";
        if (sourceType.equals(SourceType.RZXREALNAME)) {
            detailsSourceType = "实名扣点附加费";
        }
        String details = detailsSourceType + (double) (amount + present) / 100 + "元";
        addLogOperation(OperationType.SURCHARGE, sourceType, -amount, -present, clientId, "", billingCard, loginId, logShift,
                "", details);
    }

    /**
     * 操作记录 - 第三方扣款
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addThirdDeductiondOperation(SourceType sourceType, int amount, int present, BillingCard billingCard,
                                            BillingOnline billingOnline, LogShift logShift, LogLogin logLogin, String remark) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");

        String clientId = null;
        String lastClientId = null;
        String loginId = null;
        if (billingOnline != null) {
            clientId = billingOnline.getClientId();
            lastClientId = billingOnline.getClientId();
        }
        if (logLogin != null) {
            loginId = logLogin.getLoginId();
        }
        String details = "第三方扣款";

        addLogOperation(OperationType.THIRD_DEDUCTION, sourceType, amount, present, clientId, lastClientId, billingCard,
                loginId, logShift, remark, details);
    }

    /**
     * 操作记录 - 删除会员
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addDeleteCardOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift,
                                       String remark) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        if (SourceType.CASHIER == sourceType) {
            Assert.notNull(logShift, "logShift 不能为空");
        }

        String operationName = SourceType.getDesc(sourceType);
        String details = operationName + "删除会员卡";

        addLogOperation(OperationType.DELETE_CARD,
                sourceType,
                0,
                0,
                null,
                null,
                billingCard,
                null,
                logShift,
                remark,
                details);
    }

    /**
     * 操作记录 - 登录上机
     *
     * @param sourceType    来源
     * @param clientId      客户端ID
     * @param billingCard   计费卡信息
     * @param billingOnline 在线信息
     * @param logShift      班次信息
     * @param logLogin      登录信息
     */
    public void addLoginLogOperation(SourceType sourceType, String clientId, BillingCard billingCard,
                                     BillingOnline billingOnline, LogShift logShift, LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(clientId, "clientId 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(billingOnline, "billingOnline 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");

        String desc = "";

        int cost = 0;

        String details = "";
        if (billingOnline.getPackageFlag() <= 0) {
            details = "标准计费(￥" + (float) billingOnline.getCommonPrice() / 100 + "/小时)";
            cost = -billingOnline.getDeduction();
        } else {
            // 计算包时时间差
            LocalDateTime startTime = billingOnline.getBillingTime();
            LocalDateTime endTime = billingOnline.getNextTime();
            Duration duration = Duration.between(startTime, endTime);
            float middle = new BigDecimal((float) duration.toMillis() / 3600000).setScale(2, RoundingMode.HALF_UP)
                    .floatValue();

            details = "包时长(￥" + Double.valueOf(billingOnline.getCommonPrice()) / 100 + "元，" + middle + "小时)";
            if (billingOnline.getPackageFlag() == 1) {
                details = details.replace("长", "段");
            }
            cost = -billingOnline.getDeduction();
        }

        addLogOperation(OperationType.LOGIN, sourceType, cost, 0, clientId, clientId, billingCard,
                logLogin.getLoginId(), logShift, desc, details);
    }

    /**
     * 操作记录 - 开卡
     *
     * @param sourceType       来源
     * @param billingCard      计费卡信息
     * @param logShift         班次信息
     * @param presentAmortized 分期奖励数
     */
    public void addCreateCardLogOperation(SourceType sourceType, BillingCard billingCard, int cost, int present, LogShift logShift, int presentAmortized,
                                          int amortizedAmountTotal) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
//		Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";
        String details = "";
        if (BillingConstants.AUTO_CARD_IDENT.equals(billingCard.getRemark())) {
            desc = BillingConstants.AUTO_CARD_IDENT;
            details = "收银台自助开卡  ";
        }

        if (cost > 0) {

            if (presentAmortized <= 0) {
                details += "充值" + Double.valueOf(cost) / 100 + "元，奖励" + Double.valueOf(present) / 100 + "元";
            } else {
                details = "充值" + Double.valueOf(cost) / 100 + "元，赠送" + Double.valueOf(present) / 100 + "元" + "，" +
                        "剩余" + Math.abs(Double.valueOf(amortizedAmountTotal - present)) / 100 + "元赠送分" + (presentAmortized - 1) + "个月返还";
            }
        }

        addLogOperation(OperationType.CREATE_CARD,
                sourceType,
                cost,
                present,
                null,
                null,
                billingCard,
                null,
                logShift,
                desc,
                details);
    }

    /**
     * 操作记录 - 赠送网费
     *
     * @param present       赠送金额
     * @param billingCard   计费卡信息
     * @param logShift      班次信息
     * @param billingOnline 在线信息
     * @param logLogin      登入信息
     */
    public void addTopupPresentLogOperation(int present, BillingCard billingCard, BillingOnline billingOnline,
                                            LogShift logShift, LogLogin logLogin, String desc, SourceType sourceType) {
        Assert.notNull(billingCard, "billingCard 不能为空");
//        Assert.notNull(logShift, "logShift 不能为空");
        String clientId = null;
        String lastClientId = null;
        String loginId = null;
        if (billingOnline != null) {
            clientId = billingOnline.getClientId();
            lastClientId = billingOnline.getClientId();
        }
        if (logLogin != null) {
            loginId = logLogin.getLoginId();
        }
        String details = PayType.CASH.getMessage() + "充值0元，奖励" + Double.valueOf(present) / 100 + "元";
        addLogOperation(OperationType.PRESENT, sourceType, 0, present, clientId, lastClientId, billingCard,
                loginId, logShift, desc, details);
    }

    /**
     * 操作记录 - 充值
     *
     * @param sourceType           来源
     * @param amount               充值金额
     * @param present              赠送金额
     * @param billingCard          计费卡信息
     * @param logShift             班次信息
     * @param billingOnline        在线信息
     * @param logLogin             登入信息
     * @param presentAmortized     分期次数，有值时present为单次赠送金额
     * @param amortizedAmountTotal 分期赠送总额
     */
    public void addTopupLogOperation(SourceType sourceType,
                                     PayType payType,
                                     int amount,
                                     int present,
                                     BillingCard billingCard,
                                     BillingOnline billingOnline,
                                     LogShift logShift,
                                     LogLogin logLogin,
                                     String remark,
                                     int presentAmortized,
                                     int amortizedAmountTotal) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
//		Assert.notNull(logShift, "logShift 不能为空");
        String clientId = null;
        String lastClientId = null;
        String loginId = null;
        if (billingOnline != null) {
            clientId = billingOnline.getClientId();
            lastClientId = billingOnline.getClientId();
        }
        if (logLogin != null) {
            loginId = logLogin.getLoginId();
        }
        String details = null;
        if (presentAmortized <= 0) {
            details = payType.getMessage() + "充值" + Double.valueOf(amount) / 100 + "元，奖励" + Double.valueOf(present) / 100 + "元";
        } else {
            details = payType.getMessage() + "充值" + Double.valueOf(amount) / 100 + "元，赠送" + Double.valueOf(present) / 100 + "元" + "，" +
                    "剩余" + Math.abs(Double.valueOf(amortizedAmountTotal - present)) / 100 + "元赠送分" + (presentAmortized - 1) + "个月返还";
        }


        addLogOperation(OperationType.TOPUP, sourceType, amount, present, clientId, lastClientId, billingCard, loginId,
                logShift, remark, details);
    }

    /**
     * 操作记录 - 注销（临时卡）
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addCancellationLogOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift) {

        if (!"1000".equals(billingCard.getCardTypeId())) {
            return;
        }
        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");

        String desc = "";

        int cashAccount = -billingCard.getCashAccount();
        int presentAccount = -billingCard.getPresentAccount();
        int onlineAccount = -billingCard.getTemporaryOnlineAccount();

        String details = "销卡时本金剩余:" + (double) billingCard.getCashAccount() / 100 + "元，奖励"
                + (double) billingCard.getPresentAccount() / 100 + "元";
        if ("1000".equals(billingCard.getCardTypeId()) && billingCard.getTemporaryOnlineAccount() > 0) {
            details += "，线上账户:" + (double) billingCard.getTemporaryOnlineAccount() / 100 + "元";
        }

        if (billingCard.getTemporaryOnlineAccount() > 0) {
            cashAccount = cashAccount + onlineAccount; // 前端线上余额是cashBalance + presentBalance
        }

        addLogOperationNew(OperationType.CANCELLATION, sourceType, cashAccount, presentAccount, onlineAccount, null, null, billingCard,
                null, logShift, desc, details);
    }

    /**
     * 操作记录 - 冲正
     *
     * @param sourceType  来源
     * @param amount      充值金额
     * @param present     赠送金额
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param remark      备注
     */
    public void addReversalLogOperation(SourceType sourceType, int amount, int present, BillingCard billingCard,
                                        LogShift logShift, String remark) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(logShift, "logShift 不能为空");

        if (amount > 0) {
            amount = 0 - amount;
        }

        if (present > 0) {
            present = 0 - present;
        }

        String details = "冲正本金" + Double.valueOf(amount) / 100 + "元，奖励" + Double.valueOf(present) / 100 + "元";

        addLogOperation(OperationType.REVERSAL, sourceType, amount, present, null, null, billingCard, null, logShift,
                remark, details);
    }

    /**
     * 操作记录 - 分期赠送
     *
     * @param present 赠送金额
     */
    public void addRewardInstallmentLogOperation(int present, BillingCard billingCard, LogShift shift) {


        int year = LocalDateTime.now().getYear();
        int month = LocalDateTime.now().getMonth().getValue();

        String details = "奖励" + Double.valueOf(present) / 100 + "元，充值后" + year + "年-" + month + "月的分期奖励到账";

        addLogOperation(OperationType.PRESENT, SourceType.CASHIER, 0, present, null, null, billingCard, null, shift,
                null, details);
    }


    /**
     * 操作记录 - 积分调整
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param remark      备注
     */
    public void addReversalPointsLogOperation(SourceType sourceType, int points, BillingCard billingCard,
                                              LogShift logShift, String remark) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        Assert.notNull(logShift, "logShift 不能为空");

        if (points > 0) {
            points = 0 - points;
        }

        String details = "冲正积分" + points + "，操作后余" + billingCard.getPoints();

        addLogOperation(OperationType.REVERSAL_POINTS, sourceType, points, 0, null, null, billingCard, null, logShift,
                remark, details);
    }

    /**
     * 操作记录 - 结账
     *
     * @param sourceType    来源
     * @param billingCard   计费卡信息
     * @param logShift      班次信息
     * @param logLogin      登录信息
     */
    public void addLogoutLogOperation(SourceType sourceType, BillingCard billingCard, LogShift logShift, LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
       // Assert.notNull(logLogin, "billingOnline 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");

        String desc = "";

        int cost = -logLogin.getConsumptionTotal();

        String details = "本次消费" + (double) logLogin.getConsumptionTotal() / 100 + "元";

        addLogOperation(OperationType.LOGOUT, sourceType, cost, 0, logLogin.getClientId(),
                logLogin.getClientId(), billingCard, logLogin.getLoginId(), logShift, desc, details);
    }

    /**
     * 操作记录 - 结账
     *
     * @param sourceType    来源
     * @param billingCard   计费卡信息
     * @param logShift      班次信息
     * @param logLogin      登录信息
     */
    public void addLogoutLogOperationAll(SourceType sourceType, BillingCard billingCard, LogShift logShift, LogLogin logLogin) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
       // Assert.notNull(billingOnline, "billingOnline 不能为空");
        // Assert.notNull(logShift, "logShift 不能为空");
        Assert.notNull(logLogin, "logLogin 不能为空");

        String desc = "";

        int cost = -logLogin.getConsumptionTotal();

        String details = "本次消费" + Double.valueOf(logLogin.getConsumptionTotal()) / 100 + "元";

        addLogOperation(OperationType.LOGOUT_ALL, sourceType, cost, 0, logLogin.getClientId(),
                logLogin.getClientId(), billingCard, logLogin.getLoginId(), logShift, desc, details);
    }

    /**
     * 操作记录 - 注销（临时卡） 全场结账
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     */
    public void addCancellationLogOperationAll(SourceType sourceType, BillingCard billingCard, LogShift logShift) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        String desc = "";

        int cashAccount = -billingCard.getCashAccount();
        int presentAccount = -billingCard.getPresentAccount();
        int onlineAccount = -billingCard.getTemporaryOnlineAccount();

        String details = "销卡时本金剩余:" + (double) billingCard.getCashAccount() / 100 + "元，奖励"
                + (double) billingCard.getPresentAccount() / 100 + "元";
        if ("1000".equals(billingCard.getCardTypeId()) && billingCard.getTemporaryOnlineAccount() > 0) {
            details += "，线上账户:" + (double) billingCard.getTemporaryOnlineAccount() / 100 + "元";
        }

        addLogOperationNew(OperationType.CANCELLATION, sourceType, cashAccount, presentAccount, onlineAccount, null, null, billingCard,
                null, logShift, desc, details);
    }

    /**
     * 操作记录 - 优惠券使用
     *
     * @param sourceType  来源
     * @param billingCard 计费卡信息
     * @param logShift    班次信息
     * @param couponName    优惠券名称
     * @param couponType    优惠券使用类型，0充值，1包时 ...
     * @param packageTimeType    0预包时，1在线包时
     */
    public void addUseCpuponLogOperation(int present, BillingCard billingCard, BillingOnline billingOnline, LogShift logShift, LogLogin logLogin, SourceType sourceType,
                                         String couponName,int couponType,BillingRulePackageTime billingRulePackageTime,int packageTimeType) {
        Assert.notNull(billingCard, "billingCard 不能为空");
        String clientId = null;
        String lastClientId = null;
        String loginId = null;
        if (billingOnline != null) {
            clientId = billingOnline.getClientId();
            lastClientId = billingOnline.toBO().getLastOnlineClientId();
        }
        if (logLogin != null) {
            loginId = logLogin.getLoginId();
        }
//        String details = PayType.CASH.getMessage() + "充值0元，奖励" + Double.valueOf(present) / 100 + "元";
        String details = null;
        if(0 == couponType){
            details = SourceType.getDesc(sourceType) + "使用优惠券["+couponName+"]，获取奖励"+Double.valueOf(present) / 100 +"元";

            addLogOperation(OperationType.PRESENT, sourceType, 0, present, clientId, lastClientId, billingCard,
                    loginId, logShift, null, details);
        }else if(1 == couponType){
            String packageTimeTypeStr = packageTimeType == 0 ? "预包时" : "在线包时";
            if (billingRulePackageTime.getPackageFlag() == 1) {
                float startTime = billingRulePackageTime.getStartTime();
                float endTime = billingRulePackageTime.getEndTime();
                // 如果选择的是0点到0点: startTime==endTime
                if (startTime == endTime) {
                    endTime += 24;
                }
                float diff = new BigDecimal((endTime - startTime)).setScale(2, RoundingMode.HALF_UP).floatValue();
                float middle = endTime < startTime ? (diff + 24) : diff;
                details =  SourceType.getDesc(sourceType) + "使用优惠券["+couponName+"]"+packageTimeTypeStr+"，包时段(" + middle + "小时)";
            } else if (billingRulePackageTime.getPackageFlag() == 2) {
                details =  SourceType.getDesc(sourceType) + "使用优惠券["+couponName+"]"+packageTimeTypeStr+"，包时长(" + billingRulePackageTime.getDurationTime()+ "小时)";
            } else if (billingRulePackageTime.getPackageFlag() == 9) {
                details =  SourceType.getDesc(sourceType) + "使用优惠券["+couponName+"]"+packageTimeTypeStr+"，自定义包时(，" + billingRulePackageTime.getDurationTime() + "小时)";
            }

            addLogOperation(OperationType.PACKAGE_TIME, sourceType, 0, 0, billingOnline == null ? "" : billingOnline.getClientId(),
                    billingOnline == null ? "" : billingOnline.getClientId(), billingCard, logLogin == null ? "" : logLogin.getLoginId(), logShift, billingRulePackageTime.getRuleId(), details);
        }

    }

    /**
     * 添加一条操作记录
     *
     * @param operationType 操作类型
     * @param sourceType    操作来源
     * @param cost          操作金额
     * @param present       操作赠送金额
     * @param clientId      客户端ID
     * @param lastClientId  换机是上一个客户端ID
     * @param billingCard   计费卡
     * @param loginId       登录ID
     * @param logShift      班次信息
     * @param remark        备注
     * @return
     */
    private LogOperation addLogOperation(OperationType operationType,
                                         SourceType sourceType,
                                         int cost,
                                         int present,
                                         String clientId,
                                         String lastClientId,
                                         BillingCard billingCard,
                                         String loginId,
                                         LogShift logShift,
                                         String remark,
                                         String details) {

        return this.addLogOperationNew(operationType,
                sourceType,
                cost,
                present,
                billingCard.getTemporaryOnlineAccount(),
                clientId,
                lastClientId,
                billingCard,
                loginId,
                logShift,
                remark,
                details);
    }

    private LogOperation addLogOperationNew(OperationType operationType,
                                            SourceType sourceType,
                                            int cost,
                                            int present,
                                            int onlineAccount,
                                            String clientId,
                                            String lastClientId,
                                            BillingCard billingCard,
                                            String loginId,
                                            LogShift logShift,
                                            String remark,
                                            String details) {

        Assert.notNull(sourceType, "sourceType 不能为空");
        Assert.notNull(operationType, "expenseType 不能为空");
        Assert.notNull(billingCard, "billingCard 不能为空");
        LocalDateTime now = LocalDateTime.now();
        LogOperation logOperation = new LogOperation();
        logOperation.setCreated(now.minusNanos(now.getNano())); // 不要毫秒
        logOperation.setSourceType(sourceType);
        logOperation.setOperationType(operationType);
        logOperation.setCost(cost);
        logOperation.setPresent(present);
        logOperation.setPlaceId(billingCard.getPlaceId());
        logOperation.setCardId(billingCard.getCardId());
        logOperation.setIdNumber(billingCard.getIdNumber());
        logOperation.setIdName(billingCard.getIdName());
        logOperation.setCardId(billingCard.getCardId());
        logOperation.setCardTypeId(billingCard.getCardTypeId());
        logOperation.setCardTypeName(billingCard.getCardTypeName());

        if (operationType.equals(OperationType.CANCELLATION)) {
            logOperation.setCashBalance(0);
            logOperation.setPresentBalance(0);
            logOperation.setOnlineAccount(0);
        } else {
            // 前端线上余额是cashBalance + presentBalance
            if (onlineAccount > 0) {
                logOperation.setCashBalance(billingCard.getCashAccount() + onlineAccount);
            } else {
                logOperation.setCashBalance(billingCard.getCashAccount());
            }

            logOperation.setPresentBalance(billingCard.getPresentAccount());
            logOperation.setOnlineAccount(onlineAccount);
        }

        logOperation.setRemark(remark);
        logOperation.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        logOperation.setCreaterName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
        logOperation.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
        logOperation.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
        logOperation.setClientId(clientId);
        logOperation.setLastClientId(lastClientId);
        logOperation.setLoginId(loginId);
        logOperation.setDetails(details);

//        logOperationRepository.save(logOperation);
        try {
            logOperationRepository.save(logOperation);
        } catch (Exception e) {
            logOperation.setRemark(logOperation.getRemark() + "，防御性数据1");
            try {
                logOperationRepository.save(logOperation);
            } catch (Exception ex) {
                logOperation.setRemark(logOperation.getRemark() + "，防御性数据2");
                logOperationRepository.save(logOperation);
            }
        }
        return logOperation;
    }

    public Specification<LogOperation> queryParams(Map<String, Object> map) {

        return (root, query, cb) -> {

            List<Predicate> andPredicateList = new ArrayList<>();

            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {// 场所ID
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
            }
            if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {// 卡类型ID
                andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), map.get("cardTypeId")));
            }
            if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {// 卡ID
                andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
            }
            if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {// 交班ID
                andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
            }
            if (map.containsKey("loginId") && !StringUtils.isEmpty(map.get("loginId"))) {// 登入Id
                andPredicateList.add(cb.equal(root.get("loginId").as(String.class), map.get("loginId")));
            }
            if (map.containsKey("loginIds") && !org.springframework.util.StringUtils.isEmpty(map.get("loginIds"))) {// 登录id集合
                andPredicateList.add(root.get("loginId").as(String.class).in(map.get("loginIds")));
            }

            if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                if (map.get("idNumber").toString().length() == 18) {
                    andPredicateList.add(cb.equal(root.get("idNumber"), map.get("idNumber")));
                } else {
                    andPredicateList.add(cb.like(root.get("idNumber"), map.get("idNumber") + "%"));
                }
            }
            // 身份证号(收银台查询)
            if (map.containsKey("cashierIdNumber") && !StringUtils.isEmpty(map.get("cashierIdNumber"))) {
                Predicate predicate = cb.like(root.get("idNumber"), "%" + map.get("cashierIdNumber") + "%");
                if ((map.get("cashierIdNumber") + "").length() == 18) {
                    predicate = cb.equal(root.get("idNumber"), map.get("cashierIdNumber"));
                }
                andPredicateList.add(predicate);
            }
            // 如果收银台查询操作日志没有传cashierIdNumber参数，需要过滤掉自动升降级的操作记录
            if (map.containsKey("operationTypeNotIn") && !StringUtils.isEmpty(map.get("operationTypeNotIn"))) { //
                List<String> operations = (List)map.get("operationTypeNotIn");
                List<OperationType> collect = operations.stream().map(OperationType::valueOf).collect(Collectors.toList());
                andPredicateList.add(cb.not(root.get("operationType").as(OperationType.class).in(collect)));
            }

            if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 姓名
                andPredicateList.add(cb.like(root.get("idName"), map.get("idName") + "%"));
            }
            if (map.containsKey("createrName") && !StringUtils.isEmpty(map.get("createrName"))) { // 操作人姓名
                andPredicateList.add(cb.like(root.get("createrName"), map.get("createrName") + "%"));
            }
            if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {// 操作开始时间
                LocalDateTime startTime = LocalDateTime.parse(map.get("startDate").toString(), fmt);
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
            }
            if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {// 操作结束时间
                LocalDateTime endTime = LocalDateTime.parse(map.get("endDate").toString(), fmt);
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
            }
            if (map.containsKey("operationType") && !StringUtils.isEmpty(map.get("operationType"))) {// 消费类型
                andPredicateList.add(cb.equal(root.get("operationType").as(OperationType.class), OperationType.valueOf(map.get("operationType").toString())));
            }
            if (map.containsKey("operationTypeNot") && !StringUtils.isEmpty(map.get("operationTypeNot"))) {// 消费类型
                andPredicateList.add(cb.notEqual(root.get("operationType").as(OperationType.class), OperationType.valueOf(map.get("operationTypeNot").toString())));
            }
            if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {// 来源
                andPredicateList.add(cb.equal(root.get("sourceType").as(SourceType.class), SourceType.valueOf(map.get("sourceType").toString())));
            }
            if (map.containsKey("sourceTypes") && !StringUtils.isEmpty(map.get("sourceTypes"))) {// 来源
                List<String> sourceTypeStrs = (List)map.get("sourceTypes");
                List<SourceType> collect = sourceTypeStrs.stream().map(SourceType::valueOf).collect(Collectors.toList());
                andPredicateList.add(root.get("sourceType").as(SourceType.class).in(collect));
            }

            // 卡id
            if (map.containsKey("cardIds") && !org.springframework.util.StringUtils.isEmpty(map.get("cardIds"))) { // 卡Ids
                Path<Object> path = root.get("cardId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> cardIds = (List) map.get("cardIds");
                cardIds.forEach(in::value);
                andPredicateList.add(in);
            }

            if (map.containsKey("placeIds") && !org.springframework.util.StringUtils.isEmpty(map.get("placeIds"))) { // 卡Ids
                List<String> placeIds = (List) map.get("placeIds");
                if(placeIds.size() == 1){
                    andPredicateList.add(cb.equal(root.get("placeId").as(String.class), placeIds.get(0)));
                }else{
                    Path<Object> path = root.get("placeId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    placeIds.forEach(in::value);
                    andPredicateList.add(in);
                }

            }

            // 客户端id
            if (map.containsKey("clientIds") && !org.springframework.util.StringUtils.isEmpty(map.get("clientIds"))) { // 客户端id
                Path<Object> path = root.get("clientId");
                CriteriaBuilder.In<Object> in = cb.in(path);
                List<String> clientIds = (List) map.get("clientIds");
                clientIds.forEach(in::value);
                andPredicateList.add(in);
            }

            // 操作类型
            if (map.containsKey("operationTypeArr") && !org.springframework.util.StringUtils.isEmpty(map.get("operationTypeArr"))) { // 客户端id
                Path<Object> path = root.get("operationType");
                CriteriaBuilder.In<Object> in = cb.in(path.as(OperationType.class));
                List<String> operationTypeArr = (List) map.get("operationTypeArr");

                List<OperationType> collect = operationTypeArr.stream().map(OperationType::valueOf).collect(Collectors.toList());
                collect.forEach(in::value);
                andPredicateList.add(in);
            }

            // 查询钱的记录，map 都添加 searchMoney，排除掉0元开卡的，其他的显示 
            if (map.containsKey("searchMoney") && !StringUtils.isEmpty(map.get("searchMoney"))) {
                andPredicateList.add(cb.greaterThan(root.get("cost").as(int.class), 0));
            }

            // minCashBalance 本金金额大于
            if (map.containsKey("minCashBalance") && !StringUtils.isEmpty(map.get("minCashBalance"))) {
                Object minCashBalance = map.get("minCashBalance");
                assert minCashBalance instanceof String;
                Integer minCashBalanceInt = Integer.parseInt((String) minCashBalance);
                Predicate predicate = cb.greaterThanOrEqualTo(root.get("cost"), minCashBalanceInt);
                andPredicateList.add(predicate);
            }

            // maxCashBalance 本金金额小于
            if (map.containsKey("maxCashBalance") && !StringUtils.isEmpty(map.get("maxCashBalance"))) {
                Object maxCashBalance = map.get("maxCashBalance");
                assert maxCashBalance instanceof String;
                Integer maxCashBalanceInt = Integer.parseInt((String) maxCashBalance);
                Predicate predicate = cb.lessThanOrEqualTo(root.get("cost"), maxCashBalanceInt);
                andPredicateList.add(predicate);
            }

            // minPresent 赠送金额大于
            if (map.containsKey("minPresent") && !StringUtils.isEmpty(map.get("minPresent"))) {
                Object minPresent = map.get("minPresent");
                assert minPresent instanceof String;
                Integer minPresentInt = Integer.parseInt((String) minPresent);
                Predicate predicate = cb.greaterThanOrEqualTo(root.get("present"), minPresentInt);
                andPredicateList.add(predicate);
            }

            // maxPresent 赠送金额小于
            if (map.containsKey("maxPresent") && !StringUtils.isEmpty(map.get("maxPresent"))) {
                Object maxPresent = map.get("maxPresent");
                assert maxPresent instanceof String;
                Integer maxPresentInt = Integer.parseInt((String) maxPresent);
                Predicate predicate = cb.lessThanOrEqualTo(root.get("present"), maxPresentInt);
                andPredicateList.add(predicate);
            }
            // 过滤主次收银台数据混乱问题
            if (map.containsKey("shiftIdParam") && !StringUtils.isEmpty(map.get("shiftIdParam"))) {//
//                andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftIdParam")));
                andPredicateList.add(cb.or(cb.equal(root.get("shiftId").as(String.class), map.get("shiftIdParam")),
                        cb.equal(root.get("shiftId").as(String.class), "")));
            }


            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];

            return cb.and(andPredicateList.toArray(andPredicateArr));
        };
    }


    /**
     * 分页查询
     *
     * @param map
     * @param pageable
     * @return
     */
    public Page<LogOperation> findAll(Map<String, Object> map, Pageable pageable) {
        LocalDateTime startDateTime;//查询开始时间
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }

        //临时添加控制：如果查询条件的时间段大于2个月，改为2个月
        if(map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate").toString().trim())
                && map.containsKey("endDate")&& !StringUtils.isEmpty(map.get("endDate").toString().trim())  ){
            //校验查询的时间条件
            VerifyParam.checkTimeDifferenceByMonth(LocalDateTime.parse(map.get("startDate").toString(), format),
                    LocalDateTime.parse(map.get("endDate").toString(), format));
        }

        LocalDateTime endDateTime = LocalDateTime.now();
//        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (!map.containsKey("startDate") || StringUtils.isEmpty(map.get("startDate").toString().trim())) {
            String startDateTimeStr = format.format(startDateTime);
            map.put("startDate", startDateTimeStr);
        } else {
            // 如果前端传了时间，并且时间又是晚上的18点后，查询开始时间又是本月之前，则更改查询开始时间为当月第一天
            String startTime = map.get("startDate").toString();
            LocalDateTime startTimeLocal = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));//前端传参的开始时间
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
            if ((hour > 18 && hour < 22) && startDateTime.isBefore(startTimeLocal)) {
                String startDateTimeStr = format.format(startDateTime);//把查询开始时间改为当月第一天
                map.put("startDate", startDateTimeStr);
            }
        }
        if (!map.containsKey("endDate") || StringUtils.isEmpty(map.get("endDate").toString().trim())) {
            String endDateTimeStr = format.format(endDateTime);
            map.put("endDate", endDateTimeStr);
        }
        Specification<LogOperation> specification = this.queryParams(map);
        return logOperationRepository.findAll(specification, pageable);
    }


    /**
     * 按班次-赠送-收入
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostCashIncomeOfPresentByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.sumCostCashIncomeOfPresentByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }



    /**
     * 按班次-卡类型-附加费总次数
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int countSurchargeByShiftIdAndCardTypeId(String placeId, String shiftId, String cardTypeId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.countSurchargeByShiftIdAndCardTypeId(placeId, shiftId, cardTypeId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-卡类型-附加费总金额
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumSurchargeByShiftIdAndCardTypeId(String placeId, String shiftId, String cardTypeId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.sumSurchargeByShiftIdAndCardTypeId(placeId, shiftId, cardTypeId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-冲正
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostTotalReversalByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.sumCostTotalReversalByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-新开会员卡-数量
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int countCreateMemberCardByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.countCreateMemberCardByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-新开临时卡-数量
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int countCreateTemporaryCardByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.countCreateTemporaryCardByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-会员卡-消费金额
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostMemberCardConsumptionByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.sumCostMemberCardConsumptionByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次-临时卡-消费金额
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumCostTemporaryCardConsumptionByShiftId(String placeId, String shiftId) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        Integer result = logOperationRepository.sumCostTemporaryCardConsumptionByShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按时间-新开开临时卡-数量
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int countCreateTemporaryCardByDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                                  LocalDateTime endDateTime) {
        Integer result = logOperationRepository.countCreateTemporaryCardByDateTime(placeIds, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按时间-新开会员卡-数量
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int countCreateMemberCardByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logOperationRepository.countCreateMemberCardByDateTime(placeIds, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }



    /**
     * 按时间-卡号-附加费
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostSurchargeByDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                          LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumCostSurchargeByDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按时间-卡号-商品购买-总收入
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostShopCashByDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                         LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumCostShopCashByDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按时间-卡号-总退款(商品退款、取消包时、网费退款)
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostShopRefundByDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                           LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumCostShopRefundByDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }



    /**
     * 按时间-总冲正
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostTotalReversalByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumCostTotalReversalByDateTime(placeIds, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }


    /**
     * 按时间-冲正-数据列表
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public List<LogOperation> findLogOperationByReversalAndDateTime(String placeId, LocalDateTime startDateTime,
                                                                    LocalDateTime endDateTime) {
        return logOperationRepository.findLogOperationByReversalAndDateTime(placeId, startDateTime, endDateTime);
    }

    /**
     * 按时间-卡Id-总冲正
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                                       LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumCostTotalReversalByCardIdAndDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }


    /**
     * 按时间-卡Id-总冲正(本金+奖励)
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                                   LocalDateTime endDateTime) {
        Integer result = logOperationRepository.sumTotalReversalByCardIdAndDateTime(placeId, cardId, startDateTime,
                endDateTime);
        return result == null ? 0 : result;
    }


    /**
     * 组装交接班列表数据
     * @apiNote log_operation-15、log_topup-2、log_refund-2、billing_card-1、log_other_income-1
     *
     * @param placeShiftBO
     * @return
     */
    public PlaceShiftBO getShiftStatistics(PlaceShiftBO placeShiftBO) {

        String placeId = placeShiftBO.getPlaceId();
        String shiftId = placeShiftBO.getShiftId();

        LocalDateTime workingTime = placeShiftBO.getWorkingTime();
        LocalDateTime now =LocalDateTime.now();
        // 收入-现金
        int memberCardCashIncome = logTopupService.sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeIdNotIn(placeId, shiftId,"1000", workingTime, now); // 会员卡-收入
        int temporaryCardCashIncome = logTopupService.sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeId(placeId, shiftId,"1000", workingTime, now); // 临时卡-收入

        int cashIncome = logTopupService.sumCashAmountByPlaceIdAndShiftId(placeId, shiftId, workingTime, now); // 现金-收入
        // 赠送金额2023.7.1
        int presentAmount = sumCostCashIncomeOfPresentByShiftId(placeId, shiftId); // 会员卡-赠送

        // 线上收入
//        int topupOnlineIncome = sumCostOnlineIncomeByShiftId(placeId, shiftId);
        int topupOnlineIncome = logTopupService.sumOnlineAmountByPlaceIdAndShiftId(placeId, shiftId, workingTime, now);

        // 线上退款
        Integer onlineRefund = logRefundRepository.sumCostOnlineRefundByShiftId(placeId, shiftId);
        int topupOnlineRefund = onlineRefund == null ? 0 : onlineRefund;

        // 现金退款(总退款金额，已经包括销卡金额)
        Integer cashRefund = logRefundService.sumCostCashRefundByShiftId(placeId, shiftId);
        int topupCashRefund = cashRefund == null ? 0 : cashRefund;

        // 线上纯收入(除商超)
        int totalOnlineIncome = topupOnlineIncome - topupOnlineRefund;

        // 退款
//        int temporaryCardOutcome = sumCostTemporaryCardOutcomeByShiftId(placeId, shiftId); // 临时卡-退款
        int temporaryCardOutcome = logRefundService.sumRefundAmountByPlaceIdAndShiftIdAndCardTypeIdAndRefundType(placeId,shiftId,"1000");

        // 数量
        int createMemberCard = countCreateMemberCardByShiftId(placeId, shiftId); // 开会员卡数量
        int createTemporaryCard = countCreateTemporaryCardByShiftId(placeId, shiftId); // 开临时卡数量

        // 未消费
        int temporaryCardBalance = sumBalanceTemporaryCardCashIncomeByPlaceId(placeId); // 临时卡未消费总额

        // 冲正
        int totalReversal = sumCostTotalReversalByShiftId(placeId, shiftId); // 冲正

        // 九威充值总数
//        int jwellTopupIncome = marketLogOperationService.sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(placeId,shiftId,SourceType.JWELL);

        // 营销大师充值总数
//        int marketTopupIncome = marketLogOperationService.sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(placeId,shiftId,SourceType.MARKET);

        // 临时卡附加费次数
        int countTempSurcharge = countSurchargeByShiftIdAndCardTypeId(placeId, shiftId, "1000");

        // 会员卡附加费次数
        int countMemberSurcharge = countSurchargeByShiftIdAndCardTypeId(placeId, shiftId, "1001");

        // 临时卡附加费总金额
        int sumTempSurcharge = sumSurchargeByShiftIdAndCardTypeId(placeId, shiftId, "1000");

        // 会员卡附加费总金额
        int sumMemberSurcharge = sumSurchargeByShiftIdAndCardTypeId(placeId, shiftId, "1001");


        // 查询第三方充值现金总额/赠送总额


        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        String placeBizConfigThirdAccountId = placeBizConfig.getThirdAccountId();
        // 绑定了第三方账号，才查询相关数据
        if (!StringUtils.isEmpty(placeBizConfigThirdAccountId)) {
//            LocalDateTime workingTime = placeShiftBO.getWorkingTime();
//            LocalDateTime offWorkingTime = placeShiftBO.getOffWorkingTime();

//        int sumCashTopupForThird = sumCashTopupForThird(placeId, workingTime, offWorkingTime);
//        int sumPresentTopupForThird = sumPresentTopupForThird(placeId, workingTime, offWorkingTime);
//
//            int sumCashTopupForThirdMember = sumCashTopupForThirdNew(placeId, workingTime, offWorkingTime, BaseConstants.MEMBER_CARD_DESC);
//            int sumCashTopupForThirdTemp = sumCashTopupForThirdNew(placeId, workingTime, offWorkingTime, BaseConstants.TEMP_CARD_TYPE_ID);
//            int sumPresentTopupForThirdMember = sumPresentTopupForThirdNew(placeId, workingTime, offWorkingTime, BaseConstants.MEMBER_CARD_DESC);
//            int sumPresentTopupForThirdTemp = sumPresentTopupForThirdNew(placeId, workingTime, offWorkingTime, BaseConstants.TEMP_CARD_TYPE_ID);

            Map<String, Integer> tempMap = marketLogOperationService.sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeId(placeId, shiftId, "1000");
            Integer sumCashTopupForThirdTemp = tempMap.getOrDefault("sumCash", 0);
            Integer sumPresentTopupForThirdTemp = tempMap.getOrDefault("sumPresent", 0);

            Map<String, Integer> memberMap = marketLogOperationService.sumAmountAndPresentByShiftIdAndPlaceIdAndCardTypeIdNotIn(placeId, shiftId, Arrays.asList("1000", "1002"));
            Integer sumCashTopupForThirdMember = memberMap.getOrDefault("sumCash", 0);
            Integer sumPresentTopupForThirdMember = memberMap.getOrDefault("sumPresent", 0);

            placeShiftBO.setSumCashTopupForThirdMember(sumCashTopupForThirdMember);
            placeShiftBO.setSumCashTopupForThirdTemp(sumCashTopupForThirdTemp);
            placeShiftBO.setSumPresentTopupForThirdMember(sumPresentTopupForThirdMember);
            placeShiftBO.setSumPresentTopupForThirdTemp(sumPresentTopupForThirdTemp);
            placeShiftBO.setSumCashTopupForThird(sumCashTopupForThirdMember + sumCashTopupForThirdTemp);
            placeShiftBO.setSumPresentTopupForThird(sumPresentTopupForThirdMember + sumPresentTopupForThirdTemp);
        }

        // 其他收入
        int otherIncome = logOtherIncomeService.sumIncomeByShiftId(placeId, shiftId, 0);
        // 其他支出
        int otherOutcome = logOtherIncomeService.sumIncomeByShiftId(placeId, shiftId, 1);
        placeShiftBO.setOtherIncome(otherIncome + otherOutcome);

        placeShiftBO.setMemberCardCashIncome(memberCardCashIncome);
        placeShiftBO.setTemporaryCardCashIncome(temporaryCardCashIncome);
        placeShiftBO.setCashIncome(cashIncome);
        placeShiftBO.setTotalCashIncome(cashIncome + otherIncome + otherOutcome - topupCashRefund);

        placeShiftBO.setTemporaryCardOutcome(temporaryCardOutcome);
        placeShiftBO.setCashOutcome(-topupCashRefund);
        placeShiftBO.setTotalOutcome(-topupCashRefund);

        placeShiftBO.setCreateMemberCard(createMemberCard);
        placeShiftBO.setCreateTemporaryCard(createTemporaryCard);

//		placeShiftBO.setMemberCardConsumption(memberCardConsumption);
//		placeShiftBO.setTemporaryCardConsumption(temporaryCardConsumption);
//
        placeShiftBO.setTemporaryCardBalance(temporaryCardBalance);
        placeShiftBO.setTotalReversal(totalReversal);

        placeShiftBO.setTotalOnlineIncome(totalOnlineIncome); // 在线净收入
        placeShiftBO.setTopupOnlineRefund(topupOnlineRefund); // 在线退款
        placeShiftBO.setTopupOnlineIncome(topupOnlineIncome); // 在线充值
//        placeShiftBO.setJwellTopupIncome(jwellTopupIncome); // 九威充值总数
//        placeShiftBO.setMarketTopupIncome(marketTopupIncome); // 营销大师充值总数
        placeShiftBO.setPresentAmount(presentAmount); // 赠送金额
        placeShiftBO.setCountTempSurcharge(countTempSurcharge); // 临时卡附加费次数
        placeShiftBO.setCountMemberSurcharge(countMemberSurcharge); // 会员卡附加费次数
        placeShiftBO.setSumTempSurcharge(sumTempSurcharge); // 临时卡附加费总额
        placeShiftBO.setSumMemberSurcharge(sumMemberSurcharge); // 会员卡附加费总额
        return placeShiftBO;
    }

    /**
     * 按班次-临时卡-未消费总额
     *
     * @param placeId
     * @return
     */
    public int sumBalanceTemporaryCardCashIncomeByPlaceId(String placeId) {
        Integer result = billingCardRepository.sumBalanceTemporaryCardCashIncomeByPlaceId(placeId);
        return result == null ? 0 : result;
    }

    public PagerDTO<StatisticSumTopupBO> sumTopupByPlaceIdGroupByCardId(String placeId, LocalDateTime startDateTime,
                                                                        LocalDateTime endDateTime, Pageable pageable) {
        //1.查询时间段内有充值动作的会员卡
        List<String> cardIds = logOperationRepository.findCardIdsTopupHistoryByPlaceId(placeId, startDateTime, endDateTime);
        if(cardIds.size() == 0){
            throw new ServiceException(ServiceCodes.BILLING_TOPUP_CAN_USE_NOT_FOUND);
        }
        //2.校验会员卡是否还存在
        List<BillingCard> allByPlaceIdAndCardIdIn = billingCardRepository.findAllByPlaceIdAndCardIdInAndDeleted(placeId, cardIds,0);
        cardIds = allByPlaceIdAndCardIdIn.stream().map(BillingCard::getCardId).collect(Collectors.toList());

        //3.查询金额
        List<Map<String, String>> list = logOperationRepository.sumTopupByPlaceIdGroupByCardId(placeId, startDateTime,endDateTime,cardIds, pageable);
        List<Map<String, String>> cardIdAndCounts = new ArrayList<>();
        List<StatisticSumTopupBO> bos = new ArrayList<>();
        if (list.size() > 0) {
            cardIds = list.stream().map(it -> it.get("cardId")).collect(Collectors.toList());
            cardIdAndCounts = logOperationRepository.queryTopupCountByPlaceIdAndCardIdIn(placeId, startDateTime, endDateTime, cardIds);
        }
        for (Map<String, String> map : list) {
            StatisticSumTopupBO bo = new StatisticSumTopupBO();
            bo.setCardId(map.get("cardId"));
            Object obj = map.get("costs");
            BigDecimal costs = obj == null ? BigDecimal.ZERO : (BigDecimal) obj;
            bo.setCosts(costs.intValue());
            for (Map<String, String> cardIdAndCount : cardIdAndCounts) {
                if (cardIdAndCount.get("cardId").equals(map.get("cardId"))) {
                    obj = cardIdAndCount.get("counts");
                    BigInteger counts = obj == null ? BigInteger.ZERO : (BigInteger) obj;
                    bo.setCounts(counts.intValue());
                    break;
                }
            }
            for (BillingCard billingCard : allByPlaceIdAndCardIdIn) {
                if (billingCard.getCardId().equals(map.get("cardId"))) {
                    bo.setIdName(billingCard.getIdName());
                    bo.setIdNumber(billingCard.getIdNumber());
                    bo.setCardTypeName(billingCard.getCardTypeName());
                    break;
                }
            }
            obj = map.get("presents");
            BigDecimal presents = obj == null ? BigDecimal.ZERO : (BigDecimal) obj;
            bo.setPresents(presents.intValue());
            bos.add(bo);
        }

        Long count = logOperationRepository.countTopupByPlaceIdGroupByCardId(placeId, startDateTime, endDateTime,cardIds);

        return new PagerDTO<>(count.intValue(), bos);
    }


    /**
     * 带筛选条件的统计操作余额
     *
     * @param placeId
     * @param startDate
     * @param endDate
     * @param idNumber
     * @param cardTypeId
     * @param createrName
     * @param idName
     * @param sourceTypes
     * @param operationTypeArr
     * @return
     */
    public Map<String, Integer> querySumCostAndPresent(String placeId, String startDate, String endDate, String idNumber, String cardTypeId,
                                                       String createrName, String idName, List<String> sourceTypes, List<String> operationTypeArr) {
        Map<String, Integer> result = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateLdt = LocalDateTime.parse(startDate, formatter);
        LocalDateTime endDateLdt = LocalDateTime.parse(endDate, formatter);
        VerifyParam.checkTimeDifferenceByMonth(startDateLdt,endDateLdt);

        Map<String, String> map = logOperationRepository.querySumCostAndPresent(placeId, startDateLdt, endDateLdt, idNumber, cardTypeId, createrName, idName, sourceTypes, operationTypeArr);
        for (String key : map.keySet()) {
            Object obj = map.get(key);
            if (StringUtils.isEmpty(obj)) {
                result.put(key, 0);
            } else {
                result.put(key, new BigDecimal(obj.toString()).intValue());
            }
        }
        return result;
    }

    public List<Map<String, String>> findHistoryPlacePage(String idNumber) {
//        LocalDateTime startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        return logOperationRepository.findHistoryPlacePage(idNumber, startDateTime, endDateTime);
    }

    // 用于收银台会员管理页面班次的下机的用户列表
    public List<LogOperation> findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndOperationType(String placeId, LocalDateTime startTime, LocalDateTime endTime, OperationType operationType) {
        return logOperationRepository.findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqualAndOperationType(placeId, startTime, endTime, operationType);
    }

    /**
     * 查询最后一条开卡记录
     *
     * @param placeId
     * @param idNumber
     * @param operationType
     * @return
     */
    public Optional<LogOperation> findFirstByPlaceIdAndIdNumberAndDeletedAndOperationTypeOrderByIdDesc(String placeId,
                                                                                                       String idNumber,
                                                                                                       OperationType operationType) {
//        LocalDateTime startTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endTime = LocalDateTime.now();
        return logOperationRepository.findTop1ByPlaceIdAndIdNumberAndDeletedAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber, 0, operationType, startDateTime, endTime);
    }

    public Page<LogOperation> thirdQueryPage(ThirdQueryLogOperationListServiceImpl.QueryBO queryBO) {
        Specification<LogOperation> specification = this.thirdQueryParams(queryBO);

        return logOperationRepository.findAll(specification, queryBO.getPageable());
    }

    public Specification<LogOperation> thirdQueryParams(ThirdQueryLogOperationListServiceImpl.QueryBO queryBO) {

        return (root, query, cb) -> {
            Predicate andPredicate;

            List<Predicate> andPredicateList = new ArrayList<>();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (!StringUtils.isEmpty(queryBO.getIdNumber())) {// 会员ID
                andPredicateList.add(cb.equal(root.get("idNumber").as(String.class), queryBO.getIdNumber()));
            }

            if (!StringUtils.isEmpty(queryBO.getPlaceId())) {// 场所ID
                andPredicateList.add(cb.equal(root.get("placeId").as(String.class), queryBO.getPlaceId()));
            }

            if (!StringUtils.isEmpty(queryBO.getBeginTime())) {// 开始时间
                andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), queryBO.getBeginTime()));
            }

            if (!StringUtils.isEmpty(queryBO.getEndTime())) {// 结束时间
                andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), queryBO.getEndTime()));
            }

//            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
//            andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));

            List<ThirdQueryLogOperationListServiceImpl.QueryTypeObj> queryTypeObjList = queryBO.getQueryTypeObjList();

            if (CollectionUtils.isNotEmpty(queryTypeObjList)) {// 查询类型
                List<Predicate> orPredicateList1 = new ArrayList<>();

                // queryType 查询类型：0：全部，1：激活，2：上机，8：收银台充值/退费，16：第三方充值，32：第三方扣费。
                // queryType 不为0时，queryTypeObjList 只有一条；
                // queryType 为 0 时，包括下面的6种情况，queryTypeObjList有6条：
                // 1、激活记录，SourceType不限，{@link OperationType#ACTIVATE_CARD}。
                // 2、上机记录。SourceType不限，{@link OperationType#LOGIN}。
                // 8、收银机扣费就是减钱。冲正。SourceType限制为收银台 {@link SourceType#CASHIER}，{@link OperationType#TOPUP}，{@link OperationType#REVERSAL}。
                // 16、充值来自于第三方的258。SourceType限制为第三方账号，{@link OperationType#TOPUP}。
                // 32、扣款来自于第三方的259。SourceType限制为第三方账号，{@link OperationType#THIRD_DEDUCTION}。
                // sql 拼接形式为：
                // and (
                // (sourceType in (xxx1,xxx2) and operationType in (yyy1,yyy2))
                // or (sourceType in (xxx3,xxx4) and operationType in (yyy3,yyy4))
                // ...)
                // 等价于
                // and (
                //sourceType in (xxx1,xxx2) and operationType in (yyy1,yyy2)
                //or sourceType in (xxx3,xxx4) and operationType in (yyy3,yyy4)
                //...)
                // MySQL 中 and 优先级高于 or， a or (b and c) 等价于 a or b and c。

                for (ThirdQueryLogOperationListServiceImpl.QueryTypeObj queryTypeObj : queryTypeObjList) {
                    List<SourceType> sourceTypeList = queryTypeObj.getSourceTypeList();
                    List<OperationType> operationTypeList = queryTypeObj.getOperationTypeList();

                    List<Predicate> andPredicateList2 = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(sourceTypeList)) {
                        andPredicateList2.add(root.get("sourceType").as(SourceType.class).in(sourceTypeList));
                    }
                    if (CollectionUtils.isNotEmpty(operationTypeList)) {
                        andPredicateList2.add(root.get("operationType").as(OperationType.class).in(operationTypeList));
                    }

                    Predicate and = cb.and(andPredicateList2.toArray(new Predicate[andPredicateList2.size()]));
                    orPredicateList1.add(and);
                }

                andPredicateList.add(cb.or(orPredicateList1.toArray(new Predicate[orPredicateList1.size()])));
            }

            Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
            andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));

            return andPredicate;
        };
    }

    public Optional<LogOperation> findTheLatestLogout(String placeId, String idNumber, String loginId) {
//        LocalDateTime startTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        LocalDateTime startDateTime;
        LocalTime currentTime = LocalTime.now();//获取当前时间
        int hour = currentTime.getHour();//获取当前是一天的几点
        // 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
        if (hour > 18 && hour < 22) {
            startDateTime = DateTimeUtils.monthStartTime();//本月第一天
        } else {
            startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
        }
        LocalDateTime endTime = LocalDateTime.now();
        return logOperationRepository
                .findTop1ByPlaceIdAndIdNumberAndLoginIdAndDeletedAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(placeId, idNumber, loginId, 0, OperationType.LOGOUT, startDateTime, endTime);
    }
}
