package com.rzx.dim4.billing.service;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.billing.CancellationBO;
import com.rzx.dim4.base.bo.billing.PackageRefundBO;
import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.notify.polling.ActiveBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.CreateCardBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.TopupAndDeductionBusinessBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.dto.billiing.BillingCardUserDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.*;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.RegcardServerService;
import com.rzx.dim4.base.service.feign.iot.IotAuthConfigApi;
import com.rzx.dim4.base.service.feign.user.WechatMessageApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.RecardServiceUtil;
import com.rzx.dim4.base.utils.WechatAgeConfigUtils;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.invite.InviteConfig;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.BillingCardTypeRepository;
import com.rzx.dim4.billing.repository.LogRoomRepository;
import com.rzx.dim4.billing.repository.PlaceBizConfigRepository;
import com.rzx.dim4.billing.service.Invite.InviteConfigService;
import com.rzx.dim4.billing.service.Invite.InviteOnlineService;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import com.rzx.dim4.billing.service.algorithm.RegcardAlgorithm;
import com.rzx.dim4.billing.service.algorithm.UpDownUserLevelAlgorithm;
import com.rzx.dim4.billing.service.domain.PackageTimeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.rzx.dim4.base.cons.BaseConstants.*;
import static java.lang.Integer.parseInt;

/**
 * 当前类太大，继续新增方法时建议先在接口中定义!!!!!!!!!!
 */
@Slf4j
@Service
@Deprecated
public class BillingCardService implements IBillingCardService {

    @Autowired
    private BillingCardRepository billingCardRepository;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private TopupRuleService topupRuleService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private RewardPointsRuleService rewardPointsRuleService;

    @Autowired
    private LogPointsService logPointsService;

    @Autowired
    private PlaceChainStoresService placeChainStoresService;

    @Autowired
    private LogPlaceChainService logPlaceChainService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private TempRecordSurchargeService tempRecordSurchargeService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RegcardServerService regcardServerService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private IotAuthConfigApi iotAuthConfigApi;

    @Autowired
    private TempWanxiangUserService tempWanxiangUserService;

    @Autowired
    private PackageTimeDomainService packageTimeDomainService;

    @Autowired
    private RegcardAlgorithm regcardAlgorithm;

    @Autowired
    private PlaceBizConfigRepository placeBizConfigRepository;

    @Autowired
    private UpDownUserLevelAlgorithm upDownUserLevelAlgorithm;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private BillingCardTypeRepository billingCardTypeRepository;

    @Autowired
    private LogRoomRepository logRoomRepository;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    private LogRefundService logRefundService;

    @Autowired
    private BookSeatsService bookSeatsService;

    @Autowired
    private TempDuDuNiuUserService tempDuDuNiuUserService;


    @Autowired
    LogRecordRewardInstallmentService logRecordRewardInstallmentService;

    @Autowired
    private LogActivateService logActivateService;

    @Autowired
    private WechatMessageApi wechatMessageApi;

    @Autowired
    private InviteOnlineService inviteOnlineService;

    @Autowired
    BalanceDetailsAlgorithm balanceDetailsAlgorithm;

    @Autowired
    InviteConfigService inviteConfigService;

    //线上支付类型
    protected static final List<PayType> onlinePayType = new ArrayList<PayType>() {{
        add(PayType.WECHAT_PAY);
        add(PayType.WECHAT_MP);
        add(PayType.WECHAT_MINIAPP);
        add(PayType.WECHAT_APP);
        add(PayType.WECHAT_SCAN);
        add(PayType.ALIPAY_PAY);
        add(PayType.ALIPAY_MP);
        add(PayType.ALIPAY_MINIAPP);
        add(PayType.ALIPAY_APP);
        add(PayType.ALIPAY_SCAN);
        add(PayType.ALIAPY_IOT);
        add(PayType.THIRD_PAY);
        add(PayType.AGGREGATE_PAY);
        add(PayType.AGGREGATE_PAY_ALI);
        add(PayType.AGGREGATE_PAY_WECHAT);
    }};

    public BillingCard save(BillingCard card) {

        if (StringUtils.isEmpty(card.getCardId())) {
            card.setCardId(builderCardIdNew());
        }

        if (StringUtils.isEmpty(card.getChainCard())) {
            card.setChainCard(0);
        }

        if (StringUtils.isEmpty(card.getLgjCardFlag())) {
            card.setLgjCardFlag(0);
        }

        return billingCardRepository.save(card);
    }

    @Override
    public void saveAll(List<BillingCard> billingCards) {
        billingCardRepository.saveAll(billingCards);
    }

    /**
     * 2000条 一波
     *
     * @param billingCards
     */
    public void saveAllBySteps(List<BillingCard> billingCards) {
        if (billingCards.size() > 2000) {
            List<List<BillingCard>> lists = Lists.partition(billingCards, 2000);
            for (List<BillingCard> list : lists) {
                saveAll(list);
            }
        } else {
            saveAll(billingCards);
        }
    }

    public Optional<BillingCard> findByPlaceIdAndIdcardNum(String placeId, String idcardNum) {
        return billingCardRepository.findByPlaceIdAndIdcardNum(placeId, idcardNum);
    }

    public List<BillingCard> findByPlaceIdAndIdNumberLastSix(String placeId, String idNumber) {
        if (StringUtils.isEmpty(idNumber)) {
            return new ArrayList<>();
        }

        idNumber = "%" + idNumber.substring(idNumber.length() - 6);

        return billingCardRepository.findAllByPlaceIdAndDeletedAndIdNumberIsLike(placeId, 0, idNumber);
    }

    public Optional<BillingCard> findByPlaceIdAndLoginName(String placeId, String loginName) {
        return billingCardRepository.findByPlaceIdAndLoginNameAndDeleted(placeId, loginName, 0);
    }

    public Optional<BillingCard> findByPlaceIdAndCardId(String placeId, String cardId) {
        return billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
    }

    public List<BillingCard> findByPlaceIdInAndIdNumber(List<String> placeIds, String idNumber) {
        return billingCardRepository.findByPlaceIdInAndIdNumberAndDeleted(placeIds, idNumber, 0);
    }

    public List<BillingCard> findByChainIdAndIdNumber(String chainId, String idNumber) {
        return billingCardRepository.findByChainIdAndIdNumberAndDeleted(chainId, idNumber, 0);
    }

    public List<BillingCard> findByChainIdAndIdNumberByMember(String chainId, String idNumber) {
        List<String> cardTypeIds = Arrays.asList("1000", "1002");
        return billingCardRepository.findByChainIdAndIdNumberAndDeletedAndCardTypeIdNotIn(chainId, idNumber, 0, cardTypeIds);
    }

    public List<BillingCard> findByPlaceIdInAndIdNumberAndCardTypeIdIn(String chainId, String idNumber, List<String> cardTypeId) {
        return billingCardRepository.findByChainIdAndIdNumberAndCardTypeIdInAndDeleted(chainId, idNumber, cardTypeId, 0);
    }

    @Override
    public List<BillingCard> findByPlaceIdAndIdNumbers(String placeId, List<String> idNumbers) {
        return billingCardRepository.findByPlaceIdAndIdNumberInAndDeleted(placeId, idNumbers, 0);
    }

    public List<BillingCard> findByPlaceIdAndCardIds(String placeId, List<String> cardIds) {
        return billingCardRepository.findAllByPlaceIdAndCardIdIn(placeId, cardIds);
    }

    public List<BillingCard> findAllByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds) {
        return billingCardRepository.findAllByPlaceIdAndCardIdInAndDeleted(placeId, cardIds, 0);
    }

    public List<BillingCardUserDTO> findByIdNumbers(List<String> idNumbers) {
        return billingCardRepository.findByIdNumbers(idNumbers);
    }

    public List<BillingCard> findForCashierUserView(String placeId) {
        LocalDateTime dateTime = LocalDateTime.now().minusMinutes(30);
        return billingCardRepository.findForCashierUserView(placeId, dateTime);
    }

    public List<BillingCard> findByPlaceIdAndChainCard(String placeId, int chainCard) {
        return billingCardRepository.findByPlaceIdAndChainCardAndDeleted(placeId, chainCard, 0);
    }

    public Optional<BillingCard> findByPlaceIdAndIdNumber(String placeId, String idNumber) {
        return billingCardRepository.findByPlaceIdAndIdNumber(placeId, idNumber);
    }

    public Optional<BillingCard> findByChainIdAndIdNumberAndChainCard(String chainId, String idNumber, int chainCard) {
        List<String> cardTypeIds = Arrays.asList("1000", "1002");
        return billingCardRepository.findByChainIdAndIdNumberAndChainCardAndDeletedAndCardTypeIdNotIn(chainId, idNumber, chainCard, 0, cardTypeIds);
    }

    public Optional<BillingCard> findByPlaceIdAndIdNumberAndNotDeleted(String placeId, String idNumber, int deleted) {
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);

        if (optPlaceChainStores.isPresent()) { // 连锁网吧查询用户信息
            List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(optPlaceChainStores.get().getChainId());
            //List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList()); // 获取连锁场所ID列表

            List<BillingCard> billingCardList = findByChainIdAndIdNumberByMember(optPlaceChainStores.get().getChainId(), idNumber);
            if (CollectionUtils.isEmpty(billingCardList)) {
                return Optional.empty();
            }

            List<BillingCard> currCard = billingCardList.stream().filter(e -> e.getPlaceId().equals(placeId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currCard)) {
                return Optional.empty();
            }

            List<BillingCard> roamCard = billingCardList.stream().filter(e -> !e.getPlaceId().equals(placeId)).collect(Collectors.toList());

            int cashAccount = currCard.get(0).getCashAccount();
            int presentAccount = currCard.get(0).getPresentAccount();
            int memberPoint = currCard.get(0).getPoints();

            BillingCard billingCard = new BillingCard(); // 直接用等于拿出来原对象，set后会更改数据库的值
            BeanUtils.copyProperties(currCard.get(0), billingCard);

            for (BillingCard bc : roamCard) {
                for (PlaceChainStores pcs : placeChainStoresList) {
                    if (pcs.getPlaceId().equals(bc.getPlaceId())) {
                        if (pcs.getShareCashAccount() == 1) {
                            cashAccount = cashAccount + bc.getCashAccount();
                        }
                        if (pcs.getSharePresentAccount() == 1) {
                            presentAccount = presentAccount + bc.getPresentAccount();
                        }
                        if (pcs.getShareMemberPoint() == 1) {
                            memberPoint = memberPoint + bc.getPoints();
                        }
                    }
                }
            }
            billingCard.setCashAccount(cashAccount);
            billingCard.setPresentAccount(presentAccount);
            billingCard.setPoints(memberPoint);
            return Optional.of(billingCard);
        }

        // 非连锁网吧直接返回结果
        return billingCardRepository.findByPlaceIdAndIdNumberAndDeleted(placeId, idNumber, deleted);
    }

    @Override
    public Optional<BillingCard> findByPlaceIdAndIdNumberAndNotDeleted(String placeId, String idNumber) {
        return billingCardRepository.findByPlaceIdAndIdNumberAndDeleted(placeId, idNumber, 0);
    }

    public List<BillingCard> findByPlaceIdAndIdNameLike(String placeId, String idName) {
        return billingCardRepository.findByPlaceIdAndIdNameLike(placeId, idName + "%");
    }

    public List<BillingCard> findByPlaceIdInAndIdNameLike(List<String> placeIds, String idName) {
        return billingCardRepository.findByPlaceIdInAndIdNameLikeAndDeleted(placeIds, "%" + idName + "%", 0);
    }

    public Optional<BillingCard> findByPlaceIdInAndIdNumberLikeAndDeleted(String placeId, String idNumber) {
        return billingCardRepository.findByPlaceIdAndIdNumberLikeAndDeleted(placeId, "%" + idNumber + "%", 0);
    }

    public List<BillingCard> findByPlaceIdAndDeletedAndIdNumberLike(String placeId, String idNumber) {
        return billingCardRepository.findByPlaceIdAndDeletedAndIdNumberLike(placeId, 0, "%" + idNumber + "%");
    }

    public Optional<BillingCard> findByPlaceIdAndDeletedAndIdNumberForMiniApp(String placeId, String idNumber) {
        return billingCardRepository.findByPlaceIdAndIdNumber(placeId, idNumber);
    }

    public List<BillingCard> findByPlaceIdAndCardTypeIdAndDeletedOrderByIdDesc(String placeId, String cardTypeId,
                                                                               int deleted) {
        return billingCardRepository.findByPlaceIdAndCardTypeIdAndDeletedOrderByIdDesc(placeId, cardTypeId, deleted);
    }

    /**
     * 根据订单进行充值
     *
     * @param logTopup 充值日志
     */

    public synchronized boolean billingCardTopupByLogTopup(LogTopup logTopup) {
        log.info("billingCardTopupByLogTopup={}", new Gson().toJson(logTopup));

        // 接口重复调用校验
        String orderId = logTopup.getOrderId();
        String sskey = "BILLING_ORDER_CHONGZHI" + "_" + orderId;
        boolean lock = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(sskey, "1", 3000, TimeUnit.MILLISECONDS));
        // 存在返回 false，不存在返回 true
        if (!lock) {
            log.info("APP订单充值调用:::::::::::::::重复请求::orderId::::" + orderId);
            return false;
        }

        //获取充值时带过来的手机号
        String phoneNumber = stringRedisTemplate.opsForValue().get("ALIPAY_MINAPP_PHONE_" + logTopup.getIdNumber());

        if (logTopup.getStatus() == 2) {
            log.info("订单[orderId:{}]状态为[{}]，准备更新。", logTopup.getOrderId(), logTopup.getStatus());

            String placeId = logTopup.getPlaceId();
            String idNumber = logTopup.getIdNumber();
            String cardId = logTopup.getCardId();

            BillingCard billingCard;
            Optional<BillingCard> billingCardOptional = billingCardRepository.findByPlaceIdAndIdNumber(placeId, idNumber);
            if (StringUtils.isEmpty(cardId)) {
                // 生成计费卡，并带钱充值
                if (billingCardOptional.isPresent() && billingCardOptional.get().getDeleted() == 0) {
                    log.warn("订单[orderId:{}]状态为[{}]，更新失败。", logTopup.getOrderId(), logTopup.getStatus());
                    return false;
                }
                String activeType = String.valueOf(ActiveType.MINI_APP.getValue());
                if (logTopup.getSourceType().equals(SourceType.WECHAT)) {
                    activeType = String.valueOf(ActiveType.SWGJ_WECHAT.getValue());
                }

                BillingCardBO billingCardBO = new BillingCardBO();
                billingCardBO.setPlaceId(placeId);
                billingCardBO.setCardTypeId(logTopup.getCardTypeId());
                billingCardBO.setIdName(logTopup.getIdName());
                billingCardBO.setIdNumber(idNumber);
                billingCardBO.setActiveType(activeType);
                billingCardBO.setAddress("");
                billingCardBO.setLoginName(idNumber);
                billingCardBO.setNation("");
                billingCardBO.setIssuingAuthority("");
                billingCardBO.setRemark("");
                billingCardBO.setValidPeriod("-1");

                if (org.apache.commons.lang3.StringUtils.isNotBlank(phoneNumber)) {
                    billingCardBO.setPhoneNumber(phoneNumber);
                }

                autoCreateCardWhenTopup(logTopup, billingCardBO, "");
                return true;
            }
            //开卡
            if (billingCardOptional.isPresent() && billingCardOptional.get().getDeleted() == 1) {
                billingCard = billingCardOptional.get();
                //获取附加费标识
                String identification = stringRedisTemplate.opsForValue().get("[billing]_qrcode_createCard_" + placeId + "_" + billingCard.getCardId());

                log.info("充值时，有已删除卡，更新卡，logTopup={}, billingCardBO={}, identification={}", new Gson().toJson(logTopup), new Gson().toJson(billingCard), identification);

                SourceType sourceType = logTopup.getSourceType();

                int cashAmount = logTopup.getCashAmount();
                int presentAmount = logTopup.getPresentAmount();

                LocalDateTime activeTime = billingCard.getActiveTime();

                BillingCardBO billingCardBO = new BillingCardBO();
                billingCardBO.setPlaceId(placeId);
                billingCardBO.setCardTypeId(billingCard.getCardTypeId());
                billingCardBO.setIdNumber(idNumber);
                billingCardBO.setIdName(billingCard.getIdName());
                billingCardBO.setAddress(billingCard.getAddress());
                billingCardBO.setIssuingAuthority(billingCard.getIssuingAuthority());
                billingCardBO.setNation(billingCard.getNation());
                billingCardBO.setValidPeriod(billingCard.getValidPeriod());
                billingCardBO.setActiveType(String.valueOf(billingCard.getActiveType().getValue()));
                billingCardBO.setRemark(billingCard.getRemark());
                billingCardBO.setCashAccount(cashAmount);
                billingCardBO.setPresentAccount(presentAmount);
                billingCardBO.setActiveTime(activeTime);

                //如果手机号传了就更新，不然用之前的
                if (org.apache.commons.lang3.StringUtils.isNotBlank(phoneNumber)) {
                    billingCardBO.setPhoneNumber(phoneNumber);
                } else {
                    billingCardBO.setPhoneNumber(billingCard.getPhoneNumber());
                }

                autoCreateCardWhenTopup(logTopup, billingCardBO, identification);


                String cashierId = null;
                Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(logTopup.getPlaceId(),
                        logTopup.getShiftId(), 0);
                if (optLogShift.isPresent()) {
                    cashierId = optLogShift.get().getCashierId();
                }
                //写入轮询
                savePolling(cashierId, sourceType, billingCardBO, logTopup);

                logTopup.setStatus(3);
                logTopup.setUpdated(LocalDateTime.now());
                logTopupService.save(logTopup);
                log.info("订单[orderId:{}]状态为[{}]，更新成功。", logTopup.getOrderId(), logTopup.getStatus());
                return true;
            }

            Optional<BillingCard> optBillingCard = findByPlaceIdAndCardId(logTopup.getPlaceId(), logTopup.getCardId());
            billingCard = optBillingCard.get();

            //充值时更新旧手机号
            if (org.apache.commons.lang3.StringUtils.isNotBlank(phoneNumber) && !org.apache.commons.lang3.StringUtils.equals(billingCard.getPhoneNumber(), phoneNumber)) {
                billingCard.setPhoneNumber(phoneNumber);
                billingCardRepository.save(billingCard);
            }


            if ("1000".equals(logTopup.getCardTypeId()) && BillingCardService.onlinePayType.contains(logTopup.getPayType())) {
                //  如果是线上充值余额需要写入到线上金额
                billingCard = billingCardTopup(0, logTopup.getPresentAmount(), billingCard.getPlaceId(), billingCard.getCardId(), billingCard.getCardTypeId(), null, logTopup.getCashAmount(), logTopup.getSourceType());
            } else {
                // 更新计费卡余额
                billingCard = billingCardTopup(logTopup.getCashAmount(), logTopup.getPresentAmount(), billingCard.getPlaceId(), billingCard.getCardId(), billingCard.getCardTypeId(), null, 0, logTopup.getSourceType());
            }


            logTopup.setStatus(3);
            logTopup.setUpdated(LocalDateTime.now());
            logTopupService.save(logTopup);
            log.info("订单[orderId:{}]状态为[{}]，更新成功。", logTopup.getOrderId(), logTopup.getStatus());

            LogLogin logLogin = null;
            LogShift logShift = null;
            BillingOnline billingOnline = null;
            if (!StringUtils.isEmpty(logTopup.getLoginId())) {
                Optional<BillingOnline> optBillingOnline = billingOnlineService
                        .findUnfinishedByPlaceIdAndClientId(logTopup.getPlaceId(), logTopup.getClientId());
                if (optBillingOnline.isPresent()) {
                    billingOnline = optBillingOnline.get();
                    Optional<LogLogin> optLogLogin = logLoginService.findByPlaceIdAndLoginId(logTopup.getPlaceId(),
                            logTopup.getLoginId(), billingOnline.getBillingTime());
                    if (optLogLogin.isPresent()) {
                        logLogin = optLogLogin.get();
                    }
                }
            }
            if (!StringUtils.isEmpty(logTopup.getShiftId())) {
                Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(logTopup.getPlaceId(),
                        logTopup.getShiftId(), 0);
                if (optLogShift.isPresent()) {
                    logShift = optLogShift.get();
                }
            }

            //查询充值规则判断(并且写入)分期赠送
            LogRecordRewardInstallment logRecordRewardInstallment = logRecordRewardInstallmentService.addTopupRewardInstallment(logTopup);


            logOperationService.addTopupLogOperation(logTopup.getSourceType(),
                    logTopup.getPayType(),
                    logTopup.getCashAmount(),
                    logTopup.getPresentAmount(),
                    billingCard,
                    billingOnline,
                    logShift,
                    logLogin,
                    null, logRecordRewardInstallment.getSumPresentAmortized(), logRecordRewardInstallment.getSumPresentAmount());

            // 保存轮询数据
            if (null != logTopup.getClientId() || logTopup.getSourceType().equals(SourceType.CASHIER)) {
                GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(logTopup.getPlaceId(),
                        StringUtils.isEmpty(logTopup.getClientId()) ? "" : logTopup.getClientId(), billingCard.getIdNumber(), BusinessType.TOPUP);
                if (pollingBOGeneric.isResult()) {
                    PollingBO pollingBO = pollingBOGeneric.getData().getObj();

                    TopupAndDeductionBusinessBO businessTopupAndDeductionBO = new TopupAndDeductionBusinessBO();
                    if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                        businessTopupAndDeductionBO.setPlaceId(logTopup.getPlaceId());
                        businessTopupAndDeductionBO.setIdNumber(billingCard.getIdNumber());
                        businessTopupAndDeductionBO.setCashAmount(logTopup.getCashAmount());
                        businessTopupAndDeductionBO.setPresentAmount(logTopup.getPresentAmount());
                        businessTopupAndDeductionBO.setTotalAccount(billingCard.getTotalAccount());
                        businessTopupAndDeductionBO.setCreated(LocalDateTime.now().toString());
                        businessTopupAndDeductionBO.setSourceType(logTopup.getSourceType());
                        businessTopupAndDeductionBO.setBusinessType(BusinessType.TOPUP);
                        businessTopupAndDeductionBO.setBusinessId(pollingBO.getCashierBusinessId());
                        businessTopupAndDeductionBO.setClientId(logTopup.getClientId());
                        businessTopupAndDeductionBO.setType(1);
                        businessTopupAndDeductionBO.setOrderId(logTopup.getOrderId());
                        businessTopupAndDeductionBO.setPayType(logTopup.getPayType());
                        if (logTopup.getPayType().equals(PayType.AGGREGATE_PAY) ||
                                logTopup.getPayType().equals(PayType.AGGREGATE_PAY_ALI) ||
                                logTopup.getPayType().equals(PayType.AGGREGATE_PAY_WECHAT) ||
                                logTopup.getPayType().equals(PayType.WECHAT_SCAN) ||
                                logTopup.getPayType().equals(PayType.ALIPAY_SCAN)) {
                            businessTopupAndDeductionBO.setNoPopupIde("1");
//                            businessTopupAndDeductionBO.setCashierId(logShift.getCashierId());  //扫聚合码的时候才需要收银台id
                            businessTopupAndDeductionBO.setCashierId(StringUtils.isEmpty(logShift) ? "" : logShift.getCashierId());

                        }
                        // 保存收银台业务数据
                        notifyServerService.pushTopupAndDeductionBusinessData(businessTopupAndDeductionBO);

                        if (null != logTopup.getClientId()) {
                            // 客户端在线充值时，需要单独写一条，把换机通知给收银台，但是最终是要给第三方
                            businessTopupAndDeductionBO.setType(0); // 第三方时，给客户端
                            // 保存收银台业务数据
                            notifyServerService.pushTopupAndDeductionBusinessData(businessTopupAndDeductionBO);

                            log.info("订单[orderId:{}]保存轮询数据成功，TopupAndDeductionBusinessBO={}", logTopup.getOrderId(), new Gson().toJson(pollingBOGeneric));
                        }
                    }
                } else {
                    log.info("订单[orderId:{}]保存轮询数据失败，pollingBOGeneric={}", logTopup.getOrderId(), new Gson().toJson(pollingBOGeneric));
                }
            } else {
                log.info("订单[orderId:{}]没有clientId，不保存轮询数据。", logTopup.getOrderId());
            }
            return true;
        } else if (logTopup.getStatus() == 3) {
            log.info("订单[orderId:{}]状态为[{}]，已经修改，该通知丢弃。", logTopup.getOrderId(), logTopup.getStatus());
        } else if (logTopup.getStatus() == 1) {
            log.info("订单[orderId:{}]状态为[{}]，订单状态错误，请检查。", logTopup.getOrderId(), logTopup.getStatus());
        }
        return false;
    }

    /**
     * 充值时，没有计费卡，创建计费卡
     *
     * @param logTopup 充值记录
     */
    @Override
    public BillingCardBO autoCreateCardWhenTopup(LogTopup logTopup, BillingCardBO billingCardBO, String identification) {
        log.info("充值时，没有计费卡，创建计费卡，logTopup={}, billingCardBO={}, identification={}", new Gson().toJson(logTopup), new Gson().toJson(billingCardBO), identification);
        String cardTypeId = billingCardBO.getCardTypeId();
        String idNumber = billingCardBO.getIdNumber();
        String name = billingCardBO.getIdName();
        String phoneNumber = billingCardBO.getPhoneNumber();
        String address = billingCardBO.getAddress();
        String issuingAuthority = billingCardBO.getIssuingAuthority();
        String nation = billingCardBO.getNation();
        String validPeriod = billingCardBO.getValidPeriod();
        String activeType = billingCardBO.getActiveType();
        String remark = billingCardBO.getRemark();
        LocalDateTime activeTime = billingCardBO.getActiveTime();

        SourceType sourceType = logTopup.getSourceType();
        String placeId = logTopup.getPlaceId();
        String shiftId = logTopup.getShiftId();
        int cashAmount = logTopup.getCashAmount();
        int presentAmount = logTopup.getPresentAmount();

        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (!placeConfigResponse.isResult()) {
            throw new ServiceException(placeConfigResponse.getMessage());
        }
        PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();

        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        //查询充值规则判断是否为分期赠送
        LogRecordRewardInstallment logRecordRewardInstallment = logRecordRewardInstallmentService.addTopupRewardInstallment(logTopup);

        billingCardBO = commonCreateCard(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                cashAmount,
                logRecordRewardInstallment.getSumPresentAmount(),  //logTopup中记录的是分期后的金额，这里传入总赠送金额，开卡时会根据是否分期计算单期金额
                sourceType,
                activeTime,
                logTopup.getPayType(),
                logRecordRewardInstallment.getSumPresentAmortized());

        if (SourceType.WECHAT == sourceType || SourceType.MINIAPP == sourceType || SourceType.ALIPAY == sourceType) {
            savePolling("", sourceType, billingCardBO, null);
        }

        if (!StringUtils.isEmpty(billingCardBO.getCardId()) && StringUtils.isEmpty(logTopup.getCardId())) {
            logTopup.setCardId(billingCardBO.getCardId());
        }
        logTopup.setCardTypeName(billingCardBO.getCardTypeName());
        logTopup.setStatus(3);
        logTopup.setUpdated(LocalDateTime.now());
        logTopup.setTopupSourceType(2);// 开卡的充值来源类型定位2
        logTopupService.save(logTopup);
        log.info("订单[orderId:{}]状态为[{}]，更新成功。", logTopup.getOrderId(), logTopup.getStatus());

        /**充值自动开卡后计算积分赠送
         * date:2024.2.23
         * */
        // 校验班次
        Optional<LogShift> logShiftOpt = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!logShiftOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = logShiftOpt.get();

        int rewardPoints = placeConfigBO.getRewardPoints();
        // 开启积分赠送之前先判断卡类型，如果是临时卡还得
        // 开启积分赠送
        if (rewardPoints == 1) {
            // 获取对应的积分赠送值
            int points = rewardPointsRuleService.getRewardPointsNum(placeId, logTopup.getCashAmount(), ExchangePointsType.TOPUP);
            Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, billingCardBO.getCardId(), 0);
            if (billingCardOpt.isPresent()) {
                BillingCard billingCard = billingCardOpt.get();
                // 如果开卡的时候因为开启了自动升降级开关自动补足了最小积分，此时赠送的积分大于最小积分，应该将赠送的积分赋值给会员卡积分值
                if (billingCard.getPoints() < points) {
                    billingCard.setPoints(points);
                }
                billingCardRepository.save(billingCard);
                // 积分变更记录
                if (points > 0) {
                    logPointsService.addLogPointsOperation(ExchangePointsType.TOPUP, points, logTopup.getCashAmount(), billingCard, logShift, false);
                }
                // 积分赠送后，自动升降级用户等级校验
                upDownUserLevelAlgorithm.autoUpOrDownUserLevel(billingCard, logTopup.getCashAmount(), 0, 0, sourceType, logShift);
            }
        }
        return billingCardBO;
    }

    /**
     * 只能用来获取会员卡信息，不能用来更新会员卡信息！！！！！！！
     * 因为假如场所加入了连锁，则此处获得的卡信息，比如金额是聚合所有连锁场所的，而不是单个场所，如果保存至单个场所则金额错误
     *
     * @param placeId  场所id
     * @param idNumber 会员卡号
     * @return 会员卡信息
     * @apiNote 假如用户在当前场所有会员卡，且为临时卡/工作卡，则获取临时卡/工作卡；
     * 假如没有，则判断是否加入连锁，如果加入了连锁，则
     */
    @Override
    public BillingCardBO getOnlyReadOverSystem(String placeId, String idNumber) {
        return this.getOnlyReadOverSystem(placeId, idNumber, null);
    }

    /**
     * 只能用来获取会员卡信息，不能用来更新会员卡信息！！！！！！！
     * 因为假如场所加入了连锁，则此处获得的卡信息，比如金额是聚合所有连锁场所的，而不是单个场所，如果保存至单个场所则金额错误
     *
     * @param placeId  场所id
     * @param idNumber 会员卡号
     * @return 会员卡信息
     * @apiNote 假如用户在当前场所有会员卡，且为临时卡/工作卡，则获取临时卡/工作卡；
     * 假如没有，则判断是否加入连锁，如果加入了连锁，则
     */
    @Override
    public BillingCardBO getOnlyReadOverSystem(String placeId, String idNumber, String idName) {
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
        // 如果是特殊证件，不走万象和嘟嘟牛临时用户处理逻辑
        if (18 == idNumber.length()) {
            // 处理万象临时用户
            if (!optBillingCard.isPresent()) {
                Optional<TempWanxiangUser> optTempWanxiangUser = tempWanxiangUserService.findTop1ByPlaceId(placeId);
                if (optTempWanxiangUser.isPresent()) {   // 存在网吧万象用户，需要处理
                    if (StringUtils.isEmpty(idName)) {
                        throw new ServiceException(ServiceCodes.NULL_PARAM);
                    }
                    String cardIdShow = "*" + idNumber.substring(idNumber.length() - 6);
                    String nameShow = new StringBuilder(idName).replace(1, 2, "*").toString();
                    List<TempWanxiangUser> wanxiangUserList = tempWanxiangUserService.findByPlaceIdAndCardIdShowAndNameShow(placeId, cardIdShow, nameShow);
                    if (!CollectionUtils.isEmpty(wanxiangUserList)) { // 查询到多条用户，需要单独处理
                        throw new ServiceException(ServiceCodes.BILLING_WANXIANG_REPEAT_USER);
                    }
                } else {
                    // 再检查是否有嘟嘟牛用户
                    Optional<TempDuDuNiuUser> tempDuDuNiuUserOpt = tempDuDuNiuUserService.findTop1ByPlaceId(placeId);
                    if (tempDuDuNiuUserOpt.isPresent()) { // 存在网吧嘟嘟牛用户，需要处理
                        if (StringUtils.isEmpty(idName)) {
                            throw new ServiceException(ServiceCodes.NULL_PARAM);
                        }
                        String cardIdShow = idNumber.substring(0, 2) + "**************" + idNumber.substring(idNumber.length() - 2);
                        List<TempDuDuNiuUser> duDuNiuUsers = tempDuDuNiuUserService.findByPlaceIdAndCardIdShowAndNameShow(placeId, cardIdShow, idName);
                        if (!CollectionUtils.isEmpty(duDuNiuUsers)) { // 查询到多条用户，需要单独处理
                            throw new ServiceException(ServiceCodes.BILLING_DUDUNIU_REPEAT_USER);
                        }
                    }
                }
            }
        }


        /**
         * 已经注销的临时卡，也需要查询出结果，前端需要返回的卡信息。
         */
        if (optBillingCard.isPresent()) {
            BillingCard billingCard = optBillingCard.get();
            // 有卡
            if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
                if (!newCard.isPresent()) {
                    throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
                }
                billingCard = newCard.get();
            }
            BillingCardBO cardBO = billingCard.toBO();
            //查询最后上机时间
            Optional<LogLogin> findLogLoginResult = logLoginService.findFirstByPlaceIdAndCardIdAndLogoutTimeIsNotNullOrderByIdDesc(placeId, billingCard.getCardId());
            if (findLogLoginResult.isPresent()) {
                cardBO.setLastLogOutTime(findLogLoginResult.get().getLogoutTime());
            }
            //查询开卡人
            Optional<LogOperation> findCreateCardResult = logOperationService.findFirstByPlaceIdAndIdNumberAndDeletedAndOperationTypeOrderByIdDesc(placeId, idNumber, OperationType.CREATE_CARD);
            if (findCreateCardResult.isPresent()) {
                cardBO.setCreaterName(findCreateCardResult.get().getCreaterName());
            }
            return cardBO;
        }

        // 先查询是否是连锁网吧
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);
        if (optPlaceChainStores.isPresent()) { // 如果是连锁，查询开卡场所
            PlaceChainStores placeChainStores = optPlaceChainStores.get();
            // List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(optPlaceChainStores.get().getChainId());
            // List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList()); // 获取连锁场所ID列表

            // 查询动作时，一定可以找到一张已经在连锁网吧开卡记录
            Optional<BillingCard> billingCardOpt = billingCardService.findByChainIdAndIdNumberAndChainCard(placeChainStores.getChainId(), idNumber, 0);
            if (!billingCardOpt.isPresent()) {
                throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            BillingCard chainBillingCard = billingCardOpt.get();

            // 当前网吧没有卡信息，静默生成一条(此时有可能是已经删除的卡)
            BillingCard card = new BillingCard();
            BeanUtils.copyProperties(chainBillingCard, card);

            // 查询最新的卡类型
            Optional<BillingCardType> billingCardTypeOpt = billingCardTypeService.findByPlaceIdAndTypeName(placeId, card.getCardTypeName());
            if (billingCardTypeOpt.isPresent()) {
                BillingCardType billingCardType = billingCardTypeOpt.get();
                card.setCardTypeId(billingCardType.getCardTypeId());
            }

            Optional<BillingCard> cardOptional = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
            if (cardOptional.isPresent()) {
                BillingCard billingCard = cardOptional.get();
                if (billingCard.getDeleted() == 1) {
                    card.setId(billingCard.getId());
                    card.setCardId(billingCard.getCardId());
                    card.setDeleted(0);
                }
            } else {
                card.setId(null);
                card.setCardId(null);
            }
            card.setPlaceId(placeId);
            card.setCashAccount(0);
            card.setPresentAccount(0);
            card.setChainCard(1);
            card.setPoints(0);
            card.setCreated(LocalDateTime.now());
            billingCardService.save(card);

            // 查询所有卡信息
            Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
            if (!newCard.isPresent()) {
                throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            BillingCard billingCard = newCard.get();

            BillingCardBO cardBO = billingCard.toBO();
            //查询最后上机时间
            Optional<LogLogin> findLogLoginResult = logLoginService.findFirstByPlaceIdAndCardIdAndLogoutTimeIsNotNullOrderByIdDesc(placeId, billingCard.getCardId());
            if (findLogLoginResult.isPresent()) {
                cardBO.setLastLogOutTime(findLogLoginResult.get().getLogoutTime());
            }
            //查询开卡人
            Optional<LogOperation> findCreateCardResult = logOperationService.findFirstByPlaceIdAndIdNumberAndDeletedAndOperationTypeOrderByIdDesc(placeId, idNumber, OperationType.CREATE_CARD);
            if (findCreateCardResult.isPresent()) {
                cardBO.setCreaterName(findCreateCardResult.get().getCreaterName());
            }
            return cardBO;
        } else {
            // 不是连锁
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
    }

    /**
     * 收银台开临时卡（带包时）
     *
     * @param placeId          门店id
     * @param shiftId          班次id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param phoneNumber      手机号
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeType       激活类型
     * @param identification   证件类型
     * @param remark           备注
     * @param cashAmount       现金金额
     * @param presentAmount    赠送金额
     * @param amountFlag       是否带钱开卡
     * @param packageRuleId    包时规则id
     * @param packageType      包时类型 1:余额包时 2:现金包时，当前为固定值1
     * @return
     */
    @Override
    @Transactional
    public BillingCardBO cashierCreateTempCardWithPackageTime(String placeId,
                                                              String shiftId,
                                                              String cardTypeId,
                                                              String idNumber,
                                                              String name,
                                                              String phoneNumber,
                                                              String address,
                                                              String issuingAuthority,
                                                              String nation,
                                                              String validPeriod,
                                                              String activeType,
                                                              String identification,
                                                              String remark,
                                                              int cashAmount,
                                                              int presentAmount,
                                                              boolean amountFlag,
                                                              String packageRuleId,
                                                              int packageType) {
        // 当前只允许临时卡开卡同时包时
        checkTempCardType(placeId, cardTypeId);

        return cashierCreateCardWithPackageTime(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                cashAmount,
                0,
                true,
                packageRuleId,
                packageType);
    }

    @Override
    public CancellationBO doCancellationTempBillingCard(String placeId, String cardId, String shiftId, String refundType, SourceType sourceType) {
        // 查询计费卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        return doCancellationTempBillingCard(shiftId, refundType, sourceType, billingCard);
    }

    @Override
    public CancellationBO doCancellationTempBillingCard(String shiftId, String refundType, SourceType sourceType, BillingCard billingCard) {
        String placeId = billingCard.getPlaceId();
        refundType = "1";
        if (!billingCard.getCardTypeId().equals("1000")) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }

        // 是否正在上机
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
                billingCard.getCardId());
        if (optBillingOnline.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_IS_ONLINE);
        }

        // 查询班次信息
        LogShift logShift = null;
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            if (sourceType == SourceType.CASHIER) {
                throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
            }
        } else {
            logShift = optLogShift.get();
        }

        // 添加消费记录
        logOperationService.addCancellationLogOperation(sourceType, billingCard, logShift);

        // 注销
        CancellationBO cancellationBO = billingCardService.billingCardCancellationNew(billingCard);
        log.info("注销计费卡返回结果：{}", cancellationBO);

        // 写退款卡上余额记录
        logOperationService.addRefundOperationNew(sourceType, RefundType.CANCELLATION, billingCard, cancellationBO, logShift);

        Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findUnusedByPlaceIdAndCardId(placeId, billingCard.getCardId());
//        if (packageTimeReserveOpt.isPresent() && refundType == null) {
//            // 还有未使用的包时
//            throw new ServiceException(ServiceCodes.BILLING_EXIST_FUTURE_PACKAGE_RULE);
//        }

        int sumPackageCash = 0;//包时合计现金
        if (refundType != null && packageTimeReserveOpt.isPresent()) {
            //先根据场所ID和卡ID去预包时记录查询对应的未使用的记录
            PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get();//计费卡未使用的预包时记录

            Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, packageTimeReserve.getRuleId());
            if (!billingRulePackageTimeOpt.isPresent()) {
                // 还有未使用的包时
                log.info("存在未使用的包时，且包时规则未找到.......");
                throw new ServiceException(ServiceCodes.BILLING_EXIST_FUTURE_PACKAGE_RULE);
            }
            BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();

            // 退现金
            if (packageTimeReserve.getPrice() > 0 && packageTimeReserve.getCostTemporaryOnlineAccount() == 0) {
                //全部都是现金包时
                LogRefund logRefund = new LogRefund();
                logRefund.setPlaceId(placeId);
                logRefund.setCardId(billingCard.getCardId());
                logRefund.setIdNumber(billingCard.getIdNumber());
                logRefund.setIdName(billingCard.getIdName());
                logRefund.setCardTypeId(billingCard.getCardTypeId());
                logRefund.setCardTypeName(billingCard.getCardTypeName());
                logRefund.setCashRefund(packageTimeReserve.getPrice());
                logRefund.setRefundDesc("退款-取消包时现金退费，退款金额：" + Double.valueOf(packageTimeReserve.getPrice()) / 100 + "元");
                logRefund.setShiftId(shiftId);
                logRefund.setOperatorName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
                logRefund.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
                logRefund.setSourceType(sourceType);
                logRefund.setCreated(LocalDateTime.now());
                logRefund.setRefundType(3);
                logRefund.setStatus(1);
                logRefundService.save(logRefund);
                sumPackageCash = sumPackageCash + packageTimeReserve.getPrice();
                //计算退款金额
                cancellationBO.setRefundCashAmount((packageTimeReserve.getPrice() - packageTimeReserve.getCostTemporaryOnlineAccount()) + cancellationBO.getRefundCashAmount());
                cancellationBO.setRefundOnlineAmount(packageTimeReserve.getCostTemporaryOnlineAccount() + cancellationBO.getRefundOnlineAmount());
            } else if (packageTimeReserve.getCostTemporaryOnlineAccount() > 0 && packageTimeReserve.getCostTemporaryOnlineAccount() == packageTimeReserve.getPrice()) {
                //计算退款金额
                cancellationBO.setRefundOnlineAmount(packageTimeReserve.getCostTemporaryOnlineAccount() + cancellationBO.getRefundOnlineAmount());
            } else if (packageTimeReserve.getPrice() > 0 && packageTimeReserve.getCostTemporaryOnlineAccount() > 0
                    && packageTimeReserve.getPrice() > packageTimeReserve.getCostTemporaryOnlineAccount()) {
                //既有现金又使用了线上金额
                int cash = packageTimeReserve.getPrice() - packageTimeReserve.getCostTemporaryOnlineAccount(); //现金数
                LogRefund logRefund = new LogRefund();
                logRefund.setPlaceId(placeId);
                logRefund.setCardId(billingCard.getCardId());
                logRefund.setIdNumber(billingCard.getIdNumber());
                logRefund.setIdName(billingCard.getIdName());
                logRefund.setCardTypeId(billingCard.getCardTypeId());
                logRefund.setCardTypeName(billingCard.getCardTypeName());
                logRefund.setCashRefund(cash);
                logRefund.setRefundDesc("退款-取消包时现金退费，退款金额：" + Double.valueOf(cash) / 100 + "元");
                logRefund.setShiftId(shiftId);
                logRefund.setOperatorName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
                logRefund.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
                logRefund.setSourceType(sourceType);
                logRefund.setCreated(LocalDateTime.now());
                logRefund.setRefundType(3);
                logRefund.setStatus(1);
                logRefundService.save(logRefund);
                sumPackageCash = sumPackageCash + cash;
                //计算退款金额
                cancellationBO.setRefundCashAmount(cash + cancellationBO.getRefundCashAmount());
                cancellationBO.setRefundOnlineAmount(packageTimeReserve.getCostTemporaryOnlineAccount() + cancellationBO.getRefundOnlineAmount());
            }

            // 写取消包时记录
            int result = packageTimeReserveService.updateInvaildByPlaceIdAndCardId(placeId, billingCard.getCardId());
            if (result == 1) {
                // 未上机的预包时退费和更改预包时都处理后，写取消包时操作记录
                logOperationService.addCancelPackageTimeOperation(sourceType, billingCard, billingRulePackageTime.getPackageFlag(),
                        packageTimeReserve.getPackagePayFlag(), null, logShift, null, Integer.parseInt(refundType), 0);
            }
            PackageRefundBO packageRefundBO = new PackageRefundBO();
            packageRefundBO.setRefundCashAmount(packageTimeReserve.getPrice());
            packageRefundBO.setRefundOnlineAmount(packageTimeReserve.getCostTemporaryOnlineAccount());
            // 写退款记录
            logOperationService.addRefundOperationNew(sourceType, RefundType.CANCEL_PACKAGE_TIME, billingCard, packageRefundBO, logShift);
        }

        // 写退款记录
        if ((cancellationBO.getRefundCashAmount() - sumPackageCash) > 0) {
            LogRefund logRefund = new LogRefund();
            logRefund.setPlaceId(placeId);
            logRefund.setCardId(billingCard.getCardId());
            logRefund.setIdNumber(billingCard.getIdNumber());
            logRefund.setIdName(billingCard.getIdName());
            logRefund.setCardTypeId(billingCard.getCardTypeId());
            logRefund.setCardTypeName(billingCard.getCardTypeName());
            logRefund.setCashRefund(cancellationBO.getRefundCashAmount() - sumPackageCash);//需要减去包时已经退掉了的钱
            logRefund.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
            logRefund.setOperatorName(StringUtils.isEmpty(logShift) ? "" : logShift.getLoginAccountName());
            logRefund.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getLoginAccountId()));
            logRefund.setSourceType(sourceType);
            logRefund.setCreated(LocalDateTime.now());
            logRefund.setRefundType(2);
            logRefund.setStatus(1);
            logRefundService.save(logRefund);
        }

        if (cancellationBO.getRefundOnlineAmount() > 0) {
            logRefundService.addRefundTask(billingCard, cancellationBO.getRefundOnlineAmount(), sourceType, logShift);
        }

        cancellationBO.setRefundTotal(cancellationBO.getRefundCashAmount() + cancellationBO.getRefundOnlineAmount());
        return cancellationBO;
    }

    @Override
    public void doDeleteMembershipCard(String placeId, String idNumber, String shiftId, String remark, SourceType sourceType) {
        // 验证场所ID
        GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
        if (respProfile.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }

        // 查询卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
        if (!optBillingCard.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }

        BillingCard billingCard = optBillingCard.get();

        if ("1000".equals(billingCard.getCardTypeId())) {
            throw new ServiceException(ServiceCodes.BILLING_TEMP_CARD_DELETE_NOT_SUPPORT);
        }

        if (respProfile.getData().getObj().getIsChain() == 1 && !"1002".equals(billingCard.getCardTypeId())) {
            // 连锁，只能删除工作卡
            throw new ServiceException(ServiceCodes.BILLING_CHAIN_CARD_DELETE_NOT_SUPPORT);
        }

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_ONLINE_DELETE_NOT_SUPPORT);
        }

        bookSeatsService.noBookingSeats(placeId, idNumber);

        billingCard.setDeleted(1);
        billingCard.setActiveTime(null);
        billingCard.setUpdated(LocalDateTime.now());
        billingCard.setPresentAccount(0);
        billingCard.setCashAccount(0);
        billingCard.setPoints(0);
        billingCard.setTemporaryOnlineAccount(0);
        billingCardService.save(billingCard);

        // 查询班次信息
        LogShift logShift = null;
        if (!StringUtils.isEmpty(shiftId) && SourceType.CASHIER == sourceType) {
            Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
            if (!optLogShift.isPresent()) {
                throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
            }
            logShift = optLogShift.get();
        }

        // 操作记录
        logOperationService.addDeleteCardOperation(sourceType, billingCard, logShift, remark);

        // 清除附加费临时表记录
        Optional<TempRecordSurcharge> tempRecordSurchargeOpt =
                tempRecordSurchargeService.findByPlaceIdAndCardIdAndDeleted(placeId, billingCard.getCardId(), 0);
        if (tempRecordSurchargeOpt.isPresent()) {
            TempRecordSurcharge tempRecordSurcharge = tempRecordSurchargeOpt.get();
            tempRecordSurcharge.setUpdated(LocalDateTime.now());
            tempRecordSurcharge.setDeleted(1);
            tempRecordSurchargeService.save(tempRecordSurcharge);
        }

        Optional<PackageTimeReserve> packageTimeReserveOpt =
                packageTimeReserveService.findUnusedByPlaceIdAndCardId(placeId, billingCard.getCardId());
        if (packageTimeReserveOpt.isPresent()) {
            packageTimeReserveService.updateInvalidationByPlaceIdAndCardId(placeId, billingCard.getCardId());
        }
        //删除分期赠送奖励
        int i = logRecordRewardInstallmentService.deleteRewardInstallmentByPlaceIds(Arrays.asList(placeId), idNumber);
        log.info("连锁删除会员时删除分期赠送金额信息场所列表：{}，证件号：{}，删除数目：{}", Arrays.asList(placeId), idNumber, i);
    }

    private void checkTempCardType(String placeId, String cardTypeId) {
        Optional<BillingCardType> billingCardTypeOptional = billingCardTypeService.findByPlaceIdAndCardTypeId(placeId, cardTypeId);
        BillingCardType billingCardType = billingCardTypeOptional
                .orElseThrow(() -> new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND));
        if (!"1000".equals(billingCardType.getCardTypeId())) {
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
    }

    /**
     * @param placeId          门店id
     * @param shiftId          班次id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param phoneNumber      手机号
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeType       激活类型
     * @param identification   证件类型
     * @param remark           备注
     * @param cashAmount       现金金额
     * @param presentAmount    赠送金额
     * @param amountFlag       是否带钱开卡
     * @param packageRuleId    包时规则id
     * @param packageType      包时类型 1:余额包时 2:现金包时，当前为固定值1
     * @return
     */
//    @Transactional 如果 private 改为 public，需要添加事务
    private BillingCardBO cashierCreateCardWithPackageTime(String placeId,
                                                           String shiftId,
                                                           String cardTypeId,
                                                           String idNumber,
                                                           String name,
                                                           String phoneNumber,
                                                           String address,
                                                           String issuingAuthority,
                                                           String nation,
                                                           String validPeriod,
                                                           String activeType,
                                                           String identification,
                                                           String remark,
                                                           int cashAmount,
                                                           int presentAmount,
                                                           boolean amountFlag,
                                                           String packageRuleId,
                                                           int packageType) {

        // 开卡
        BillingCardBO billingCardBO = this.cashierCreateCard(placeId, shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                cashAmount,
                0,
                true
                , 0);

        // 带钱开卡包时，没有记log_topup
        if (packageType == 1) {
            LogShift logShift = getLogShift(placeId, shiftId, SourceType.CASHIER);
            BillingCard card = new BillingCard(billingCardBO);
            // 收银台带钱开临时卡包时，包时类型写3，作为开卡充值来源用。
            logTopupService.cashTopup(card,
                    logShift,
                    "",
                    "",
                    cashAmount,
                    "3");
        }

        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 包时
        packageTimeDomainService.cashierCreate(billingCardBO, shiftId, packageRuleId, packageType);

        return billingCardBO;
    }

    /**
     * 计费卡 - 冲正
     *
     * @param amount      冲正金额
     * @param present     冲正赠送金额
     * @param billingCard 计费卡信息
     * @return
     */
    public BillingCard billingCardReversal(int amount, int present, BillingCard billingCard) {
        if (amount > billingCard.getCashAccount()) {
            amount = billingCard.getCashAccount();
        }
        if (present > billingCard.getPresentAccount()) {
            amount = billingCard.getPresentAccount();
        }
        billingCard.setCashAccount(billingCard.getCashAccount() - amount);
        billingCard.setPresentAccount(billingCard.getPresentAccount() - present);
        billingCard.setUpdated(LocalDateTime.now());
        billingCardRepository.save(billingCard);
        return billingCard;
    }

    /**
     * 计费卡 - 冲正积分
     *
     * @param points      冲正积分
     * @param billingCard 计费卡信息
     * @return
     */
    public BillingCard billingCardReversalPoints(int points, BillingCard billingCard) {
        if (points > billingCard.getPoints()) {
            points = billingCard.getPoints();
        }
        billingCard.setPoints(billingCard.getPoints() - points);
        billingCard.setUpdated(LocalDateTime.now());
        billingCardRepository.save(billingCard);
        return billingCard;
    }


    /**
     * 计费卡 - 充值(加钱)
     *
     * @param amount        充值金额
     * @param presentAmount 赠送金额
     * @param placeId       计费卡信息
     * @param cardId        班次信息
     * @param sourceType    来源
     * @return
     */
    public synchronized BillingCard billingCardTopup(int amount, int presentAmount, String placeId, String cardId, String cardTypeId, LogShift logShift, int temporaryOnlineAccount, SourceType sourceType) {
        log.info("计费卡充值，placeId:{},cardId:{},amount:{},presentAmount:{}", placeId, cardId, amount, presentAmount);

        // 积分赠送
        // 查询场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (placeConfigResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
            int rewardPoints = placeConfigBO.getRewardPoints();
            // 开启积分赠送之前先判断卡类型，如果是临时卡还得
            // 开启积分赠送
            if (rewardPoints == 1) {
                // 获取对应的积分赠送值
                int points = rewardPointsRuleService.getRewardPointsNum(placeId, amount + temporaryOnlineAccount, ExchangePointsType.TOPUP);
                Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
                if (billingCardOpt.isPresent()) {
                    BillingCard billingCard = billingCardOpt.get();
//                    billingCard.setPoints(billingCard.getPoints() + points);
//                    billingCardRepository.save(billingCard);
                    billingCardDeductionService.updateBillingCardPoints(placeId, cardId, points);
                    // 积分变更记录
                    if (points > 0) {
                        BillingCard card = new BillingCard();
                        card.setPoints(billingCard.getPoints() + points);
                        card.setPlaceId(placeId);
                        card.setCardId(cardId);
                        card.setIdNumber(billingCard.getIdNumber());
                        card.setIdName(billingCard.getIdName());
                        card.setCardTypeId(billingCard.getCardTypeId());
                        card.setCardTypeName(billingCard.getCardTypeName());
                        logPointsService.addLogPointsOperation(ExchangePointsType.TOPUP, points, amount + temporaryOnlineAccount, card, logShift, false);
                    }
                    // 积分赠送后，自动升降级用户等级校验
                    upDownUserLevelAlgorithm.autoUpOrDownUserLevel(billingCard, amount + temporaryOnlineAccount, 0, 0, sourceType, logShift);
                }
                //如果开启了积分赠送,开始是临时卡并且有在线充值金额，需要判断赠送后会员卡类型是否发生了变化，如果发生了变化金额不能写入到在线余额字段
                if ("1000".equals(cardTypeId) && 0 < temporaryOnlineAccount) {
                    Optional<BillingCard> byPlaceIdAndCardIdAndDeleted = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
                    if (byPlaceIdAndCardIdAndDeleted.isPresent() && !"1000".equals(byPlaceIdAndCardIdAndDeleted.get().getCardTypeId())) {
                        return billingCardDeductionService.updateBillingCardAccount(placeId, cardId, -amount, -(presentAmount + temporaryOnlineAccount), 0);
                    }
                }
            }
        }

        return billingCardDeductionService.updateBillingCardAccount(placeId, cardId, -amount, -presentAmount, -temporaryOnlineAccount);
    }

    /**
     * 计费卡结账，只能用于更改 active_time,deleted
     *
     * @param placeId
     * @param cardId
     * @param deleted
     */
    public synchronized void billingCardLogout(String placeId, String cardId, int deleted) {
        billingCardRepository.billingCardLogout(placeId, cardId, deleted);
    }

    /**
     * 连锁网吧扣费
     *
     * @return
     */
    @Override
    public synchronized BillingCard billingCardDeduction(List<PlaceChainBillingCardCostDetail> costDetails, String currPlaceId) {
        //TODO
        if (CollectionUtils.isEmpty(costDetails)) {
            return null;
        }
        boolean checkError = false;
        BillingCard returnBillingCard = null;
        for (int i = 0; i < costDetails.size(); i++) {
            // 查询出实时的余额，再做判断
            PlaceChainBillingCardCostDetail detail = costDetails.get(i);
            Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(detail.getPlaceId(), detail.getCardId(), 0);
            if (!billingCardOpt.isPresent()) {
                checkError = true;
                break;
            }
            BillingCard billingCard = billingCardOpt.get();
            if (currPlaceId.equals(billingCard.getPlaceId())) {
                returnBillingCard = billingCard;
            }
            if (billingCard.getCashAccount() < detail.getCostCash() || billingCard.getPresentAccount() < detail.getCostPresent() || billingCard.getTemporaryOnlineAccount() < detail.getCostTemporaryOnlineAccount()) {
                log.info("退款失败 (BillingCard: cashAccount={} => Request: cashAmount={})", billingCard.getCashAccount(), detail.getCostCash());
                log.info("退款失败 (BillingCard: presentAccount={}, => Request: presentAmount={})", billingCard.getPresentAccount(), detail.getCostPresent());
                log.info("退款失败 (BillingCard: temporaryOnlineAccount={} => Request: temporaryOnlineAccount={})", billingCard.getTemporaryOnlineAccount(), detail.getCostTemporaryOnlineAccount());

                checkError = true;
                break;
            }
        }
        if (checkError) {
            return null;
        }
        String uuid = Dim4StringUtils.getUUIDWithoutHyphen();
        LocalDateTime now = LocalDateTime.now();
        // 临时卡，在线账户部分金额，需要合计，然后去找在线充值订单进行退款
        for (PlaceChainBillingCardCostDetail detail : costDetails) {
            returnBillingCard = billingCardDeductionService.updateBillingCardAccount(detail.getPlaceId(),
                    detail.getCardId(),
                    detail.getCostCash(),
                    detail.getCostPresent(),
                    detail.getCostTemporaryOnlineAccount());
            if (!StringUtils.isEmpty(detail.getChainId())) { // 连锁扣费记录扣费日志
                LogPlaceChain logPlaceChain = new LogPlaceChain();
                logPlaceChain.setCardId(detail.getCardId());
                logPlaceChain.setCostPlaceId(detail.getPlaceId());
                logPlaceChain.setChainId(detail.getChainId());
                logPlaceChain.setCurrPlaceId(currPlaceId);
                logPlaceChain.setCostUid(uuid);
                logPlaceChain.setCreated(now);
                logPlaceChain.setCostCashAccount(detail.getCostCash());
                logPlaceChain.setCostPresentAccount(detail.getCostPresent());
                logPlaceChain.setCostTemporaryOnlineAccount(detail.getCostTemporaryOnlineAccount());
                logPlaceChain.setLoginId(detail.getLoginId());
                logPlaceChainService.save(logPlaceChain);
            }
            // 只有当前包时的场所Id和连锁门店列表中的场所Id相等时，赋值给要返回的卡
//            if (currPlaceId.equals(detail.getPlaceId())) {
//                 returnBillingCard = returnBillingCardNew;
//            }
        }
        return returnBillingCard;
    }

    /**
     * 计费卡 - 结账
     *
     * @param billingCard      计费卡信息
     * @param extraDeduction   额外扣除
     * @param alreadyDeduction 已消费金额
     * @param logShift         班次信息
     * @return 会员卡信息
     */
    public List<PlaceChainBillingCardCostDetail> billingCardLogout(BillingCard billingCard, int extraDeduction, int alreadyDeduction, LogShift logShift, String loginId, SourceType sourceType, int deductionOrder) {
        int deduction; // 扣除的金额

        if (extraDeduction > 0) { // 如果有额外的扣除就不过扣除零钱
            deduction = extraDeduction;
        } else { // 不扣除额外金额才计算小数
//            deduction = BalanceAlgorithm.additionalDeduct(billingCard.getTotalAccount(), 50);// 清理零钱
            deduction = 0;// 清理零钱
        }
        int deductionAtOnline = 0;

        // 临时卡结账，同时不是全场结账
        boolean selfLogout = SourceType.CLIENT.equals(sourceType) || SourceType.WECHAT.equals(sourceType) || SourceType.MINIAPP.equals(sourceType);

        boolean checkTempAmountForSelfFlag = "1000".equals(billingCard.getCardTypeId()) && selfLogout;
        log.info("selfLogout={}, checkTempAmountForSelfFlag={}", selfLogout, checkTempAmountForSelfFlag);

        // 如果是临时卡结账，并且不是包间副卡上机
        if (checkTempAmountForSelfFlag) {
            // 临时卡结账的时候跳过包间副卡
            LogRoom logRoom = null;
            // 查询是否在包间上机
            Optional<LogRoom> logRoomOpt = logRoomRepository.findByPlaceIdAndCardIdAndFinished(billingCard.getPlaceId(), billingCard.getCardId(), 0);
            if (logRoomOpt.isPresent()) {
                // 是在包间上机
                logRoom = logRoomOpt.get();
            }
            if (logRoom == null || (logRoom != null && logRoom.getIsMaster() == 1)) {
                if (deduction < billingCard.getCashAccount()) {
                    log.info("临时卡消费小于现金余额，还会剩现金，不让自助结账");
                    throw new ServiceException(ServiceCodes.BILLING_TEMP_CARD_NOT_ALLOW_SELF_CHECKOUT_BY_CASH);
                } else {
                    // 先扣现金部分，扣完了后还有剩余部分，扣在线账户里面的钱，如果在线账户里面还有剩余需要在线退款
                    deductionAtOnline = deduction - billingCard.getCashAccount();
                    if (deductionAtOnline > billingCard.getTemporaryOnlineAccount()) {
                        log.warn("扣除现金消费的，剩余部分还大于在线账户余额，这里金额错误了，无法退款");
                        throw new ServiceException(ServiceCodes.BILLING_TEMP_CARD_NOT_ALLOW_SELF_CHECKOUT_BY_CASH);
                    }
                }
            }
        }

        // 扣费
        if (deduction > 0) {
            // 消费少于最低扣费,需扣除差值
            List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, deduction, loginId, 1, deductionOrder);
            billingCardDeduction(costDetails, billingCard.getPlaceId());
            return costDetails;
        }

        // 查询当前门店的最新值
        Optional<BillingCard> nowCard = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(billingCard.getPlaceId(), billingCard.getCardId(), 0);
        if (!nowCard.isPresent()) {
            return null;
        }
        billingCard = nowCard.get();

        billingCard.setUpdated(LocalDateTime.now());
        updateActiveTime(billingCard);

        billingCardRepository.save(billingCard);

        // 积分赠送
//        updateRewardPoints(billingCard, alreadyDeduction, logShift, deduction, sourceType);

        return null;
    }

    /**
     * 被邀请人结账
     *
     * @param billingCard
     * @param extraDeduction
     * @param loginId
     * @param deductionOrder
     * @param inviteCode
     * @return
     */
    public List<PlaceChainBillingCardCostDetail> inviteCardLogout(BillingCard billingCard, int extraDeduction, String loginId, int deductionOrder, String inviteCode) {
        int deduction; // 扣除的金额
        if (extraDeduction > 0) { // 如果有额外的扣除就不过扣除零钱
            deduction = extraDeduction;
        } else { // 不扣除额外金额才计算小数
            deduction = 0;// 清理零钱
        }

        // 扣费
        if (deduction > 0) {
            BillingCard inviteCard = inviteOnlineService.getInviteCard(billingCard.getPlaceId(), inviteCode);
            if (inviteCard == null) {
                return null;
            }
            // 获取配置
            int supportPresentSwitch = 0;
            Optional<InviteConfig> inviteConfigOpt = inviteConfigService.findByPlaceId(billingCard.getPlaceId());
            if (inviteConfigOpt.isPresent()) {
                supportPresentSwitch = inviteConfigOpt.get().getSupportPresentSwitch();
            }
            // 消费少于最低扣费,需扣除差值
            List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(inviteCard, deduction, loginId, 1, supportPresentSwitch == 1 ? 2 : deductionOrder);
            billingCardDeduction(costDetails, inviteCard.getPlaceId());
            int costCash = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum);
            int costPresent = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum);
            balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(), inviteCard, "", 0, 0, costCash, costPresent, "被邀请人结账消费少于最低消费扣除", 0, 0, inviteCode);
            return costDetails;
        }

        billingCard.setUpdated(LocalDateTime.now());
        updateActiveTime(billingCard);
        billingCardRepository.save(billingCard);
        return null;
    }

    private void updateActiveTime(BillingCard billingCard) {
        if (billingCard.getIdNumber().length() != 18) { //特殊证件，判断是不是酒店或者pms，如果是则特殊处理
            GenericResponse<ObjDTO<PlaceProfileBO>> byPlaceId = placeServerService.findByPlaceId(billingCard.getPlaceId());
            if (!byPlaceId.isResult() || (byPlaceId.getData().getObj().getType() != 1 && byPlaceId.getData().getObj().getType() != 7)) {
                billingCard.setActiveTime(null);
            }
        } else {
            billingCard.setActiveTime(null);
        }
    }

    /**
     * 积分赠送
     *
     * @param billingCard      计费卡
     * @param alreadyDeduction 已消费金额
     * @param logShift         班次
     * @param deduction        扣除的金额
     */
    public void updateRewardPoints(BillingCard billingCard, int alreadyDeduction, LogShift logShift, int deduction, SourceType sourceType) {
        Optional<PlaceBizConfig> optPlaceBizConfig = placeBizConfigRepository.findByPlaceId(billingCard.getPlaceId());
        int tempCardPointsUpgrade = 0;
        if (optPlaceBizConfig.isPresent() && optPlaceBizConfig.get().getTempCardPointsUpgrade() == 1) {
            // 场所业务配置开启了临时卡参与自动升级
            tempCardPointsUpgrade = 1;
        }
        // 临时卡如果开启了“临时卡参与会员升降等级”，则也可赠送积分
        if (!"1000".equals(billingCard.getCardTypeId()) || tempCardPointsUpgrade == 1) {
            // 先查询场所业务配置，临时卡是否参与积分升降
            // 查询场所配置信息
            GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(billingCard.getPlaceId());
            if (placeConfigResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
                int rewardPoints = placeConfigBO.getRewardPoints();
                // 开启积分赠送
                BillingCard card = billingCard;
                if (rewardPoints == 1) {
                    // 获取对应的积分赠送值
                    int sumDeduction = alreadyDeduction + deduction;
                    log.info("::::::::结账积分赠送:::sumDeduction::" + sumDeduction);
                    int points = rewardPointsRuleService.getRewardPointsNum(billingCard.getPlaceId(), sumDeduction, ExchangePointsType.LOGOUT);
                    log.info("::::::::结账积分赠送:::points::" + points);
                    // billingCard.setPoints(billingCard.getPoints() + points);
                    billingCardRepository.updateBillingCardPoints(billingCard.getPlaceId(), billingCard.getCardId(), points);
                    Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(billingCard.getPlaceId(), billingCard.getCardId(), 0);
                    card = billingCardOpt.orElse(billingCard);
                    // 积分变更记录
                    if (points > 0) {
                        logPointsService.addLogPointsOperation(ExchangePointsType.LOGOUT, points, sumDeduction, card, logShift, false);
                    }
                }
                // 会员卡根据积分自动升降用户等级
                upDownUserLevelAlgorithm.autoUpOrDownUserLevel(card, (deduction + alreadyDeduction), 1, 0, sourceType, logShift);

            }
        }
    }

    /**
     * 计费卡 - 注销(临时卡)
     *
     * @param billingCard 计费卡信息
     * @return 现金退款金额
     */
    public int billingCardCancellation(BillingCard billingCard) {
        if (!"1000".equals(billingCard.getCardTypeId())) {
            return 0; // 只有临时卡才可以注销
        }
        int refundAmount = billingCard.getCashAccount();
        billingCard.setCashAccount(0);
        billingCard.setPresentAccount(0);
        billingCard.setUpdated(LocalDateTime.now());
        billingCard.setActiveTime(null);
        billingCard.setDeleted(1);
        billingCardRepository.save(billingCard);
        return refundAmount;
    }

    /**
     * 注销临时卡
     *
     * @param billingCard
     * @return
     */
    public CancellationBO billingCardCancellationNew(BillingCard billingCard) {
        CancellationBO cancellationBO = new CancellationBO();
        if (!"1000".equals(billingCard.getCardTypeId())) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_SUPPORT);
        }
        log.info("会员卡：{},需退款线上余额：{},需退款现金余额：{}", billingCard.getCardId(), billingCard.getTemporaryOnlineAccount(), billingCard.getCashAccount());
        cancellationBO.setCardId(billingCard.getCardId());
        cancellationBO.setIdNumber(billingCard.getIdNumber());
        cancellationBO.setPlaceId(billingCard.getPlaceId());
        cancellationBO.setRefundTotal(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount());
        cancellationBO.setRefundCashAmount(billingCard.getCashAccount());
        cancellationBO.setRefundOnlineAmount(billingCard.getTemporaryOnlineAccount());
        cancellationBO.setCardTypeId(billingCard.getCardTypeId());

        billingCard.setCashAccount(0);
        billingCard.setPresentAccount(0);
        billingCard.setTemporaryOnlineAccount(0);
        billingCard.setUpdated(LocalDateTime.now());
        billingCard.setActiveTime(null);
        billingCard.setDeleted(1);
        billingCard.setPoints(0);
//        billingCardRepository.save(billingCard);
        billingCardRepository.saveAndFlush(billingCard);

        return cancellationBO;
    }

    /**
     * 批量开卡
     *
     * @param billingCardBOList
     * @return
     */
    public GenericResponse<ObjDTO<BillingCardBO>> batchCreateBillingCard(List<BillingCardBO> billingCardBOList) {
        if (CollectionUtils.isEmpty(billingCardBOList)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        List<BillingCard> billingCardList = new ArrayList<>();
//        Long cardId = Long.parseLong(builderCardId()) + 100; // 在最新的cardId基础上加100，避免在导入时网吧新开卡产生的cardId冲突
        for (BillingCardBO o : billingCardBOList) {
            BillingCard card = new BillingCard();
            if (!StringUtils.isEmpty(o.getCardId()) && !StringUtils.isEmpty(o.getId())) {
                // 批量更新
                BeanUtils.copyProperties(o, card);
                billingCardList.add(card);
                continue;
            }
            card.setPlaceId(o.getPlaceId());
            card.setCardTypeId(o.getCardTypeId());
            card.setCashAccount(o.getCashAccount());
            card.setPresentAccount(o.getPresentAccount());
            card.setIdName(o.getIdName());
            card.setLoginName(o.getLoginName());
            card.setLoginPass(o.getLoginPass());
            card.setIdNumber(o.getIdNumber().toUpperCase());
            card.setCardId(builderCardIdNew());
            card.setCreated(LocalDateTime.now());
            card.setCreater(o.getCreater());
            card.setCardTypeName(o.getCardTypeName());
            card.setPhoneNumber(o.getPhoneNumber());
            card.setNation(o.getNation());
            card.setAddress(o.getAddress());
            billingCardList.add(card);
//            cardId++;
        }
        billingCardRepository.saveAll(billingCardList);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 分布式锁key
     */
    String BUILD_CARD_ID_LOCK_KEY = "billing:buildCardIdLock";

    /**
     * 构建卡id 的 key
     */
    String BUILD_CARD_ID = "billing:buildCardId";

    public String builderCardIdNew() {
        Boolean b = stringRedisTemplate.hasKey(BUILD_CARD_ID);
        if (b) {
            Long increment = stringRedisTemplate.opsForValue().increment(BUILD_CARD_ID, 1);
            log.info("builderCardIdNew()，获取成功，卡id为:" + increment);

            return String.valueOf(increment);
        } else {
            // 不存在，则进行初始化
            // 先上锁
            boolean locked = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(BUILD_CARD_ID_LOCK_KEY, "locked"));
            if (locked) {
                log.info("builderCardIdNew()，获取成功，设置1分钟TTL");
                stringRedisTemplate.expire(BUILD_CARD_ID_LOCK_KEY, 1, TimeUnit.MINUTES);

                int cardId = 1000000000;
                Optional<BillingCard> lastCard = billingCardRepository.findTop1ByOrderByIdDesc();
                if (lastCard.isPresent()) {
                    cardId = parseInt(lastCard.get().getCardId()) + 1;
                }
                log.info("builderCardIdNew()，最新的卡id为:" + cardId);
                stringRedisTemplate.opsForValue().set(BUILD_CARD_ID, String.valueOf(cardId));

                // 释放锁
                stringRedisTemplate.delete(BUILD_CARD_ID_LOCK_KEY);
                return String.valueOf(cardId);
            } else {
                log.info("builderCardIdNew()，获取失败，重新执行");
                return builderCardIdNew();
            }
        }
    }

    /**
     * 查询该身份证在所有网吧的计费卡余额
     *
     * @param idNumber idNumber
     * @return List<BillingCard>
     */
    public List<BillingCard> findByIdNumber(String idNumber) {
        List<BillingCard> billingCardList = billingCardRepository.findByIdNumberAndDeleted(idNumber, 0);
        return billingCardList;
    }

    public List<BillingCard> findYLChainAll(String chainId, String chainCardTypeId, String idNumber, String placeId, String idName, int start, int end) {
        return billingCardRepository.findByChainCardMemberAndDeleted(chainId, chainCardTypeId, idNumber, placeId, idName, start, end);
    }

    public List<BillingCard> findYLChainAll2(String chainId, String chainCardTypeId, String idNumber, String placeId, String idName, int start, int end) {
        return billingCardRepository.findByChainCardMemberAndDeleted2(chainId, chainCardTypeId, idNumber, placeId, idName, start, end);
    }

    /**
     * 分页查询
     *
     * @param param
     * @param pageable
     * @return
     */
    public Page<BillingCard> findAll(Map<String, Object> param, Pageable pageable) {
        log.info("BillingCardService.findAll param:{}", new Gson().toJson(param));

        return billingCardRepository.findAll(new Specification<BillingCard>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<BillingCard> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicates = new ArrayList<Predicate>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = cb.equal(root.get("deleted"), 0);
                predicates.add(p1);

                // 场所ID
                if (param.containsKey("placeId") && !StringUtils.isEmpty(param.get("placeId"))) {
                    Predicate predicate = cb.equal(root.get("placeId").as(String.class), param.get("placeId"));
                    predicates.add(predicate);
                }

                // 连锁ID
                if (param.containsKey("chainId") && !StringUtils.isEmpty(param.get("chainId"))) {
                    Predicate predicate = cb.equal(root.get("chainId").as(String.class), param.get("chainId"));
                    predicates.add(predicate);
                }

                // 连锁卡类型ID
                if (param.containsKey("chainCardTypeId") && !StringUtils.isEmpty(param.get("chainCardTypeId"))) {
                    Predicate predicate = cb.equal(root.get("chainCardTypeId").as(String.class), param.get("chainCardTypeId"));
                    predicates.add(predicate);
                }

                if (param.containsKey("activeTimeIsNotNull") && !StringUtils.isEmpty(param.get("activeTimeIsNotNull"))
                        && Boolean.parseBoolean(String.valueOf(param.get("activeTimeIsNotNull")))) {
                    Predicate predicate = cb.isNotNull(root.get("activeTime"));
                    predicates.add(predicate);
                }

                // 身份证号
                if (param.containsKey("idNumber") && !StringUtils.isEmpty(param.get("idNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), param.get("idNumber") + "%");
                    if ((param.get("idNumber") + "").length() == 18) {
                        predicate = cb.equal(root.get("idNumber"), param.get("idNumber"));
                    }
                    predicates.add(predicate);
                }
                // 身份证号(收银台查询)
                if (param.containsKey("cashierIdNumber") && !StringUtils.isEmpty(param.get("cashierIdNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), "%" + param.get("cashierIdNumber") + "%");
                    if ((param.get("cashierIdNumber") + "").length() == 18) {
                        predicate = cb.equal(root.get("idNumber"), param.get("cashierIdNumber"));
                    }
                    predicates.add(predicate);
                }

                // 卡类型
                if (param.containsKey("cardTypeId") && !StringUtils.isEmpty(param.get("cardTypeId"))) {
                    Predicate predicate = cb.equal(root.get("cardTypeId").as(String.class), param.get("cardTypeId"));
                    predicates.add(predicate);
                    param.remove("notTemporaryCard");//指定了 equal 就不需要再指定 notEqual 了
                }

                // 是否连锁开卡
                if (param.containsKey("chainCard") && !StringUtils.isEmpty(param.get("chainCard"))) {
                    Predicate predicate = cb.equal(root.get("chainCard"), param.get("chainCard"));
                    predicates.add(predicate);
                }

                // 卡类型（除临时卡以外的所有卡类型）
                if (param.containsKey("notTemporaryCard") && !StringUtils.isEmpty(param.get("notTemporaryCard"))) {
                    Predicate predicate = cb.notEqual(root.get("cardTypeId").as(String.class),
                            param.get("notTemporaryCard"));
                    predicates.add(predicate);
                }

                // 姓名
                if (param.containsKey("idName") && !StringUtils.isEmpty(param.get("idName"))) {
                    Predicate predicate = cb.like(root.get("idName"), param.get("idName") + "%");
                    predicates.add(predicate);
                }

                // 开卡时间
                if (param.containsKey("startTime") && !org.springframework.util.StringUtils.isEmpty(param.get("startTime"))) {// 开卡时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(param.get("startTime")), fmt);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                if (param.containsKey("endTime") && !org.springframework.util.StringUtils.isEmpty(param.get("endTime"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(param.get("endTime")), fmt);
                    predicates.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 开卡时间
                if (param.containsKey("startDate") && !org.springframework.util.StringUtils.isEmpty(param.get("startDate"))) {// 开卡时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(param.get("startDate")), fmt);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                if (param.containsKey("endDate") && !org.springframework.util.StringUtils.isEmpty(param.get("endDate"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(param.get("endDate")), fmt);
                    predicates.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 卡Id CriteriaBuilder in的用法
                if (param.containsKey("cardIds") && (!ObjectUtils.isEmpty(param.get("cardIds")) || param.containsKey("cardIdsMustExit"))) {
                    Path<Object> path = root.get("cardId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("cardIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                if (param.containsKey("cardTypeIds") && !ObjectUtils.isEmpty(param.get("cardTypeIds"))) {
                    Path<Object> path = root.get("cardTypeId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("cardTypeIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                if (param.containsKey("cardTypeIdsNotIn") && !ObjectUtils.isEmpty(param.get("cardTypeIdsNotIn"))) { //
                    predicates.add(cb.not(root.get("cardTypeId").as(String.class).in(param.get("cardTypeIdsNotIn"))));
                }

                if (param.containsKey("placeIds") && !ObjectUtils.isEmpty(param.get("placeIds"))) {
                    Path<Object> path = root.get("placeId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("placeIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                // mobile
                if (param.containsKey("mobile") && !StringUtils.isEmpty(param.get("mobile"))) {
                    Predicate predicate = cb.like(root.get("phoneNumber"), param.get("mobile") + "%");
                    predicates.add(predicate);
                }

                // minTotalAmount >/= cashAccount + presentAccount
                if (param.containsKey("minTotalAmount") && !StringUtils.isEmpty(param.get("minTotalAmount"))) {
                    Object minTotalAmount = param.get("minTotalAmount");
                    assert minTotalAmount instanceof String;
                    Integer minTotalAmountInt = Integer.parseInt((String) minTotalAmount);

                    Expression<Integer> sum = cb.sum(root.get("cashAccount").as(Integer.class), root.get("presentAccount").as(Integer.class));
                    Predicate predicate = cb.greaterThanOrEqualTo(sum, minTotalAmountInt);
                    predicates.add(predicate);
                }

                // maxTotalAmount </= cashAccount + presentAccount
                if (param.containsKey("maxTotalAmount") && !StringUtils.isEmpty(param.get("maxTotalAmount"))) {
                    Object maxTotalAmount = param.get("maxTotalAmount");
                    assert maxTotalAmount instanceof String;
                    Integer maxTotalAmountInt = Integer.parseInt((String) maxTotalAmount);

                    Expression<Integer> sum = cb.sum(root.get("cashAccount").as(Integer.class), root.get("presentAccount").as(Integer.class));
                    Predicate predicate = cb.lessThanOrEqualTo(sum, maxTotalAmountInt);
                    predicates.add(predicate);
                }

                // minPoints
                if (param.containsKey("minPoints") && !StringUtils.isEmpty(param.get("minPoints"))) {
                    Object minPoints = param.get("minPoints");
                    assert minPoints instanceof String;
                    Integer minPointsInt = Integer.parseInt((String) minPoints);

                    Predicate predicate = cb.greaterThanOrEqualTo(root.get("points"), minPointsInt);
                    predicates.add(predicate);
                }

                // maxPoints
                if (param.containsKey("maxPoints") && !StringUtils.isEmpty(param.get("maxPoints"))) {
                    Object maxPoints = param.get("maxPoints");
                    assert maxPoints instanceof String;
                    Integer maxPointsInt = Integer.parseInt((String) maxPoints);
                    Predicate predicate = cb.lessThanOrEqualTo(root.get("points"), maxPointsInt);
                    predicates.add(predicate);
                }

                // remark
                if (param.containsKey("remark") && !StringUtils.isEmpty(param.get("remark"))) {
                    Predicate predicate = cb.like(root.get("remark"), "%" + param.get("remark") + "%");
                    predicates.add(predicate);
                }

                // 身份证号，用于收银台会员列表管理页面获取班次下的开卡会员
                if (param.containsKey("idNumbers") && !ObjectUtils.isEmpty(param.get("idNumbers"))) {
                    Path<Object> path = root.get("idNumber");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> idNumbers = (List) param.get("idNumbers");
                    idNumbers.forEach(in::value);
                    predicates.add(in);
                }
                // ID卡号码（账号）
                if (param.containsKey("idcardNum") && !StringUtils.isEmpty(param.get("idcardNum"))) {
                    //只要输入了账号查询，那么就去查询身份证号或者会员卡号
//                    predicates.add(cb.or(cb.like(root.get("idNumber"), "%" + param.get("idcardNum") + "%"),
//                            cb.like(root.get("idcardNum"), "%" + param.get("idcardNum") + "%")));
                    //2023.12.28去除账号里的证件号模糊查询
                    Predicate predicate = cb.like(root.get("idNumber"), param.get("idcardNum") + "%");
                    predicates.add(predicate);
                }

                if (param.containsKey("searchKey") && !StringUtils.isEmpty(param.get("searchKey"))) {
                    Predicate idPredicate = cb.like(root.get("idNumber"), "%" + param.get("searchKey") + "%");
                    Predicate cardIdNumPredicate = cb.like(root.get("idcardNum"), "%" + param.get("searchKey") + "%");
                    Predicate orPredicate = cb.or(idPredicate, cardIdNumPredicate);

                    predicates.add(orPredicate);
                }

                Predicate[] p = new Predicate[predicates.size()];
                return cb.and(predicates.toArray(p));
            }
        }, pageable);
    }

    public List<BillingCard> findByList(Map<String, Object> param) {
//        Specification<LogOperation> specification = this.queryParams(map);
        return billingCardRepository.findAll(new Specification<BillingCard>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<BillingCard> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicates = new ArrayList<Predicate>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = cb.equal(root.get("deleted"), 0);
                predicates.add(p1);

                // 场所ID
                if (param.containsKey("placeId") && !StringUtils.isEmpty(param.get("placeId"))) {
                    Predicate predicate = cb.equal(root.get("placeId").as(String.class), param.get("placeId"));
                    predicates.add(predicate);
                }

                if (param.containsKey("activeTimeIsNotNull") && !StringUtils.isEmpty(param.get("activeTimeIsNotNull"))
                        && Boolean.parseBoolean(String.valueOf(param.get("activeTimeIsNotNull")))) {
                    Predicate predicate = cb.isNotNull(root.get("activeTime"));
                    predicates.add(predicate);
                }

                // 身份证号
                if (param.containsKey("idNumber") && !StringUtils.isEmpty(param.get("idNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), param.get("idNumber") + "%");
                    predicates.add(predicate);
                }
                // 身份证号(收银台查询)
                if (param.containsKey("cashierIdNumber") && !StringUtils.isEmpty(param.get("cashierIdNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), "%" + param.get("cashierIdNumber") + "%");
                    if ((param.get("cashierIdNumber") + "").length() == 18) {
                        predicate = cb.equal(root.get("idNumber"), param.get("cashierIdNumber"));
                    }
                    predicates.add(predicate);
                }
                // 卡类型
                if (param.containsKey("cardTypeId") && !StringUtils.isEmpty(param.get("cardTypeId"))) {
                    Predicate predicate = cb.equal(root.get("cardTypeId").as(String.class), param.get("cardTypeId"));
                    predicates.add(predicate);
                    param.remove("notTemporaryCard");//指定了 equal 就不需要再指定 notEqual 了
                }

                // 是否连锁开卡
                if (param.containsKey("chainCard") && !StringUtils.isEmpty(param.get("chainCard"))) {
                    Predicate predicate = cb.equal(root.get("chainCard").as(String.class), param.get("chainCard"));
                    predicates.add(predicate);
                }

                // 卡类型（除临时卡以外的所有卡类型）
                if (param.containsKey("notTemporaryCard") && !StringUtils.isEmpty(param.get("notTemporaryCard"))) {
                    Predicate predicate = cb.notEqual(root.get("cardTypeId").as(String.class),
                            param.get("notTemporaryCard"));
                    predicates.add(predicate);
                }

                // 姓名
                if (param.containsKey("idName") && !StringUtils.isEmpty(param.get("idName"))) {
                    Predicate predicate = cb.like(root.get("idName"), param.get("idName") + "%");
                    predicates.add(predicate);
                }

                // 开卡时间
                if (param.containsKey("startTime") && !org.springframework.util.StringUtils.isEmpty(param.get("startTime"))) {// 开卡时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(param.get("startTime")), fmt);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                if (param.containsKey("endTime") && !org.springframework.util.StringUtils.isEmpty(param.get("endTime"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(param.get("endTime")), fmt);
                    predicates.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 开卡时间
                if (param.containsKey("startDate") && !org.springframework.util.StringUtils.isEmpty(param.get("startDate"))) {// 开卡时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(param.get("startDate")), fmt);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                if (param.containsKey("endDate") && !org.springframework.util.StringUtils.isEmpty(param.get("endDate"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(param.get("endDate")), fmt);
                    predicates.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 卡Id CriteriaBuilder in的用法
                if (param.containsKey("cardIds") && (!ObjectUtils.isEmpty(param.get("cardIds")) || param.containsKey("cardIdsMustExit"))) {
                    Path<Object> path = root.get("cardId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("cardIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                if (param.containsKey("cardTypeIds") && !ObjectUtils.isEmpty(param.get("cardTypeIds"))) {
                    Path<Object> path = root.get("cardTypeId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("cardTypeIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                if (param.containsKey("placeIds") && !ObjectUtils.isEmpty(param.get("placeIds"))) {
                    Path<Object> path = root.get("placeId");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> cardIds = (List) param.get("placeIds");
                    cardIds.forEach(in::value);
                    predicates.add(in);
                }

                // mobile
                if (param.containsKey("mobile") && !StringUtils.isEmpty(param.get("mobile"))) {
                    Predicate predicate = cb.like(root.get("phoneNumber"), param.get("mobile") + "%");
                    predicates.add(predicate);
                }

                // minTotalAmount >/= cashAccount + presentAccount
                if (param.containsKey("minTotalAmount") && !StringUtils.isEmpty(param.get("minTotalAmount"))) {
                    Object minTotalAmount = param.get("minTotalAmount");
                    assert minTotalAmount instanceof String;
                    Integer minTotalAmountInt = Integer.parseInt((String) minTotalAmount);

                    Expression<Integer> sum = cb.sum(root.get("cashAccount").as(Integer.class), root.get("presentAccount").as(Integer.class));
                    Predicate predicate = cb.greaterThanOrEqualTo(sum, minTotalAmountInt);
                    predicates.add(predicate);
                }

                // maxTotalAmount </= cashAccount + presentAccount
                if (param.containsKey("maxTotalAmount") && !StringUtils.isEmpty(param.get("maxTotalAmount"))) {
                    Object maxTotalAmount = param.get("maxTotalAmount");
                    assert maxTotalAmount instanceof String;
                    Integer maxTotalAmountInt = Integer.parseInt((String) maxTotalAmount);

                    Expression<Integer> sum = cb.sum(root.get("cashAccount").as(Integer.class), root.get("presentAccount").as(Integer.class));
                    Predicate predicate = cb.lessThanOrEqualTo(sum, maxTotalAmountInt);
                    predicates.add(predicate);
                }

                // minPoints
                if (param.containsKey("minPoints") && !StringUtils.isEmpty(param.get("minPoints"))) {
                    Object minPoints = param.get("minPoints");
                    assert minPoints instanceof String;
                    Integer minPointsInt = Integer.parseInt((String) minPoints);

                    Predicate predicate = cb.greaterThanOrEqualTo(root.get("points"), minPointsInt);
                    predicates.add(predicate);
                }

                // maxPoints
                if (param.containsKey("maxPoints") && !StringUtils.isEmpty(param.get("maxPoints"))) {
                    Object maxPoints = param.get("maxPoints");
                    assert maxPoints instanceof String;
                    Integer maxPointsInt = Integer.parseInt((String) maxPoints);
                    Predicate predicate = cb.lessThanOrEqualTo(root.get("points"), maxPointsInt);
                    predicates.add(predicate);
                }

                // remark
                if (param.containsKey("remark") && !StringUtils.isEmpty(param.get("remark"))) {
                    Predicate predicate = cb.like(root.get("remark"), param.get("remark") + "%");
                    predicates.add(predicate);
                }

                // 身份证号，用于收银台会员列表管理页面获取班次下的开卡会员
                if (param.containsKey("idNumbers") && !ObjectUtils.isEmpty(param.get("idNumbers"))) {
                    Path<Object> path = root.get("idNumber");
                    CriteriaBuilder.In<Object> in = cb.in(path);
                    List<String> idNumbers = (List) param.get("idNumbers");
                    idNumbers.forEach(in::value);
                    predicates.add(in);
                }
                // ID卡号码
                if (param.containsKey("idcardNum") && !StringUtils.isEmpty(param.get("idcardNum"))) {
//                    Predicate predicate = cb.like(root.get("idcardNum"), "%" + param.get("idcardNum") + "%");
//                    predicates.add(predicate);
                    //只要输入了账号查询，那么就去查询身份证号或者会员卡号
//                    predicates.add(cb.or(cb.like(root.get("idNumber"), "%" + param.get("idcardNum") + "%"),
//                            cb.like(root.get("idcardNum"), "%" + param.get("idcardNum") + "%")));
                    Predicate predicate = cb.like(root.get("idNumber"), param.get("idcardNum") + "%");
                    predicates.add(predicate);
                }

                Predicate[] p = new Predicate[predicates.size()];
                return cb.and(predicates.toArray(p));
            }
        });
    }

    public void updateCardType(String placeId, String oldCardTypeId, BillingCardType newBillingCardType, String chainId) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(oldCardTypeId) || newBillingCardType == null) {
            log.warn("updateCardType param is null");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (StringUtils.isEmpty(newBillingCardType.getCardTypeId()) || StringUtils.isEmpty(newBillingCardType.getTypeName())) {
            log.warn("updateCardType param is null");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        billingCardRepository.updateCardType(placeId, oldCardTypeId, newBillingCardType.getCardTypeId(), newBillingCardType.getTypeName(), chainId, newBillingCardType.getChainCardTypeId());
    }

    public Map<String, BigDecimal> findCardNumAndTotalAccount2(String placeId,
                                                               String cardTypeId,
                                                               String idName,
                                                               String idNumber,
                                                               LocalDateTime startTime,
                                                               LocalDateTime endTime,
                                                               List<String> cardIds,
                                                               String mobile,
                                                               Integer minTotalAmount,
                                                               Integer maxTotalAmount,
                                                               Integer minPoints,
                                                               Integer maxPoints,
                                                               String remark,
                                                               List<String> idNumbers,
                                                               String idcardNum) {
        return billingCardRepository.findCardNumAndTotalAccount2(placeId,
                cardTypeId,
                idName,
                idNumber,
                startTime,
                endTime,
                mobile,
                minTotalAmount,
                maxTotalAmount,
                minPoints,
                maxPoints,
                remark,
                cardIds,
                idNumbers,
                idcardNum);
    }

    /**
     * 按班次-临时卡-未消费总额
     *
     * @param placeId
     * @return
     */
    public int sumBalanceTemporaryCardCashIncomeByPlaceId(String placeId) {
        Integer result = billingCardRepository.sumBalanceTemporaryCardCashIncomeByPlaceId(placeId);
        return result == null ? 0 : result;
    }

    /**
     * 根据卡Id批量修改卡类型
     *
     * @param placeId
     * @param cardTypeId
     * @param cardIds    2024.2.19改动:由一次性修改list变为单个修改卡类型
     */
    public void updateCardType(String placeId, String cardTypeId, String cardTypeName, List<String> cardIds, int chainCard) {
        for (int i = 0; i < cardIds.size(); i++) {
            String cardId = cardIds.get(i);
            // 先获取会员卡信息，然后通过当前会员卡的cardTypeId去和要更改的卡类型Id的最小积分要求做比较
            Optional<BillingCard> optBillingCard = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
            if (!optBillingCard.isPresent()) {
                continue;
            }
            BillingCard billingCard = optBillingCard.get();
            String curCardTypeId = billingCard.getCardTypeId();
            // 判断每个卡的当前卡类型对应的最小积分要求，和要修改的卡类型的等级高低关系，如果是从低到高需要补全
            Optional<BillingCardType> currentBillingCardType = billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, curCardTypeId, 0);
            if (!currentBillingCardType.isPresent()) {
                continue;
            }
            BillingCardType currentCardType = currentBillingCardType.get();
            // 获取要修改的卡类型数据，然后拿它的最小积分要求
            Optional<BillingCardType> billingCardType = billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
            if (!billingCardType.isPresent()) {
                continue;
            }
            BillingCardType newCardType = billingCardType.get();
            // 临时卡升级卡类型处理
            if ("1000".equals(billingCard.getCardTypeId())) {
                //int chainCard = 0;
//                List<PlaceChainStores> placeChainStores = placeChainStoresService.findPlaceChainStoresListInSamePlaceChainByPlaceId(placeId);
//                if (placeChainStores != null) {
//                    // 查询是否已经存在开卡门店的会员卡
//                    List<String> placeIds = placeChainStores.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
//                    Optional<BillingCard> chainCardOpt = findByChainIdAndIdNumberByMember(placeIds, billingCard.getIdNumber(), 0);
//                    if (chainCardOpt.isPresent()) {
//                        BillingCard chainBillingCard = chainCardOpt.get();
//                        log.info("临时卡更换会员卡,当前是连锁店，以开卡门店卡类型为准:::::chainCardTypeId::::" + chainBillingCard.getCardTypeId());
//                        chainCard = 1;
//                        Optional<BillingCardType> billingCardTypeOptional = billingCardTypeService.findByPlaceIdAndTypeName(placeId,chainBillingCard.getCardTypeName());
//                        if (billingCardTypeOptional.isPresent()) {
//                            newCardType = billingCardTypeOptional.get();
//                        }
//                    }
//                }

                int upgradePoints = billingCard.getPoints() > newCardType.getMinPointsRequirement() ? billingCard.getPoints() : newCardType.getMinPointsRequirement();
                // 更改卡类型
                billingCardService.tempUpgradeCardType(placeId, newCardType.getChainCardTypeId(), newCardType.getCardTypeId(),
                        newCardType.getTypeName(), cardId, billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount(), chainCard, upgradePoints);
                continue;
            }

            // 开始进行比较最小积分要求，判断本次修改卡类型是否从低到高修改会员卡类型等级
            int minPointsRequirement = newCardType.getMinPointsRequirement();
            if (currentCardType.getMinPointsRequirement() < minPointsRequirement) {
                // 会员卡从低变为高，需要补全卡类型的最小积分要求
                minPointsRequirement = billingCard.getPoints() > newCardType.getMinPointsRequirement() ? billingCard.getPoints() : newCardType.getMinPointsRequirement();
                billingCardRepository.updateCardTypeByCardIdParam(billingCardType.get().getChainCardTypeId(), cardTypeId, cardTypeName, minPointsRequirement, placeId, cardId);
            } else {
                // 如果不是从低到高，只改变会员卡类型即可
                billingCardRepository.updateCardTypeByCardId(billingCardType.get().getChainCardTypeId(), cardTypeId, cardTypeName, placeId, new ArrayList<>(Arrays.asList(cardId)), billingCard.getCashAccount(), billingCard.getChainCard());
            }
        }
    }


    /**
     * 营销充值网费后卡等级调整，根据卡Id修改卡类型
     *
     * @param placeId
     * @param cardTypeId
     * @param cardId
     */
    public void updateCardTypeWithTopup(String placeId, String cardTypeId, String cardTypeName, String cardId) {
        if(cardTypeId.equals("1000")){
            billingCardRepository.updateCardTypeWithTopup(cardTypeName, cardTypeId, placeId, cardId);
        }else{
            billingCardRepository.updateTempCardTypeWithTopup(cardTypeName, cardTypeId, placeId, cardId);
        }
    }

    /**
     * 根据卡Id修改卡类型
     *
     * @param placeId
     * @param cardTypeId
     * @param cardId
     */
    public void tempUpgradeCardType(String placeId, String chainCardTypeId, String cardTypeId, String cardTypeName, String cardId, int cashAccount, int chainCard, int points) {
        billingCardRepository.tempUpgradeCardType(chainCardTypeId, cardTypeId, cardTypeName, placeId, cardId, cashAccount, chainCard, points);
    }

    /**
     * 根据卡类型Id，修改卡类型名称
     *
     * @param placeId
     * @param cardTypeId
     * @param cardTypeName
     */
    public void updateCardTypeName(String placeId, String cardTypeId, String cardTypeName, List<String> cardIds) {
        billingCardRepository.updateCardTypeName(cardTypeName, placeId, cardTypeId, cardIds);
    }

    public List<BillingCard> findByChainIdAndIdNumberInAndDeleted(String chainId, List<String> idNumbers) {
        return billingCardRepository.findByChainIdAndIdNumberInAndDeleted(chainId, idNumbers, 0);
    }

    /**
     * 查询登入记录列表时获取姓名调用该方法
     *
     * @param placeIds
     * @param idNumbers
     * @return
     */
    public List<BillingCard> findByPlaceIdInAndIdNumberIn(List<String> placeIds, List<String> idNumbers) {
        return billingCardRepository.findByPlaceIdInAndIdNumberInAndDeleted(placeIds, idNumbers, 0);
    }

    public List<BillingCard> findByPlaceIdAndCardTypeId(String placeId, String cardTypeId) {
        return this.findByPlaceIdAndCardTypeIdAndDeletedOrderByIdDesc(placeId, cardTypeId, 0);
    }


    /**
     * 删除余额为零的计费卡
     *
     * @param placeId 场所 Id
     * @param cardId  会员卡 Id
     * @apiNote 传入  placeId/cardId，如果有卡，且卡余额为 0，则删除，即设置 deleted= 1，不为零不删除
     */
    public ServiceCodes removeUnderfundedBillingCard(String placeId, String cardId) {
        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(cardId)) {
            return ServiceCodes.NULL_PARAM;
        }

        Optional<BillingCard> billingCardOpt = billingCardRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
        if (!billingCardOpt.isPresent()) {
            return ServiceCodes.BILLING_CARD_NOT_FOUND;
        }

        BillingCard billingCard = billingCardOpt.get();

        int totalAcount = billingCard.getTotalAccount();
        if (totalAcount > 0) {
            log.warn("卡余额不为零，不允许删除该会员卡，placeId={}, cardId={}", placeId, cardId);
            return ServiceCodes.OPT_ERROR;
        }

        billingCard.setDeleted(1);
        billingCard.setUpdated(LocalDateTime.now());
        billingCardRepository.save(billingCard);
        return ServiceCodes.NO_ERROR;
    }

    /**
     * 删除连锁下的会员卡信息
     *
     * @param placeIds  场所 Ids
     * @param idNumbers 证件号
     * @apiNote 传入  placeIds/idNumbers，判断是否还在上机，没有上机就可以删除
     */
    public ServiceCodes removeChainCard(List<String> placeIds, List<String> idNumbers) {
        if (CollectionUtils.isEmpty(placeIds) || CollectionUtils.isEmpty(idNumbers)) {
            return ServiceCodes.NULL_PARAM;
        }
        List<BillingOnline> byPlaceIdInAndIdNumberInAndFinished = billingOnlineService.findByPlaceIdInAndIdNumberInAndFinished(placeIds, idNumbers);
        if (!CollectionUtils.isEmpty(byPlaceIdInAndIdNumberInAndFinished)) {
            return ServiceCodes.BILLING_ONLINE_DELETE_NOT_SUPPORT;
        }
        // 预包时取消
        List<PackageTimeReserve> packageTimeReserves = packageTimeReserveService.findUnusedByPlaceIdsAndIdNumbers(placeIds, idNumbers);
        if (!CollectionUtils.isEmpty(packageTimeReserves)) {
            for (PackageTimeReserve packageTimeReserve : packageTimeReserves) {
                packageTimeReserveService.updateInvalidationByPlaceIdAndCardId(packageTimeReserve.getPlaceId(), packageTimeReserve.getCardId());
                log.info("连锁会员卡预包时取消，placeId:{}, cardId:{}", packageTimeReserve.getPlaceId(), packageTimeReserve.getCardId());
            }
        }
        //删除分期赠送奖励
        for (String idNumber : idNumbers) {
            int i = logRecordRewardInstallmentService.deleteRewardInstallmentByPlaceIds(placeIds, idNumber);
            log.info("连锁删除会员时删除分期赠送金额信息场所列表：{}，证件号：{}，删除数目：{}", placeIds, idNumber, i);
        }

        int result = billingCardRepository.updateCardByPlaceIdsAndIdNumbers(placeIds, idNumbers);
        log.info("删除连锁会员卡条目数:{}", result);
        return ServiceCodes.NO_ERROR;
    }

    public List<BillingCard> findByBillingCardTypes(List<BillingCardType> billingCardTypesFromAllPlace) {
        if (CollectionUtils.isEmpty(billingCardTypesFromAllPlace)) {
            return Collections.emptyList();
        }

        List<BillingCard> billingCards = new ArrayList<>();
        Map<Long, BillingCardTypeBO> collect = billingCardTypesFromAllPlace.stream()
                .collect(Collectors.toMap(BillingCardType::getId, BillingCardType::toBO));
        // for collect
        for (Map.Entry<Long, BillingCardTypeBO> entry : collect.entrySet()) {
            BillingCardTypeBO billingCardTypeBO = entry.getValue();
            String placeId = billingCardTypeBO.getPlaceId();
            String cardTypeId = billingCardTypeBO.getCardTypeId();
            List<BillingCard> billingCardList = billingCardRepository.findByPlaceIdAndCardTypeIdAndDeletedOrderByIdDesc(placeId, cardTypeId, 0);
            billingCards.addAll(billingCardList);
        }

        log.info("根据计费卡类型，获取最新的计费卡，billingCards={}", new Gson().toJson(billingCards));
        return billingCards;
    }

    public void exitChain(String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            log.warn("退出连锁时，更新会员卡接口入参错误，placeId is empty");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        log.info("退出连锁时，更新会员卡，placeId={}", placeId);
        billingCardRepository.updateChainToZeroCardByPlaceId(placeId);
    }

    public void updateRoamChainCard(List<String> cardIds, String chainId) {
        if (StringUtils.isEmpty(chainId) || StringUtils.isEmpty(cardIds)) {
            log.warn("退出连锁时，更新漫游会员卡接口入参错误，chainId or cardIds is empty");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        log.info("退出连锁时，更新漫游会员卡，chainId={}, cardIds={}", chainId, cardIds);
        billingCardRepository.updateChainToZeroCardByChainId(cardIds, chainId);
    }

    /**
     * 收银台开卡
     *
     * @param placeId          门店id
     * @param shiftId          班次id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeType       激活类型
     * @param identification   证件类型
     * @param remark           备注
     * @param cashAmount       现金金额
     * @param presentAmount    赠送金额
     * @param amountFlag       是否带钱开卡
     * @param presentAmortized 分期奖励数
     * @return BillingCardBO
     */
    @Override
    public BillingCardBO cashierCreateCard(String placeId,
                                           String shiftId,
                                           String cardTypeId,
                                           String idNumber,
                                           String name,
                                           String phoneNumber,
                                           String address,
                                           String issuingAuthority,
                                           String nation,
                                           String validPeriod,
                                           String activeType,
                                           String identification,
                                           String remark,
                                           int cashAmount,
                                           int presentAmount,
                                           boolean amountFlag,
                                           int presentAmortized) {
        return commonCreateCard(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                cashAmount,
                presentAmount,
                SourceType.CASHIER,
                null,
                null,
                presentAmortized);
    }

    /**
     * 通用开卡，不允许直接使用
     *
     * @param placeId          场所ID（必填）
     * @param shiftId          班次ID （收银台调用有值，其他没有）
     * @param cardTypeId       卡类型ID（必填）
     * @param idNumber         身份证号（必填）
     * @param name             姓名
     * @param phoneNumber      手机号
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param validPeriod      有效期
     * @param activeTypeStr    激活类型（必填）
     * @param identification   是否附加费
     * @param remark           备注
     * @param topupAmount      开卡金额（必填）
     * @param presentAmount    赠送金额（必填）
     * @param presentAmortized 分期奖励数
     * @return 卡信息
     */
    public BillingCardBO commonCreateCard(String placeId,
                                          String shiftId,
                                          String cardTypeId,
                                          String idNumber,
                                          String name,

                                          String phoneNumber,
                                          String address,
                                          String issuingAuthority,
                                          String nation,
                                          String validPeriod,

                                          String activeTypeStr,
                                          String identification,
                                          String remark,
                                          int topupAmount,
                                          int presentAmount,

                                          SourceType sourceType,
                                          LocalDateTime activeTime,
                                          PayType payType,
                                          int presentAmortized) {

        log.info("commonCreateCard 开卡接口入参：placeId={}, shiftId={}, cardTypeId={}, idNumber={}, name={}, phoneNumber={}, address={}, " +
                        "issuingAuthority={}, nation={}, validPeriod={}, activeType={}, identification={}, remark={}, " +
                        "topupAmount={}, presentAmount={}, sourceType={},activeTime={}, payType={}",
                placeId, shiftId, cardTypeId, idNumber, name, phoneNumber, address,
                issuingAuthority, nation, validPeriod, activeTypeStr, identification, remark,
                topupAmount, presentAmount, sourceType.name(), activeTime, payType);

        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
        //校验年龄
        Boolean aBoolean = WechatAgeConfigUtils.verifyWechatAgeConfig(placeConfigBO.getWechatAgeConfig(), idNumber);
        if (!aBoolean) {
            log.info("用户：{}开卡时未通过场所：{}的上机年龄校验", idNumber, placeId);
            throw new ServiceException(ServiceCodes.BILLING_CONFIG_AGE_CONFIG_NO_PASS);//提示去实名认证付费
        }
        LocalDateTime now = LocalDateTime.now(); // 当前时间
        BillingCard billingCard = getExistButDeletedBillingCard(placeId, idNumber, now);

        billingCard.setPhoneNumber(phoneNumber);
        billingCard.setCardTypeId(cardTypeId);

        // 初始化参数
        billingCard.setLoginPass("123456");// 默认密码
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        if (!StringUtils.isEmpty(placeBizConfig.getForcePassword()) && "******".equals(placeBizConfig.getForcePassword())) {
            billingCard.setLoginPass(idNumber.substring(idNumber.length() - 6));
        }

        updateCardByRegcardServer(sourceType, billingCard, placeBizConfig);

        // 落地连锁编号
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);
        if (optPlaceChainStores.isPresent()) {
            PlaceChainStores placeChainStores = optPlaceChainStores.get();
            billingCard.setChainId(placeChainStores.getChainId());
        }
        BillingCardType billingCardTypeInfo = getBillingCardType(placeId, cardTypeId);
        billingCard.setCardTypeName(billingCardTypeInfo.getTypeName());
        billingCard.setChainCardTypeId(billingCardTypeInfo.getChainCardTypeId());
        // 判断开卡类型的最低开卡金额是否符合要求
        presentAmount = checkTopupAmountAndUpdatePresentAmount(topupAmount, presentAmount, sourceType, billingCardTypeInfo);

        // 充值的钱（临时卡充到在线账户，其他会员卡充到现金账户）
        if (onlinePayType.contains(payType) && "1000".equals(cardTypeId)) {
            //如果是临时卡线上充值余额需要写入到线上金额
            billingCard.setTemporaryOnlineAccount(topupAmount);
        } else {
            billingCard.setCashAccount(topupAmount);
        }
        if (presentAmortized > 0) {
            billingCard.setPresentAccount(presentAmount / presentAmortized);
        } else {
            billingCard.setPresentAccount(presentAmount);
        }

        //如果开启根据“积分”自动升级用户等级，需要补齐会员卡最低积分要求(需要当前积分小于最低积分要求)
        if ((placeBizConfig.getUpgradeUserLevelFlag() == 1 ||
                placeBizConfig.getDowngradeUserLevelFlag() == 1)
                && billingCard.getPoints() < billingCardTypeInfo.getMinPointsRequirement()) {
            log.info("开卡时打开了会员卡类型升降级开关，开卡类型为{}，当前积分为{}，补充到{}积分", billingCardTypeInfo.getTypeName(), billingCard.getPoints(), billingCardTypeInfo.getMinPointsRequirement());
            billingCard.setPoints(billingCardTypeInfo.getMinPointsRequirement());
        }

        billingCard.setIdName(name);

        billingCard.setLoginName(idNumber);
        billingCard.setAddress(address);
        billingCard.setIssuingAuthority(issuingAuthority);

        billingCard.setNation(nation);
        billingCard.setValidPeriod(validPeriod);
        billingCard.setRemark(remark);
        billingCard.setActiveTime(ObjectUtils.isEmpty(activeTime) ? now : activeTime);
        billingCard.setActiveType(ActiveType.getActiveTypes(Integer.parseInt(activeTypeStr))); // 激活方式
        LogShift logShift = getLogShift(placeId, shiftId, sourceType);
        billingCard.setCreater(StringUtils.isEmpty(logShift) ? 0 : Long.parseLong(logShift.getAccountId()));
        billingCard.setUpdated(now);
        billingCard.setDeleted(0);
        billingCard.setChainCard(0);

        billingCard = this.save(billingCard);
        log.info("the logShift={}", new Gson().toJson(logShift));
        log.info("the billingCard={}", new Gson().toJson(billingCard));

        logOperationService.addCreateCardLogOperation(sourceType, billingCard, topupAmount, (presentAmortized == 0 ? presentAmount : presentAmount / presentAmortized), logShift, presentAmortized, presentAmount);
        // 附加费逻辑,工作卡除外,注意此处只有收银台明确传了 identification=N 才不去收附加费，其他情况都要收附加费
        // 2023.12.16去除第三方来开卡判断了是否扣附加费的逻辑，目前公众号开卡都会传identification=N,因此不会写附加费记录临时表，那么上机的时候就不会扣附加费
        if (!"1002".equals(billingCard.getCardTypeId()) && !"N".equals(identification)) {
            tempRecordSurchargeService.getSurcharge(placeId, activeTypeStr, billingCard.getCardId(), billingCard.getCardTypeId());
        }

        BillingCardBO bo = billingCard.toBO();
        bo.setCreated(now); // 针对已经开过卡又销卡的，因为created字段不能被更新，返回给收银台bo设置值就可以了
        return bo;
    }

    /**
     * 判断开卡类型的最低开卡金额是否符合要求
     *
     * @param topupAmount
     * @param presentAmount
     * @param sourceType
     * @param billingCardTypeInfo
     * @return
     */
    private int checkTopupAmountAndUpdatePresentAmount(int topupAmount, int presentAmount, SourceType sourceType, BillingCardType billingCardTypeInfo) {
        String curCardTypeId = billingCardTypeInfo.getCardTypeId();
        boolean createCardWithMoneyFlag = ("1000".equals(curCardTypeId) && topupAmount != 0) || (!"1000".equals(curCardTypeId) && (topupAmount != 0 || presentAmount != 0));
        if (!createCardWithMoneyFlag) {
            return presentAmount;
        }

        int minCreateCardAmount = billingCardTypeInfo.getMinCreateCardAmount();
        if (topupAmount < minCreateCardAmount) {
            throw new ServiceException(ServiceCodes.BILLING_FAILED_TO_CREATE_CARD, "开卡失败，最低开卡金额为" + minCreateCardAmount / 100 + "元");
        }

        // 计算赠送金额
        if (presentAmount == -1) {
            TopupRule effectedTopupRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(billingCardTypeInfo.getPlaceId(),
                    curCardTypeId,
                    topupAmount,
                    SourceType.CASHIER == sourceType ? 1 : 0);
            if (effectedTopupRule == null) {
                presentAmount = 0;
            } else {
                presentAmount = effectedTopupRule.getPresentAmount();
            }
        }

        return presentAmount;
    }

    /**
     * 注册卡相关逻辑
     *
     * @param sourceType
     * @param billingCard
     */
    private void updateCardByRegcardServer(SourceType sourceType, BillingCard billingCard, PlaceBizConfig placeBizConfig) {
        String placeId = billingCard.getPlaceId();
        String idNumber = billingCard.getIdNumber();
        PlaceConfigBO placeConfigBO = getPlaceConfigBO(placeId);
        // 注册卡逻辑

        if ((idNumber.length() != 18)
                || (placeConfigBO.getRegcard() != 1)
                || ("1002".equals(billingCard.getCardTypeId()))
                || (SourceType.CASHIER != sourceType && SourceType.WECHAT != sourceType && SourceType.MINIAPP != sourceType && SourceType.YISHANGWANG != sourceType)) {
            return;
        }

        // 开启注册(工作卡除外)
        PlaceProfileBO profileBO = getPlaceProfileBO(placeId);

        if (StringUtils.isEmpty(profileBO.getAuditId())) {
            throw new ServiceException(ServiceCodes.REGCARD_CONFIG_ERROR);
        }

        // 如果场所设置的移动端扫码跳过注册卡绑定的值是0,代表需要校验注册卡。注意，收银台不受影响，因为收银台开卡不知道是扫码来的还是刷卡的来源。
        if (placeConfigBO.getCheckRegcard() == 0 && SourceType.CASHIER != sourceType) {
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

            GenericResponse<SimpleDTO> respOnActive = regcardServerService.onActive(requestTicket, placeId,
                    profileBO.getAuditId(), profileBO.getAreaId(), idNumber);

            if (respOnActive.getCode() != ServiceCodes.REGCARD_SUCCESS.getCode()) {
                if (respOnActive.getCode() == ServiceCodes.REGCARD_SERVER_ERROR.getCode()) {
                    // 注册卡中心异常,自动关闭注册卡开关，发送短信通知
                    regcardAlgorithm.closeRegcard(placeConfigBO);
                }
                log.info("注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());
                throw new ServiceException(ServiceCodes.REGCARD_INVALID_NEED_BINDING);//提示“请到收银台绑定注册卡”

            }
            String[] resultStr = respOnActive.getData().getResult().split(",");
            if (placeBizConfig.getUseRegPassword() == 1) {
                billingCard.setLoginPass("123456");// 不使用注册卡密码
            } else {
                billingCard.setLoginPass(resultStr[0]);// 注册卡注册成功后传回登入密码
            }

            billingCard.setPhoneNumber(resultStr[1]); // 注册卡注册成功后传回手机号码
        } else if (placeConfigBO.getCheckRegcard() == 1 && SourceType.CASHIER != sourceType) {
            // 需要跳过的客户端.  移动端扫码跳过注册卡绑定的客户端配置：0: 全部, 1:四维新版&V8公众号,2:易上网APP,3:IOT自助机,4:支付宝小程序
            String regcardCheckedClients = placeConfigBO.getRegcardCheckedClients();
            if (!RecardServiceUtil.isInRegcardCheckedClients(sourceType, regcardCheckedClients)) {
                // 注册卡逻辑
                String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
                stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
                GenericResponse<SimpleDTO> respOnActive = regcardServerService.onActive(requestTicket, placeId, profileBO.getAuditId(), profileBO.getAreaId(), idNumber);
                if (respOnActive.getCode() != ServiceCodes.REGCARD_SUCCESS.getCode()) {
                    log.info("注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());
                    if (respOnActive.getCode() == ServiceCodes.REGCARD_SERVER_ERROR.getCode()) {
                        // 注册卡中心异常,自动关闭注册卡开关，发送短信通知
                        regcardAlgorithm.closeRegcard(placeConfigBO);
                    }
                    throw new ServiceException(ServiceCodes.REGCARD_INVALID_NEED_BINDING);
                }
            }

            // 如果是移动跳过校验check_regcard为1的情况下，那么就去查询iot接口判断人脸实名认证是否需要付费
            GenericResponse<ListDTO<IotAuthConfigBO>> needCharge = iotAuthConfigApi.needCharge(placeId, "1", placeConfigBO.getType() + "");
            if (needCharge.isResult() && needCharge.getData().getList() != null) {
                GenericResponse<ObjDTO<LogAuthFeeBO>> needChargeResponse = iotAuthConfigApi.currentNeedCharge(placeId, idNumber);
                if (needChargeResponse.isResult() && needChargeResponse.getData().getObj() == null) {
                    throw new ServiceException(ServiceCodes.PAY_REALNAME_AUTH_FEE);//提示去实名认证付费
                }
            }
        } else if (SourceType.CASHIER == sourceType) {
            //收银台开卡只判断注册卡
            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

            GenericResponse<SimpleDTO> respOnActive = regcardServerService.onActive(requestTicket, placeId,
                    profileBO.getAuditId(), profileBO.getAreaId(), idNumber);

            if (respOnActive.getCode() == ServiceCodes.REGCARD_SUCCESS.getCode()) {
                log.info("注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());
                String[] resultStr = respOnActive.getData().getResult().split(",");
                if (placeBizConfig.getUseRegPassword() == 1) {
                    billingCard.setLoginPass("123456");// 不使用注册卡密码
                } else {
                    billingCard.setLoginPass(resultStr[0]);// 注册卡注册成功后传回登入密码
                }
                billingCard.setPhoneNumber(resultStr[1]); // 注册卡注册成功后传回手机号码
            } else {
                if (respOnActive.getCode() == ServiceCodes.REGCARD_SERVER_ERROR.getCode()) {
                    // 注册卡中心异常,自动关闭注册卡开关，发送短信通知
                    regcardAlgorithm.closeRegcard(placeConfigBO);
                }
            }
        }
    }

    /**
     * 从注销的卡中获取已经存在的信息
     *
     * @param placeId  场所id
     * @param idNumber 身份证
     * @param now      当前时间
     * @return 已经存在的卡信息
     */
    private BillingCard getExistButDeletedBillingCard(String placeId, String idNumber, LocalDateTime now) {
        BillingCard billingCard = new BillingCard();
        billingCard.setCreated(now); // 创建时间
        billingCard.setPlaceId(placeId);
        billingCard.setIdNumber(idNumber);

        Optional<BillingCard> optBillingCard = billingCardRepository.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (optBillingCard.isPresent()) {
            BillingCard existedCard = optBillingCard.get();
            if (existedCard.getDeleted() == 0) { // 会员卡状态正常
                throw new ServiceException(ServiceCodes.BILLING_CARD_IS_EXSIT);
            }
            billingCard = existedCard; // 获取已经存在的卡信息
        }
        return billingCard;
    }

    private LogShift getLogShift(String placeId, String shiftId, SourceType sourceType) {
        LogShift logShift = null;
        if (SourceType.CASHIER == sourceType) {
            // 查询班次信息
            Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
            if (!optLogShift.isPresent()) {
                log.info("班次信息不存在:::" + placeId + ":::" + shiftId);
                throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
            }
            logShift = optLogShift.get();
        } else {
            logShift = logShiftService.getShiftId(placeId);
        }
        return logShift;
    }

    private PlaceConfigBO getPlaceConfigBO(String placeId) {
        // 获取场所配置信息
        GenericResponse<ObjDTO<PlaceConfigBO>> respConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (respConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.BILLING_PLACE_CONFIG_NOT_FOUND);
        }
        return respConfig.getData().getObj();
    }

    private BillingCardType getBillingCardType(String placeId, String cardTypeId) {
        // 验证卡类型ID
        Optional<BillingCardType> optBillingCardType = billingCardTypeService.findByPlaceIdAndCardTypeId(placeId, cardTypeId);
        if (!optBillingCardType.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }
        return optBillingCardType.get();
    }

    private PlaceProfileBO getPlaceProfileBO(String placeId) {
        // 验证场所ID
        GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
        if (respProfile.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.BILLING_PLACE_PROFILE_NOT_FOUND);
        }

        // 校验场所状态
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (!placeConfig.isResult()) {
            throw new ServiceException(placeConfig.getMessage());
        }
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        return respProfile.getData().getObj();
    }

    /**
     * 第三方开卡
     *
     * @param placeId          门店id
     * @param cardTypeId       卡类型id
     * @param idNumber         身份证号
     * @param name             姓名
     * @param identification   证件类型
     * @param cashierId        收银台id
     * @param address          地址
     * @param issuingAuthority 签发机关
     * @param nation           民族
     * @param phoneNumber      手机号
     * @param validPeriod      有效期
     * @param thirdAccount     第三方账户
     * @return BillingCardBO
     */
    @Override
    public BillingCardBO thirdCreateCard(String placeId,
                                         String cardTypeId,
                                         String idNumber,
                                         String name,
                                         String identification,
                                         String cashierId,
                                         String address,
                                         String issuingAuthority,
                                         String nation,
                                         String phoneNumber,
                                         String validPeriod,
                                         ThirdAccount thirdAccount,
                                         LocalDateTime activeTime) {
        PlaceProfileBO placeProfileBO = getPlaceProfileBO(placeId);
        // 获取来源
        SourceType sourceType = thirdAccount.getSourceType();
        // 校验该场所是否属于该账号类型
//        ServiceCodes checkCode = checkThirdProfile(placeProfileBO.getType(), sourceType);
//        if (checkCode != null) {
//            throw new ServiceException(checkCode);
//        }

        String activeType =
                sourceType.name().equals(SourceType.JWELL.name())
                        ? String.valueOf(ActiveType.SELF_MACHINE_JWELL.getValue())
                        : sourceType.name().equals(SourceType.YISHANGWANG.name())
                        ? String.valueOf(ActiveType.APP_YI_SHANG_WANG.getValue())
                        : sourceType.name().equals(SourceType.IOT.name())
                        ? String.valueOf(ActiveType.ALI_IOT.getValue())
                        : String.valueOf(ActiveType.THIRD.getValue());
        String shiftId = "";
        int cashAmount = 0;
        int presentAmount = 0;

        BillingCardBO billingCardBO = this.commonCreateCard(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                "",
                cashAmount,
                presentAmount,
                sourceType,
                activeTime,
                null
                , 0);

        savePolling(cashierId, sourceType, billingCardBO, null);

        return billingCardBO;
    }

    public ServiceCodes checkThirdProfile(int type, SourceType sourceType) {
        if (sourceType.equals(SourceType.MARKET) && type != 4) {
            return ServiceCodes.PLACE_NOT_MARKET;
        }

        if (sourceType.equals(SourceType.JWELL) && type != 5) {
            return ServiceCodes.PLACE_NOT_JWELL;
        }

        if (sourceType.equals(SourceType.DABAZHANG) && type != 6) {
            return ServiceCodes.PLACE_NOT_DABAZHANG;
        }
        return null;
    }

    public SourceType getType(String thirdName) {
        switch (thirdName) {
            case THIRD_TYPE_JWELL:
                return SourceType.JWELL;
            case THIRD_TYPE_MARKET:
                return SourceType.MARKET;
            case THIRD_DA_BA_ZHANG:
                return SourceType.DABAZHANG;
            case THIRD_TYPE_YI_SHANG_WANG:
                return SourceType.YISHANGWANG;
            case THIRD_TYPE_QING_WANG:
                return SourceType.QINGWANG;
            case THIRD_TYPE_IOT:
                return SourceType.IOT;
            default:
                return SourceType.OTHER;
        }
    }

    private void savePolling(String cashierId, SourceType sourceType, BillingCardBO billingCardBO, LogTopup logTopup) {
        // 保存轮询数据
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(billingCardBO.getPlaceId(), "", billingCardBO.getIdNumber(), BusinessType.CREATECARD);

        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();

            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {

                CreateCardBusinessBO createCardBusinessBO = new CreateCardBusinessBO();
                createCardBusinessBO.setPlaceId(billingCardBO.getPlaceId());
                createCardBusinessBO.setIdNumber(billingCardBO.getIdNumber());
                createCardBusinessBO.setCreated(LocalDateTime.now().toString());
                createCardBusinessBO.setSourceType(sourceType);
                createCardBusinessBO.setBusinessType(BusinessType.CREATECARD);
                createCardBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                createCardBusinessBO.setClientId("");
                createCardBusinessBO.setCashierId(cashierId);
                createCardBusinessBO.setType(1);
                createCardBusinessBO.setOperator(sourceType.name());
                createCardBusinessBO.setCardId(billingCardBO.getCardId());
                createCardBusinessBO.setPresentAccount(billingCardBO.getPresentAccount());
                createCardBusinessBO.setCashAccount(billingCardBO.getCashAccount());
                createCardBusinessBO.setIdName(billingCardBO.getIdName());
                createCardBusinessBO.setPhoneNumber(billingCardBO.getPhoneNumber());
                if (null != logTopup) {
                    createCardBusinessBO.setOrderId(logTopup.getOrderId());
                    createCardBusinessBO.setPayType(logTopup.getPayType());
                    if (logTopup.getPayType().equals(PayType.AGGREGATE_PAY) ||
                            logTopup.getPayType().equals(PayType.AGGREGATE_PAY_ALI) ||
                            logTopup.getPayType().equals(PayType.AGGREGATE_PAY_WECHAT) ||
                            logTopup.getPayType().equals(PayType.WECHAT_SCAN) ||
                            logTopup.getPayType().equals(PayType.ALIPAY_SCAN)) {
                        createCardBusinessBO.setNoPopupIde("1");
                    }
                }


                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                billingCardBO.setActiveTimeStr(null != billingCardBO.getActiveTime() ? formatter.format(billingCardBO.getActiveTime()) : null);
                billingCardBO.setCreatedStr(null != billingCardBO.getCreated() ? formatter.format(billingCardBO.getCreated()) : null);
                billingCardBO.setUpdatedStr(null != billingCardBO.getUpdated() ? formatter.format(billingCardBO.getUpdated()) : null);
                createCardBusinessBO.setBillingCardBO(billingCardBO);

                // 保存收银台业务数据
                notifyServerService.pushCreateCardBusinessData(createCardBusinessBO);
                log.info("pushCreateCardBusinessData:{}", new Gson().toJson(createCardBusinessBO));
            }
        }
    }

    public void activation(String placeId, String idNumber, int activeTypeValue, String name, String phoneNumber) {
        LocalDateTime now = LocalDateTime.now();
        // 激活分成两步，解决后面activeTime保存时会把总余额入库到分店余额的情况
        Optional<BillingCard> optionalBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
        if (!optionalBillingCard.isPresent()) {
            log.info("激活会员卡失败，会员卡不存在，placeId={}, idNumber={}", placeId, idNumber);
            throw new ServiceException(ServiceCodes.NOT_FOUND);
        }

        ActiveType activeType = ActiveType.getActiveTypes(activeTypeValue);
        if (null == activeType) {
            log.info("激活会员卡失败，激活类型不存在，placeId={}, idNumber={}, activeType={}", placeId, idNumber, activeTypeValue);
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        BillingCard billingCard = optionalBillingCard.get();
        BillingCard roamCard = null;

        if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            Optional<BillingCard> optRoamBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
            if (!optRoamBillingCard.isPresent()) {
                throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
            }
            roamCard = optRoamBillingCard.get();
        }
        if (roamCard == null) {
            roamCard = billingCard;
        }


        if (billingCard.getActiveTime() == null || billingCard.getActiveTime().plusMinutes(30).isBefore(now)) {
            billingCard.setActiveTime(now);
            billingCard.setActiveType(activeType);
            if (!StringUtils.isEmpty(billingCard.getIdName()) &&
                    billingCard.getIdName().contains("*") &&
                    !StringUtils.isEmpty(name)) {
                billingCard.setIdName(name);
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(phoneNumber)) {
            billingCard.setPhoneNumber(phoneNumber);
        }

        billingCard.setUpdated(now);
        billingCardService.save(billingCard);

        BillingCard chainBillingCard = billingCard;
        if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
            // 查询卡余额，要用这个方法
            Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber,
                    0);
            if (!optBillingCard.isPresent()) {
                log.info("激活会员卡失败，会员卡不存在，placeId={}, idNumber={}", placeId, idNumber);
                throw new ServiceException(ServiceCodes.NOT_FOUND);
            }
            chainBillingCard = optBillingCard.get();
        }

        // 附加费逻辑,工作卡除外
        if (!"1002".equals(chainBillingCard.getCardTypeId())) {
            tempRecordSurchargeService.getSurcharge(placeId, String.valueOf(activeType.getValue()),
                    chainBillingCard.getCardId(), chainBillingCard.getCardTypeId());
        }

        // 根据激活方式获取来源
        SourceType sourceType = tempRecordSurchargeService.getSourceType(activeTypeValue);

        // 写激活记录
//        logOperationService.addActivateCardOperation(sourceType, roamCard, null, String.valueOf(activeTypeValue));
        logActivateService.addLogActivateCard(sourceType, roamCard, null, String.valueOf(activeTypeValue));

        GenericResponse<ObjDTO<PlaceProfileBO>> profileResponse = placeServerService.findByPlaceId(placeId);
        if (profileResponse.isResult()) {
            //推送激活成功消息
            String cardIdShow = idNumber.substring(0, 2) + "******" + idNumber.substring(idNumber.length() - 2); // 身份证号
            wechatMessageApi.sendActiveCardSuccessMessage(placeId, idNumber, profileResponse.getData().getObj().getDisplayName(), now, cardIdShow);
        }

        // 保存轮询数据
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, "", idNumber,
                BusinessType.ACTIVE);

        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();

            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {

                ActiveBusinessBO activeBusinessBO = new ActiveBusinessBO();
                activeBusinessBO.setPlaceId(placeId);
                activeBusinessBO.setIdNumber(billingCard.getIdNumber());
                activeBusinessBO.setCreated(LocalDateTime.now().toString());
                activeBusinessBO.setSourceType(sourceType);
                activeBusinessBO.setBusinessType(BusinessType.ACTIVE);
                activeBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                activeBusinessBO.setClientId("");
                activeBusinessBO.setType(1);
                activeBusinessBO.setOperator(sourceType.name());
                activeBusinessBO.setActiveTime(LocalDateTime.now().toString());
                activeBusinessBO.setIdName(billingCard.getIdName());
                activeBusinessBO.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount());
                activeBusinessBO.setPresentAccount(billingCard.getPresentAccount());
                activeBusinessBO.setCardId(billingCard.getCardId());
                activeBusinessBO.setPhoneNumber(billingCard.getPhoneNumber());
                BillingCardBO bo = billingCard.toBO();

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                bo.setActiveTimeStr(null != bo.getActiveTime() ? formatter.format(bo.getActiveTime()) : null);
                bo.setCreatedStr(null != bo.getCreated() ? formatter.format(bo.getCreated()) : null);
                bo.setUpdatedStr(null != bo.getUpdated() ? formatter.format(bo.getUpdated()) : null);
                activeBusinessBO.setBillingCardBO(bo);

                // 保存收银台业务数据
                notifyServerService.pushActiveBusinessData(activeBusinessBO);
                log.info("pushActiveBusinessData:{}", new Gson().toJson(activeBusinessBO));
            }
        }
    }

    /**
     * 在卡激活前先校验注册卡状态逻辑公用方法
     *
     * @param placeId
     * @param idNumber
     * @param activeTypeValue
     * @param phoneNumber
     * @return
     */
    public GenericResponse<?> verifyRegisterCard(String placeId, String idNumber, int activeTypeValue, String name, String phoneNumber) {
        // 通过页面开关判断是否开启以下查询校验逻辑
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();

        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            return new GenericResponse<>(ServiceCodes.PLACE_REALNAME_DISABLED);
        }

        // 如果开启了注册卡开关
        if (placeConfigBO.getRegcard() == 1) {
            if (org.springframework.util.StringUtils.isEmpty(placeConfigBO.getCheckRegcard())) {
                return new GenericResponse<>(ServiceCodes.REGCARD_CONFIG_ERROR);
            }
            // 如果场所设置的 移动端扫码跳过注册卡绑定 的值是0,代表需要校验注册卡
            if (placeConfigBO.getCheckRegcard() == 0) {
                GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
                // 注册卡逻辑
                PlaceProfileBO profileBO = respProfile.getData().getObj();
                if (org.apache.commons.lang.StringUtils.isEmpty(profileBO.getAuditId())) {
                    return new GenericResponse<>(ServiceCodes.REGCARD_CONFIG_ERROR);
                }

                GenericResponse<SimpleDTO> respOnActive = regcardServerService.onActive(requestTicket, placeId, profileBO.getAuditId(), profileBO.getAreaId(), idNumber);
                if (respOnActive.getCode() != ServiceCodes.REGCARD_SUCCESS.getCode()) {
                    log.info("卡激活第三方接口调用注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());
                    if (respOnActive.getCode() == ServiceCodes.REGCARD_SERVER_ERROR.getCode()) {
                        // 注册卡中心异常,自动关闭注册卡开关，发送短信通知
                        regcardAlgorithm.closeRegcard(placeConfigBO);
                    }
                    return new GenericResponse<>(ServiceCodes.REGCARD_INVALID_NEED_BINDING);
                    //提示“请到收银台绑定注册卡”
                }
            } else {
                // 需要跳过的客户端.  移动端扫码跳过注册卡绑定的客户端配置：0: 全部, 1:四维新版&V8公众号,2:易上网APP,3:IOT自助机,4:支付宝小程序
                String regcardCheckedClients = placeConfigBO.getRegcardCheckedClients();
                if (!RecardServiceUtil.isInRegcardCheckedClients(ActiveType.getActiveTypes(activeTypeValue), regcardCheckedClients)) {
                    GenericResponse<ObjDTO<PlaceProfileBO>> respProfile = placeServerService.findByPlaceId(placeId);
                    // 注册卡逻辑
                    PlaceProfileBO profileBO = respProfile.getData().getObj();
                    if (org.apache.commons.lang.StringUtils.isEmpty(profileBO.getAuditId())) {
                        return new GenericResponse<>(ServiceCodes.REGCARD_CONFIG_ERROR);
                    }
                    stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
                    GenericResponse<SimpleDTO> respOnActive = regcardServerService.onActive(requestTicket, placeId, profileBO.getAuditId(), profileBO.getAreaId(), idNumber);
                    if (respOnActive.getCode() != ServiceCodes.REGCARD_SUCCESS.getCode()) {
                        log.info("注册卡返回结果:::" + respOnActive.getCode() + "返回信息:::" + respOnActive.getMessage());
                        if (respOnActive.getCode() == ServiceCodes.REGCARD_SERVER_ERROR.getCode()) {
                            // 注册卡中心异常,自动关闭注册卡开关，发送短信通知
                            regcardAlgorithm.closeRegcard(placeConfigBO);
                        }
                        return new GenericResponse<>(ServiceCodes.REGCARD_INVALID_NEED_BINDING);
                    }
                }

                // 如果是移动跳过校验check_regcard 为1的情况下，那么就去查询iot接口判断人脸实名认证是否需要付费
                GenericResponse<ListDTO<IotAuthConfigBO>> needCharge = iotAuthConfigApi.needCharge(placeId, "1", placeConfigBO.getType() + "");
                if (needCharge.isResult() && needCharge.getData().getList() != null) {
                    GenericResponse<ObjDTO<LogAuthFeeBO>> needChargeResponse = iotAuthConfigApi.currentNeedCharge(placeId, idNumber);
                    if (needChargeResponse.isResult() && needChargeResponse.getData().getObj() == null) {
                        log.info("卡激活第三方接口查询当前用户在当前场所本次实名认证是否需要收费返回空数据,说明没有认证，提示去认证");
                        return new GenericResponse<>(ServiceCodes.PAY_REALNAME_AUTH_FEE);//提示去实名认证付费
                    }
                }
            }
            //end<-- 查询注册卡信息，根据返回的结果判断是否需有一个绑定的有效注册卡
        }
        //校验年龄
        Boolean aBoolean = WechatAgeConfigUtils.verifyWechatAgeConfig(placeConfigBO.getWechatAgeConfig(), idNumber);
        if (!aBoolean) {
            log.info("用户：{}卡激活时未通过场所：{}的上机年龄校验", idNumber, placeId);
            return new GenericResponse<>(ServiceCodes.PAY_REALNAME_AUTH_FEE);//提示去实名认证付费
        }
        // 激活
        this.activation(placeId, idNumber, activeTypeValue, name, phoneNumber);
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    /**
     * 查询当前场所所有卡信息
     *
     * @param placeId
     * @param deleted
     * @return
     */
    public List<BillingCard> findByPlaceIdAndDeleted(String placeId, int deleted) {
        return billingCardRepository.findByPlaceIdAndDeletedAndChainIdIsNull(placeId, deleted);
    }

    public List<BillingCard> findByPlaceIdAndDeletedUnderChain(String placeId, int deleted) {
        return billingCardRepository.findByPlaceIdAndDeletedAndCardTypeIdNotIn(placeId, deleted, Arrays.asList("1000", "1002"));
    }

    /**
     * 批量刷连锁会员数据
     *
     * @param placeId
     * @param cardIds
     * @param chainId
     * @param chainCardTypeId
     */
    public void updateChainMember(String placeId, List<String> cardIds, String chainId, String chainCardTypeId) {
        billingCardRepository.updateChainMember(placeId, cardIds, chainId, chainCardTypeId);
    }

    /**
     * 获取同一连锁下面，除了当前门店下外，当前身份证的最开始的会员卡
     *
     * @param chainId
     * @param placeIdWaitingJoinChain
     * @param idNumbers
     * @return
     */
    public List<BillingCard> getTheOriginalCardUnderChain(String chainId, String placeIdWaitingJoinChain, List<String> idNumbers) {
        return billingCardRepository.findByChainIdAndChainCardAndPlaceIdNotAndIdNumberInAndDeletedAndCardTypeIdNotIn(chainId, 0, placeIdWaitingJoinChain, idNumbers, 0, Arrays.asList("1000", "1002"));
    }

    public List<BillingCard> findByPlaceIdAndCardIdIn(String placeId, List<String> cardIds) {
        return billingCardRepository.findByPlaceIdAndCardIdInAndDeleted(placeId, cardIds, 0);
    }


    /**
     * 不分页查询，返回所有符合条件的BillingCard
     *
     * @param queryMap 查询参数
     * @return List<BillingCard>
     */
    public List<BillingCard> activeCardList(Map<String, Object> queryMap) {

        log.info("BillingCardService.activeCardList param:{}", new Gson().toJson(queryMap));

        return billingCardRepository.findAll(new Specification<BillingCard>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<BillingCard> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicates = new ArrayList<Predicate>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = cb.equal(root.get("deleted"), 0);
                predicates.add(p1);

                // 场所ID
                if (queryMap.containsKey("placeId") && !StringUtils.isEmpty(queryMap.get("placeId"))) {
                    Predicate predicate = cb.equal(root.get("placeId").as(String.class), queryMap.get("placeId"));
                    predicates.add(predicate);
                }

                // 先加入 activeTime 不为 null 的条件
                Predicate activeTimeNotNullPredicate = cb.isNotNull(root.get("activeTime"));
                predicates.add(activeTimeNotNullPredicate);

                // 查询“激活时间”在最近30分钟内
                if (queryMap.containsKey("activeTimeWithin30Minutes") &&
                        Boolean.parseBoolean(String.valueOf(queryMap.get("activeTimeWithin30Minutes")))) {
                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime thirtyMinutesAgo = now.minusMinutes(30);
                    Predicate recentActiveTimePredicate = cb.between(
                            root.get("activeTime"),
                            thirtyMinutesAgo,
                            now
                    );
                    predicates.add(recentActiveTimePredicate);
                }

                // 身份证号
                if (queryMap.containsKey("idNumber") && !StringUtils.isEmpty(queryMap.get("idNumber"))) {
                    Predicate predicate = cb.like(root.get("idNumber"), queryMap.get("idNumber") + "%");
                    if ((queryMap.get("idNumber") + "").length() == 18) {
                        predicate = cb.equal(root.get("idNumber"), queryMap.get("idNumber"));
                    }
                    predicates.add(predicate);
                }

                Predicate[] p = new Predicate[predicates.size()];
                return cb.and(predicates.toArray(p));
            }
        });
    }

//    /**
//     * 清除用户在连锁下的余额
//     * @param chainId
//     * @param idNumber
//     */
//    public void clearChainCashAccount(String chainId,String idNumber){
//        billingCardRepository.clearChainCashAccount(chainId,idNumber);
//    }
//
//    /**
//     * 清除用户在连锁下的奖励余额
//     * @param chainId
//     * @param idNumber
//     */
//    public void clearChainPresentAccount(String chainId,String idNumber){
//        billingCardRepository.clearChainPresentAccount(chainId,idNumber);
//    }
//
//    public void updateChainThirdCard(String chainId, String idNumber, String cardTypeId, String password, String idName,String phoneNumber,String chainCardTypeId){
//        billingCardRepository.updateChainThirdCard(chainId,idNumber,cardTypeId,password,idName,phoneNumber,chainCardTypeId);
//    }

}