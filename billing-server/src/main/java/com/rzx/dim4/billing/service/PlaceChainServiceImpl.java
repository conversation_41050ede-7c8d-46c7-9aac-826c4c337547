package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.admin.JoinPlaceToChainBO;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceChainStoresApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/4/16
 **/
@Slf4j
@Service
public class PlaceChainServiceImpl implements PlaceChainService {

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PlaceChainStoresApi placeChainStoresApi;

    @Async
    @Override
    public void placeJoinChain(JoinPlaceToChainBO joinPlaceToChainBO) {
        if (null == joinPlaceToChainBO) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        PlaceChainStoresBO placeChainStoresBO = joinPlaceToChainBO.getPlaceChainStoresBO();
        List<BillingCardTypeBO> billingCardTypeBOS = joinPlaceToChainBO.getBillingCardTypeBOS();

        billingCardTypeService.updateAfterChain(billingCardTypeBOS);

        if (null == placeChainStoresBO || StringUtils.isEmpty(placeChainStoresBO.getPlaceId())
                || StringUtils.isEmpty(placeChainStoresBO.getPlaceName())
                || StringUtils.isEmpty(placeChainStoresBO.getChainId())
                || StringUtils.isEmpty(placeChainStoresBO.getChainName())) {
            log.info("新增连锁门店失败，参数错误，placeChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (0 == placeChainStoresBO.getShareCashAccount()) {
            placeChainStoresBO.setShareCashAccount(1);
        }
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<?> response = placeChainStoresApi.add(requestTicket, placeChainStoresBO);

        if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            log.warn("新增连锁门店失败，placeId={}, code={}, msg={}", placeChainStoresBO.getPlaceId(), response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
    }

    @Override
    public void checkParamsBeforeJoinChain(JoinPlaceToChainBO joinPlaceToChainBO) {
        log.info("执行加入连锁校验，joinPlaceToChainBO={}", new Gson().toJson(joinPlaceToChainBO));

        if (null == joinPlaceToChainBO) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        PlaceChainStoresBO placeChainStoresBO = joinPlaceToChainBO.getPlaceChainStoresBO();
        List<BillingCardTypeBO> billingCardTypeBOS = joinPlaceToChainBO.getBillingCardTypeBOS();

        billingCardTypeService.checkCardTypeParamsForJoinChain(billingCardTypeBOS);

        if (null == placeChainStoresBO || StringUtils.isEmpty(placeChainStoresBO.getPlaceId())
                || StringUtils.isEmpty(placeChainStoresBO.getPlaceName())
                || StringUtils.isEmpty(placeChainStoresBO.getChainId())
                || StringUtils.isEmpty(placeChainStoresBO.getChainName())) {
            log.info("新增连锁门店失败，参数错误，placeChainStoresBO={}", new Gson().toJson(placeChainStoresBO));
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (0 == placeChainStoresBO.getShareCashAccount()) {
            placeChainStoresBO.setShareCashAccount(1);
        }
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<?> response = placeChainStoresApi.checkBeforeAdd(requestTicket, placeChainStoresBO);

        if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            log.warn("新增连锁门店失败，placeId={}, code={}, msg={}", placeChainStoresBO.getPlaceId(), response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
    }

    @Async
    @Override
    public void doJoinChain(JoinPlaceToChainBO joinPlaceToChainBO) {
        log.info("异步执行加入连锁，joinPlaceToChainBO={}", new Gson().toJson(joinPlaceToChainBO));

        billingCardTypeService.doMergeCardTypesForJoinChain(joinPlaceToChainBO.getBillingCardTypeBOS());

        PlaceChainStoresBO placeChainStoresBO = joinPlaceToChainBO.getPlaceChainStoresBO();

        if (0 == placeChainStoresBO.getShareCashAccount()) {
            placeChainStoresBO.setShareCashAccount(1);
        }
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        GenericResponse<?> response = placeChainStoresApi.doAdd(requestTicket, placeChainStoresBO);

        if (response.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            log.warn("新增连锁门店失败，placeId={}, code={}, msg={}", placeChainStoresBO.getPlaceId(), response.getCode(), response.getMessage());
        }
    }
}
