package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.PeakOnline;
import com.rzx.dim4.billing.repository.PeakOnlineRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 当日高峰在线人数
 *
 * <AUTHOR>
 * @date 2025年4月24日
 */
@Slf4j
@Service
public class PeakOnlineService {

    @Autowired
    PeakOnlineRepository peakOnlineRepository;


    @Autowired
    StringRedisTemplate stringRedisTemplate;

    /**
     * 保存网费余额明细
     * @param peakOnline
     * @return
     */
    public PeakOnline save(PeakOnline peakOnline) {
        return peakOnlineRepository.save(peakOnline);
    }

    public Optional<PeakOnline> findByPlaceIdAndCountDay(String placeId, String countDay) {
        return peakOnlineRepository.findByPlaceIdAndCountDayAndDeleted(placeId,countDay,0);
    }

    // 批量保存
    public List<PeakOnline> saveAll(List<PeakOnline> peakOnline) {
        return peakOnlineRepository.saveAll(peakOnline);
    }

    public Page<PeakOnline> findAll(Map<String, String> map, Pageable pageable) {
        return peakOnlineRepository.findAll(new Specification<PeakOnline>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<PeakOnline> root, CriteriaQuery<?> query,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Predicate p1 = criteriaBuilder.equal(root.get("deleted"), 0);
                predicateList.add(p1);
                // 场所ID
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("placeId").as(String.class), map.get("placeId"));
                    predicateList.add(predicate);
                }

                if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("cardId").as(String.class), map.get("cardId"));
                    predicateList.add(predicate);
                }

                if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {// 是否删除
                    predicateList.add(criteriaBuilder.equal(root.get("deleted"),Integer.parseInt(map.get("deleted"))));
                }

                // 报损单状态
                if (map.containsKey("type") && !StringUtils.isEmpty(map.get("type"))) {// 类型名称
//                    predicateList.add(criteriaBuilder.equal(root.get("deleted"),Integer.parseInt(map.get("deleted"))));
                    predicateList.add(criteriaBuilder.equal(root.get("type").as(Integer.class),map.get("type")));
                }

                // 开始时间
                if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {// 开始时间
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(map.get("startDate")), fmt);
                    predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                // 结束时间
                if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {// 操作结束时间
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(map.get("endDate")), fmt);
                    predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }


                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicateArr));
            }
        }, pageable);
    }



}
