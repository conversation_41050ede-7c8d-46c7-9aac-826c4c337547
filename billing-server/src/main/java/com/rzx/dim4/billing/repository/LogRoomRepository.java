package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogRoom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2023年3月8日 上午11:23:49
 */
public interface LogRoomRepository extends JpaRepository<LogRoom, Long> {

	List<LogRoom> findByPlaceIdAndAreaIdAndFinished(String placeId, String areaId, int finished);

	Optional<LogRoom> findByPlaceIdAndCardIdAndFinished(String placeId, String cardId, int finished);

	List<LogRoom> findByPlaceIdAndAreaIdAndFinishedAndIsMaster(String placeId, String areaId, int finished, int isMaster);

	/**
	 * 查询所有主卡下机 副卡在线的记录
	 * @return
	 */
	@Query(value = "SELECT * FROM log_room lr \n" +
			"WHERE\n" +
			"lr.is_master = 0 \n" +
			"AND lr.finished = 0 \n" +
			"AND lr.place_id IN ( SELECT place_id FROM `log_room` WHERE is_master = 1 GROUP BY place_id, area_id ) \n" +
			"AND lr.area_id NOT IN ( SELECT area_id FROM `log_room` WHERE is_master = 1 AND lr.place_id = place_id AND finished = 0 GROUP BY place_id, area_id )", nativeQuery = true)
	List<LogRoom> queryNotDismountedRoom();

}
