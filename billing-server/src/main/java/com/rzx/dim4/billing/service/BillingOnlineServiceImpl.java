package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.service.feign.billing.param.BillingOnlineParam;
import com.rzx.dim4.billing.entity.BaseEntity;
import com.rzx.dim4.billing.entity.BillingOnline;
import com.rzx.dim4.billing.repository.BillingOnlineRepository;
import com.rzx.dim4.billing.repository.LogHbRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date Jul 1, 2020 10:46:37 AM
 */
@Slf4j
@Service
public class BillingOnlineServiceImpl implements BillingOnlineService {

	@Autowired
	BillingOnlineRepository onlineRepository;

	@Autowired
	LogHbRepository hbRepository;
	
	@Autowired
	PlaceBizConfigService placeBizConfigService;

	@Override
	public BillingOnline save(BillingOnline billingOnline) {
		return onlineRepository.save(billingOnline);
	}

	/**
	 * 单独对nextTime更新,避免使用save时nextTime并发问题
	 * @param id
	 * @param timestamp
	 */
	@Override
	public synchronized void updateOnlineNextTime (Long id, Long timestamp, int onceDeduction, int onceDeductionCash, int onceDeductionPresent) {
		onlineRepository.updateOnlineNextTime(id, timestamp, onceDeduction, onceDeductionCash, onceDeductionPresent);
	}

	@Override
	public Optional<BillingOnline> findUnfinishedByPlaceIdAndClientId(String placeId, String clientId) {

		List<BillingOnline> list = onlineRepository.findByPlaceIdAndFinishedAndClientIdOrderByIdDesc(placeId, 0, clientId);
		if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
			for (int i=1;i<list.size();i++) {
				BillingOnline billingOnline = list.get(i);
				billingOnline.setUpdated(LocalDateTime.now());
				billingOnline.setRemark("异常数据修复");
				billingOnline.setFinished(1);
				onlineRepository.save(billingOnline);
			}
			return Optional.of(list.get(0));
		} else if (list.size() == 1) {
			return Optional.of(list.get(0));
		}
		return Optional.empty();
	}

	/**
	 * 查询多个场所的在线信息
	 * @param placeIds
	 * @return
	 */
	@Override
	public List<BillingOnline> findByPlaceIdInAndFinished(List<String> placeIds) {
		return onlineRepository.findByPlaceIdInAndFinished(placeIds, 0);
	}

	@Override
	public List<BillingOnline> findByPlaceIdInAndIdNumberInAndFinished(List<String> placeIds,List<String> idNumbers) {
		return onlineRepository.findByPlaceIdInAndIdNumberInAndFinished(placeIds,idNumbers, 0);
	}

	@Override
	public Optional<BillingOnline> findUnfinishedByPlaceIdAndCardId(String placeId, String cardId) {
		List<BillingOnline> list = onlineRepository.findAllByPlaceIdAndCardIdAndFinishedAndDeletedOrderByIdDesc(placeId, cardId, 0,
				0);
		if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
			for (int i=1;i<list.size();i++) {
				BillingOnline billingOnline = list.get(i);
				billingOnline.setFinished(1);
				onlineRepository.save(billingOnline);
			}
			return Optional.of(list.get(0));
		} else if (list.size() == 1) {
			return Optional.of(list.get(0));
		}
		return Optional.empty();
	}

	@Override
	public List<BillingOnline> findTop5ByPlaceIdAndIdNumber(String placeId, String idNumber) {
		return onlineRepository.findTop5ByPlaceIdAndIdNumberAndFinishedAndDeletedOrderByIdDesc(placeId, idNumber, 1, 0);
	}

	@Override
	public Optional<BillingOnline> findUnfinishedByIdNumber(String idNumber) {
		return onlineRepository.findByIdNumberAndFinished(idNumber, 0);
	}

	@Override
	public List<BillingOnline> findUnfinishedByPlaceId(String placeId) {
		return onlineRepository.findByPlaceIdAndFinishedOrderByIdDesc(placeId, 0);
	}

	@Override
	public List<BillingOnline> findFinishedByPlaceIdAndCardIds(String placeId, List<String> cardIds) {
		return onlineRepository.findByPlaceIdAndCardIdInAndFinishedOrderByIdDesc(placeId, cardIds, 1);
	}

	@Override
	public List<BillingOnline> findFinishedByPlaceIdsAndIdNumber(List<String> placeIds, String idNumber, int finish) {
		return onlineRepository.findByPlaceIdInAndIdNumberAndFinishedOrderByIdDesc(placeIds, idNumber, finish);
	}

	@Override
	public Optional<BillingOnline> findTop1FinishedByPlaceIdsAndIdNumber(List<String> placeIds, String idNumber, int finish) {
		return onlineRepository.findTop1ByPlaceIdInAndIdNumberAndFinishedOrderByIdDesc(placeIds, idNumber, finish);
	}

	@Override
	public List<BillingOnline> findUnfinishedByPlaceIdAndClientIds(String placeId, List<String> clientIds) {
		return onlineRepository.findByPlaceIdAndClientIdInAndFinishedOrderByIdDesc(placeId, clientIds, 0);
	}

	@Override
	public List<BillingOnline> findUnfinishedByPlaceIdAndCardIds(String placeId, List<String> cardIds) {
		return onlineRepository.findByPlaceIdAndCardIdInAndFinishedOrderByIdDesc(placeId, cardIds, 0);
	}

	@Override
	public Optional<BillingOnline> findByPlaceIdAndIdNumber(String placeId, String idNumber) {
		return onlineRepository.findByPlaceIdAndIdNumberAndFinished(placeId, idNumber, 0);
	}

	@Override
	public Optional<BillingOnline> findUnfinishedByPlaceIdAndIdNumber(String placeId, String idNumber) {
		return onlineRepository.findTop1ByPlaceIdAndIdNumberAndFinishedAndDeleted(placeId, idNumber, 0, BaseEntity.NO);
	}

	@Override
	public Optional<BillingOnline> findOnlineByPlaceIdAndCardIdAndLoginId(String placeId, String cardId, String loginId) {
		return onlineRepository.findByPlaceIdAndCardIdAndLoginIdAndFinishedOrderByIdDesc(placeId, cardId, loginId, 0);
	}

	@Override
	public Optional<BillingOnline> findTop1ByPlaceIdAndCardIdAndLoginId(String placeId, String cardId, String loginId) {
		return onlineRepository.findTop1ByPlaceIdAndCardIdAndLoginIdOrderByIdDesc(placeId, cardId, loginId);
	}

	@Override
	public Boolean haveOnline(String placeId) {
		List<BillingOnline> billingOnlines = onlineRepository.findByPlaceIdInAndFinished(Collections.singletonList(placeId), 0);
		return !CollectionUtils.isEmpty(billingOnlines);
	}

	@Override
	public Optional<BillingOnline> findByPlaceIdAndClientId(String placeId, String clientId) {
		return 	onlineRepository.findByPlaceIdAndClientIdAndFinished(placeId,clientId,0);
	}

	@Override
	public Optional<BillingOnline> findTop1ByPlaceIdAndClientId(String placeId, String clientId) {
		return 	onlineRepository.findTop1ByPlaceIdAndClientIdAndFinishedOrderByIdDesc(placeId,clientId,0);
	}

	@Override
	public Optional<BillingOnline> findTop1ByIdNumberOrderByIdDesc(String idNumber) {
		return onlineRepository.findTop1ByIdNumberOrderByIdDesc(idNumber);
	}

	@Override
	public int countByPlaceIdAndCardTypeIdAndFinished(String placeId, String cardTypeId, int finish) {
		return onlineRepository.countByPlaceIdAndCardTypeIdAndFinished(placeId, cardTypeId, finish);
	}

	@Override
	public int countByPlaceIdAndCardTypeIdNotAndFinished(String placeId, String cardTypeId, int finish) {
		return onlineRepository.countByPlaceIdAndCardTypeIdNotAndFinished(placeId, cardTypeId, finish);
	}

	@Override
	public List<BillingOnline> findByList(BillingOnlineParam billingOnlineParam) {
		Specification<BillingOnline> specification = this.queryParams(billingOnlineParam);
		return onlineRepository.findAll(specification);
	}

	@Override
	public Page<BillingOnline> findByPage(BillingOnlineParam billingOnlineParam, Pageable pageable) {
		Specification<BillingOnline> specification = this.queryParams(billingOnlineParam);
		// 先不排序，临时处理数据库cpu过高
		pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
		return onlineRepository.findAll(specification, pageable);
	}

	@Override
	public List<BillingOnline> findByPlaceIdAndLoginId(String placeId, String loginId) {
		return onlineRepository.findByPlaceIdAndLoginId(placeId, loginId);
	}


	@Override
	public Page<BillingOnline> findAll(Map<String, Object> map, Pageable pageable) {

		return onlineRepository.findAll(new Specification<BillingOnline>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingOnline> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
				List<Predicate> andPredicateList = new ArrayList<>();
				// 场所
				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
				}

				if (map.containsKey("placeIds") && !ObjectUtils.isEmpty(map.get("placeIds"))) { // 场所Ids
					Path<Object> path = root.get("placeId");
					CriteriaBuilder.In<Object> in = cb.in(path);
					List<String> placeIds = (List)map.get("placeIds");
					placeIds.forEach(in::value);
					andPredicateList.add(in);
				}
				// 身份证号码
				if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
					andPredicateList.add(cb.equal(root.get("idNumber").as(String.class), map.get("idNumber")));
				}
				// 上机状态
				if (map.containsKey("finished") && !StringUtils.isEmpty(map.get("finished"))) {
					andPredicateList.add(cb.equal(root.get("finished").as(int.class), Integer.valueOf(String.valueOf(map.get("finished")))));
				}

				// 姓名
				if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 是否查询在线订单
					andPredicateList.add(cb.like(root.get("idName"), "%" + map.get("idName") + "%"));
				}

				Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
				return cb.and(andPredicateList.toArray(andPredicateArr));
			}
		}, pageable);

	}

	// 结账后调用该方法清掉数据
	@Override
	public void deleteByPlaceIdAndLoginId (String placeId, String loginId) {
		onlineRepository.deleteByPlaceIdAndLoginId(placeId, loginId);
	}

	@Override
	public Integer findSumDeductionByLoginId(String loginId) {
		Integer sumDeductionByLoginId = onlineRepository.findSumDeductionByLoginId(loginId);
		return null == sumDeductionByLoginId ? 0 : sumDeductionByLoginId;
	}


	private Specification<BillingOnline> queryParams(BillingOnlineParam onlineBO) {
		return (root, query, cb) -> {
			List<Predicate> andPredicateList = new ArrayList<>();
			DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

			// 操作开始结束时间
			if (!StringUtils.isEmpty(onlineBO.getStartDate()) && !StringUtils.isEmpty(onlineBO.getEndDate())) {
				LocalDateTime startTime = LocalDateTime.parse(onlineBO.getStartDate(), fmt);
				LocalDateTime endTime = LocalDateTime.parse(onlineBO.getEndDate(), fmt);
				andPredicateList.add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
				andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
			}
			if (!StringUtils.isEmpty(onlineBO.getPlaceId())) {
				andPredicateList.add(cb.equal(root.get("placeId").as(String.class), onlineBO.getPlaceId()));
			}
			if (!StringUtils.isEmpty(onlineBO.getAreaId())) {
				andPredicateList.add(cb.equal(root.get("areaId").as(String.class), onlineBO.getAreaId()));
			}

			if (!StringUtils.isEmpty(onlineBO.getCardTypeId())) {
				andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), onlineBO.getCardTypeId()));
			}

			andPredicateList.add(cb.equal(root.get("finished").as(int.class), onlineBO.getFinished()));

			if (!StringUtils.isEmpty(onlineBO.getIdName())) {
				andPredicateList.add(cb.like(root.get("idName"), "%" + onlineBO.getIdName() + "%"));
			}

			if (!StringUtils.isEmpty(onlineBO.getIdNumber())) {
				andPredicateList.add(cb.like(root.get("idNumber"), "%" + onlineBO.getIdNumber() + "%"));
			}

			if (!StringUtils.isEmpty(onlineBO.getClientId())) {
				andPredicateList.add(cb.equal(root.get("clientId").as(String.class), onlineBO.getClientId()));
			}

			if (!StringUtils.isEmpty(onlineBO.getClientIds())) {
				andPredicateList.add(root.get("clientId").as(String.class).in(onlineBO.getClientIds()));
			}

			Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
			return cb.and(andPredicateList.toArray(andPredicateArr));
		};
	}

	public List<BillingOnline> queryPlaceOnlineListByPlaceIdAndIdNumberIn(String placeId, List<String> idNumbers) {
		return onlineRepository.findAllByPlaceIdAndDeletedAndFinishedAndIdNumberIn(placeId, 0, 0, idNumbers);
	}
	@Override
	public int updateOnlineCardTypeId( String cardTypeId, String placeId, String cardId){
		return onlineRepository.updateOnlineCardTypeId(cardTypeId,placeId,cardId);
	}



	// 统计网吧当前在线人数
	@Override
	public int countByPlaceIdAndFinished(String placeId, int finish) {
		return onlineRepository.countByPlaceIdAndFinished(placeId, finish);
	}

	@Override
	public int updateOnlineInviteStatus(int isInvite, String placeId, String loginId){
		return onlineRepository.updateOnlineInviteStatus(isInvite,placeId,loginId);
	}
	// 获取在线信息
	@Override
	public Optional<BillingOnline> findUnfinishedByPlaceIdAndClientIdAndFinished(String placeId, String clientId) {
		return onlineRepository.findUnfinishedByPlaceIdAndClientIdAndFinished(placeId,clientId,0);
	}

	@Override
	public List<BillingOnline> findAll(Map<String, Object> map) {
		return onlineRepository.findAll(new Specification<BillingOnline>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<BillingOnline> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
				List<Predicate> andPredicateList = new ArrayList<>();
				// 场所
				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
				}
				if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {
					andPredicateList.add(cb.equal(root.get("deleted").as(String.class), map.get("deleted")));
				}
				// 身份证号码
				if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
					andPredicateList.add(cb.like(root.get("idNumber"), "%" + map.get("idNumber") + "%"));
				}
				// 区域id
				if (map.containsKey("areaId") && !StringUtils.isEmpty(map.get("areaId"))) {
					andPredicateList.add(cb.equal(root.get("areaId").as(String.class), map.get("areaId")));
				}
				// 机器id
				if (map.containsKey("clientId") && !StringUtils.isEmpty(map.get("clientId"))) {
					andPredicateList.add(cb.equal(root.get("clientId").as(String.class), map.get("clientId")));
				}

				if (map.containsKey("packageFlag")) {
					String flagValue = String.valueOf(map.get("packageFlag"));

					if ("true".equalsIgnoreCase(flagValue)) {
						// 查询 packageFlag > 0,即packageFlag=1，2
						Predicate packageFlagPredicate = cb.gt(root.get("packageFlag").as(Integer.class), 0);
						andPredicateList.add(packageFlagPredicate);
					} else if ("false".equalsIgnoreCase(flagValue)) {
						// 查询 packageFlag = 0
						Predicate packageFlagPredicate = cb.equal(root.get("packageFlag").as(Integer.class), 0);
						andPredicateList.add(packageFlagPredicate);
					}
				}
				// 上机状态 finished=0 上机中
				if (map.containsKey("finished") && !StringUtils.isEmpty(map.get("finished"))) {
					andPredicateList.add(cb.equal(root.get("finished").as(int.class), Integer.valueOf(String.valueOf(map.get("finished")))));
				}

				return cb.and(andPredicateList.toArray(new Predicate[0]));
			}
		});
	}

}
