package com.rzx.dim4.billing.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.ClientWallpaper;
import com.rzx.dim4.billing.entity.ClientWallpaperDeliver;
import com.rzx.dim4.billing.repository.ClientWallpaperDeliverRepository;
import com.rzx.dim4.billing.repository.ClientWallpaperRepository;

@Service
public class ClientWallpaperService {

	@Autowired
	ClientWallpaperRepository placeWallpaperRepository;

	@Autowired
	ClientWallpaperDeliverRepository placeWallpaperDeliverRepository;
	
	public Optional<ClientWallpaper> findById(int id){
		return placeWallpaperRepository.findById(Long.valueOf(id));
	}

	public Page<ClientWallpaper> findByPlaceId(Pageable pageable) {
		return placeWallpaperRepository.findByOrderByIdDesc(pageable);
	}

	public ClientWallpaper saveClientWallpaper(ClientWallpaper clientWallpaper) {
		return placeWallpaperRepository.save(clientWallpaper);
	}

	public ClientWallpaperDeliver saveClientWallpaperDeliver(ClientWallpaperDeliver clientWallpaperDeliver) {
		return placeWallpaperDeliverRepository.save(clientWallpaperDeliver);
	}

}
