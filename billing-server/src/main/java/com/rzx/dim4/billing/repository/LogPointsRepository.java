package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.enums.billing.ExchangePointsType;
import com.rzx.dim4.billing.entity.LogPoints;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface LogPointsRepository extends JpaRepository<LogPoints, Long>, JpaSpecificationExecutor<LogPoints> {

    /**
     * 查询变更类型的次数
     *
     * @param exchangePointsType
     * @param cardId
     * @return
     */
    @Query("select count(lp.id) from LogPoints lp where lp.placeId=?1 and lp.exchangePointsType=?2 and lp.cardId=?3 ")
    Integer countSignedNum(String placeId, ExchangePointsType exchangePointsType, String cardId);

}
