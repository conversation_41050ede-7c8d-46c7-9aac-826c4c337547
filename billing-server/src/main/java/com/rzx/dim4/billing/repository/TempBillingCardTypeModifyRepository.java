package com.rzx.dim4.billing.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.rzx.dim4.billing.entity.TempBillingCardTypeModify;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月12日 下午2:10:57
 */
public interface TempBillingCardTypeModifyRepository extends JpaRepository<TempBillingCardTypeModify, Long> {

	Optional<TempBillingCardTypeModify> findByPlaceIdAndCardIdAndDeleted(String placeId, String cardId, int deleted);

	List<TempBillingCardTypeModify> findByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);
}
