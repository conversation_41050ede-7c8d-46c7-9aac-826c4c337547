package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.BaseEntity;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.entity.SurchargeConfig;
import com.rzx.dim4.billing.entity.TempRecordSurcharge;
import com.rzx.dim4.billing.repository.PlaceBizConfigRepository;
import com.rzx.dim4.billing.repository.SurchargeConfigRepository;
import com.rzx.dim4.billing.repository.TempRecordSurchargeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 附加费临时表记录
 */
@Service
@Slf4j
public class TempRecordSurchargeService {

    @Autowired
    TempRecordSurchargeRepository tempRecordSurchargeRepository;

    @Autowired
    SurchargeConfigRepository surchargeConfigRepository;

    @Autowired
    PlaceBizConfigRepository placeBizConfigRepository;

    /**
     * 查询当天未失效的附加费记录信息
     * @param placeId
     * @param cardId
     * @param deleted
     * @param startTime
     * @param endTime
     * @return
     */
    public Optional<TempRecordSurcharge> findUnfinishedSurcharge (String placeId, String cardId, int deleted, LocalDateTime startTime, LocalDateTime endTime) {
        return tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndDeletedAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, cardId, deleted, startTime, endTime);
    }

    /**
     * 查询当天已产生的附加费记录
     * @param placeId
     * @param cardId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<TempRecordSurcharge> findSurchargeByDay (String placeId, String cardId, LocalDateTime startTime, LocalDateTime endTime) {
        return tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, cardId, startTime, endTime);
    }

    /**
     * 保存
     * @param tempRecordSurcharge
     * @return
     */
    public TempRecordSurcharge save (TempRecordSurcharge tempRecordSurcharge) {
        return tempRecordSurchargeRepository.save(tempRecordSurcharge);
    }

    /**
     * 查询当前卡的附加费临时表未生效的记录
     * @param placeId
     * @param cardId
     * @param deleted
     * @return
     */
    public Optional<TempRecordSurcharge> findByPlaceIdAndCardIdAndDeleted (String placeId, String cardId, int deleted) {
        return tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, deleted);
    }

    /**
     * 查询当前卡的附加费临时表未生效的记录(实名补扣逻辑)
     * @param placeId
     * @param idNumber
     * @param deleted
     * @return
     */
    public Optional<TempRecordSurcharge> findByPlaceIdAndIdNumberAndDeleted (String placeId, String idNumber, int deleted) {
        return tempRecordSurchargeRepository.findByPlaceIdAndIdNumberAndDeleted(placeId, idNumber, deleted);
    }

    public List<TempRecordSurcharge> findByPlaceIdAndCardIdInAndDeleted (String placeId, List<String> cardIds) {
        return tempRecordSurchargeRepository.findByPlaceIdAndCardIdInAndDeleted(placeId, cardIds, BaseEntity.NO);
    }

    /**
     * 根据激活类型获取来源
     * @param activeValue
     * @return
     */
    public SourceType getSourceType (int activeValue) {
        if (activeValue == ActiveType.SWGJ_WECHAT.getValue()) {
            return SourceType.WECHAT;
        } else if (activeValue == ActiveType.MINI_APP.getValue()) {
            return SourceType.MINIAPP;
        } else if (activeValue == ActiveType.ALI_IOT.getValue()) {
            return SourceType.ALIPAY;
        } else if (activeValue == ActiveType.MANUAL_INPUT.getValue() || activeValue == ActiveType.RZX_REALNAME.getValue()) {
            return SourceType.RZXREALNAME;
        } else if (activeValue == ActiveType.SELF_MACHINE_JWELL.getValue()) {
            return SourceType.JWELL;
        } else if (activeValue == ActiveType.APP_YI_SHANG_WANG.getValue()) {
            return SourceType.YISHANGWANG;
        } else {
            return SourceType.CASHIER;
        }
    }

    /**
     * 校验传入的机会方式，是否在设置的范围内
     * @param checkActiveType
     * @param activeTypes
     * @return
     */
    public boolean checkActiveType(String checkActiveType, String activeTypes) {
        List<String> types = Arrays.asList(activeTypes.split("_"));
        for (String type : types) {
            List<String> checkType = Arrays.asList(type.split(","));
            for (String aType : checkType) {
                if (checkActiveType.equals(aType)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 附加费相关逻辑
     * @param placeId
     * @param activeType
     * @param cardId
     * @param cardTypeId
     * @return
     */
    public TempRecordSurcharge getSurcharge(String placeId, String activeType, String cardId,String cardTypeId) {

        Optional<PlaceBizConfig> placeBizConfigOpt = placeBizConfigRepository.findByPlaceId(placeId);
        // 查询附加费扣费配置信息
        Optional<SurchargeConfig> surchargeConfigOpt = surchargeConfigRepository.findByPlaceId(placeId);

        if (placeBizConfigOpt.isPresent() && placeBizConfigOpt.get().getSurcharge() == 1 && surchargeConfigOpt.isPresent() && activeType != null) {
            SurchargeConfig surchargeConfig = surchargeConfigOpt.get();
            // 判断当前激活方式是否是设置的范围内
            if (!checkActiveType(activeType, surchargeConfig.getActiveTypes())) {
                // 不在设置的机会范围内
                return null;
            }
            // 处理临时卡和会员卡设置附加费扣费为0时也会写附加费记录临时表
            // 临时卡并且临时卡扣附加费配置为0 ，返回ture不写记录
            if ("1000".equals(cardTypeId) && surchargeConfig.getDeductTempCard() == 0) {
                return null;
            }
            // 如果不是临时卡和工作卡并且会员卡扣附加费配置为0，返回ture不写记录
            if (!"1000".equals(cardTypeId) && !"1002".equals(cardTypeId) && surchargeConfig.getDeductMemberCard() == 0) {
                return null;
            }

            Optional<TempRecordSurcharge> tempRecordSurchargeOpt = tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndDeleted(placeId,cardId,0);
            if (tempRecordSurchargeOpt.isPresent()) {
                // 如果有一条未使用的，则更新扣款信息
                TempRecordSurcharge tempRecordSurcharge = tempRecordSurchargeOpt.get();
                tempRecordSurcharge.setSourceType(getSourceType(Integer.parseInt(activeType)));
                tempRecordSurcharge.setDeductAccount(surchargeConfig.getDeductAccount());
                tempRecordSurcharge.setDeductMemberCard(surchargeConfig.getDeductMemberCard());
                tempRecordSurcharge.setDeductTempCard(surchargeConfig.getDeductTempCard());
                tempRecordSurchargeRepository.save(tempRecordSurcharge);
                return tempRecordSurcharge;
            } else {
                // 新增一条
                TempRecordSurcharge tempRecordSurcharge = new TempRecordSurcharge();
                tempRecordSurcharge.setPlaceId(placeId);
                tempRecordSurcharge.setCardId(cardId);
                tempRecordSurcharge.setDeductAccount(surchargeConfig.getDeductAccount());
                tempRecordSurcharge.setDeductMemberCard(surchargeConfig.getDeductMemberCard());
                tempRecordSurcharge.setDeductTempCard(surchargeConfig.getDeductTempCard());
                tempRecordSurcharge.setSourceType(getSourceType(Integer.parseInt(activeType)));
                tempRecordSurcharge.setCreated(LocalDateTime.now());
                tempRecordSurchargeRepository.save(tempRecordSurcharge);
                return tempRecordSurcharge;
            }
            // 查询当天已产生的附加费条数
//            LocalDateTime today_start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);// 当天开始时间
//            LocalDateTime today_end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX); // 当天结束时间
//            List<TempRecordSurcharge> tempRecordSurcharges = tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId,cardId,today_start,today_end);
//
//            if (tempRecordSurcharges.size() < 1) {
//                // 当天还没记录过，则新增一条
//                TempRecordSurcharge tempRecordSurcharge = new TempRecordSurcharge();
//                tempRecordSurcharge.setPlaceId(placeId);
//                tempRecordSurcharge.setCardId(cardId);
//                tempRecordSurcharge.setDeductAccount(surchargeConfig.getDeductAccount());
//                tempRecordSurcharge.setDeductMemberCard(surchargeConfig.getDeductMemberCard());
//                tempRecordSurcharge.setDeductTempCard(surchargeConfig.getDeductTempCard());
//                tempRecordSurcharge.setSourceType(getSourceType(Integer.parseInt(activeType)));
//                tempRecordSurcharge.setCreated(LocalDateTime.now());
//                tempRecordSurchargeRepository.save(tempRecordSurcharge);
//                return true;
//            }
//
//            Optional<TempRecordSurcharge> findUnfinishedSurcharge = tempRecordSurchargeRepository.findByPlaceIdAndCardIdAndDeletedAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId,cardId,0,today_start,today_end);
//            if (findUnfinishedSurcharge.isPresent()) {
//                // 如果有一条未使用的，则更新扣款信息
//                TempRecordSurcharge tempRecordSurcharge = findUnfinishedSurcharge.get();
//                tempRecordSurcharge.setSourceType(getSourceType(Integer.parseInt(activeType)));
//                tempRecordSurcharge.setDeductAccount(surchargeConfig.getDeductAccount());
//                tempRecordSurcharge.setDeductMemberCard(surchargeConfig.getDeductMemberCard());
//                tempRecordSurcharge.setDeductTempCard(surchargeConfig.getDeductTempCard());
//                tempRecordSurchargeRepository.save(tempRecordSurcharge);
//            }
        }
        return null;
    }

    @Transactional(rollbackOn = Exception.class)
    public void saveAll(List<TempRecordSurcharge> tempRecordSurchargeOpt) {
        tempRecordSurchargeRepository.saveAll(tempRecordSurchargeOpt);
    }
}
