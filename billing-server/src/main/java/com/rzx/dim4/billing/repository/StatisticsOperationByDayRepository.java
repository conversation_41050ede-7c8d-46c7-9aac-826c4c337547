package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.StatisticsOperationByDay;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface StatisticsOperationByDayRepository extends JpaRepository<StatisticsOperationByDay, Long>, JpaSpecificationExecutor<StatisticsOperationByDay> {

    @Query(value = "SELECT count_day AS countDay,"
            + "place_id AS placeId,"
            + "id AS id,"
            + "creater AS creater,"
            + "created AS created,"
            + "updated AS updated,"
            + "deleted AS deleted,"
            + "sum_member_topup_income AS sumMemberTopupIncome,"
            + "sum_temporary_topup_income AS sumTemporaryTopupIncome,"
            + "sum_client_topup_income AS sumClientTopupIncome,"
            + "sum_cash_topup_income AS sumCashTopupIncome,"
            + "sum_cashier_online_topup_income AS sumCashierOnlineTopupIncome,"
            + "sum_mp_topup_income AS sumMpTopupIncome,"

            + "sum_present_topup_total AS sumPresentTupupTotal,"
            + "sum_present_reversal_total AS sumPresentReversalTotal,"

            + "sum_present_income AS sumPresentIncome,"
            + "sum_total_income AS sumTotalIncome,"
            + "sum_total_reversal AS sumTotalReversal,"
            + "create_member_card_num AS createMemberCardNum,"
            + "create_temporary_card_num AS createTemporaryCardNum,"
            + "count_reversal AS countReversal,"
            + "count_topup AS countTopup,"
            + "count_refund AS countRefund,"
            + "count_package AS countPackage,"
            + "sum_package_total AS sumPackageTotal,"
            + "other_income AS otherIncome,"
            + "other_outcome AS otherOutcome,"
            + "sum_online_out_income AS sumOnlineOutIncome,"
            + "sum_app_topup_income AS sumAppTopupIncome,"
            + "sum_cash_out_income AS sumCashOutIncome FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds) ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> queryStatisticsOperation(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeIds") List<String> placeIds, Pageable pageable);

    @Query(value = "SELECT COUNT(id)" +
            "FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id  IN (:placeIds)", nativeQuery = true)
    int countStatisticsOperation(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                 @Param("placeIds") List<String> placeIds);

    @Query(value = "SELECT "
            + "SUM( sum_member_topup_income ) AS sumMemberTopupIncome,"
            + "SUM( sum_temporary_topup_income ) AS sumTemporaryTopupIncome,"
            + "SUM( sum_client_topup_income ) AS sumClientTopupIncome,"
            + "SUM( sum_cash_topup_income ) AS sumCashTopupIncome,"
            + "SUM( sum_cashier_online_topup_income ) AS sumCashierOnlineTopupIncome,"
            + "SUM( sum_mp_topup_income ) AS sumMpTopupIncome,"
            + "SUM( sum_present_topup_total ) AS sumPresentTupupTotal,"
            + "SUM( sum_present_reversal_total ) AS sumPresentReversalTotal,"
            + "SUM( sum_present_income ) AS sumPresentIncome,"
            + "SUM( sum_total_income ) AS sumTotalIncome,"
            + "SUM( sum_total_reversal ) AS sumTotalReversal,"
            + "SUM( create_member_card_num ) AS sumCreateMemberCardNum,"
            + "SUM( create_temporary_card_num ) AS sumCreateTemporaryCardNum,"
            + "SUM( count_reversal ) AS sumCountReversal,"
            + "SUM( count_topup ) AS sumCountTopup,"
            + "SUM( count_refund ) AS sumCountRefund,"
            + "SUM( count_package ) AS sumCountPackage,"
            + "SUM( sum_package_total ) AS sumPackageTotal,"
            + "SUM( other_income ) AS sumOtherIncome,"
            + "SUM( other_outcome ) AS sumOtherOutcome,"
            + "SUM( sum_online_out_income ) AS sumOnlineOutIncome, "
            + "SUM( sum_cash_out_income ) AS sumCashOutIncome, "
            + "SUM( sum_app_topup_income ) AS sumAppTopupIncome "
            + "FROM statistics_operation_by_day "
            + "WHERE STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds) ORDER BY count_day DESC", nativeQuery = true)
    Map<String, String> allQueryStatisticsOperation(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                    @Param("placeIds") List<String> placeIds);

    @Query(value = "SELECT count_day AS countDay,"
            + "sum(sum_total_income) AS sumTotalIncome FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds) GROUP BY countDay ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> querySumTotalIncomeByDay(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeIds") List<String> placeIds);

    @Query(value = "SELECT DATE_FORMAT(count_day, \"%Y-%m\") AS countDay,"
            + "sum(sum_total_income) AS sumTotalIncome FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") > :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds) GROUP BY DATE_FORMAT(count_day, \"%Y-%m\") ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> querySumTotalIncomeByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                         @Param("placeIds") List<String> placeIds);

    @Query(value = "SELECT sum( a.sumTotalIncome ) AS sumTotalIncome,"
            + "a.placeId AS placeId FROM ( SELECT "
            + "count_day AS countDay,"
            + "sum( sum_total_income ) AS sumTotalIncome,"
            + "place_id AS placeId FROM statistics_operation_by_day"
            + " where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds) GROUP BY countDay,placeId )a GROUP BY a.placeId ORDER BY sumTotalIncome DESC", nativeQuery = true)
    List<Map<String, String>> querySumTotalIncomeRank(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                      @Param("placeIds") List<String> placeIds);


    @Query(value = "SELECT DATE_FORMAT(count_day, \"%Y-%m\") AS countDay,"
            + "place_id AS placeId,"
            + "sum(sum_member_topup_income) AS sumMemberTopupIncome,"
            + "sum(sum_temporary_topup_income) AS sumTemporaryTopupIncome,"
            + "sum(sum_client_topup_income) AS sumClientTopupIncome,"
            + "sum(sum_cash_topup_income) AS sumCashTopupIncome,"
            + "sum(sum_cashier_online_topup_income) AS sumCashierOnlineTopupIncome,"
            + "sum(sum_mp_topup_income) AS sumMpTopupIncome,"

            + "sum(sum_present_topup_total) AS sumPresentTupupTotal,"
            + "sum(sum_present_reversal_total) AS sumPresentReversalTotal,"

            + "sum(sum_present_income) AS sumPresentIncome,"
            + "sum(sum_total_income) AS sumTotalIncome,"
            + "sum(sum_total_reversal) AS sumTotalReversal,"
            + "sum(create_member_card_num) AS createMemberCardNum,"
            + "sum(create_temporary_card_num) AS createTemporaryCardNum,"
            + "sum(count_reversal) AS countReversal,"
            + "sum(count_topup) AS countTopup,"
            + "sum(count_refund) AS countRefund,"
            + "sum(count_package) AS countPackage,"
            + "sum(sum_package_total) AS sumPackageTotal,"
            + "sum(other_income) AS otherIncome,"
            + "sum(other_outcome) AS otherOutcome,"
            + "sum(sum_online_out_income) AS sumOnlineOutIncome,"
            + "sum(sum_app_topup_income) AS sumAppTopupIncome,"
            + "sum(sum_cash_out_income) AS sumCashOutIncome FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id IN (:placeIds)  GROUP BY DATE_FORMAT(count_day, \"%Y-%m\") ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> queryStatisticsOperationByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeIds") List<String> placeIds, Pageable pageable);


    @Query(value = "SELECT COUNT(countDay) FROM (SELECT DATE_FORMAT(count_day, \"%Y-%m\") AS countDay  " +
            "FROM statistics_operation_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id  IN (:placeIds) GROUP BY countDay)a", nativeQuery = true)
    int countStatisticsOperationByMonth(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                        @Param("placeIds") List<String> placeIds);
}
