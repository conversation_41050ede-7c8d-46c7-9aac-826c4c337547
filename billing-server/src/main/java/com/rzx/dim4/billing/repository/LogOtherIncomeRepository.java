package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogOtherIncome;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface LogOtherIncomeRepository extends JpaRepository<LogOtherIncome, Long>, JpaSpecificationExecutor<LogOtherIncome> {

    /**
     * 按班次-类型-统计金额
     *
     * @param placeId
     * @param shiftId
     * @param type
     * @return
     */
    @Query("select sum(lo.amount) from LogOtherIncome lo where lo.placeId=?1 and lo.shiftId=?2 and lo.type=?3 and lo.deleted=0 ")
    Integer sumIncomeByShiftId(String placeId, String shiftId, int type);

    Optional<LogOtherIncome> findTop1ByPlaceIdOrderByIdDesc(String placeId);

    Optional<LogOtherIncome> findByPlaceIdAndLogOtherIncomeId(String placeId, String logOtherIncomeId);

}
