package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.LogOperationBO;
import com.rzx.dim4.base.bo.place.PlaceShiftBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.VerifyParam;
import com.rzx.dim4.billing.entity.LogOperation;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogOperationService;
import com.rzx.dim4.billing.service.LogShiftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收银台分页查询交班页面操作记录信息(因209接口不能复用，新加接口)
 */
@Service
@Slf4j
public class CashierQueryPageShiftLogOperationServiceImpl implements CoreService {

    @Autowired
    LogOperationService logOperationService;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    LogShiftService logShiftService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() < 7) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0);
        String shiftId = params.get(1); // 班次ID
        String startStr = params.get(2);
        String pageSizeStr = params.get(3);
        String operationType = params.get(4);
        String idName = params.get(5);
        String idNumber = params.get(6);

        int start = 0;
        int pageSize = 100;
        try {
            start = Integer.parseInt(startStr);
            pageSize = Integer.parseInt(pageSizeStr);
            if (pageSize > 100) {
                pageSize = 100;
            }
        } catch (Exception e) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Map<String, Object> queryMap = new HashMap<>();
        if (params.size() >= 9) {
            String startTime = params.get(7);
            String endTime = params.get(8);

            queryMap.put("startDate", startTime);
            queryMap.put("endDate", endTime);
        } else {
            // 班次对应的时间
            GenericResponse<ObjDTO<PlaceShiftBO>> shiftBO = placeServerService.findWorkingShiftByShiftId(placeId , shiftId);
            if (shiftBO.isResult()) {
                PlaceShiftBO placeShiftBO = shiftBO.getData().getObj();
                queryMap.put("startDate", placeShiftBO.getWorkingTime().toString().substring(0, 19).replaceAll("T", " "));
                queryMap.put("endDate", ObjectUtils.isEmpty(placeShiftBO.getOffWorkingTime()) ? LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " ") : placeShiftBO.getOffWorkingTime().toString().substring(0, 19).replaceAll("T", " "));
            } else {
                // 默认查询近三个月的
                queryMap.put("startDate", LocalDateTime.now().minusMonths(2).toString().substring(0, 19).replaceAll("T", " "));
                queryMap.put("endDate", LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " "));
            }
        }

        queryMap.put("placeId", placeId);
        queryMap.put("shiftId", shiftId);
        if (operationType.contains(",")) {
            queryMap.put("operationTypeArr", Arrays.asList(operationType.split(",")));
        } else {
            queryMap.put("operationType", operationType);
        }

        // 姓名筛选
        if (!StringUtils.isEmpty(idName)) {
            queryMap.put("idName", idName);
        }

        // 证件号筛选
        if (!StringUtils.isEmpty(idNumber)) {
            queryMap.put("cashierIdNumber", idNumber);
        }

        // 2023.11.09新增查询参数
        // --------------start
        String orderParam = "desc,id"; // 需要排序的参数进行排序，如“desc,id”,逗号前是代表升序还是降序，逗号后代表要排序的字段
        String minCashBalance= ""; // 本金金额最小值
        String maxCashBalance = ""; // 本金金额最大值
        String minPresent = ""; // 赠送金额最小值
        String maxPresent = ""; // 赠送金额最大值
        String sourceType = ""; // 来源
        String createrName = ""; //操作人
        String cashierId = ""; //收银台ID
        String shiftMark = "";// 班次标记;参数内容是currentShift代表本班，lastShift代表上一班次；当本班的时候shiftId是有值不为空的，如果是last,那么需要自己去查询上一班次信息

        if (params.size() == 18) {
            minCashBalance = params.get(9);
            maxCashBalance = params.get(10);
            minPresent = params.get(11);
            maxPresent = params.get(12);
            if (!org.springframework.util.StringUtils.isEmpty(params.get(13))){
                orderParam = params.get(13);
            }
            sourceType = params.get(14);
            createrName = params.get(15);
            shiftMark = params.get(16);
            cashierId = params.get(17);
        }
        queryMap.put("minCashBalance", minCashBalance);
        queryMap.put("maxCashBalance", maxCashBalance);
        queryMap.put("minPresent", minPresent);
        queryMap.put("maxPresent", maxPresent);
        queryMap.put("sourceType",sourceType);
        queryMap.put("createrName",createrName);
        queryMap.put("cashierId",cashierId);

        // 如果班次标识是要查寻上一班次，那么这时候shifitId是空的，需要自己去获取
        if(!StringUtils.isEmpty(shiftMark)) {
            if("lastShift".equals(shiftMark)){
                String lastShiftId = "";// 上一班次Id
                Optional<LogShift> optLogShift = logShiftService.findTop1ByPlaceIdAndCashierIdAndStatusOrderByIdDesc(placeId, cashierId,1);// 从多个历史班次选最近的一个班次，作为上一个班次
                if (!optLogShift.isPresent()) {
                    return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
                }
                lastShiftId = optLogShift.get().getShiftId();
                // 如果查询的是上一班次，那么需要重新put shittId
                queryMap.put("shiftId", lastShiftId);

                // 上一个班次对应的时间
                GenericResponse<ObjDTO<PlaceShiftBO>> shiftBO = placeServerService.findWorkingShiftByShiftId(placeId , lastShiftId);
                if (shiftBO.getCode() != ServiceCodes.NO_ERROR.getCode()) {
                    return shiftBO;
                }
                if (shiftBO.isResult()) {
                    PlaceShiftBO placeShiftBO = shiftBO.getData().getObj();
                    queryMap.put("startDate", placeShiftBO.getWorkingTime().toString().substring(0, 19).replaceAll("T", " "));
                    queryMap.put("endDate", placeShiftBO.getOffWorkingTime().toString().substring(0, 19).replaceAll("T", " "));
                    queryMap.put("shiftIdParam",lastShiftId);
                }
            }else if ("currentShift".equals(shiftMark)){
                // 如果是查询当前班次，那么shiftId 一定不为空
                Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndCashierIdAndStatus(placeId, cashierId,0);
                if (!optLogShift.isPresent()) {
                    return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
                }
                shiftId = optLogShift.get().getShiftId();
                GenericResponse<ObjDTO<PlaceShiftBO>> shiftBO = placeServerService.findWorkingShiftByShiftId(placeId , shiftId);
                if (shiftBO.getCode() != ServiceCodes.NO_ERROR.getCode()) {
                    return shiftBO;
                }
                if (shiftBO.isResult()) {
                    PlaceShiftBO placeShiftBO = shiftBO.getData().getObj();
                    queryMap.put("startDate", placeShiftBO.getWorkingTime().toString().substring(0, 19).replaceAll("T", " "));
                    queryMap.put("endDate", LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " "));
                    queryMap.put("shiftIdParam",shiftId);
                }
            }
        }

        String order = "desc";
        String[] orderColumns = new String[]{"id"};
        String[] orderData = new String[]{};
        try {
            if (!org.springframework.util.StringUtils.isEmpty(orderParam)) {
                orderData = orderParam.split(",");
            }
        } catch (Exception e) {
            log.error("排序参数截取错误", e);
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if(!org.springframework.util.StringUtils.isEmpty(orderData[0])){
            order = orderData[0];
        }
        if(!org.springframework.util.StringUtils.isEmpty(orderData[1])){
//            orderSplit = orderData[1];
            orderColumns = new String[]{orderData[1]};
        }
        // --------------end
        if (queryMap.containsKey("operationType") && (queryMap.get("operationType").equals(OperationType.CREATE_CARD.name()) ||
                queryMap.get("operationType").equals(OperationType.REVERSAL.name()) ||
                queryMap.get("operationType").equals(OperationType.PRESENT.name()) ||
                queryMap.get("operationType").equals(OperationType.CANCELLATION.name()) ||
                queryMap.get("operationType").equals(OperationType.SURCHARGE.name()))) {
            // 收银台交班弹出的冲正、开卡、网费赠送、销卡列表记录不能过滤cost>0,其他情况都需要过滤
        } else if (queryMap.containsKey("operationTypeArr")) {
            // 收银台计费订单的冲正、开卡、网费赠送列表记录不能过滤cost>0,其他情况都需要过滤
        } else {
            queryMap.put("searchMoney", "searchMoney");
        }

        Pageable pageable = PageRequest.of(start, pageSize, Sort.Direction.fromString(order), orderColumns);
        long now = System.currentTimeMillis();
        //临时添加控制：如果查询条件的时间段大于2个月，改为2个月
        if(queryMap.containsKey("startDate") && !StringUtils.isEmpty(queryMap.get("startDate").toString().trim())
                && queryMap.containsKey("endDate")&& !StringUtils.isEmpty(queryMap.get("endDate").toString().trim())  ){
            LocalDateTime startDate = LocalDateTime.parse(queryMap.get("startDate").toString().substring(0, 19).replaceAll("T", " "), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endDate = LocalDateTime.parse(queryMap.get("endDate").toString().substring(0, 19).replaceAll("T", " "), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            try {
                VerifyParam.checkTimeDifferenceByMonth(startDate,endDate);
            } catch (ServiceException e){
                log.info("{} 查询班次列表详情，时间段大于2个月，自动调整为2个月",placeId);
                queryMap.put("startDate",endDate.minusMonths(1).toString().substring(0, 19).replaceAll("T", " "));
            }
        }
        Page<LogOperation> page = logOperationService.findAll(queryMap, pageable);
        log.info("场所编码:::::::" + placeId + "收银台0X246接口分页查询********************耗时:::ms" + (System.currentTimeMillis() - now));
        List<LogOperationBO> bos =  page.getContent().stream().map(e -> {
            return e.toBO();
        }).collect(Collectors.toList());


        return new GenericResponse<>(new PagerDTO<>((int) page.getTotalElements(), bos));
    }
}
