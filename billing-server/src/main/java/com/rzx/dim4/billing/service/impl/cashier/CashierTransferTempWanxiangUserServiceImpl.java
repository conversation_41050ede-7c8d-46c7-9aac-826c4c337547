package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.TempWanxiangUser;
import com.rzx.dim4.billing.service.BillingCardService;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogShiftService;
import com.rzx.dim4.billing.service.TempWanxiangUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 将万象临时用转成计费卡
 */
@Service
public class CashierTransferTempWanxiangUserServiceImpl implements CoreService {

    @Autowired
    TempWanxiangUserService tempWanxiangUserService;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    LogShiftService logShiftService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() > 10) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardIds = params.get(2); // 万象用户ID，逗号分割
        String cardTypeId = params.get(3); // 卡类型ID
        String cardTypenName = params.get(4); // 卡类型姓名
        String idNumber = params.get(5).toUpperCase(); // 身份证号码
        String name = params.get(6); // 姓名
        String activeType = params.get(7); // 激活类型
        String phoneNumber = params.get(8); // 手机号
        String remark = params.get(9); // 备忘

        int addCashAccount = 0; // 增加的余额
        int addPresentAccount = 0; // 增加的赠送
        int addPoint = 0; // 增加的积分

        LocalDateTime now = LocalDateTime.now();

        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = optLogShift.get();

        List<String> cardIdList = Arrays.asList(cardIds.split(","));
        List<TempWanxiangUser> wanxiangUserList = tempWanxiangUserService.findByPlaceIdAndCardIdIn(placeId, cardIdList);
        List<TempWanxiangUser> delUserList = new ArrayList<>();

        for (TempWanxiangUser t : wanxiangUserList) {
            if (t.getRemainCashAccount() == 1) {
                addCashAccount += t.getRemain();
            } else {
                addPresentAccount += t.getRemain();
            }
            addPoint += t.getHeapCanUse();
            t.setDeleted(1);
            t.setUpdated(now);
            delUserList.add(t);
        }

        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (optBillingCard.isPresent()) {
            BillingCard billingCard = optBillingCard.get();
            billingCard.setPresentAccount(billingCard.getPresentAccount() + addPresentAccount);
            billingCard.setCashAccount(billingCard.getCashAccount() + addCashAccount);
            billingCard.setPoints(billingCard.getPoints() + addPoint);
            billingCard.setRemark(billingCard.getRemark() + "[来自WX]");
            billingCard = billingCardService.save(billingCard);
            tempWanxiangUserService.saveAll(delUserList);
            return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
        }

//        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder(); // 密码加密工具

        BillingCard billingCard = new BillingCard();
        billingCard.setCreated(now); // 创建时间
        billingCard.setPlaceId(placeId);
        billingCard.setIdNumber(idNumber);
        billingCard.setCashAccount(addCashAccount);
        billingCard.setPresentAccount(addPresentAccount);
        billingCard.setCardTypeId(cardTypeId);
        billingCard.setCardTypeName(cardTypenName);
        billingCard.setIdName(name);
        billingCard.setLoginName(idNumber);
//        billingCard.setLoginPass(passwordEncoder.encode("123456"));
        billingCard.setLoginPass("123456");//新版本不再加密
        billingCard.setPhoneNumber(phoneNumber);
        billingCard.setRemark(remark + "[来自WX]");
        billingCard.setActiveTime(now); // 激活时间
        int activeTypeInt = 0;
        try {
            activeTypeInt = Integer.parseInt(activeType);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        billingCard.setActiveType(ActiveType.getActiveTypes(activeTypeInt)); // 激活方式
        billingCard.setCreater(StringUtils.isEmpty(logShift) ? 0 : Long.parseLong(logShift.getAccountId()));
        billingCard.setUpdated(now);
        billingCard.setDeleted(0);
        billingCard.setChainCard(0);
        billingCard.setPoints(addPoint);
        billingCard = billingCardService.save(billingCard);

        tempWanxiangUserService.saveAll(delUserList);

        return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
    }

}
