package com.rzx.dim4.billing.service.third;

import com.google.gson.Gson;
import com.rzx.dim4.base.cons.BaseConstants;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.entity.third.ThirdAuthority;
import com.rzx.dim4.billing.repository.third.ThirdAccountRepository;
import com.rzx.dim4.billing.repository.third.ThirdAuthorityRepository;
import com.rzx.dim4.billing.service.PlaceBizConfigService;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;

import static com.rzx.dim4.base.cons.BaseConstants.*;

@Slf4j
@Service
public class ThirdAccountService {

    @Autowired
    ThirdAccountRepository thirdAccountRepository;

    @Autowired
    ThirdAuthorityRepository thirdAuthorityRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    public Page<ThirdAccount> findAll(Map<String, String> map, Pageable pageable) {
        return thirdAccountRepository.findAll(new Specification<ThirdAccount>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<ThirdAccount> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> andPredicateList = new ArrayList<>();
//              DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 网吧id
                if (map.containsKey("thirdAccountId") && !StringUtils.isEmpty(map.get("thirdAccountId"))) {
                    andPredicateList
                            .add(cb.equal(root.get("thirdAccountId").as(String.class), map.get("thirdAccountId")));
                }
                if (map.containsKey("name") && !StringUtils.isEmpty(map.get("name"))) { // 是否查询在线订单
                    andPredicateList.add(cb.notEqual(root.get("name").as(String.class), map.get("name")));
                }

                // 用户卡身份证号
                if (map.containsKey("status") && !StringUtils.isEmpty(map.get("status"))) {
                    andPredicateList.add(cb.equal(root.get("status").as(int.class), map.get("status")));
                }

                Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
                return cb.and(andPredicateList.toArray(andPredicateArr));
            }
        }, pageable);

    }

    /**
     * 根据账号id查询调用用户信息
     *
     * @param thirdAccountId
     * @return
     */
    public Optional<ThirdAccount> findByThirdAccountId(String thirdAccountId) {
        return thirdAccountRepository.findByThirdAccountId(thirdAccountId);
    }

    public ThirdAccount getThirdAccount(String thirdAccountId) {
        Optional<ThirdAccount> thirdAccountOpt = thirdAccountRepository.findByThirdAccountId(thirdAccountId);
        return thirdAccountOpt.orElseThrow(() -> {
            log.warn("thirdAccountId:{} not found", thirdAccountId);
            return new ServiceException(ServiceCodes.BAD_PARAM);
        });
    }

    public String getSecretKey(String thirdAccountId) {
        Optional<ThirdAccount> thirdAccountOpt = thirdAccountRepository.findByThirdAccountId(thirdAccountId);
        return thirdAccountOpt.orElseThrow(() -> {
            log.warn("thirdAccountId:{} not found", thirdAccountId);
            return new ServiceException(ServiceCodes.BAD_PARAM);
        }).getSecretKey();
    }

    /**
     * 新增/修改用户
     *
     * @param thirdAccount
     * @return
     */
    public ThirdAccount save(ThirdAccount thirdAccount) {

        if (StringUtils.isEmpty(thirdAccount.getThirdAccountId())) {
            thirdAccount.setThirdAccountId(builderThirdAccountId());
        }
        return thirdAccountRepository.save(thirdAccount);
    }

    @Synchronized
    private synchronized String builderThirdAccountId() {
        String thirdAccountId = Dim4StringUtils.generateCode(8);
        Optional<ThirdAccount> lastThirdAccountId = thirdAccountRepository.findTop1ByOrderByIdDesc();
        if (lastThirdAccountId.isPresent()) {
            String lastestThirdAccountId = lastThirdAccountId.get().getThirdAccountId();
            if (lastestThirdAccountId.length() < 8) {
                lastestThirdAccountId = Dim4StringUtils.generateCode(8);
            }
            thirdAccountId = String.valueOf(Integer.parseInt(lastestThirdAccountId)
                    + Integer.parseInt(Dim4StringUtils.generateCode(2)));
        }
        return thirdAccountId;
    }

    /**
     * 获取来源
     *
     * @param thirdName
     * @return
     */
    public SourceType getType(String thirdName) {
        switch (thirdName) {
            case THIRD_TYPE_JWELL:
                return SourceType.JWELL;
            case THIRD_TYPE_MARKET:
                return SourceType.MARKET;
            case THIRD_DA_BA_ZHANG:
                return SourceType.DABAZHANG;
            case THIRD_TYPE_YI_SHANG_WANG:
                return SourceType.YISHANGWANG;
            default:
                return SourceType.OTHER;
        }
    }

    /**
     * 权限校验
     *
     * @param thirdAccountId
     * @param authorityValue
     * @return
     */
    public boolean checkAuthority(String thirdAccountId, int authorityValue) {
        Optional<ThirdAuthority> thirdAuthorityOpt = thirdAuthorityRepository.findByThirdAccountId(thirdAccountId);
        if (!thirdAuthorityOpt.isPresent()) {
            return false;
        }
        ThirdAuthority thirdAuthority = thirdAuthorityOpt.get();
        if (!thirdAuthority.getOpAuthority().contains(String.valueOf(authorityValue))) {
            return false;
        }
        return true;
    }

    public ThirdAccount getThirdAccountByPlaceId(String placeId) {
        String key = BaseConstants.KEY_THIRD_ACCOUNT_BY_PLACEID + placeId;

        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))) {
            String s = stringRedisTemplate.opsForValue().get(key);
            return new Gson().fromJson(s, ThirdAccount.class);
        }

        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        String thirdAccountId = placeBizConfig.getThirdAccountId();

        if (!StringUtils.isEmpty(thirdAccountId)) {
            Optional<ThirdAccount> thirdAccountOptional = thirdAccountRepository.findByThirdAccountId(thirdAccountId);
            return thirdAccountOptional.orElseThrow(() -> new ServiceException(ServiceCodes.ACCOUNT_ERROR));
        } else {
            throw new ServiceException(ServiceCodes.ACCOUNT_NO_BOUND);
        }
    }

    /**
     * 获取所有第三方账号
     *
     * @return 所有账号
     */
    public List<ThirdAccount> findAll() {
        return thirdAccountRepository.findAll();
    }

    public List<ThirdAccount> findByThirdAccountIds(List<String> thirdAccountIds) {
        return thirdAccountRepository.findByThirdAccountIdInAndStatusAndDeleted(thirdAccountIds, 0, 0);
    }
}
