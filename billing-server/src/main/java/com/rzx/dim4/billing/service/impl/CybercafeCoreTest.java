package com.rzx.dim4.billing.service.impl;

import com.google.common.hash.Hashing;
import org.apache.commons.lang.StringUtils;

/**
 * 核心接口的解释工具类，运行后输出请求参数，使用curl或者postman进行测试
 */
public class CybercafeCoreTest {

    @SuppressWarnings("deprecation")
	public static void main(String[] args) {

        // 参数设置开始 
    	// int serviceIndex = 260;// 客户端结账
        // int serviceIndex = 515;// 收银台结账
        // int serviceIndex = 259;// 客户端登录
    	int serviceIndex = 276; //客户端获取二维码（新）
    	
        String placeId = "42010120000044";
        String identifier = "0A2AF11A6C4F0F84";
        //String[] params = {};
        String[] params = {"4001", "2001"};
        
        // 参数设置结束

        boolean defaultPalceId = "00000000000000".equals(placeId);

        // 头部标识
        StringBuffer requestBody = new StringBuffer("4W");

        // 接口序号
        String serviceIndexHex = Integer.toHexString(serviceIndex);
        requestBody.append(StringUtils.leftPad(serviceIndexHex, 3, "0"));

        // 时间戳
        Long timestap = System.currentTimeMillis() / 1000;
        requestBody.append(String.valueOf(timestap));

        // 场所ID
        requestBody.append(placeId);

        // 业务参数（最后一个有值入参前，参数没有值的参数都为空，用00占位）
        int indexOfLastHaveValue = -1;
        for (int index = 0; index < params.length; index++) {
            if (!StringUtils.isEmpty(params[index])) {
                indexOfLastHaveValue = index;
            }
        }
        for (int index = 0; index < params.length; index++) {
            if (index < indexOfLastHaveValue || index == indexOfLastHaveValue) {
                if (StringUtils.isEmpty(params[index])) {
                    requestBody.append("00");
                } else {
                    int len = params[index].length();
                    String lenHex = Integer.toHexString(len);
                    requestBody.append(StringUtils.leftPad(lenHex, 2, "0"));
                    requestBody.append(params[index]);
                }
            }
        }

        // 签名
        if (defaultPalceId) {
            String sign = Hashing.md5().hashBytes((requestBody.toString()).getBytes()).toString().substring(8, 24);
            requestBody.append(sign.toUpperCase());
        } else {
            String sign = Hashing.md5().hashBytes((requestBody.toString() + identifier).getBytes()).toString().substring(8,
                    24);
            requestBody.append(sign.toUpperCase());
        }

        System.out.println(requestBody.toString());
    }
}
