package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.ExchangePointsRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ExchangePointsRuleRepository extends JpaRepository<ExchangePointsRule, Long>, JpaSpecificationExecutor<ExchangePointsRule> {

    Optional<ExchangePointsRule> findTop1ByOrderByIdDesc();

    Optional<ExchangePointsRule> findByPlaceIdAndExchangePointsRuleIdAndDeleted(String placeId, String exchangePointsRuleId, int deleted);

    List<ExchangePointsRule> findByPlaceIdAndExchangeTypeAndDeleted(String placeId, int exchangeType, int deleted);

    Optional<ExchangePointsRule> findByPlaceIdAndExchangeTypeAndExchangePresentAndDeleted (String placeId, int exchangeType, int exchangePresent, int deleted);

    Optional<ExchangePointsRule> findByPlaceIdAndExchangeTypeAndExchangePointsAndDeleted (String placeId, int exchangeType, int exchangePoints, int deleted);

}
