package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.BillingOnline;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 计费卡登录逻辑
 *
 * <AUTHOR>
 * @since 2023/10/26
 **/
@Slf4j
@Service
public class BillingCardLoginServiceImpl implements BillingCardLoginService {

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private BillingCardRepository billingCardRepository;


    @Autowired
    private BillingOnlineService billingOnlineService;

    /**
     * 校验是否允许在客户端直接扫码激活登录
     *
     * @param placeId
     * @param idNumber
     * @param cardId
     */
    @Override
    public void checkForbiddenClientActiveDirectly(String placeId, String idNumber, String cardId) {
        log.info("checkForbiddenClientActiveDirectly, placeId={}, idNumber={}, cardId={}", placeId, idNumber, cardId);

        PlaceBizConfig placeBizConfig = getPlaceBizConfig(placeId);
        int forbiddenClientActiveDirectly = placeBizConfig.getForbiddenClientActiveDirectly();
        boolean forbiddenClientActiveDirectlyFlag = forbiddenClientActiveDirectly == 1;
        log.info("forbiddenClientActiveDirectlyFlag={}", forbiddenClientActiveDirectlyFlag);

        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (forbiddenClientActiveDirectlyFlag && !billingOnlineOpt.isPresent()) {
            BillingCard billingCard = getBillingCard(placeId, idNumber, cardId);
            if (null == billingCard) {
                log.info("没有计费卡，需要去收银台开卡激活");
                throw new ServiceException(ServiceCodes.BILLING_NEED_ACTIVE_IN_CASHIER);
            }
            LocalDateTime activeTime = billingCard.getActiveTime();

            if (activeTime == null || activeTime.plusMinutes(30).isBefore(LocalDateTime.now())) {
                throw new ServiceException(ServiceCodes.BILLING_NEED_ACTIVE_IN_CASHIER);
            }
        }
    }


    /**
     * 有返回一定不为空
     * @param placeId 场所id
     * @return
     */
    private PlaceBizConfig getPlaceBizConfig(String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
        return placeBizConfigService.findByPlaceId(placeId);
    }

    /**
     * 有返回一定不为空
     * @param placeId 场所id
     * @param idNumber 身份证
     * @param cardId 计费卡
     * @return
     */
    private BillingCard getBillingCard(String placeId, String idNumber, String cardId) {
        if (StringUtils.isEmpty(placeId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        if (StringUtils.isEmpty(idNumber) && StringUtils.isEmpty(cardId)) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        BillingCard billingCard = null;
        if (!StringUtils.isEmpty(idNumber)) {
            Optional<BillingCard> billingCardOptional = billingCardRepository.findByPlaceIdAndIdNumber(placeId, idNumber);
            if (billingCardOptional.isPresent()) {
                billingCard = billingCardOptional.get();
            }
        }

        if (!StringUtils.isEmpty(cardId)) {
            Optional<BillingCard> billingCardOptional = billingCardRepository.findByPlaceIdAndIdcardNum(placeId, cardId);
            if (billingCardOptional.isPresent()) {
                billingCard = billingCardOptional.get();
            }
        }

        return billingCard;
    }
}
