package com.rzx.dim4.billing.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.LogHb;
import com.rzx.dim4.billing.repository.LogHbRepository;

/**
 * <AUTHOR>
 * @date Jul 9, 2020 5:27:01 PM
 */
@Service
public class LogHbService {

	@Autowired
	LogHbRepository logHbRepository;

	public LogHb save(LogHb logHb) {
		return logHbRepository.save(logHb);
	}

	public void stopBilling(String placeId, String clientId) {
		logHbRepository.stopBilling(placeId, clientId);
	}

	public void startBilling(String placeId, String clientId) {
		logHbRepository.startBilling(placeId, clientId);
	}

	public Optional<LogHb> findByPlaceIdAndClientId(String placeId, String clientId) {
		return logHbRepository.findByPlaceIdAndClientId(placeId, clientId);
	}

	public int updateHeartBeatTime(String placeId, String clientId) {
		return logHbRepository.updateHeartBeatTime(placeId, clientId);
	}

	public int updateStatus(String placeId, String clientId, int status) {
		return logHbRepository.updateStatus(placeId, clientId, status);
	}

	public int updateLostContactStatus(String placeId, String clientId) {
		return updateStatus(placeId, clientId, 5); // 5 断开链接
	}

	public int countOnlineByPlaceId(String placeId) {
		return logHbRepository.countByPlaceIdAndBillingFlag(placeId, 1);
	}
	
	public int countOnline() {
		return logHbRepository.countByBillingFlag(1);
	}

}
