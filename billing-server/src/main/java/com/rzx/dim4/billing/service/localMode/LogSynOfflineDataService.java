package com.rzx.dim4.billing.service.localMode;

import com.rzx.dim4.billing.entity.localMode.LogSynOfflineData;
import com.rzx.dim4.billing.repository.localMode.LogSynOfflineDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class LogSynOfflineDataService {

    @Autowired
    LogSynOfflineDataRepository logSynOfflineDataRepository;

    public Optional<LogSynOfflineData> findByOfflineDataId (String offlineDataId) {
        return logSynOfflineDataRepository.findByOfflineDataId(offlineDataId);
    }

    public LogSynOfflineData save (LogSynOfflineData logSynOfflineData) {
        return logSynOfflineDataRepository.save(logSynOfflineData);
    }

}
