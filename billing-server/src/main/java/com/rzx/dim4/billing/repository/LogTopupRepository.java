package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.billing.entity.LogTopup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date Jul 13, 2020 3:12:14 PM
 */
public interface LogTopupRepository extends JpaRepository<LogTopup, Long> , JpaSpecificationExecutor<LogTopup> {

	Optional<LogTopup> findByOrderIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String orderId, LocalDateTime startDateTime,LocalDateTime endDateTime);
	
	Optional<LogTopup> findByPlaceIdAndOrderIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, String orderId, LocalDateTime startDateTime,LocalDateTime endDateTime);

	Page<LogTopup> findAll(Specification<LogTopup> logTopupSpecification, Pageable pageable);

	@Query(value = "SELECT SUM(cash_amount) AS sumCashTopup,\n" + "SUM(present_amount) AS sumPresentTopup \n" + "FROM \n" + "log_topup \n"
			+ "WHERE\n" + "place_id = :placeId \n"
			+ "AND created >= :startDate AND created <= :endDate \n"
			+ "AND refund_status != 2 \n"
			+ "AND if(:idNumber = '' OR :idNumber is null ,1=1, id_number = :idNumber) \n"
			+ "AND if(:cardTypeId = '' OR :cardTypeId is null ,1=1, card_type_id = :cardTypeId) \n"
			+ "AND if(:operatorName = '' OR :operatorName is null ,1=1, operator_name like :operatorName) \n"
			+ "AND if(:idName = '' OR :idName is null ,1=1, id_name like :idName) \n"
			+ "AND if(:status = '' OR :status is null ,1=1, status = :status) \n"
			+ "AND if(:orderId = '' OR :status is null ,1=1, order_id like :orderId) \n"
			+ "AND if(:ldOrderId = '' OR :status is null ,1=1, ld_order_id = :ldOrderId) \n"
			+ "AND if(:payTypeNotEqual = '' OR :payTypeNotEqual is null ,1=1, pay_type != :payTypeNotEqual) \n"
			+ "AND if(:sourceType = '' OR :sourceType is null ,1=1, source_type = :sourceType) \n"
			+ "AND if(:statusNotEqual = '' OR :statusNotEqual is null ,1=1, status != :statusNotEqual) \n", nativeQuery = true)
	Map<String, String> querySumCashTopupAndPresentTopup(@Param("placeId") String placeId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("idNumber") String idNumber,
														 @Param("cardTypeId") String cardTypeId, @Param("operatorName") String operatorName, @Param("idName") String idName,
														 @Param("status") String status, @Param("orderId") String orderId, @Param("ldOrderId") String ldOrderId, @Param("payTypeNotEqual") String payTypeNotEqual,
														 @Param("sourceType") String sourceType, @Param("statusNotEqual") String statusNotEqual);

	List<LogTopup> findByPlaceIdAndLdOrderIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, List<String> ldorderIds, LocalDateTime startDateTime,LocalDateTime endDateTime);

	/**
	 * 按时间-卡号-总充值
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id=?1 and lo.card_id=?2 and lo.status=3 and lo.created >= ?3 and lo.created <= ?4  and lo.topup_source_type in(?5)", nativeQuery = true)
	Integer sumCostCashAmountByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,LocalDateTime endDateTime,List<Integer> topupSourceTypes);

	/**
	 * 按时间-场所id-总充值
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id= ?1 and lo.status=3 and lo.created >= ?2 and lo.created <= ?3  and lo.topup_source_type in(?4)", nativeQuery = true)
	Integer sumCostCashAmountByPlaceIdAndDateTime(String placeId,  LocalDateTime startDateTime,LocalDateTime endDateTime,List<Integer> topupSourceTypes);

	/**
	 * 按时间-总充值
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id in (?1)  and lo.status=3 and lo.created >= ?2 and lo.created <= ?3", nativeQuery = true)
	Integer sumTopupAmountByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
												 LocalDateTime endDateTime);

	/**
	 * 按时间--现金--总充值
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id in (?1)  and lo.status=3 and lo.pay_type = 'CASH' and lo.created >= ?2 and lo.created <= ?3", nativeQuery = true)
	Integer sumCashAmountByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-现金总收入
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id = ?1  and lo.status=3 AND lo.pay_type = 'CASH'  and lo.created >= ?2 and lo.created <= ?3  ", nativeQuery = true)
	Integer sumCashAmountByDateTime(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	Optional<LogTopup> findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndCashAmountAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, List<PayType> payTypeList,int cashAmount,int status, LocalDateTime startDateTime, LocalDateTime endDateTime);
	Optional<LogTopup> findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, List<PayType> payTypeList,int status, LocalDateTime startDateTime, LocalDateTime endDateTime);
	Optional<LogTopup> findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndIdNotInAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, List<PayType> payTypeList, List<Long> idList,int status, LocalDateTime startDateTime, LocalDateTime endDateTime);
	/**
	 * 按班次--现金--总充值
	 *
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id = ?1 and lo.shift_id = ?2  and lo.status=3 and lo.pay_type = 'CASH' and lo.created >= ?3 and lo.created <= ?4 ", nativeQuery = true)
	Integer sumCashAmountByPlaceIdAndShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按班次--线上--总充值
	 *
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id = ?1 and lo.shift_id = ?2  and lo.status=3 AND lo.pay_type NOT IN ('CASH','BILLING_CARD','LOSS','MARKET_PAY','JWELL_PAY') and lo.created >= ?3 and lo.created <= ?4", nativeQuery = true)
	Integer sumOnlineAmountByPlaceIdAndShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-线上总收入
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id = ?1 and lo.created >= ?2 and lo.created <= ?3  and lo.status=3 AND lo.pay_type NOT IN ('CASH','BILLING_CARD','LOSS','MARKET_PAY','JWELL_PAY')", nativeQuery = true)
	Integer sumOnlineAmountByDateTime(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime);



//	/**
//	 * 按时间-总收入
//	 *
//	 * @param startDateTime
//	 * @param endDateTime
//	 * @return
//	 */
//	@Query(value = "select sum(cash_amount) from log_topup WHERE place_id in ?1 AND status = 3 AND created >= ?2 AND created <= ?3", nativeQuery = true)
//	Integer sumAmountTotalIncomeByDateTimeAndPlaceIds(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-总收入
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(cash_amount) from log_topup WHERE place_id = ?1 AND status = 3 AND created >= ?2 AND created <= ?3", nativeQuery = true)
	Integer sumAmountTotalIncomeByDateTimeAndPlaceId(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

//	/**
//	 * 按 班次\场所列表-总收入
//	 *
//	 * @param placeIds
//	 * @param shiftId
//	 * @return
//	 */
//	@Query(value = "select sum(cash_amount) from log_topup WHERE place_id in (?1) AND status = 3 AND shift_id =?2", nativeQuery = true)
//	Integer sumAmountTotalIncomeByShiftIdAndPlaceIds(List<String> placeIds, String shiftId);

	/**
	 * 按 班次、单个场所-总收入
	 *
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select sum(cash_amount) from log_topup WHERE place_id = ?1 AND status = 3 AND shift_id =?2 AND created >= ?3 AND created <=?4", nativeQuery = true)
	Integer sumAmountTotalIncomeByShiftIdAndPlaceId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按 班次、来源单个场所-总收入
	 *
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id=?1  and lo.shift_id=?2 AND `status` = 3 and lo.source_type=?3 AND created >= ?4 AND created <=?5", nativeQuery = true)
	Integer sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(String placeId, String shiftId, String sourceType, LocalDateTime startDateTime, LocalDateTime endDateTime);


	/**
	 * 按 班次、卡类型 单个场所-总收入
	 *
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id=?1  and lo.shift_id=?2 AND `status` = 3 and lo.card_type_id=?3 AND created >= ?4 AND created <=?5", nativeQuery = true)
	Integer sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeId(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按 班次、卡类型不等于 单个场所-总收入
	 *
	 * @param placeId
	 * @param shiftId
	 * @return
	 */
	@Query(value = "select sum(lo.cash_amount) from log_topup lo where lo.place_id=?1  and lo.shift_id=?2 AND `status` = 3 and lo.card_type_id<>?3 AND created >= ?4 AND created <=?5 ", nativeQuery = true)
	Integer sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeIdNotIn(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime);
}
