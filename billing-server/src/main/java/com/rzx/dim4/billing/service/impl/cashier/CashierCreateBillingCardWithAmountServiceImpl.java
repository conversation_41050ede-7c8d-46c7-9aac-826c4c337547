package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.entity.TopupRule;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收银台带钱开卡
 *
 * <AUTHOR>
 * @since 2023/7/27
 * 2023.8.29该接口已不再使用
 **/

@Slf4j
@Service
@Deprecated // 已经被252 253 256接口替换
public class CashierCreateBillingCardWithAmountServiceImpl{

    @Autowired
    private IBillingCardService iBillingCardService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    TopupRuleService topupRuleService;

    @Autowired
    PlaceBizConfigService placeBizConfigService;



    // @Override
    public GenericResponse<ObjDTO<BillingCardBO>> doService(List<String> params) {

        // 必填：0.场所ID , 1.卡类型ID，2.收银台ID 3.身份证号码，4.姓名，5.地址，6.签发机关，7.民族, 8.有效期
        if (params.size() < 9) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 获取参数
        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardTypeId = params.get(2); // 卡类型ID
        String idNumber = params.get(3).toUpperCase(); // 身份证号码
        String name = params.get(4); // 姓名
        String address = params.get(5); // 地址
        String issuingAuthority = params.get(6); // 发证机关
        String nation = params.get(7); // 民族
        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡

        String activeType = null;
        if (params.size() >= 10) { // 新老接口兼容
            activeType = params.get(9); // 激活方式,传value值
        }

        String identification = null;
        if (params.size() >= 11) { // 新老接口兼容
            identification = params.get(10); // 附加费标识
        }

        String phoneNumber = ""; // 手机号，场所开启注册卡后，能获取到手机号
        String remark = null;
        if (params.size() >= 13) { // 新老接口兼容
            phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
            remark = params.get(12); // 备注
            if (!StringUtils.isEmpty(remark) && remark.length() > 100) {
                log.warn("remark length is too long, remark:{}", remark);
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
        }

        int cashAmount = 0;
        int presentAmount = -1;
        if (params.size() == 15) { // 新老接口兼容
            String cashAmountStr = params.get(13); // 充值金额
            String presentAmountStr = params.get(14); // 赠送金额，如果为-1则根据网吧现有充值规则查询，否则以客户端为准
            // 处理充金额和赠送金额
            try {
                cashAmount = Integer.parseInt(cashAmountStr);
                presentAmount = Integer.parseInt(presentAmountStr);
            } catch (NumberFormatException nfe) {
                return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
            }
        }

        // 计算赠送金额
        TopupRule effectedTopupRule = null;
        if (presentAmount == -1) {
            effectedTopupRule = topupRuleService.getEffectedTopupRuleByTopupAmount(placeId, cardTypeId,
                    cashAmount, 1);
            if (effectedTopupRule == null) {
                presentAmount = 0;
            } else {
                presentAmount = effectedTopupRule.getPresentAmount();
            }
        }

        // 验证身份证合法性，姓名不能为空
        boolean flag = IdNumberValidator.verificate(idNumber);
        if (!flag || StringUtils.isEmpty(name)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        // 校验班次
        Optional<LogShift> logShiftOpt = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!logShiftOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = logShiftOpt.get();

        LocalDateTime now = LocalDateTime.now();

        BillingCardBO billingCardBO = new BillingCardBO();
        billingCardBO.setPlaceId(placeId);
        billingCardBO.setCardTypeId(cardTypeId);
        billingCardBO.setIdName(name);
        billingCardBO.setIdNumber(idNumber);
        billingCardBO.setActiveType(activeType);
        billingCardBO.setAddress(address);
        billingCardBO.setLoginName(idNumber);
        billingCardBO.setNation(nation);
        billingCardBO.setIssuingAuthority(issuingAuthority);
        billingCardBO.setPhoneNumber(phoneNumber);
        billingCardBO.setRemark(remark);
        billingCardBO.setValidPeriod(StringUtils.isEmpty(validPeriod) ? "-1" : validPeriod);

        LogTopup logTopup = new LogTopup();
        logTopup.setCashAmount(cashAmount);
        logTopup.setPresentAmount(presentAmount);
        logTopup.setCreated(now);
        logTopup.setCreater(null);
        logTopup.setIdName(name);
        logTopup.setIdNumber(idNumber);
        logTopup.setCardTypeId(cardTypeId);
        logTopup.setOrderId("CASH" + System.currentTimeMillis());
        logTopup.setOperator(logShift.getLoginAccountId());
        logTopup.setOperatorName(logShift.getLoginAccountName());
        logTopup.setShiftId(shiftId);
        logTopup.setStatus(2);
        logTopup.setPayType(PayType.CASH);
        logTopup.setPlaceId(placeId);
        logTopup.setSourceType(SourceType.CASHIER);
        logTopup.setTopupTime(now);
        logTopup.setTopupRuleId(effectedTopupRule != null ? effectedTopupRule.getTopupRuleId() : null);

        billingCardBO = iBillingCardService.autoCreateCardWhenTopup(logTopup, billingCardBO, identification);

        return new GenericResponse<>(new ObjDTO<>(billingCardBO));
    }
}