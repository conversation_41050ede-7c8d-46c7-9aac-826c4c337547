package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.SecurityRequestConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface SecurityRequestConfigRepository extends JpaRepository<SecurityRequestConfig, Long>, JpaSpecificationExecutor<SecurityRequestConfig> {

    Optional<SecurityRequestConfig> findByType(int type);

    Optional<SecurityRequestConfig> findByPlaceIdAndDeleted(String placeId,int deleted);

    List<SecurityRequestConfig> findByPlaceIdIn( List<String> placeIds);

}
