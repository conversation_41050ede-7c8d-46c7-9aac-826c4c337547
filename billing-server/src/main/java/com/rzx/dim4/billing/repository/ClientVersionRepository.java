package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.ClientVersion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ClientVersionRepository extends JpaRepository<ClientVersion, Long>, JpaSpecificationExecutor<ClientVersion> {

    Optional<ClientVersion> findByVersionIdAndDeleted(String versionId,int deleted);

    Optional<ClientVersion> findByVersionNumberAndClientTypeAndDeleted(String versionNumber, int clientType,int deleted);

    Optional<ClientVersion> findByClientTypeAndFileMD5(int clientType,String fileMD5);

    Page<ClientVersion> findByClientTypeAndDeletedOrderByIdDesc(int clientType,int deleted, Pageable pageable);

    Page<ClientVersion> findByClientTypeAndVersionIdInAndDeletedOrderByIdDesc(int clientType, List<String> versionIds, int deleted, Pageable pageable);

    Page<ClientVersion> findByClientTypeAndVersionNumberAndDeletedOrderByIdDesc(int clientType, String versionNumber, int deleted,  Pageable pageable);

    Page<ClientVersion> findByClientTypeAndVersionNumberAndVersionIdInAndDeletedOrderByIdDesc(int clientType, String versionNumber, List<String> versionIds, int deleted,  Pageable pageable);

    Optional<ClientVersion> findTop1ByOrderByIdDesc();

    List<ClientVersion> findByClientTypeAndDeletedOrderByIdDesc(int clientType,int deleted);

    List<ClientVersion> findByVersionIdInAndDeleted(List<String> versionIds,int deleted);
}
