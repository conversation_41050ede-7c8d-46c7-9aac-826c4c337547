package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BalanceDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface BalanceDetailsRepository extends JpaRepository<BalanceDetails, Long>, JpaSpecificationExecutor<BalanceDetails> {

    @Query("select sum(bc.amount) from BalanceDetails bc where bc.placeId=:placeId and bc.deleted = 0 and bc.accountType=:accountType " +
            " and bc.clientId != '-1' and bc.type=0 and bc.created >= :startTime and bc.created < :endTime ")
    Integer sumAmountIncome(String placeId, LocalDateTime startTime, LocalDateTime endTime, Integer accountType);

    Optional<BalanceDetails> findTop1ByPlaceIdOrderByIdDesc(String placeId);

    List<BalanceDetails> findByPlaceIdAndDeleted(String placeId, int deleted);

    List<BalanceDetails> findByPlaceIdAndInviteCodeOrderByIdDesc(String placeId, String inviteCode);

    Optional<BalanceDetails> findFirstByPlaceIdAndCardIdAndAccountTypeAndDeletedOrderByIdDesc(String placeId, String cardId, int accountType, int deleted);

    List<BalanceDetails> findByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);
}
