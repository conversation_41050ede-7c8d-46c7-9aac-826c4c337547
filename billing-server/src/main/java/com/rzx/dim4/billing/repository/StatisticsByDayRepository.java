package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.StatisticsByDay;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface StatisticsByDayRepository extends JpaRepository<StatisticsByDay, Long>, JpaSpecificationExecutor<StatisticsByDay> {

    @Query(value = "SELECT count_day AS countDay," +
              "sum_cost_online_income AS sumCostOnlineIncome,"
            + "sum_cost_online_outcome AS sumCostOnlineOutcome,"
            + "sum_cost_cash_income AS sumCostCashIncome,"
            + "sum_cost_cash_outcome AS sumCostCashOutcome,"
            + "sum_online_visits AS sumOnlineVisits,"
            + "sum_online_num AS sumOnlineNum,"
            + "sum_consumption_total AS sumConsumptionTotal,"
            + "sum_cost_total_reversal AS sumCostTotalReversal,"
            + "sum_online_time AS sumOnlineTime FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id = :placeId ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> queryDashboardStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeId") String placeId, Pageable pageable);

    @Query(value = "SELECT count(id)"
            + "FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id = :placeId", nativeQuery = true)
    int countDashboardStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeId") String placeId);

    @Query(value = "SELECT a.count_day,COUNT(a.sumOnlineNum) as sumOnlineNum "
            + "FROM (SELECT count_day,sum_online_num as sumOnlineNum FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= ?1 "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= ?2 "
            + "AND sum_online_num > 0 "
            + "AND place_id IN ?3)a GROUP BY a.count_day", nativeQuery = true)
    List<Map<String, String>> querySumOnlineNum (@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("placeIds") List<String> placeIds);

    @Query(value = " SELECT ?3 placeId,IFNULL(SUM(sum_consumption_total),0) totalConsumption " +
            "FROM `statistics_by_day` WHERE place_id = ?3 AND STR_TO_DATE(count_day,\"%Y-%m-%d\") >= ?1" +
            " AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= ?2", nativeQuery = true)
    Map<String, String> queryTotalConsumptionByPlaceId(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("placeId") String placeId);

    @Query(value = "SELECT sum(sum_cost_online_income) AS sumCostOnlineIncome,"
            + "sum(sum_cost_online_outcome) AS sumCostOnlineOutcome,"
            + "sum(sum_cost_cash_income) AS sumCostCashIncome,"
            + "sum(sum_cost_cash_outcome) AS sumCostCashOutcome,"
            + "sum(sum_online_visits) AS sumOnlineVisits,"
            + "sum(sum_online_num) AS sumOnlineNum,"
            + "sum(sum_consumption_total) AS sumConsumptionTotal,"
            + "sum(sum_cost_total_reversal) AS sumCostTotalReversal,"
            + "sum(sum_online_time) AS sumOnlineTime FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id in (:placeIds)", nativeQuery = true)
    Map<String, String> sumDashboardStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeIds") List<String> placeIds);


    @Query(value = "SELECT DATE_FORMAT(count_day, \"%Y-%m\") AS countDay,"
            + "sum(sum_cost_online_income) AS sumCostOnlineIncome,"
            + "sum(sum_cost_online_outcome) AS sumCostOnlineOutcome,"
            + "sum(sum_cost_cash_income) AS sumCostCashIncome,"
            + "sum(sum_cost_cash_outcome) AS sumCostCashOutcome,"
            + "sum(sum_online_visits) AS sumOnlineVisits,"
            + "sum(sum_online_num) AS sumOnlineNum,"
            + "sum(sum_consumption_total) AS sumConsumptionTotal,"
            + "sum(sum_cost_total_reversal) AS sumCostTotalReversal,"
            + "sum(sum_online_time) AS sumOnlineTime FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id = :placeId group by countDay ORDER BY countDay DESC", nativeQuery = true)
    List<Map<String, String>> queryMonthDashboardStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                       @Param("placeId") String placeId, Pageable pageable);

    @Query(value = "select count(*) from (SELECT count(id)"
            + "FROM statistics_by_day "
            + "where STR_TO_DATE(count_day,\"%Y-%m-%d\") >= :startDate "
            + "AND STR_TO_DATE(count_day,\"%Y-%m-%d\") <= :endDate "
            + "AND place_id = :placeId group by DATE_FORMAT(count_day, \"%Y-%m\"))a", nativeQuery = true)
    int countMonthDashboardStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                 @Param("placeId") String placeId);


//    @Query(value = "select a.place_id from (select pd.place_id from statistics_by_day pd where pd.count_day >= ?1 and pd.count_day <= ?2 and pd.sum_online_time >0 group by pd.place_id)a join (select place_id from 4dim_place.place_profile where type!=1 and deleted=0) b on a.place_id = b.place_id", nativeQuery = true)
@Query(value = "select pd.place_id from statistics_by_day pd where pd.deleted=0 and  pd.count_day >= ?1 and pd.count_day <= ?2 and pd.sum_online_num >0 group by pd.place_id", nativeQuery = true)
List<String> queryPlaceIdsByCountDay(String startTime,String endTime);
}
