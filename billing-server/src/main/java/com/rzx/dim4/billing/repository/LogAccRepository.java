package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogAcc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年12月14日 上午10:55:21
 */
public interface LogAccRepository extends JpaRepository<LogAcc, Long>, JpaSpecificationExecutor<LogAcc> {

	Optional<LogAcc> findFirstByPlaceIdAndLoginIdOrderByIdDesc(String placeId, String loginId);

	Optional<LogAcc> findByPlaceIdAndLoginIdAndFinishTimeIsNull(String placeId, String loginId);

}
