package com.rzx.dim4.billing.repository;
import com.rzx.dim4.billing.entity.SurchargeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface SurchargeConfigRepository extends JpaRepository<SurchargeConfig, Long>, JpaSpecificationExecutor<SurchargeConfig> {

    Optional<SurchargeConfig> findByPlaceId(String placeId);

}
