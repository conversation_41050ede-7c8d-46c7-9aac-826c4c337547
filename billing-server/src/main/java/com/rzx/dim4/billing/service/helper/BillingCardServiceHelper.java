package com.rzx.dim4.billing.service.helper;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BillingCardBalanceUpdateRequestBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.TopupAndDeductionBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.UpdateCardTypeBusinessBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.BalanceDetailOperationType;
import com.rzx.dim4.base.enums.billing.PackageTimeReserveStatus;
import com.rzx.dim4.base.enums.billing.RefundType;
import com.rzx.dim4.base.enums.marketing.OrderType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.ResultHandleUtil;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * BillingCardService辅助类
 */
@Slf4j
@Service
public class BillingCardServiceHelper {
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private PlaceChainStoresService placeChainStoresService;

    @Autowired
    private BalanceDetailsService balanceDetailsService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private LogPlaceChainService logPlaceChainService;

    public BillingCard billingCardDeduction(BillingCard billingCard, int requestCashAmount, int requestPresentAmount, int requestTemporaryAmount, String loginId) {
        if (billingCard.getCashAccount() < requestCashAmount || billingCard.getPresentAccount() < requestPresentAmount || billingCard.getTemporaryOnlineAccount() < requestTemporaryAmount) {
            log.info("退款失败AA (BillingCard: cashAccount={} => Request: cashAmount={})", billingCard.getCashAccount(), requestCashAmount);
            log.info("退款失败AA (BillingCard: presentAccount={}, => Request: presentAmount={})", billingCard.getPresentAccount(), requestPresentAmount);
            log.info("退款失败AA (BillingCard: temporaryOnlineAccount={} => Request: temporaryOnlineAccount={})", billingCard.getTemporaryOnlineAccount(), requestTemporaryAmount);
            return null;
        }

        BillingCard newBillingCard = billingCardDeductionService.updateBillingCardAccount(billingCard.getPlaceId(), billingCard.getCardId(), requestCashAmount, requestPresentAmount, requestTemporaryAmount);

        // 连锁扣费记录扣费日志
        if (StringUtils.isNotBlank(billingCard.getChainId())) {
            LogPlaceChain logPlaceChain = new LogPlaceChain();
            logPlaceChain.setChainId(billingCard.getChainId());
            logPlaceChain.setCardId(billingCard.getCardId());
            logPlaceChain.setCurrPlaceId(billingCard.getPlaceId());
            logPlaceChain.setCostPlaceId(billingCard.getPlaceId());
            logPlaceChain.setCostUid(Dim4StringUtils.getUUIDWithoutHyphen());
            logPlaceChain.setCostCashAccount(requestCashAmount);
            logPlaceChain.setCostPresentAccount(requestPresentAmount);
            logPlaceChain.setCostTemporaryOnlineAccount(requestTemporaryAmount);
            logPlaceChain.setLoginId(loginId);
            logPlaceChain.setCreated(LocalDateTime.now());
            logPlaceChain.setDeleted(BaseEntity.NO);
            logPlaceChainService.save(logPlaceChain);
        }

        return newBillingCard;
    }

    @Transactional(rollbackFor = Exception.class)
    public GenericResponse<SimpleDTO> updateBillingCardAccountForRefund(BillingCardBalanceUpdateRequestBO paramsBo) {
        log.info("updateBillingCardAccountForRefund:::orderId={}, params={}", paramsBo.getOrderId(), new Gson().toJson(paramsBo));

        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndCardId(paramsBo.getPlaceId(), paramsBo.getCardId());
        if (!billingCardOptional.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard oldBillingCard = billingCardOptional.get();

        RefundType refundType = RefundType.TOPUP;

        // 注意：step1和step2两者之间不能互换位置，假如互换位置，BalanceDetails记录中的余额会重复计算。抛异常都会回滚代码的


        // step1: balanceDetails 增加记录
        if (OrderType.PACKAGE_TIME.getCode() != paramsBo.getOrderType() ) {
            boolean isSuccess = this.addBalanceDetailsRecord(paramsBo, oldBillingCard,0);
            if (!isSuccess) {
                throw new ServiceException("退款失败");
            }
        }

        // step2: 更新 BillingCard
        BillingCard newBillingCard;
        if (OrderType.PACKAGE_TIME.getCode() != paramsBo.getOrderType() ) {
            newBillingCard = this.billingCardDeduction(oldBillingCard, paramsBo.getCashAccount(), paramsBo.getPresentAccount(), paramsBo.getTemporaryOnlineAccount(), paramsBo.getAccountId() + "");
            if (newBillingCard == null) {
                throw new ServiceException("退款失败");
            }
        } else {
            newBillingCard = oldBillingCard;
        }

        // step3: 更新packageTimeReserve的status状态为 2已使用
        if (OrderType.PACKAGE_TIME.getCode() == paramsBo.getOrderType() || OrderType.PACKAGE_TIME_BILLING_CARD.getCode() == paramsBo.getOrderType()) {
            packageTimeReserveService.updatePackageTimeStatusByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId(), PackageTimeReserveStatus.USED);
            refundType = RefundType.CANCEL_PACKAGE_TIME;
        }

        // step4: logOperation增加记录
        logOperationService.addRefundOperation(paramsBo.getSourceType(), refundType, newBillingCard, paramsBo.getCashAccount(), paramsBo.getPresentAccount(), paramsBo.getTemporaryOnlineAccount(), null);

        // step5: 回显收银台余额
        Optional<LogTopup> logTopupOptional = logTopupService.findByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId());
        BillingCard finalNewBillingCard = newBillingCard;
        logTopupOptional.ifPresent(logTopup -> {
            LogShift logShift = null;
            if (StringUtils.isNotBlank(logTopup.getShiftId())) {
                Optional<LogShift> logShiftOptional = logShiftService.findByPlaceIdAndShiftIdAndStatus(logTopup.getPlaceId(), logTopup.getShiftId(), 0);
                if (logShiftOptional.isPresent()) {
                    logShift = logShiftOptional.get();
                }
            }
            this.saveTopUpPolling(finalNewBillingCard.toBO(), logTopup, logShift);
        });

        return GenericResponse.toSimpleResponse(ServiceCodes.NO_ERROR.getMessage());
    }

    /**
     * 本方法是针对orderType=6的网费支付的包时订单退款
     */
    @Transactional(rollbackFor = Exception.class)
    public GenericResponse<SimpleDTO> refundForInternetFeePackageTimeOrder(BillingCardBalanceUpdateRequestBO paramsBo) {
        log.info("refundForInternetFeePackageTimeOrder:::orderId={}, params={}", paramsBo.getOrderId(), new Gson().toJson(paramsBo));

        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndCardId(paramsBo.getPlaceId(), paramsBo.getCardId());
        if (!billingCardOptional.isPresent()) {
            return GenericResponse.toFailureResponse(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard oldBillingCard = billingCardOptional.get();

        // 注意：step1和step2两者之间不能互换位置，假如互换位置，BalanceDetails记录中的余额会重复计算。抛异常都会回滚代码的

        // step1: balanceDetails 增加记录
        boolean isSuccess = this.addBalanceDetailsRecord(paramsBo, oldBillingCard,1);
        if (!isSuccess) {
            throw new ServiceException("退款失败");
        }

        // step2: 更新BillingCard
        int cashAmount = -1 * paramsBo.getCashAccount();
        int presentAmount = -1 * paramsBo.getPresentAccount();
        int temporaryAccount = -1 * paramsBo.getTemporaryOnlineAccount();
        BillingCard newBillingCard = billingCardDeductionService.updateBillingCardAccount(paramsBo.getPlaceId(), paramsBo.getCardId(), cashAmount, presentAmount, temporaryAccount);
        if (newBillingCard == null) {
            throw new ServiceException("退款失败");
        }

        // step3: 更新packageTimeReserve的status状态为 已使用
        packageTimeReserveService.updatePackageTimeStatusByPlaceIdAndOrderId(paramsBo.getPlaceId(), paramsBo.getOrderId(), PackageTimeReserveStatus.USED);

        // step4: logOperation增加记录
        logOperationService.addRefundOperation(paramsBo.getSourceType(), RefundType.TOPUP, newBillingCard, paramsBo.getCashAccount(), paramsBo.getPresentAccount(), paramsBo.getTemporaryOnlineAccount(), null);

        // step5: 回显收银台余额
        Optional<BillingOnline> billingOnlineOptional = billingOnlineService.findUnfinishedByPlaceIdAndCardId(paramsBo.getPlaceId(), paramsBo.getCardId());
        billingOnlineOptional.ifPresent(billingOnline -> this.savePolling(oldBillingCard.toBO(), billingOnline));

        return GenericResponse.toSimpleResponse(ServiceCodes.NO_ERROR.getMessage());
    }

    /**
     * 增加BalanceDetails记录：
     * <p>
     * 如果当前场所是连锁，需要共享本金（cashAccount+temporaryOnlineAccount），奖励则根据配置sharePresentAccount来决定是否共享
     * cashAccount和temporaryOnlineAccount的总和 看做 本金
     * <p>
     * 如果当前场所不是连锁，走以前的逻辑
     *
     * @param paramsBo       BalanceDetails需要的参数
     * @param oldBillingCard 未修改前的BillingCard
     * @param isInternetFee  是否网费订单
     */
    private boolean addBalanceDetailsRecord(BillingCardBalanceUpdateRequestBO paramsBo, BillingCard oldBillingCard, int isInternetFee) {
        // 退款现金：本金+临时卡金额
        int requestCashAmount = paramsBo.getCashAccount() + paramsBo.getTemporaryOnlineAccount();
        // 退款奖励
        int requestPresentAmount = paramsBo.getPresentAccount();

        log.info("addBalanceDetailsRecord::::placeId={}, orderId={}, refundCashAmount={}, refundPresentAmount={}", paramsBo.getPlaceId(), paramsBo.getOrderId(), requestCashAmount, requestPresentAmount);

        // 连锁下所有场所的本金总和 (本金 + 临时卡金额)
        int billingCardCashAmountSum = 0;
        // 连锁下所有场所的奖励总和
        int billingCardPresentAmountSum = 0;
        // 连锁下所有场所的临时卡金额总和 （填充数据作用）
        int billingCardTemporaryAmountSum = 0;

        // 连锁情况
        if (StringUtils.isNotBlank(oldBillingCard.getChainId())) {
            PlaceChainStores placeChainStores = placeChainStoresService.findByChainIdAndPlaceId(oldBillingCard.getChainId(), paramsBo.getPlaceId());
            if (placeChainStores == null) {
                log.info(":::::::::未查到场所的连锁配置, placeId={}, chainId={} :::::::::::", oldBillingCard.getPlaceId(), oldBillingCard.getChainId());
                return false;
            }
            List<BillingCard> billingCards = billingCardService.findByChainIdAndIdNumber(placeChainStores.getChainId(), oldBillingCard.getIdNumber());
            log.info(":::::::::连锁chainId={}下的场所数量：{} :::::::::::", oldBillingCard.getChainId(), billingCards.size());
            // 现阶段本金是必须共享，以后此处可能会根据shareCashAccount来判断 @2025.7.24
            for (BillingCard card : billingCards) {
                billingCardCashAmountSum += card.getCashAccount() + card.getTemporaryOnlineAccount();
                billingCardTemporaryAmountSum += card.getTemporaryOnlineAccount();
                billingCardPresentAmountSum += card.getPresentAccount();
            }
            // 如果奖励的配置为不共享，重置为当前场所下的奖励金额
            if (placeChainStores.getSharePresentAccount() == BaseEntity.NO) {
                billingCardPresentAmountSum = oldBillingCard.getPresentAccount();
            }
        } else {  // 非连锁情况
            // 现金金额：本金+临时卡金额
            billingCardCashAmountSum = oldBillingCard.getCashAccount() + oldBillingCard.getTemporaryOnlineAccount();
            // 奖励金额
            billingCardPresentAmountSum = oldBillingCard.getPresentAccount();
            // 填充数据
            billingCardTemporaryAmountSum = oldBillingCard.getTemporaryOnlineAccount();
        }

        SpecialPlaceClients specialPlaceClient = paramsBo.getSpecialPlaceClient();

        BalanceDetails cashBalanceDetails = new BalanceDetails();
        cashBalanceDetails.setPlaceId(paramsBo.getPlaceId());
        cashBalanceDetails.setCardId(paramsBo.getCardId());
        cashBalanceDetails.setIdNumber(oldBillingCard.getIdNumber());
        cashBalanceDetails.setCreater(paramsBo.getAccountId());
        cashBalanceDetails.setDescription(paramsBo.getRemark());
        cashBalanceDetails.setClientId(specialPlaceClient.getClientId());
        cashBalanceDetails.setClientName(specialPlaceClient.getClientName());


        // 操作本金
        if (requestCashAmount > 0) {
            cashBalanceDetails.setType(1);
            // 操作金额
            cashBalanceDetails.setAmount(requestCashAmount);

            if (isInternetFee == 0 ) {
                requestCashAmount  = -1 *  requestCashAmount;
                cashBalanceDetails.setType(0);
            }

            // 余额剩余
            cashBalanceDetails.setBalanceAmount(billingCardCashAmountSum + requestCashAmount);
            // 恢复本金
            cashBalanceDetails.setCashAccount(billingCardCashAmountSum + requestCashAmount);
            // 奖励金额不变
            cashBalanceDetails.setPresentAccount(billingCardPresentAmountSum);
            // 临时卡金额不变
            cashBalanceDetails.setTemporaryOnlineAccount(billingCardTemporaryAmountSum);

            cashBalanceDetails.setClientId(specialPlaceClient.getClientId());
            cashBalanceDetails.setClientName(specialPlaceClient.getClientName());
            cashBalanceDetails.setOperationType(BalanceDetailOperationType.INTERNET_FEE_REFUND.getCode());
            cashBalanceDetails.setAccountType(0);
            cashBalanceDetails.setCreated(LocalDateTime.now());
            cashBalanceDetails.setDeleted(BaseEntity.NO);
            cashBalanceDetails.setChainId(oldBillingCard.getChainId());

            balanceDetailsService.save(cashBalanceDetails);

            log.info("addBalanceDetailsRecord 退款--本金::::{}", new Gson().toJson(cashBalanceDetails));
        }

        if (requestPresentAmount > 0) {
            BalanceDetails presentBalanceDetails = new BalanceDetails();
            presentBalanceDetails.setPlaceId(cashBalanceDetails.getPlaceId());
            presentBalanceDetails.setCardId(cashBalanceDetails.getCardId());
            presentBalanceDetails.setIdNumber(cashBalanceDetails.getIdNumber());
            presentBalanceDetails.setCreater(cashBalanceDetails.getCreater());
            presentBalanceDetails.setDescription(cashBalanceDetails.getDescription());
            presentBalanceDetails.setCreated(LocalDateTime.now());
            presentBalanceDetails.setDeleted(BaseEntity.NO);
            presentBalanceDetails.setClientId(specialPlaceClient.getClientId());
            presentBalanceDetails.setClientName(specialPlaceClient.getClientName());
            presentBalanceDetails.setOperationType(BalanceDetailOperationType.INTERNET_FEE_REFUND.getCode());
            presentBalanceDetails.setType(1);
            presentBalanceDetails.setAccountType(1);
            presentBalanceDetails.setChainId(oldBillingCard.getChainId());
            // 如果是在线充值，退款应该减钱。 操作类型应该变为支出
            if (isInternetFee == 0 ) {
                requestCashAmount  = -1 *  requestCashAmount;
                requestPresentAmount = -1 *  requestPresentAmount;
                presentBalanceDetails.setType(0);
            }
            // 操作金额
            presentBalanceDetails.setAmount(requestPresentAmount);
            // 余额剩余
            presentBalanceDetails.setBalanceAmount(billingCardPresentAmountSum + requestPresentAmount);
            // 沿用上面的恢复后的本金
            presentBalanceDetails.setCashAccount(billingCardCashAmountSum + requestCashAmount);
            // 恢复奖励金额
            presentBalanceDetails.setPresentAccount(billingCardPresentAmountSum + requestPresentAmount);
            // 临时卡金额不变
            presentBalanceDetails.setTemporaryOnlineAccount(billingCardTemporaryAmountSum);

            balanceDetailsService.save(presentBalanceDetails);

            log.info("addBalanceDetailsRecord 退款--奖励::::{}", new Gson().toJson(presentBalanceDetails));
        }
        return true;
    }

    public void savePolling(BillingCardBO billingCardBo, BillingOnline billingOnline) {
        // 保存轮询数据
        GenericResponse<ObjDTO<PollingBO>> pollingResponse = notifyServerService.savePolling(billingCardBo.getPlaceId(), billingOnline.getClientId(),
                billingCardBo.getIdNumber(), BusinessType.UPDATE_CARD_TYPE);

        ResultHandleUtil.handleObjectResponse(pollingResponse, pollingBO -> {
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                UpdateCardTypeBusinessBO updateCardTypeBusinessBo = new UpdateCardTypeBusinessBO();
                updateCardTypeBusinessBo.setBusinessId(pollingBO.getCashierBusinessId());
                updateCardTypeBusinessBo.setBusinessType(BusinessType.UPDATE_CARD_TYPE);
                updateCardTypeBusinessBo.setCreated(LocalDateTime.now().toString());
                updateCardTypeBusinessBo.setPlaceId(billingCardBo.getPlaceId());
                updateCardTypeBusinessBo.setIsOnline(1);

                billingCardBo.setActiveTimeStr(null != billingCardBo.getActiveTime() ? formatter.format(billingCardBo.getActiveTime()) : null);
                billingCardBo.setCreatedStr(null != billingCardBo.getCreated() ? formatter.format(billingCardBo.getCreated()) : null);
                billingCardBo.setUpdatedStr(null != billingCardBo.getUpdated() ? formatter.format(billingCardBo.getUpdated()) : null);
                updateCardTypeBusinessBo.setBillingCardBO(billingCardBo);

                // 保存收银台业务数据
                notifyServerService.pushUpdateCardTypeBusinessData(updateCardTypeBusinessBo);

                log.info("savePolling.pushUpdateCardTypeBusinessData: {}", new Gson().toJson(updateCardTypeBusinessBo));
            }
        });
    }


    public void saveTopUpPolling(BillingCardBO billingCard, LogTopup logTopup, LogShift logShift) {
        String clientId = StringUtils.isBlank(logTopup.getClientId()) ? "" : logTopup.getClientId();
        GenericResponse<ObjDTO<PollingBO>> pollingResponse = notifyServerService.savePolling(logTopup.getPlaceId(), clientId, billingCard.getIdNumber(), BusinessType.TOPUP);

        ResultHandleUtil.handleObjectResponse(pollingResponse, pollingBO -> {
            TopupAndDeductionBusinessBO businessTopupAndDeductionBO = new TopupAndDeductionBusinessBO();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                businessTopupAndDeductionBO.setPlaceId(logTopup.getPlaceId());
                businessTopupAndDeductionBO.setIdNumber(billingCard.getIdNumber());
                businessTopupAndDeductionBO.setCashAmount(logTopup.getCashAmount());
                businessTopupAndDeductionBO.setPresentAmount(logTopup.getPresentAmount());
                businessTopupAndDeductionBO.setTotalAccount(billingCard.getTotalAccount());
                businessTopupAndDeductionBO.setCreated(LocalDateTime.now().toString());
                businessTopupAndDeductionBO.setSourceType(logTopup.getSourceType());
                businessTopupAndDeductionBO.setBusinessType(BusinessType.TOPUP);
                businessTopupAndDeductionBO.setBusinessId(pollingBO.getCashierBusinessId());
                businessTopupAndDeductionBO.setClientId(logTopup.getClientId());
                businessTopupAndDeductionBO.setType(1);
                businessTopupAndDeductionBO.setOrderId(logTopup.getOrderId());
                businessTopupAndDeductionBO.setPayType(logTopup.getPayType());
                if (logTopup.getPayType().equals(PayType.AGGREGATE_PAY) ||
                        logTopup.getPayType().equals(PayType.AGGREGATE_PAY_ALI) ||
                        logTopup.getPayType().equals(PayType.AGGREGATE_PAY_WECHAT) ||
                        logTopup.getPayType().equals(PayType.WECHAT_SCAN) ||
                        logTopup.getPayType().equals(PayType.ALIPAY_SCAN)) {
                    businessTopupAndDeductionBO.setNoPopupIde("1");
                    businessTopupAndDeductionBO.setCashierId(ObjectUtils.isEmpty(logShift) ? "" : logShift.getCashierId());
                }
                // 保存收银台业务数据
                notifyServerService.pushTopupAndDeductionBusinessData(businessTopupAndDeductionBO);

                if (null != logTopup.getClientId()) {
                    // 客户端在线充值时，需要单独写一条，把换机通知给收银台，但是最终是要给第三方
                    businessTopupAndDeductionBO.setType(0); // 第三方时，给客户端
                    // 保存收银台业务数据
                    notifyServerService.pushTopupAndDeductionBusinessData(businessTopupAndDeductionBO);

                    log.info("订单[orderId:{}]保存轮询数据成功，TopupAndDeductionBusinessBO={}", logTopup.getOrderId(), new Gson().toJson(pollingResponse));
                }
            }
        });
    }
}