package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceChainCardTypeBO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.request.DataTablesRequest;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.PlaceChainCardType;

import java.util.List;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/12
 **/
public interface PlaceChainCardTypeService {

    List<PlaceChainCardType> findByChainIdAndDeleted(String chainId, int deleted);

    Optional<PlaceChainCardType> findByChainIdAndCardTypeId (String chainId, String cardTypeId);

    void addNewCardType(PlaceChainCardTypeBO placeChainCardTypeBO);

    void editCardType(PlaceChainCardTypeBO placeChainCardTypeBO);

    GenericResponse<PagerDTO<PlaceChainCardTypeBO>> page(DataTablesRequest dtRequest);

    void delete(String chainId, String cardTypeId);
}
