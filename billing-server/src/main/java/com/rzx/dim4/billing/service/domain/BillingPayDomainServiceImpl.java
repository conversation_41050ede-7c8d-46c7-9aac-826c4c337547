package com.rzx.dim4.billing.service.domain;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.payment.PaymentRequestBO;
import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.BizServer;
import com.rzx.dim4.base.enums.ClientBusinessIds;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.third.ThirdAccount;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.third.ThirdAccountService;
import com.rzx.dim4.billing.service.util.RegionChnCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/18
 **/
@Slf4j
@Service
public class BillingPayDomainServiceImpl implements BillingPayDomainService {

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PlaceServerService placeServerService;

    @Autowired
    private TopupRuleService topupRuleService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private PaymentServerService paymentServerService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private BillingCardTypeService billingCardTypeService;

    @Autowired
    private ThirdAccountService thirdAccountService;

    @Autowired
    private LogRecordRewardInstallmentService logRecordRewardInstallmentService;

    @Autowired
    private RegionChnCodeUtil regionChnCodeUtil;

    @Override
    public PaymentResultBO doMiniCreateOrder(String placeId,
                                             String idNumber,
                                             int amount,
                                             String openId,
                                             String cardTypeId,
                                             String idName,
                                             String appId) {
        log.info("BillingPayDomainServiceImpl.createOrder placeId:{}, idNumber:{}, amount:{}, openId:{}, cardTypeId:{}, idName:{}, appId:{}",
                placeId, idNumber, amount, openId, cardTypeId, idName, appId);

        PlaceConfigBO placeConfigBO = getPlaceConfigBO(placeId);
        // 校验场所是否支持在线充值
        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }

        checkPlacePaymentConfigIsReady(placeId);

        BillingCard billingCard = getTempBillingCard(placeId, idNumber, cardTypeId, idName);

        checkMinCreateCardAmount(placeId, amount, billingCard);

        // 获取充值赠送规则
        TopupRule activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(), amount, 3);
        // 获取场所计费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        int dayNumConfig = placeBizConfig.getMemberDayNum();
        // 会员日生效次数校验
        // 20230811注释取消改功能校验
//        if (!StringUtils.isEmpty(activedRule) && activedRule.getTopupType() > 1 && !StringUtils.isEmpty(billingCard.getCardId())) {
//            String memberDayNum = topupRuleService.getMemberDayNum(placeId, billingCard.getCardId());
//            if (topupRuleService.getCheckMemberDayNum(memberDayNum, dayNumConfig)) {
//                throw new ServiceException(ServiceCodes.BILLING_MEMBER_DAY_TOPUP_OVER);
//            }
//        }

        // 创建支付订单
        PaymentResultBO paymentResultBO = getPaymentResultBO(idNumber, amount, openId, placeConfigBO, appId);

        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());

        // 临时卡在线充值往在线账户里面充钱
        logTopup.setCashAmount(amount);
        if ("1000".equals(billingCard.getCardTypeId())) {
            logTopup.setTemporaryOnlineBalance(billingCard.getTemporaryOnlineAccount());
        } else {
            logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
            logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
            if (activedRule == null) {
                logTopup.setPresentAmount(0);
            } else {

                //判断是否分期赠送，直接获得计算后的金额
                int installmentAmount = logRecordRewardInstallmentService.getInstallmentAmount(activedRule, amount);
                logTopup.setPresentAmount(installmentAmount);
            }
        }


        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);

        logTopup.setPayType(PayType.WECHAT_MINIAPP);
        logTopup.setSourceType(SourceType.MINIAPP);

        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);
        logTopup.setPayUrl(paymentResultBO.getPayUrl());

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        log.info("保存充值日志, ={}", new Gson().toJson(logTopup));
        logTopupService.save(logTopup);

        return paymentResultBO;
    }

    private void checkMinCreateCardAmount(String placeId, int amount, BillingCard billingCard) {
        // 没有卡号或者卡号已经被删除，需要重新创建卡号
        boolean needCreateCard = StringUtils.isEmpty(billingCard.getCardId()) || billingCard.getDeleted() == 1;

        BillingCardType billingCardType = billingCardTypeService.getBillingCardType(placeId, billingCard.getCardTypeId());
        int minCreateCardAmount = billingCardType.getMinCreateCardAmount();
        if (needCreateCard && amount < minCreateCardAmount) {
            log.error("amount is less than minCreateCardAmount = {}", minCreateCardAmount);
            throw new ServiceException(ServiceCodes.BILLING_FAILED_TO_CREATE_CARD, "开卡失败，最低开卡金额为" + minCreateCardAmount / 100 + "元");
        }
    }
    
    @Override
    public PaymentResultBO doMpCreateOrder(String placeId, String idNumber, int amount, String cardTypeId, String idName,String returnUrl,String topupRuleId,String openId) {

        PlaceConfigBO placeConfigBO = getPlaceConfigBO(placeId);

        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }

        checkPlacePaymentConfigIsReady(placeId);

        BillingCard billingCard = getTempBillingCard(placeId, idNumber, cardTypeId, idName);

        checkMinCreateCardAmount(placeId, amount, billingCard);

        // 获取充值赠送规则
        TopupRule activedRule = null;
        if(!"1000".equals(cardTypeId)){
            if(StringUtils.isEmpty(topupRuleId)){
                activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(),
                        amount, 3);
            }else{
                Optional<TopupRule> byPlaceIdAndTopupRuleId = topupRuleService.findByPlaceIdAndTopupRuleId(placeId, topupRuleId);
                activedRule = byPlaceIdAndTopupRuleId.get();
            }
        }

        // 获取场所计费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        int dayNumConfig = placeBizConfig.getMemberDayNum();

        // 会员日生效次数校验
        // 20230811注释取消改功能校验
//        if (!StringUtils.isEmpty(activedRule) && activedRule.getTopupType() > 1 && !StringUtils.isEmpty(billingCard.getCardId())) {
//            String memberDayNum = topupRuleService.getMemberDayNum(placeId, billingCard.getCardId());
//            if (topupRuleService.getCheckMemberDayNum(memberDayNum, dayNumConfig)) {
//                throw new ServiceException(ServiceCodes.BILLING_MEMBER_DAY_TOPUP_OVER);
//            }
//        }

        PaymentResultBO paymentResultBO = getPaymentResultBO(idNumber, amount, placeConfigBO,returnUrl,openId);

        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());

        // 临时卡在线充值往在线账户里面充钱
        logTopup.setCashAmount(amount);
        if ("1000".equals(billingCard.getCardTypeId())) {
            logTopup.setTemporaryOnlineBalance(billingCard.getTemporaryOnlineAccount());
        } else {
            logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
            logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
            if (activedRule == null) {
                logTopup.setPresentAmount(0);
            } else {

                //判断是否分期赠送，直接获得计算后的金额
                int installmentAmount = logRecordRewardInstallmentService.getInstallmentAmount(activedRule, amount);
                logTopup.setPresentAmount(installmentAmount);

            }
        }

        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);
        logTopup.setPayType(PayType.WECHAT_MP);
        logTopup.setSourceType(SourceType.WECHAT);
        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setStatus(0);
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);
        logTopup.setPayUrl(paymentResultBO.getPayUrl());

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        logTopupService.save(logTopup);

        return paymentResultBO;
    }

    /**
     * 创建订单（有无会员卡都能充值）
     *
     * @param thirdAccountId 第三方账户ID
     * @param placeId        场所ID
     * @param cardId         会员卡ID
     * @param cardTypeId     会员卡类型ID
     * @param idNumber       身份证号
     * @param idName         姓名
     * @param amount         充值金额
     * @param payType        支付类型
     * @param openId         微信openId
     * @return PaymentResultBO
     */
    @Override
    public PaymentResultBO doThirdCreateOrder(String thirdAccountId, String placeId, String cardId, String cardTypeId, String idNumber, String idName, String amount, String payType, String openId, String payCode) {
        int amountInt = getAmountInt(amount);
        PayType payTypeEnum = getPayType(payType, openId);
        BillingCard tempBillingCard = getTempBillingCard(placeId, idNumber, cardTypeId, idName);

        checkMinCreateCardAmount(placeId, amountInt, tempBillingCard);

        ThirdAccount thirdAccount = thirdAccountService.getThirdAccount(thirdAccountId);

        PaymentResultBO paymentResultBO = getPaymentResultBO(placeId, PayType.getPayTypeByCode(payType).name(), openId, amountInt, idNumber, thirdAccount, payCode);

        String clientId = "";
        String loginId = "";
        // 获取充值赠送规则
        TopupRule effectedTopupRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, tempBillingCard.getCardTypeId(),
                amountInt, 3);

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId, cardId);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            clientId = billingOnline.getClientId();
            loginId = billingOnline.getLoginId();
        }

        // 获取来源
        SourceType sourceType = thirdAccountService.getType(thirdAccount.getName());

        // 记录充值操作日志
        LocalDateTime now = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setClientId(clientId);
        logTopup.setCardId(tempBillingCard.getCardId());
        logTopup.setCardTypeId(tempBillingCard.getCardTypeId());
        logTopup.setCardTypeName(tempBillingCard.getCardTypeName());
        logTopup.setIdNumber(tempBillingCard.getIdNumber());
        logTopup.setIdName(tempBillingCard.getIdName());
        logTopup.setCashBalance(tempBillingCard.getCashAccount());
        logTopup.setPresentBalance(tempBillingCard.getPresentAccount());
        logTopup.setCashAmount(amountInt);
        if (effectedTopupRule == null) {
            logTopup.setPresentAmount(0);
        } else {

            //判断是否分期赠送，直接获得计算后的金额
            int installmentAmount = logRecordRewardInstallmentService.getInstallmentAmount(effectedTopupRule, amountInt);
            logTopup.setPresentAmount(installmentAmount);

        }
        logTopup.setPayType(payTypeEnum);
        logTopup.setSourceType(sourceType);

        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setLoginId(loginId);
        logTopup.setOperator(thirdAccountId);
        logTopup.setOperatorName(thirdAccount.getName());

        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setQrcodeUrl(paymentResultBO.getQrcodeUrl());

        logTopup.setStatus(1);
        logTopup.setCreated(now);

        logTopupService.save(logTopup);

        return paymentResultBO;
    }

    private int getAmountInt(String amount) {
        int amountInt;
        try {
            amountInt = Integer.parseInt(amount);
            if (amountInt < 1) {
                log.info("充值金额错误");
                throw new ServiceException(ServiceCodes.BILLING_AMOUNT_ERROR);
            }
        } catch (Exception e) {
            log.info("充值金额错误");
            throw new ServiceException(ServiceCodes.BILLING_AMOUNT_ERROR);
        }
        return amountInt;
    }

    private PayType getPayType(String payType, String openId) {
        PayType payTypeEnum = PayType.getPayTypeByCode(payType);
        if (payTypeEnum == null) {
            log.info("支付类型错误");
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        if (payTypeEnum == PayType.WECHAT_MINIAPP && org.apache.commons.lang.StringUtils.isEmpty(openId)) {
            log.info("支付类型错误");
            throw new ServiceException(ServiceCodes.BAD_PARAM);
        }
        return payTypeEnum;
    }

    private PaymentResultBO getPaymentResultBO(String placeId, String payType, String openId, int amountInt, String idNumber, ThirdAccount thirdAccount, String payCode) {
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setBizType("chongzhi");
        requestBO.setOrderAmount(amountInt);
        requestBO.setOrderDesc(thirdAccount.getName() + "网费充值" + (amountInt / 100.00) + "元");
        requestBO.setIdNumber(idNumber);
        requestBO.setStoreNo(placeId);
        requestBO.setPlaceId(placeId);
        requestBO.setPayType(payType);
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setOpenId(openId);
        requestBO.setPayCode(payCode);

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeId));
        requestBO.setBusinessId(ClientBusinessIds.CLIENT.getCode());// 第三方业务，目前没有这个接口的调用，先使用client代替

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);

        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.info("create payment order failed, code={}, message={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }

        PaymentResultBO paymentResultBO = response.getData().getObj();
        return paymentResultBO;
    }
    
    private PaymentResultBO getPaymentResultBO4Iot(String placeId, String placeName, String payType, int amountInt, String idNumber, String payCode) {
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);

        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setBizType("chongzhi");
        requestBO.setOrderAmount(amountInt);
        requestBO.setOrderDesc(placeName + "网费充值" + (amountInt / 100.00) + "元");
        requestBO.setIdNumber(idNumber);
        requestBO.setStoreNo(placeId);
        requestBO.setPlaceId(placeId);
        requestBO.setPayType(payType);
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setPayCode(payCode);

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeId));
        requestBO.setBusinessId(ClientBusinessIds.ALIPAY_IOT.getCode());
        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);

        if (!response.isResult()) {
            log.info("create payment order failed, code={}, message={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();
        if (paymentResultBO == null || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        return paymentResultBO;
    }

    private PaymentResultBO getPaymentResultBO(String idNumber, int amount, PlaceConfigBO placeConfigBO,String returnUrl,String openId) {
        // 创建支付订单
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(amount);

        requestBO.setOrderDesc(placeConfigBO.getPlaceName() + "网费充值" + (amount / 100.00) + "元");
        requestBO.setStoreNo(placeConfigBO.getPlaceId());
        requestBO.setPayType(PayType.WECHAT_MP.name());
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setBizType("chongzhi");
        requestBO.setIdNumber(idNumber);
        requestBO.setPlaceId(placeConfigBO.getPlaceId());
        if(!StringUtils.isEmpty(returnUrl)){
            requestBO.setReturnUrl(returnUrl);
        }
        if(!StringUtils.isEmpty(openId)){
            requestBO.setOpenId(openId);
        }

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));
        requestBO.setBusinessId(ClientBusinessIds.WECHAT_MP.getCode());

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);

        if (!response.isResult()) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();

        if (paymentResultBO == null || StringUtils.isEmpty(paymentResultBO.getPayUrl())
                || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        return paymentResultBO;
    }

    public BillingCard getTempBillingCard(String placeId, String idNumber, String cardTypeId, String idName) {
        BillingCard billingCard;

        //查询卡类型，现在每种价格只支持设置一种卡类型
        Optional<BillingCard> billingCardOptional = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingCardOptional.isPresent()) {
            billingCard = billingCardOptional.get();
            if (billingCard.getDeleted() == 0) {
                return billingCard;
            }
        }
        String cardTypeName = "普通会员";
        if(!"1000".equals(cardTypeId)){
            if (!StringUtils.isEmpty(cardTypeId)) {
                BillingCardType billingCardType = billingCardTypeService.get(placeId, cardTypeId);
                cardTypeName = billingCardType.getTypeName();
            } else {
                cardTypeId = "1001";
            }
        }else{
            cardTypeName = "临时卡";
        }


        // cardId为空，则没有会员卡，需要后面 notify 时生成普通会员卡
        billingCard = new BillingCard();
        billingCard.setCardTypeId(cardTypeId);
        billingCard.setCardTypeName(cardTypeName);
        billingCard.setIdNumber(idNumber);
        billingCard.setIdName(idName);
        billingCard.setCashAccount(0);
        billingCard.setPresentAccount(0);

//        if ("1000".equals(billingCard.getCardTypeId())) {
//            throw new ServiceException(ServiceCodes.TEMPORARY_NOT_SUPPORT_ONLINE_PAY);
//        }
        return billingCard;
    }

    private PaymentResultBO getPaymentResultBO(String idNumber, int amount, String openId, PlaceConfigBO placeConfigBO, String appId) {
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(amount);
        requestBO.setOrderDesc(placeConfigBO.getPlaceName() + "网费充值" + (amount / 100.00) + "元");
        requestBO.setStoreNo(placeConfigBO.getPlaceId());
        requestBO.setOpenId(openId);
        requestBO.setPayType(PayType.WECHAT_MINIAPP.name());
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setBizType("chongzhi");
        requestBO.setIdNumber(idNumber);
        requestBO.setPlaceId(placeConfigBO.getPlaceId());
        requestBO.setPayAppId(appId);

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));
        requestBO.setBusinessId(ClientBusinessIds.WECHAT_MINI_APP.getCode());

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);
        if (!response.isResult()) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();

        // || StringUtils.isEmpty(paymentResultBO.getPayUrl())  //小程序支付不需要payUrl
        if (paymentResultBO == null || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        return paymentResultBO;
    }

    private void checkPlacePaymentConfigIsReady(String placeId) {
        GenericResponse<ObjDTO<PlaceProfileBO>> placeProfileResponse = placeServerService.findByPlaceId(placeId);
        if (placeProfileResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.GET_PLACE_INFO_FAIL);
        }
        PlaceProfileBO placeProfileBO = placeProfileResponse.getData().getObj();
        if (placeProfileBO.getIsRegistered() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_REGISTER_PAY_AMOUNT);
        }
        GenericResponse<ObjDTO<PlaceConfigBO>> placeConfig = placeServerService.findPlaceConfigByPlaceId(placeId);
        if (placeConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = placeConfig.getData().getObj();
        // 校验场所状态
        if (placeConfigBO.getStatus() != 0) {
            throw new ServiceException(ServiceCodes.PLACE_REALNAME_DISABLED);
        }
    }

    private PlaceConfigBO getPlaceConfigBO(String placeId) {
        GenericResponse<ObjDTO<PlaceConfigBO>> responsePlaceConfig = placeServerService.findPlaceConfigByPlaceId(placeId);

        if (responsePlaceConfig.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            throw new ServiceException(ServiceCodes.GET_PLACE_CONFIG_FAIL);
        }
        PlaceConfigBO placeConfigBO = responsePlaceConfig.getData().getObj();
        return placeConfigBO;
    }

    @Override
    public String onlineTopupNotify(String orderId, PayType payType) {
        // 记录充值操作日志
        Optional<LogTopup> optLogTopup = logTopupService.findByOrderId(orderId);
        if (!optLogTopup.isPresent()) {
            log.warn("充值记录不存在, orderId:{}", orderId);
            return "ERROR";
        }
        LogTopup logTopup = optLogTopup.get();
        if (logTopup.getStatus() == 3) {
            log.info("充值已完成, orderId:{}", orderId);
            return "SUCCESS";
        }
        if (logTopup.getStatus() != 1) {
            log.warn("充值状态异常, orderId:{}", orderId);
            return "ERROR";
        }

        logTopup.setTopupTime(LocalDateTime.now());
        logTopup.setUpdated(LocalDateTime.now());
        logTopup.setStatus(2);
        logTopup.setPayType(payType);
        logTopupService.save(logTopup);
        log.info("充值记录更新成功, orderId:{}", orderId);

        // 执行充值
        boolean result = billingCardService.billingCardTopupByLogTopup(logTopup);
        if (result) {
            return "SUCCESS";
        } else {
            log.info("充值失败, orderId:{}", orderId);
            return "ERROR";
        }
    }

	@SuppressWarnings("rawtypes")
	@Override
	public PaymentResultBO doIotCreateOrder(String placeId, String idNumber, int amount, String cardTypeId, String name,
			String payType, String payCode) {
        log.info("BillingPayDomainServiceImpl.doIotCreateOrder placeId:{}, idNumber:{}, amount:{}, cardTypeId:{}, name:{}, payType:{}, payCode:{}",
                placeId, idNumber, amount, cardTypeId, name, payType, payCode);

        PlaceConfigBO placeConfigBO = getPlaceConfigBO(placeId);
        // 校验场所是否支持在线充值
        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }

        checkPlacePaymentConfigIsReady(placeId);

        BillingCard billingCard = getTempBillingCard(placeId, idNumber, cardTypeId, name);

        checkMinCreateCardAmount(placeId, amount, billingCard);

        // 获取充值赠送规则
        TopupRule activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(), amount, 3);

        PayType payTypeEnum = PayType.getPayTypeByCode(payType);
        
        // 创建支付订单
        PaymentResultBO paymentResultBO = getPaymentResultBO4Iot(placeId, placeConfigBO.getPlaceName(), payTypeEnum.name(), amount, idNumber, payCode);

        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setTopupTime(LocalDateTime.now());
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());
        logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
        logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
        logTopup.setCashAmount(amount);

        if (activedRule == null) {
            logTopup.setPresentAmount(0);
        } else {

            //判断是否分期赠送，直接获得计算后的金额
            int installmentAmount = logRecordRewardInstallmentService.getInstallmentAmount(activedRule, amount);
            logTopup.setPresentAmount(installmentAmount);
        }
        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);

        logTopup.setPayType(payTypeEnum);
        logTopup.setSourceType(SourceType.IOT);

        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);
        logTopup.setPayUrl(paymentResultBO.getPayUrl());
        logTopup.setQrcodeUrl(paymentResultBO.getQrcodeUrl());
        
        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        log.info("保存充值日志, ={}", new Gson().toJson(logTopup));
        logTopupService.save(logTopup);

        return paymentResultBO;
	}

    @Override
    public PaymentResultBO aliAppCreateOrder( String placeId, String idNumber, int amount, String openId, String cardTypeId, String idName, String topupRuleId, String appId) {

        PlaceConfigBO placeConfigBO = getPlaceConfigBO(placeId);

        if (placeConfigBO.getOnlineTopup() == 0) {
            throw new ServiceException(ServiceCodes.PLACE_NOT_SUPPORT_ONLINE_TOPUP);
        }

        checkPlacePaymentConfigIsReady(placeId);

        BillingCard billingCard = getTempBillingCard(placeId, idNumber, cardTypeId, idName);

        checkMinCreateCardAmount(placeId, amount, billingCard);

        // 获取充值赠送规则
        TopupRule activedRule = null;
        if(!"1000".equals(cardTypeId)){
            if(StringUtils.isEmpty(topupRuleId)){
                activedRule = topupRuleService.getEffectedTopupRuleByTopupAmountNew(placeId, billingCard.getCardTypeId(),
                        amount, 3);
            }else{
                Optional<TopupRule> byPlaceIdAndTopupRuleId = topupRuleService.findByPlaceIdAndTopupRuleId(placeId, topupRuleId);
                activedRule = byPlaceIdAndTopupRuleId.get();
            }
        }

        // 创建支付订单
        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        PaymentRequestBO requestBO = new PaymentRequestBO();
        requestBO.setOrderAmount(amount);
        requestBO.setOrderDesc(placeConfigBO.getPlaceName() + "网费充值" + (amount / 100.00) + "元");
        requestBO.setStoreNo(placeConfigBO.getPlaceId());
        requestBO.setPayType(PayType.ALIPAY_MINIAPP.name());
        requestBO.setBizServer(BizServer.BILLING.name());
        requestBO.setBizType("chongzhi");
        requestBO.setIdNumber(idNumber);
        requestBO.setPlaceId(placeConfigBO.getPlaceId());
        requestBO.setOpenId(openId);
        requestBO.setPayAppId(appId);
        requestBO.setPayCode("");

        // 业绩自动化需求新增字段
        requestBO.setRegionChnCode(regionChnCodeUtil.getNamePinYin(placeConfigBO.getPlaceId()));
        requestBO.setBusinessId(ClientBusinessIds.ALIPAY_MINI_APP.getCode());

        GenericResponse<ObjDTO<PaymentResultBO>> response = paymentServerService.createPaymentOrder(requestTicket, requestBO);

        if (!response.isResult()) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }
        PaymentResultBO paymentResultBO = response.getData().getObj();

        if (paymentResultBO == null
                || StringUtils.isEmpty(paymentResultBO.getOrderId())
                || StringUtils.isEmpty(paymentResultBO.getLdOrderId())) {
            throw new ServiceException(ServiceCodes.PAYMENT_CREATE_FAIL);
        }

        // 创建订单成功
        LocalDateTime nowTime = LocalDateTime.now();
        LogTopup logTopup = new LogTopup();
        logTopup.setPlaceId(placeId);
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setIdNumber(idNumber);
        logTopup.setIdName(billingCard.getIdName());

        // 临时卡在线充值往在线账户里面充钱
        logTopup.setCashAmount(amount);
        if ("1000".equals(billingCard.getCardTypeId())) {
            logTopup.setTemporaryOnlineBalance(billingCard.getTemporaryOnlineAccount());
        } else {
            logTopup.setCashBalance(billingCard.getCashAccount()); // 充值时填入账户实时金额
            logTopup.setPresentBalance(billingCard.getPresentAccount()); // 充值时填入账户实时金额
            if (activedRule == null) {
                logTopup.setPresentAmount(0);
            } else {

                //判断是否分期赠送，直接获得计算后的金额
                int installmentAmount = logRecordRewardInstallmentService.getInstallmentAmount(activedRule, amount);
                logTopup.setPresentAmount(installmentAmount);

            }
        }

        logTopup.setTopupRuleId(activedRule == null ? null : activedRule.getTopupRuleId());
        logTopup.setOptType(0);
        logTopup.setPayType(PayType.ALIPAY_MINIAPP);
        logTopup.setSourceType(SourceType.ALIPAY);
        logTopup.setShiftId(logShiftService.getShiftId(placeId) == null ? "" : logShiftService.getShiftId(placeId).getShiftId());
        logTopup.setOperator(idNumber);
        logTopup.setOperatorName("用户");
        logTopup.setStatus(0);
        logTopup.setCreated(nowTime);
        logTopup.setOrderId(paymentResultBO.getOrderId());
        logTopup.setLdOrderId(paymentResultBO.getLdOrderId());
        logTopup.setStatus(1);

        // 查询在线信息
        Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (billingOnlineOpt.isPresent()) {
            BillingOnline billingOnline = billingOnlineOpt.get();
            Optional<LogLogin> logLoginOpt = logLoginService.findOnlineByPlaceIdAndClientId(placeId,
                    billingOnline.getClientId(), billingOnline.getBillingTime());
            if (logLoginOpt.isPresent()) {
                LogLogin logLogin = logLoginOpt.get();
                logTopup.setLoginId(logLogin.getLoginId());
                logTopup.setClientId(logLogin.getClientId());
            }
        }

        logTopupService.save(logTopup);

        return paymentResultBO;
    }
}
