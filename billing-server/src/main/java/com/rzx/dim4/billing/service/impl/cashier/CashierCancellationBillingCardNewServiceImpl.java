package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.CancellationBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收银台注销临时卡
 * CashierCancellationBillingCardNew(615, "收银台注销计费卡-新"), // 0x267
 *
 * <AUTHOR>
 * @date 2021年9月3日 下午5:31:01
 * @see com.rzx.dim4.base.enums.billing.ServiceIndexes#CashierCancellationBillingCardNew
 */
@Slf4j
@Service
public class CashierCancellationBillingCardNewServiceImpl implements CoreService {

    @Autowired
    private IBillingCardService iBillingCardService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() < 3) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0);
        String shiftId = params.get(1); // 班次ID
        String cardId = params.get(2);

        String refundType = null;
        if (params.size() == 4) {
            refundType = params.get(3); //退款方式，用户选择退现金还是退余额。1-余额退费，2-现金退费。收银台调用写死为2，不会传1。
        }

        CancellationBO cancellationBO = iBillingCardService.doCancellationTempBillingCard(placeId, cardId, shiftId, refundType, SourceType.CASHIER);

        return new GenericResponse<>(new ObjDTO<>(cancellationBO));
    }

}
