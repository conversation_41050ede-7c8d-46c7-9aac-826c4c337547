package com.rzx.dim4.billing.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.repository.SysInterfaceRepository;

/**
 * 
 * <AUTHOR>
 * @date 2021年9月28日 下午3:00:45
 */
@Service
public class SysInterfaceService {

	@Autowired
	SysInterfaceRepository sysInterfaceRepository;

	public int update(int indexes, int useTime) {
		return sysInterfaceRepository.update(indexes, useTime);
	}

}
