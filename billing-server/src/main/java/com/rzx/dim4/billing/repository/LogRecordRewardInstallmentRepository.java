package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogRecordRewardInstallment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024年05月30日 10:44
 */
public interface LogRecordRewardInstallmentRepository extends JpaRepository<LogRecordRewardInstallment, Long>, JpaSpecificationExecutor<LogRecordRewardInstallment> {

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET status =2 WHERE once_present_amortized = 0 AND status =1 AND deleted = 0", nativeQuery = true)
    int clearCompletedTasks();

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET status = :status WHERE place_id = :placeId AND id_number = :idNumber AND deleted = 0", nativeQuery = true)
    int updateStatusByPlaceIdAndIdNumber(@Param("status") int status ,@Param("placeId") String placeId ,@Param("idNumber") String idNumber);

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET once_present_amortized = once_present_amortized -1 ,STATUS = IF(once_present_amortized = 0,2,1) WHERE id IN (:ids)", nativeQuery = true)
    int updateStatusAndOncePresentAmortizedByIds(@Param("ids") List<Long> ids);

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET deleted = 1 WHERE place_id IN (:placeIds) AND id_number = :idNumber AND deleted =0 AND status = 1", nativeQuery = true)
    int deleteRewardInstallment(@Param("placeIds") List<String> placeIds,@Param("idNumber") String idNumber);

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET deleted = 1 WHERE id= :id", nativeQuery = true)
    int deletedById(@Param("id") Long id);

    @Modifying
    @Transactional
    @Query(value = "UPDATE log_record_reward_installment SET deleted = 1 WHERE topup_order_id= :orderId", nativeQuery = true)
    int deletedByOrderId(@Param("orderId") String orderId);

    List<LogRecordRewardInstallment> findByDeletedAndStatus(int deleted,int status);

    Optional<LogRecordRewardInstallment> findByTopupOrderIdAndDeleted(String topupOrderId,int deleted);
}
