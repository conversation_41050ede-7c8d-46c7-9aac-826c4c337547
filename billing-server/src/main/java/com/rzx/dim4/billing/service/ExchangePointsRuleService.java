package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.ExchangePointsRule;
import com.rzx.dim4.billing.repository.ExchangePointsRuleRepository;
import lombok.Synchronized;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class ExchangePointsRuleService {

    @Autowired
    ExchangePointsRuleRepository exchangePointsRuleRepository;

    /**
     * 分页查询积分兑换规则信息
     *
     * @param map
     * @param pageable
     * @return
     */
    public Page<ExchangePointsRule> findAll(Map<String, String> map, Pageable pageable) {
        return exchangePointsRuleRepository.findAll(new Specification<ExchangePointsRule>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<ExchangePointsRule> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicates = new ArrayList<Predicate>();

                Predicate p1 = cb.equal(root.get("deleted"), 0);
                predicates.add(p1);

                // 场所ID
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    Predicate predicate = cb.equal(root.get("placeId").as(String.class), map.get("placeId"));
                    predicates.add(predicate);
                }

                Predicate[] p = new Predicate[predicates.size()];
                return cb.and(predicates.toArray(p));
            }
        }, pageable);
    }

    /**
     * 保存积分兑换规则
     * @param exchangePointsRule
     * @return
     */
    public ExchangePointsRule save (ExchangePointsRule exchangePointsRule) {
        return exchangePointsRuleRepository.save(exchangePointsRule);
    }

    /**
     * 根据规则id查询积分兑换规则信息
     * @param placeId
     * @param exchangePointsRuleId
     * @return
     */
    public Optional<ExchangePointsRule> findByPlaceIdAndExchangePointsRuleId(String placeId, String exchangePointsRuleId) {
        return exchangePointsRuleRepository.findByPlaceIdAndExchangePointsRuleIdAndDeleted(placeId,exchangePointsRuleId,0);
    }

    /**
     * 相同赠送金额 只能有一条
     * @param placeId
     * @param exchangeType
     * @param exchangePresent
     * @return
     */
    public Optional<ExchangePointsRule> findByPlaceIdAndExchangeTypeAndExchangePresent (String placeId, int exchangeType, int exchangePresent) {
        return exchangePointsRuleRepository.findByPlaceIdAndExchangeTypeAndExchangePresentAndDeleted(placeId, exchangeType, exchangePresent, 0);
    }

    /**
     * 相同积分 只能兑换一条
     * @param placeId
     * @param exchangeType
     * @param exchangePoints
     * @return
     */
    public Optional<ExchangePointsRule> findByPlaceIdAndExchangeTypeAndExchangePoints (String placeId, int exchangeType, int exchangePoints) {
        return exchangePointsRuleRepository.findByPlaceIdAndExchangeTypeAndExchangePointsAndDeleted(placeId, exchangeType, exchangePoints, 0);
    }

    /**
     * 根据场所,兑换类型--查询积分兑换规则
     * @param placeId
     * @param exchangeType
     * @return
     */
    public List<ExchangePointsRule> findByPlaceIdAndExchangeType (String placeId, int exchangeType) {
        return exchangePointsRuleRepository.findByPlaceIdAndExchangeTypeAndDeleted(placeId,exchangeType,0);
    }

    /**
     * 构建积分兑换规则id
     * @return
     */
    @Synchronized
    public synchronized String builderExchangePointsRuleId() {
        int exchangePointsRuleId = 6000;
        Optional<ExchangePointsRule> lastExchangePointsRule = exchangePointsRuleRepository.findTop1ByOrderByIdDesc();
        if (lastExchangePointsRule.isPresent()) {
            exchangePointsRuleId = Integer.parseInt(lastExchangePointsRule.get().getExchangePointsRuleId()) + 1;
        }
        return String.valueOf(exchangePointsRuleId);
    }

}
