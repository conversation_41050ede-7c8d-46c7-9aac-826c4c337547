package com.rzx.dim4.billing.service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.rzx.dim4.base.utils.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.rzx.dim4.billing.entity.LogReversal;
import com.rzx.dim4.billing.repository.LogReversalRepository;

@Service
public class LogReversalService {

	@Autowired
	LogReversalRepository logReversalRepository;

	public LogReversal save(LogReversal logReversal) {
		return logReversalRepository.save(logReversal);
	}

	public List<LogReversal> findByPlaceIdAndTopupOrderId(String placeId, String topupOrderId) {
		return logReversalRepository.findByPlaceIdAndTopupOrderId(placeId, topupOrderId);
	}

	public int sumCostTotalReversalByShiftId(String placeId, String shiftId) {  //todo log_reversql表从2024-8-27日后开始存入收银台减钱数据，等数据完整后可以替换 logoperationService 的 sumCostTotalReversalByShiftId方法
		LocalDateTime startDateTime;
		LocalTime currentTime = LocalTime.now();//获取当前时间
		int hour = currentTime.getHour();//获取当前是一天的几点
		// 如果当前时间是18、19、20、21 22，则默认查询的开始时间当前月第一天时间
		if (hour > 18 && hour < 22) {
			startDateTime = DateTimeUtils.monthStartTime();//本月第一天
		} else {
			startDateTime = LocalDateTime.now().plusMonths(-1); // 往前推1个月
		}
		LocalDateTime endDateTime = LocalDateTime.now();
		Integer result = logReversalRepository.sumCostTotalReversalByShiftId(placeId, shiftId, startDateTime, endDateTime);
		return result == null ? 0 : result;
	}

	public List<LogReversal> findListByPlaceIdAndCreated(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime){
		return logReversalRepository.findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId,startDateTime,endDateTime);
	}

	/**
	 * 按时间-总冲正
	 *
	 * @param placeIds
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	public int sumCostTotalReversalByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime) {
		Integer result = logReversalRepository.sumCostTotalReversalByDateTime(placeIds, startDateTime, endDateTime);
		return result == null ? 0 : result;
	}


	/**
	 * 按时间-卡Id-总冲正
	 *
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	public int sumCostTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
													   LocalDateTime endDateTime) {
		Integer result = logReversalRepository.sumCostTotalReversalByCardIdAndDateTime(placeId, cardId, startDateTime,
				endDateTime);
		return result == null ? 0 : result;
	}


	/**
	 * 按时间-卡Id-总冲正(本金+奖励)
	 *
	 * @param placeId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	public int sumTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
												   LocalDateTime endDateTime) {
		Integer result = logReversalRepository.sumTotalReversalByCardIdAndDateTime(placeId, cardId, startDateTime,
				endDateTime);
		return result == null ? 0 : result;
	}

	public Page<LogReversal> findAll(Map<String, String> map, Pageable pageable) {
		return logReversalRepository.findAll(new Specification<LogReversal>() {
			private static final long serialVersionUID = 1L;

			@Override
			public Predicate toPredicate(Root<LogReversal> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
				List<Predicate> andPredicateList = new ArrayList<>();
				List<Predicate> orPredicateList = new ArrayList<>();
				DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				// 网吧id
				if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
					andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
				}
				// 用户卡id
				if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {
					andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
				}
				// 用户卡身份证号
				if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
					andPredicateList.add(cb.equal(root.get("idNumber").as(String.class), map.get("idNumber")));
				}
//				// 订单来源
//				if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {
//					andPredicateList.add(cb.equal(root.get("sourceType").as(String.class), map.get("sourceType")));
//				}
//				// 状态
//				if (map.containsKey("refundStatus") && !StringUtils.isEmpty(map.get("refundStatus"))) {
//					andPredicateList.add(cb.equal(root.get("refundStatus").as(Integer.class), Integer.parseInt(map.get("refundStatus"))));
//				}
				// 班次
				if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {
					andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
				}
				// 充值订单
				if (map.containsKey("topupOrderId") && !StringUtils.isEmpty(map.get("topupOrderId"))) {
					andPredicateList.add(cb.equal(root.get("topupOrderId").as(String.class), map.get("topupOrderId")));
				}
				// 操作开始时间
				if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
					LocalDateTime startTime = LocalDateTime.parse(map.get("startDate"), fmt);
					andPredicateList.add(cb.greaterThanOrEqualTo(root.get("refundTime").as(LocalDateTime.class), startTime));
				}
				// 操作结束时间
				if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
					LocalDateTime endTime = LocalDateTime.parse(map.get("endDate"), fmt);
					andPredicateList.add(cb.lessThanOrEqualTo(root.get("refundTime").as(LocalDateTime.class), endTime));
				}

				Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
				if (orPredicateList.size() == 0) {
					return cb.and(andPredicateList.toArray(andPredicateArr));
				} else {
					Predicate[] orPredicateArr = new Predicate[orPredicateList.size()];
					Predicate andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));
					Predicate orPredicate = cb.or(orPredicateList.toArray(orPredicateArr));
					return cb.and(andPredicate, orPredicate);
				}
			}
		}, pageable);
	}

}
