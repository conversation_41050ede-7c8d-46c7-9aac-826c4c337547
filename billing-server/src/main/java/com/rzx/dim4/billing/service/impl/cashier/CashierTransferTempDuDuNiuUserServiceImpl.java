package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.TempDuDuNiuUser;
import com.rzx.dim4.billing.service.BillingCardService;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.LogShiftService;
import com.rzx.dim4.billing.service.TempDuDuNiuUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 将嘟嘟牛临时用户转成计费卡
 */
@Service
public class CashierTransferTempDuDuNiuUserServiceImpl implements CoreService {

    @Autowired
    TempDuDuNiuUserService tempDuDuNiuUserService;

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    BillingCardService billingCardService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() > 10) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardIds = params.get(2); // 嘟嘟牛用户ID，逗号分割
        String cardTypeId = params.get(3); // 卡类型ID
        String cardTypeName = params.get(4); // 卡类型姓名
        String idNumber = params.get(5).toUpperCase(); // 身份证号码
        String name = params.get(6); // 姓名
        String activeType = params.get(7); // 激活类型
        String phoneNumber = params.get(8); // 手机号
        String remark = params.get(9); // 备忘

        int cashAccount = 0; // 本金余额
        int presentAccount = 0; // 赠送余额
        int points = 0; // 积分

        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = optLogShift.get();

        List<String> cardIdList = Arrays.asList(cardIds.split(","));
        List<TempDuDuNiuUser> duduniuUserList = tempDuDuNiuUserService.findByPlaceIdAndCardIdIn(placeId, cardIdList);
        List<TempDuDuNiuUser> delUserList = new ArrayList<>();

        for (TempDuDuNiuUser user : duduniuUserList) {
            cashAccount += user.getCashAccount();
            presentAccount += user.getPresentAccount();
            points += user.getHeapCanUse();
            user.setDeleted(1);
            user.setUpdated(LocalDateTime.now());
            delUserList.add(user);
        }

        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
        if (optBillingCard.isPresent()) {
            BillingCard billingCard = optBillingCard.get();
            billingCard.setPresentAccount(billingCard.getPresentAccount() + presentAccount);
            billingCard.setCashAccount(billingCard.getCashAccount() + cashAccount);
            billingCard.setPoints(billingCard.getPoints() + points);
            billingCard.setRemark(billingCard.getRemark() + "[来自DuDuNiu]");
            billingCard.setDeleted(0);
            billingCard = billingCardService.save(billingCard);
            tempDuDuNiuUserService.saveAll(delUserList);
            return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
        }

        BillingCard billingCard = new BillingCard();
        billingCard.setCreated(LocalDateTime.now()); // 创建时间
        billingCard.setPlaceId(placeId);
        billingCard.setIdNumber(idNumber);
        billingCard.setCashAccount(cashAccount);
        billingCard.setPresentAccount(presentAccount);
        billingCard.setCardTypeId(cardTypeId);
        billingCard.setCardTypeName(cardTypeName);
        billingCard.setIdName(name);
        billingCard.setLoginName(idNumber);
//        billingCard.setLoginPass(passwordEncoder.encode("123456"));
        billingCard.setLoginPass("123456");//新版本不再加密
        billingCard.setPhoneNumber(phoneNumber);
        billingCard.setRemark(remark + "[来自DuDuNiu]");
        billingCard.setActiveTime(LocalDateTime.now()); // 激活时间
        int activeTypeInt = 0;
        try {
            activeTypeInt = Integer.parseInt(activeType);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        billingCard.setActiveType(ActiveType.getActiveTypes(activeTypeInt)); // 激活方式
        billingCard.setCreater(StringUtils.isEmpty(logShift) ? 0 : Long.parseLong(logShift.getAccountId()));
        billingCard.setUpdated(LocalDateTime.now());
        billingCard.setDeleted(0);
        billingCard.setChainCard(0);
        billingCard.setPoints(points);
        billingCard = billingCardService.save(billingCard);

        tempDuDuNiuUserService.saveAll(delUserList);

        return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
    }
}
