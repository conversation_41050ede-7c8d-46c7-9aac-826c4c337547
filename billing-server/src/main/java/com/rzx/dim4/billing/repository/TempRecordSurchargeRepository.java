package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.TempRecordSurcharge;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TempRecordSurchargeRepository extends JpaRepository<TempRecordSurcharge, Long> {

    Optional<TempRecordSurcharge> findByPlaceIdAndCardIdAndDeletedAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, String cardId, int deleted, LocalDateTime startTime, LocalDateTime endTime);

    List<TempRecordSurcharge> findByPlaceIdAndCardIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual (String placeId, String cardId, LocalDateTime startTime, LocalDateTime endTime);

    Optional<TempRecordSurcharge> findByPlaceIdAndCardIdAndDeleted (String placeId, String cardId, int deleted);

    Optional<TempRecordSurcharge> findByPlaceIdAndIdNumberAndDeleted (String placeId, String idNumber, int deleted);

    List<TempRecordSurcharge> findByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);
}
