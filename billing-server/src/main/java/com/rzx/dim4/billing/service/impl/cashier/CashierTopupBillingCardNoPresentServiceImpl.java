package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 充值卡自定义充值
 * 
 * <AUTHOR>
 * @date 2022年1月6日 下午5:40:59
 */
@Service
public class CashierTopupBillingCardNoPresentServiceImpl implements CoreService {

	@Autowired
	LogShiftService logShiftService;

	@Autowired
	LogLoginService logLoginService;

	@Autowired
	BillingCardService billingCardService;

	@Autowired
	BillingOnlineService billingOnlineService;

	@Autowired
	LogOperationService logOperationService;
	
	@Autowired
	LogTopupService logTopupService;

	@Override
	public GenericResponse<ObjDTO<BillingCardBO>> doService(List<String> params) {

		// 检查参数
		if (params.size() != 4) {
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		// 获取参数
		String placeId = params.get(0);
		String shiftId = params.get(1); // 班次ID
		String cardId = params.get(2);
		String amountStr = params.get(3); // 充值金额

		// 处理充金额和赠送金额
		int amount = 0;
		try {
			amount = Integer.parseInt(amountStr);
		} catch (NumberFormatException nfe) {
			return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
		}
		if (amount <= 0 || amount > 1000000) { // 充值范围 0元-1万元
			return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
		}

		// 查询卡信息
		Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
		if (!optBillingCard.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
		}
		BillingCard billingCard = optBillingCard.get();

		// 查询班次
		Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
		if (!optLogShift.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
		}
		LogShift logShift = optLogShift.get();

		// 查询在线信息
		Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
				cardId);
		BillingOnline billingOnline = null;
		if (optBillingOnline.isPresent()) {
			billingOnline = optBillingOnline.get();
		}

		LogLogin logLogin = null;
		if (billingOnline != null) {
			Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, cardId, billingOnline.getBillingTime());
			if (optLogLogin.isPresent()) {
				logLogin = optLogLogin.get();
			}
		}

		billingCard = billingCardService.billingCardTopup(amount, 0, placeId,cardId,billingCard.getCardTypeId(), logShift,0,SourceType.CASHIER);
		
		logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, amount, 0, billingCard, billingOnline,
				logShift, logLogin, null,0,0);
		
		LogTopup logTopup = new LogTopup();
		logTopup.setCardId(billingCard.getCardId());
		logTopup.setCashAmount(amount);
		logTopup.setCashBalance(billingCard.getCashAccount());
		logTopup.setClientId(billingOnline != null ? billingOnline.getClientId() : null);
		logTopup.setCreated(LocalDateTime.now());
		logTopup.setCreater(null);
		logTopup.setIdName(billingCard.getIdName());
		logTopup.setIdNumber(billingCard.getIdNumber());
		logTopup.setCardTypeId(billingCard.getCardTypeId());
		logTopup.setCardTypeName(billingCard.getCardTypeName());
		logTopup.setLoginId(logLogin != null ? logLogin.getLoginId() : null);
		logTopup.setOrderId("CASH" + System.currentTimeMillis());
		logTopup.setOperator(logShift.getAccountId());
		logTopup.setOperatorName(logShift.getLoginAccountName());
		logTopup.setStatus(3);
		logTopup.setPayType(PayType.CASH);
		logTopup.setPlaceId(billingCard.getPlaceId());
		logTopup.setPresentAmount(0);
		logTopup.setPresentBalance(billingCard.getPresentAccount());
		logTopup.setShiftId(logShift.getShiftId());
		logTopup.setSourceType(SourceType.CASHIER);
		logTopup.setTopupTime(LocalDateTime.now());
		logTopupService.save(logTopup);
		return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
	}

}
