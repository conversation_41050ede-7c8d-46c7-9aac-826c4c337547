package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.PeakOnline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface PeakOnlineRepository extends JpaRepository<PeakOnline, Long>, JpaSpecificationExecutor<PeakOnline> {

	Optional<PeakOnline> findByPlaceIdAndCountDayAndDeleted(String placeId,String countDay,int deleted);



}
