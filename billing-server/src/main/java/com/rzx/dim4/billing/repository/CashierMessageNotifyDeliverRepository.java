package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.CashierMessageNotifyDeliver;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.time.LocalDateTime;
import java.util.Optional;

public interface CashierMessageNotifyDeliverRepository extends JpaRepository<CashierMessageNotifyDeliver, Long>, JpaSpecificationExecutor<CashierMessageNotifyDeliver> {

    Optional<CashierMessageNotifyDeliver> findTop1ByStartTimeLessThanEqualAndEndTimeGreaterThanEqualAndPlaceIdsLikeOrderByIdDesc (LocalDateTime startTime, LocalDateTime endTime, String placeId);

}
