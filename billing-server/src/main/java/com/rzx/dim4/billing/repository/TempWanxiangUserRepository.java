package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.TempWanxiangUser;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TempWanxiangUserRepository extends JpaRepository<TempWanxiangUser, Long> {

    Optional<TempWanxiangUser> findTop1ByPlaceIdAndDeletedOrderByIdDesc(String placeId,int deleted);

    List<TempWanxiangUser> findByPlaceIdAndCardIdShowAndNameShowAndDeleted(String placeId, String cardIdShow, String heapCanUse, int deleted);

    List<TempWanxiangUser> findByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);


}
