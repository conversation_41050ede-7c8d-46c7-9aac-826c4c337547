package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.CashierAuthority;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.repository.CashierAuthorityRepository;
import com.rzx.dim4.billing.repository.LogShiftRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022年1月5日 上午9:41:32
 */
@Service
public class CashierAuthorityService {

    @Autowired
    CashierAuthorityRepository cashierAuthorityRepository;
    @Autowired
    LogShiftRepository logShiftRepository;

    public CashierAuthority save(CashierAuthority ashierAuthority) {
        return cashierAuthorityRepository.save(ashierAuthority);
    }

    public Optional<CashierAuthority> findByPlaceIdAndAccountId(String placeId, String accountId) {
        return cashierAuthorityRepository.findByPlaceIdAndAccountId(placeId, accountId);
    }
    public List<CashierAuthority> findByPlaceId(String placeId) {
        return cashierAuthorityRepository.findByPlaceId(placeId);
    }

    public Optional<CashierAuthority> findCurrCashierByPlaceId(String placeId, String shiftId) {
        Optional<LogShift> optLogShift = logShiftRepository.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            return Optional.empty();
        }
        LogShift logShift = optLogShift.get();
        return cashierAuthorityRepository.findByPlaceIdAndAccountId(placeId, logShift.getLoginAccountId());
    }


}
