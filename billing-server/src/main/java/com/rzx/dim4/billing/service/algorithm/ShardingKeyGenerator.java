package com.rzx.dim4.billing.service.algorithm;

import org.apache.shardingsphere.sharding.spi.KeyGenerateAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ShardingKeyGenerator implements KeyGenerateAlgorithm {

    @Autowired
    KeyAlgorithm keyAlgorithm;

    @Override
    public Comparable<?> generateKey() {
        // 这里自定义生成KEY的算法
        return keyAlgorithm.nextId();
    }

    @Override
    public void init() {

    }

    @Override
    public String getType() {
        return "idGenerator";
    }
}
