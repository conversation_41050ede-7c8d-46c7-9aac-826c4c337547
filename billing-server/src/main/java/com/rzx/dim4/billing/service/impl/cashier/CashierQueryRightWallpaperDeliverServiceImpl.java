package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.ClientWallpaperDeliver;
import com.rzx.dim4.billing.service.ClientWallpaperDeliverService;
import com.rzx.dim4.billing.service.CoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 收银台查询右侧素材栏图片
 * <AUTHOR>
 * @date 2025年03月20日 14:50
 */
@Service
public class CashierQueryRightWallpaperDeliverServiceImpl  implements CoreService {
    @Autowired
    ClientWallpaperDeliverService placeWallpaperDeliverService;

    @Override
    public GenericResponse<?> doService(List<String> params) {

        if (params.size() != 1) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }
        String placeId = params.get(0);

        Optional<ClientWallpaperDeliver> optDeliver = placeWallpaperDeliverService
                .findValidPlaceWallpaperDeliverByPlaceId(placeId,1);
        if (optDeliver.isPresent() && optDeliver.get().getIsDefault() == 0) {
            return new GenericResponse<>(new ObjDTO<>(optDeliver.get().toBO()));
        }
        return new GenericResponse<>(new ObjDTO<>());
    }
}
