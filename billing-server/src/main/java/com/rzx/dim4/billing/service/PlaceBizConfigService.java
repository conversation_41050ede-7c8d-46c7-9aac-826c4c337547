package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.repository.PlaceBizConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2021年11月15日 下午1:51:38
 */
@Service
public class PlaceBizConfigService {

	@Autowired
	private PlaceBizConfigRepository placeBizConfigRepository;

	@Autowired
	private PlaceServerService placeServerService;

	/**
	 * 如果没有查询到网吧的业务配置，新增一条新的，使用默认参数
	 * 
	 * @param placeId
	 * @return
	 */
	public PlaceBizConfig findByPlaceId(String placeId) {
		Optional<PlaceBizConfig> optPlaceBizConfig = placeBizConfigRepository.findByPlaceId(placeId);
		if (optPlaceBizConfig.isPresent()) {
			return optPlaceBizConfig.get();
		}
		PlaceBizConfig bizConfig = new PlaceBizConfig();
		GenericResponse<ObjDTO<PlaceProfileBO>> resp = placeServerService.findByPlaceId(placeId);
		if (resp.isResult()) {
			bizConfig.setType(resp.getData().getObj().getType());
			bizConfig.setBillingType(resp.getData().getObj().getBillingType());
		} else {
			bizConfig.setType(0);
			bizConfig.setBillingType(0);
		}
		bizConfig.setClientShutdownTimeout(300);
//		bizConfig.setMinConsume(0);
//		bizConfig.setUnitConsume(100);
		bizConfig.setMemberDayNum(0);
		bizConfig.setPlaceId(placeId);
		bizConfig.setPlaceRealnameDisabled(0);
		bizConfig.setCultureBoxFlag(1);
		bizConfig.setCreated(LocalDateTime.now());
		bizConfig.setCreater(-1L);
		bizConfig.setPayPatter("0,1,2,3,4");
		bizConfig.setClientQrCodeAuth("MPWECHAT");//默认赋值【四维管家公众号新版本】
		bizConfig.setCashierQrCodeAuth("MPWECHAT");//默认赋值【四维管家公众号新版本】
		bizConfig.setClientLoginMethod("1,0");// 默认扫码登录+密码登录
		bizConfig.setDefCardType("1000");// 默认开卡类型：临时卡
		bizConfig.setCertificateClient("CASHIER,CLIENT,WECHAT,WECHATAPP");
		bizConfig.setConstraintSubscribe(1);
		placeBizConfigRepository.save(bizConfig);
		return bizConfig;
	}

	/**
	 * 存储场所业务配置
	 *
	 * @param placeBizConfig 场所业务配置
	 */
	public void updatePlaceBizConfig(PlaceBizConfig placeBizConfig) {
		placeBizConfig.setUpdated(LocalDateTime.now());
		placeBizConfigRepository.save(placeBizConfig);
	}


	/**
	 * 关闭临时卡参与积分升级
	 */
	public int closeBizConfigTempCardPointsUpgrade(String placeId, int upgradeUserLevelFlag, int downgradeUserLevelFlag) {
		return placeBizConfigRepository.closeBizConfigTempCardPointsUpgrade(placeId,upgradeUserLevelFlag,downgradeUserLevelFlag);
	}

	public List<PlaceBizConfig> queryByPlaceIds(List<String> placeIds) {
		return placeBizConfigRepository.findByPlaceIdIn(placeIds);
	}

	public List<PlaceBizConfig> queryByThirdAccountIds(List<String> thirdAccountIds) {
		return placeBizConfigRepository.findByThirdAccountIdInAndDeleted(thirdAccountIds, 0);
	}

	public PlaceBizConfig save(PlaceBizConfig placeBizConfig){
		return placeBizConfigRepository.save(placeBizConfig);
	}
}
