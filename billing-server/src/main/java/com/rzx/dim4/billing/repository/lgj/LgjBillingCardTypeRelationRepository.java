package com.rzx.dim4.billing.repository.lgj;

import com.rzx.dim4.billing.entity.lgj.LgjBillingCardTypeRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface LgjBillingCardTypeRelationRepository extends JpaRepository<LgjBillingCardTypeRelation, Long>, JpaSpecificationExecutor<LgjBillingCardTypeRelation> {

    List<LgjBillingCardTypeRelation> findByPlaceId(String placeId);

    Optional<LgjBillingCardTypeRelation> findByPlaceIdAndLgjCardTypeId(String placeId,String lgjCardTypeId);

}
