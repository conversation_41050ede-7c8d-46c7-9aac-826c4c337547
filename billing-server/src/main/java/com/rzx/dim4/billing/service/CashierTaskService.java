package com.rzx.dim4.billing.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.CashierTask;
import com.rzx.dim4.billing.repository.CashierTaskRepository;

/**
 * 
 * <AUTHOR>
 * @date 2021年11月30日 下午3:56:14
 */
@Service
public class CashierTaskService {

	@Autowired
	CashierTaskRepository cashierTaskRepository;

	public CashierTask save(CashierTask cashierTask) {
		return cashierTaskRepository.save(cashierTask);
	}

	public Optional<CashierTask> findByPlaceIdAndTaskId(String placeId, String taskId) {
		return cashierTaskRepository.findByPlaceIdAndTaskId(placeId, taskId);
	}

	public List<CashierTask> findNotExecByPlaceId(String placeId) {
		return cashierTaskRepository.findByPlaceIdAndTaskStatus(placeId, 0);
	}

	public Page<CashierTask> findByPlaceId(String placeId, Pageable pageable) {
		return cashierTaskRepository.findByPlaceIdOrderByIdDesc(placeId, pageable);
	}

}
