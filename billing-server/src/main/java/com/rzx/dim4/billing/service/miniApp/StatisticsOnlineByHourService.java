package com.rzx.dim4.billing.service.miniApp;

import com.rzx.dim4.billing.entity.StatisticsOnlineByHour;
import com.rzx.dim4.billing.repository.miniApp.StatisticsOnlineByHourRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小程序查询上座率统计数据
 */
@Service
public class StatisticsOnlineByHourService {

    @Autowired
    private StatisticsOnlineByHourRepository statisticsOnlineByHourRepository;

    public List<StatisticsOnlineByHour> findByPlaceIdAndCountDayOrderById(String placeId,String countDay) {
        return statisticsOnlineByHourRepository.findByPlaceIdAndCountDayOrderById(placeId, countDay);
    }
}
