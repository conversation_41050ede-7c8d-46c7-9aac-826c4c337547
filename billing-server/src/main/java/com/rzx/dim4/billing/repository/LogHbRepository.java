package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.LogHb;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date Jun 30, 2020 5:22:45 PM
 */
public interface LogHbRepository extends JpaRepository<LogHb, Long> {

	int countByPlaceIdAndBillingFlag(String placeId, int billingFlag);
	
	int countByBillingFlag(int billingFlag);

	List<LogHb> findByBillingFlagAndHbTimeLessThan(int billingFlag, LocalDateTime localDateTime);

	@Modifying
	@Transactional
	@Query(value = "UPDATE log_hb SET billing_flag = 0, status = 2, updated = now() WHERE place_id = ?1 and client_id = ?2", nativeQuery = true)
	int stopBilling(String placeId, String clientId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE log_hb hb SET billing_flag = 1, status = 1, updated = now() WHERE place_id = ?1 and client_id = ?2", nativeQuery = true)
	int startBilling(String placeId, String clientId);

	Optional<LogHb> findByPlaceIdAndClientId(String placeId, String clientId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE log_hb SET status = ?3, updated = now() WHERE place_id = ?1 and client_id = ?2", nativeQuery = true)
	int updateStatus(String placeId, String clientId, int status);

	@Modifying
	@Transactional
	@Query(value = "UPDATE log_hb hb SET hb.hb_time = now(), hb.updated = now() WHERE hb.place_id = ?1 and hb.client_id = ?2", nativeQuery = true)
	int updateHeartBeatTime(String placeId, String clientId);

}
