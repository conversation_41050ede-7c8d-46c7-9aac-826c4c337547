package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BookSeatsBO;
import com.rzx.dim4.base.bo.billing.PlacementVO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.BillingOnline;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 机位图数据 查询、刷新缓存
 * <AUTHOR>
 * @date 2025年05月10日 11:32
 */
@Service
@Slf4j
public class PlacementService {


    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    private final static String PLACEMENT_CLIENT_KEY = "billing:placement_client_key_";

    /**
     * 换机专用刷新机位图接口，7在线换在线
     */
    @Async
    public void onlineToOnlineRefreshPlacementDate(String placeId,String clientIds,String type,
                                                   String idName,String idNumber,String cardId,
                                                   String newIdName,String newIdNumber,String newCardId){

        String[] clientArr = clientIds.split(",");
        if("7".equals(type) && clientArr.length == 2){
            log.info("换机在线换在线 - 刷新机位图redis中的缓存,参数 {}_{}_{}_{}_{}_{}",placeId,clientIds,type,idName,idNumber,cardId);
            for (int i = 0; i < clientArr.length; i++) {
                if(i == 0){
                    refreshPlacementDate(placeId,clientArr[i],"1",newIdName,newIdNumber,newCardId);
                }else{
                    refreshPlacementDate(placeId,clientArr[i],"1",idName,idNumber,cardId);
                }
            }
        }
    }

    /**
     * 换机专用刷新机位图接口，6普通换机（先结账原来机器），7在线换在线
     */
    @Async
    public void checkRefreshPlacementDate(String placeId,String clientIds,String type,
                                     String idName,String idNumber,String cardId){

        String[] clientArr = clientIds.split(",");
        if(("6".equals(type) || "7".equals(type)) && clientArr.length == 2){
            log.info("换机 - 刷新机位图redis中的缓存,参数 {}_{}_{}_{}_{}_{}",placeId,clientIds,type,idName,idNumber,cardId);
            for (int i = 0; i < clientArr.length; i++) {
                if(i == 0 && "6".equals(type)) {
                    //下机
                    refreshPlacementDate(placeId,clientArr[i],"2",idName,idNumber,cardId);
                }else {
                    //上机
                    refreshPlacementDate(placeId,clientArr[i],"1",idName,idNumber,cardId);
                }
            }
        }
    }
    /**
     * 刷新redis PLACEMENT_CLIENT_KEY 中的数据
     * @param placeId 场所id
     * @param clientIds 客户端ids
     * @param type 1上机，2结账，3包时，4订座，5取消订座
     * @param idName 姓 名
     * @param idNumber 证件号
     * @param cardId 卡id
     */
    @Async
    public void refreshPlacementDate(String placeId,String clientIds,String type,
                                     String idName,String idNumber,String cardId){
        try {
            String[] split = clientIds.split(",");

            for (String clientId : split) {
                log.info("刷新机位图redis中的缓存,参数 {}_{}_{}_{}_{}_{}",placeId,clientId,type,idName,idNumber,cardId);
                String redisKey = PLACEMENT_CLIENT_KEY+placeId+"_"+clientId;
                String json = stringRedisTemplate.opsForValue().get(redisKey);
                PlacementVO placementVO = null;
                if(!StringUtils.isEmpty(json)){
                    placementVO = new Gson().fromJson(json, PlacementVO.class);
                }else {
                    placementVO = new PlacementVO();
                }

                placementVO.setPlaceId(placeId);
                placementVO.setClientId(clientId);
                placementVO.setIdName(idName);
                placementVO.setCardId(cardId);
                if("1".equals(type) || "3".equals(type)){
                    placementVO.setIsOnline(1);
                    placementVO.setIsBookSeats(0);
                    placementVO.setSex(StringUtils.isEmpty(idNumber) ? null: getGender(idNumber));
                    if("3".equals(type)){
                        placementVO.setIsPackageTime(1);
                    }
                }else if("2".equals(type) || "5".equals(type)){
                    placementVO.setIsOnline(0);
                    placementVO.setIsBookSeats(0);
                    placementVO.setIsPackageTime(0);
                    if("2".equals(type)){
                        placementVO.setLogoutTime(LocalTime.now());
                    }
                }else if("4".equals(type) ){
                    placementVO.setIsOnline(0);
                    placementVO.setIsBookSeats(1);
                    placementVO.setIsPackageTime(0);
                }else if("999x".equals(type)){ //全部清除
                    if(!StringUtils.isEmpty(clientId)){
                        placementVO.setPlaceId(placeId);
                        placementVO.setClientId(clientId);
                        placementVO.setIdName(idName);
                        placementVO.setCardId(cardId);
                        placementVO.setIsOnline(0);
                        placementVO.setIsBookSeats(0);
                        placementVO.setIsPackageTime(0);
                        placementVO.setLogoutTime(null);
                    }else{
                        //清除所有
                    }
                }
                json = new Gson().toJson(placementVO);
                stringRedisTemplate.opsForValue().set(redisKey,json,1, TimeUnit.SECONDS); //缓存一分钟 （前端设置的5秒查询一次）
            }


        }catch (Exception e){
            log.info("刷新机位图redis缓存失败！参数: {}_{}_{}",placeId,clientIds,type);
            e.printStackTrace();
        }
    }

    private static String getGender(String idCard) {
        if (idCard == null || idCard.length() < 15) return "未知";
        int genderPos = (idCard.length() == 15) ? 14 : 16; // 确定性别位位置
        char code = idCard.charAt(genderPos);
        return (Character.isDigit(code) && (code - '0') % 2 != 0) ? "男" : (Character.isDigit(code) ? "女" : "未知");
    }
}
