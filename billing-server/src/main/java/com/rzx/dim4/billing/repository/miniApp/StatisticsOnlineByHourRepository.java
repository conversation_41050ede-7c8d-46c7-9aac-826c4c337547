package com.rzx.dim4.billing.repository.miniApp;

import com.rzx.dim4.billing.entity.StatisticsOnlineByHour;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface StatisticsOnlineByHourRepository extends JpaRepository<StatisticsOnlineByHour, Long>, JpaSpecificationExecutor<StatisticsOnlineByHour> {

    List<StatisticsOnlineByHour> findByPlaceIdAndCountDayOrderById(String placeId,String countDay);
}
