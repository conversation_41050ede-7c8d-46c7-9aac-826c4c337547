package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.*;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.billing.PackageTimeReserveStatus;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.BillingRulePackageTime;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.PackageTimeReserve;
import com.rzx.dim4.billing.repository.PackageTimeReserveRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021年9月6日 下午5:53:03
 */
@Service
@Slf4j
public class PackageTimeReserveService {

    @Autowired
    PackageTimeReserveRepository packageTimeReserveRepository;

    public PackageTimeReserve save(PackageTimeReserve cashierTempPackageTime) {
        return packageTimeReserveRepository.save(cashierTempPackageTime);
    }

    public Optional<PackageTimeReserve> findTop1ByPlaceIdAndCardIdOrderByIdDesc(String placeId, String cardId) {
        return packageTimeReserveRepository.findTop1ByPlaceIdAndCardIdAndEndTimeGreaterThanOrderByIdDesc(placeId,
                cardId, LocalDateTime.now());
    }


    //查询当前用户的预包时记录，如果预包时记录已失效设置为失效
    public Optional<PackageTimeReserve> findUnusedByPlaceIdAndCardId(String placeId, String cardId) {
        Optional<PackageTimeReserve> byPlaceIdAndCardIdAndStatus = packageTimeReserveRepository.findByPlaceIdAndCardIdAndStatus(placeId, cardId, 0);
        if (byPlaceIdAndCardIdAndStatus.isPresent()) {
            PackageTimeReserve packageTimeReserve = byPlaceIdAndCardIdAndStatus.get();
            if (LocalDateTime.now().compareTo(packageTimeReserve.getEndTime()) > 0) {
                updateInvalidationByPlaceIdAndCardIdAndRuleId(packageTimeReserve.getPlaceId(), packageTimeReserve.getCardId(), packageTimeReserve.getRuleId());
                return Optional.empty();
            } else {
                return byPlaceIdAndCardIdAndStatus;
            }
        } else {
            return byPlaceIdAndCardIdAndStatus;
        }
    }

    //查询当前用户的预包时记录，如果预包时记录已失效设置为失效
    public PackageTimeReserve queryByPlaceIdAndCardIdAndOrderId(String placeId, String cardId, String orderId) {
        log.info("查询预包时记录 placeId={}, cardId={}, orderId={}", placeId, cardId, orderId);

        Optional<PackageTimeReserve> packageTimeReserveOptional = packageTimeReserveRepository.findByPlaceIdAndCardIdAndOrderId(placeId, cardId, orderId);
        if (packageTimeReserveOptional.isPresent()) {
            PackageTimeReserve packageTimeReserve = packageTimeReserveOptional.get();
            if (LocalDateTime.now().isAfter(packageTimeReserve.getEndTime())) {
                // 更新预包时记录状态为2
                packageTimeReserveRepository.updateInvalidationByPlaceIdAndCardIdAndOrderId(placeId, cardId, orderId);

                packageTimeReserve.setStatus(PackageTimeReserveStatus.USED.getCode());
                packageTimeReserve.setUpdated(LocalDateTime.now());
            }

            log.info("根据订单号{}查询预包时记录 queryByPlaceIdAndCardIdAndOrderId.packageTimeReserve={}", orderId, new Gson().toJson(packageTimeReserve));

            return packageTimeReserve;
        } else {
            log.info("根据订单号{},未查询到预包时记录", orderId);
            return null;
        }
    }

    public PackageTimeReserveStatusBO queryPackageTimeStatus(PackageTimeReserveStatusBO packageTimeReserveStatusBo) {
        for (PackageTimeReserveStatusBO.RequestItem params : packageTimeReserveStatusBo.getRequestItemList()) {
            Optional<PackageTimeReserve> packageTimeReserveOptional = packageTimeReserveRepository.findByPlaceIdAndCardIdAndOrderId(params.getPlaceId(), params.getCardId(), params.getOrderId());
            packageTimeReserveOptional.ifPresent(packageTimeReserve -> {
                packageTimeReserveStatusBo.getResponseItemList().add(new PackageTimeReserveStatusBO.ResponseItem(params.getOrderId(), packageTimeReserve.getStatus()));
            });
        }
        return packageTimeReserveStatusBo;
    }

    public void updatePackageTimeStatus(PackageTimeReserveUpdateStatusBO params) {
        // 优先用orderId查询预包时记录，如果没有再用idNumber查询
        if (!StringUtils.isEmpty(params.getOrderId())) {
            Optional<PackageTimeReserve> packageTimeReserveOptional = packageTimeReserveRepository.findByPlaceIdAndCardIdAndOrderId(params.getPlaceId(), params.getCardId(), params.getOrderId());
            if (packageTimeReserveOptional.isPresent()) {
                log.info("A.根据orderId更新PackageTimeReserve={}", new Gson().toJson(packageTimeReserveOptional.get()));
                this.doUpdatePackageTimeStatus(packageTimeReserveOptional.get());
            } else {
                PackageTimeReserve packageTimeReserve = packageTimeReserveRepository.findTop1ByPlaceIdAndCardIdAndIdNumberAndRuleIdOrderByIdDesc(params.getPlaceId(), params.getCardId(), params.getIdNumber(), params.getRuleId());
                log.info("B.更新PackageTimeReserve={}", new Gson().toJson(packageTimeReserve));
                this.doUpdatePackageTimeStatus(packageTimeReserve);
            }
        } else {
            PackageTimeReserve packageTimeReserve = packageTimeReserveRepository.findTop1ByPlaceIdAndCardIdAndIdNumberAndRuleIdOrderByIdDesc(params.getPlaceId(), params.getCardId(), params.getIdNumber(), params.getRuleId());
            log.info("C.更新PackageTimeReserve={}", new Gson().toJson(packageTimeReserve));
            this.doUpdatePackageTimeStatus(packageTimeReserve);
        }
    }

    public void updatePackageTimeStatusByPlaceIdAndOrderId(String placeId, String orderId, PackageTimeReserveStatus status) {
        status = status == null ? PackageTimeReserveStatus.USED : status;
        log.info("更新状态 updatePackageTimeStatusByPlaceIdAndOrderId place={}, orderId={}, status={}", placeId, orderId, status.getCode());
        packageTimeReserveRepository.updateInvalidationByPlaceIdAndOrderId(placeId, orderId, status.getCode());
    }

    private void doUpdatePackageTimeStatus(PackageTimeReserve packageTimeReserve) {
        if (packageTimeReserve != null) {
            packageTimeReserve.setStatus(PackageTimeReserveStatus.USED.getCode());
            packageTimeReserve.setUpdated(LocalDateTime.now());
            packageTimeReserveRepository.save(packageTimeReserve);
        }
    }

    public GenericResponse<ObjDTO<PackageTimeReserveSumBO>> sumPackageTime(String placeId, LocalDateTime startTime, LocalDateTime endTime) {

        PackageTimeReserveSumBO packageTimeReserveSumBO = new PackageTimeReserveSumBO();

        Integer sum1 = packageTimeReserveRepository.sumPackageTime(placeId, startTime, endTime, 3);
        packageTimeReserveSumBO.setOnlineSum(sum1);

        Integer sum2 = packageTimeReserveRepository.sumPackageTime(placeId, startTime, endTime, 4);
        packageTimeReserveSumBO.setThirdSum(sum2);

        Integer sum3 = packageTimeReserveRepository.sumPackageTime(placeId, startTime, endTime);
        packageTimeReserveSumBO.setNetSum(sum3);

        Integer count = packageTimeReserveRepository.countPackageTime(placeId, startTime, endTime);
        packageTimeReserveSumBO.setOrderNormalCount(count);

        return new GenericResponse<>(new ObjDTO<>(packageTimeReserveSumBO));
    }


    public List<PackageTimeReserve> findUnusedByPlaceIdsAndIdNumbers(List<String> placeIds, List<String> idNumbers) {
        return packageTimeReserveRepository.findByPlaceIdInAndIdNumberInAndStatus(placeIds, idNumbers, 0);
    }

    public List<PackageTimeReserve> findByPlaceIdAndStatus(String placeId) {
        return packageTimeReserveRepository.findByPlaceIdAndStatus(placeId, 0);
    }

    public int updateUnusedCashierTempPackageTimeInvalidation() {
        return packageTimeReserveRepository.updateUnusedCashierTempPackageTimeInvalidation();
    }

    public int updateInvaildByPlaceIdAndCardId(String placeId, String cardId) {
        return packageTimeReserveRepository.updateInvaildByPlaceIdAndCardId(placeId, cardId);
    }

    public int updateInvalidationByPlaceIdAndCardId(String placeId, String cardId) {
        return packageTimeReserveRepository.updateInvalidationByPlaceIdAndCardId(placeId, cardId);
    }

    public int updateInvalidationByPlaceIdAndCardIdAndRuleId(String placeId, String cardId, String ruleId) {
        log.info("清除预包时:::{} {} {}", placeId, cardId, ruleId);
        return packageTimeReserveRepository.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, cardId, ruleId);
    }

    public int clearPackageTimeReserveByPlaceIdAndCardId(String placeId, String cardId) {
        log.info("clearPackageTimeReserveByPlaceIdAndCardId 清除预包时:::{} {} {}", placeId, cardId);
        return packageTimeReserveRepository.clearPackageTimeReserveByPlaceIdAndCardId(placeId, cardId);
    }

    public List<PackageTimeReserve> findByPlaceIdAndRuleId(String placeId, String ruleId) {
        return packageTimeReserveRepository.findByPlaceIdAndRuleIdAndStatus(placeId, ruleId, 0);
    }

    private void doAddPackageTimeReserveRecord(LocalDateTime startTime,
                                  LocalDateTime endTime,
                                  BillingCard billingCard,
                                  BillingRulePackageTime billingRulePackageTime,
                                  LogShift logShift,
                                  int limitedLogin,
                                  int packagePayFlag,
                                  int costTemporaryOnlineAccount,
                                  int costCashAccount,
                                  int costPresentAccount,
                                  String orderId, PackageTimeReserveStatus status) {
        LocalDateTime now = LocalDateTime.now();
        // 新生成一条记录，保存入库
        PackageTimeReserve packageTimeReserve = new PackageTimeReserve();
        packageTimeReserve.setOrderId(orderId); // 这里新增orderId字段
        packageTimeReserve.setStartTime(startTime);
        packageTimeReserve.setEndTime(endTime);
        packageTimeReserve.setPlaceId(billingCard.getPlaceId());
        packageTimeReserve.setCardId(billingCard.getCardId());
        packageTimeReserve.setIdNumber(billingCard.getIdNumber());
        packageTimeReserve.setIdName(billingCard.getIdName());
        packageTimeReserve.setAreaIds(billingRulePackageTime.getAreaIds());
        packageTimeReserve.setRuleId(billingRulePackageTime.getRuleId());
        packageTimeReserve.setCreater(StringUtils.isEmpty(logShift) ? -1 : Long.parseLong(logShift.getAccountId()));
        packageTimeReserve.setShiftId(StringUtils.isEmpty(logShift) ? "" : logShift.getShiftId());
        packageTimeReserve.setCreated(now);
        packageTimeReserve.setStatus(status.getCode());
        packageTimeReserve.setPrice(billingRulePackageTime.getPrice());
        packageTimeReserve.setCostTemporaryOnlineAccount(costTemporaryOnlineAccount);
        packageTimeReserve.setLimitedLogin(limitedLogin);
        packageTimeReserve.setPackagePayFlag(packagePayFlag);
        if (1 == packagePayFlag && "1000".equals(billingCard.getCardTypeId())) {
            packageTimeReserve.setCostCashAccount(0);
            packageTimeReserve.setCostTemporaryOnlineAccount(costCashAccount);
            packageTimeReserve.setCostPresentAccount(costPresentAccount);
        } else {
            packageTimeReserve.setCostCashAccount(costCashAccount);
            packageTimeReserve.setCostPresentAccount(costPresentAccount);
        }

        packageTimeReserveRepository.save(packageTimeReserve);
    }


    /**
     * 写一条预包时状态为2的入库记录，仅仅是方便退款时查询
     */
    public void addUsedPackageTimeReserveRecord(BillingCard billingCard, BillingRulePackageTime billingRulePackageTime,
                                                LogShift logShift, int costCashAccount, int costPresentAccount, String orderId) {
        // 本条记录仅仅是方便退款时查询做一些判断，所以这里的startTime和endTime不重要，仅仅是填充数据
        this.doAddPackageTimeReserveRecord(LocalDateTime.now(), LocalDateTime.now().minusHours(1L), billingCard, billingRulePackageTime, logShift, 0,
                1, 0, costCashAccount, costPresentAccount, orderId, PackageTimeReserveStatus.USED);
    }

    /**
     * 预包时入库操作(有orderId)
     */
    public void futurePackageTime(LocalDateTime startTime,
                                  LocalDateTime endTime,
                                  BillingCard billingCard,
                                  BillingRulePackageTime billingRulePackageTime,
                                  LogShift logShift,
                                  int limitedLogin,
                                  int packagePayFlag,
                                  int costTemporaryOnlineAccount,
                                  int costCashAccount,
                                  int costPresentAccount,
                                  String orderId) {

        this.doAddPackageTimeReserveRecord(startTime, endTime, billingCard, billingRulePackageTime, logShift, limitedLogin,
                packagePayFlag, costTemporaryOnlineAccount, costCashAccount, costPresentAccount, orderId, PackageTimeReserveStatus.NOT_USED);
    }

    /**
     * 预包时入库操作(没有orderId)
     */
    public void futurePackageTime(LocalDateTime startTime,
                                  LocalDateTime endTime,
                                  BillingCard billingCard,
                                  BillingRulePackageTime billingRulePackageTime,
                                  LogShift logShift,
                                  int limitedLogin,
                                  int packagePayFlag,
                                  int costTemporaryOnlineAccount,
                                  int costCashAccount,
                                  int costPresentAccount) {
        // 调用上面的方法，传入orderId为null
        this.doAddPackageTimeReserveRecord(startTime, endTime, billingCard, billingRulePackageTime, logShift, limitedLogin,
                packagePayFlag, costTemporaryOnlineAccount, costCashAccount, costPresentAccount, null, PackageTimeReserveStatus.NOT_USED);
    }

    public PackageTimeReserveQueryBO getBatchPackageTimeStatus(PackageTimeReserveQueryBO params) {
        if (StringUtils.isEmpty(params.getPlaceId()) || CollectionUtils.isEmpty(params.getIdNumbers())) {
            return params;
        }

        params.getIdNumbers().forEach(idNumber -> {
            PackageTimeReserveQueryResultBO resultBo = new PackageTimeReserveQueryResultBO();
            resultBo.setIdNumber(idNumber);

            packageTimeReserveRepository.findTop1ByPlaceIdAndIdNumberAndStatusOrderByIdDesc(params.getPlaceId(), idNumber, PackageTimeReserveStatus.NOT_USED.getCode())
                    .ifPresent(a -> resultBo.setReverseRuleId(a.getRuleId()));

            packageTimeReserveRepository.findTop1ByPlaceIdAndIdNumberAndStatusOrderByIdDesc(params.getPlaceId(), idNumber, PackageTimeReserveStatus.USING.getCode())
                    .ifPresent(a -> resultBo.setRuleId(a.getRuleId()));

            params.getResultMap().put(idNumber, resultBo);
        });

        return params;
    }
}
