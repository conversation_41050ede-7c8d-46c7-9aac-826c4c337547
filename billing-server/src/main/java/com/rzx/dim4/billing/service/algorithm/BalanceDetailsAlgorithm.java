package com.rzx.dim4.billing.service.algorithm;

import com.google.gson.Gson;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.utils.ResultHandleUtil;
import com.rzx.dim4.billing.entity.BalanceDetails;
import com.rzx.dim4.billing.entity.BaseEntity;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.service.BalanceDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 网费余额明细
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BalanceDetailsAlgorithm {

    @Autowired
    private BalanceDetailsService balanceDetailsService;

    /**
     * 保存网费余额明细公用方法
     *
     * @param nowTime
     * @param billingCard   会员卡
     * @param clientId      客户端ID
     * @param operationType 操作类型，0上机扣费，1在线充值网费，2在线购买包时，
     *                      3网费购买包时，4 网费购买商品，5换机补差价，6 上机登录扣费，7订座扣网费，
     *                      8预包时退费，9临时卡结账销卡，10 IOT网费赠送
     * @param type          网费余额类型，0支出，1收
     * @param costCash      本金
     * @param costPresent   奖励
     * @param description   说明
     * @param tempCash      临时增加的账户金额，如在线包时后要给本金加一笔包时花费的金额。
     * @param isAmountZero  操作金额是否为0,0代表否，1代表是。包时成功后立马生效的时候要写一条包时剩余时间，此时amount操作金额是0，
     * @return
     */
    @Async
    public void saveBalanceDetails(LocalDateTime nowTime, BillingCard billingCard, String clientId,
                                   int operationType, int type, int costCash, int costPresent,
                                   String description, int tempCash, int isAmountZero, String inviteCode) {

        log.info("BalanceDetailsAlgorithm.saveBalanceDetails::::::::costCash={},costPresent={}", costCash, costPresent);

        // 创建基础记录对象（公共字段）
        BalanceDetails baseDetails = createBaseDetails(
                billingCard,
                nowTime,
                clientId,
                operationType,
                type,
                description,
                tempCash,
                inviteCode
        );
        baseDetails.setChainId(billingCard.getChainId());
        log.info("BalanceDetailsAlgorithm::::BalanceDetails.baseDetails={}", new Gson().toJson(baseDetails));

        // 保存记录列表
        List<BalanceDetails> recordsToSaveList = new ArrayList<>();
        // 处理现金扣费记录
        if (costCash > 0) {
            BalanceDetails cashRecord = new BalanceDetails(); // 复制基础记录
            BeanUtils.copyProperties(baseDetails, cashRecord);
            if (isAmountZero == 0) {
                cashRecord.setAmount(costCash);
                cashRecord.setAccountType(0);
                cashRecord.setBalanceAmount(baseDetails.getCashAccount());
            } else {
                cashRecord.setAmount(0);
                cashRecord.setAccountType(1);
                //cashRecord.setBalanceAmount(baseDetails.getCashAccount() +  baseDetails.getPresentAccount());//  当包时生效展示时，展示所有余额
                cashRecord.setBalanceAmount(baseDetails.getPresentAccount());//  当包时生效展示时，展示网费奖励
            }
            recordsToSaveList.add(cashRecord);

            log.info("BalanceDetailsAlgorithm::::BalanceDetails.cashRecord={}", new Gson().toJson(cashRecord));
        }

        // 处理奖励余额扣费记录
        if (costPresent > 0) {
            BalanceDetails presentRecord = new BalanceDetails(); // 复制基础记录
            BeanUtils.copyProperties(baseDetails, presentRecord);
            presentRecord.setAmount(costPresent);
            presentRecord.setAccountType(1);
            if (presentRecord.getOperationType() == 1 && "在线充值".equals(baseDetails.getDescription())) {
                presentRecord.setDescription("赠送网费");
            }
            presentRecord.setBalanceAmount(baseDetails.getPresentAccount());
            recordsToSaveList.add(presentRecord);

            log.info("BalanceDetailsAlgorithm::::BalanceDetails.presentRecord={}", new Gson().toJson(presentRecord));
        }

        // 保存所有记录
        if (CollectionUtils.isNotEmpty(recordsToSaveList)) {
            try {
                balanceDetailsService.saveAll(recordsToSaveList);
            } catch (Exception e) {
                log.info("保存网费余额明细错误: {}", e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建基础记录（公共字段）
     */
    private BalanceDetails createBaseDetails(
            BillingCard billingCard,
            LocalDateTime nowTime,
            String clientId,
            int operationType,
            int type,
            String description,
            int tempCash,
            String inviteCode
    ) {
        BalanceDetails balanceDetails = new BalanceDetails();
        balanceDetails.setPlaceId(billingCard.getPlaceId());
        balanceDetails.setCreated(nowTime);
        balanceDetails.setDeleted(BaseEntity.NO);
        balanceDetails.setCardId(billingCard.getCardId());
        balanceDetails.setIdNumber(billingCard.getIdNumber());
        balanceDetails.setPlaceName("");
        if (clientId != null) {
            SpecialPlaceClients specialPlaceClient = SpecialPlaceClients.findByClientId(clientId);
            if (specialPlaceClient != null) {
                balanceDetails.setClientId(specialPlaceClient.getClientId());
                balanceDetails.setClientName(specialPlaceClient.getClientName());
            } else {
                balanceDetails.setClientId(clientId);
                balanceDetails.setClientName("");
            }
        } else {
            balanceDetails.setClientId("");
            balanceDetails.setClientName("");
        }
        balanceDetails.setPresentAccount(billingCard.getPresentAccount());//奖励剩余
        balanceDetails.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount() + tempCash); //本金剩余：线上金额+现金金额+临时本金（在线包时）
        balanceDetails.setTemporaryOnlineAccount(billingCard.getTemporaryOnlineAccount()); //临时卡线上金额
        balanceDetails.setOperationType(operationType);
        balanceDetails.setType(type);
        balanceDetails.setDescription(description);
        balanceDetails.setInviteCode(inviteCode);
        balanceDetails.setChainId(billingCard.getChainId());

        log.info("BalanceDetailsAlgorithm::::BalanceDetails.createBaseDetails={}", new Gson().toJson(balanceDetails));

        return balanceDetails;
    }


    /**
     * 保存网费余额明细公用方法
     *
     * @param oldBillingCard 会员卡
     * @param costCash       本金
     * @param costPresent    奖励
     */
    @Async
    public void saveBalanceDetailsForMiniApp(BillingCard oldBillingCard, int costCash, int costPresent) {
        log.info("BalanceDetailsAlgorithm.saveBalanceDetailsForMiniApp::::::::costCash={}, costPresent={}", costCash, costPresent);

        BalanceDetails baseDetails = createBaseDetailsForMiniApp(oldBillingCard);

        List<BalanceDetails> toSaveBalanceDetailList = new ArrayList<>();

        if (costCash > 0) {
            BalanceDetails cashRecord = new BalanceDetails();
            BeanUtils.copyProperties(baseDetails, cashRecord);
            cashRecord.setAmount(costCash);
            cashRecord.setAccountType(0);
            cashRecord.setDescription("在线充值");
            cashRecord.setBalanceAmount(oldBillingCard.getCashAccount() + oldBillingCard.getTemporaryOnlineAccount() + costCash);
            cashRecord.setPresentAccount(oldBillingCard.getPresentAccount());
            cashRecord.setCashAccount(oldBillingCard.getCashAccount() + oldBillingCard.getTemporaryOnlineAccount() + costCash);
            cashRecord.setTemporaryOnlineAccount(oldBillingCard.getTemporaryOnlineAccount());

            toSaveBalanceDetailList.add(cashRecord);

            log.info("saveBalanceDetailsForMiniApp.cashRecord={}", new Gson().toJson(cashRecord));
        }

        if (costPresent > 0) {
            BalanceDetails presentRecord = new BalanceDetails();
            BeanUtils.copyProperties(baseDetails, presentRecord);
            presentRecord.setAmount(costPresent);
            presentRecord.setAccountType(1);
            presentRecord.setDescription("赠送网费");
            presentRecord.setBalanceAmount(oldBillingCard.getPresentAccount() + costPresent);
            presentRecord.setPresentAccount(oldBillingCard.getPresentAccount() + costPresent);
            presentRecord.setCashAccount(oldBillingCard.getCashAccount() + oldBillingCard.getTemporaryOnlineAccount() + costCash);
            presentRecord.setTemporaryOnlineAccount(oldBillingCard.getTemporaryOnlineAccount());

            toSaveBalanceDetailList.add(presentRecord);

            log.info("saveBalanceDetailsForMiniApp.presentRecord={}", new Gson().toJson(presentRecord));
        }

        // 保存所有记录
        ResultHandleUtil.handleList(toSaveBalanceDetailList, () -> balanceDetailsService.saveAll(toSaveBalanceDetailList));
    }

    /**
     * 创建基础记录
     */
    private BalanceDetails createBaseDetailsForMiniApp(BillingCard billingCard) {
        BalanceDetails balanceDetails = new BalanceDetails();
        balanceDetails.setPlaceId(billingCard.getPlaceId());
        balanceDetails.setCreated(LocalDateTime.now());
        balanceDetails.setDeleted(0);
        balanceDetails.setCardId(billingCard.getCardId());
        balanceDetails.setIdNumber(billingCard.getIdNumber());
        balanceDetails.setPlaceName("");
        balanceDetails.setClientId(SpecialPlaceClients.MINIAPP.getClientId());
        balanceDetails.setClientName(SpecialPlaceClients.MINIAPP.getClientName());
        balanceDetails.setPresentAccount(billingCard.getPresentAccount());
        balanceDetails.setCashAccount(billingCard.getCashAccount() + billingCard.getTemporaryOnlineAccount());
        balanceDetails.setTemporaryOnlineAccount(billingCard.getTemporaryOnlineAccount());
        balanceDetails.setOperationType(1);
        balanceDetails.setType(1);
        balanceDetails.setInviteCode(null);
        balanceDetails.setChainId(billingCard.getChainId());
        return balanceDetails;
    }
}