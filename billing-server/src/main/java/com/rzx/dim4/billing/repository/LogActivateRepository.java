package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.enums.billing.OperationType;
import com.rzx.dim4.billing.entity.LogActivate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月13日 上午9:38:05
 */
public interface LogActivateRepository extends JpaRepository<LogActivate, Long> {
	Page<LogActivate> findAll(Specification<LogActivate> specification, Pageable pageable);

	Optional<LogActivate> findTop1ByPlaceIdAndCardIdAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String cardId, OperationType operationType, LocalDateTime startTime, LocalDateTime endTime);

	Optional<LogActivate> findTop1ByPlaceIdAndIdNumberAndOperationTypeAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(String placeId, String idNumber,OperationType operationType, LocalDateTime startDateTime, LocalDateTime endDateTime);
}
