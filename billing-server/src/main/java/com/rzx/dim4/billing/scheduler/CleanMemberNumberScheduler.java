package com.rzx.dim4.billing.scheduler;

import com.rzx.dim4.base.bo.place.PlaceChainBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.place.PlaceChainApi;
import com.rzx.dim4.billing.entity.SecurityRequestConfig;
import com.rzx.dim4.billing.service.SecurityRequestConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 清理收银台、网吧后台会员管理页面已请求会员数量定时任务
 *
 * <AUTHOR>
 * @since 2024/05/10
 **/
@Slf4j
@Component
public class CleanMemberNumberScheduler {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    SecurityRequestConfigService securityRequestConfigService;

    @Autowired private PlaceChainApi placeChainApi;

    String cleanMemberNumberLockKey = "billing:lock:clean_member_number";

    String cashierPre = "[cashier]_get_member_number_";
    String placePre = "[place]_get_member_number_";

    String placeMemberCardIdPre = "[place]_get_member_number_byCardId_";

    String placeLogLoginPre = "[place]_get_log_login_number_";

    String placeLogLoginDetailsPre = "[place]_get_log_login_details_number_";

    String cashierLogLoginPre = "[cashier]_get_log_login_number_";
    String placeChainMemberPre = "[place]_chain_member_number_";



//    @Scheduled(cron = "0 52 10 * * ?") //每天晚上19.52
    @Scheduled(cron = "0 0 0 * * ?") //每天晚上0点
    public void cleanMemberNumber() {
        log.info("中心清除会员请求限制开始, 获取锁:::" + cleanMemberNumberLockKey);
        boolean locked = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(cleanMemberNumberLockKey, "locked"));
        if (locked) {
            log.info("清除会员锁获取成功，设置5分钟TTL");
            stringRedisTemplate.expire(cleanMemberNumberLockKey, 5, TimeUnit.MINUTES);
        } else {
            log.info("清除会员锁获取失败，查询锁状态");
            long expired = stringRedisTemplate.getExpire(cleanMemberNumberLockKey, TimeUnit.SECONDS);
            log.info("没有获得锁, 停止中心清除会员请求数查询未下机任务, TTL剩余:::" + expired);
            if (expired == -1L) {
                log.info("上次TTL设置失败，重新设置5分钟TTL");
                stringRedisTemplate.expire(cleanMemberNumberLockKey, 5, TimeUnit.MINUTES);
            }
            log.info("中心清除会员请求数任务结束！！！");
            return;
        }
        // 1、先查询表里的placeId
        List<SecurityRequestConfig> securityRequestConfigList = securityRequestConfigService.findAll();
        log.info("中心开始清除收银台、网吧门店会员请求限制:::");
        for (SecurityRequestConfig securityRequestConfig : securityRequestConfigList) {
            String cashierRequestKey = cashierPre + securityRequestConfig.getPlaceId();
            String placeRequestKey = placePre + securityRequestConfig.getPlaceId();
            String placeMemberCardIdRequestKey = placeMemberCardIdPre + securityRequestConfig.getPlaceId();
            String placeLogLoginRequestKey = placeLogLoginPre + securityRequestConfig.getPlaceId();
            String placeLogLoginDetailsRequestKey = placeLogLoginDetailsPre + securityRequestConfig.getPlaceId();
            String cashierLogLoginRequestKey = cashierLogLoginPre + securityRequestConfig.getPlaceId();

            if (stringRedisTemplate.hasKey(cashierRequestKey)) {
                stringRedisTemplate.opsForValue().set(cashierRequestKey, "0");// 收银台
            }
            if (stringRedisTemplate.hasKey(placeRequestKey)) {
                stringRedisTemplate.opsForValue().set(placeRequestKey, "0");// 网吧后台
            }
            if (stringRedisTemplate.hasKey(placeMemberCardIdRequestKey)) {
                stringRedisTemplate.opsForValue().set(placeMemberCardIdRequestKey, "0");// 网吧后台会员详情
            }

            if (stringRedisTemplate.hasKey(placeLogLoginRequestKey)) {
                stringRedisTemplate.opsForValue().set(placeLogLoginRequestKey, "0");// 网吧后台上机记录
            }

            if (stringRedisTemplate.hasKey(placeLogLoginDetailsRequestKey)) {
                stringRedisTemplate.opsForValue().set(placeLogLoginDetailsRequestKey, "0");// 网吧后台上机记录详情
            }

            if (stringRedisTemplate.hasKey(cashierLogLoginRequestKey)) {
                stringRedisTemplate.opsForValue().set(cashierLogLoginRequestKey, "0");// 收银台上机记录
            }
        }

        // 定时清除连锁账号的已请求会员数量
        GenericResponse<ListDTO<PlaceChainBO>> placeChainBOSResponse = placeChainApi.findAllPlaceChains();
        log.info("中心开始清除连锁账号会员请求限制:::");
        if (placeChainBOSResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            if (placeChainBOSResponse.isResult()) {
                List<PlaceChainBO> placeChainBOS = placeChainBOSResponse.getData().getList();
                for (PlaceChainBO placeChainBO : placeChainBOS) {
                    String placeChainRequestKey = placeChainMemberPre + placeChainBO.getChainId();
                    if (stringRedisTemplate.hasKey(placeChainRequestKey)) {
                        stringRedisTemplate.opsForValue().set(placeChainRequestKey, "0");// 连锁账号后台会员数据
                    }
                }
            }
        }


        log.info("中心清除会员数量请求限制结束！！！释放锁:::" + cleanMemberNumberLockKey);
        stringRedisTemplate.delete(cleanMemberNumberLockKey);
    }
}
