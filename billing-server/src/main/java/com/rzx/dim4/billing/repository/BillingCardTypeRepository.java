package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.BillingCardType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * 
 * <AUTHOR>
 * @date Jun 19, 2020 11:37:05 AM
 */
public interface BillingCardTypeRepository extends JpaRepository<BillingCardType, Long> {

	@Modifying
	@Transactional
	@Query(value = "update billing_card_type set deleted = 1, updated = now() where place_id = :placeId and card_type_id != '1000' and card_type_id != '1002'", nativeQuery = true)
	int updateCardTypesWhenChain(String placeId);

	Optional<BillingCardType> findById(Long id);

	Optional<BillingCardType> findTop1ByPlaceIdOrderByIdDesc(String placeId);

	Optional<BillingCardType> findTop1ByPlaceIdOrderByCardTypeIdDesc(String placeId);

	Optional<BillingCardType> findByPlaceIdAndTypeNameAndDeleted(String placeId, String typeName, int deleted);

	Optional<BillingCardType> findByPlaceIdAndCardTypeIdAndDeleted(String placeId, String cardTypeId, int deleted);

	Optional<BillingCardType> findByPlaceIdAndChainCardTypeIdAndDeleted(String placeId,String chainCardTypeId, int deleted);

	Optional<BillingCardType> findByPlaceIdAndChainCardTypeIdAndTypeNameAndDeleted(String placeId, String chainCardTypeId, String typeName, int deleted);

	List<BillingCardType> findByPlaceId(String placeId);

	List<BillingCardType> findByPlaceIdAndDeleted(String placeId, int deleted);

	List<BillingCardType> findByPlaceIdInAndDeletedAndChainCardTypeId(List<String> placeIds, int deleted,String chainCardTypeId);

    List<BillingCardType> findByPlaceIdInAndChainCardTypeIdAndDeleted(List<String> placeId, String chainCardTypeId, int deleted);

	List<BillingCardType> findByChainCardTypeIdAndDeletedAndPlaceIdIn(String chainCardTypeId, int deleted, List<String> placeIds);

	/**
	 * 通过 场所id + 卡类型id列表 查询 计费卡类型列表
	 *
	 * @param placeId 		场所id
	 * @param cardTypeIds	卡类型id列表
	 * @return 计费卡类型列表
	 */
	List<BillingCardType> findByPlaceIdAndDeletedAndCardTypeIdIn(String placeId, int deleted, List<String> cardTypeIds);

	List<BillingCardType> findByPlaceIdInAndCardTypeIdIn(List<String> placeId, List<String> cardTypeIds);

	List<BillingCardType> findByPlaceIdInAndChainCardTypeIdInAndDeleted(List<String> placeIds,List<String> chainCardTypeIds, int deleted);

	List<BillingCardType> findByPlaceIdInAndDeleted(List<String> placeIds, int deleted);


	/**
	 * 根据场所Id和最小积分要求查询出一个对应的卡类型（去除临时卡和工作卡,避免自动降级的时候会员卡降成临时卡），如果积分相同按照升序找最小id的记录
	 * */
	Optional<BillingCardType> findTop1ByPlaceIdAndMinPointsRequirementLessThanEqualAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementDescId(String placeId,int minPointsRequirement, int deleted,List<String> cardTypeIds);

	// 查询当前场所下非临时卡和工作卡外的最小积分要求的卡类型
	Optional<BillingCardType> findTop1ByPlaceIdAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementAscId(String placeId,int deleted,List<String> cardTypeIds);

}
