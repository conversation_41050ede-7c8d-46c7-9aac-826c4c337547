package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.ClientUpgrade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface ClientUpgradeRepository extends JpaRepository<ClientUpgrade, Long>, JpaSpecificationExecutor<ClientUpgrade> {

    Optional<ClientUpgrade> findByPlaceIdAndClientTypeAndDeleted(String placeId, int clientType, int deleted);

    Page<ClientUpgrade> findByClientTypeAndDeletedOrderByIdDesc(int clientType, int deleted, Pageable pageable);

    Page<ClientUpgrade> findByClientTypeAndVersionIdAndDeletedOrderByIdDesc(int clientType, String versionId, int deleted, Pageable pageable);

    Page<ClientUpgrade> findByClientTypeAndPlaceIdAndDeletedOrderByIdDesc(int clientType, String placeId, int deleted, Pageable pageable);

    Page<ClientUpgrade> findByClientTypeAndPlaceIdAndVersionIdAndDeletedOrderByIdDesc(int clientType, String placeId,String versionId, int deleted, Pageable pageable);

    List<ClientUpgrade> findByClientTypeOrderById(int clientType);

    List<ClientUpgrade> findByClientTypeAndVersionIdOrderById(int clientType,String versionId);

    List<ClientUpgrade> findByClientTypeAndDeletedAndPlaceIdIn(int clientType, int deleted, List<String> placeIds);
}
