package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.utils.IdNumberValidator;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.PlaceBizConfig;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 收银台激活或者开卡
 */
@Slf4j
@Service
public class CashierActiveOrCreateBillingCardServiceImpl implements CoreService {

    @Autowired
    private IBillingCardService iBillingCardService;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private CashierActivationServiceImpl cashierActivationService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogOperationService logOperationService;


    public GenericResponse<?> doService(List<String> params) {


        String placeId = params.get(0); // 场所ID
        String shiftId = params.get(1); // 班次ID
        String cardTypeId = params.get(2); // 卡类型ID //本接口前端不会传该字段
        String idNumber = params.get(3); // 身份证号码
        String name = params.get(4); // 姓名
        String address = params.get(5); // 地址
        String issuingAuthority = params.get(6); // 发证机关
        String nation = params.get(7); // 民族
        String validPeriod = params.get(8); // 有效期为-1是表示计费开卡
        String activeType =  params.get(9); // 激活方式,传value值
        String identification = params.get(10); // 附加费标识
        String phoneNumber = params.get(11); // 手机号，场所开启注册卡后，能获取到手机号
        String  remark = params.get(12); // 备注
        if (!StringUtils.isEmpty(remark) && remark.length() > 100) {
            log.warn("remark length is too long, remark:{}", remark);
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }
        if(StringUtils.isEmpty(placeId) || StringUtils.isEmpty(idNumber)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        if (idNumber.length() != 18) { // 非大陆居民身份证，
            String regex = "^[a-zA-Z0-9]+$"; // 只能是数字和字母
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(idNumber);
            if (!matcher.matches()) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
            PlaceBizConfig config = placeBizConfigService.findByPlaceId(placeId); // 查询网吧的非身份证配置
            if (config.getNonIdNumber() > 0) { // 大于0，允许非身份证号注册，需要判断今天已经激活的非身份证数量
                int activated = logOperationService.countTodayActivatedNonIdNumberByPlaceId(placeId);
                if (activated >= config.getNonIdNumber()) { // 激活数量已经达到上限
                    return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
                }
            } else if (config.getNonIdNumber() == 0) { // 等于0， 不允许非身份证号注册，不允许注册非身份证号码
                return new GenericResponse<>(ServiceCodes.BILLING_CARD_NON_ID_NUMBER_LIMIT);
            }
        } else { // 大陆居民身份证
            // 验证身份证合法性，姓名不能为空
            idNumber = idNumber.toUpperCase();
            boolean flag = IdNumberValidator.verificate(idNumber);
            if (!flag) {
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
        }

        Optional<BillingCard> byPlaceIdAndIdNumberAndNotDeleted = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber);
        if(byPlaceIdAndIdNumberAndNotDeleted.isPresent()){
            //激活
            if(StringUtils.isEmpty(activeType)){
                return new GenericResponse<>(ServiceCodes.BAD_PARAM);
            }
            List<String> paramList = new ArrayList<>();
            paramList.add(placeId);
            paramList.add(shiftId);
            paramList.add(idNumber);
            paramList.add(activeType);
            paramList.add(identification);
            paramList.add(name);
            return cashierActivationService.doService(paramList);
        }
        //开卡
        if(StringUtils.isEmpty(name)){
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        PlaceBizConfig byPlaceId = placeBizConfigService.findByPlaceId(placeId);

        cardTypeId = StringUtils.isEmpty(byPlaceId.getDefCardType()) ? "1000" : byPlaceId.getDefCardType();

        BillingCardBO billingCardBO = iBillingCardService.cashierCreateCard(placeId,
                shiftId,
                cardTypeId,
                idNumber,
                name,
                phoneNumber,
                address,
                issuingAuthority,
                nation,
                validPeriod,
                activeType,
                identification,
                remark,
                0,
                0,
                false
                ,0);

        return new GenericResponse<>(new ObjDTO<>(billingCardBO));
    }
}
