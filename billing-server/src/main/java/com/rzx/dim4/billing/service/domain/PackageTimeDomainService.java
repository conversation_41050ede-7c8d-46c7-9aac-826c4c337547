package com.rzx.dim4.billing.service.domain;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/11/29
 **/
public interface PackageTimeDomainService {

    /**
     * 收银台创建临时卡包时
     *
     * @param billingCardBO 会员卡信息
     * @param shiftId 班次ID
     * @param packageRuleId 包时套餐规则ID
     * @param packagePayType 包时套餐支付类型，1:余额包时 2:现金包时，当前临时卡固定为余额包时
     */
    void cashierCreate(BillingCardBO billingCardBO, String shiftId, String packageRuleId, int packagePayType);

    /**
     * 收银台包时，立即生效
     *
     * @param placeId         场所Id
     * @param shiftId         班次id
     * @param cardId          计费卡id
     * @param packageRuleId   包时规则id
     * @param packagePayType  包时支付方式 1:余额包时 2:现金包时
     */
    void packageImmediately(String placeId, String shiftId, String cardId, String packageRuleId, int packagePayType, SourceType sourceType);

    /**
     * 收银台预包时
     *
     * @param placeId         场所Id
     * @param shiftId         班次Id
     * @param cardId          计费卡Id
     * @param packageRuleId   包时套餐规则Id
     * @param packagePayType  包时套餐支付类型 1:余额包时 2:现金包时
     */
    void packagePre(String placeId, String shiftId, String cardId, String packageRuleId, int packagePayType, int limitedLogin);

    /**
     * 收银台续包时
     * @param placeId
     * @param ruleId
     * @param billingKey
     * @param billingOnline
     * @param billingCard
     * @param packageType
     * @param logLogin
     * @param logShift
     * @param oldBillingRulePackageTime
     */
    void continuePackageTime (String placeId, String ruleId, String billingKey, BillingOnline billingOnline, BillingCard billingCard, String packageType, LogLogin logLogin, LogShift logShift, BillingRulePackageTime oldBillingRulePackageTime);

}
