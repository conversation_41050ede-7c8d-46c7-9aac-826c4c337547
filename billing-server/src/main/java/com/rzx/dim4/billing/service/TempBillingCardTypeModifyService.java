package com.rzx.dim4.billing.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BaseEntity;
import com.rzx.dim4.billing.entity.BillingCard;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.TempBillingCardTypeModify;
import com.rzx.dim4.billing.repository.TempBillingCardTypeModifyRepository;
import org.springframework.util.CollectionUtils;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月12日 下午2:16:42
 */
@Service
public class TempBillingCardTypeModifyService {

	@Autowired
	TempBillingCardTypeModifyRepository tempBillingCardTypeModifyRepository;

	public Optional<TempBillingCardTypeModify> findByPlaceIdAndCardId(String placeId, String cardId) {
		return tempBillingCardTypeModifyRepository.findByPlaceIdAndCardIdAndDeleted(placeId, cardId, 0);
	}

	public TempBillingCardTypeModify save(TempBillingCardTypeModify temp) {
		return tempBillingCardTypeModifyRepository.save(temp);
	}

}
