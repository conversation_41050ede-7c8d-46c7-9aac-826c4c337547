package com.rzx.dim4.billing.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.rzx.dim4.billing.entity.ClientWallpaperDeliver;
import com.rzx.dim4.billing.repository.ClientWallpaperDeliverRepository;

@Service
public class ClientWallpaperDeliverService {

	@Autowired
	ClientWallpaperDeliverRepository placeWallpaperDeliverRepository;

	public Page<ClientWallpaperDeliver> findAll(String search, Pageable pageable) {
		if (StringUtils.isEmpty(search)) {
			return placeWallpaperDeliverRepository.findAll(pageable);
		}
		Page<ClientWallpaperDeliver> page = placeWallpaperDeliverRepository
				.findAll(new Specification<ClientWallpaperDeliver>() {
					private static final long serialVersionUID = 1L;

					@Override
					public Predicate toPredicate(Root<ClientWallpaperDeliver> root, CriteriaQuery<?> criteriaQuery,
							CriteriaBuilder builder) {
						LocalDateTime now = LocalDateTime.now();
						List<Predicate> predicateList = new ArrayList<>();
						predicateList
								.add(builder.greaterThanOrEqualTo(root.get("endTime").as(LocalDateTime.class), now));
						predicateList
								.add(builder.lessThanOrEqualTo(root.get("startTime").as(LocalDateTime.class), now));
						predicateList.add(builder.like(root.get("placeIds"), "%" + search + "%"));
						Predicate[] predicateArr = new Predicate[predicateList.size()];
						return builder.and(predicateList.toArray(predicateArr));
					}
				}, pageable);
		return page;
	}

	public Optional<ClientWallpaperDeliver> findDefaultPlaceWallpaperDeliver() {
		Optional<ClientWallpaperDeliver> optDefault = placeWallpaperDeliverRepository.findTop1ByIsDefaultOrderById(1);
		return optDefault;
	}

	/**
	 * 根据网吧ID查询锁屏图片 <br/>
	 * 1. 先查询有没有当前发布的图片 <br/>
	 * 2. 如果没有正在发布的图片，就查询默认的图片 <br/>
	 * 
	 * @param placeId
	 * @return
	 */
	public Optional<ClientWallpaperDeliver> findValidPlaceWallpaperDeliverByPlaceId(String placeId,int type) {
		LocalDateTime nowDateTime = LocalDateTime.now(); // 获取当前时间
		placeId = "%" + placeId + "%";

		// 查询正在发布的锁屏照片
		Optional<ClientWallpaperDeliver> optRelease = placeWallpaperDeliverRepository
				.findTop1ByStartTimeLessThanEqualAndEndTimeGreaterThanEqualAndPlaceIdsLikeAndWallpaperTypeOrderByIdDesc(nowDateTime,
						nowDateTime, placeId,type);
		if (optRelease.isPresent()) {
			return optRelease;
		}

		// 没有找到正在发布的锁屏照片，查询系统默认的照片
		Optional<ClientWallpaperDeliver> optDefault = placeWallpaperDeliverRepository.findTop1ByIsDefaultOrderById(1);
		if (optDefault.isPresent()) {
			return optDefault;
		}

		// 都没找到，返回空
		return Optional.empty();
	}

}
