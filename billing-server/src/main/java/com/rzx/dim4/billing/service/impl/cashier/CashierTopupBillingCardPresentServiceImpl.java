package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.BalanceDetailOperationType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.BillingOnline;
import com.rzx.dim4.billing.entity.LogLogin;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 计费卡卡赠送充值
 * 
 * <AUTHOR>
 * @date 2022年4月6日 上午10:43:15
 */
@Slf4j
@Service
public class CashierTopupBillingCardPresentServiceImpl implements CoreService {

	@Autowired
	LogShiftService logShiftService;

	@Autowired
	LogLoginService logLoginService;

	@Autowired
	BillingCardService billingCardService;

	@Autowired
	BillingOnlineService billingOnlineService;

	@Autowired
	LogOperationService logOperationService;

	@Autowired
	BalanceDetailsAlgorithm balanceDetailsAlgorithm;

	@Override
	public GenericResponse<ObjDTO<BillingCardBO>> doService(List<String> params) {

		// 检查参数
		if (params.size() != 5) {
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		// 获取参数
		String placeId = params.get(0);
		String shiftId = params.get(1); // 班次ID
		String cardId = params.get(2);
		String presentAmountStr = params.get(3); //
		String desc = params.get(4);

		// 处理充金额和赠送金额
		int presentAmount = 0;
		try {
			presentAmount = Integer.parseInt(presentAmountStr);
		} catch (NumberFormatException nfe) {
			return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
		}
		if (presentAmount <= 0 || presentAmount > 1000000) { // 充值范围 0元-1万元
			return new GenericResponse<>(ServiceCodes.BILLING_AMOUNT_ERROR);
		}

		// 查询卡信息
		Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
		if (!optBillingCard.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_CARD_NOT_FOUND);
		}
		BillingCard billingCard = optBillingCard.get();

		// 查询班次
		Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId,shiftId,0);
		if (!optLogShift.isPresent()) {
			return new GenericResponse<>(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
		}
		LogShift logShift = optLogShift.get();

		// 查询在线信息
		Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
				cardId);
		BillingOnline billingOnline = null;
		if (optBillingOnline.isPresent()) {
			billingOnline = optBillingOnline.get();
		}

		LogLogin logLogin = null;
		if (billingOnline != null) {
			Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, cardId, billingOnline.getBillingTime());
			if (optLogLogin.isPresent()) {
				logLogin = optLogLogin.get();
			}
		}

		billingCard = billingCardService.billingCardTopup(0, presentAmount, placeId, cardId, billingCard.getCardTypeId(), logShift,0, SourceType.CASHIER);

		logOperationService.addTopupPresentLogOperation(presentAmount, billingCard, billingOnline, logShift, logLogin,
				desc, SourceType.CASHIER);

		try {
			balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(), billingCard, null, BalanceDetailOperationType.CASHIER_ADD_AMOUNT.getCode(), 1, 0, presentAmount, "收银台加钱-" + desc, 0, 0, null);
		}catch (Exception e){
			log.info("卡号:::{} 网费明细记录收银台加钱异常",billingCard.getCardId());
			e.printStackTrace();
		}

		return new GenericResponse<>(new ObjDTO<>(billingCard.toBO()));
	}

}
