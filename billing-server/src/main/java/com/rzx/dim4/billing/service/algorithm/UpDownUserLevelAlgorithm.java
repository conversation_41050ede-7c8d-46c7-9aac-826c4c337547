package com.rzx.dim4.billing.service.algorithm;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ExchangePointsType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.repository.BillingCardRepository;
import com.rzx.dim4.billing.repository.BillingCardTypeRepository;
import com.rzx.dim4.billing.repository.BillingOnlineRepository;
import com.rzx.dim4.billing.repository.PlaceBizConfigRepository;
import com.rzx.dim4.billing.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 根据积分自动升降级用户等级公用方法
 */
@Service
@Slf4j
public class UpDownUserLevelAlgorithm {
    @Autowired
    LogOperationService logOperationService;

    @Autowired
    PlaceBizConfigRepository placeBizConfigRepository;

    @Autowired
    BillingCardTypeRepository billingCardTypeRepository;

    @Autowired
    private BillingCardRepository billingCardRepository;

    @Autowired
    BillingOnlineRepository onlineRepository;

    @Autowired
    PackageTimeReserveService packageTimeReserveService;
    @Autowired
    PlaceServerService placeServerService;
    @Autowired
    BillingOnlineService billingOnlineService;
    @Autowired
    RewardPointsRuleService rewardPointsRuleService;
    @Autowired
    PlaceChainStoresService placeChainStoresService;
    @Autowired
    BillingCardService billingCardService;

    /**
     * 根据积分自动升降用户等级公用方法:调用该方法前提是没有未使用的包时。
     * 逻辑步骤:
     * 0、先判断当前会员卡是否有未使用的包时，没有才继续往下走
     * 1、先查询场所业务配置信息,是否开启自动升降级开关
     * 2、去查询用户是否正在上机，如果用户未上机，则立马进行升降级变更卡类型；如果用户在上机，则不操作 ，等结账的时候调用该方法去变更卡类型
     * 3、根据场所Id查询场所的卡类型列表，用于当前用户根据会员卡积分去匹配能够升级的卡类型
     * 4、根据当前卡类型积分和匹配到的会员卡类型积分，比较判断是升级还是降级，如果是降级，会员卡最低降低至普通会员，不允许降低至临时卡
     * 5、写操作记录
     * <p>
     * <p>
     * 如果当前卡类型对应的最小积分要求大于 “增减”积分后的卡类型积分，则是降级
     * 备注:会员卡最低降低至普通会员，不允许降低至临时卡
     *
     * @param billingCard 会员卡信息
     * @param amount      操作的金额，充值、消费。
     * @param points      积分
     * @param amountType  金额类型:0-充值，1-消费(结账消费时的积分赠送)，2-冲正（积分调整），3-积分兑换,4-取消包时
     * @param sourceType  来源，用于写操作记录
     */
    public void autoUpOrDownUserLevel(BillingCard billingCard, int amount, int amountType, int points, SourceType sourceType, LogShift logShift) {
        log.info("autoUpOrDownUserLevel,,billingCard={},amount={},amountType={},points={},sourceType={}",
                billingCard, amount, amountType, points, sourceType);

        String placeId = billingCard.getPlaceId();
        String cardTypeId = billingCard.getCardTypeId();
        //临时卡直接结束
        if ("1002".equals(cardTypeId)) {
            return;
        }
        int existedPoints = billingCard.getPoints();
        String cardId = billingCard.getCardId();
        String chainId = null;

        //判断是不是连锁门店，是连锁的话-》直接执行连锁的积分升级相关方法
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);
        if(optPlaceChainStores.isPresent()){
            if("1000".equals(billingCard.getCardTypeId())){//连锁临时卡直接跳过积分升级逻辑
                return;
            }
            Optional<BillingCard> byPlaceIdAndIdNumberAndNotDeleted = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
            if(byPlaceIdAndIdNumberAndNotDeleted.isPresent()){
                BillingCard chainCard = byPlaceIdAndIdNumberAndNotDeleted.get();
                existedPoints = chainCard.getPoints();        //连锁总积分
                chainId = optPlaceChainStores.get().getChainId();
            }
        }

        int upOrDown = 0;// 0代表升级，1代表降级，2代表积分相同，不做任何操作
        // 赠送完积分后，判断场所业务配置是否开启了“根据积分自动升降级用户等级”；临时卡还要判断是否开启了“临时卡参与自动升降级开关”
        // 0、有预包时的话，不进行积分升降级处理
        Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService.findUnusedByPlaceIdAndCardId(placeId, cardId);
        if (!packageTimeReserveOpt.isPresent() || amountType == 1) {
            // 1、先查询场所业务配置信息,只有开启了升级或者降级任何一个按钮才往下走
            Optional<PlaceBizConfig> optPlaceBizConfig = placeBizConfigRepository.findByPlaceId(placeId);
            if (!optPlaceBizConfig.isPresent()) {
                log.error("placeBizConfig not found,placeId={}", placeId);
                throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
            }
            PlaceBizConfig placeBizConfig = optPlaceBizConfig.get();
            if (placeBizConfig.getUpgradeUserLevelFlag() == 1 || placeBizConfig.getDowngradeUserLevelFlag() == 1) {
                // 2、查询用户是否正在上机,如果不在上机，直接进行升降级
                List<String> cardIds = new ArrayList<>(Arrays.asList(cardId));
                Optional<BillingOnline> billingOnline = onlineRepository.findByPlaceIdAndCardIdAndFinished(placeId, cardId, 0);
                // 如果不在上机，则进行自动升降级操作
                if (!billingOnline.isPresent() || amountType == 1) {//会员卡不在线或者点击结账时允许更新会员卡等级
                    // 3、根据场所Id查询场所的卡类型列表，用于当前用户根据会员卡积分去匹配能够升级的卡类型
                    Optional<BillingCardType> currentBillingCardType = billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
                    if(!currentBillingCardType.isPresent()){
                        log.error("{}用户{}会员卡类型{}未查询到!",placeId,billingCard.getIdNumber(),cardTypeId);
                        return;
                    }
                    // 根据当前用户充值/兑换后的积分去会员卡类型表中匹配该升级（降级）的卡类型并返回。
                    List<String> cardTypeIds = Arrays.asList("1000", "1002");//过滤临时卡和工作卡
                    Optional<BillingCardType> optBillingCardType = billingCardTypeRepository.findTop1ByPlaceIdAndMinPointsRequirementLessThanEqualAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementDescId(placeId, existedPoints, 0, cardTypeIds);
                    String currentCardTypeName = currentBillingCardType.get().getTypeName();
                    String matchCardTypeName = "";
                    BillingCardType billingCardType = null; //
                    /** 判断根据积分匹配到的卡类型是否为空,如果为空需要返回当前场所最小积分的卡类型*/
                    if (optBillingCardType.isPresent()) {
                        matchCardTypeName = optBillingCardType.get().getTypeName();
                        billingCardType = optBillingCardType.get();
                    } else {
                        // 非临时卡时查找一个积分要求最小的会员卡类型，因为临时卡时查找最小积分要求时会查到普通会员，然后就会进行升级动作
                        if(!"1000".equals(cardTypeId)){
                            Optional<BillingCardType> optMinBillingCardType = billingCardTypeRepository.findTop1ByPlaceIdAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementAscId(placeId, 0, cardTypeIds);
                            if (optMinBillingCardType.isPresent()) {
                                matchCardTypeName = optMinBillingCardType.get().getTypeName();
                                billingCardType = optMinBillingCardType.get();
                            }
                        }

                    }
                    // 判断是否降级：当前卡类型对应的最小积分和当前用户会员卡的积分去匹配卡类型表中对应的卡类型最小积分要求
                    if(null == billingCardType){
                        upOrDown = 2;
                    } else if (currentBillingCardType.get().getMinPointsRequirement() > billingCardType.getMinPointsRequirement()) {
                        upOrDown = 1;// 大于说明是降级
                    }else if (currentBillingCardType.get().getMinPointsRequirement() < billingCardType.getMinPointsRequirement()) {
                        upOrDown = 0;//
                    } else if (currentBillingCardType.get().getMinPointsRequirement() == billingCardType.getMinPointsRequirement()) {
                        upOrDown = 2;
                    } else if (existedPoints < billingCardType.getMinPointsRequirement()) {
                        upOrDown = 2;
                    }

                    // 只有出现卡对应的积分发生差异的时候，才做升降级处理
                    if (!"1000".equals(cardTypeId) && upOrDown != 2) {
                        // 如果当前积分情况是升级并且场所又配置了可根据积分自动升级用户等级
                        try {
                            if(1 == upOrDown && placeBizConfig.getDowngradeUserLevelFlag() == 1){
                                // 用户等级降级
                                upOrDownUserLevel(placeId, billingCard, amount, amountType, points, sourceType, upOrDown, cardIds,
                                        billingCardType, currentCardTypeName, matchCardTypeName, logShift,chainId);
                            }
                            if(0 == upOrDown && placeBizConfig.getUpgradeUserLevelFlag() == 1){
                                // 用户等级升级
                                upOrDownUserLevel(placeId, billingCard, amount, amountType, points, sourceType, upOrDown, cardIds,
                                        billingCardType, currentCardTypeName, matchCardTypeName, logShift,chainId);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            String upOrDownStr = "升级";
                            if (upOrDown == 1) {
                                upOrDownStr = "降级";
                            }
                            log.error("根据积分自动" + upOrDownStr + "用户等级失败:::" + e.getMessage());
                        }
                    } else {
                        // 如果是临时卡,这只考虑升级场景
                        try {
                            if (upOrDown == 0 && placeBizConfig.getTempCardPointsUpgrade() == 1 && placeBizConfig.getUpgradeUserLevelFlag() == 1) {
                                // 临时卡用户等级升级
                                upOrDownUserLevel(placeId, billingCard, amount, amountType, points, sourceType, upOrDown, cardIds,
                                        billingCardType, currentCardTypeName, matchCardTypeName, logShift,chainId);
                            }
                        } catch (Exception e) {
                            log.error("临时卡根据积分自动升级用户等级失败:::" + e.getMessage());
                        }
                    }
                }
            }
        }
    }

    /**
     * 修改用户卡类型，写根据积分自动升降用户等级操作记录
     */
    private void upOrDownUserLevel(String placeId, BillingCard billingCard, int amount, int amountType, int points, SourceType sourceType,
                                   int upOrDown, List<String> cardIds, BillingCardType billingCardType, String currentCardTypeName,
                                   String matchCardTypeName, LogShift logShift,String chainId) {
        if(StringUtils.isEmpty(chainId)){
            billingCardRepository.updateCardTypeByCardId(billingCardType.getChainCardTypeId(), billingCardType.getCardTypeId(), billingCardType.getTypeName(), placeId, cardIds,billingCard.getCashAccount()+billingCard.getTemporaryOnlineAccount(), billingCard.getChainCard());
        }else{
            //连锁，需要查询出当前连锁下所有门店，然后修改该用户的会员卡类型（不能修改金额）
            List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(chainId);
            // 获取连锁场所ID列表
            List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
            //根据场所列表查询出改会员卡类型每个场所下的id
            List<BillingCardType> billingCardTypes = billingCardTypeRepository.findByPlaceIdInAndDeletedAndChainCardTypeId(placeIds,0,billingCardType.getChainCardTypeId());
            if(billingCardTypes.isEmpty()){
                log.info("连锁{}下没有会员卡类型",chainId);
                return;
            }
            //查询出连锁下的所有该类型会员卡
            List<BillingCard> billingCards = billingCardService.findByChainIdAndIdNumberByMember(chainId, billingCard.getIdNumber());
          //  billingCards = billingCards.stream().filter(it-> !it.getCardTypeId().equals("1000") && !it.getCardTypeId().equals("1002")).collect(Collectors.toList());
            //将所有会员卡一张张改变为对应的卡类型id
            for (BillingCard card : billingCards) {
                List<BillingCardType> collect = billingCardTypes.stream().filter(it -> it.getPlaceId().equals(card.getPlaceId()) && it.getChainCardTypeId().equals(billingCardType.getChainCardTypeId())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(collect)){
                    continue;
                }
                BillingCardType cardTypeByChain = collect.get(0);
                billingCardRepository.updateChainCardTypeByCardId(cardTypeByChain.getChainCardTypeId(), cardTypeByChain.getCardTypeId(), cardTypeByChain.getTypeName(), card.getPlaceId(), card.getCardId());
            }

        }
        logOperationService.addGradeUserLevelLogOperation(sourceType, amount, billingCard, amountType, points, upOrDown, currentCardTypeName, matchCardTypeName, logShift);
    }

    /**
     * 计费卡是否需要升降机卡类型
     *
     * 注意：只能用来判断是否会卡类型变化，不能用来抽共用方法，此时amountType写死为0！！！
     * @param billingCard
     * @return 0代表升级，1代表降级，2代表积分相同，不做任何操作
     */
    public int getUpOrDown(BillingCardBO billingCard) {
        String placeId = billingCard.getPlaceId();
        String cardTypeId = billingCard.getCardTypeId();
        int points = billingCard.getPoints();

        if ("1002".equals(cardTypeId)) {
            log.info("1002 不参与积分导致卡类型变化");
            return -1;
        }

        int upOrDown = 2;// 0代表升级，1代表降级，2代表积分相同，不做任何操作
        // 赠送完积分后，判断场所业务配置是否开启了“根据积分自动升降级用户等级”；临时卡还要判断是否开启了“临时卡参与自动升降级开关”
        // 1、先查询场所业务配置信息,只有开启了升级或者降级任何一个按钮才往下走
        Optional<PlaceBizConfig> optPlaceBizConfig = placeBizConfigRepository.findByPlaceId(placeId);
        if (!optPlaceBizConfig.isPresent()) {
            log.error("placeBizConfig not found,placeId={}", placeId);
            throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
        }
        PlaceBizConfig placeBizConfig = optPlaceBizConfig.get();
        boolean flag = true;
        if("1000".equals(billingCard.getCardTypeId())){
            flag = placeBizConfig.getTempCardPointsUpgrade() == 1;
        }

        if (flag && (placeBizConfig.getUpgradeUserLevelFlag() == 1 || placeBizConfig.getDowngradeUserLevelFlag() == 1)) {
            //---（1 判断是否在上机，如果在上机需要计算本次上机消耗了多少钱，并将其转换为积分（需要先校验后台配置了消费金额转换为积分）
            GenericResponse<ObjDTO<PlaceConfigBO>> placeConfigResponse = placeServerService.findPlaceConfigByPlaceId(billingCard.getPlaceId());
            if (placeConfigResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
                PlaceConfigBO placeConfigBO = placeConfigResponse.getData().getObj();
                // 开启积分赠送
                if(placeConfigBO.getRewardPoints() == 1){
                    //判断是否在线
                    Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId, billingCard.getCardId());
                    if(optBillingOnline.isPresent()){
                        BillingOnline billingOnline = optBillingOnline.get();
                        points += rewardPointsRuleService.getRewardPointsNum(billingCard.getPlaceId(), billingOnline.getDeduction(), ExchangePointsType.LOGOUT);
                    }
                }
            }
            // 3、根据场所Id查询场所的卡类型列表，用于当前用户根据会员卡积分去匹配能够升级的卡类型
            Optional<BillingCardType> currentBillingCardType = billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
            // 根据当前用户充值/兑换后的积分去会员卡类型表中匹配该升级（降级）的卡类型并返回。
            List<String> cardTypeIds = Arrays.asList("1000", "1002");//过滤临时卡和工作卡
            Optional<BillingCardType> optBillingCardType =
                    billingCardTypeRepository.findTop1ByPlaceIdAndMinPointsRequirementLessThanEqualAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementDescId(placeId, points, 0, cardTypeIds);
            BillingCardType billingCardType = null; //
            /** 判断根据积分匹配到的卡类型是否为空,如果为空需要返回当前场所最小积分的卡类型*/
            if (optBillingCardType.isPresent()) {
                billingCardType = optBillingCardType.get();
            } else {
//                 查找一个积分要求最小的会员卡类型
                if(!"1000".equals(cardTypeId)){
                    Optional<BillingCardType> optMinBillingCardType = billingCardTypeRepository.findTop1ByPlaceIdAndDeletedAndCardTypeIdNotInOrderByMinPointsRequirementAscId(placeId, 0, cardTypeIds);
                    if (optMinBillingCardType.isPresent()) {
                        billingCardType = optMinBillingCardType.get();
                    }
                }
            }
            if(null == billingCardType){
                return 2;
            }

            log.info("billingCardType:{}", new Gson().toJson(billingCardType));
            // 判断是否降级：当前卡类型对应的最小积分和当前用户会员卡的积分去匹配卡类型表中对应的卡类型最小积分要求
            if(null == billingCardType || !currentBillingCardType.isPresent()){
                return 2;
            }else if (currentBillingCardType.get().getMinPointsRequirement() > billingCardType.getMinPointsRequirement()) {
//                log.info("降级");
                upOrDown = 1;// 大于说明是降级
            }else if (currentBillingCardType.get().getMinPointsRequirement() < billingCardType.getMinPointsRequirement()) {
//                log.info("升级");
                upOrDown = 1;
            } else if (currentBillingCardType.get().getMinPointsRequirement() == billingCardType.getMinPointsRequirement()) {
//                log.info("积分相同，不做操作1");
                upOrDown = 2;
            } else if (points < billingCardType.getMinPointsRequirement()) {
//                log.info("积分相同，不做操作2");
                upOrDown = 2;
            }
        } else {
//            log.info("配置没有打开");
            upOrDown = 2;
        }

        log.info("placeId={},cardTypeId={},points={},upOrDown={}", placeId, cardTypeId, points, upOrDown);
        return upOrDown;
    }
}
