package com.rzx.dim4.billing.service.impl.cashier;

import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.service.CoreService;
import com.rzx.dim4.billing.service.impl.client.ClientQueryBillingOnlineServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/** 收银台查询计费卡本次消费详情
 * <AUTHOR> hwx
 * @since 2025/2/13 10:18
 */
@Slf4j
@Service
public class CashierQueryBillingOnlineByClientServiceImpl implements CoreService {
    @Autowired
    protected ClientQueryBillingOnlineServiceImpl clientQueryBillingOnlineService;

    @Override
    public GenericResponse<?> doService(List<String> params) {
        log.info("CashierQueryBillingOnlineByClientServiceImpl,param:{}",params);
        return clientQueryBillingOnlineService.doService(params);
    }
}
