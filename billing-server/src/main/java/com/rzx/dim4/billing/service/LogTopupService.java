package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.LogShift;
import com.rzx.dim4.billing.entity.LogTopup;
import com.rzx.dim4.billing.repository.LogTopupRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LogTopupService {

    @Autowired
    LogTopupRepository logTopupRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    public LogTopup save(LogTopup logTopup) {
        if (StringUtils.isEmpty(logTopup.getId())) {
            try {
                logTopup = logTopupRepository.save(logTopup);
            } catch (Exception e) {
                log.info("保存logTopup失败::::::" + e.getMessage());
                try {
                    logTopup = logTopupRepository.save(logTopup);
                } catch (Exception ex) {
                    log.info("保存logTopup第二次失败::::::" + e.getMessage());
                    logTopup = logTopupRepository.save(logTopup);
                }
            }
            return logTopup;
            // return logLoginRepository.save(logLogin);
        }

        LogTopup logTopupCopy = new LogTopup();
        BeanUtils.copyProperties(logTopup,logTopupCopy);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id",logTopupCopy.getId());
        paramMap.put("startTime",logTopupCopy.getCreated());
        paramMap.put("endTime",DateTimeUtils.monthEndTime(logTopupCopy.getCreated()));

        // 续更新的字段,nextTime和deduction不在这做更新
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("updated",LocalDateTime.now());
        updateMap.put("clientId",logTopupCopy.getClientId());
        updateMap.put("topupRuleId",logTopupCopy.getTopupRuleId());
        updateMap.put("cardId",logTopupCopy.getCardId());
        updateMap.put("cardTypeId",logTopupCopy.getCardTypeId());
        updateMap.put("cardTypeName",logTopupCopy.getCardTypeName());
        updateMap.put("idNumber",logTopupCopy.getIdNumber());
        updateMap.put("idName",logTopupCopy.getIdName());
        updateMap.put("cashBalance",logTopupCopy.getCashBalance());
        updateMap.put("presentBalance",logTopupCopy.getPresentBalance());
        updateMap.put("temporaryOnlineBalance",logTopupCopy.getTemporaryOnlineBalance());
        updateMap.put("cashAmount",logTopupCopy.getCashAmount());
        updateMap.put("presentAmount",logTopupCopy.getPresentAmount());
        updateMap.put("optType",logTopupCopy.getOptType());
        updateMap.put("topupTime",logTopupCopy.getTopupTime());
        updateMap.put("payType",logTopupCopy.getPayType());
        updateMap.put("sourceType",logTopupCopy.getSourceType());
        updateMap.put("refundStatus",logTopupCopy.getRefundStatus());
        updateMap.put("shiftId",logTopupCopy.getShiftId());
        updateMap.put("loginId",logTopupCopy.getLoginId());
        updateMap.put("operator",logTopupCopy.getOperator());
        updateMap.put("operatorName",logTopupCopy.getOperatorName());
        updateMap.put("status",logTopupCopy.getStatus());
        updateMap.put("remark",logTopupCopy.getRemark());
        updateMap.put("ruleId",logTopupCopy.getRuleId());
        updateMap.put("topupSourceType",logTopupCopy.getTopupSourceType());
        update(entityManager, paramMap, updateMap);
        return logTopupCopy;
    }

    public Optional<LogTopup> findByOrderId(String orderId) {
        LocalDateTime now = LocalDateTime.now();
        return logTopupRepository.findByOrderIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(orderId, now.minusDays(7), now);
    }

//	public Optional<LogTopup> findByLdOrderId(String ldOrderId) {
//		return logTopupRepository.findByLdOrderId(ldOrderId);
//	}

    public Optional<LogTopup> findByPlaceIdAndOrderId(String placeId, String orderId) {
        LocalDateTime now = LocalDateTime.now();
        return logTopupRepository.findByPlaceIdAndOrderIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, orderId, now.minusDays(7), now);
    }

    /**
     * 获取最新一条充值订单
     *
     * @param billingCard 会员卡信息
     * @param logtopupIds 不包括这里的充值订单ids
     * @param cashAmount 优先需要精确匹配的金额
     * @return 充值订单
     * @Description 查找下一条最新的可用的用于退款的在线充值订单。
     */
    public Optional<LogTopup> findLatestOneCanRefund(BillingCard billingCard, List<Long> logtopupIds,Integer cashAmount) {
        String placeId = billingCard.getPlaceId();
        String cardId = billingCard.getCardId();
        LocalDateTime now = LocalDateTime.now();
        List<PayType> excludePayTypes = Arrays.asList(PayType.CASH, PayType.BILLING_CARD, PayType.LOSS, PayType.MARKET_PAY, PayType.JWELL_PAY);

        if(null != cashAmount){  //根据金额精准匹配订单
            return logTopupRepository
                    .findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndCashAmountAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(
                            placeId, cardId, excludePayTypes,cashAmount,3, now.minusMonths(1), now);
        }

        if (CollectionUtils.isEmpty(logtopupIds)) {
            return logTopupRepository
                    .findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(
                            placeId, cardId, excludePayTypes,3, now.minusMonths(1), now);
        } else {

            return logTopupRepository
                    .findTop1ByPlaceIdAndCardIdAndPayTypeNotInAndIdNotInAndStatusAndCreatedGreaterThanEqualAndCreatedLessThanEqualOrderByIdDesc(
                            placeId, cardId, excludePayTypes, logtopupIds,3, now.minusMonths(1), now);
        }
    }

    public Page<LogTopup> findAll(Map<String, Object> map, Pageable pageable) {
        if (!StringUtils.isEmpty(map) && (!map.containsKey("startDate") || StringUtils.isEmpty(map.get("startDate")))) {
            map.put("startDate", LocalDateTime.now().minusMonths(1).toString().substring(0, 19).replaceAll("T", " "));
        }
        if (!StringUtils.isEmpty(map) && (!map.containsKey("endDate") || StringUtils.isEmpty(map.get("endDate")))) {
            map.put("endDate", LocalDateTime.now().toString().substring(0, 19).replaceAll("T", " "));
        }
        return logTopupRepository.findAll(new Specification<LogTopup>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<LogTopup> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> andPredicateList = new ArrayList<>();
                List<Predicate> orPredicateList = new ArrayList<>();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 网吧id
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    andPredicateList.add(cb.equal(root.get("placeId").as(String.class), map.get("placeId")));
                }
                if (map.containsKey("queryOnlineOrder")) { // 是否查询在线订单
                    andPredicateList.add(cb.notEqual(root.get("payType").as(PayType.class), PayType.CASH));
                }
                // 用户卡id
                if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {
                    andPredicateList.add(cb.equal(root.get("cardId").as(String.class), map.get("cardId")));
                }
                // 用户卡类型id
                if (map.containsKey("cardTypeId") && !StringUtils.isEmpty(map.get("cardTypeId"))) {
                    andPredicateList.add(cb.equal(root.get("cardTypeId").as(String.class), map.get("cardTypeId")));
                }
                // 用户卡身份证号
                if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {// 身份证号
                    andPredicateList.add(cb.like(root.get("idNumber"), map.get("idNumber") + "%")); // 单边匹配
                }
                // 用户卡身份证号
                if (map.containsKey("idName") && !StringUtils.isEmpty(map.get("idName"))) { // 姓名
                    andPredicateList.add(cb.like(root.get("idName"), map.get("idName") + "%")); // 单边匹配
                }
                if (map.containsKey("operatorName") && !StringUtils.isEmpty(map.get("operatorName"))) { // 操作人姓名
                    andPredicateList.add(cb.like(root.get("operatorName"), map.get("operatorName") + "%")); // 单边匹配
                }
                // 支付订单ID
                if (map.containsKey("orderId") && !StringUtils.isEmpty(map.get("orderId"))) {
                    andPredicateList.add(cb.like(root.get("orderId").as(String.class), map.get("orderId") + "%")); // 单边匹配
                }
                // 龙兜支付订单Id
                if (map.containsKey("ldOrderId") && !StringUtils.isEmpty(map.get("ldOrderId"))) {
                    andPredicateList.add(cb.equal(root.get("ldOrderId").as(String.class), map.get("ldOrderId") + "%")); // 单边匹配
                }
                // 订单来源
                if (map.containsKey("sourceType") && !StringUtils.isEmpty(map.get("sourceType"))) {
                    andPredicateList.add(cb.equal(root.get("sourceType").as(String.class), map.get("sourceType")));
                }
                if (map.containsKey("sourceTypes") && !StringUtils.isEmpty(map.get("sourceTypes"))) {// 来源
                    andPredicateList.add(root.get("sourceType").as(String.class).in(Arrays.asList(map.get("sourceTypes").toString().split(","))));
                }
                // 状态
                if (map.containsKey("status") && !StringUtils.isEmpty(map.get("status"))) {
                    try {
                        int status = Integer.parseInt(map.get("status").toString());
                        if (-1 != status) {
                            andPredicateList.add(cb.equal(root.get("status").as(int.class), status));
                        }
                    } catch (Exception e) {
                    }
                }
                // 状态，不等于
                if (map.containsKey("statusNotEqual") && !StringUtils.isEmpty(map.get("statusNotEqual"))) {
                    try {
                        int status = Integer.parseInt(map.get("statusNotEqual").toString());
                        if (-1 != status) {
                            andPredicateList.add(cb.notEqual(root.get("status").as(int.class), status));
                        }
                    } catch (Exception e) {
                    }
                }
                // 退款状态
                if (map.containsKey("refundStatus") && !StringUtils.isEmpty(map.get("refundStatus"))) {
                    try {
                        int status = Integer.parseInt(map.get("refundStatus").toString());
                        if (-1 != status) {
                            andPredicateList.add(cb.equal(root.get("refundStatus").as(int.class), status));
                        }
                    } catch (Exception e) {
                    }
                }
                // 班次
                if (map.containsKey("shiftId") && !StringUtils.isEmpty(map.get("shiftId"))) {
                    andPredicateList.add(cb.equal(root.get("shiftId").as(String.class), map.get("shiftId")));
                }
                // 支付方式
                if (map.containsKey("payType") && !StringUtils.isEmpty(map.get("payType"))) {
                    if (map.get("payType").toString().contains(",")) {
                        List<String> payTypeStr = Arrays.asList(map.get("payType").toString().split(","));
                        long count = payTypeStr.stream().filter(it -> !PayType.hasName(it)).count();
                        if(count > 0){
                            andPredicateList.add(root.get("payType").as(String.class).in(payTypeStr));
                        }else{
                            List<PayType> collect = payTypeStr.stream().map(PayType::valueOf).collect(Collectors.toList());
                            andPredicateList.add(root.get("payType").as(PayType.class).in(collect));
                        }

                    } else {
                        if(PayType.hasName(map.get("payType").toString())){
                            andPredicateList.add(cb.equal(root.get("payType").as(PayType.class), PayType.valueOf(map.get("payType").toString())));
                        }else{
                            andPredicateList.add(cb.equal(root.get("payType").as(String.class), map.get("payType")));
                        }
                    }
                }
                // 支付方式，不等于
                if (map.containsKey("payTypeNotEqual") && !StringUtils.isEmpty(map.get("payTypeNotEqual"))) {PayType.WECHAT_MP.getMessage();
                    if(PayType.hasName(map.get("payTypeNotEqual").toString())){
                        andPredicateList.add(cb.notEqual(root.get("payType").as(PayType.class), PayType.valueOf(map.get("payTypeNotEqual").toString())));
                    }else{
                        andPredicateList.add(cb.notEqual(root.get("payType").as(String.class), map.get("payTypeNotEqual")));
                    }

                }
                // 支付类型
                if (map.containsKey("optType") && !StringUtils.isEmpty(map.get("optType"))) {
                    andPredicateList.add(cb.equal(root.get("optType").as(int.class), map.get("optType")));
                }
                // 操作开始时间
                if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                    LocalDateTime startTime = LocalDateTime.parse(map.get("startDate").toString(), fmt);
                    andPredicateList
                            .add(cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }
                // 操作结束时间
                if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                    LocalDateTime endTime = LocalDateTime.parse(map.get("endDate").toString(), fmt);
                    andPredicateList.add(cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }
                Predicate[] andPredicateArr = new Predicate[andPredicateList.size()];
                if (orPredicateList.size() == 0) {
                    return cb.and(andPredicateList.toArray(andPredicateArr));
                } else {
                    Predicate[] orPredicateArr = new Predicate[orPredicateList.size()];
                    Predicate andPredicate = cb.and(andPredicateList.toArray(andPredicateArr));
                    Predicate orPredicate = cb.or(orPredicateList.toArray(orPredicateArr));
                    return cb.and(andPredicate, orPredicate);
                }
            }
        }, pageable);
    }

    /**
     * 带筛选条件的统计充值余额
     *
     * @param placeId
     * @param startDate
     * @param endDate
     * @param idNumber
     * @param cardTypeId
     * @param operatorName
     * @param idName
     * @param status
     * @param orderId
     * @param ldOrderId
     * @param payTypeNotEqual
     * @return
     */
    public Map<String, Integer> querySumCashTopupAndPresentTopup(String placeId, LocalDateTime startDate, LocalDateTime endDate, String idNumber,
                                                                 String cardTypeId, String operatorName, String idName,
                                                                 String status, String orderId, String ldOrderId, String payTypeNotEqual,
                                                                 String sourceType, String statusNotEqual) {
        Map<String, Integer> result = new HashMap<>();
        Map<String, String> map = logTopupRepository.querySumCashTopupAndPresentTopup(placeId, startDate, endDate, idNumber, cardTypeId, operatorName, idName, status, orderId, ldOrderId, payTypeNotEqual, sourceType, statusNotEqual);
        for (String key : map.keySet()) {
            Object obj = map.get(key);
            if (StringUtils.isEmpty(obj)) {
                result.put(key, 0);
            } else {
                result.put(key, new BigDecimal(obj.toString()).intValue());
            }
        }
        return result;
    }

    /**
     * 查询该场所下指定范围的充值订单记录
     *
     * @param placeId
     * @param ldorderIds
     * @return
     */
    public List<LogTopup> findByPlaceIdAndLdOrderIdIn(String placeId, List<String> ldorderIds, LocalDateTime startDateTime,LocalDateTime endDateTime) {
        return logTopupRepository.findByPlaceIdAndLdOrderIdInAndCreatedGreaterThanEqualAndCreatedLessThanEqual(placeId, ldorderIds, startDateTime, endDateTime);
    }

    /**
     * 按时间-卡号-总充值
     *
     * @param placeId
     * @param cardId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCostCashAmountByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                                    LocalDateTime endDateTime,List<Integer> topupSourceTypes) {
        Integer result = 0;
        if(!StringUtils.isEmpty(cardId)){
            result = logTopupRepository.sumCostCashAmountByCardIdAndDateTime(placeId, cardId, startDateTime,endDateTime,topupSourceTypes);
        }else{
            result = logTopupRepository.sumCostCashAmountByPlaceIdAndDateTime(placeId, startDateTime,endDateTime,topupSourceTypes);
        }
        return result == null ? 0 : result;
    }

    /**
     * 按时间-总充值
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumTopupAmountByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                                   LocalDateTime endDateTime) {
        Integer result = 0;
        if(!CollectionUtils.isEmpty(placeIds) && placeIds.size() == 1){
            result = logTopupRepository.sumCostCashAmountByPlaceIdAndDateTime(placeIds.get(0),startDateTime,endDateTime,Arrays.asList(0,1,2));
        }else{
            result = logTopupRepository.sumTopupAmountByPlaceIdsAndDateTime(placeIds, startDateTime,endDateTime);
        }

        return result == null ? 0 : result;
    }

    /**
     * 按时间--现金--总充值
     *
     * @param placeIds
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumCashAmountByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                                  LocalDateTime endDateTime) {
        Integer result = 0;
        if(null != placeIds && placeIds.size() == 1){
            result = logTopupRepository.sumCashAmountByDateTime(placeIds.get(0),startDateTime,endDateTime);
        }else{
            result = logTopupRepository.sumCashAmountByPlaceIdsAndDateTime(placeIds, startDateTime, endDateTime);
        }
        return result == null ? 0 : result;
    }

    /**
     * 按班次--现金--总充值
     *
     * @param placeId
     * @return
     */
    public int sumCashAmountByPlaceIdAndShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumCashAmountByPlaceIdAndShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按班次--线上--总充值
     *
     * @param placeId
     * @return
     */
    public int sumOnlineAmountByPlaceIdAndShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumOnlineAmountByPlaceIdAndShiftId(placeId, shiftId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 按时间-线上总收入
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumOnlineAmountByDateTime(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumOnlineAmountByDateTime(placeId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }



    /**
     * 按时间-总收入
     *
     * @param placeId
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public int sumAmountTotalIncomeByDateTime(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumAmountTotalIncomeByDateTimeAndPlaceId(placeId, startDateTime, endDateTime);
//        if(null != placeIds && placeIds.size() == 1){
//            result = logTopupRepository.sumAmountTotalIncomeByDateTimeAndPlaceId(placeIds.get(0), startDateTime, endDateTime);
//        }else{
//            result = logTopupRepository.sumAmountTotalIncomeByDateTimeAndPlaceIds(placeIds, startDateTime, endDateTime);
//        }
        return result == null ? 0 : result;
    }

    /**
     * 按班次-总收入
     *
     * @param placeId
     * @param shiftId
     * @return
     */
    public int sumAmountTotalIncomeByShiftIdAndPlaceId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceId(placeId, shiftId, startDateTime, endDateTime);
//        if(null != placeIds && placeIds.size() == 1){
//            result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceId(placeIds.get(0), shiftId);
//        }else{
//            result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceIds(placeIds, shiftId);
//        }
        return result == null ? 0 : result;
    }

    public int sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(String placeId, String shiftId, SourceType sourceType, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceIdAndSourceType(placeId, shiftId, sourceType.name(), startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    public int sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeId(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeId(placeId, shiftId, cardTypeId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    public int sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeIdNotIn(String placeId, String shiftId, String cardTypeId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Integer result = logTopupRepository.sumAmountTotalIncomeByShiftIdAndPlaceIdAndCardTypeIdNotIn(placeId, shiftId, cardTypeId, startDateTime, endDateTime);
        return result == null ? 0 : result;
    }

    /**
     * 现金充值，目前只给收银台包时调用
     *
     * @param billingCard
     * @param logShift
     * @param clientId
     * @param loginId
     * @param cashAmount
     */
    public void cashTopup(BillingCard billingCard, LogShift logShift, String clientId, String loginId, int cashAmount, String packagePayFlag) {
        LogTopup logTopup = new LogTopup();
        logTopup.setCardId(billingCard.getCardId());
        logTopup.setCashAmount(cashAmount);
        logTopup.setCashBalance(billingCard.getCashAccount() + cashAmount);
        logTopup.setClientId(clientId);
        logTopup.setCreated(LocalDateTime.now());
        logTopup.setCreater(null);
        logTopup.setIdName(billingCard.getIdName());
        logTopup.setIdNumber(billingCard.getIdNumber());
        logTopup.setCardTypeId(billingCard.getCardTypeId());
        logTopup.setCardTypeName(billingCard.getCardTypeName());
        logTopup.setLoginId(loginId);
        logTopup.setOrderId("CASH" + System.currentTimeMillis());
        logTopup.setOperator(logShift.getAccountId());
        logTopup.setOperatorName(logShift.getLoginAccountName());
        logTopup.setStatus(3);
        logTopup.setPayType(PayType.CASH);
        logTopup.setPlaceId(billingCard.getPlaceId());
        logTopup.setPresentAmount(0);
        logTopup.setPresentBalance(billingCard.getPresentAccount());
        logTopup.setShiftId(logShift.getShiftId());
        logTopup.setSourceType(SourceType.CASHIER);
        logTopup.setTopupTime(LocalDateTime.now());
        logTopup.setTopupRuleId(null);
//        logTopup.setTopupSourceType("2".equals(packagePayFlag) ? 1 : 0);
        logTopup.setTopupSourceType("2".equals(packagePayFlag) ? 1 : "3".equals(packagePayFlag) ? 2 : 0);//收银台开临时卡并包时的情况下，packagePayFlag值为3,充值来源类型记为开卡充值。

        save(logTopup);
    }

    public void update(EntityManager entityManager, Map<String, Object> parameMap, Map<String, Object> updateMap) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        entityManager.clear();
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaUpdate<LogTopup> update = cb.createCriteriaUpdate(LogTopup.class);
        Root<LogTopup> root = update.from(LogTopup.class);

        // 条件,有id就一定有起始时间
        if (parameMap.containsKey("id") && !StringUtils.isEmpty(parameMap.get("id"))) {
            Predicate idPredicate = cb.equal(root.get("id"), parameMap.get("id"));
            LocalDateTime startTime = DateTimeUtils.DateFormat(parameMap.get("startTime").toString());
            LocalDateTime endTime = DateTimeUtils.DateFormat(parameMap.get("endTime").toString());
            Predicate idPredicate2 = cb.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime);
            Predicate idPredicate3 = cb.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime);
            update.where(idPredicate,idPredicate2,idPredicate3);
        }

        // 拼接的参数
        for (Map.Entry<String, Object> entry : updateMap.entrySet()) {
            update.set(root.get(entry.getKey()), entry.getValue());
        }

        entityManager.createQuery(update).executeUpdate();
    }
}
