package com.rzx.dim4.billing.repository;

import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.billing.entity.LogRefund;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface LogRefundRepository extends JpaRepository<LogRefund, Long>, JpaSpecificationExecutor<LogRefund> {

    @Query(value = "SELECT SUM(cash_refund) AS sumCashRefund,\n" + "SUM(online_refund) AS sumOnlineRefund \n" + "FROM\n" + "log_refund\n"
            + "WHERE\n" + "place_id = :placeId \n"
            + "AND created >= :startDate AND created <= :endDate \n"
            + "AND if(:idNumber = '' OR :idNumber is null ,1=1, id_number = :idNumber) \n"
            + "AND if(:cardTypeId = '' OR :cardTypeId is null ,1=1, card_type_id = :cardTypeId) \n"
            + "AND if(:operatorName = '' OR :operatorName is null ,1=1, operator_name like :operatorName) \n"
            + "AND if(:idName = '' OR :idName is null ,1=1, id_name like :idName) \n"
            + "AND if(:refundOrderId = '' OR :refundOrderId is null ,1=1, refund_order_id like :refundOrderId) \n"
            + "AND if(:ldorderid = '' OR :ldorderid is null ,1=1, ldorderid like :ldorderid) \n"
            + "AND if(:sourceType = '' OR :sourceType is null ,1=1, source_type = :sourceType) \n", nativeQuery = true)
    Map<String, String> querySumCashRefundAndOnlineRefund(@Param("placeId") String placeId, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("idNumber") String idNumber,
                                               @Param("cardTypeId") String cardTypeId, @Param("operatorName") String operatorName, @Param("idName") String idName,
                                               @Param("refundOrderId") String refundOrderId, @Param("ldorderid") String ldorderid, @Param("sourceType") String sourceType);

    List<LogRefund> findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, LocalDateTime startTime, LocalDateTime endTime);

    List<LogRefund> findByPlaceIdAndShiftId(String placeId, String shiftId);

    /**
     * 按班次-线上总退款
     *
     * @param shiftId
     * @return
     */
    @Query("select sum(lr.onlineRefund) from LogRefund lr where lr.placeId=?1 and lr.shiftId=?2")
    Integer sumCostOnlineRefundByShiftId(String placeId, String shiftId);

    /**
     * 按班次-现金总退款
     *
     * @param shiftId
     * @return
     */
    @Query("select sum(lr.cashRefund) from LogRefund lr where lr.placeId=?1 and lr.shiftId=?2 ")
    Integer sumCostCashRefundByShiftId(String placeId, String shiftId);

    /**
     * 按班次-现金总退款
     *
     * @param shiftId
     * @return
     */
    @Query("select sum(lr.cashRefund) from LogRefund lr where lr.placeId=?1 and lr.shiftId=?2 and status = 1 and refund_type in(1,2)")
    Integer sumCostCashRefundByShiftIdAndRefundType(String placeId, String shiftId);

    /**
     * 按班次-总退款
     *
     * @param shiftId
     * @return
     */
    @Query("select sum(lr.cashRefund)+sum(lr.onlineRefund) from LogRefund lr where lr.placeId=?1 and lr.shiftId=?2 and status = 1 and refund_type in(1,2)")
    Integer sumTotalRefundByShiftIdAndRefundType(String placeId, String shiftId);

    /**
     * 按班次-来源-线上总退款
     *
     * @param shiftId
     * @return
     */
    @Query("select sum(lr.onlineRefund) from LogRefund lr where lr.placeId=?1 and lr.shiftId=?2 and lr.sourceType = ?3")
    Integer sumCostOnlineRefundByShiftIdAndSource(String placeId, String shiftId, SourceType sourceType);

    /**
     * 按场所--卡号--时间段-现金总退款
     *
     * @param cardId
     * @return
     */
    @Query("select sum(lr.cashRefund) from LogRefund lr where lr.placeId=?1 and lr.cardId=?2 and lr.created >= ?3 and lr.created <= ?4")
    Integer sumCashRefundByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                             LocalDateTime endDateTime);

    /**
     * 按场所--卡号--时间段-线上总退款
     *
     * @param cardId
     * @return
     */
    @Query("select sum(lr.onlineRefund) from LogRefund lr where lr.placeId=?1 and lr.cardId=?2 and lr.created >= ?3 and lr.created <= ?4")
    Integer sumOnlineRefundByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime,
                                               LocalDateTime endDateTime);

    /**
     * 按场所--时间段-现金总退款
     *
     * @return
     */
    @Query("select sum(lr.cashRefund) from LogRefund lr where lr.placeId in (?1) and lr.created >= ?2 and lr.created <= ?3")
    Integer sumCashRefundByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime,
                                             LocalDateTime endDateTime);

    /**
     * 按场所--时间段-线上总退款
     * @return
     */
    @Query("select sum(lr.onlineRefund) from LogRefund lr where lr.placeId in (?1) and lr.created >= ?2 and lr.created <= ?3")
    Integer sumOnlineRefundByPlaceIdsAndDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);

    /**
     * 按场所-班次-卡类型-退款方式 查询总退款额（线上+线下）
     * @return
     */
    @Query("SELECT sum(lo.cashRefund)+sum(lo.onlineRefund) FROM LogRefund lo  WHERE lo.placeId = ?1  AND lo.shiftId = ?2 and lo.cardTypeId=?3 and lo.status =1 and lo.refundType in(1,2) ")
    Integer sumRefundAmountByPlaceIdAndShiftIdAndCardTypeIdAndRefundType(String placeId,String shiftId,String cardTypeId);

}
