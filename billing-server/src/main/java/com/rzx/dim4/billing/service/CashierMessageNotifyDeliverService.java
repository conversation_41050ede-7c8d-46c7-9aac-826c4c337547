package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.CashierMessageNotifyDeliver;
import com.rzx.dim4.billing.entity.ClientWallpaperDeliver;
import com.rzx.dim4.billing.repository.CashierMessageNotifyDeliverRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class CashierMessageNotifyDeliverService {

    @Autowired
    private CashierMessageNotifyDeliverRepository cashierMessageNotifyDeliverRepository;

    public void saveCashierMessageNotifyDeliver (CashierMessageNotifyDeliver cashierMessageNotifyDeliver) {
        cashierMessageNotifyDeliverRepository.save(cashierMessageNotifyDeliver);
    }

    public Page<CashierMessageNotifyDeliver> findAll(String search, Pageable pageable) {
        if (StringUtils.isEmpty(search)) {
            return cashierMessageNotifyDeliverRepository.findAll(pageable);
        }
        Page<CashierMessageNotifyDeliver> page = cashierMessageNotifyDeliverRepository
                .findAll(new Specification<CashierMessageNotifyDeliver>() {
                    private static final long serialVersionUID = 1L;

                    @Override
                    public Predicate toPredicate(Root<CashierMessageNotifyDeliver> root, CriteriaQuery<?> criteriaQuery,
                                                 CriteriaBuilder builder) {
                        LocalDateTime now = LocalDateTime.now();
                        List<Predicate> predicateList = new ArrayList<>();
                        predicateList
                                .add(builder.greaterThanOrEqualTo(root.get("endTime").as(LocalDateTime.class), now));
                        predicateList
                                .add(builder.lessThanOrEqualTo(root.get("startTime").as(LocalDateTime.class), now));
                        predicateList.add(builder.like(root.get("placeIds"), "%" + search + "%"));
                        Predicate[] predicateArr = new Predicate[predicateList.size()];
                        return builder.and(predicateList.toArray(predicateArr));
                    }
                }, pageable);
        return page;
    }

    public Optional<CashierMessageNotifyDeliver> queryRecentMessageNotifyByPlaceId (String placeId) {
        LocalDateTime now = LocalDateTime.now();
        placeId = "%" + placeId + "%";
        return cashierMessageNotifyDeliverRepository.findTop1ByStartTimeLessThanEqualAndEndTimeGreaterThanEqualAndPlaceIdsLikeOrderByIdDesc(now, now, placeId);
    }

}
