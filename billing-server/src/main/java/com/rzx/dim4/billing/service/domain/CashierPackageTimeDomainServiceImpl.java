package com.rzx.dim4.billing.service.domain;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.OrderPayStatus;
import com.rzx.dim4.base.enums.billing.ServiceIndexes;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.marketing.OrderType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.enums.place.SpecialPlaceClients;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.marketing.OrdersApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.base.utils.ResultHandleUtil;
import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/11/29
 **/
@Slf4j
@Service
public class CashierPackageTimeDomainServiceImpl implements PackageTimeDomainService {

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private PlaceBizConfigService placeBizConfigService;

    @Autowired
    private LogRoomService logRoomService;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private LogShiftService logShiftService;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    private BillingCardDeductionService billingCardDeductionService;

    @Autowired
    private LogOperationService logOperationService;

    @Autowired
    private LogTopupService logTopupService;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private CashierAuthorityService cashierAuthorityService;

    @Autowired
    BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    private OrdersApi ordersApi;

    /**
     * 收银台开卡包时
     *
     * @param billingCardBO  会员卡信息
     * @param shiftId        班次ID
     * @param packageRuleId  包时套餐规则ID
     * @param packagePayType 包时套餐支付类型，1:余额包时 2:现金包时
     */
    @Override
    public void cashierCreate(BillingCardBO billingCardBO, String shiftId, String packageRuleId, int packagePayType) {
        if (null == billingCardBO) {
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        // 预包时
        packagePre(billingCardBO.getPlaceId(), shiftId, billingCardBO.getCardId(), packageRuleId, packagePayType, 0);
    }

    /**
     * 收银台预包时
     *
     * @param placeId        场所Id
     * @param shiftId        班次Id
     * @param cardId         计费卡Id
     * @param packageRuleId  包时套餐规则Id
     * @param packagePayType 包时套餐支付类型 1:余额包时 2:现金包时
     * @param limitedLogin   是否限制登入
     */
    @Override
    public void packagePre(String placeId, String shiftId, String cardId, String packageRuleId, int packagePayType, int limitedLogin) {
        // 获取卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        // 查询是否在包间上机
        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, cardId);
        if (logRoomOpt.isPresent()) {
            LogRoom logRoom = logRoomOpt.get();
            if (logRoom.getIsMaster() == 0) {
                // 副卡在包间上机 不允许操作包时
                throw new ServiceException(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
            }
        }

        // 2.查询当前上机状态
        BillingOnline billingOnline = null;
        LogLogin logLogin = null;
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
                cardId);
        if (optBillingOnline.isPresent()) {
            billingOnline = optBillingOnline.get();

            // 查询登录信息
            Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, billingOnline.getCardId(), billingOnline.getBillingTime());
            if (!optLogLogin.isPresent()) {
                throw new ServiceException(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
            }
            logLogin = optLogLogin.get();
        }

        // 查询班次
        Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
        if (!optLogShift.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
        }
        LogShift logShift = optLogShift.get();

        // 获取计费规则
        Optional<BillingRulePackageTime> optBillingRulePackageTime =
                billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, packageRuleId);
        if (!optBillingRulePackageTime.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        BillingRulePackageTime billingRulePackageTime = optBillingRulePackageTime.get();

        if (billingOnline != null && !billingRulePackageTime.getAreaIds().contains(billingOnline.getAreaId())) { // 区域不符合
            throw new ServiceException(ServiceCodes.BILLING_PT_AREA_CONFLICT);
        }

        if (!billingRulePackageTime.getCardTypeIds().contains(billingCard.getCardTypeId())) { // 卡类型不符合
            throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
        }

        // 获取扣费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(billingCard.getPlaceId());
        int deductionOrder = placeBizConfig.getDeductionOrder();

        // 判断包时时间
        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();

        LocalDateTime nowDateTime = LocalDateTime.now();
        int nowMinute = LocalDateTime.now().getMinute();
        float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
        float currHours = nowDateTime.getHour() + minute;

        if (billingOnline != null) {
            if (end - start > 0) { // 同一天
                if (currHours >= start && currHours < end) {
                    throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FUTURE_PACKAGE);
                }
            } else { // 跨0点包时
                if (currHours >= start || currHours < end) {
                    throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FUTURE_PACKAGE);
                }
            }
        }

        // 不管是否在线、是否是已经在包时段或包时长，只要PackageTimeReserve中已经存在一条status为0的记录，则不允许预包时
        Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                .findUnusedByPlaceIdAndCardId(placeId, cardId);
        if (packageTimeReserveOpt.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_PT_REPEAT);
        }

        // 预包时，先扣钱
        int executeType = 1; // 默认是走预包时业务，但是准点的预包时要写准点包时
        int costTemporaryOnlineAccount = 0; // 临时卡在线余额扣除金额
        int costCashAccount = 0; // 本金消费
        int costPresentAccount = 0; // 奖励消费
        if (1 == packagePayType) {
            // 余额包时
            if (billingRulePackageTime.getLimitMustCash() == 1) {
                deductionOrder = 0; // 包时限制必须本金
            }
            // 根据登入卡 获取所有卡
            List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);

            // 如果是限制了只能现金包时，判断现金余额是否足够
            if (billingRulePackageTime.getLimitMustCash() == 1
                    && billingCardDeductionService.sumAccount(billingCards, 0) < billingRulePackageTime.getPrice()) {
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_CASH_BALANCE);
            }

            if (billingCardDeductionService.sumAccount(billingCards, 2) < billingRulePackageTime.getPrice()) { // 余额不足
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }

            // 获取扣费信息
            List<PlaceChainBillingCardCostDetail> costDetails =
                    billingCardDeductionService.getChainBillingCardCostDetails(billingCard,
                            billingRulePackageTime.getPrice(),
                            logLogin == null ? "" : logLogin.getLoginId(),
                            0, deductionOrder);
            if (costDetails != null) {
                costTemporaryOnlineAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).sum();
                costCashAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).sum();
                costPresentAccount = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).sum();
            }
            // 7.扣费，先扣现金账户
            billingCardService.billingCardDeduction(costDetails, placeId);
            if (billingOnline != null) {
                billingOnline.setPackagePayFlag(1);
            }
        } else {
            // 现金包时
            logTopupService.cashTopup(billingCard,
                    logShift,
                    billingOnline == null ? "" : billingOnline.getClientId(),
                    logLogin == null ? "" : logLogin.getLoginId(),
                    billingRulePackageTime.getPrice(),
                    String.valueOf(packagePayType));

            logOperationService.addTopupLogOperation(SourceType.CASHIER,
                    PayType.CASH,
                    billingRulePackageTime.getPrice(),
                    0,
                    billingCard,
                    billingOnline,
                    logShift,
                    logLogin,
                    null,0,0);

            if (billingOnline != null) {
                billingOnline.setPackagePayFlag(2);
            }
        }

        // 获取预包时开始时间、结束时间
        LocalDateTime futureStartTime = null;
        LocalDateTime futureEndTime = null;
        if (billingOnline != null && billingOnline.getPackageFlag() > 0) {
            // 如果是正在包时上机
            LocalDateTime nowEndTime = billingOnline.getNextTime();

            // 计算第二次的开始时间
            futureStartTime = PackageTimeAlgorithm.getFutureStartTime(LocalDateTime.now(), billingRulePackageTime);
            futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
            if (billingRulePackageTime.getPackageFlag() == 1) {
                // 包时段
                if (futureStartTime.isBefore(nowEndTime) && futureEndTime.isAfter(nowEndTime)) {
                    // 第一次的结束时间就是第二次的开始时间
                    futureStartTime = nowEndTime;

                    futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
                }
            } else {
                // 包时长
                if (futureStartTime.isBefore(nowEndTime)) {
                    // 第一次的结束时间就是第二次的开始时间
                    futureStartTime = nowEndTime;
                    futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
                }
            }


        } else {
            if (((start - end < 0) && (currHours < start)) || ((start - end > 0) && (currHours > end) && (currHours < start))) {
                // 当天预包时/跨天预包时
                futureStartTime = PackageTimeAlgorithm.getStartTime(billingRulePackageTime.getStartTime());
                futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
            } else {
                executeType = 0;
                // 因为是在包时规则时间内，预包时的开始时间就是当前时间,此行代码修复 凌晨2点在收银台选择22:30-7:00包时，会当做未来时间处理,应该立即生效。
                futureStartTime = LocalDateTime.now();
                // futureStartTime = PackageTimeAlgorithm.getStartTime(LocalDateTime.now(),billingRulePackageTime);
                futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
            }
        }

        //*****************************网费支付包时改造start*******************************//
        // 向orders表写入记录，方便统计和退款等
        String orderId = this.createOrderRecord(billingRulePackageTime, billingCard, logShift, shiftId);
        //*****************************网费支付包时改造end*******************************//

        // 预包时入库
        packageTimeReserveService.futurePackageTime(futureStartTime, futureEndTime, billingCard, billingRulePackageTime,
                logShift, limitedLogin, packagePayType, costTemporaryOnlineAccount, costCashAccount, costPresentAccount, orderId);

        // 写入预包时操作记录
        logOperationService.addPackageTimeOperation(SourceType.CASHIER,
                executeType,
                packagePayType,
                0,
                billingCard,
                billingOnline,
                billingRulePackageTime,
                logShift,
                logLogin);
    }

    private String createOrderRecord(BillingRulePackageTime billingRulePackageTime, BillingCard billingCard, LogShift logShift, String shiftId) {
        String orderId = String.format("SHO%s%s", Dim4StringUtils.generateCode(3), System.currentTimeMillis());
        OrdersBO orders = new OrdersBO();
        orders.setPlaceId(billingCard.getPlaceId());
        orders.setOrderId(orderId);
        orders.setSourceType(SourceType.CASHIER);
        orders.setRuleId(billingRulePackageTime.getRuleId());
        orders.setTotalMoney(billingRulePackageTime.getPrice());
        orders.setRealMoney(billingRulePackageTime.getPrice());
        orders.setPayType(PayType.BILLING_CARD);
        orders.setClientId(SpecialPlaceClients.CASHIER.getClientId());
        orders.setClientName(SpecialPlaceClients.CASHIER.getClientName());
        orders.setCashierId(logShift != null ? logShift.getShiftId() : null);
        orders.setCreater(logShift != null ? StringUtils.isEmpty(logShift.getAccountId()) ? null : Long.parseLong(logShift.getAccountId()) : null);
        orders.setCreaterName(logShift != null ? logShift.getAccountName() : null);
        orders.setShiftId(shiftId);
        orders.setIdNumber(billingCard.getIdNumber());
        orders.setIdName(billingCard.getIdName());
        orders.setCardId(billingCard.getCardId());
        orders.setCardTypeId(billingCard.getCardTypeId());
        orders.setStatus(OrderPayStatus.FINISHED.getCode());
        orders.setOrderType(OrderType.PACKAGE_TIME_BILLING_CARD.getCode());
        orders.setIsMeals(0);
        orders.setPayTime(LocalDateTime.now());
        orders.setFinishedTime(LocalDateTime.now());
        orders.setDeleted(BaseEntity.NO);
        orders.setCreated(LocalDateTime.now());
        String remark = String.format("网费支付的包时订单:%s,生效人:%s", billingRulePackageTime.getRuleName(), billingCard.getCardId());
        orders.setRemark(remark.length() > 95 ? remark.substring(0, 95) : remark);

        GenericResponse<ObjDTO<OrdersBO>> orderResponse = ordersApi.saveOrder(orders);
        ResultHandleUtil.handleObjectResponse(orderResponse, orderBo -> {
            log.info("<<<<<<<<<CashierPackageTimeDomainServiceImpl.packagePre 创建订单成功, order={}>>>>>>>>>>", new Gson().toJson(orderBo));
        }, () -> {
            log.info("<<<<<<<<<CashierPackageTimeDomainServiceImpl.packagePre 创建订单失败, order={}>>>>>>>>>>", new Gson().toJson(orders));
        });

        return orderId;
    }

    /**
     * 收银台包时，立即生效
     *
     * @param placeId        场所Id
     * @param shiftId        班次id
     * @param cardId         计费卡id
     * @param packageRuleId  包时规则id
     * @param packagePayType 包时支付方式
     */
    @Override
    public void packageImmediately(String placeId, String shiftId, String cardId, String packageRuleId, int packagePayType, SourceType sourceType) {

        // 并发加锁处理
        String billingKey = billingLockAlgorithm.cardIdAcquireLock(placeId, cardId);
        if (billingKey == null) {
            // 没拿到锁
            throw new ServiceException(ServiceCodes.BILLING_IN_PROGRESS);
        }
        log.info("收银台包时:::::::放入锁时间::::" + LocalDateTime.now());

        LogShift logShift = null;
        // 查询班次
        if (SourceType.CASHIER.equals(sourceType)) {
            Optional<LogShift> optLogShift = logShiftService.findByPlaceIdAndShiftIdAndStatus(placeId, shiftId, 0);
            if (!optLogShift.isPresent()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_LOG_SHIFT_NOT_FOUND);
            }
            logShift = optLogShift.get();

            // 验证权限
            Optional<CashierAuthority> cashierAuthorityOpt = cashierAuthorityService.findByPlaceIdAndAccountId(placeId, logShift.getLoginAccountId());
            if (!cashierAuthorityOpt.isPresent()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
            }

            CashierAuthority cashierAuthority = cashierAuthorityOpt.get();
            if (!cashierAuthority.getOpAuthority().contains(String.valueOf(ServiceIndexes.CashierPackageTimeNew.getValue()))) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_CASHIER_NO_AUTH);
            }
        }

        // 获取卡信息
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
        if (!optBillingCard.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_CARD_NOT_FOUND);
        }
        BillingCard billingCard = optBillingCard.get();

        // 查询是否在包间上机
        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, cardId);
        if (logRoomOpt.isPresent()) {
            LogRoom logRoom = logRoomOpt.get();
            if (logRoom.getIsMaster() == 0) {
                // 副卡在包间上机 不允许操作包时
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_ROOM_SECOND_CARD_NOT_SUPPORT_PACKAGE);
            }
        }

        // 2.查询当前上机状态
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId,
                cardId);
        if (!optBillingOnline.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_ONLINE_NOT_FOUND);// 未找到该客户端在线计费信息
        }
        BillingOnline billingOnline = optBillingOnline.get();

        if (billingOnline.getPackageFlag() > 0) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_PT_REPEAT);
        }

        int oldCommonPrice = billingOnline.getCommonPrice();

        // 查询登录信息
        Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(placeId, billingOnline.getCardId(), billingOnline.getBillingTime());
        if (!optLogLogin.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_LOG_LOGIN_NOT_FOUND);
        }
        LogLogin logLogin = optLogLogin.get();


        // 获取计费规则
        Optional<BillingRulePackageTime> optBillingRulePackageTime = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, packageRuleId);
        if (!optBillingRulePackageTime.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        BillingRulePackageTime billingRulePackageTime = optBillingRulePackageTime.get();

        if (!billingRulePackageTime.getAreaIds().contains(billingOnline.getAreaId())) { // 区域不符合
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_PT_AREA_CONFLICT);
        }

        if (!billingRulePackageTime.getCardTypeIds().contains(billingOnline.getCardTypeId())) { // 卡类型不符合
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
        }

        // 获取扣费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(billingCard.getPlaceId());
        int deductionOrder = placeBizConfig.getDeductionOrder();

        // 判断包时时间
        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();

        LocalDateTime nowDateTime = LocalDateTime.now();
        int nowMinute = LocalDateTime.now().getMinute();
        float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
        float currHours = nowDateTime.getHour() + minute;
        int extraDeductionCash = 0;
        int extraDeductionPresent = 0;

        if (end - start > 0) { // 同一天
            if (currHours < start || currHours >= end) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_PT_NOT_START);
            }
        } else { // 跨0点包时
            if (currHours < start && currHours >= end) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_PT_NOT_START);
            }
        }

        if (1 == packagePayType) {
            // 余额包时
            if (billingRulePackageTime.getLimitMustCash() == 1) {
                deductionOrder = 0;
            }
            // 根据登入卡 获取所有卡
            List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);

            // 如果是限制了只能现金包时，判断现金余额是否足够
            if (billingRulePackageTime.getLimitMustCash() == 1 && billingCardDeductionService.sumAccount(billingCards, 0) < billingRulePackageTime.getPrice()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_CASH_BALANCE);
            }

            if (billingCardDeductionService.sumAccount(billingCards, 2) < billingRulePackageTime.getPrice()) { // 余额不足
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }

            // 获取扣费信息
            List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, billingRulePackageTime.getPrice(), logLogin.getLoginId(), 0, deductionOrder);

            // 7.扣费，先扣现金账户
            billingCardService.billingCardDeduction(costDetails, placeId);
            billingOnline.setPackagePayFlag(1);
            if (!StringUtils.isEmpty(costDetails)) {
                extraDeductionCash = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum);
                extraDeductionPresent = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum);
            }
        } else {
            // 现金包时
            extraDeductionCash = billingRulePackageTime.getPrice();
            logTopupService.cashTopup(billingCard, logShift, billingOnline.getClientId(), logLogin.getLoginId(), billingRulePackageTime.getPrice(), String.valueOf(packagePayType));

            logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, billingRulePackageTime.getPrice(), 0,
                    billingCard, billingOnline, logShift, logLogin, null,0,0);

            billingOnline.setPackagePayFlag(2);
        }

        LocalDateTime nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime, billingRulePackageTime);
        if (StringUtils.isEmpty(nextTime)) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        billingOnline.setUpdated(nowDateTime);
        billingOnline.setRuleId(billingRulePackageTime.getRuleId());
        billingOnline.setNextTime(nextTime);
        billingOnline.setDeduction(billingOnline.getDeduction() + billingRulePackageTime.getPrice());
        billingOnline.setDeductionCash(billingOnline.getDeductionCash() + extraDeductionCash);
        billingOnline.setDeductionPresent(billingOnline.getDeductionPresent() + extraDeductionPresent);
        billingOnline.setCommonPrice(billingRulePackageTime.getPrice());
        billingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
        billingOnline.setAccFlag(0);
        billingOnlineService.save(billingOnline);

        logOperationService.addPackageTimeOperation(sourceType, 0, packagePayType, 0, billingCard, billingOnline,
                billingRulePackageTime, logShift, logLogin);

        logOperationService.addConvertBillingRuleOperation(sourceType, 1, oldCommonPrice,
                0, billingCard, billingOnline, null, billingRulePackageTime, logShift, logLogin);

        // 释放锁
        billingLockAlgorithm.releaseLock(billingKey);
        log.info("收银台包时成功:::::::释放锁时间::::" + LocalDateTime.now());
    }

    /**
     * 收银台续包时
     * @param placeId
     * @param ruleId
     * @param billingKey
     * @param billingOnline
     * @param billingCard
     * @param packageType
     * @param logLogin
     * @param logShift
     * @param oldBillingRulePackageTime
     */
    @Override
    public void continuePackageTime(String placeId, String ruleId, String billingKey, BillingOnline billingOnline, BillingCard billingCard, String packageType, LogLogin logLogin, LogShift logShift, BillingRulePackageTime oldBillingRulePackageTime) {
        // 获取选择的包时规则
        Optional<BillingRulePackageTime> optBillingRulePackageTime = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, ruleId);
        if (!optBillingRulePackageTime.isPresent()) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        BillingRulePackageTime billingRulePackageTime = optBillingRulePackageTime.get();

        // 续包时长,不能和预包时同时存在
        if (billingRulePackageTime.getPackageFlag() == 2) {
            // 不管是否在线、是否是已经在包时段或包时长，只要PackageTimeReserve中已经存在一条status为0的记录，则不允许预包时
            Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                    .findUnusedByPlaceIdAndCardId(placeId, billingCard.getCardId());
            if (packageTimeReserveOpt.isPresent()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_PT_REPEAT);
            }
        }

        if (!billingRulePackageTime.getAreaIds().contains(billingOnline.getAreaId())) { // 区域不符合
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_PT_AREA_CONFLICT);
        }

        if (!billingRulePackageTime.getCardTypeIds().contains(billingOnline.getCardTypeId())) { // 卡类型不符合
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_PT_CARD_TYPE_CONFLICT);
        }

        // 获取扣费配置
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(billingCard.getPlaceId());
        int deductionOrder = placeBizConfig.getDeductionOrder();

        // 判断包时时间
        float start = billingRulePackageTime.getStartTime();
        float end = billingRulePackageTime.getEndTime();
        LocalDateTime nowDateTime = LocalDateTime.now();
        int nowMinute = LocalDateTime.now().getMinute();
        float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
        float currHours = nowDateTime.getHour() + minute;
        int extraDeductionCash = 0;
        int extraDeductionPresent = 0;

        if (end - start > 0) { // 同一天
            if (currHours < start || currHours >= end) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_PT_NOT_START);
            }
        } else { // 跨0点包时
            if (currHours < start && currHours >= end) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_PT_NOT_START);
            }
        }

        if ("1".equals(packageType)) {
            // 余额包时
            if (billingRulePackageTime.getLimitMustCash()==1) {
                deductionOrder = 0;
            }
            // 根据登入卡 获取所有卡
            List<BillingCard> billingCards = billingCardDeductionService.getChainBillingCard(billingCard);

            // 如果是限制了只能现金包时，判断现金余额是否足够
            if (billingRulePackageTime.getLimitMustCash()==1 && billingCardDeductionService.sumAccount(billingCards,0) < billingRulePackageTime.getPrice()) {
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_CASH_BALANCE);
            }

            if (billingCardDeductionService.sumAccount(billingCards,2) < billingRulePackageTime.getPrice()) { // 余额不足
                // 释放锁
                billingLockAlgorithm.releaseLock(billingKey);
                throw new ServiceException(ServiceCodes.BILLING_INSUFFICIENT_BALANCE);
            }

            // 获取扣费信息
            List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard,billingRulePackageTime.getPrice(), logLogin.getLoginId(),0, deductionOrder);

            // 7.扣费，先扣现金账户
            billingCardService.billingCardDeduction(costDetails, placeId);
            if (!StringUtils.isEmpty(costDetails)) {
                extraDeductionCash = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostCash).reduce(0, Integer::sum) + costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostTemporaryOnlineAccount).reduce(0, Integer::sum);
                extraDeductionPresent = costDetails.stream().mapToInt(PlaceChainBillingCardCostDetail::getCostPresent).reduce(0, Integer::sum);
            }
            billingOnline.setPackagePayFlag(1);
        } else {
            // 现金包时
            logTopupService.cashTopup(billingCard, logShift, billingOnline.getClientId(), logLogin.getLoginId(),billingRulePackageTime.getPrice(), packageType);
            logOperationService.addTopupLogOperation(SourceType.CASHIER, PayType.CASH, billingRulePackageTime.getPrice(), 0,
                    billingCard, billingOnline, logShift, logLogin, null,0,0);

            billingOnline.setPackagePayFlag(2);
            extraDeductionCash = billingRulePackageTime.getPrice();
        }

        LocalDateTime nextTime;
        if (billingRulePackageTime.getPackageFlag() == 1) {
            // 续包时段
            nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime, billingRulePackageTime);

        } else {
            // 包时长
            // 计算第二次的开始时间
            LocalDateTime futureStartTime = billingOnline.getNextTime();
            nextTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
        }
        if (StringUtils.isEmpty(nextTime)) {
            // 释放锁
            billingLockAlgorithm.releaseLock(billingKey);
            throw new ServiceException(ServiceCodes.BILLING_RULE_NOT_FOUND);
        }
        billingOnline.setUpdated(nowDateTime);
        billingOnline.setRuleId(billingRulePackageTime.getRuleId());
        billingOnline.setNextTime(nextTime);
        billingOnline.setDeduction(billingOnline.getDeduction() + billingRulePackageTime.getPrice());
        billingOnline.setDeductionCash(billingOnline.getDeductionCash() + extraDeductionCash);
        billingOnline.setDeductionPresent(billingOnline.getDeductionPresent() + extraDeductionPresent);
        billingOnline.setCommonPrice(billingRulePackageTime.getPrice());
        billingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
        billingOnline.setAccFlag(0);
        billingOnlineService.save(billingOnline);

        logOperationService.addPackageTimeOperation(SourceType.CASHIER, 2, Integer.parseInt(packageType), 0, billingCard, billingOnline,
                billingRulePackageTime, logShift, logLogin);

        logOperationService.addConvertBillingRuleOperation(SourceType.CASHIER,3, 0,
                0, billingCard, billingOnline, oldBillingRulePackageTime, billingRulePackageTime, logShift, logLogin);

        // 释放锁
        billingLockAlgorithm.releaseLock(billingKey);
        log.info("收银台续包时成功:::::::释放锁时间::::" + LocalDateTime.now());
    }

}
