package com.rzx.dim4.billing.service.domain;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.bo.billing.BookSeatsBO;
import com.rzx.dim4.base.bo.iot.IotAuthConfigBO;
import com.rzx.dim4.base.bo.iot.LogAuthFeeBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.bo.place.PlaceConfigBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.bo.user.customer.BaseInfo;
import com.rzx.dim4.base.bo.user.customer.CardInfo;
import com.rzx.dim4.base.bo.user.customer.ClientInfo;
import com.rzx.dim4.base.bo.user.customer.MiniAuthContextBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.ActiveType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.iot.IotAuthConfigApi;
import com.rzx.dim4.base.service.feign.place.PlaceProfileApi;
import com.rzx.dim4.base.utils.WechatAgeConfigUtils;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.billing.entity.BillingCard;
import com.rzx.dim4.billing.entity.BillingOnline;
import com.rzx.dim4.billing.entity.LogQrCode;
import com.rzx.dim4.billing.service.BillingCardService;
import com.rzx.dim4.billing.service.BillingOnlineService;
import com.rzx.dim4.billing.service.LogQrCodeService;
import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm.getStartTime;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/5/24
 **/
@Slf4j
@Service
public class PlaceDomainServiceImpl implements PlaceDomainService {

    @Autowired
    private PlaceProfileApi placeProfileApi;

    @Autowired private LogQrCodeService logQrCodeService;

    @Autowired private BillingCardService billingCardService;

    @Autowired private IotAuthConfigApi iotAuthConfigApi;

    @Autowired private BillingOnlineService billingOnlineService;

    @Autowired private PlaceChainStoresService placeChainStoresService;

    @Autowired private BillingCardTypeService billingCardTypeService;

    @Autowired private PackageTimeReserveService packageTimeReserveService;

    @Autowired private BillingRuleCommonService billingRuleCommonService;

    @Autowired private BillingCardBlackListService billingCardBlackListService;

    @Autowired private PlaceBizConfigService placeBizConfigService;

    @Autowired private PlaceServerService placeServerService;

    @Autowired private BookSeatsService bookSeatsService;

    @Autowired
    private BillingRulePackageTimeService billingRulePackageTimeService;


    @Override
    public MiniAuthContextBO doContext(String token,String idNumber, String idName, String scanState) {
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(idNumber) || StringUtils.isEmpty(idName)) {
            log.warn("入参为空");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<LogQrCode> optionalLogQrCode = logQrCodeService.findByToken(token);
        LogQrCode logQrCode = optionalLogQrCode.orElseThrow(() -> {
            log.warn("token错误，无法查到对应场所信息");
            return new ServiceException(ServiceCodes.BAD_PARAM);
        });
        if (logQrCode.getDeadline().isBefore(LocalDateTime.now())) {// 增加判断条件
            log.warn("token错误，二维码已失效");
            throw new ServiceException(ServiceCodes.BILLING_QR_CODE_NOT_FOUND);
        }

        if ("1".equals(scanState) && logQrCode.getDeadline().isBefore(LocalDateTime.now())) {
            log.warn("二维码已失效 token:::{}",token);
            throw new ServiceException(ServiceCodes.BILLING_QR_CODE_LOSE_EFFICACY);
        }

        String placeId = logQrCode.getPlaceId();
        String clientId = logQrCode.getClientId();
        String areaId = logQrCode.getAreaId();

        MiniAuthContextBO miniAuthContextBO = new MiniAuthContextBO();
        // 1、获取场所基本信息
        setBaseInfo(placeId, miniAuthContextBO);

        // 2、获取是否已经开卡/卡信息
        setHaveCard(idNumber, idName, miniAuthContextBO);
        PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
        miniAuthContextBO.setCertificateClient(!StringUtils.isEmpty(placeBizConfig.getCertificateClient()) && placeBizConfig.getCertificateClient().contains("WECHAT"));
        if(!StringUtils.isEmpty(clientId)){ //收银端二维码没有clientId
            // 客户端信息
            setClientInfo(clientId, miniAuthContextBO);

            CardInfo cardInfo = miniAuthContextBO.getCardInfo();
            if(null == cardInfo || cardInfo.getFinished() == 1){  //没有卡 或 有卡未上机
                //添加判断是否禁止扫码激活
                if(null != placeBizConfig){
                    boolean forbiddenClientActiveDirectlyFlag = placeBizConfig.getForbiddenClientActiveDirectly() == 1;
                    log.info("forbiddenClientActiveDirectlyFlag={}", forbiddenClientActiveDirectlyFlag);
                    if (forbiddenClientActiveDirectlyFlag) {
                        if (null == cardInfo) {
                            log.info("没有计费卡，需要去收银台开卡激活");
                            throw new ServiceException(ServiceCodes.BILLING_NEED_ACTIVE_IN_CASHIER);
                        }
                        LocalDateTime activeTime = cardInfo.getActiveTime();

                        if (activeTime == null || Duration.between(activeTime, LocalDateTime.now()).toMinutes() > 30) {
                            throw new ServiceException(ServiceCodes.BILLING_NEED_ACTIVE_IN_CASHIER);
                        }
                    }
                }
            }

        }else{
            miniAuthContextBO.setCashierId(logQrCode.getCashierId());
        }


        // 2、获取是否已经开卡/卡信息
        // setHaveCard(idNumber, idName, miniAuthContextBO);

        // 3、获取场所是否开启实名认证收费
        setAuthNeedCharge(miniAuthContextBO);

        // 4、获取当前用户本次实名认证是否需要收费
        setCurrentNeedCharge(idNumber, miniAuthContextBO);

        // 5、根据场所配置，判定是否允许当前用户上机
        setNotAllowLogin(idNumber, miniAuthContextBO);

        // 6、新增判断是否允许0元上机登录：1.用户是否已经开通包时、2.是否包间副卡登录 、 判断是否已经订座
        setPackageLogin(areaId, miniAuthContextBO);

        // 7、判断会员类型在该区域是否支持上机
        setNotRuleLogin(idNumber, miniAuthContextBO,areaId);

        return miniAuthContextBO;
    }

    @Override
    public MiniAuthContextBO doCashierContext(String token) {
        if (StringUtils.isEmpty(token)) {
            log.warn("入参为空");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<LogQrCode> optionalLogQrCode = logQrCodeService.findByToken(token);
        LogQrCode logQrCode = optionalLogQrCode.orElseThrow(() -> {
            log.warn("token错误，无法查到对应场所信息");
            return new ServiceException(ServiceCodes.BAD_PARAM);
        });

        String placeId = logQrCode.getPlaceId();

        MiniAuthContextBO miniAuthContextBO = new MiniAuthContextBO();
        // 1、获取场所基本信息
        setBaseInfo(placeId, miniAuthContextBO);

        miniAuthContextBO.setCashierId(logQrCode.getCashierId());

        // 3、获取场所是否开启实名认证收费
        setAuthNeedCharge(miniAuthContextBO);
        return miniAuthContextBO;
    }

    private void setNotAllowLogin(String idNumber, MiniAuthContextBO miniAuthContextBO) {
        GenericResponse<ObjDTO<PlaceConfigBO>> response = placeProfileApi.config(miniAuthContextBO.getBaseInfo().getPlaceId());
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.warn("获取place 场所配置信息失败，code={},msg={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
        PlaceConfigBO placeConfigBO = response.getData().getObj();
        String wechatAgeConfig = placeConfigBO.getWechatAgeConfig();
        // log.info("wechatAgeConfig={}", wechatAgeConfig);

        log.info("wechatAgeConfig={}", wechatAgeConfig);
        miniAuthContextBO.setFaceAuthType(2 == placeConfigBO.getAuthWay());
        Boolean aBoolean = WechatAgeConfigUtils.verifyWechatAgeConfig(wechatAgeConfig, idNumber);
        if(!aBoolean){
            miniAuthContextBO.setNotAllowedLogin(true);
            miniAuthContextBO.setNotAllowedLoginDesc("年龄不在许可范围内，不允许上机");
        }else{
            miniAuthContextBO.setNotAllowedLogin(false);
            miniAuthContextBO.setNotAllowedLoginDesc("允许上机");
        }
//        if (!StringUtils.isEmpty(wechatAgeConfig) && wechatAgeConfig.contains("-")) {
//            // log.info("获取到场所上机年龄限制");
//            String wechatAgeConfigMin = wechatAgeConfig.split("-")[0];
//            String wechatAgeConfigMax = wechatAgeConfig.split("-")[1];
//
//            int yearBirth = Integer.parseInt(idNumber.substring(6, 10));
//            int monthBirth = Integer.parseInt(idNumber.substring(10, 12));
//            int dayBirth = Integer.parseInt(idNumber.substring(12, 14));
//
//            //获取当前年月日并计算年龄
//            LocalDate localDate = LocalDate.now();
//            int year = localDate.getYear();
//            int month = localDate.getMonth().getValue();
//            int dayOfMonth = localDate.getDayOfMonth();
//            int age = year - yearBirth;
//            if (month < monthBirth || (month == monthBirth && dayOfMonth < dayBirth)) {
//                age--;
//            }
//            boolean is_adult = age >= Integer.parseInt(wechatAgeConfigMin) && age <= Integer.parseInt(wechatAgeConfigMax);
//            // log.info("the age is {}, is_adult={}", age, is_adult);
//            if (is_adult) {
//                miniAuthContextBO.setNotAllowedLogin(false);
//                miniAuthContextBO.setNotAllowedLoginDesc("允许上机");
//            } else {
//                miniAuthContextBO.setNotAllowedLogin(true);
//                miniAuthContextBO.setNotAllowedLoginDesc("年龄不在许可范围内，不允许上机");
//            }
//        } else {
//            //log.info("场所没有设置上机年龄限制");
//            miniAuthContextBO.setNotAllowedLogin(false);
//            miniAuthContextBO.setNotAllowedLoginDesc("允许上机");
//        }
        //添加黑名单校验(有卡的时候才校验)
        if (!ObjectUtils.isEmpty(miniAuthContextBO.getCardInfo())) {
            // 查询黑名单
            Optional<BillingCardBlackList> billingCardBlackListOpt = billingCardBlackListService.findByPlaceIdAndCardId(miniAuthContextBO.getBaseInfo().getPlaceId(),
                    miniAuthContextBO.getCardInfo().getCardId());
            if (billingCardBlackListOpt.isPresent()) {
                // 如果有，校验限制时间
                BillingCardBlackList billingCardBlackList = billingCardBlackListOpt.get();
                String limitDate = billingCardBlackList.getLimitDate();
                int nowDay = LocalDateTime.now().getDayOfWeek().getValue();
                if (limitDate.contains(String.valueOf(nowDay))) {
                    // 继续校验当前时间是否在限制区间
                    LocalTime nowTime = LocalTime.now();
                    LocalTime limitStartTime = billingCardBlackList.getLimitStartTime();
                    LocalTime limitEndTime = billingCardBlackList.getLimitEndTime();
                    if (nowTime.isAfter(limitStartTime) && nowTime.isBefore(limitEndTime)) {
                        miniAuthContextBO.setNotAllowedLogin(true);
                        miniAuthContextBO.setNotAllowedLoginDesc("当前时段禁止该用户上机，请联系管理员解禁！");
                    }
                }
            }
        }

    }

    private void setClientInfo(String clientId, MiniAuthContextBO miniAuthContextBO) {
        GenericResponse<ObjDTO<PlaceClientBO>> response = placeProfileApi.findClient(miniAuthContextBO.getBaseInfo().getPlaceId(), clientId);
        if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
            log.warn("获取客户端信息失败，code={},msg={}", response.getCode(), response.getMessage());
            throw new ServiceException(ServiceCodes.getByCode(response.getCode()));
        }
        PlaceClientBO placeClientBO = response.getData().getObj();
        ClientInfo clientInfo = new ClientInfo();
        BeanUtils.copyProperties(placeClientBO, clientInfo);
        miniAuthContextBO.setClientInfo(clientInfo);
    }

    private void setCurrentNeedCharge(String idNumber, MiniAuthContextBO miniAuthContextBO) {
        //判断是否开启实名认证收费，未开启直接不需要收费
        if(!miniAuthContextBO.isAuthNeedCharge()){
            miniAuthContextBO.setCurrentNeedCharge(false);
            return;
        }

       try {
           GenericResponse<ObjDTO<LogAuthFeeBO>> genericResponse =
                   iotAuthConfigApi.currentNeedCharge(miniAuthContextBO.getBaseInfo().getPlaceId(), idNumber);
           if (ServiceCodes.NO_ERROR.getCode() != genericResponse.getCode()) {
               log.info("从 iot-server 获取当前用户在当前场所本次认证是否需要收费失败，code={}, msg={}",
                       genericResponse.getCode(), genericResponse.getMessage());
               throw new ServiceException(ServiceCodes.getByCode(genericResponse.getCode()));
           }
           LogAuthFeeBO logAuthFeeBO = genericResponse.getData().getObj();
           boolean currentNeedCharge = (null == logAuthFeeBO);
           miniAuthContextBO.setCurrentNeedCharge(currentNeedCharge);
           if(!currentNeedCharge && !ObjectUtils.isEmpty(logAuthFeeBO.getEffectiveTime())){
               miniAuthContextBO.setEffectiveTime(DateTimeUtils.getTimeFormat(logAuthFeeBO.getEffectiveTime()));
               miniAuthContextBO.setTimeRemaining(Math.abs(DateTimeUtils.getOnlineTimeNumber(logAuthFeeBO.getEffectiveTime())));
           }

       }catch (Exception e){
           miniAuthContextBO.setCurrentNeedCharge(false);
           log.info("从 iot-server 获取当前用户在当前场所本次认证是否需要收费失败"+e.getMessage());
       }

    }

    private void setBaseInfo(String placeId, MiniAuthContextBO miniAuthContextBO) {
        PlaceProfileBO placeProfileBO = getPlaceProfileBO(placeId);
        miniAuthContextBO.setBaseInfo(new BaseInfo(placeProfileBO));
    }

    private void setHaveCard(String idNumber, String idName, MiniAuthContextBO miniAuthContextBO) {
        CardInfo cardInfo = getCardInfo(idNumber, idName, miniAuthContextBO);
        if (null == cardInfo) {
            miniAuthContextBO.setHaveCard(false);
        } else {
            miniAuthContextBO.setHaveCard(true);
            miniAuthContextBO.setCardInfo(cardInfo);
            // 获取在线信息
            Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findByPlaceIdAndIdNumber(miniAuthContextBO.getBaseInfo().getPlaceId(), idNumber);
            if (billingOnlineOpt.isPresent()) {
                BillingOnline billingOnline = billingOnlineOpt.get();
                cardInfo.setFinished(billingOnline.getFinished());
            } else {
                cardInfo.setFinished(1);
            }
        }

    }

    private void setAuthNeedCharge(MiniAuthContextBO miniAuthContextBO) {
        List<IotAuthConfigBO> iotAuthConfigBOS = getIotAuthConfigBOS(miniAuthContextBO.getBaseInfo().getPlaceId());
        if (!CollectionUtils.isEmpty(iotAuthConfigBOS)) {
            miniAuthContextBO.setAuthNeedCharge(true);
        } else {
            miniAuthContextBO.setAuthNeedCharge(false);
        }
    }

    private List<IotAuthConfigBO> getIotAuthConfigBOS(String placeId) {
       try {
           GenericResponse<ListDTO<IotAuthConfigBO>> response = iotAuthConfigApi.needCharge(placeId, "", "");
           if(ServiceCodes.NO_SERVICE.getCode() == response.getCode()){
               //如果服务调用失败了，直接返回未开启实名认证收费
               return new ArrayList<>();
           }
           if (ServiceCodes.NO_ERROR.getCode() != response.getCode()) {
               log.warn("获取 iot-server  场所实名认证收费规则时失败，code={}, msg={}", response.getCode(), response.getMessage());
               throw new ServiceException(ServiceCodes.SYSTEM_ERROR);
           }
           return response.getData().getList();
       }catch (Exception e){
           log.info("获取 iot-server  场所实名认证收费规则时失败"+e.getMessage());
           return new ArrayList<>();
       }
    }

    private CardInfo getCardInfo(String idNumber, String idName, MiniAuthContextBO miniAuthContextBO) {
        String placeId = miniAuthContextBO.getBaseInfo().getPlaceId();
        //判断是不是连锁门店，是连锁的话在当前门店有没有卡，没有的话新增一张金额为0的连锁会员卡
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresService.findByPlaceId(placeId);
        if (optPlaceChainStores.isPresent()) { // 如果是连锁，查询开卡场所
            PlaceChainStores placeChainStores = optPlaceChainStores.get();
           // List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(optPlaceChainStores.get().getChainId());
            // 获取连锁场所ID列表
           // List<String> placeIds = placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());

            // 查询在当前连锁场所下开通的所有会员卡
            List<BillingCard> chainPlaceCards = billingCardService.findByChainIdAndIdNumber(placeChainStores.getChainId(), idNumber);
            if (CollectionUtils.isEmpty(chainPlaceCards)) {
                return null;
            }

            List<BillingCard> currCard = chainPlaceCards.stream().filter(e -> e.getPlaceId().equals(placeId)).collect(Collectors.toList()); //当前网吧的会员卡
            List<BillingCard> roamCard = chainPlaceCards.stream().filter(e -> !e.getPlaceId().equals(placeId) &&
                    !"1000".equals(e.getCardTypeId()) && !"1002".equals(e.getCardTypeId())).collect(Collectors.toList());//连锁其他门店的会员卡(非临时卡和工作卡)
            //如果当前门店没有会员卡并且已在连锁下拥有会员卡，则需要新增一张金额为0的连锁会员卡
            if(CollectionUtils.isEmpty(currCard) && !CollectionUtils.isEmpty(roamCard)){
                // 当前网吧没有卡信息，静默生成一条(此时有可能是已经删除的卡)
                BillingCard card = new BillingCard();
                BeanUtils.copyProperties(roamCard.get(0), card);
                // 查询最新的卡类型
                Optional<BillingCardType> billingCardTypeOpt = billingCardTypeService.findByPlaceIdAndTypeName(placeId, card.getCardTypeName());
                if (billingCardTypeOpt.isPresent()) {
                    BillingCardType billingCardType = billingCardTypeOpt.get();
                    card.setCardTypeId(billingCardType.getCardTypeId());
                }

                Optional<BillingCard> cardOptional = billingCardService.findByPlaceIdAndIdNumber(placeId, idNumber);
                if (cardOptional.isPresent()) {
                    BillingCard billingCard = cardOptional.get();
                    if (billingCard.getDeleted() == 1) {
                        card.setId(billingCard.getId());
                        card.setCardId(billingCard.getCardId());
                        card.setDeleted(0);
                    }
                } else {
                    card.setId(null);
                    card.setCardId(null);
                }
                card.setPlaceId(placeId);
                card.setCashAccount(0);
                card.setPresentAccount(0);
                card.setChainCard(1);
                card.setPoints(0);
                card.setCreated(LocalDateTime.now());
                billingCardService.save(card);
            }else if(!CollectionUtils.isEmpty(currCard) && CollectionUtils.isEmpty(roamCard)){
                //如果在当前门店有卡，并且在其他连锁门店没有会员卡，直接返回这张卡信息就行
                return new CardInfo(currCard.get(0).toBO());
            }else if(!CollectionUtils.isEmpty(currCard) && !CollectionUtils.isEmpty(roamCard)){
                //如果当前门店是工作卡或者临时卡直接返回数据
                if("1002".equals(currCard.get(0).getCardTypeId()) || "1000".equals(currCard.get(0).getCardTypeId())){
                    return new CardInfo(currCard.get(0).toBO());
                }
                //如果多个门店都有卡，根据合并配置合并金额
                Optional<BillingCard> byPlaceIdAndIdNumberAndNotDeleted = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
                return new CardInfo(byPlaceIdAndIdNumberAndNotDeleted.get().toBO());
            }
        }


        Optional<BillingCard> optionalBillingCard =
                billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, idNumber, 0);
        BillingCard billingCard = optionalBillingCard.orElse(null);
        if (null == billingCard || billingCard.getDeleted() == 1) {
            PlaceProfileBO placeProfileBO = getPlaceProfileBO(placeId);
            if (placeProfileBO.getType() == 1) {
                // 酒店静默生成会员卡
                BillingCardBO billingCardBO = billingCardService.commonCreateCard(placeId,"","1001", idNumber, idName, "", "", "", "", "",
                        "51","N","",0,0, SourceType.WECHAT,LocalDateTime.now().minusMinutes(30), PayType.WECHAT_PAY,0);
                billingCardBO.setActiveTime(null);
                miniAuthContextBO.setActiveState("0");
                CardInfo cardInfo = new CardInfo(billingCardBO);
                return cardInfo;
            }
            return null;
        } else {
            CardInfo cardInfo = new CardInfo(billingCard.toBO());
            return cardInfo;
        }
    }

    private PlaceProfileBO getPlaceProfileBO(String placeId) {
        GenericResponse<ObjDTO<PlaceProfileBO>> placeProfileResponse = placeProfileApi.findPlaceByPlaceId(placeId);

        if (ServiceCodes.NO_ERROR.getCode() != placeProfileResponse.getCode()) {
            log.warn("获取场所接口错误");
            throw new ServiceException(ServiceCodes.getByCode(placeProfileResponse.getCode()));
        }
        return placeProfileResponse.getData().getObj();
    }

    private void setPackageLogin(String areaId, MiniAuthContextBO miniAuthContextBO) {
        //默认为不可上机，当这个字段为true时，余额为0也可以上机
        miniAuthContextBO.setPackageLogin(false);
        miniAuthContextBO.setPackageLoginArea(false);

        if(!ObjectUtils.isEmpty(miniAuthContextBO.getCardInfo()) && !StringUtils.isEmpty(areaId)){
            //查询用户是否已经上机
            Optional<BillingOnline> billingOnlineOptional = billingOnlineService.findUnfinishedByPlaceIdAndCardId(miniAuthContextBO.getBaseInfo().getPlaceId(),
                    miniAuthContextBO.getCardInfo().getCardId());
            if (billingOnlineOptional.isPresent()) {
                miniAuthContextBO.setPackageLogin(true);
                miniAuthContextBO.setPackageLoginArea(true);
            }else{
                //查询是否已经预包时
                Optional<PackageTimeReserve> unusedByPlaceIdAndCardId = packageTimeReserveService.findUnusedByPlaceIdAndCardId(miniAuthContextBO.getBaseInfo().getPlaceId(),
                        miniAuthContextBO.getCardInfo().getCardId());
                log.info("小程序扫码进入用户是否包时:::{}",unusedByPlaceIdAndCardId.isPresent());
                if(unusedByPlaceIdAndCardId.isPresent()){
                    PackageTimeReserve packageTimeReserve = unusedByPlaceIdAndCardId.get();
                    if (packageTimeReserve.getAreaIds().contains(areaId)) {
                        // 2. 查询符合要求的包时信息
                        Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService
                                .findByPlaceIdAndRuleId(miniAuthContextBO.getBaseInfo().getPlaceId(), packageTimeReserve.getRuleId());
                        if (billingRulePackageTimeOpt.isPresent()) {
                            BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
                            int nowMinute = LocalDateTime.now().getMinute();
                            float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
                            float currHours = LocalDateTime.now().getHour() + minute;
                            if (billingRulePackageTime.getEndTime() - billingRulePackageTime.getStartTime() > 0) { // 同一天
                                if ((currHours < billingRulePackageTime.getStartTime() || currHours >= billingRulePackageTime.getEndTime()) && billingRulePackageTime.getPackageFlag() == 1) {
                                    return;
                                }
                            } else { // 跨0点包时
                                if ((currHours < billingRulePackageTime.getStartTime() && currHours >= billingRulePackageTime.getEndTime() && billingRulePackageTime.getPackageFlag() == 1)) {
                                    return;
                                }
                            }
                            miniAuthContextBO.setPackageLogin(true);
                            miniAuthContextBO.setPackageLoginArea(true);
                        }
                    }
                }

                //判断是否为包间二维码，如果是包间二维码允许调用 clientLogin接口内部逻辑处理
                GenericResponse<ObjDTO<PlaceAreaBO>> areaResult = placeServerService.findPlaceAreaByPlaceIdAndAreaId(miniAuthContextBO.getBaseInfo().getPlaceId(), areaId);
                if(areaResult.isResult() && !ObjectUtils.isEmpty(areaResult.getData())
                        && !ObjectUtils.isEmpty(areaResult.getData().getObj()) && areaResult.getData().getObj().getIsRoom() == 1){
                    miniAuthContextBO.setPackageLogin(true);
                    miniAuthContextBO.setPackageLoginArea(true);
                }

            }

        }
        //判断是否订座锁屏并且获取锁屏码
        if(!ObjectUtils.isEmpty(miniAuthContextBO.getCardInfo()) && !ObjectUtils.isEmpty(miniAuthContextBO.getClientInfo())){
            BookSeatsBO bookSeatsBO = bookSeatsService.find(miniAuthContextBO.getBaseInfo().getPlaceId(), miniAuthContextBO.getClientInfo().getClientId());
            if(!ObjectUtils.isEmpty(bookSeatsBO)){
                miniAuthContextBO.setUnlockCode(bookSeatsBO.getUnlockCode());
            }
        }

    }



    private void setNotRuleLogin(String idNumber, MiniAuthContextBO miniAuthContextBO,String areaId) {
        //默认为可上机
        miniAuthContextBO.setNotRuleLogin(true);
        //查询是否有费率(有会员卡并且非收银台二维码的时候才执行)
        if((!ObjectUtils.isEmpty(miniAuthContextBO.getCardInfo()) && !StringUtils.isEmpty(areaId)) && miniAuthContextBO.getBaseInfo().getType() != 1){
            Optional<BillingRuleCommon> billingRuleCommon = billingRuleCommonService.billingRuleCommons(miniAuthContextBO.getBaseInfo().getPlaceId(), areaId, miniAuthContextBO.getCardInfo().getCardTypeId());
            if (!billingRuleCommon.isPresent()) {
                log.info("context计费规则未找到***-->areaId：" + areaId + "cardTypeId---->>>>" + miniAuthContextBO.getCardInfo().getCardTypeId()+"idNumber---->>>>" + idNumber);
                miniAuthContextBO.setNotRuleLogin(false);
            }else{
                BillingRuleCommon commonRule = billingRuleCommon.get();
                if (CommonRuleAlgorithm.getPrice(commonRule.getPrices()) < 0) {
                    log.info("context计费规则不可用***-->areaId：" + areaId + "cardTypeId---->>>>" + miniAuthContextBO.getCardInfo().getCardTypeId()+"idNumber---->>>>" + idNumber);
                    miniAuthContextBO.setNotRuleLogin(false);
                }
            }
        }

    }
}
