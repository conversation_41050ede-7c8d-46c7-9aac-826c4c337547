package com.rzx.dim4.billing.service;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.billing.entity.BaseEntity;
import com.rzx.dim4.billing.entity.PlaceChainStores;
import com.rzx.dim4.billing.repository.PlaceChainStoresRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlaceChainStoresServiceImpl implements PlaceChainStoresService {

    @Autowired
    PlaceChainStoresRepository placeChainStoresRepository;

    @Override
    public void save(PlaceChainStoresBO placeChainStoresBO) {
        log.info("新增连锁门店：{}", new Gson().toJson(placeChainStoresBO));

        PlaceChainStores placeChainStores;

        Optional<PlaceChainStores> placeChainStoresOptional
                = placeChainStoresRepository.findByPlaceId(placeChainStoresBO.getPlaceId());
        if (placeChainStoresOptional.isPresent()) {
            placeChainStores = placeChainStoresOptional.get();
            if (placeChainStores.getDeleted() == 1) {
                placeChainStores.setDeleted(0);
            }
            placeChainStores.setChainId(placeChainStoresBO.getChainId());
            placeChainStores.setPlaceName(placeChainStoresBO.getPlaceName());
            placeChainStores.setSharePresentAccount(placeChainStoresBO.getSharePresentAccount());
            placeChainStores.setShareMemberPoint(placeChainStoresBO.getShareMemberPoint());
            placeChainStores.setUpdated(LocalDateTime.now());
        } else {
            placeChainStores = new PlaceChainStores();
            BeanUtils.copyProperties(placeChainStoresBO, placeChainStores);
            if (placeChainStores.getCreated() == null) {
                placeChainStores.setCreated(LocalDateTime.now());
                placeChainStores.setCreater(-1L);
            }
        }

        placeChainStoresRepository.save(placeChainStores);
    }

    @Override
    public void save(List<PlaceChainStoresBO> placeChainStoresBOS) {
        if (!CollectionUtils.isEmpty(placeChainStoresBOS)) {
            List<PlaceChainStores> placeChainStoresList = placeChainStoresBOS.stream().map(placeChainStoresBO -> {
                PlaceChainStores placeChainStores = new PlaceChainStores();

                Optional<PlaceChainStores> placeChainStoresOptional
                        = placeChainStoresRepository.findByPlaceId(placeChainStoresBO.getPlaceId());
                if (placeChainStoresOptional.isPresent()) {
                    placeChainStores = placeChainStoresOptional.get();

                    placeChainStores.setChainId(placeChainStoresBO.getChainId());
                    placeChainStores.setPlaceName(placeChainStoresBO.getPlaceName());

                    placeChainStores.setSharePresentAccount(placeChainStoresBO.getSharePresentAccount());
                    placeChainStores.setShareMemberPoint(placeChainStoresBO.getShareMemberPoint());

                    placeChainStores.setDeleted(0);
                    placeChainStores.setUpdated(LocalDateTime.now());
                } else {
                    BeanUtils.copyProperties(placeChainStoresBO, placeChainStores);
                    if (placeChainStores.getCreated() == null) {
                        placeChainStores.setCreated(LocalDateTime.now());
                        placeChainStores.setCreater(-1L);
                    }
                }
                return placeChainStores;
            }).collect(Collectors.toList());
            placeChainStoresRepository.saveAll(placeChainStoresList);
        }
    }

    @Override
    public PlaceChainStores save(PlaceChainStores placeChainStores) {
        return placeChainStoresRepository.save(placeChainStores);
    }

    @Override
    public Optional<PlaceChainStores> findByPlaceId(String placeId) {
        return placeChainStoresRepository.findByPlaceIdAndDeleted(placeId, 0);
    }

    @Override
    public List<PlaceChainStores> findByChainId(String chainId) {
        return placeChainStoresRepository.findByChainIdAndDeleted(chainId, 0);
    }

    @Override
    public PlaceChainStores findByChainIdAndPlaceId(String chainId, String placeId) {
        return placeChainStoresRepository.findByChainIdAndPlaceIdAndDeleted(chainId, placeId, BaseEntity.NO);
    }

    /**
     * 根据场所Id查询同一个连锁下的其他场所Id
     *
     * @param placeId
     * @return 同一个连锁下其他场所ID
     */
    @Override
    public List<PlaceChainStores> findPlaceChainStoresListInSamePlaceChainByPlaceId(String placeId) {
        // 查询网吧所在连锁
        Optional<PlaceChainStores> optPlaceChainStores = placeChainStoresRepository.findByPlaceIdAndDeleted(placeId, 0);
        return optPlaceChainStores.map(placeChainStores -> placeChainStoresRepository.findByChainIdAndDeleted(placeChainStores.getChainId(), 0)).orElse(null);
        // 查询该连锁下的所有网吧ID
    }

    @Override
    public int openChainAllPlaceShareMemberPoint(String chainId) {
        return placeChainStoresRepository.openChainAllPlaceShareMemberPoint(chainId);
    }

    @Override
    public List<String> findPlaceIdsInSamePlaceChainByPlaceId(String placeId) {
        List<PlaceChainStores> placeChainStoresList = findPlaceChainStoresListInSamePlaceChainByPlaceId(placeId);
        if (!CollectionUtils.isEmpty(placeChainStoresList)) {
            return placeChainStoresList.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public void exit(PlaceChainStoresBO placeChainStoresBO) {
        if (placeChainStoresBO == null || StringUtils.isEmpty(placeChainStoresBO.getPlaceId())) {
            log.warn("场所退出连锁时，参数为空");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        Optional<PlaceChainStores> placeChainStoresOptional
                = placeChainStoresRepository.findByPlaceIdAndDeleted(placeChainStoresBO.getPlaceId(), 0);

        PlaceChainStores placeChainStores = placeChainStoresOptional.orElseThrow(() -> {
            log.warn("场所退出连锁时，场所不存在");
            return new ServiceException(ServiceCodes.PLACE_PLACE_PROFILE_NOT_FOUND);
        });

        placeChainStores.setDeleted(1);
        placeChainStores.setUpdated(LocalDateTime.now());
        placeChainStoresRepository.save(placeChainStores);
    }

    @Override
    public List<PlaceChainStores> findAllPlaceChain (int deleted) {
        return placeChainStoresRepository.findByDeleted(deleted);
    }

}
