package com.rzx.dim4.billing.service.third;

import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.billing.entity.third.ThirdAccount;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/7/20
 **/
public interface MarketService {

    /**
     * （带钱）开卡
     *
     * @apiNote 开卡的时候，amount 为零，同时卡类型没有金额限制，则直接开卡；
     * 如果 amount 不为零，则是先创建支付订单，支付完成后，查询订单状态，如果支付成功，则开卡；
     *
     * @param placeId
     * @param cardTypeId
     * @param idNumber
     * @param name
     * @param amount
     * @param payType
     * @param openId
     * @param identification
     * @param cashierId
     * @param address
     * @param issuingAuthority
     * @param nation
     * @param phoneNumber
     * @param validPeriod
     * @param thirdAccount
     * @return
     */
    PaymentResultBO createCard(String placeId, String cardTypeId, String idNumber, String name, String amount, String payType, String openId, String identification, String cashierId, String address, String issuingAuthority, String nation, String phoneNumber, String validPeriod, ThirdAccount thirdAccount, String payCode);
}
