//package com.rzx.dim4.billing.scheduler;
//
//import com.rzx.dim4.base.enums.billing.SourceType;
//import com.rzx.dim4.base.response.GenericResponse;
//import com.rzx.dim4.billing.bo.PlaceChainBillingCardCostDetail;
//import com.rzx.dim4.billing.cons.BillingConstants;
//import com.rzx.dim4.billing.entity.*;
//import com.rzx.dim4.billing.service.*;
//import com.rzx.dim4.billing.service.algorithm.CommonRuleAlgorithm;
//import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
//import com.rzx.dim4.billing.service.impl.client.ClientLogoutServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.format.DateTimeFormatter;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//import java.util.concurrent.TimeUnit;
//
//import static com.rzx.dim4.billing.cons.BillingConstants.BILLING_SIMULTANEOUSLY_LOCK;
//import static com.rzx.dim4.billing.cons.BillingConstants.NEXT_TIME_PLUS;
//
///**
// * 修改任何一行代码前，请百分百确认该代码的作用和逻辑。 当前已经过测试，没有任何一行多余的代码！！！
// *
// * <AUTHOR>
// * @date Jul 9, 2020 4:50:30 PM
// */
//@Slf4j
//@Component
//public class BillingCoreScheduler {
//
//    @Autowired
//    BillingOnlineService billingOnlineService;
//
//    @Autowired
//    BillingCardService billingCardService;
//
//    @Autowired
//    ClientLogoutServiceImpl clientLogoutServiceImpl;
//
//    @Autowired
//    LogOperationService logOperationService;
//
//    @Autowired
//    LogLoginService logLoginService;
//
//    @Autowired
//    PlaceBizConfigService placeBizConfigService;
//
//    @Autowired
//    LogHbService logHbService;
//
//    @Autowired
//    LogShiftService logShiftService;
//
//    @Autowired
//    StringRedisTemplate stringRedisTemplate;
//
//    @Autowired
//    BillingRuleCommonService billingRuleCommonService;
//
//    @Autowired
//    BillingRuleAccService billingRuleAccService;
//
//    @Autowired
//    LogRoomService logRoomService;
//
//    @Autowired
//    LogAccService logAccService;
//
//    @Autowired
//    BillingCardDeductionService billingCardDeductionService;
//
//    @Autowired
//    PackageTimeReserveService packageTimeReserveService;
//
//    @Autowired
//    BillingRulePackageTimeService billingRulePackageTimeService;
//
//	@Autowired
//	LogRefundService logRefundService;
//
//	DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//    String serverBillingLockKey = "[billing]server_billing_lock";
//
//    /**
//     * 每5分钟查询是否有正在计费但是失去心跳的客户端
//     */
//    // @Scheduled(cron = "0 */5 * * * ?")
//    @Scheduled(initialDelay = 1000 * 30, fixedRate = 1000 * 60 * 5)
//    public void serverBilling() {
//
//        log.info("中心计费开始, 获取锁:::" + serverBillingLockKey);
//        boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(serverBillingLockKey, "locked");
//        if (locked) {
//            log.info("获取成功，设置5分钟TTL");
//            stringRedisTemplate.expire(serverBillingLockKey, 5, TimeUnit.MINUTES);
//        } else {
//            log.info("获取失败，查询锁状态");
//            long expired = stringRedisTemplate.getExpire(serverBillingLockKey, TimeUnit.SECONDS);
//            log.info("没有获得锁, 停止中心计费, TTL剩余:::" + expired);
//            if (expired == -1L) {
//                log.info("上次TTL设置失败，重新设置5分钟TTL");
//                stringRedisTemplate.expire(serverBillingLockKey, 5, TimeUnit.MINUTES);
//            }
//            log.info("中心计费结束！！！");
//            return;
//        }
//
//		// 丢失心跳的客户端
//		List<LogHb> lostClients = billingOnlineService.findLostClients();
//		if (CollectionUtils.isEmpty(lostClients)) {
//			log.info("中心计费查询结果:::中心没有找到失联的客户端");
//			log.info("释放锁:::" + serverBillingLockKey);
//			stringRedisTemplate.delete(serverBillingLockKey);
//			return; // 没有失联的客户端
//		}
//
//		List<BillingOnline> lostClientBillingOnlineList = new ArrayList<>(); // 丢失心跳未触发结账配置的客户端
//		List<BillingOnline> logoutBillingOnlineList = new ArrayList<>(); // 结账客户端
//		for (LogHb logHb : lostClients) {
//			PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(logHb.getPlaceId());
//			if (placeBizConfig.getBillingType() == 0) { // 非计费场所延迟30分钟
//				if (logHb.getHbTime().plusMinutes(BillingConstants.HOTEL_HB_TIMEOUT).isAfter(LocalDateTime.now())) {
//					continue;
//				}
//			}
//			Optional<BillingOnline> optLostBillingOnline = billingOnlineService.findByPlaceIdAndClientId(logHb.getPlaceId(), logHb.getClientId());
//			if (optLostBillingOnline.isPresent()) {
//				BillingOnline billingOnline = optLostBillingOnline.get();
//				int lostClientLogout = placeBizConfig.getLostClientLogout();
//				if (lostClientLogout > 0 && logHb.getHbTime().plusMinutes(lostClientLogout).isBefore(LocalDateTime.now())
//						&& billingOnline.getPackageFlag() == 0
//						&& billingOnline.getCreated().plusMinutes(lostClientLogout).isBefore(LocalDateTime.now())) {
//					// 开启丢失心跳结账配置,只针对标准计费
//					logoutBillingOnlineList.add(billingOnline);
//					continue;
//				}
//				lostClientBillingOnlineList.add(billingOnline);
//			}
//		}
//		log.info("中心找到失联触发结账的客户端:::" + logoutBillingOnlineList.size());
//		for (BillingOnline logoutBillingOnline : logoutBillingOnlineList) {
//			String placeId = logoutBillingOnline.getPlaceId();
//			String clientId = logoutBillingOnline.getClientId();
//			String cardId = logoutBillingOnline.getCardId();
//			log.info("{}-{}-{} 失联触发结账开始处理......", placeId, clientId, cardId);
//
//			// 获取计费卡
//			Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
//			if (!optBillingCard.isPresent()) {
//				log.info("中心定时器处理时未找到会员卡信息::::::placeId:::::cardId:::::" + placeId+"::::::"+cardId);
//				continue;
//			}
//			BillingCard billingCard = optBillingCard.get();
//
//			// 获取登录信息
//			Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndClientIdAndCardId(placeId,
//					clientId, cardId);
//			if (!optLogLogin.isPresent()) {
//				continue;
//			}
//			LogLogin logLogin = optLogLogin.get();
//
//			try {
//				// 查询是否是副卡上机
//				Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId,cardId);
//				// 有包间记录
//				if (logRoomOpt.isPresent()) {
//					LogRoom logRoom = logRoomOpt.get();
//					// 包间上机
//					logoutBillingOnline.setRemark("包间关机后自动结账下机");
//					logRoom.setFinishedTime(LocalDateTime.now());
//					logRoom.setFinished(1);
//					logRoomService.save(logRoom);
//				}
//				logoutBillingOnline.setFinished(1);
//				logoutBillingOnline.setUpdated(LocalDateTime.now());
//				billingOnlineService.save(logoutBillingOnline);
//				logLogin = logLoginService.doLogout(placeId, clientId, "-1", "关机后自动结账",
//						logoutBillingOnline.getDeduction(), logLogin.getLoginTime(),
//						billingCard.getTotalAccount());// 更新LogLogin
//				logOperationService.addLogoutLogOperation(SourceType.SYSTEM, billingCard,
//						logoutBillingOnline, null, logLogin);// 添加消费记录
//				logHbService.stopBilling(placeId, clientId);// 更新心跳
////				if ("1000".equals(billingCard.getCardTypeId())) {
////					log.info("{}-{}-{} 临时卡，相关记录delete设置为1", placeId, clientId, cardId);
////					// billingCard.setDeleted(1);
////					logoutBillingOnline.setDeleted(1);
////					// 写注销卡记录
////					LogShift logShift = logShiftService.getShiftId(placeId);
////					logOperationService.addCancellationLogOperation(SourceType.SYSTEM, billingCard, logShift);
////					logOperationService.addRefundOperation(SourceType.SYSTEM, RefundType.CANCELLATION, billingCard, billingCard.getCashAccount(), 0, logShift);
////					int refundAmount = billingCardService.billingCardCancellation(billingCard);
////					LogRefund logRefund = logRefundService.addLogRefund(placeId, billingCard, logShift, SourceType.SYSTEM, refundAmount);
////					logRefund.setRefundType(2);
////					logRefund.setRefundDesc("客户端关机后自动结账、临时卡销卡退款");
////					logRefundService.save(logRefund);
////				}
//				billingCard.setUpdated(LocalDateTime.now());
//				billingCard.setActiveTime(null); // 重置卡激活时间
//				billingCardService.save(billingCard);
//				log.info("{}-{}-{} 已完成关机后自动结账下机", placeId, clientId, cardId);
//			} catch (Exception e) {
//				e.printStackTrace();
//				log.info("{}-{}-{}关机后自动结账发生错误:::" + e.getMessage(), placeId, clientId, cardId);
//			}
//		}
//
//		log.info("中心找到失联的客户端:::" + lostClientBillingOnlineList.size());
//        for (BillingOnline lostClientBillingOnline : lostClientBillingOnlineList) { // 循环处理
//            String placeId = lostClientBillingOnline.getPlaceId();
//            String clientId = lostClientBillingOnline.getClientId();
//            String areaId = lostClientBillingOnline.getAreaId();
//            String cardId = lostClientBillingOnline.getCardId();
//            String cardTypeId = lostClientBillingOnline.getCardTypeId();
//            LocalDateTime nextTime = lostClientBillingOnline.getNextTime();
//            LocalDateTime nowTime = LocalDateTime.now(); // 定义
//
//            log.info("{}-{}-{} 开始处理......", placeId, clientId, cardId);
//			// 查询是否有simultaneouslyLock
//			String simultaneouslyLock = BILLING_SIMULTANEOUSLY_LOCK + "_" + placeId + "_" + cardId;
//			if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(simultaneouslyLock))) {
//				log.info("{}-{}-{} 定时器处理出现并发操作本次处理跳过......", placeId, clientId, cardId);
//				continue;
//			}
//
//            PlaceBizConfig placeBizConfig = placeBizConfigService.findByPlaceId(placeId);
////			boolean isHotel = "2000".equals(lostClientBillingOnline.getPlaceId().substring(6, 10));
//            boolean noBilling = placeBizConfig.getBillingType() == 0;
//
//            // 是否是包间主卡
//            boolean isMaster = true;
//
//            if (noBilling) {
//                log.info("{}-{}-{}【酒店】处理逻辑......", placeId, clientId, cardId);
//                List<String> loginOutParamList = new ArrayList<>();
//                loginOutParamList.add(lostClientBillingOnline.getPlaceId());
//                loginOutParamList.add(lostClientBillingOnline.getClientId());
//                loginOutParamList.add(lostClientBillingOnline.getCardId());
//                GenericResponse<?> repsLogout = clientLogoutServiceImpl.doService(loginOutParamList);
//                if (!repsLogout.isResult()) {
//                    log.error("{}-{}-{}【酒店】客户端自动结账失败，调用ClientLogout返回" + repsLogout.getCode() + "!!!", placeId,
//                            clientId, cardId);
//                    continue;
//                }
//                log.info("{}-{}-{}【酒店】处理逻辑结束.", placeId, clientId, cardId);
//            } else {
//                BillingCard billingCard = null;
//                log.info("{}-{}-{}【网吧】处理逻辑开始......", placeId, clientId, cardId);
//                // 获取登录信息
//                Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndClientIdAndCardId(placeId,
//                        clientId, cardId);
//                if (!optLogLogin.isPresent()) {
//                    continue;
//                }
//                LogLogin logLogin = optLogLogin.get();
//
//                Optional<BillingRuleCommon> optNonPackageRule = billingRuleCommonService
//                        .billingRuleCommons(placeId, areaId, cardTypeId);
//                if (!optNonPackageRule.isPresent()) {
//                    continue;
//                }
//                try {
//                    // 1、next_time增加 NEXT_TIME_PLUS 等于或大于
//                    // 当前时间，即当前时间不大于中心计费扣费时间点（+NEXT_TIME_PLUS），则不做任何操作加一次 next_time
//                    // 时间（NEXT_TIME_PLUS），是因为刚上机的时候立马就扣了一次心跳的钱，这个正好补偿（心跳计费同样，最后余额为零时，还有最后一次
//                    // next_time 可以上机）
//                    // 2、如果 next_time 增加 NEXT_TIME_PLUS 一直小于 当前时间，则继续扣费
//                    int count = 0; // 一个证件号循环处理的次数
//                    while (nextTime.isBefore(nowTime)) {
//
//                        if (count >= 15) {
//                            log.info("{}-{}-{}-{}定时器循环处理超过15次，退出本次循环......", placeId, clientId, cardId, cardTypeId);
//                            break;
//                        }
//                        count++;
//
//                        // 获取计费卡
//                        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(placeId, cardId);
//                        if (!optBillingCard.isPresent()) {
//                            log.info("中心定时器处理时未找到会员卡信息::::::placeId:::::cardId:::::" + placeId + "::::::" + cardId);
//                            break;
//                        }
//                        billingCard = optBillingCard.get();
//                        log.info(
//                                "{}-{}-{} 当前时间(" + fmt.format(nowTime) + ") > 扣费时间("
//                                        + fmt.format(nextTime.plusMinutes(NEXT_TIME_PLUS)) + ") ==>> 进行自动扣费",
//                                placeId, clientId, cardId);
//                        lostClientBillingOnline.setTimerFlag(1);
//                        lostClientBillingOnline.setUpdated(nowTime);
//
//                        // 查询是否是副卡上机
//                        Optional<LogRoom> logRoomOpt = logRoomService.findByPlaceIdAndCardIdAndFinished(placeId, cardId);
//                        if (logRoomOpt.isPresent() && logRoomOpt.get().getIsMaster() == 0) {
//                            isMaster = false;
//                        }
//
//                        // 处理工作卡 type==6为大巴掌
//                        if ("1002".equals(cardTypeId) || placeBizConfig.getType() == 6 || !isMaster) {
//                            log.info("{}-{}-{}-{}【网吧】处理工作卡、包间副卡逻辑开始......", placeId, clientId, cardId, cardTypeId);
//                            lostClientBillingOnline.setNextTime(nextTime.plusMinutes(NEXT_TIME_PLUS));
//                            billingOnlineService.save(lostClientBillingOnline);
//                            nextTime = lostClientBillingOnline.getNextTime(); // 非常重要，千万误删
//                            continue;
//                        }
//
//                        BillingRuleCommon billingRuleCommon = optNonPackageRule.get(); // 查询当前区域的普通计费
//                        int unitConsume = billingRuleCommon.getUnitConsume(); // 获取单位扣费
//                        int price = CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices());
//                        if (price == 0 || unitConsume == 0) { //
//                            log.info("{}-{}-{}-{}【网吧】计费0元计费/单位扣费0元关机不下线......", placeId, clientId, cardId, cardTypeId);
//                            lostClientBillingOnline.setNextTime(nextTime.plusMinutes(NEXT_TIME_PLUS));
//                            billingOnlineService.save(lostClientBillingOnline);
//                            nextTime = lostClientBillingOnline.getNextTime(); // 非常重要，千万误删
//                            continue;
//                        }
//						lostClientBillingOnline.setCommonPrice(price);
//
//						// 是否需要转包时
//						Optional<PackageTimeReserve> cashierTempPackageTimeOpt = packageTimeReserveService
//								.findUnusedByPlaceIdAndCardId(lostClientBillingOnline.getPlaceId(), lostClientBillingOnline.getCardId());
//						if (lostClientBillingOnline.getPackageFlag() > 0) {
//							Optional<BillingRulePackageTime> optBillingRulePackageTime = billingRulePackageTimeService
//									.findByPlaceIdAndRuleId(placeId, lostClientBillingOnline.getRuleId());
//							if (!optBillingRulePackageTime.isPresent()) {
//								log.info("{}-{}-{} 当前包时转换计费规则失败......未找到当前包时规则信息.....退出中心扣费", placeId, clientId, cardId);
//								break;
//							}
//							BillingRulePackageTime oldPackageTime = optBillingRulePackageTime.get();
//
//							if (cashierTempPackageTimeOpt.isPresent()) {
//								PackageTimeReserve cashierTempPackageTime = cashierTempPackageTimeOpt.get();
//								if (cashierTempPackageTime.getAreaIds().contains(lostClientBillingOnline.getAreaId())) {
//									log.info("{}-{}-{} 当前包时转换包时规则准备计算nextTime......", placeId, clientId, cardId);
//									Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService
//											.findByPlaceIdAndRuleId(lostClientBillingOnline.getPlaceId(), cashierTempPackageTime.getRuleId());
//									if (billingRulePackageTimeOpt.isPresent()) {
//										if ((nextTime.isAfter(cashierTempPackageTime.getStartTime()) || nextTime.equals(cashierTempPackageTime.getStartTime()))
//												&& nextTime.isBefore(cashierTempPackageTime.getEndTime())) {
//											BillingRulePackageTime billingRulePackageTime =  billingRulePackageTimeOpt.get();
//											// 转包时
//											if (billingRulePackageTime.getPackageFlag() == 2) {
//												// 包时长不限制结束时间
//												LocalDateTime futureNextTime = PackageTimeAlgorithm.getNextTime(nextTime,billingRulePackageTime);
//												LocalDateTime endTime = PackageTimeAlgorithm.getDateTime(billingRulePackageTime);
//												if (billingRulePackageTime.getLimitEndTime() == 1) {
//													if (futureNextTime.isBefore(endTime)) {
//														nextTime = futureNextTime;
//													} else {
//														nextTime = cashierTempPackageTime.getEndTime();
//													}
//												} else {
//													nextTime = futureNextTime;
//												}
//											} else {
//												nextTime = cashierTempPackageTime.getEndTime();
//											}
//
//											log.info("{}-{}-{} 原包时转为新包时计费......", placeId, clientId, cardId);
//											// 使用完的包时规则状态变为2
//											packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, lostClientBillingOnline.getCardId(), lostClientBillingOnline.getRuleId());
//											lostClientBillingOnline.setRuleId(billingRulePackageTime.getRuleId());
//											lostClientBillingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
//											lostClientBillingOnline.setCommonPrice(billingRulePackageTime.getPrice());
//											lostClientBillingOnline.setPackagePayFlag(cashierTempPackageTime.getPackagePayFlag());
//											lostClientBillingOnline.setNextTime(nextTime);
//											lostClientBillingOnline.setDeduction(lostClientBillingOnline.getDeduction() + billingRulePackageTime.getPrice());
//											billingOnlineService.save(lostClientBillingOnline);
//											logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM,3, 0,
//													0, billingCard, lostClientBillingOnline, oldPackageTime, billingRulePackageTime, logShiftService.getShiftId(billingCard.getPlaceId()), logLogin);
//
//											cashierTempPackageTime.setUpdated(nowTime);
//											cashierTempPackageTime.setStatus(1);
//											packageTimeReserveService.save(cashierTempPackageTime);
//											log.info("{}-{}-{} 包时转换完成", placeId, clientId, cardId);
//											continue;
//										}
//									}
//								}
//							}
//							// 3、如果之前的计费规则是包时，说明包时时间到，转成普通计费，查询当前网吧、区域和卡类型的普通计费规则
//							log.info("{}-{}-{} 包时结束，转为标准计费......", placeId, clientId, cardId);
//							// 使用完的包时规则状态变为2
//							packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(placeId, lostClientBillingOnline.getCardId(), lostClientBillingOnline.getRuleId());
//
//							// 如果场所配置了包时结束后结账，这里就调用结账(只对包时段有效)
//							if (placeBizConfig.getPackageEndLogoutFlag() == 1 && lostClientBillingOnline.getPackageFlag() > 0) {
//								log.info("{}-{}-{} 包时段结束触发结账设置.......执行结账", placeId, clientId, cardId);
//								// 有包间记录
//								if (logRoomOpt.isPresent() && logRoomOpt.get().getIsMaster() == 1) {
//									LogRoom logRoom = logRoomOpt.get();
//									// 包间上机
//									lostClientBillingOnline.setRemark("包间主卡结账下机");
//									logRoom.setFinishedTime(LocalDateTime.now());
//									logRoom.setFinished(1);
//									logRoomService.save(logRoom);
//								}
//								lostClientBillingOnline.setFinished(1);
//								lostClientBillingOnline.setUpdated(LocalDateTime.now());
//								billingOnlineService.save(lostClientBillingOnline);
//								logLogin = logLoginService.doLogout(placeId, clientId, "-1", "包时结束结账",
//										lostClientBillingOnline.getDeduction(), logLogin.getLoginTime(),
//										billingCard.getTotalAccount());// 更新LogLogin
//								logOperationService.addLogoutLogOperation(SourceType.SYSTEM, billingCard,
//										lostClientBillingOnline, null, logLogin);// 添加消费记录
//								logHbService.stopBilling(placeId, clientId);// 更新心跳
////								if ("1000".equals(billingCard.getCardTypeId())) {
////									log.info("{}-{}-{} 临时卡，相关记录delete设置为1", placeId, clientId, cardId);
////									billingCard.setDeleted(1);
////									billingCard.setUpdated(nowTime);
////									lostClientBillingOnline.setDeleted(1);
////									// 写注销卡记录
////									LogShift logShift = logShiftService.getShiftId(placeId);
////									logOperationService.addCancellationLogOperation(SourceType.SYSTEM, billingCard, logShift);
////									logOperationService.addRefundOperation(SourceType.SYSTEM, RefundType.CANCELLATION, billingCard, billingCard.getCashAccount(), 0, logShift);
////									int refundAmount = billingCardService.billingCardCancellation(billingCard);
////									LogRefund logRefund = logRefundService.addLogRefund(placeId, billingCard, logShift, SourceType.SYSTEM, refundAmount);
////									logRefund.setRefundType(2);
////									logRefund.setRefundDesc("包时结束后自动结账、临时卡销卡退款");
////									logRefundService.save(logRefund);
////								} else {
////									billingCard.setActiveTime(null); // 重置卡激活时间
////									billingCardService.save(billingCard);
////								}
//								billingCard.setActiveTime(null); // 重置卡激活时间
//								billingCardService.save(billingCard);
//								log.info("{}-{}-{} 已完成结账下机", placeId, clientId, cardId);
//								break; // 退出循环
//							}
//
//							lostClientBillingOnline.setRuleId(billingRuleCommon.getRuleId());
//							lostClientBillingOnline.setPackageFlag(0);
//							lostClientBillingOnline.setPackagePayFlag(0);
//							billingOnlineService.save(lostClientBillingOnline);
////							logOperationService.addEndPackageTimeCardOperation(SourceType.SYSTEM, billingCard,
////									lostClientBillingOnline, billingRuleCommon, null, logLogin);
//
//                            logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM, 2, 0,
//                                    CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices()), billingCard, lostClientBillingOnline, oldPackageTime, null, logShiftService.getShiftId(billingCard.getPlaceId()), logLogin);
//
//                            log.info("{}-{}-{} 包时转普通计费完成", placeId, clientId, cardId);
//                            continue; // 结束本次，继续 while 的下次判断
//                        }
//
//						// 普通转包时
//						if (cashierTempPackageTimeOpt.isPresent()) {
//							PackageTimeReserve cashierTempPackageTime = cashierTempPackageTimeOpt.get();
//							if (cashierTempPackageTime.getAreaIds().contains(lostClientBillingOnline.getAreaId())) {
//								Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService
//										.findByPlaceIdAndRuleId(lostClientBillingOnline.getPlaceId(), cashierTempPackageTime.getRuleId());
//								if (billingRulePackageTimeOpt.isPresent()) {
//									if (nextTime.isAfter(cashierTempPackageTime.getStartTime())
//											&& nextTime.isBefore(cashierTempPackageTime.getEndTime())) {
//										BillingRulePackageTime billingRulePackageTime =  billingRulePackageTimeOpt.get();
//										// 转包时
//										if (billingRulePackageTime.getPackageFlag() == 2) {
//											// 包时长不限制结束时间
//											LocalDateTime futureNextTime = PackageTimeAlgorithm.getNextTime(nextTime,billingRulePackageTime);
//											LocalDateTime endTime = PackageTimeAlgorithm.getDateTime(billingRulePackageTime);
//											if (billingRulePackageTime.getLimitEndTime() == 1) {
//												if (futureNextTime.isBefore(endTime)) {
//													nextTime = futureNextTime;
//												} else {
//													nextTime = cashierTempPackageTime.getEndTime();
//												}
//											} else {
//												nextTime = futureNextTime;
//											}
//										} else {
//											nextTime = cashierTempPackageTime.getEndTime();
//										}
//
//										log.info("{}-{}-{} 转为包时计费......", placeId, clientId, cardId);
//										lostClientBillingOnline.setRuleId(billingRulePackageTime.getRuleId());
//										lostClientBillingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
//										lostClientBillingOnline.setCommonPrice(billingRulePackageTime.getPrice());
//										lostClientBillingOnline.setPackagePayFlag(cashierTempPackageTime.getPackagePayFlag());
//										lostClientBillingOnline.setNextTime(nextTime);
//										lostClientBillingOnline.setDeduction(lostClientBillingOnline.getDeduction() + billingRulePackageTime.getPrice());
//										billingOnlineService.save(lostClientBillingOnline);
//
////										logOperationService.addBeginPackageTimeCardOperation(SourceType.SYSTEM, billingCard, lostClientBillingOnline,
////												billingRulePackageTime, null, logLogin); // 包时开始记录
//
//                                        logOperationService.addConvertBillingRuleOperation(SourceType.SYSTEM, 1, CommonRuleAlgorithm.getPrice(billingRuleCommon.getPrices()),
//                                                0, billingCard, lostClientBillingOnline, null, billingRulePackageTime, logShiftService.getShiftId(billingCard.getPlaceId()), logLogin);
//
//                                        cashierTempPackageTime.setUpdated(nowTime);
//                                        cashierTempPackageTime.setStatus(1);
//                                        packageTimeReserveService.save(cashierTempPackageTime);
//                                        log.info("{}-{}-{} 包时转换完成", placeId, clientId, cardId);
//                                        continue;
//                                    }
//                                }
//                            }
//                        }
//
//                        // 获取扣费信息
//                        List<PlaceChainBillingCardCostDetail> costDetails = billingCardDeductionService.getChainBillingCardCostDetails(billingCard, unitConsume, logLogin.getLoginId(), 1);
//                        if (costDetails == null || costDetails.isEmpty()) { // 4、余额为零，终止计费，结账下机
//                            log.info("{}-{}-{} 余额不足，结账登出", placeId, clientId, cardId);
//							// 有包间记录
//							if (logRoomOpt.isPresent() && logRoomOpt.get().getIsMaster() == 1) {
//								LogRoom logRoom = logRoomOpt.get();
//								// 包间上机
//								lostClientBillingOnline.setRemark("包间主卡结账下机");
//								logRoom.setFinishedTime(LocalDateTime.now());
//								logRoom.setFinished(1);
//								logRoomService.save(logRoom);
//							}
//							lostClientBillingOnline.setFinished(1);
//							lostClientBillingOnline.setUpdated(LocalDateTime.now());
//							billingOnlineService.save(lostClientBillingOnline);
//							logLogin = logLoginService.doLogout(placeId, clientId, "-1", "系统结账",
//									lostClientBillingOnline.getDeduction(), logLogin.getLoginTime(),
//									billingCard.getTotalAccount());// 更新LogLogin
//							logOperationService.addLogoutLogOperation(SourceType.SYSTEM, billingCard,
//									lostClientBillingOnline, null, logLogin);// 添加消费记录
//							logHbService.stopBilling(placeId, clientId);// 更新心跳
//							if ("1000".equals(billingCard.getCardTypeId())) {
//								log.info("{}-{}-{} 临时卡，相关记录delete设置为1", placeId, clientId, cardId);
//								billingCard.setDeleted(1);
//								billingCard.setUpdated(nowTime);
//								lostClientBillingOnline.setDeleted(1);
//								// 写注销卡记录
//								logOperationService.addCancellationLogOperation(SourceType.SYSTEM, billingCard, null);
//							}
//                            billingCard.setActiveTime(null); // 重置卡激活时间
//                            billingCardService.save(billingCard);
//                            log.info("{}-{}-{} 已完成结账下机", placeId, clientId, cardId);
//                            break; // 退出循环
//                        } else { // 5、余额不为零，正常扣费
//                            BillingCard card;
//                            if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
//                                // 查询最新的值
//                                Optional<BillingCard> newOptBillingCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
//                                if (!newOptBillingCard.isPresent()) {
//                                    log.info("{}-{}-{} 未找到连锁卡信息", placeId, clientId, cardId);
//                                    break;
//                                }
//                                card = newOptBillingCard.get();
//                            } else {
//                                card = billingCard;
//                            }
//                            log.info("{}-{}-{} 余额(" + card.getTotalAccount() + ")不为零，计算扣费金额......", placeId,
//                                    clientId, cardId);
//                            int actualFee = Math.min(card.getTotalAccount(), unitConsume); // 实际扣除的费用，正常情况下等于expectedFee
//                            BillingCard newBillingCard = billingCardService.billingCardDeduction(costDetails, billingCard.getPlaceId());
//
////							if (billingCard.getTotalAcount() < expectedFee) { // 不足一次扣费，账户清零，再上6分钟
////								actualFee = billingCard.getTotalAcount(); // 此时账户余额不足一次扣费，将扣费金额修改为实际扣费金额
////								//newBillingCard = billingCardService.billingCardDeduction(placeId, cardId, billingCard.getCashAccount(), billingCard.getPresentAccount());
////								newBillingCard = billingCardService.billingCardDeduction(Stream.of(new PlaceChainBillingCardCostDetail(placeId, cardId, billingCard.getCashAccount(), billingCard.getPresentAccount())).collect(Collectors.toList()), placeId, null, null);
////
////								//billingCard.setCashAccount(0);
////								//billingCard.setPresentAccount(0);
////							} else {
////								if (billingCard.getCashAccount() >= expectedFee) { // 现金账户大于扣费金额
//////									billingCard.setCashAccount(billingCard.getCashAccount() - expectedFee);
//////									newBillingCard = billingCardService.billingCardDeduction(placeId, cardId, expectedFee, 0);
////									newBillingCard = billingCardService.billingCardDeduction(Stream.of(new PlaceChainBillingCardCostDetail(placeId, cardId, expectedFee, 0)).collect(Collectors.toList()), placeId, null, null);
//////								} else { // 现金账户小于扣费金额
//////									billingCard.setPresentAccount(billingCard.getPresentAccount() - (expectedFee - billingCard.getCashAccount()));
//////									billingCard.setCashAccount(0);
//////									newBillingCard = billingCardService.billingCardDeduction(placeId, cardId, billingCard.getCashAccount(), (expectedFee - billingCard.getCashAccount()));
////									newBillingCard = billingCardService.billingCardDeduction(Stream.of(new PlaceChainBillingCardCostDetail(placeId, cardId, billingCard.getCashAccount(), (expectedFee - billingCard.getCashAccount()))).collect(Collectors.toList()),placeId, null, null);
////								}
////							}
//                            if (lostClientBillingOnline.getCommonPrice() == 0) {
//                                nextTime = nextTime.plusMinutes(NEXT_TIME_PLUS);
//                            } else {
//                                float extend = ((float) actualFee / (float) lostClientBillingOnline.getCommonPrice())
//                                        * 60;
//                                int minutes = (int) extend;
//                                float seconds = (extend - minutes) * 60;
//                                nextTime = nextTime.plusMinutes(minutes).plusSeconds((int) seconds);
//                            }
//                            lostClientBillingOnline.setDeduction(lostClientBillingOnline.getDeduction() + actualFee); // 累加扣费金额
//                            lostClientBillingOnline.setNextTime(nextTime);
//							// 处理累计包时业务
//							BillingRuleAcc billingRuleAcc = billingRuleAccService.findCurrBillingRuleAcc(placeId,
//									lostClientBillingOnline.getCardTypeId(), lostClientBillingOnline.getAreaId(), lostClientBillingOnline.getLoginId()); // 获得当前生肖的累计包时规则
//							if (billingRuleAcc == null && lostClientBillingOnline.getAccFlag() > 0) { // 累计包时结束，修改标识，不做任何处理
//								lostClientBillingOnline.setAccFlag(0);
//							} else if (lostClientBillingOnline.getAccFlag() == 1 && billingRuleAcc != null) { // 正在累计中
//								Optional<LogAcc> optLogAcc = logAccService.findByPlaceIdAndLoginId(placeId,
//										lostClientBillingOnline.getLoginId());
//								LogAcc logAcc = optLogAcc.isPresent() ? optLogAcc.get() : null;
//								if (logAcc.getCurrPrice() + actualFee >= logAcc.getAccPrice()) { // 达到扣费标准
//									actualFee = logAcc.getAccPrice() - logAcc.getCurrPrice(); // 补足累计金额即可
//									logAcc.setUpdated(nowTime);
//									logAcc.setFinishTime(nowTime);
//									logAcc.setCurrPrice(logAcc.getCurrPrice() + actualFee);
//									logAccService.save(logAcc);
//									// 计算此时nextTime
//									LocalDateTime accNextTime = LocalDateTime.of(LocalDate.now(), LocalTime
//											.of((int) logAcc.getEndTime(), (int) (logAcc.getEndTime() % 1 * 60)));
//									if ((logAcc.getEndTime() < logAcc.getStartTime()) && accNextTime.isBefore(nowTime)) {
//										nextTime = accNextTime.plusDays(1);
//									} else {
//										nextTime = accNextTime;
//									}
//									lostClientBillingOnline.setNextTime(nextTime);
//									lostClientBillingOnline.setAccFlag(2);
//									logOperationService.addAccOperation(SourceType.CLIENT, newBillingCard, null, logAcc);
//								} else { // 没达到扣费标准
//									logAcc.setUpdated(nowTime);
//									logAcc.setCurrPrice(logAcc.getCurrPrice() + actualFee);
//									logAccService.save(logAcc);
//								}
//							} else if (lostClientBillingOnline.getPackageFlag() == 0
//									&& lostClientBillingOnline.getAccFlag() == 0 && billingRuleAcc != null) { // 开始累计
//								Optional<LogAcc> optLogAcc = logAccService.findByPlaceIdAndLoginId(placeId, lostClientBillingOnline.getLoginId());
//                                if (optLogAcc.isPresent()) {
//                                        LogAcc logAcc = optLogAcc.get();
//                                        logAcc.setUpdated(nowTime);
//                                        logAcc.setCurrPrice(logAcc.getCurrPrice() + actualFee);
//                                        logAccService.save(logAcc);
//                                        lostClientBillingOnline.setAccFlag(1); // 设置online中的acc标识
//                                } else {
//                                        LogAcc logAcc = new LogAcc();
//                                        logAcc.setAccPrice(billingRuleAcc.getAccPrice());
//                                        logAcc.setAccRuleId(billingRuleAcc.getRuleId());
//                                        logAcc.setCreated(nowTime);
//                                        logAcc.setCurrPrice(actualFee);
//                                        logAcc.setEndTime(billingRuleAcc.getEndTime());
//                                        logAcc.setLoginId(lostClientBillingOnline.getLoginId());
//                                        logAcc.setPlaceId(placeId);
//                                        logAcc.setStartTime(billingRuleAcc.getStartTime());
//                                        logAccService.save(logAcc);
//                                        lostClientBillingOnline.setAccFlag(1); // 设置online中的acc标识
//                                }
//							}
//                            billingOnlineService.save(lostClientBillingOnline);
//                            //billingCardService.save(billingCard);
//
//                            nextTime = lostClientBillingOnline.getNextTime(); // 非常重要，千万误删
//                            log.info("{}-{}-{} 完成一次单位扣费:::" + actualFee + ", nextTime更新为:::" + fmt.format(nextTime),
//                                    placeId, clientId, cardId);
//                        }
//                    }
//                    log.info(
//                            "{}-{}-{} 当前时间(" + fmt.format(nowTime) + ") < 扣费时间("
//                                    + fmt.format(nextTime.plusMinutes(NEXT_TIME_PLUS)) + ") ==>> 未到扣费时间",
//                            placeId, clientId, cardId);
//                } catch (Exception e) {
//                    // 6、如果发生异常就终止计费
//                    e.printStackTrace();
//                    log.info("{}-{}-{}发生错误, 中止计费:::" + e.getMessage(), placeId, clientId, cardId);
//                    lostClientBillingOnline.setFinished(1);
//                    lostClientBillingOnline.setRemark("注意!!!异常退出");
//                    billingOnlineService.save(lostClientBillingOnline);
//                    logHbService.stopBilling(placeId, clientId);
//                    logLogin = logLoginService.doLogout(placeId, clientId, "-1", "系统结账",
//                            lostClientBillingOnline.getDeduction(), logLogin.getLoginTime(),
//                            billingCard == null ? 0 : billingCard.getTotalAccount());
//                    // 记录操作日志
//                    if (billingCard != null) {
//                        logOperationService.addLogoutLogOperation(SourceType.SYSTEM, billingCard, lostClientBillingOnline,
//                                null, logLogin);
//                    }
//                    break;
//                }
//                log.info("{}-{}-{}【网吧】处理逻辑结束.", placeId, clientId, cardId);
//            }
//            log.info("{}-{}-{} 处理结束.", placeId, clientId, cardId);
//        }
//        log.info("中心计费结束！！！释放锁:::" + serverBillingLockKey);
//        stringRedisTemplate.delete(serverBillingLockKey);
//    }
//}