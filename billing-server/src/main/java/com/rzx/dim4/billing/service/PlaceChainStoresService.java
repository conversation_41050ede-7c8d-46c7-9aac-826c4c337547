package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceChainStoresBO;
import com.rzx.dim4.billing.entity.PlaceChainStores;

import java.util.List;
import java.util.Optional;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/6/15
 **/
public interface PlaceChainStoresService {
    void exit(PlaceChainStoresBO placeChainStoresBO);

    void save(PlaceChainStoresBO placeChainStoresBO);

    void save(List<PlaceChainStoresBO> placeChainStoresBOS);

    Optional<PlaceChainStores> findByPlaceId(String placeId);

    PlaceChainStores save(PlaceChainStores placeChainStores);

    PlaceChainStores findByChainIdAndPlaceId(String chainId, String placeId);

    List<String> findPlaceIdsInSamePlaceChainByPlaceId(String placeId);

    List<PlaceChainStores> findByChainId(String chainId);

    /**
     * 根据场所Id查询同一个连锁下的其他场所Id
     *
     * @param placeId
     * @return 同一个连锁下其他场所ID
     */
    List<PlaceChainStores> findPlaceChainStoresListInSamePlaceChainByPlaceId(String placeId);

    public int openChainAllPlaceShareMemberPoint(String chainId);

    List<PlaceChainStores> findAllPlaceChain (int deleted);
}
