package com.rzx.dim4.billing.service.algorithm;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.stereotype.Service;

import java.net.Inet4Address;
import java.net.UnknownHostException;

/**
 * logOperation主键id业务公共方法
 */
@Service
@Slf4j
public class KeyAlgorithm {
    // 起始的时间戳（自定义，例如系统上线时间）
//    private final long twepoch = 1288834974657L;
    private final long twepoch = 1477929600000L;  //2016-11-01 00:00:00

    // 机器id所占的位数，不算数据中心id就是10L
    private final long workerIdBits = 5L;

    // 数据标识id所占的位数
    private final long datacenterIdBits = 5L;

    // 最大机器ID
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);

    // 最大数据标识ID
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

    // 序列在id中占的位数
    private final long sequenceBits = 12L;

    // 机器ID左移12位
    private final long workerIdShift = sequenceBits;

    // 数据标识id左移17位(12+5)
    private final long datacenterIdShift = sequenceBits + workerIdBits;

    // 时间截左移22位(5+5+12)
    private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;

    // 不加数据中心ID版本:时间截左移22位(10+12)
//    private final long timestampLeftShift = sequenceBits + workerIdBits;

    // 序列的掩码，这里为4095 (0b111111111111=4095)
    private final long sequenceMask = -1L ^ (-1L << sequenceBits);

    // 上次生成ID的时间截
    private long lastTimestamp = -1L;

    // 序列号
    private long sequence = 0L;

    // 工作机器ID
    private final long workerId;

    // 数据中心ID
    private final long datacenterId;

    public KeyAlgorithm() {
         long workerId = getWorkId();
         long datacenterId = getDataCenterId();
        log.info("自定义主键id算法初始化workId:::"+ workerId +  " nextId:::" + datacenterId);

        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(String.format("datacenter Id can't be greater than %d or less than 0", maxDatacenterId));
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *  如果一个线程反复获取Synchronized锁，那么synchronized锁将变成偏向锁。
     * @return 生成的ID
     */
    public synchronized long nextId() {
        // 获取当前时间的时间戳，单位（毫秒）
        long timestamp = timeGen();

        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退，抛出异常(当前时间戳不能小于上次时间戳，生成ID失败. 时间戳差值为)
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        // 如果时间戳相同，则序列号自增(获取当前时间戳如果等于上次时间戳（同一毫秒内），则在序列号加一；否则序列号赋值为0，从0开始)
        if (lastTimestamp == timestamp) {
            /** 逻辑：意思是说一个毫秒内最多只能有4096个数字，无论你传递多少进来，
                    这个位运算保证始终就是在4096这个范围内，避免你自己传递个sequence超过了4096这个范围 */
            // sequence：毫秒内序列(0~4095);  sequenceMask: 序列号最大值;
            sequence = (sequence + 1) & sequenceMask;
            /* 逻辑：当某一毫秒的时间，产生的id数 超过4095，系统会进入等待，直到下一毫秒，系统继续产生ID */
            // 序列号溢出，等待下一毫秒
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 时间戳改变，序列号重置为0
            sequence = 0L;
        }

        // 更新最后的时间戳(将上次时间戳值刷新（逻辑：记录一下最近一次生成id的时间戳，单位是毫秒）)
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成64位的ID
         /** 核心逻辑：生成一个64bit的id；
                  先将当前时间戳左移，放到41 bit那儿；
                  将机房id左移放到5 bit那儿；
                  将机器id左移放到5 bit那儿；
                  将序号放最后12 bit
                  最后拼接起来成一个64 bit的二进制数字，转换成10进制就是个long型 */
        /**
         * 返回结果：
         * (timestamp - twepoch) << timestampLeftShift) 表示将时间戳减去初始时间戳，再左移相应位数
         * (datacenterId << datacenterIdShift) 表示将数据id左移相应位数
         * (workerId << workerIdShift) 表示将工作id左移相应位数
         * | 是按位或运算符，例如：x | y，只有当x，y不为0的时候结果才为0，其它情况结果都为1。
         * 因为个部分只有相应位上的值有意义，其它位上都是0，所以将各部分的值进行 | 运算就能得到最终拼接好的id
         */
        return ((timestamp - twepoch) << timestampLeftShift) |
                (datacenterId << datacenterIdShift) |
                (workerId << workerIdShift) |
                sequence;
    }

    /**
     * 获取当前系统时间戳
     * @return 当前时间的时间戳 14位
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    // 等待下一个毫秒
    /**
     * 上次时间戳与当前时间戳进行比较
     * 逻辑：当某一毫秒的时间，产生的id数 超过4095，系统会进入等待，直到下一毫秒，系统继续产生ID
     * @param lastTimestamp 上次时间戳
     * @return 若当前时间戳小于等于上次时间戳（时间回拨了），则返回最新当前时间戳； 否则，返回当前时间戳
     */
    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }


    /**
     * workId使用IP生成
     * @return workId
     */
    private static Long getWorkId() {
        try {
            String hostAddress = Inet4Address.getLocalHost().getHostAddress();
            log.info("getWorkId:::hostAddress={}",hostAddress);
            int[] ints = StringUtils.toCodePoints(hostAddress);
            int sums = 0;
            for(int b : ints){
                sums += b;
            }
            return (long)(sums % 32);
        } catch (UnknownHostException e) {
            // 如果获取失败，则使用随机数备用
            log.info("getWorkId:::获取失败则采用随机数");
            return RandomUtils.nextLong(0,31);
        }
    }


    /**
     * dataCenterId使用hostName生成
     * @return dataCenterId
     */
    private static Long getDataCenterId() {
        try {
            String hostName = SystemUtils.getHostName();
            log.info("getDataCenterId:::hostAddress={}",hostName);
            int[] ints = StringUtils.toCodePoints(hostName);
            int sums = 0;
            for (int i: ints) {
                sums = sums + i;
            }
            return (long) (sums % 32);
        }
        catch (Exception e) {
            // 失败就随机
            log.info("getDataCenterId:::获取失败则采用随机数");
            return RandomUtils.nextLong(0, 31);
        }
    }



}
