package com.rzx.dim4.billing.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;

import com.rzx.dim4.billing.entity.LogReversal;
import org.springframework.data.jpa.repository.Query;

/**
 *
 * <AUTHOR>
 * @date 2022年4月13日 上午9:38:05
 */
public interface LogReversalRepository extends JpaRepository<LogReversal, Long> {

	List<LogReversal> findByPlaceIdAndTopupOrderId(String placeId, String topupOrderId);

	Page<LogReversal> findAll(Specification<LogReversal> specification, Pageable pageable);

	/**
	 * 按班次查询冲正-防止长时间未交班的数据 需要传一个默认的时间段
	 * @param placeId
	 * @param shiftId
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_reversal) from log_reversal lo where lo.place_id=?1 and lo.shift_id=?2  and lo.created >= ?3 and lo.created <= ?4" , nativeQuery = true)
	Integer sumCostTotalReversalByShiftId(String placeId, String shiftId, LocalDateTime startDateTime, LocalDateTime endDateTime);


	/**
	 * 按时间-总冲正
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_reversal) from log_reversal lo where lo.place_id in (?1) and lo.created >= ?2 and lo.created <= ?3" , nativeQuery = true)
	Integer sumCostTotalReversalByDateTime(List<String> placeIds, LocalDateTime startDateTime, LocalDateTime endDateTime);


	List<LogReversal> findByPlaceIdAndCreatedGreaterThanEqualAndCreatedLessThanEqual(String placeId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-总冲正
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_reversal) from log_reversal lo where lo.place_id=?1  and lo.card_id = ?2 and lo.created >= ?3 and lo.created <= ?4", nativeQuery = true)
	Integer sumCostTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);

	/**
	 * 按时间-卡-总冲正(本金+奖励)
	 *
	 * @param startDateTime
	 * @param endDateTime
	 * @return
	 */
	@Query(value = "select sum(lo.cash_reversal + lo.present_reversal) from log_reversal lo where lo.place_id=?1  and lo.card_id = ?2 and lo.created >= ?3 and lo.created <= ?4", nativeQuery = true)
	Integer sumTotalReversalByCardIdAndDateTime(String placeId, String cardId, LocalDateTime startDateTime, LocalDateTime endDateTime);
}
