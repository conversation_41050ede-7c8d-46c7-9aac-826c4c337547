package com.rzx.dim4.billing.service.impl.cashier;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.base.bo.billing.BillingCardBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.service.CoreService;

/**
 * 计费卡以老带新充值
 * 
 * <AUTHOR>
 * @date 2022年4月6日 上午10:43:15
 */
@Service
public class CashierTopupBillingCardOldWithNewServiceImpl implements CoreService {

	@Autowired
	CashierTopupBillingCardPresentServiceImpl cashierTopupBillingCardPresentServiceImpl;

	@Override
	public GenericResponse<ObjDTO<BillingCardBO>> doService(List<String> params) {

		// 检查参数
		if (params.size() != 5) {
			return new GenericResponse<>(ServiceCodes.BAD_PARAM);
		}

		return cashierTopupBillingCardPresentServiceImpl.doService(params);
	}

}
