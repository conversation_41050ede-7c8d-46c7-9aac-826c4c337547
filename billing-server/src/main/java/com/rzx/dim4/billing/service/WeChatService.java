package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.payment.PaymentResultBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.response.GenericResponse;

/**
 * <AUTHOR>
 * @date 2023年05月25日 11:30
 */
public interface WeChatService {

    GenericResponse<ObjDTO<PaymentResultBO>> createOrderPayByAppletOfWeChat(
            String requestTicket, String placeId, String idNumber, int amount, String openId, String appId);
}
