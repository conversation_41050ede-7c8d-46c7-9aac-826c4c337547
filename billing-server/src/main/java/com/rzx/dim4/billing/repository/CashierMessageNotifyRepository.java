package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.CashierMessageNotify;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface CashierMessageNotifyRepository extends JpaRepository<CashierMessageNotify, Long>, JpaSpecificationExecutor<CashierMessageNotify> {

    Optional<CashierMessageNotify> findByNotifyId (String notifyId);

    Page<CashierMessageNotify> findByOrderByIdDesc(Pageable pageable);

    Optional<CashierMessageNotify> findTop1ByOrderByIdDesc ();

    List<CashierMessageNotify> findByNotifyIdInOrderByIdDesc (List<String> notifyIds);

}
