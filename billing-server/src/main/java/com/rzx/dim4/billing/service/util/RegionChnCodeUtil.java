package com.rzx.dim4.billing.service.util;

import com.rzx.dim4.base.bo.notify.region.RegionCountryBO;
import com.rzx.dim4.base.bo.place.PlaceProfileBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.notify.RegionServerApi;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class RegionChnCodeUtil {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    RegionServerApi regionServerApi;

    @Autowired
    PlaceServerService placeServerService;

    /**
     * 根据场所地区编码获取对应拼音用于龙兜统计；
     * @param placeId
     * @return
     */
    public String getNamePinYin (String placeId) {

        GenericResponse<ObjDTO<PlaceProfileBO>> genericResponse = placeServerService.findByPlaceId(placeId);
        if(genericResponse.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return placeId;
        }
        String regionCode = genericResponse.getData().getObj().getRegionCode();

        String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
        GenericResponse<ObjDTO<RegionCountryBO>> countryRes = regionServerApi.regionCountry(requestTicket , regionCode);

        if(countryRes.getCode() != ServiceCodes.NO_ERROR.getCode()) {
            return regionCode;
        }

        RegionCountryBO countryBO = countryRes.getData().getObj();

        return countryBO.getNamePinyin();
    }

}
