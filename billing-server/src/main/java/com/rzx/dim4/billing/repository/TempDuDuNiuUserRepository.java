package com.rzx.dim4.billing.repository;

import com.rzx.dim4.billing.entity.TempDuDuNiuUser;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TempDuDuNiuUserRepository extends JpaRepository<TempDuDuNiuUser, Long> {

    Optional<TempDuDuNiuUser> findTop1ByPlaceIdAndDeletedOrderByIdDesc(String placeId,int deleted);

    List<TempDuDuNiuUser> findByPlaceIdAndCardIdShowAndNameShowAndDeleted(String placeId, String cardIdShow, String nameShow, int deleted);

    List<TempDuDuNiuUser> findByPlaceIdAndCardIdInAndDeleted(String placeId, List<String> cardIds, int deleted);

}
