package com.rzx.dim4.billing.service.third;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.third.ThirdPlaceConfig;
import com.rzx.dim4.billing.repository.third.ThirdPlaceConfigRepository;

@Service
public class ThirdPlaceConfigService {

	@Autowired
	ThirdPlaceConfigRepository thirdPlaceConfigRepository;

	public Optional<ThirdPlaceConfig> findByPlaceId(String placeId) {
		return thirdPlaceConfigRepository.findByPlaceId(placeId);
	}

}
