package com.rzx.dim4.billing.repository.third;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.rzx.dim4.billing.entity.third.ThirdPlaceConfig;

public interface ThirdPlaceConfigRepository
		extends JpaRepository<ThirdPlaceConfig, Long>, JpaSpecificationExecutor<ThirdPlaceConfig> {

	Optional<ThirdPlaceConfig> findByPlaceId(String placeId);

}
