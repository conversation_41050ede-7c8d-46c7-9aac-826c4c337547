package com.rzx.dim4.billing.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import com.rzx.dim4.billing.entity.CashierTask;

/**
 * 
 * <AUTHOR>
 * @date 2021年11月30日 下午3:51:46
 */
public interface CashierTaskRepository extends JpaRepository<CashierTask, Long> {

	Optional<CashierTask> findByPlaceIdAndTaskId(String placeId, String taskId);

	List<CashierTask> findByPlaceIdAndTaskStatus(String placeId, int taskStatus);

	Page<CashierTask> findByPlaceIdOrderByIdDesc(String placeId, Pageable pageable);

}
