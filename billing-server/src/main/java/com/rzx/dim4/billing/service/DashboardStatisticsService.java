package com.rzx.dim4.billing.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.rzx.dim4.base.bo.billing.StatisticsByDayBO;
import com.rzx.dim4.billing.repository.StatisticsByDayRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;


@Service
public class DashboardStatisticsService {

    @Autowired
    private StatisticsByDayRepository statisticsByDayRepository;

    /**
     * 查询首页统计数据（历史数据）
     * @param startDate
     * @param endDate
     * @param placeId
     * @return
     */
    public List<StatisticsByDayBO> queryDashboardStatistics(String startDate, String endDate, String placeId, Pageable pageable) {

        List<Map<String, String>> list = statisticsByDayRepository.queryDashboardStatistics(startDate, endDate, placeId, pageable);

        List<StatisticsByDayBO> bos = new ArrayList<>();
        for (Map<String, String> map : list) {
            StatisticsByDayBO bo = new StatisticsByDayBO();
            bo.setCountDay(map.get("countDay"));
            Object obj = map.get("sumCostOnlineIncome");
            bo.setSumCostOnlineIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCostOnlineOutcome");
            bo.setSumCostOnlineOutcome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCostCashIncome");
            bo.setSumCostCashIncome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCostCashOutcome");
            bo.setSumCostCashOutcome(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumOnlineVisits");
            bo.setSumOnlineVisits(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumConsumptionTotal");
            bo.setSumConsumptionTotal(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumCostTotalReversal");
            bo.setSumCostTotalReversal(obj == null ? 0 : (Integer) obj);
            obj = map.get("sumOnlineTime");
            bo.setSumOnlineTime(obj == null ? 0 : ((Integer) obj));
            obj = map.get("sumOnlineNum");
            bo.setSumOnlineNum(obj == null ? 0 : ((Integer) obj));
            bos.add(bo);
        }
        return bos;

    }

    public int countDashboardStatistics(String startDate, String endDate, String placeId) {
        return statisticsByDayRepository.countDashboardStatistics(startDate, endDate, placeId);
    }

    public List<Map<String, String>> querySumOnlineNum (String startDate, String endDate, List<String> placeIds) {
        return statisticsByDayRepository.querySumOnlineNum(startDate, endDate, placeIds);
    }

    public Map<String, String> queryTotalConsumptionByPlaceId(String startDate, String endDate, String placeId) {
        return statisticsByDayRepository.queryTotalConsumptionByPlaceId(startDate, endDate, placeId);
    }
    public List<String> queryPlaceIdsByCountDay(String startTime, String endTime){
        return  statisticsByDayRepository.queryPlaceIdsByCountDay(startTime,endTime);
    }

    public StatisticsByDayBO sumDashboardStatistics(String startDate, String endDate, List<String> placeIds) {

        Map<String, String> map = statisticsByDayRepository.sumDashboardStatistics(startDate, endDate, placeIds);

        StatisticsByDayBO bo = new StatisticsByDayBO();

        Object obj = map.get("sumCostOnlineIncome");
        bo.setSumCostOnlineIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCostOnlineOutcome");
        bo.setSumCostOnlineOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCostCashIncome");
        bo.setSumCostCashIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCostCashOutcome");
        bo.setSumCostCashOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOnlineVisits");
        bo.setSumOnlineVisits(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumConsumptionTotal");
        bo.setSumConsumptionTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumCostTotalReversal");
        bo.setSumCostTotalReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOnlineTime");
        bo.setSumOnlineTime(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        obj = map.get("sumOnlineNum");
        bo.setSumOnlineNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
        return bo;

    }

    /**
     * 查询首页统计数据（历史数据）（按月统计）
     * @param startDate
     * @param endDate
     * @param placeId
     * @return
     */
    public List<StatisticsByDayBO> queryMonthDashboardStatistics(String startDate, String endDate, String placeId, Pageable pageable) {

        List<Map<String, String>> list = statisticsByDayRepository.queryMonthDashboardStatistics(startDate, endDate, placeId, pageable);

        List<StatisticsByDayBO> bos = new ArrayList<>();
        for (Map<String, String> map : list) {
            StatisticsByDayBO bo = new StatisticsByDayBO();
            bo.setCountDay(map.get("countDay"));
            Object obj = map.get("sumCostOnlineIncome");
            bo.setSumCostOnlineIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCostOnlineOutcome");
            bo.setSumCostOnlineOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCostCashIncome");
            bo.setSumCostCashIncome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCostCashOutcome");
            bo.setSumCostCashOutcome(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumOnlineVisits");
            bo.setSumOnlineVisits(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumConsumptionTotal");
            bo.setSumConsumptionTotal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumCostTotalReversal");
            bo.setSumCostTotalReversal(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumOnlineTime");
            bo.setSumOnlineTime(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            obj = map.get("sumOnlineNum");
            bo.setSumOnlineNum(obj == null ? 0 : new BigDecimal(obj.toString()).intValue());
            bos.add(bo);
        }
        return bos;

    }

    public int countMonthDashboardStatistics(String startDate, String endDate, String placeId) {
        return statisticsByDayRepository.countMonthDashboardStatistics(startDate, endDate, placeId);
    }
}
