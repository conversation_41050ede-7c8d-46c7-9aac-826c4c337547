package com.rzx.dim4.billing.service;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingCardTypeBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.notify.polling.UpdateConfigBusinessBO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.notify.PollingType;
import com.rzx.dim4.base.exception.ServiceException;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsApi;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.entity.invite.InviteConfig;
import com.rzx.dim4.billing.repository.*;
import com.rzx.dim4.billing.service.Invite.InviteConfigService;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillingCardTypeService {

    @Autowired
    private BillingCardTypeRepository billingCardTypeRepository;

    @Autowired
    private BillingCardService billingCardService;

    @Autowired
    private PlaceChainStoresService placeChainStoresService;

    @Autowired
    private BillingCardRepository billingCardRepository;

    @Autowired
    private TopupRuleRepository topupRuleRepository;

    @Autowired
    private BillingRulePackageTimeRepository billingRulePackageTimeRepository;

    @Autowired
    private BillingRuleCommonRepository billingRuleCommonRepository;

    @Autowired
    private BillingRuleAccRepository billingRuleAccRepository;

    @Autowired
    private BillingOnlineService billingOnlineService;

    @Autowired
    private NotifyServerService notifyServerService;

    @Autowired
    private PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private MarketingGoodsApi goodsApi;

    @Autowired
    private InviteConfigService inviteConfigService;

    public BillingCardType save(BillingCardType billingCardType) {
        if (StringUtils.isEmpty(billingCardType.getCardTypeId())) {
            billingCardType.setCardTypeId(buildCardTypeId(billingCardType.getPlaceId()));
        }
        billingCardType.setCreated(LocalDateTime.now());
        billingCardType.setUpdated(LocalDateTime.now());
        billingCardTypeRepository.save(billingCardType);
        return billingCardType;
    }

    public List<BillingCardType> batchSave(List<BillingCardTypeBO> billingCardTypeBOS) {
        // 检验卡类型名称是否已经存在
        List<BillingCardType> billingCardTypes = new ArrayList<>();
        for (BillingCardTypeBO bo : billingCardTypeBOS) {
            Optional<BillingCardType> billingCardTypeOpt = billingCardTypeRepository.findByPlaceIdAndTypeNameAndDeleted(bo.getPlaceId(), bo.getTypeName(), 0);
            if (billingCardTypeOpt.isPresent()) {
                BillingCardType billingCardType = billingCardTypeOpt.get();
                billingCardTypes.add(billingCardType);
                continue;
            }
            BillingCardType billingCardType = new BillingCardType(bo);
            billingCardType.setCreated(LocalDateTime.now());
            billingCardType = save(billingCardType);
            billingCardTypes.add(billingCardType);
        }
        return billingCardTypes;
    }

    @Synchronized
    private String buildCardTypeId(String placeId) {
        int cardTypeId = 1002;
        Optional<BillingCardType> lastCardType = billingCardTypeRepository.findTop1ByPlaceIdOrderByCardTypeIdDesc(placeId);
        if (lastCardType.isPresent()) {
            cardTypeId = Integer.parseInt(lastCardType.get().getCardTypeId()) + 1;
        }
        return String.valueOf(cardTypeId);
    }

    public BillingCardType getBillingCardType(String placeId, String cardTypeId) {
        // 验证卡类型ID
        Optional<BillingCardType> optBillingCardType = billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
        if (!optBillingCardType.isPresent()) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }
        return optBillingCardType.get();
    }

    public Optional<BillingCardType> findByPlaceIdAndCardTypeId(String placeId, String cardTypeId) {
        return billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);
    }

    public List<BillingCardType> findByPlaceId(String placeId) {
        List<BillingCardType> list = billingCardTypeRepository.findByPlaceIdAndDeleted(placeId, 0);
        if (CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < 3; i++) {
                BillingCardType type = new BillingCardType();
                type.setPlaceId(placeId);
                type.setCardTypeId(String.valueOf(1000 + i));
                type.setTypeName(i == 0 ? "临时卡" : (i == 1 ? "普通会员" : "工作卡"));
                type.setLevel(i);
                type.setCreater(0L);
                type.setCreated(LocalDateTime.now());
                list.add(type);
            }
            billingCardTypeRepository.saveAll(list);
            return list;
        }
        return list;
    }

    public GenericResponse<SimpleDTO> delete(Long id) {
        Optional<BillingCardType> optBillingCardType = billingCardTypeRepository.findById(id);
        if (!optBillingCardType.isPresent()) {
            return new GenericResponse<>(ServiceCodes.BAD_ID);
        }

        BillingCardType billingCardType = optBillingCardType.get();
        String placeId = billingCardType.getPlaceId();
        Optional<PlaceChainStores> placeChainStoresOptional = placeChainStoresService.findByPlaceId(placeId);
        if (placeChainStoresOptional.isPresent()) {
            log.info("当前场所已加入连锁，场所无法独自新增卡类型");
            throw new ServiceException(ServiceCodes.NO_RIGHT);
        }

        billingCardTypeRepository.deleteById(id);
        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, "", "", BusinessType.UPDATECONFIG);
        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
                updateConfigBusinessBO.setPlaceId(placeId);
                updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
                updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                updateConfigBusinessBO.setType(1);
                // 保存收银台业务数据
                notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
            }
        }
        return new GenericResponse<>(ServiceCodes.NO_ERROR);
    }

    public BillingCardTypeBO create(BillingCardTypeBO bo) {

        Optional<PlaceChainStores> placeChainStoresOptional = placeChainStoresService.findByPlaceId(bo.getPlaceId());
        if (placeChainStoresOptional.isPresent()) {
            log.info("当前场所已加入连锁，场所无法独自新增卡类型");
            throw new ServiceException(ServiceCodes.NO_RIGHT);
        }

        // 校验卡类型名称，同一个网吧，同一种卡类型名称只能存在一个
        Optional<BillingCardType> billingCardTypeOpt = findByPlaceIdAndTypeName(bo.getPlaceId(), bo.getTypeName());
        if (billingCardTypeOpt.isPresent() && StringUtils.isEmpty(bo.getCardTypeId()) && !bo.getCardTypeId().equals(billingCardTypeOpt.get().getCardTypeId())) {
            throw new ServiceException(ServiceCodes.BILLING_CARD_TYPE_NAME_IS_EXSIT);
        }

        int minCreateCardAmount = bo.getMinCreateCardAmount();
//        if (minCreateCardAmount > 999) {
//            throw new ServiceException(ServiceCodes.BILLING_AMOUNT_ERROR);
//        }
        bo.setMinCreateCardAmount(bo.getMinCreateCardAmount() != 0 ? bo.getMinCreateCardAmount() * 100 : 0);
        // 新增
        if (StringUtils.isEmpty(bo.getCardTypeId())) {
            BillingCardType billingCardType = new BillingCardType(bo);
            this.save(billingCardType);
            GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(bo.getPlaceId(), "", "", BusinessType.UPDATECONFIG);
            if (pollingBOGeneric.isResult()) {
                PollingBO pollingBO = pollingBOGeneric.getData().getObj();
                if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                    UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
                    updateConfigBusinessBO.setPlaceId(bo.getPlaceId());
                    updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
                    updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                    updateConfigBusinessBO.setType(1);
                    // 保存收银台业务数据
                    notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
                }
            }
            return billingCardType.toBO();
        }

        // 编辑
        Optional<BillingCardType> findByPlaceIdAndCardTypeId = findByPlaceIdAndCardTypeId(bo.getPlaceId(), bo.getCardTypeId());
        if (!findByPlaceIdAndCardTypeId.isPresent()) {
            throw new ServiceException(ServiceCodes.NOT_FOUND);
        }
        BillingCardType billingCardType = findByPlaceIdAndCardTypeId.get();
        billingCardType.setTypeName(bo.getTypeName());
        billingCardType.setMinCreateCardAmount(bo.getMinCreateCardAmount());
        billingCardType.setMinPointsRequirement(bo.getMinPointsRequirement());
        billingCardType.setUpdated(LocalDateTime.now());
        this.save(billingCardType);

        // 更新billingCard中的卡类型名称
        List<BillingCard> byPlaceIdAndCardTypeId = billingCardService.findByPlaceIdAndCardTypeId(bo.getPlaceId(), bo.getCardTypeId());
        if(byPlaceIdAndCardTypeId.size() > 0){
            List<List<BillingCard>> lists = Lists.partition(byPlaceIdAndCardTypeId, 2000);
            for (List<BillingCard> list : lists) {
                List<String> cardIds = list.stream().map(BillingCard::getCardId).collect(Collectors.toList());
                billingCardService.updateCardTypeName(bo.getPlaceId(), bo.getCardTypeId(), bo.getTypeName(),cardIds);
            }
        }

        GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(bo.getPlaceId(), "", "", BusinessType.UPDATECONFIG);
        if (pollingBOGeneric.isResult()) {
            PollingBO pollingBO = pollingBOGeneric.getData().getObj();
            if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
                updateConfigBusinessBO.setPlaceId(bo.getPlaceId());
                updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
                updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                updateConfigBusinessBO.setType(1);
                // 保存收银台业务数据
                notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
            }
        }
        return billingCardType.toBO();
    }

    /**
     * 根据卡类型名称查询卡信息
     *
     * @param placeId
     * @param typeName
     * @return
     */
    public Optional<BillingCardType> findByPlaceIdAndTypeName(String placeId, String typeName) {
        return billingCardTypeRepository.findByPlaceIdAndTypeNameAndDeleted(placeId, typeName, 0);
    }

    public List<BillingCardType> findByPlaceIdAndCardTypeIdIn(String placeId, List<String> cardTypeIds) {
        return billingCardTypeRepository.findByPlaceIdAndDeletedAndCardTypeIdIn(placeId, 0, cardTypeIds);
    }

    public List<BillingCardType> findByPlaceIdInAndCardTypeIdIn(List<String> placeIds, List<String> cardTypeIds) {
        return billingCardTypeRepository.findByPlaceIdInAndCardTypeIdIn(placeIds, cardTypeIds);
    }

    public List<BillingCardTypeBO> convertToBOList(List<BillingCardType> billingCardTypeList) {
        List<BillingCardTypeBO> billingCardTypeBOList = new ArrayList<>();
        for (BillingCardType billingCardType : billingCardTypeList) {
            billingCardTypeBOList.add(billingCardType.toBO());
        }
        return billingCardTypeBOList;
    }

    public List<BillingCardType> findByPlaceIdInAndChainCardTypeId(List<String> placeId, String chainCardTypeId) {
        return billingCardTypeRepository.findByPlaceIdInAndChainCardTypeIdAndDeleted(placeId, chainCardTypeId, 0);
    }

    public Optional<BillingCardType> findByPlaceIdAndChainCardTypeIdAndDeleted(String placeId, String chainCardTypeId) {
        return billingCardTypeRepository.findByPlaceIdAndChainCardTypeIdAndDeleted(placeId, chainCardTypeId, 0);
    }

    public List<BillingCardType> findByPlaceIdInAndChainCardTypeIdInAndDeleted(List<String> placeIds, List<String> chainCardTypeIds) {
        return billingCardTypeRepository.findByPlaceIdInAndChainCardTypeIdInAndDeleted(placeIds, chainCardTypeIds, 0);
    }

    public List<BillingCardType> findByPlaceIdInAndDeleted(List<String> placeIds) {
        return billingCardTypeRepository.findByPlaceIdInAndDeleted(placeIds, 0);
    }

    public void exitChain(String placeId) {
        if (StringUtils.isEmpty(placeId)) {
            log.warn("退出连锁时，更新会员卡类型入参错误，placeId is empty");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        log.info("退出连锁时，更新会员卡类型，placeId={}", placeId);
        List<BillingCardType> billingCardTypeList = billingCardTypeRepository.findByPlaceIdAndDeleted(placeId, 0);
        for (BillingCardType billingCardType : billingCardTypeList) {
            billingCardType.setChainCardTypeId(null);
            billingCardType.setUpdated(LocalDateTime.now());
        }
        billingCardTypeRepository.saveAll(billingCardTypeList);
    }

    public void checkCardTypeParamsForJoinChain(List<BillingCardTypeBO> createNewBillingCardTypeBOList) {
        log.info("加入连锁时，校验合并卡类型参数");

        checkSize(createNewBillingCardTypeBOList);

        // 有在线用户时不允许添加
        boolean haveOnline = billingOnlineService.haveOnline(createNewBillingCardTypeBOList.get(0).getPlaceId());
        if (haveOnline) {
            log.info("当前场所有在线用户，无法同步卡类型");
            throw new ServiceException(ServiceCodes.BILLINT_ONLINE_NOT_SUPPORT);
        }
        //有包时时不允许添加
        List<PackageTimeReserve> byPlaceIdAndStatus = packageTimeReserveService.findByPlaceIdAndStatus(createNewBillingCardTypeBOList.get(0).getPlaceId());
        if (CollectionUtils.isNotEmpty(byPlaceIdAndStatus)) {
            List<String> collect = byPlaceIdAndStatus.stream().map(it -> it.getIdName()).collect(Collectors.toList());
            throw new ServiceException("用户：" + collect.toString() + "存在未使用包时，请先结束包时！");
        }

        String placeId1 = createNewBillingCardTypeBOList.get(0).getPlaceId();
        String chainId = createNewBillingCardTypeBOList.get(0).getChainId();

        // 查询该场所是否已加入过连锁，已加入则禁止合并卡类型
        Optional<PlaceChainStores> placeChainStoresOptional = placeChainStoresService.findByPlaceId(placeId1);
        if (placeChainStoresOptional.isPresent()) {
            log.info("当前场所已加入连锁，无需再次同步卡类型");
            throw new ServiceException(ServiceCodes.BILLING_PLACE_ALREADY_JOIN_CHAIN);
        }
    }

    /**
     * 加入连锁时，场所需要同步连锁的卡类型
     *
     * @param createNewBillingCardTypeBOList 场所需要同步连锁的卡类型，里面包含该场所需要转换过来的旧卡类型
     * @apiNote 1、先删除场所旧卡类型及相关费率；2、新增新卡类型；3、更新场所下会员卡的卡类型。
     */
    public void doMergeCardTypesForJoinChain(List<BillingCardTypeBO> createNewBillingCardTypeBOList) {
        String placeIdWaitingJoinChain = createNewBillingCardTypeBOList.get(0).getPlaceId();
        String chainId = createNewBillingCardTypeBOList.get(0).getChainId();

        // 删除场所旧卡类型及相关费率
        deleteOldBillingCardType(placeIdWaitingJoinChain);

        String specialPlaceCardTypeId = "1001"; // （场所）会员卡，cardTypeId 和 name 固定
        String specialChainCardTypeName = "普通会员";

        Map<String, List<BillingCard>> billingCardMapOfCurrentPlaceGroupByCardTypeId = new HashMap<>();
        List<BillingCard> billingCardsOfCurrentPlace = billingCardService.findByPlaceIdAndDeletedUnderChain(placeIdWaitingJoinChain, 0); //场所的所有会员
        if (org.springframework.util.CollectionUtils.isEmpty(billingCardsOfCurrentPlace)) {
            log.info("场所没有会员卡");
        } else {
            billingCardMapOfCurrentPlaceGroupByCardTypeId =
                    billingCardsOfCurrentPlace.stream().collect(Collectors.groupingBy(BillingCard::getCardTypeId)); // cardTypeId <-> card  的键值对
        }


        LocalDateTime now = LocalDateTime.now();
        Map<String, BillingCardType> newCreatedBillingCardTypes = new HashMap<>(); //记录所有新增的连锁会员卡类型
        // 新增新的卡类型，同时更新对应会员卡里面的卡类型信息
        for (BillingCardTypeBO newBillingCardTypeBO : createNewBillingCardTypeBOList) {
            // 新增新卡
            String newTypeName = newBillingCardTypeBO.getTypeName();
            // 需要更新掉的对应场所旧卡类型
            log.info("连锁时，更新会员卡类型，placeId={}, newTypeName={}, oldCardTypes={}", placeIdWaitingJoinChain, newTypeName, new Gson().toJson(newBillingCardTypeBO.getOldCardTypes()));

            BillingCardType newBillingCardType = new BillingCardType();
            newBillingCardType.setPlaceId(placeIdWaitingJoinChain);
            newBillingCardType.setTypeName(newTypeName);
            newBillingCardType.setChainCardTypeId(newBillingCardTypeBO.getChainCardTypeId());
            newBillingCardType.setCreater(-1L);
            LocalDateTime dateTime = LocalDateTime.now();
            newBillingCardType.setCreated(dateTime);
            newBillingCardType.setMinPointsRequirement(newBillingCardTypeBO.getMinPointsRequirement());
            newBillingCardType.setMinCreateCardAmount(newBillingCardTypeBO.getMinCreateCardAmount() * 100);

            if (specialChainCardTypeName.equals(newTypeName)) {
                newBillingCardType.setCardTypeId(specialPlaceCardTypeId);
            } else {
                newBillingCardType.setCardTypeId(buildCardTypeId(placeIdWaitingJoinChain));
            }
            log.info("create new billingCardType, placeId={}, newTypeName={}", placeIdWaitingJoinChain, newTypeName);
            BillingCardType newCreatedBillingCardType = billingCardTypeRepository.save(newBillingCardType);

            newCreatedBillingCardTypes.put(newCreatedBillingCardType.getChainCardTypeId(),newCreatedBillingCardType);
        }

        List<BillingCardType> joinPlaceAllCardType = billingCardTypeRepository.findByPlaceIdAndDeleted(placeIdWaitingJoinChain,  0); //查询出所有会员卡类型
        String cardTypeIds = String.join(",",joinPlaceAllCardType.stream().map(BillingCardType::getCardTypeId).collect(Collectors.toList()));
        joinPlaceAllCardType = joinPlaceAllCardType.stream().filter(it-> !it.getCardTypeId().equals("1000") && !it.getCardTypeId().equals("1002")).collect(Collectors.toList());

        //上面处理完所有的会员卡类型后，再进行会员的处理
        for (BillingCardTypeBO newBillingCardTypeBO : createNewBillingCardTypeBOList) {
            List<BillingCardTypeBO> oldCardTypes = newBillingCardTypeBO.getOldCardTypes();
            if (CollectionUtils.isEmpty(oldCardTypes)) {
                // 新卡类型，没有对应的没有旧卡类型，跳过
                continue;
            }
            // 刷新对应的会员卡
            for (BillingCardTypeBO oldBillingCardTypeBO : oldCardTypes) {
                // 更新旧卡类型对应的会员卡信息为新的卡类型

//                // 新卡类型名称等于普通会员卡，同时旧卡类型 id 等于 1001 时，更新旧卡类型
//                if (specialChainCardTypeName.equals(newTypeName) && specialPlaceCardTypeId.equals(oldBillingCardTypeBO.getCardTypeId())) {
//                    // id 相同，但是卡类型名称不同的，更新为新的，最终是要把 1001 的名称更新为 “普通会员卡”
//                    billingCardRepository.updateSpecialCardType(placeIdWaitingJoinChain, specialPlaceCardTypeId, specialChainCardTypeName);
//                } else {
//                    // 更新 billingCard
//                    billingCardService.updateCardType(placeIdWaitingJoinChain, oldBillingCardTypeBO.getCardTypeId(), newCreatedBillingCardType);
//                }

                String oldCardTypeId = oldBillingCardTypeBO.getCardTypeId();
                if (billingCardMapOfCurrentPlaceGroupByCardTypeId.size() == 0) {
                    continue;

                }
                List<BillingCard> oldBillingCardsOnOneCardType = billingCardMapOfCurrentPlaceGroupByCardTypeId.get(oldCardTypeId);
                if (CollectionUtils.isEmpty(oldBillingCardsOnOneCardType)) {
                    continue;
                }
                doUnionCardTypeUnderChainNew(placeIdWaitingJoinChain, chainId, oldBillingCardsOnOneCardType, newCreatedBillingCardTypes.get(newBillingCardTypeBO.getChainCardTypeId()),joinPlaceAllCardType);
            }
        }

//        unionCardTypeUnderChain(chainId, placeIdWaitingJoinChain);
//        unionCardTypeUnderChainNew(placeIdWaitingJoinChain, chainId);
        //处理包时的卡类型限制
        billingRulePackageTimeRepository.updateCardTypeIdsByPlaceId(placeIdWaitingJoinChain,cardTypeIds);
        inviteConfigService.clearCardTypeIdsByPlaceId(placeIdWaitingJoinChain);
        //处理营销模块的卡类型限制
        goodsApi.joinChainUpdateCardTypeIds(placeIdWaitingJoinChain,cardTypeIds);

    }

    /**
     * 同一个人，同一连锁下面的所有子卡，只能有一种卡类型，以最先进入连锁时合并的卡类型为准
     * 所以，根据身份证号获取这个人在连锁下最开始的那张会员卡，然后用来更新现在的会员卡类型
     *
     * @param placeIdWaitingJoinChain
     * @param chainId
     */
//    private void unionCardTypeUnderChainNew(String placeIdWaitingJoinChain, String chainId) {
//        LocalDateTime startTime = LocalDateTime.now();
//
//        List<BillingCard> billingCardList = billingCardService.findByPlaceIdAndDeleted(placeIdWaitingJoinChain, 0);
//        if (CollectionUtils.isEmpty(billingCardList)) {
//            return;
//        }
//        Map<String, List<BillingCard>> billingCardsGroupByCardTypeId =
//                billingCardList.stream().collect(Collectors.groupingBy(BillingCard::getCardTypeId));
//        LocalDateTime now = LocalDateTime.now();
//
//        for (Map.Entry<String, List<BillingCard>> entry : billingCardsGroupByCardTypeId.entrySet()) {
////            String cardTypeId = entry.getKey();
//            List<BillingCard> billingCards = entry.getValue();
//            if (CollectionUtils.isEmpty(billingCards)) {
//                continue;
//            }
//            // 2000条一组
//            if (billingCards.size() < 2000) {
//                doUnionCardTypeUnderChain(placeIdWaitingJoinChain, chainId, billingCards, now);
//            } else {
//                // 分批处理，2000条一组
//                int i = 0;
//                while (i < billingCards.size()) {
//                    int j = i + 2000;
//                    if (j > billingCards.size()) {
//                        j = billingCards.size();
//                    }
//                    List<BillingCard> subList = billingCards.subList(i, j);
//                    doUnionCardTypeUnderChain(placeIdWaitingJoinChain, chainId, subList, now);
//                    i = j;
//                }
//            }
//        }
//
//        LocalDateTime endTime = LocalDateTime.now();
//        log.info("加入连锁，统一卡类型，花费时间：{}", Duration.between(startTime, endTime).toMillis() + "ms，了");
//    }

    /**
     * 更新场所会员卡类型，如果这个身份证号连锁网吧下面有会员，更新为这个会员类型，没有的话，更新为当前合并的卡类型
     * @param placeIdWaitingJoinChain
     * @param chainId
     * @param billingCards
     * @param newCreatedBillingCardType
     */
    private void doUnionCardTypeUnderChainNew(String placeIdWaitingJoinChain, String chainId, List<BillingCard> billingCards, BillingCardType newCreatedBillingCardType,List<BillingCardType> joinPlaceAllCardType) {
        // 根据身份证号获取这个人在连锁下最开始的那张会员卡
        List<String> idNumbers = billingCards.stream().filter(it-> !it.getCardTypeId().equals("1000") && !it.getCardTypeId().equals("1002") ).map(BillingCard::getIdNumber).collect(Collectors.toList());
        List<BillingCard> originalCards = billingCardService.getTheOriginalCardUnderChain(chainId, placeIdWaitingJoinChain, idNumbers);

        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isEmpty(originalCards)) {
            // 会员都是新加入连锁
            billingCards.forEach(e -> {
                e.setChainCardTypeId(newCreatedBillingCardType.getChainCardTypeId());
                e.setCardTypeName(newCreatedBillingCardType.getTypeName());
                e.setCardTypeId(newCreatedBillingCardType.getCardTypeId());
                e.setChainCard(0);
                e.setChainId(chainId);
                e.setUpdated(now);
            });
            billingCardService.saveAllBySteps(billingCards);
        } else {
            // 根据身份证号，拿到原来的会员卡数据
            Map<String, BillingCard> billingCardMapWaitFromOrigin = originalCards.stream().collect(Collectors.toMap(BillingCard::getIdNumber, v -> v, (v1, v2) -> v1));
            billingCards.forEach(e -> {
                if (billingCardMapWaitFromOrigin.containsKey(e.getIdNumber())) {
                    BillingCard originalCard = billingCardMapWaitFromOrigin.get(e.getIdNumber());
                    List<BillingCardType> collect = joinPlaceAllCardType.stream().filter(it -> originalCard.getChainCardTypeId().equals(it.getChainCardTypeId())).collect(Collectors.toList());
                    if(collect.size() > 0){
                        e.setCardTypeId(collect.get(0).getCardTypeId());
                    }else{
                        e.setCardTypeId(newCreatedBillingCardType.getCardTypeId());
                    }
                    e.setChainCardTypeId(originalCard.getChainCardTypeId());
                    e.setChainId(chainId);
                    e.setCardTypeName(originalCard.getCardTypeName());
                    e.setChainCard(1);
                } else {
                    e.setChainCardTypeId(newCreatedBillingCardType.getChainCardTypeId());
                    e.setCardTypeId(newCreatedBillingCardType.getCardTypeId());
                    e.setCardTypeName(newCreatedBillingCardType.getTypeName());
                    e.setChainId(chainId);
                    e.setChainCard(0);
                }
                e.setUpdated(now);
            });

            billingCardService.saveAllBySteps(billingCards);
        }
    }

//    private void doUnionCardTypeUnderChain(String placeIdWaitingJoinChain, String chainId, List<BillingCard> billingCards, LocalDateTime now) {
//        List<String> idNumbers = billingCards.stream().map(BillingCard::getIdNumber).collect(Collectors.toList());
//
//        // 根据身份证号获取这个人在连锁下最开始的那张会员卡
//        List<BillingCard> originalCards = billingCardService.getTheOriginalCardUnderChain(chainId, placeIdWaitingJoinChain, idNumbers);
//        Map<String, BillingCard> billingCardMapWaitForUpdate = billingCards.stream().collect(Collectors.toMap(BillingCard::getIdNumber, v -> v));
//
//        List<BillingCard> updateBillingCards = new ArrayList<>();
//        for (BillingCard originalCard : originalCards) {
//            if (billingCardMapWaitForUpdate.containsKey(originalCard.getIdNumber())) {
//                BillingCard billingCard = billingCardMapWaitForUpdate.get(originalCard.getIdNumber());
//                billingCard.setChainCardTypeId(originalCard.getChainCardTypeId());
//                billingCard.setCardTypeId(originalCard.getCardTypeId());
//                billingCard.setCardTypeName(originalCard.getCardTypeName());
//                billingCard.setUpdated(now);
//                updateBillingCards.add(billingCard);
//            }
//        }
//
//        if (!CollectionUtils.isEmpty(updateBillingCards)) {
//            billingCardService.saveAll(updateBillingCards);
//        }
//    }

//    private void unionCardTypeUnderChain(String chainId, String placeIdWaitingJoinChain) {
//        // 同一个人，同一连锁下面的所有子卡，只能有一种卡类型，以最先进入连锁时合并的卡类型为准
//        // 所以，这个人在同一连锁下的所有门店的卡类型都需要统一一下
//        List<PlaceChainStores> placeChainStores = placeChainStoresService.findByChainId(chainId);
//        if (CollectionUtils.isEmpty(placeChainStores)) {
//            // 当前连锁本次之前没有其他场所
//            return;
//        }
//        List<String> placeIds = placeChainStores.stream().map(PlaceChainStores::getPlaceId).collect(Collectors.toList());
//        LocalDateTime startTime = LocalDateTime.now();
//        int i1 = billingCardRepository.unionCardType(placeIds, placeIdWaitingJoinChain);
//        LocalDateTime endTime = LocalDateTime.now();
//        log.info("统一卡类型，花费时间：{}", Duration.between(startTime, endTime).toMillis() + "ms，了");
//
//        log.info("连锁时，更新会员卡类型，placeId={}, 更新了{}条数据", placeIdWaitingJoinChain, i1);
//    }

    @Transactional
    public void updateAfterChain(List<BillingCardTypeBO> createNewBillingCardTypeBOList) {
        log.info("连锁时，更新会员卡类型");

        checkSize(createNewBillingCardTypeBOList);

        // 有在线用户时不允许添加
        boolean haveOnline = billingOnlineService.haveOnline(createNewBillingCardTypeBOList.get(0).getPlaceId());
        if (haveOnline) {
            log.info("当前场所有在线用户，无法同步卡类型");
            throw new ServiceException(ServiceCodes.BILLINT_ONLINE_NOT_SUPPORT);
        }
        //有包时时不允许添加
        List<PackageTimeReserve> byPlaceIdAndStatus = packageTimeReserveService.findByPlaceIdAndStatus(createNewBillingCardTypeBOList.get(0).getPlaceId());
        if (CollectionUtils.isNotEmpty(byPlaceIdAndStatus)) {
            List<String> collect = byPlaceIdAndStatus.stream().map(it -> it.getIdName()).collect(Collectors.toList());
            throw new ServiceException("用户：" + collect.toString() + "存在未使用包时，请先结束包时！");
        }

        String placeId1 = createNewBillingCardTypeBOList.get(0).getPlaceId();
        String chainId = createNewBillingCardTypeBOList.get(0).getChainId();

        // 查询该场所是否已加入过连锁，已加入则禁止合并卡类型
        Optional<PlaceChainStores> placeChainStoresOptional = placeChainStoresService.findByPlaceId(placeId1);
        if (placeChainStoresOptional.isPresent()) {
            log.info("当前场所已加入连锁，无需再次同步卡类型");
            throw new ServiceException(ServiceCodes.BILLING_PLACE_ALREADY_JOIN_CHAIN);
        }

        // 删除场所旧卡类型及相关费率
        deleteOldBillingCardType(placeId1);

        String specialPlaceCardTypeId = "1001"; // （场所）会员卡，cardTypeId 和 name 固定
        String specialChainCardTypeName = "普通会员";
        // 遍历需要新增的卡类型
        for (BillingCardTypeBO bo : createNewBillingCardTypeBOList) {
            String placeId = bo.getPlaceId();
            // 新增新卡
            String newTypeName = bo.getTypeName();
            List<BillingCardTypeBO> oldCardTypes = bo.getOldCardTypes();
            log.info("连锁时，更新会员卡类型，placeId={}, newTypeName={}, oldCardTypes={}", placeId, newTypeName, new Gson().toJson(oldCardTypes));

            BillingCardType newBillingCardType = new BillingCardType();
            newBillingCardType.setPlaceId(placeId);
            newBillingCardType.setTypeName(newTypeName);
            newBillingCardType.setChainCardTypeId(bo.getChainCardTypeId());
            newBillingCardType.setCreater(-1L);
            LocalDateTime dateTime = LocalDateTime.now();
            newBillingCardType.setCreated(dateTime);
            newBillingCardType.setMinPointsRequirement(bo.getMinPointsRequirement());
            newBillingCardType.setMinCreateCardAmount(bo.getMinCreateCardAmount() * 100);

            if (specialChainCardTypeName.equals(newTypeName)) {
                newBillingCardType.setCardTypeId(specialPlaceCardTypeId);
            } else {
                newBillingCardType.setCardTypeId(buildCardTypeId(placeId));
            }
            log.info("create new billingCardType, placeId={}, newTypeName={}", placeId, newTypeName);
            BillingCardType billingCardType = billingCardTypeRepository.save(newBillingCardType);

            if (CollectionUtils.isEmpty(oldCardTypes)) {
                continue;
            }

            for (BillingCardTypeBO billingCardTypeBO : oldCardTypes) {
                // 更新旧卡类型
                billingCardService.updateCardType(placeId, billingCardTypeBO.getCardTypeId(), billingCardType, chainId);
//                if (specialChainCardTypeName.equals(newTypeName) && specialPlaceCardTypeId.equals(billingCardTypeBO.getCardTypeId())) {
//                    log.info("加入连锁更改旧卡类型:::::placeId:::" + placeId + "::::chainId::::" + chainId + "::::ChainCardTypeId:::::" + bo.getChainCardTypeId());
//                    billingCardRepository.updateSpecialCardType(placeId, specialPlaceCardTypeId, specialChainCardTypeName, chainId, bo.getChainCardTypeId());
//                } else {
//                    // 更新 billingCard
//                    log.info("加入连锁更改旧卡类型2:::::placeId:::" + placeId + "::::chainId::::" + chainId + "::::ChainCardTypeId:::::" + billingCardType.getChainCardTypeId());
//                    billingCardService.updateCardType(placeId, billingCardTypeBO.getCardTypeId(), billingCardType, chainId);
//                }
            }
        }

        // 同一个人，同一连锁下面的所有子卡，只能有一张卡类型，以最先进入连锁时合并的卡类型为准
        List<String> placeIds = new ArrayList<>();
        List<PlaceChainStores> placeChainStores = placeChainStoresService.findByChainId(chainId);
        if (!CollectionUtils.isEmpty(placeChainStores)) {
            placeChainStores.stream().map(PlaceChainStores::getPlaceId).forEach(placeIds::add);
            int i1 = billingCardRepository.unionCardType(placeIds, placeId1);
            log.info("连锁时，更新会员卡类型，placeId={}, 更新了{}条数据", placeId1, i1);
        }
    }

    private void deleteOldBillingCardType(String placeId1) {
        billingCardTypeRepository.updateCardTypesWhenChain(placeId1);

        // 删除场所就卡类型对应的充值规则
        topupRuleRepository.deleteTopupRulesByPlaceId(placeId1);
        billingRuleCommonRepository.deletedByPlaceId(placeId1);
        updateBillingRuleAccWhenJoinChain(placeId1);
//        updateBillingRulePackageTimeWhenJoinChain(placeId1);
    }

    private void updateBillingRulePackageTimeWhenJoinChain(String placeId1) {
        List<BillingRulePackageTime> billingRulePackageTimeList = billingRulePackageTimeRepository.findByPlaceIdAndDeleted(placeId1, 0);
        if (CollectionUtils.isEmpty(billingRulePackageTimeList)) {
            return;
        }
        List<BillingRulePackageTime> needUpdateBillingRulePackageTimeList = new ArrayList<>();
        for (BillingRulePackageTime billingRulePackageTime : billingRulePackageTimeList) {
            String cardTypeIds = billingRulePackageTime.getCardTypeIds();
            // 一个值
            if ("1000".equals(cardTypeIds) || "1001".equals(cardTypeIds)
                    || "1002".equals(cardTypeIds)) {
                continue;
            }
            // 多个值
            List<String> cardTypeIdList = new ArrayList<>();
            if (cardTypeIds.contains("1000")) {
                cardTypeIdList.add("1000");
            }
            if (cardTypeIds.contains("1001")) {
                cardTypeIdList.add("1001");
            }
            if (cardTypeIds.contains("1002")) {
                cardTypeIdList.add("1002");
            }
            LocalDateTime now = LocalDateTime.now();
            if (CollectionUtils.isEmpty(cardTypeIdList)) {
                // 为空，这条记录标记删除
                billingRulePackageTime.setDeleted(1);
                billingRulePackageTime.setUpdated(now);
                needUpdateBillingRulePackageTimeList.add(billingRulePackageTime);
            } else {
                // 不为空，只保留1000/1001/1002更新
                String newCardTypeIds = String.join(",", cardTypeIdList);
                log.info("连锁时，更新计时套餐，placeId={}, oldCardTypeIds={}, cardTypeIdList={}",
                        placeId1, billingRulePackageTime.getCardTypeIds(), new Gson().toJson(cardTypeIdList));
                billingRulePackageTime.setCardTypeIds(newCardTypeIds);
                billingRulePackageTime.setUpdated(now);
                needUpdateBillingRulePackageTimeList.add(billingRulePackageTime);
            }
        }
        billingRulePackageTimeRepository.saveAll(needUpdateBillingRulePackageTimeList);
    }

    private void updateBillingRuleAccWhenJoinChain(String placeId1) {
        List<BillingRuleAcc> billingRuleAccList = billingRuleAccRepository.findByPlaceIdAndDeleted(placeId1, 0);
        if (CollectionUtils.isEmpty(billingRuleAccList)) {
            return;
        }
        List<BillingRuleAcc> needUpdateBillingRuleAccList = new ArrayList<>();
        for (BillingRuleAcc billingRuleAcc : billingRuleAccList) {
            String cardTypeIds = billingRuleAcc.getCardTypeIds();
            // 一个值
            if ("1000".equals(cardTypeIds) || "1001".equals(cardTypeIds)
                    || "1002".equals(cardTypeIds)) {
                continue;
            }
            // 多个值
            List<String> cardTypeIdList = new ArrayList<>();
            if (cardTypeIds.contains("1000")) {
                cardTypeIdList.add("1000");
            }
            if (cardTypeIds.contains("1001")) {
                cardTypeIdList.add("1001");
            }
            if (cardTypeIds.contains("1002")) {
                cardTypeIdList.add("1002");
            }
            LocalDateTime now = LocalDateTime.now();
            if (CollectionUtils.isEmpty(cardTypeIdList)) {
                // 为空，这条记录标记删除
                billingRuleAcc.setDeleted(1);
                billingRuleAcc.setUpdated(now);
                needUpdateBillingRuleAccList.add(billingRuleAcc);
            } else {
                // 不为空，只保留1000/1001/1002更新
                String newCardTypeIds = String.join(",", cardTypeIdList);
                billingRuleAcc.setCardTypeIds(newCardTypeIds);
                billingRuleAcc.setUpdated(now);
                needUpdateBillingRuleAccList.add(billingRuleAcc);
            }
        }
        billingRuleAccRepository.saveAll(needUpdateBillingRuleAccList);
    }

    private void checkSize(List<BillingCardTypeBO> createNewBillingCardTypeBOList) {
        if (CollectionUtils.isEmpty(createNewBillingCardTypeBOList)) {
            log.warn("更新连锁后，新增会员卡类型入参错误，createNewBillingCardTypeBOList is empty");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }

        int size = createNewBillingCardTypeBOList.size();
        if (size < 1) {
            log.warn("更新连锁后，新增会员卡类型入参错误，createNewBillingCardTypeBOList size < 1");
            throw new ServiceException(ServiceCodes.NULL_PARAM);
        }
    }

    /**
     * date:2024.2.1
     * 增加最低开卡金额和最小积分要求参数
     * minCreateCardAmount      最低开卡金额
     * minPointsRequirement     最小积分要求
     */
    public void synchronizedNewCardTypeFromChain(String chainId, String typeName, String chainCardTypeId, int minCreateCardAmount, int minPointsRequirement) {
        List<PlaceChainStores> placeChainStoresList = placeChainStoresService.findByChainId(chainId);
        if (org.springframework.util.CollectionUtils.isEmpty(placeChainStoresList)) {
            log.warn("同步连锁新卡类型时，连锁下没有网吧，chainId={}", chainId);
            return;
        }

        for (PlaceChainStores placeChainStores : placeChainStoresList) {
            String placeId = placeChainStores.getPlaceId();
            Optional<BillingCardType> billingCardTypeOptional =
                    billingCardTypeRepository.findByPlaceIdAndChainCardTypeIdAndTypeNameAndDeleted(placeId, chainCardTypeId, typeName, 0);
            if (!billingCardTypeOptional.isPresent()) {
                BillingCardType billingCardType = new BillingCardType();
                billingCardType.setPlaceId(placeId);
                billingCardType.setTypeName(typeName);
                billingCardType.setChainCardTypeId(chainCardTypeId);
                billingCardType.setCreater(-1L);
                LocalDateTime dateTime = LocalDateTime.now();
                billingCardType.setCreated(dateTime);
                billingCardType.setCardTypeId(buildCardTypeId(placeId));
                billingCardType.setMinCreateCardAmount(minCreateCardAmount * 100);
                billingCardType.setMinPointsRequirement(minPointsRequirement);
                billingCardTypeRepository.save(billingCardType);
            } else {
                BillingCardType billingCardType = billingCardTypeOptional.get();
                billingCardType.setTypeName(typeName);
                billingCardType.setMinCreateCardAmount(minCreateCardAmount * 100);
                billingCardType.setMinPointsRequirement(minPointsRequirement);
                billingCardTypeRepository.save(billingCardType);
            }

            // 连锁增加卡类型同步给分店时，信息同步给分店收银台
            GenericResponse<ObjDTO<PollingBO>> pollingBOGeneric = notifyServerService.savePolling(placeId, "", "", BusinessType.UPDATECONFIG);
            if (pollingBOGeneric.isResult()) {
                PollingBO pollingBO = pollingBOGeneric.getData().getObj();
                if (pollingBO.getPollingType().equals(PollingType.RES_JSON)) {
                    UpdateConfigBusinessBO updateConfigBusinessBO = new UpdateConfigBusinessBO();
                    updateConfigBusinessBO.setPlaceId(placeId);
                    updateConfigBusinessBO.setBusinessType(BusinessType.UPDATECONFIG);
                    updateConfigBusinessBO.setBusinessId(pollingBO.getCashierBusinessId());
                    updateConfigBusinessBO.setType(1);
                    // 保存收银台业务数据
                    notifyServerService.pushUpdateConfigBusinessData(updateConfigBusinessBO);
                }
            }

        }
    }

    public BillingCardType get(String placeId, String cardTypeId) {
        Optional<BillingCardType> billingCardTypeOptional =
                billingCardTypeRepository.findByPlaceIdAndCardTypeIdAndDeleted(placeId, cardTypeId, 0);

        return billingCardTypeOptional.orElseThrow(() -> {
            log.info("根据placeId和cardTypeId查询会员卡类型，未查询到数据，placeId={}, cardTypeId={}", placeId, cardTypeId);
            return new ServiceException(ServiceCodes.NULL_PARAM);
        });
    }
}
