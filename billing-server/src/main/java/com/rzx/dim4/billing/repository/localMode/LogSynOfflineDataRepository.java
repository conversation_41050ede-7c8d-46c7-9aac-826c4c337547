package com.rzx.dim4.billing.repository.localMode;

import com.rzx.dim4.billing.entity.localMode.LogSynOfflineData;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface LogSynOfflineDataRepository extends JpaRepository<LogSynOfflineData, Long> {

    Optional<LogSynOfflineData> findByOfflineDataId (String offlineDataId);

    List<LogSynOfflineData> findByRequestIdOrderByIdDesc(String requestId);

}
