package com.rzx.dim4.billing.service.algorithm;

/**
 * 计费规则中的关于余额计算方法
 * 
 * <AUTHOR>
 * @date 2021年11月16日 上午11:35:26
 */
public class BalanceAlgorithm {

	/**
	 * 《额外扣除》 结账时清理掉零钱。<br/>
	 * 清理规则是不足precision/100元的直接扣除，大于precision/100元，不足1元的，扣除到precision/100元<br/>
	 * 例如，当precision = 50时，4.3元 => 4.0元，返回30，4.7 => 4.5，返回20
	 * 
	 * @param totalBalance 当前余额，单位分
	 * @param precision    计算精度，单位分
	 * @return 返回需要扣除的零钱，单位分
	 */
	public static int additionalDeduct(int totalBalance, int precision) {
		int remainder = totalBalance % 100; // 零钱
		if (remainder > 0 && remainder != precision) { // 存在零钱且不等于0.5元
			if (remainder > precision) {
				return remainder - precision;
			} else {
				return remainder;
			}
		}
		return 0;
	}
}
