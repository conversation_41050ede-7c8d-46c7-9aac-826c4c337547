package com.rzx.dim4.billing.service;

import com.rzx.dim4.base.bo.place.PlaceShiftSumBO;
import com.rzx.dim4.billing.entity.BalanceDetails;
import com.rzx.dim4.billing.repository.BalanceDetailsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 网费余额明细
 *
 * <AUTHOR>
 * @date 2025年3月4日 下午2:20:00
 */
@Slf4j
@Service
public class BalanceDetailsService {

    @Autowired
    private BalanceDetailsRepository balanceDetailsRepository;

    /**
     * 保存网费余额明细
     *
     * @param balanceDetails
     * @return
     */
    public BalanceDetails save(BalanceDetails balanceDetails) {
        return balanceDetailsRepository.save(balanceDetails);
    }

    // 批量保存
    public List<BalanceDetails> saveAll(List<BalanceDetails> balanceDetails) {
        return balanceDetailsRepository.saveAll(balanceDetails);
    }

    private Specification<BalanceDetails> createSpecification(Map<String, String> map) {
        return new Specification<BalanceDetails>() {
            private static final long serialVersionUID = 1L;

            @Override
            public Predicate toPredicate(Root<BalanceDetails> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                List<Predicate> predicateList = new ArrayList<>();

                // 是否删除
                if (map.containsKey("deleted") && !StringUtils.isEmpty(map.get("deleted"))) {
                    predicateList.add(criteriaBuilder.equal(root.get("deleted"), Integer.parseInt(map.get("deleted"))));
                } else {
                    predicateList.add(criteriaBuilder.equal(root.get("deleted"), 0));
                }

                // 场所ID
                if (map.containsKey("placeId") && !StringUtils.isEmpty(map.get("placeId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("placeId").as(String.class), map.get("placeId"));
                    predicateList.add(predicate);
                }

                if (map.containsKey("chainId") && !StringUtils.isEmpty(map.get("chainId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("chainId").as(String.class), map.get("chainId"));
                    predicateList.add(predicate);
                }

                if (map.containsKey("cardId") && !StringUtils.isEmpty(map.get("cardId"))) {
                    Predicate predicate = criteriaBuilder.equal(root.get("cardId").as(String.class), map.get("cardId"));
                    predicateList.add(predicate);
                }

                // 余额明细类型，0支出，1收入
                if (map.containsKey("type") && !StringUtils.isEmpty(map.get("type"))) {// 类型名称
                    predicateList.add(criteriaBuilder.equal(root.get("type").as(Integer.class), map.get("type")));
                }

                if (map.containsKey("idNumber") && !StringUtils.isEmpty(map.get("idNumber"))) {
                    Predicate predicate = criteriaBuilder.like(root.get("idNumber").as(String.class), "%" + map.get("idNumber") + "%");
                    predicateList.add(predicate);
                }

                // 开始时间
                if (map.containsKey("startDate") && !StringUtils.isEmpty(map.get("startDate"))) {
                    LocalDateTime startTime = LocalDateTime.parse(String.valueOf(map.get("startDate")), formatter);
                    predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get("created").as(LocalDateTime.class), startTime));
                }

                // 结束时间 （小于等于）
                if (map.containsKey("endDate") && !StringUtils.isEmpty(map.get("endDate"))) {
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(map.get("endDate")), formatter);
                    predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 结束时间 (小于)
                if (map.containsKey("endDateTime") && !StringUtils.isEmpty(map.get("endDateTime"))) {
                    LocalDateTime endTime = LocalDateTime.parse(String.valueOf(map.get("endDateTime")), formatter);
                    predicateList.add(criteriaBuilder.lessThan(root.get("created").as(LocalDateTime.class), endTime));
                }

                // 操作人(注意：数据库中字段单词已拼写错误，请勿修改)
                if (map.containsKey("operator") && !StringUtils.isEmpty(map.get("operator"))) {
                    predicateList.add(criteriaBuilder.equal(root.get("creater").as(Long.class), Long.parseLong(map.get("operator"))));
                }

                // 操作类型
                if (map.containsKey("operationType") && !StringUtils.isEmpty(map.get("operationType"))) {
                    predicateList.add(criteriaBuilder.equal(root.get("operationType").as(Integer.class), Integer.parseInt(map.get("operationType"))));
                }

                // 扣费账户类型，0网费本金，1网费奖励
                if (map.containsKey("accountType") && !StringUtils.isEmpty(map.get("accountType"))) {// 账户类型
                    predicateList.add(criteriaBuilder.equal(root.get("accountType").as(Integer.class), map.get("accountType")));
                }

                Predicate[] predicateArr = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicateArr));
            }
        };
    }

    public List<BalanceDetails> findAll(Map<String, String> map) {
        return balanceDetailsRepository.findAll(createSpecification(map));
    }

    public Page<BalanceDetails> findAll(Map<String, String> map, Pageable pageable) {
        return balanceDetailsRepository.findAll(createSpecification(map), pageable);
    }

    public PlaceShiftSumBO queryPlaceShiftStatisticsInfo(String placeId, LocalDateTime startTime, LocalDateTime endTime) {
        Integer cashAmountCostSum = balanceDetailsRepository.sumAmountIncome(placeId, startTime, endTime, 0);
        Integer presentAmountCostSum = balanceDetailsRepository.sumAmountIncome(placeId, startTime, endTime, 1);

        PlaceShiftSumBO placeShiftSumBO = new PlaceShiftSumBO();
        placeShiftSumBO.setPlaceId(placeId);
        placeShiftSumBO.setWorkingTime(startTime);
        placeShiftSumBO.setOffWorkingTime(endTime);
        placeShiftSumBO.setCashAmountCostSum(cashAmountCostSum == null ? 0 : cashAmountCostSum);
        placeShiftSumBO.setPresentAmountCostSum(presentAmountCostSum == null ? 0 : presentAmountCostSum);

        return placeShiftSumBO;
    }

    public List<BalanceDetails> findByPlaceIdAndInviteCode(String placeId, String inviteCode) {
        return balanceDetailsRepository.findByPlaceIdAndInviteCodeOrderByIdDesc(placeId, inviteCode);
    }
}