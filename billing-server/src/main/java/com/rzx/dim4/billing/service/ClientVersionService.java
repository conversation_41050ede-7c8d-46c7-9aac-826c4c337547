package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.ClientUpgrade;
import com.rzx.dim4.billing.entity.ClientVersion;
import com.rzx.dim4.billing.repository.ClientVersionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

@Service
public class ClientVersionService {
    @Autowired
    private ClientVersionRepository clientVersionRepository;

    @Autowired
    private ClientUpgradeService clientUpgradeService;
    /**
     * 根据客户端类型和版本号查询版本信息
     * @param versionNumber
     * @param clientType
     * @return
     */
    public Optional<ClientVersion> findByVersionNumberAndClientType(String versionNumber, int clientType){
        return clientVersionRepository.findByVersionNumberAndClientTypeAndDeleted(versionNumber,clientType,0);
    }

    public ClientVersion getClientVersion(String placeId, String clientType) {
        Optional<ClientUpgrade> cashierUpgradeOpt = clientUpgradeService.findByPlaceIdAndClientType(placeId, clientType);
        if (!cashierUpgradeOpt.isPresent()) {
            return null;
        }
        ClientUpgrade cashierUpgrade = cashierUpgradeOpt.get();
        Optional<ClientVersion> cashierVersionOpt = clientVersionRepository.findByVersionIdAndDeleted(cashierUpgrade.getVersionId(),0);
        return cashierVersionOpt.orElse(null);
    }

    /**
     * 根据版本Id 查询版本信息
     * @param versionId
     * @return
     */
    public Optional<ClientVersion> findByVersionId(String versionId){
        return clientVersionRepository.findByVersionIdAndDeleted(versionId,0);
    }

    /**
     * 根据版本Id 批量查询版本信息
     * @param versionIds
     * @return
     */
    public List<ClientVersion> findByVersionIdIn (List<String> versionIds) {
        return clientVersionRepository.findByVersionIdInAndDeleted(versionIds,0);
    }

    /**
     * 查询所有的版本信息
     * @param clientType
     * @return
     */
    public List<ClientVersion> findClientVersions(int clientType){
        return clientVersionRepository.findByClientTypeAndDeletedOrderByIdDesc(clientType,0);
    }

    /**
     * 版本列表分页
     *
     * @param pageable
     * @return
     */
    public Page<ClientVersion> findAll(String versionNumber, int clientType, Pageable pageable) {
        if (StringUtils.isEmpty(versionNumber)) {
            return clientVersionRepository.findByClientTypeAndDeletedOrderByIdDesc(clientType,0,pageable);
        } else {
            return clientVersionRepository.findByClientTypeAndVersionNumberAndDeletedOrderByIdDesc(clientType,versionNumber,0, pageable);
        }
    }

    public Page<ClientVersion> findClientVersionsByVersionIds(String versionNumber,
            int clientType, List<String> versionIds, Pageable pageable) {
        if (StringUtils.isEmpty(versionNumber)) {
            return clientVersionRepository.findByClientTypeAndVersionIdInAndDeletedOrderByIdDesc(
                    clientType, versionIds, 0, pageable);
        }
        return clientVersionRepository.findByClientTypeAndVersionNumberAndVersionIdInAndDeletedOrderByIdDesc(
                clientType, versionNumber, versionIds, 0, pageable);
    }

    public Optional<ClientVersion> findTop1() {
        return clientVersionRepository.findTop1ByOrderByIdDesc();
    }

    public Optional<ClientVersion> findByFileMD5(int clientType,String fileMD5){
        return clientVersionRepository.findByClientTypeAndFileMD5(clientType,fileMD5);
    }

    public ClientVersion save(ClientVersion clientVersion){
        return clientVersionRepository.save(clientVersion);
    }

    public String buildVersionId () {
        Optional<ClientVersion> lastClientVersion = findTop1();
        int indexInt = 1000;
        if (lastClientVersion.isPresent()) {
            String lastVersionId = lastClientVersion.get().getVersionId();
            indexInt = Integer.parseInt(lastVersionId) + 1;
        }
        return String.valueOf(indexInt);
    }
}
