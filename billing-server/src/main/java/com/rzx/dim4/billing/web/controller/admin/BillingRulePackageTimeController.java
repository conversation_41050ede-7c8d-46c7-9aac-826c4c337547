package com.rzx.dim4.billing.web.controller.admin;

import com.google.gson.Gson;
import com.rzx.dim4.base.bo.billing.BillingRulePackageTimeBO;
import com.rzx.dim4.base.bo.marketing.GoodsBO;
import com.rzx.dim4.base.bo.marketing.OrdersBO;
import com.rzx.dim4.base.bo.notify.polling.BookSeatsBusinessBO;
import com.rzx.dim4.base.bo.notify.polling.PollingBO;
import com.rzx.dim4.base.bo.payment.PaymentRefundOrderBO;
import com.rzx.dim4.base.bo.place.PlaceAreaBO;
import com.rzx.dim4.base.bo.place.PlaceClientBO;
import com.rzx.dim4.base.dto.ListDTO;
import com.rzx.dim4.base.dto.ObjDTO;
import com.rzx.dim4.base.dto.PagerDTO;
import com.rzx.dim4.base.dto.SimpleDTO;
import com.rzx.dim4.base.enums.ServiceCodes;
import com.rzx.dim4.base.enums.billing.RefundType;
import com.rzx.dim4.base.enums.billing.SourceType;
import com.rzx.dim4.base.enums.notify.BusinessType;
import com.rzx.dim4.base.enums.payment.PayType;
import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.base.service.feign.NotifyServerService;
import com.rzx.dim4.base.service.feign.PaymentServerService;
import com.rzx.dim4.base.service.feign.PlaceServerService;
import com.rzx.dim4.base.service.feign.marketing.MarketingGoodsApi;
import com.rzx.dim4.base.service.feign.marketing.OrdersApi;
import com.rzx.dim4.base.service.feign.notify.BusinessDataApi;
import com.rzx.dim4.base.utils.DateTimeUtils;
import com.rzx.dim4.base.utils.Dim4StringUtils;
import com.rzx.dim4.billing.entity.*;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.algorithm.BalanceDetailsAlgorithm;
import com.rzx.dim4.billing.service.algorithm.BillingLockAlgorithm;
import com.rzx.dim4.billing.service.algorithm.PackageTimeAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/billing/admin/billing/rule/packageTime")
@Slf4j
public class BillingRulePackageTimeController {

    @Autowired
    BillingRulePackageTimeService billingRulePackageTimeService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    BillingCardTypeService billingCardTypeService;

    @Autowired
    PlaceServerService placeServerService;

    @Autowired
    LogTopupService logTopupService;

    @Autowired
    PaymentServerService paymentServerService;

    @Autowired
    LogRefundService logRefundService;

    @Autowired
    BillingOnlineService billingOnlineService;

    @Autowired
    BillingCardService billingCardService;

    @Autowired
    LogLoginService logLoginService;

    @Autowired
    LogOperationService logOperationService;

    @Autowired
    LogShiftService logShiftService;

    @Autowired
    NotifyServerService notifyServerService;

    @Autowired
    BillingRuleCommonService billingRuleCommonService;

    @Autowired
    BillingLockAlgorithm billingLockAlgorithm;

    @Autowired
    BusinessDataApi businessDataApi;

    @Autowired
    PackageTimeReserveService packageTimeReserveService;

    @Autowired
    private OrdersApi ordersApi;

    @Autowired
    MarketingGoodsApi goodsApi;

    @Autowired
    private BalanceDetailsService balanceDetailsService;

    @Autowired
    BalanceDetailsAlgorithm balanceDetailsAlgorithm;

    @Autowired
    private PlacementService placementService;

    @Autowired
    private LogHbService logHbService;

    /**
     * @param placeId
     * @return
     */
    @GetMapping("/queryPageBillingRulePackageTime")
    public GenericResponse<PagerDTO<BillingRulePackageTimeBO>> queryPageBillingRulePackageTime(@RequestParam String placeId,
                                                                                               @RequestParam(name = "size", defaultValue = "10") int size,
                                                                                               @RequestParam(name = "page", defaultValue = "0") int page,
                                                                                               @RequestParam(name = "orderColumns", defaultValue = "id") String[] orderColumns,
                                                                                               @RequestParam(name = "order", defaultValue = "desc") String order,
                                                                                               @RequestParam(required = false) String areaId,
                                                                                               @RequestParam(required = false) String cardTypeId,
                                                                                               @RequestParam(required = false) SourceType sourceType,
                                                                                               @RequestParam(value = "packageFlag", required = false) String packageFlag,
                                                                                               @RequestParam(value = "ruleName", required = false) String ruleName){

        Pageable pageable = PageRequest.of(page, size, Sort.Direction.fromString(order), orderColumns);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("placeId", placeId);
        if(null != sourceType){
            queryMap.put("forbidden", "0");
        }
        if (!ObjectUtils.isEmpty(sourceType)) {
            queryMap.put("limitSalesTerminal", sourceType.name());
        }
        if(!StringUtils.isEmpty(packageFlag)){
            queryMap.put("packageFlag", packageFlag);
        }
        if(!StringUtils.isEmpty(ruleName)){
            queryMap.put("ruleName", ruleName);
        }

        // 区域id
//        if (!StringUtils.isEmpty(areaId)) {
//            queryMap.put("areaId", areaId);
//        }
        // 卡类型id
        if (!StringUtils.isEmpty(cardTypeId)) {
            queryMap.put("cardTypeId", cardTypeId);
        }
        LocalDateTime now = LocalDateTime.now();
        Page<BillingRulePackageTime> billingRulePackagePage = billingRulePackageTimeService.findAll(queryMap, pageable);
        List<BillingRulePackageTimeBO> billingRulePackageTimeBOS = billingRulePackagePage.getContent().stream().map(e -> {
            BillingRulePackageTimeBO billingRulePackageTimeBO = e.toBO();
            if(null != sourceType){
                if (SourceType.CLIENT.equals(sourceType) && (!billingRulePackageTimeBO.getAreaIds().contains(areaId))){
                    billingRulePackageTimeBO.setCycleStatus(false);
                }else{
                    billingRulePackageTimeBO.setCycleStatus(billingRulePackageTimeService.verifyPackageTimeCycle(billingRulePackageTimeBO,now));
                }
            }
            return billingRulePackageTimeBO;
        }).collect(Collectors.toList());

        List<PlaceAreaBO> placeAreas = new ArrayList<>();
        // 获取区域名称
        GenericResponse<ListDTO<PlaceAreaBO>> placeAreasResponse = placeServerService.findPlaceAreaByPlaceId(placeId);
        if (placeAreasResponse.getCode() == ServiceCodes.NO_ERROR.getCode()) {
            placeAreas = placeAreasResponse.getData().getList();
        }
        // 获取卡类型名称
        List<BillingCardType> billingCardTypes = billingCardTypeService.findByPlaceId(placeId);

        // 组装数据给页面VO
        billingRulePackageTimeBOS = getAreaNameCardTypeName (billingRulePackageTimeBOS, placeAreas, billingCardTypes);

        billingRulePackageTimeBOS = billingRulePackageTimeBOS.stream().sorted(Comparator.comparing(BillingRulePackageTimeBO::isCycleStatus).reversed()).collect(Collectors.toList());
        return new GenericResponse<>(new PagerDTO<>((int) billingRulePackagePage.getTotalElements(), billingRulePackageTimeBOS));
    }

    /**
     * 查询该规则详细信息
     *
     * @param placeId
     * @param ruleId
     * @return
     */
    @GetMapping("/queryBillingRulePackageTime")
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> queryBillingRulePackageTime(@RequestParam String placeId, @RequestParam String ruleId) {

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(ruleId)) {
            return new GenericResponse<>(ServiceCodes.NULL_PARAM);
        }

        Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, ruleId);
        if (!billingRulePackageTimeOpt.isPresent()) {
            return new GenericResponse<>(ServiceCodes.NOT_FOUND);
        }
        BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();

        return new GenericResponse<>(new ObjDTO<>(billingRulePackageTime.toBO()));
    }

    /**
     * 新增/编辑 包时费率
     *
     * @param requestTicket
     * @param billingRulePackageTimeBO
     * @return
     */
    @PostMapping("/create")
    public GenericResponse<ObjDTO<BillingRulePackageTimeBO>> createBillingRulePackageTime(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                                          @RequestBody BillingRulePackageTimeBO billingRulePackageTimeBO) {

        if (!stringRedisTemplate.delete(requestTicket)) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }
        log.info("新增或修改包时信息:::{}",new Gson().toJson(billingRulePackageTimeBO));
//        if ((billingRulePackageTimeBO.getPackageFlag() > 0) && (billingRulePackageTimeBO.getStartTime() == billingRulePackageTimeBO.getEndTime()) && billingRulePackageTimeBO.getStartTime() != 0) {
//            return new GenericResponse<>(ServiceCodes.BILLING_PT_CONFLICT);
//        }

        String placeId = billingRulePackageTimeBO.getPlaceId();
        LocalDateTime now = LocalDateTime.now();
        // 同一开始时间、结束时间、包时类型可以存在多条，这里不做限制，交给用户选择。
        // 校验卡类型
        List<BillingCardType> billingCardTypes = billingCardTypeService.findByPlaceId(placeId);
        // 该场所下所有的卡类型Id
        List<String> cardTypeIds = billingCardTypes.stream().map(BillingCardType::getCardTypeId).collect(Collectors.toList());
        String[] changeCardTypeIds = billingRulePackageTimeBO.getCardTypeIds().split(",");
        if (!new HashSet<>(cardTypeIds).containsAll(Arrays.asList(changeCardTypeIds))) {
            return new GenericResponse<>(ServiceCodes.BILLING_CARD_TYPE_NOT_FOUND);
        }

        // 为避免代码耦合，校验区域放在place服务。

        BillingRulePackageTime billingRulePackageTime = new BillingRulePackageTime();
        BeanUtils.copyProperties(billingRulePackageTimeBO, billingRulePackageTime);


        if (!StringUtils.isEmpty(billingRulePackageTime.getRuleId())) {
            Optional<BillingRulePackageTime> byPlaceIdAndRuleId = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, billingRulePackageTime.getRuleId());
            if(byPlaceIdAndRuleId.isPresent()){
                BillingRulePackageTime billingRulePackageTime1 = byPlaceIdAndRuleId.get();
                billingRulePackageTime.setId(billingRulePackageTime1.getId());
            }
        }

        // 校验时间格式
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("HH:mm:ss");
        try {
            LocalTime limitDurationEndTime = LocalTime.parse(billingRulePackageTimeBO.getLimitDurationEndTime(), sdf);
            LocalTime salesStartTime = LocalTime.parse(billingRulePackageTimeBO.getSalesStartTime(), sdf);
            LocalTime salesEndTime = LocalTime.parse(billingRulePackageTimeBO.getSalesEndTime(), sdf);
            billingRulePackageTime.setLimitDurationEndTime(Time.valueOf(limitDurationEndTime));
            billingRulePackageTime.setSalesStartTime(Time.valueOf(salesStartTime));
            billingRulePackageTime.setSalesEndTime(Time.valueOf(salesEndTime));
        } catch (Exception e) {
            log.info(e.getMessage());
            return new GenericResponse<>(ServiceCodes.BILLING_DATE_PARSE_ERROR);
        }

        // 新增
        if (billingRulePackageTime.getId() == null) {
            billingRulePackageTime.setCreated(now);
            billingRulePackageTime.setRuleId(billingRulePackageTimeService.builderRuleId(placeId));
        } else {
            // 编辑
            billingRulePackageTime.setUpdated(now);
        }

        // 包时段记录时长字段
//        if (billingRulePackageTime.getPackageFlag() == 1) {
//            float diff = billingRulePackageTime.getEndTime() - billingRulePackageTime.getStartTime();
//            float durationTime = diff <=0 ? diff + 24 : diff;
//            billingRulePackageTime.setDurationTime(new BigDecimal(durationTime).setScale(2, RoundingMode.HALF_UP).intValue());
//        }
        BillingRulePackageTime result = billingRulePackageTimeService.save(billingRulePackageTime);
        if (result == null) {
            return new GenericResponse<>(ServiceCodes.OPT_ERROR);
        }
        try {
            //保存一个goods对象
            GoodsBO goods = new GoodsBO();
            goods.setPackageRuleId(billingRulePackageTime.getRuleId());
            goods.setPlaceId(billingRulePackageTime.getPlaceId());
            goods.setGoodsName(billingRulePackageTime.getRuleName());
            goods.setGoodsTypeId("");
            goods.setGoodsTypeName("默认分类");
            goods.setUnitPrice(billingRulePackageTime.getPrice());
            goods.setUnit(9);//张
            goods.setGoodsCategory(4);//商品类型优惠券
            goods.setSellStatus(1);//停售
            goods.setCycle(0);//售卖周期默认每日
            goods.setStartTime1(0);
            goods.setEndTime1(24);
            goods.setShowMobileSwitch(1);
            goods.setShowClientSwitch(1);
            goods.setShowCashierSwitch(1);
            goods.setOnlinePaySwitch(1);
            goods.setShowRank(1);
            goods.setCreated(billingRulePackageTime.getCreated());
            goods.setCreater(billingRulePackageTime.getCreater());
            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
            goodsApi.saveGoods(requestTicket2,goods);
        }catch (Exception e){
            log.info("保存包时时新增商品对象异常"+e.getMessage());
            e.printStackTrace();
        }
        return new GenericResponse<>(new ObjDTO<>(result.toBO()));
    }

    /**
     * 删除包时费率
     *
     * @param requestTicket
     * @param placeId
     * @param ruleId
     * @return
     */
    @GetMapping("/delete")
    public GenericResponse<SimpleDTO> deleteBillingRulePackageTime(@RequestHeader(value = "request_ticket") String requestTicket,
                                                                   @RequestParam String placeId, @RequestParam String ruleId) {

        if (!stringRedisTemplate.delete(requestTicket)) {
            return new GenericResponse<>(ServiceCodes.NO_TICKET);
        }

        if (StringUtils.isEmpty(placeId) || StringUtils.isEmpty(ruleId)) {
            return new GenericResponse<>(ServiceCodes.BAD_PARAM);
        }

        Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(placeId, ruleId);
        if (billingRulePackageTimeOpt.isPresent()) {
            BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();
            billingRulePackageTimeService.delete(billingRulePackageTime);

            GoodsBO goods = new GoodsBO();
            goods.setPackageRuleId(billingRulePackageTime.getRuleId());
            goods.setPlaceId(billingRulePackageTime.getPlaceId());
            goods.setGoodsName(billingRulePackageTime.getRuleName());
            goods.setDeleted(1);
            goods.setGoodsCategory(4);
            String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
            goodsApi.saveGoods(requestTicket2,goods);
            return new GenericResponse<>(ServiceCodes.NO_ERROR);
        }

        return new GenericResponse<>(ServiceCodes.NOT_FOUND);
    }

    // 在线包时支付后回调方法
    @ResponseBody
    @PostMapping("/notify")
    public String notify(@RequestHeader(value = "request_ticket") String requestTicket,
                         @RequestParam(value = "orderId") String orderId,
                         @RequestParam(value = "payType") PayType payType,
                         @RequestParam(required = false) Integer poundage) {

        log.info("request_ticket:::{}", requestTicket);
        log.info("online package go into notify:::orderId:{}:::poundage={}", orderId,poundage);
        if (Boolean.FALSE.equals(stringRedisTemplate.delete(requestTicket)) || StringUtils.isEmpty(orderId)) {
            log.warn("请求异常");
            return "ERROR";
        }

        // 记录充值操作日志
        Optional<LogTopup> optLogTopup = logTopupService.findByOrderId(orderId);
        if (!optLogTopup.isPresent()) {
            try {
                //扫码枪支付会导致回调太快查询不到logtopup，等待0.5秒
                Thread.sleep(500);
                optLogTopup = logTopupService.findByOrderId(orderId);
                if (!optLogTopup.isPresent()) {
                    log.warn("充值记录不存在, orderId:{}", orderId);
                    return "ERROR";
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        LogTopup logTopup = optLogTopup.get();
        if (logTopup.getStatus() == 3) {
            log.info("转包时已完成, orderId:{}", orderId);
            return "SUCCESS";
        }
        if (logTopup.getRefundStatus() == 2) {
            log.info("该订单已退款, orderId:{}", orderId);
            return "ERROR";
        }
        if (logTopup.getStatus() != 1) {
            log.warn("充值状态异常, orderId:{}", orderId);
            return "ERROR";
        }

        OrdersBO ordersBO = new OrdersBO();
        ordersBO.setOrderId(orderId);
        ordersBO.setStatus(2);
        int fee = StringUtils.isEmpty(poundage) ? 0 : poundage;
        log.info("包时支付成功手续费::::::::{}",poundage);
        ordersBO.setFee(fee);
        ordersBO.setOriginalFee(fee);//同步保存订单初始的手续费
        ordersBO.setPlaceId(logTopup.getPlaceId());
        ordersApi.saveOrder(ordersBO);

        logTopup.setTopupTime(LocalDateTime.now());
        logTopup.setUpdated(LocalDateTime.now());
        logTopup.setStatus(2);
        logTopup.setPayType(payType);
        logTopupService.save(logTopup);
        log.info("充值记录更新成功,等待执行转包时 orderId:{}", orderId);

        // 查询计费卡信息并判断余额
        Optional<BillingCard> optBillingCard = billingCardService.findByPlaceIdAndCardId(logTopup.getPlaceId(),
                logTopup.getCardId());
        if (!optBillingCard.isPresent()) {
            log.info("在线包时转换失败，计费卡信息未找到, orderId:{}, orderId:{}", orderId, logTopup.getCardId());
            return "ERROR";
        }
        BillingCard billingCard = optBillingCard.get();
        SourceType sourceType = logTopup.getSourceType();
        // 获取预包时开始时间、结束时间
        LocalDateTime futureStartTime = null;
        LocalDateTime futureEndTime = null;
        LocalDateTime nowDateTime = LocalDateTime.now();
        boolean  effectiveImmediately = false; // 包时是否立即生效

        // 查询包时规则信息
        Optional<BillingRulePackageTime> billingRulePackageTimeOpt = billingRulePackageTimeService.findByPlaceIdAndRuleId(logTopup.getPlaceId(), logTopup.getRuleId());
        if (!billingRulePackageTimeOpt.isPresent()) {
            log.info("包时规则不存在, orderId:{}, ruleId:{}", orderId, logTopup.getRuleId());
            String ERROR = refund(orderId, logTopup, sourceType, billingCard);
            if (ERROR != null) return ERROR;
            return "ERROR";
        }
        BillingRulePackageTime billingRulePackageTime = billingRulePackageTimeOpt.get();

        // 查询当前上机状态
        Optional<BillingOnline> optBillingOnline = billingOnlineService.findUnfinishedByPlaceIdAndCardId(logTopup.getPlaceId(),
                logTopup.getCardId());
        if (!optBillingOnline.isPresent()) {

            // 不管是否在线、是否是已经在包时段或包时长，只要PackageTimeReserve中已经存在一条status为0的记录，则不允许预包时
            Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                    .findUnusedByPlaceIdAndCardId(logTopup.getPlaceId(), billingCard.getCardId());
            if (packageTimeReserveOpt.isPresent()) {
//                log.info("在线包时回调.........已经有一个未使用的包时，不能继续预包时,cardId:::{}", billingCard.getCardId());
//                String ERROR = refund(orderId, logTopup, sourceType, billingCard);
//                if (ERROR != null) return ERROR;
//                return "ERROR";
                PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get(); //清除掉预包时
                packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(packageTimeReserve.getPlaceId(),packageTimeReserve.getCardId(),packageTimeReserve.getRuleId());
            }
            // 预包时
            if (billingRulePackageTime.getPackageFlag() == 2) {
                // 包时长
                futureStartTime = nowDateTime;
//                futureEndTime = nowDateTime.plusMinutes(billingRulePackageTime.getDurationTime());
                futureEndTime = PackageTimeAlgorithm.getNextTime(nowDateTime,billingRulePackageTime);
            } else {
                // 包时段
                futureStartTime = PackageTimeAlgorithm.getStartTime(nowDateTime,billingRulePackageTime);
                futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);

                if(nowDateTime.isAfter(futureStartTime) && nowDateTime.isAfter(futureEndTime)){
                    futureStartTime = futureStartTime.plusDays(1);
                    futureEndTime = futureEndTime.plusDays(1);
                }
            }

            // 预包时入库
            packageTimeReserveService.futurePackageTime(futureStartTime, futureEndTime, billingCard, billingRulePackageTime,
                    logShiftService.getShiftId(billingCard.getPlaceId()), 0, 3, billingRulePackageTime.getPrice(), 0, 0, orderId);

            // 写入预包时操作记录
            logOperationService.addPackageTimeOperation(sourceType,
                    1,
                    3,
                    0,
                    billingCard,
                    null,
                    billingRulePackageTime,
                    logShiftService.getShiftId(billingCard.getPlaceId()),
                    null);

            // 未登录情况下预包时充值记录更新为完成
            logTopup.setStatus(3);
            logTopup.setUpdated(LocalDateTime.now());
            logTopupService.save(logTopup);

            ordersBO.setStatus(3); //已完成
            // 获取返回结果order，在saveBalanceDetails的时候传入clientId
            GenericResponse<ObjDTO<OrdersBO>> orderResponse = ordersApi.saveOrder(ordersBO);
            if (orderResponse != null && orderResponse.isResult()) {
                ordersBO = orderResponse.getData().getObj();
            }

            try {
                String description = "预包时-定额套餐-生效人id:" + logTopup.getCardId();
                balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,ordersBO.getClientId(),2,1,logTopup.getCashAmount(),0, description,logTopup.getCashAmount(),0,null);
                String descriptionData = "预包时-定额包时套餐[" + billingRulePackageTime.getRuleName() + "]-生效人id:" + logTopup.getCardId();
                balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,ordersBO.getClientId(),2,0,logTopup.getCashAmount(),0, descriptionData,0,0,null);
            }catch (Exception e){
                log.info("卡号:::{} 网费明细记录预包时异常",logTopup.getCardId());
                e.printStackTrace();
            }

            return "SUCCESS";
        }
        BillingOnline billingOnline = optBillingOnline.get();
        // 以下是在线的包时

        Optional<BillingRuleCommon> billingRuleCommonOpt = billingRuleCommonService.billingRuleCommons(logTopup.getPlaceId(), billingOnline.getAreaId(),billingOnline.getCardTypeId());
        if (!billingRuleCommonOpt.isPresent()) {
            log.info("当前标准费率未找到, orderId:{}, ruleId:{}", orderId, billingOnline.getRuleId());
            String ERROR = refund(orderId, logTopup, sourceType, billingCard);
            if (ERROR != null) return ERROR;
            return "ERROR";
        }

        // 查询登录信息
        Optional<LogLogin> optLogLogin = logLoginService.findOnlineByPlaceIdAndCardIdAndBillingTime(logTopup.getPlaceId(), logTopup.getCardId(), billingOnline.getBillingTime());
        if (!optLogLogin.isPresent()) {
            log.info("在线包时转换失败,登入信息未找到 orderId:{}, orderId:{}", orderId, logTopup.getCardId());
            String ERROR = refund(orderId, logTopup, sourceType, billingCard);
            if (ERROR != null) return ERROR;
            return "ERROR";
        }
        LogLogin logLogin = optLogLogin.get();
        LocalDateTime nextTime = billingOnline.getNextTime();
        String placeId = billingCard.getPlaceId();
        String cardId = billingCard.getCardId();
        String currClientId = billingOnline.getClientId();
        String newClientId = logTopup.getClientId();

        // 包时长
        if (billingRulePackageTime.getPackageFlag() == 2) {
            nextTime = PackageTimeAlgorithm.getNextTime(nextTime,billingRulePackageTime);

            // 包时换机补差价，nextTime计算应把上一个包时失效；
            if (!currClientId.equals(newClientId) && logTopup.getCashAmount() != billingRulePackageTime.getPrice()) {
                nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime,billingRulePackageTime);
            }

            // 如果是正在累计包时中,则不能操作立即包时
            if (billingOnline.getAccFlag() == 2) {
                // 退款
                String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                if (ERROR != null) return ERROR;
                return "ERROR";
            }
            if (billingRulePackageTime.getDurationRepeatedTime() == 0) {
                // 开启了重复购买叠加时长
//                nextTime = nowDateTime.plusMinutes(billingRulePackageTime.getDurationTime());
                nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime,billingRulePackageTime);
            }
        } else {
            // 包时段
            int nowMinute = nowDateTime.getMinute();
            float minute = new BigDecimal((float) nowMinute / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
            float currHours = nowDateTime.getHour() + minute;

            if (billingRulePackageTime.getEndTime() - billingRulePackageTime.getStartTime() > 0) { // 同一天
                if (currHours < billingRulePackageTime.getStartTime() || currHours >= billingRulePackageTime.getEndTime()) {
                    // 不管是否在线、是否是已经在包时段或包时长，只要PackageTimeReserve中已经存在一条status为0的记录，则不允许预包时
                    Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                            .findUnusedByPlaceIdAndCardId(placeId, cardId);
                    if (packageTimeReserveOpt.isPresent()) {
//                        log.info("balancePackageTime.........已经有一个未使用的包时，不能继续预包时,cardId:::{}", cardId);
//                        // 退款
//                        String ERROR = refund(orderId, logTopup, sourceType, billingCard);
//                        if (ERROR != null) return ERROR;
//                        return "ERROR";
                        PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get(); //清除掉预包时
                        packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(packageTimeReserve.getPlaceId(),packageTimeReserve.getCardId(),packageTimeReserve.getRuleId());

                    }
                    // 在线预包时,并且是不跨天的
                    futureStartTime = PackageTimeAlgorithm.getStartTime(nowDateTime,billingRulePackageTime);
                    futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
                    if(nowDateTime.isAfter(futureStartTime) && nowDateTime.isAfter(futureEndTime)){
                        futureStartTime = futureStartTime.plusDays(1);
                        futureEndTime = futureEndTime.plusDays(1);
                    }
                    // 预包时入库
                    packageTimeReserveService.futurePackageTime(futureStartTime, futureEndTime, billingCard, billingRulePackageTime,
                            logShiftService.getShiftId(billingCard.getPlaceId()), 0, 3, billingRulePackageTime.getPrice(), 0, 0, orderId);

                    // 写入预包时操作记录
                    logOperationService.addPackageTimeOperation(sourceType,
                            1,
                            3,
                            0,
                            billingCard,
                            null,
                            billingRulePackageTime,
                            logShiftService.getShiftId(billingCard.getPlaceId()),
                            null);

                    logTopup.setStatus(3);
                    logTopup.setUpdated(LocalDateTime.now());
                    logTopupService.save(logTopup);

                    ordersBO.setStatus(3); //已完成
                    ordersApi.saveOrder(ordersBO);
                    // 正在上机在线充值预包时完成后，写一条网费余额明细
                    String description = "在线-定额套餐-生效人id:" + billingOnline.getCardId();
                    balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,1,logTopup.getCashAmount(),0, description,logTopup.getCashAmount(),0, null);

                    String descriptionData = "在线-定额包时套餐[" + billingRulePackageTime.getRuleName() + "]-生效人id:" + billingOnline.getCardId();
                    balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,0,logTopup.getCashAmount(),0, descriptionData,0,0, null);

                    if(null != billingOnline && (sourceType.equals(SourceType.WECHAT) || sourceType.equals(SourceType.MINIAPP)) ){
                        sendNotifyToCashier(billingOnline,sourceType);
                    }
                    return "SUCCESS";
                } else {
                    // 在线立即包时,并且是不跨天的
                    nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime, billingRulePackageTime);
                    // 如果是正在累计包时中,则不能操作立即包时
                    if (billingOnline.getAccFlag() == 2) {
                        // 退款
                        String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                        if (ERROR != null) return ERROR;
                        return "ERROR";
                    }
                    effectiveImmediately = true;
                }
            } else { // 跨0点包时
                if (currHours < billingRulePackageTime.getStartTime() && currHours >= billingRulePackageTime.getEndTime()) {
                    // 不管是否在线、是否是已经在包时段或包时长，只要PackageTimeReserve中已经存在一条status为0的记录，则不允许预包时
                    Optional<PackageTimeReserve> packageTimeReserveOpt = packageTimeReserveService
                            .findUnusedByPlaceIdAndCardId(placeId, cardId);
                    if (packageTimeReserveOpt.isPresent()) {
//                        log.info("balancePackageTime.........已经有一个未使用的包时，不能继续预包时,cardId:::{}", cardId);
//                        // 退款
//                        String ERROR = refund(orderId, logTopup, sourceType, billingCard);
//                        if (ERROR != null) return ERROR;
//                        return "ERROR";
                        PackageTimeReserve packageTimeReserve = packageTimeReserveOpt.get(); //清除掉预包时
                        packageTimeReserveService.updateInvalidationByPlaceIdAndCardIdAndRuleId(packageTimeReserve.getPlaceId(),packageTimeReserve.getCardId(),packageTimeReserve.getRuleId());
                    }
                    // 在线预包时,并且是跨天的
                    futureStartTime = PackageTimeAlgorithm.getStartTime(nowDateTime,billingRulePackageTime);
                    futureEndTime = PackageTimeAlgorithm.getNextTime(futureStartTime, billingRulePackageTime);
                    // 预包时入库
                    packageTimeReserveService.futurePackageTime(futureStartTime, futureEndTime, billingCard, billingRulePackageTime,
                            logShiftService.getShiftId(billingCard.getPlaceId()), 0, 3, billingRulePackageTime.getPrice(), 0, 0, orderId);

                    // 写入预包时操作记录
                    logOperationService.addPackageTimeOperation(sourceType,
                            1,
                            3,
                            0,
                            billingCard,
                            null,
                            billingRulePackageTime,
                            logShiftService.getShiftId(billingCard.getPlaceId()),
                            null);

                    logTopup.setStatus(3);
                    logTopup.setUpdated(LocalDateTime.now());
                    logTopupService.save(logTopup);

                    ordersBO.setStatus(3); //已完成
                    ordersApi.saveOrder(ordersBO);
                    String description = "在线-定额套餐-生效人id:" + billingOnline.getCardId();
                    balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,1,logTopup.getCashAmount(),0, description,logTopup.getCashAmount(),0, null);

                    String descriptionData = "在线-定额包时套餐[" + billingRulePackageTime.getRuleName() + "]-生效人id:" + billingOnline.getCardId();
                    balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,0,logTopup.getCashAmount(),0, descriptionData,0,0, null);

                    if(null != billingOnline && (sourceType.equals(SourceType.WECHAT) || sourceType.equals(SourceType.MINIAPP)) ){
                        sendNotifyToCashier(billingOnline,sourceType);
                    }
                    return "SUCCESS";
                } else {
                    // 在线立即包时,并且是跨天的
                    nextTime = PackageTimeAlgorithm.getNextTime(nowDateTime, billingRulePackageTime);
                    // 如果是正在累计包时中,则不能操作立即包时
                    if (billingOnline.getAccFlag() == 2) {
                        // 退款
                        String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                        if (ERROR != null) return ERROR;
                        return "ERROR";
                    }
                    effectiveImmediately = true;
                }
            }
        }

        LocalDateTime now = LocalDateTime.now();
        if (!currClientId.equals(newClientId) && logTopup.getCashAmount() != billingRulePackageTime.getPrice()) {
            // 包时补差价换机
            // 获取最新的会员卡余额
            BillingCard billingCardCurr = null;
            if (!"1000".equals(billingCard.getCardTypeId()) && !"1002".equals(billingCard.getCardTypeId())) {
                Optional<BillingCard> newCard = billingCardService.findByPlaceIdAndIdNumberAndNotDeleted(placeId, billingCard.getIdNumber(), 0);
                if (!newCard.isPresent()) {
                    String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                    if (ERROR != null) return ERROR;
                    return "ERROR";
                }
                billingCardCurr = newCard.get();
            } else {
                Optional<BillingCard> optBillingCardCurr = billingCardService.findByPlaceIdAndCardId(placeId, billingOnline.getCardId());
                if (!optBillingCardCurr.isPresent()) {
                    String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                    if (ERROR != null) return ERROR;
                    return "ERROR";
                }
                billingCardCurr = optBillingCardCurr.get();
            }

            // 当前使用的包时规则
            Optional<BillingRulePackageTime> oldBillingRulePackageTimeOpt = billingRulePackageTimeService
                    .findByPlaceIdAndRuleId(billingOnline.getPlaceId(), billingOnline.getRuleId());
            if (!oldBillingRulePackageTimeOpt.isPresent()) {
                String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                if (ERROR != null) return ERROR;
                return "ERROR";
            }
            BillingRulePackageTime oldBillingRulePackageTime = oldBillingRulePackageTimeOpt.get();

            // 查询区域
            GenericResponse<ObjDTO<PlaceClientBO>> genericResponse = placeServerService.findClientByPlaceIdAndClientId(logLogin.getPlaceId(), newClientId);
            if (!genericResponse.isResult()) {
                String ERROR = refund(orderId, logTopup, sourceType, billingCard);
                if (ERROR != null) return ERROR;
                return "ERROR";
            }
            PlaceClientBO placeClientBO = genericResponse.getData().getObj();

            balanceDetailsAlgorithm.saveBalanceDetails(LocalDateTime.now(), billingCardCurr, newClientId,5,0, logTopup.getCashAmount(),0, "包时跨区换机补差",0,0, null);

            // 更新登录信息
            logLogin.setClientId(newClientId);
            logLogin.setLastClientId(billingOnline.getClientId());
            logLogin.setConsumptionTotal(logLogin.getConsumptionTotal() + billingOnline.getDeduction());
            logLogin.setConsumptionCashTotal(logLogin.getConsumptionCashTotal() + billingOnline.getDeductionCash());
            logLogin.setConsumptionPresentTotal(logLogin.getConsumptionPresentTotal() + billingOnline.getDeductionPresent());
            logLogin.setTotalAccount(billingCard.getTotalAccount());
            logLoginService.save(logLogin);

            // Online表生成一条记录
            BillingOnline nowBillingOnline = new BillingOnline();
            nowBillingOnline.setPlaceId(placeId);
            nowBillingOnline.setClientId(newClientId);
            nowBillingOnline.setAreaId(placeClientBO.getAreaId());
            nowBillingOnline.setCardId(billingOnline.getCardId());
            nowBillingOnline.setRuleId(billingRulePackageTime.getRuleId());
            nowBillingOnline.setIdNumber(billingOnline.getIdNumber());
            nowBillingOnline.setIdName(billingOnline.getIdName());
            nowBillingOnline.setCardTypeId(billingOnline.getCardTypeId());
            nowBillingOnline.setLoginId(billingOnline.getLoginId());
            nowBillingOnline.setDeduction(logTopup.getCashAmount()); // 把换机补差的钱
            nowBillingOnline.setDeductionCash(logTopup.getCashAmount());
            nowBillingOnline.setDeductionPresent(0);
            nowBillingOnline.setNextTime(nextTime);
            nowBillingOnline.setBillingTime(billingOnline.getBillingTime());
            nowBillingOnline.setFinished(0);
            nowBillingOnline.setTimerFlag(0);
            nowBillingOnline.setCreated(now);
            nowBillingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
            nowBillingOnline.setCommonPrice(logTopup.getCashAmount());
            nowBillingOnline.setLoginId(logLogin.getLoginId());
            nowBillingOnline.setAccFlag(billingOnline.getAccFlag());
            nowBillingOnline.setPackagePayFlag(billingOnline.getPackagePayFlag());
            nowBillingOnline.setIsInvite(billingOnline.getIsInvite());
            billingOnlineService.save(nowBillingOnline);

            // 结束当前BillingOnline的计费
            billingOnline.setFinished(1);
            billingOnline.setTimerFlag(0);
            billingOnline.setUpdated(now);
            billingOnlineService.save(billingOnline);

            // 未登录情况下预包时充值记录更新为完成
            logTopup.setStatus(3);
            logTopup.setUpdated(LocalDateTime.now());
            logTopupService.save(logTopup);

            ordersBO.setStatus(3); //已完成
            ordersApi.saveOrder(ordersBO);
            
            // 更新
            logHbService.stopBilling(placeId, billingOnline.getClientId());
            logHbService.startBilling(placeId, newClientId);

            logOperationService.addExchangeClientOperation(SourceType.CLIENT, billingCardCurr, billingOnline, nowBillingOnline,
                    null, logLogin);
            placementService.checkRefreshPlacementDate(billingOnline.getPlaceId(),billingOnline.getClientId()+","+nowBillingOnline.getClientId(),"6",
                    billingOnline.getIdName(),billingOnline.getIdNumber(),billingOnline.getCardId());
            logOperationService.addConvertBillingRuleOperation(SourceType.CLIENT, 3, 0,
                    0, billingCard, billingOnline, oldBillingRulePackageTime, billingRulePackageTime, null, logLogin);
            // 保存轮询数据
            logLoginService.saveLoginPolling(placeId, newClientId, billingOnline.getIdNumber(), SourceType.WECHAT);
            return "SUCCESS";
        }

        // 更新billingOnline
        billingOnline.setUpdated(nowDateTime);
        billingOnline.setRuleId(billingRulePackageTime.getRuleId());
        billingOnline.setNextTime(nextTime);
        // billingOnline.setBillingTime(nowDateTime);
        billingOnline.setDeduction(billingOnline.getDeduction() + billingRulePackageTime.getPrice());
//        billingOnline.setDeductionCash(billingOnline.getDeductionCash());
//        billingOnline.setDeductionPresent(billingOnline.getDeductionPresent());
        billingOnline.setCommonPrice(billingRulePackageTime.getPrice());
        billingOnline.setPackageFlag(billingRulePackageTime.getPackageFlag());
        billingOnline.setAccFlag(0);
        billingOnline.setPackagePayFlag(3);
        billingOnlineService.save(billingOnline);
        log.info("balancePackageTime包时成功:::::::nextTime::::" + nextTime);
        logOperationService.addPackageTimeOperation(sourceType, 0, 1, 0, billingCard, billingOnline, billingRulePackageTime, null, logLogin);
        logOperationService.addConvertBillingRuleOperation(sourceType, 1, billingOnline.getCommonPrice(), 0, billingCard, billingOnline, null, billingRulePackageTime, null, logLogin);
        logTopup.setStatus(3);
        logTopup.setUpdated(LocalDateTime.now());
        logTopupService.save(logTopup);
        log.info("转包时已完成, orderId:{}", orderId);

        ordersBO.setStatus(3); //已完成
        ordersApi.saveOrder(ordersBO);
        placementService.refreshPlacementDate(billingCard.getPlaceId(),billingOnline.getClientId(),"3",billingCard.getIdName(),billingCard.getIdNumber(),billingCard.getCardId());

        // 在线包时支付成功后，写一条网费余额明细
        String description = "在线-定额套餐-生效人id:" + billingOnline.getCardId();
        balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,1,logTopup.getCashAmount(),0, description,logTopup.getCashAmount(),0, null);


        // 网费余额明细里记一笔这个包时的使用记录
        String descriptionData = "在线-定额包时赛餐[" + billingRulePackageTime.getRuleName() + "]-生效人id:" + billingOnline.getCardId();
        balanceDetailsAlgorithm.saveBalanceDetails(nowDateTime,billingCard,billingOnline.getClientId(),2,0,logTopup.getCashAmount(),0, descriptionData,0,0, null);

        // 如果包时在生效时间范围内，网费余额明细里记一笔这个包时的时间可用范围/剩余有效时间
        if (effectiveImmediately) {
            LocalDateTime nowTime = LocalDateTime.now();
            String descriptionUsed = "";
            if (billingRulePackageTime.getPackageFlag() == 1) {
                LocalDateTime startTime = PackageTimeAlgorithm.getStartTime(nowDateTime, billingRulePackageTime); // 包时段的开始时间
                LocalDateTime endTime = PackageTimeAlgorithm.getNextTime(startTime, billingRulePackageTime);    // 包时段的结束时间
//                packageUsed.setCreated(startTime); // 包时段的开始时间
                Duration duration = Duration.between(nowDateTime, endTime);
                long minutes = duration.toMinutes();  // 剩余 分钟
//                packageUsed.setDescription(billingRulePackageTime.getRuleName() + "-" + minutes + "分钟");
                nowTime = startTime ;
                descriptionUsed = billingRulePackageTime.getRuleName() + "-" + minutes + "分钟";
            } else {
                // 包时长
//                packageUsed.setCreated(nowDateTime);//当下时间
//                packageUsed.setDescription(billingRulePackageTime.getRuleName() + "-" + billingRulePackageTime.getDurationTime() + "分钟");
                descriptionUsed = billingRulePackageTime.getRuleName() + "-" + billingRulePackageTime.getDurationTime() + "分钟";
            }

            balanceDetailsAlgorithm.saveBalanceDetails(nowTime,billingCard,billingOnline.getClientId(),2,0,logTopup.getCashAmount(),0, descriptionUsed,0,1, null);

        }
        if(null != billingOnline && (sourceType.equals(SourceType.WECHAT) || sourceType.equals(SourceType.MINIAPP)) ){
            sendNotifyToCashier(billingOnline,sourceType);
        }
        return "SUCCESS";
    }

    private String refund(String orderId, LogTopup logTopup, SourceType sourceType, BillingCard billingCard) {
        String requestTicket2 = Dim4StringUtils.getUUIDWithoutHyphen();
        stringRedisTemplate.opsForValue().set(requestTicket2, requestTicket2, 1, TimeUnit.MINUTES);
        GenericResponse<ObjDTO<PaymentRefundOrderBO>> result = paymentServerService.refundPaymentOrder(requestTicket2, logTopup.getPlaceId(), orderId, logTopup.getCashAmount());
        if (result.getCode() != ServiceCodes.PAYMENT_REFUND_SUCC.getCode()) {
            log.info("在线包时退款失败, orderId:{}, ruleId:{}, message:{}", orderId, logTopup.getRuleId(), result.getMessage());
            return "ERROR";
        }
        PaymentRefundOrderBO paymentRefundOrderBO = result.getData().getObj();


        // 更改订单状态
        logTopup.setRefundStatus(2);
        logTopup.setUpdated(LocalDateTime.now());
        logTopupService.save(logTopup);

        // 写入退款记录
        LogRefund logRefund = new LogRefund();
        logRefund.setPlaceId(logTopup.getPlaceId());
        logRefund.setIdNumber(logTopup.getIdNumber());
        logRefund.setIdName(logTopup.getIdName());
        logRefund.setCardTypeId(logTopup.getCardTypeId());
        logRefund.setCardTypeName(logTopup.getCardTypeName());
        logRefund.setCardId(logTopup.getCardId());
        logRefund.setRefundDesc(paymentRefundOrderBO.getRefundDesc());
        logRefund.setReturnMessage(paymentRefundOrderBO.getReturnMessage());
        logRefund.setRefundOrderId(paymentRefundOrderBO.getRefundOrderId());
        logRefund.setOnlineRefund(paymentRefundOrderBO.getRefundAmount());
        logRefund.setLdorderid(paymentRefundOrderBO.getLdorderid());
        logRefund.setOperatorName("在线包时失败--退款");
        logRefund.setSourceType(sourceType);
        logRefund.setShiftId(logTopup.getShiftId());
        logRefund.setCreated(LocalDateTime.now());
        logRefund.setRefundType(1);
        logRefund.setStatus(1);
        logRefundService.save(logRefund);

        OrdersBO ordersBO = new OrdersBO();
        ordersBO.setPlaceId(logRefund.getPlaceId());
        ordersBO.setOrderId(orderId);
        ordersBO.setStatus(5); //已退款
        ordersBO.setPayRefundId(paymentRefundOrderBO.getRefundOrderId());
        ordersApi.saveOrder(ordersBO);
        logOperationService.addRefundOperation(sourceType, RefundType.TOPUP, billingCard, logRefund.getOnlineRefund(), 0, 0, logShiftService.getShiftId(billingCard.getPlaceId()));
        return null;
    }


    private void sendNotifyToCashier(BillingOnline billingOnline, SourceType sourceType) {
        GenericResponse<ObjDTO<PollingBO>> response = notifyServerService.savePolling(billingOnline.getPlaceId(), billingOnline.getClientId(), billingOnline.getIdNumber(), BusinessType.ONLINE_PACKAGE_TIME);

        if (response.isResult()) {
            PollingBO polling = response.getData().getObj();
            BookSeatsBusinessBO bookSeatsBusinessBO = new BookSeatsBusinessBO();
            bookSeatsBusinessBO.setBusinessId(polling.getCashierBusinessId());
            bookSeatsBusinessBO.setPlaceId(billingOnline.getPlaceId());
            bookSeatsBusinessBO.setClientId(billingOnline.getClientId());
            bookSeatsBusinessBO.setIdNumber(billingOnline.getIdNumber());
            bookSeatsBusinessBO.setCreated(LocalDateTime.now().toString());
            bookSeatsBusinessBO.setSourceType(sourceType);
            bookSeatsBusinessBO.setBusinessType(BusinessType.ONLINE_PACKAGE_TIME);

            String requestTicket = Dim4StringUtils.getUUIDWithoutHyphen();
            stringRedisTemplate.opsForValue().set(requestTicket, requestTicket, 1, TimeUnit.MINUTES);
            GenericResponse<ObjDTO<BookSeatsBusinessBO>> response1 = businessDataApi.pushBookSeats(requestTicket, bookSeatsBusinessBO);
            if (!response1.isResult()) {
                log.warn("businessDataApi.pushBookSeats error: {}", response1.getCode() + " " + response1.getMessage());
            }else{
                log.warn("businessDataApi.pushBookSeats access: {}", response1.getCode() + " " + response1.getMessage());
            }
        } else {
            log.warn("notifyServerService.savePolling error: {}", response.getCode() + " " + response.getMessage());
        }
    }

    /**
     * 获取区域、卡类型名称
     * @param bos
     * @param placeAreaBOs
     * @param billingCardTypes
     * @return
     */
    private List<BillingRulePackageTimeBO> getAreaNameCardTypeName(List<BillingRulePackageTimeBO> bos,
                                                                   List<PlaceAreaBO> placeAreaBOs,
                                                                   List<BillingCardType> billingCardTypes) {
        for (BillingRulePackageTimeBO billingRulePackageTimeBO : bos) {

            String areaNames = "";
            List<String> areaIds = Arrays.asList(billingRulePackageTimeBO.getAreaIds().split(","));
            for (PlaceAreaBO placeArea : placeAreaBOs) {
                for (String id : areaIds) {
                    if (placeArea.getAreaId().equals(id)) {
                        areaNames += areaIds.size() > 1 ? StringUtils.isEmpty(areaNames) ? placeArea.getAreaName() : "," + placeArea.getAreaName() : placeArea.getAreaName();
                    }
                }
            }

            String cardTypeNames = "";
            List<String> cardTypeIds = Arrays.asList(billingRulePackageTimeBO.getCardTypeIds().split(","));
            for (BillingCardType billingCardType : billingCardTypes) {
                for (String cardTypeId : cardTypeIds) {
                    if (billingCardType.getCardTypeId().equals(cardTypeId)) {
                        cardTypeNames += cardTypeIds.size() > 1 ? StringUtils.isEmpty(cardTypeNames) ? billingCardType.getTypeName() : "," + billingCardType.getTypeName() : billingCardType.getTypeName();
                    }
                }
            }

            billingRulePackageTimeBO.setAreaNames(areaNames);
            billingRulePackageTimeBO.setCardTypeNames(cardTypeNames);
        }
        return bos;
    }
}
