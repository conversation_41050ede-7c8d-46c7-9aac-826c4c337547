package com.rzx.dim4.billing.scheduler;

import com.rzx.dim4.base.response.GenericResponse;
import com.rzx.dim4.billing.entity.BillingOnline;
import com.rzx.dim4.billing.entity.invite.InviteOnline;
import com.rzx.dim4.billing.service.*;
import com.rzx.dim4.billing.service.Invite.InviteOnlineService;
import com.rzx.dim4.billing.service.impl.client.ClientLogoutServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class BillingInviteOnlineScheduler {

	@Autowired
	BillingOnlineService billingOnlineService;

	@Autowired
	BillingCardService billingCardService;

	@Autowired
	ClientLogoutServiceImpl clientLogoutServiceImpl;

	@Autowired
	StringRedisTemplate stringRedisTemplate;

	@Autowired
	InviteOnlineService inviteOnlineService;

	String serverBillingInviteOnlineLockKey = "[billing]server_billing_invite_lock";

	/**
	 * 每5分钟查询是否有请客上网请客人下机，被邀请人未下机定时器
	 */
	@Scheduled(initialDelay = 1000 * 30, fixedRate = 1000 * 60 * 5)
	public void serverBillingRoom() {

		log.info("中心请客上网查询邀请人下机，被邀请人未下机开始, 获取锁:::" + serverBillingInviteOnlineLockKey);
		boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(serverBillingInviteOnlineLockKey, "locked");
		if (locked) {
			log.info("获取成功，设置5分钟TTL");
			stringRedisTemplate.expire(serverBillingInviteOnlineLockKey, 5, TimeUnit.MINUTES);
		} else {
			log.info("获取失败，查询锁状态");
			long expired = stringRedisTemplate.getExpire(serverBillingInviteOnlineLockKey, TimeUnit.SECONDS);
			log.info("没有获得锁, 停止中心请客上网查询未下机任务, TTL剩余:::" + expired);
			if (expired == -1L) {
				log.info("上次TTL设置失败，重新设置5分钟TTL");
				stringRedisTemplate.expire(serverBillingInviteOnlineLockKey, 5, TimeUnit.MINUTES);
			}
			log.info("中心请客上网查询未下机任务结束！！！");
			return;
		}

		// 查询所有请客人下机 被请客人在线的记录
		List<InviteOnline> notDismountedInvite = inviteOnlineService.queryNotDismountedInvite();

		if (CollectionUtils.isEmpty(notDismountedInvite)) {
			log.info("请客上网查询未下机任务查询结果:::中心没有找到请客上网未下机的被请客人");
			log.info("释放锁:::" + serverBillingInviteOnlineLockKey);
			stringRedisTemplate.delete(serverBillingInviteOnlineLockKey);
			return; // 没有未下机的副卡
		}
		log.info("中心找到请客上网被请客人未下机:::" + notDismountedInvite.size());

		for (InviteOnline inviteOnline : notDismountedInvite) { // 循环处理
			String placeId = inviteOnline.getPlaceId();
			String cardId = inviteOnline.getCardId();

			// 查询BillingOnline
			Optional<BillingOnline> billingOnlineOpt = billingOnlineService.findUnfinishedByPlaceIdAndCardId(placeId, cardId);
			if (!billingOnlineOpt.isPresent()) {
				inviteOnline.setStatus(2);
				inviteOnline.setUpdated(LocalDateTime.now());
				inviteOnlineService.save(inviteOnline);
				log.info("{}-{}【请客上网】处理逻辑......未找到被请客人上机记录信息................", placeId, cardId);
				continue;
			}
			String clientId = billingOnlineOpt.get().getClientId();

			log.info("{}-{}-{}【请客上网】处理逻辑......", placeId, clientId, cardId);
			List<String> loginOutParamList = new ArrayList<>();
			loginOutParamList.add(placeId);
			loginOutParamList.add(clientId);
			loginOutParamList.add(cardId);
			GenericResponse<?> repsLogout = clientLogoutServiceImpl.doService(loginOutParamList);
			if (!repsLogout.isResult()) {
				log.error("{}-{}-{}【请客上网】客户端跟随请客人自动结账失败，调用ClientLogout返回" + repsLogout.getCode() + "!!!", placeId,
						clientId, cardId);
				continue;
			}
			// 在clientLogoutServiceImpl已经把inviteOnline改了.
//			inviteOnline.setStatus(2);
//			inviteOnline.setUpdated(nowTime);
//			inviteOnlineService.save(inviteOnline);
			log.info("{}-{}-{}【请客上网】处理逻辑结束.", placeId, clientId, cardId);
		}
		log.info("中心请客上网查询未下机任务结束！！！释放锁:::" + serverBillingInviteOnlineLockKey);
		stringRedisTemplate.delete(serverBillingInviteOnlineLockKey);
	}

}