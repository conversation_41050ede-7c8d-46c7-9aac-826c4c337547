package com.rzx.dim4.billing.service;

import com.rzx.dim4.billing.entity.TempWanxiangConsume;
import com.rzx.dim4.billing.repository.TempWanxiangConsumeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 临时充值记录
 */
@Service
public class TempWanxiangConsumeService {

    @Autowired
    TempWanxiangConsumeRepository tempWanxiangConsumeRepository;

    public void saveAll(List list) {
        tempWanxiangConsumeRepository.saveAll(list);
    }

    public List<TempWanxiangConsume> findByPlaceIdAndCardId(String placeId, String cardId) {
        return tempWanxiangConsumeRepository.findByPlaceIdAndCardId(placeId, cardId);
    }


}
