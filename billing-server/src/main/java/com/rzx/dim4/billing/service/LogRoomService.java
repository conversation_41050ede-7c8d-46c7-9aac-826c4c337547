package com.rzx.dim4.billing.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.rzx.dim4.billing.entity.LogRoom;
import com.rzx.dim4.billing.repository.LogRoomRepository;

/**
 * 
 * <AUTHOR>
 * @date 2023年3月8日 上午11:30:33
 */
@Service
public class LogRoomService {

	@Autowired
	LogRoomRepository logRoomRepository;

	public LogRoom save(LogRoom logRoom) {
		return logRoomRepository.save(logRoom);
	}

	public List<LogRoom> findByPlaceIdAndAreaId(String placeId, String areaId) {
		return logRoomRepository.findByPlaceIdAndAreaIdAndFinished(placeId, areaId, 0);
	}

	public Optional<LogRoom> findByPlaceIdAndCardIdAndFinished (String placeId, String cardId) {
		return logRoomRepository.findByPlaceIdAndCardIdAndFinished(placeId, cardId, 0);
	}

	public List<LogRoom> findByPlaceIdAndAreaIdAndFinishedAndIsMaster(String placeId, String areaId, int isMaster) {
		return logRoomRepository.findByPlaceIdAndAreaIdAndFinishedAndIsMaster(placeId, areaId, 0, isMaster);
	}

	/**
	 * 查询所有主卡下机 副卡在线的记录
	 * @return
	 */
	public List<LogRoom> queryNotDismountedRoom() {
		return logRoomRepository.queryNotDismountedRoom();
	}

}
