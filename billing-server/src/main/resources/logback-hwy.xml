<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">

	<springProperty scope="context" name="appName" source="spring.application.name" />

	<include resource="org/springframework/boot/logging/logback/base.xml" />
	
	<appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
		<destination>10.0.2.50:5044</destination>
		<encoder charset="UTF-8" class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
			<providers>
				<timestamp>
					<timeZone>Asia/Shanghai</timeZone>
				</timestamp>
				<pattern>
					<pattern>
						{
							"logLevel": "%level",
							"appName": "${appName}",
							"pid": "${PID:-}",
							"trace": "%X{X-B3-TraceId:-}",
	            			"span": "%X{X-B3-SpanId:-}",
	           				"parent": "%X{X-B3-ParentSpanId:-}",
	            			"thread": "%thread",
							"class": "%logger{40}",
							"message": "%message",
							"stack_trace": "%exception"
						}
					</pattern>
				</pattern>
			</providers>
		</encoder>
	</appender>
	
	<root level="INFO">
		<appender-ref ref="LOGSTASH"/>
	</root>
	
</configuration>