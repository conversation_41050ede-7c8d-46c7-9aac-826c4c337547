eureka:
  instance:
    prefer-ip-address: true
    instanceId: ${spring.application.name}:${server.port}:${random.value}
    leaseRenewalIntervalInSeconds: 5
    healthCheckUrlPath: /actuator/health
    metadata-map:
      startup: ${random.int}
  client:
    registryFetchIntervalSeconds: 5
    serviceUrl:
      defaultZone: http://**************:9990/eureka/,http://*************:9990/eureka
spring:
  cloud:
    config:
      discovery:
        enabled: true
        serviceId: config-server