{"outPath": "./src/main/resources/static/doc", "allInOne": true, "packageFilters": "com.rzx.dim4.billing.web.controller.third.MarketController", "groups": [{"name": "小程序扫码认证相关接口", "apis": "com.rzx.dim4.billing.web.controller.third.MarketController"}], "errorCodeDictionaries": [{"title": "title", "enumClassName": "com.rzx.dim4.base.enums.ServiceCodes", "codeField": "code", "descField": "message"}], "dataDictionaries": [{"title": "网吧认证费用模式", "enumClassName": "com.rzx.dim4.base.enums.place.PlaceAuthFeeTypes", "codeField": "value", "descField": "name"}]}