广州中心网吧迁移数据库表数据迁移贵州中心操作文档  【有道云笔记】数据库表迁移sql脚本https://note.youdao.com/s/RTceZREt
说明：以billing_card_type表为例，所有数据库表的复制迁移操作，所有表只要根据sql语句查询出数据，然后再导入到贵州中心数据（通过执行导出的sql语句/运行sql文件）来完成数据迁移。查询数据可以使用sql语句，也可复制sql语句中where关键字后的过滤条件在工具中查询数据，然后复制导出数据。下面是操作步骤示例：


1、根据场所id查询出数据
select created, creater, deleted, updated, card_type_id, level, place_id, type_name, chain_card_type_id, min_create_card_amount, min_points_requirement from billing_card_type where place_id='**************'
2、以navicate工具为例，点击导出结果按钮，选择‘导出当前的结果’，选择“sql 脚本文件”，下一步，选择保存的位置和文件名称，点击下一步，点击开始按钮，执行成功，关闭。（也可全选查询出来的结果数据，右击选择“复制为insert语句”，用于数据迁移）
3、再导出的sql文件中，把‘’全局替换成billing_card_type，然后保存文件（如果是直接在工具内将查询结果"复制为insert语句"，则要替换的内容是``）。
4、迁移数据前先检查表中是否已经有该场所的错误数据，然后再开始导入数据。
右击要迁移的目标数据库，选择“运行sql文件”，选择上面第三步导出保存的文件，点击开始，看到“Finished successfully”后表示导入数据执行成功（或者通过直接执行sql来完成数据复制迁移，打开导出的sql文件，复制sql语句然后打开贵州中心数据库，在sql执行器中粘贴sql语句，点击【运行】，等待sql数据插入执行完成）。
5、在数据库表中执行查询命令，检查刚才我们导入的数据是否和广州中心数据库导出的sql文件内容一致。
select * from billing_card_type where place_id = '**************';
6、表数据迁移完成。


所有数据库表迁移完后，再迁移log_room、log_hb、log_acc、log_login、billing_card、billing_online 这几个表的数据，影响用户上下机和扣费余额问题。



下面开始从每个数据库表开始复制数据迁移动作：
4dim_admin数据库：整个数据库表数据已经复制到贵州中心，不需要单独复制表数据。
lgj_data_push 表不需要
log_login 表登录日志不需要
sys_account 系统账号，已经初始化好，不需要
sys_menu 系统菜单，已经复制初始化好
sys_resource 系统资源表
sys_rlat_account_role 管理中心账号
sys_rlat_menu_resource 管理中心账号对应的角色
sys_role 管理中心账号对应的角色
wechat_user 微信用户信息

4dim_notify数据库：是区域地区信息，已经提前迁移好数据。statistics_online_detail statistics_online_area statistics_online_region 这三张表不迁移，是实名上报数据，没有场所关联。

4dim_payment数据库:
1、检查payment_biz和payment_merchant表，广州数据库表有无新增数据，有的话需要同步。
2、支付订单表，查询数据并导入到贵州中心
SELECT created, creater, deleted, updated, biz_server, biz_type, handling_fee, id_number, informed, ldorderid, openid, order_amount, order_desc, order_id, pay_type, payappid, place_id, prepayid, return_code, return_message, settle_type, status, store_no, user_id, pay_params, fee_of_gift
FROM `4dim_payment`.payment_order where place_id='**************';
3、支付退款订单，查询数据并导入贵州中心
SELECT created, creater, deleted, updated, order_amount, order_id, place_id, refund_amount, refund_desc, refund_order_id, refund_status, return_code, return_message, ldorderid
FROM `4dim_payment`.payment_refund_order where place_id='**************';

4dim_place数据库：
代理商充值记录，不用迁移
SELECT id, create_name, created, creater, deleted, updated, account_id, account_name, agent_name, amount, balance, detail, `type`, voucher_url, account_spot, login_name, model, spot, unit
FROM `4dim_place`.log_agent_balance;
可删除表 `4dim_place`.log_place_renew，系统代码未使用
代理商延期记录
SELECT create_name, created, creater, deleted, updated, account_id, account_name, after_time, before_time, detail, model, place_id, place_name, price, total_price, unit
FROM `4dim_place`.log_renewal where place_id='**************';
场所账号，发现多余字段create_people，代码中entity没有该字段，可以删除
SELECT created, creater, deleted, updated, account_id, mobile, place_id, `type`, account_name, login_name, login_pass, create_name, chain_id, forbidden, allow_login_web, mini_app_auth, place_ids FROM `4dim_place`.place_account where place_id='**************';
场所代理商表，已经提前同步好数据
场所区域表
SELECT created, creater, deleted, updated, area_id, area_name, is_default, place_id, create_name, is_room FROM `4dim_place`.place_area where place_id='**************';
场所收银台
SELECT created, creater, deleted, updated, cashier_version, host_name, ip_addr, local_server_version, mac_addr, major_version, os_version, place_id, update_version, cashier_id, cashier_name, `type`, create_name FROM `4dim_place`.place_cashier where place_id='**************';
连锁场所列表。
SELECT create_name, created, creater, deleted, updated, chain_id, place_id, place_name, share_cash_account, share_member_point, share_present_account, subsidiary_ledger
FROM `4dim_place`.place_chain_stores where place_id='**************';
场所客户端，导入数据花费9s
SELECT created, creater, deleted, updated, area_id, client_id, client_version, host_name, ip_addr, mac_addr, place_id, gx_version, jf_version, jk_version, os_version, sp_version, create_name
FROM `4dim_place`.place_client where place_id='**************';
场所配置
SELECT created, creater, deleted, updated, cashier_local_server, online_topup, place_id, place_name, realname, renew, lock_and_login_out, wechat_qrcode_login, regcard, customize, create_name, random_code_login_number, super_login_config_for_json, shift_online_income, wechat_age_config, face_effective_time, nebula_open_flag, cash_package, alone_version, cashier_realname_qrcode, exchange_points, reward_points, check_regcard, qrcode_refresh_time, online_package, auth_way, qrcode_refresh_time_cashier, repeat_scan_code, quick_activation, realname_check_id_number, status, upgrade_v8_status, cashier_get_auth_image_flag, regcard_checked_clients
FROM `4dim_place`.place_config where place_id='**************';
场所菜单表已经复制初始化过，不需要再操作；
place_opinion_collect表 问卷，可不迁移
SELECT create_name, created, creater, deleted, updated, account_name, mobile, place_id, question_describe_collect, question_pic_collect
FROM `4dim_place`.place_opinion_collect where place_id='**************';
场所表
SELECT created, creater, deleted, updated, area_id, audit_id, address, billing_type, client_num, display_name, name, expired, frequent_contactor, frequent_contactor_mobile, geo_hash, identifier, is_registered, latitude, longitude, mer_username, place_id, region_code, `type`, cloud_version_expired, cashier_num, create_name, is_chain, version_flag, checked_region_code, first_use_v8_time, last_login_time
FROM `4dim_place`.place_profile where place_id='**************';
place_rate 这个表系统代码中已经不再使用，可以删除
place_renew_rate 这个表系统代码中已经不再使用，可以删除
网吧续费模式：全部复制迁移
SELECT id, create_name, created, creater, deleted, updated, account_id, account_name, agent_name, model, price_1, price_2, price_3, unit_1, unit_2, unit_3, exchange_spot
FROM `4dim_place`.place_renewal;
place_rlat_account_menu 表（账号菜单关系），根据场所账号过滤数据导出
SELECT create_name, created, creater, deleted, updated, account_id, menu_id
FROM `4dim_place`.place_rlat_account_menu where account_id in (SELECT account_id FROM 4dim_place.place_account where place_id='**************');
场所交班信息，导入运行5s
SELECT created, creater, deleted, updated, cashier_id, cash_income, cash_outcome, create_member_card, create_temporary_card, hand_in_cash, last_shift_leave_cash, member_card_cash_income, next_shift_handover_cash, off_working_time, on_duty_account_id, on_duty_account_name, place_id, remark, shift_id, status, successor_account_id, successor_account_name, temporary_card_balance, temporary_card_cash_income, temporary_card_outcome, total_cash_income, total_reversal, working_time, create_name, shop_cash_refund, shop_cash_total, shop_online_refund, shop_online_total, topup_online_income, topup_online_refund, total_online_income, other_income, one_month_card_amount, one_month_card_count, present_amount, three_month_card_amount, three_month_card_count, sum_cash_topup_for_third, sum_present_topup_for_third, count_member_surcharge, count_temp_surcharge, sum_member_surcharge, sum_temp_surcharge, two_years_card_amount, two_years_card_count, count_good_refund, count_good_sale, shop_card_total, shop_loss_total, sum_cash_topup_for_third_member, sum_cash_topup_for_third_temp, sum_present_topup_for_third_member, sum_present_topup_for_third_temp
FROM `4dim_place`.place_shift where place_id='**************';
renew_payment_order系统代码中没有该表，可以删除
SELECT id, created, creater, deleted, updated, account_id, business_id, handling_fee, ldorderid, mer_id, order_amount, order_id, original_params, place_id, place_name, product_desc, product_id, return_code, settle_type, status
FROM `4dim_place`.renew_payment_order;
场所客户端上网数据按日统计表，3367条数据，导入sql文件执行时间5s
SELECT create_name, created, creater, deleted, updated, area_id, area_name, client_id, count_day, host_name, place_id, sum_online_time, sum_online_visits
FROM `4dim_place`.statistics_client_by_day where place_id='**************';

4dim_user数据库：
dim4_user表(四维管家用户-微信实名认证信息)
dim4_user 表的数据需要从billing_card中根据场所过滤出来，导出，sql如下
select created, creater, deleted, updated, face_auth, id_number, mobile, name, name_auth, user_id, auth_image_key, auth_image_md5, last_auth_time from 4dim_user.dim4_user where id_number in ( select id_number from 4dim_billing.billing_card where place_id ='**************' )
log_wechat_scan_code 已经不再使用，不迁移
小程序用户
SELECT created, creater, deleted, updated, openid, place_id, place_name, nickname, account_id, chain_id FROM `4dim_user`.mini_app_user where place_id='**************';
微信用户 10条数据
SELECT created, creater, deleted, updated, city, country, headimgurl, id_number, `language`, latitude, longitude, nickname, openid, province, qr_scene, qr_scene_str, remark, sex, subscribe, subscribe_scene, subscribe_time, unionid, last_face_time, mini_open_id, v8openid, v8subscribe
FROM `4dim_user`.wechat_user where id_number in ( select id_number from 4dim_billing.billing_card where place_id ='**************' )

4dim_shop数据库：
goods商品表导入
select created, creater, deleted, updated, goods_id, goods_name, goods_stocks, goods_type_id, goods_type_name, place_id, shelves_time, unit_price, use_cash_account from 4dim_shop.goods
where place_id ='**************';
goods_stocks_detail 库存进货退款明细 1345条数据，导入花费14s
select created, creater, deleted, updated, changing_stocks, creator, goods_id, goods_name, goods_stocks, goods_type_id, goods_type_name, log_record_id, old_storage_number, operation_type, place_id, price_total, remark, shelves_time, storage_number, supplier_id, supplier_name, unit_price from 4dim_shop.goods_stocks_detail where place_id ='**************';
供应商
select created, creater, deleted, updated, contacts_name, contacts_phone, place_id, supplier_id, supplier_name from 4dim_shop.goods_suppliers where place_id ='**************';
商品类型
select created, creater, deleted, updated, goods_type_id, goods_type_name, place_id, sales_time from 4dim_shop.goods_type where place_id ='**************';
收银台记录日志 11558条记录 ，执行1分36秒
select created, creater, deleted, updated, cashier_id, cashier_name, changing_stocks, changing_type, desciption, goods_id, goods_name, goods_pic, goods_pic_md5, goods_type_id, goods_type_name, new_stocks, order_id, place_id, remark, shift_id, storage_number, supplier_id, supplier_name, unit_price from 4dim_shop.log_goods_stocks where place_id ='**************';
库存记录
select created, creater, deleted, updated, creator, goods_stocks, log_record_id, operation_type, place_id, price_total, remark, storage_number, supplier_id, supplier_name from 4dim_shop.log_storage_stocks where place_id='**************';
订单商品
select created, creater, deleted, updated, goods_id, goods_name, order_id, place_id, quantity, unit_price from 4dim_shop.order_goods where place_id='**************';
退款订单
select created, creater, deleted, updated, cashier_id, cashier_name, order_amount, order_id, place_id, refund_amount, refund_id, refund_time, refund_type, remark, shift_id from 4dim_shop.order_refund where place_id='**************';
订单退款商品
select created, creater, deleted, updated, goods_id, goods_name, order_id, place_id, quantity, refund_id from 4dim_shop.order_refund_goods where place_id='**************';
订单表，查询7088条数据 8s，导入花费1分16s
select created, creater, deleted, updated, card_id, cashier_id, cashier_name, client_id, client_name, finished_time, id_name, id_number, ld_order_id, order_amount, order_id, pay_time, pay_type, place_id, refund_time, remark, shift_id, source_type, status, buckle_type from 4dim_shop.orders where place_id='**************';
商超配置
select created, creater, deleted, updated, admin_switch, buckle_switch, limit_cash_account, order_switch, place_id, shop_switch, storage_warning_number, storage_warning_switch, take_stock_switch, up_down_switch, buckle_type from 4dim_shop.shop_config where place_id='**************';
商品销售每日统计
select created, creater, deleted, updated, count_day, count_refund, count_refund_cash, count_refund_online, count_sale, count_sale_cash, count_sale_online, goods_id, goods_name, goods_type_id, goods_type_name, place_id, sum_refund_cash_total, sum_refund_online_total, sum_refund_total, sum_sale_cash_total, sum_sale_online_total, sum_sale_total, sum_stock_minus_total, sum_stock_plus_total from 4dim_shop.statistics_goods_sale_by_day where place_id='**************';
库存商品
select created, creater, deleted, updated, goods_barcode, goods_id, goods_name, goods_pic, goods_stocks, goods_type_id, goods_type_name, place_id, unit_price, use_cash_account from 4dim_shop.storage_goods where place_id='**************';


4dim_billing数据库：
billing_card
这个表最后来操作，查询哪些不在上机的用户，然后把数据迁移过去；迁移成功后，最后只要把在上机的人数据迁移到贵州中心，即可。这样就可以保证因为场所会员数据太多影响正在上机的用户数据同步不准确。
log_room、log_hb、log_acc、log_login、billing_online这几个表都到最后迁移，等所有数据库表数据迁移完。
billing_card_black_list
1、根据场所id查询出数据
SELECT created, creater, deleted, updated, card_id, id_name, id_number, limit_date, limit_end_time, limit_start_time, place_id
FROM `4dim_billing`.billing_card_black_list where place_id = '**************';
billing_card_type 会员卡类型
1、根据场所id查询出数据
select created, creater, deleted, updated, card_type_id, level, place_id, type_name, chain_card_type_id, min_create_card_amount, min_points_requirement from billing_card_type where place_id='**************'
billing_rule_common 计费规则
SELECT created, creater, deleted, updated, area_id, area_name, card_type_id, card_type_name, place_id, prices, rule_id, rule_name, free_minutes, min_consume, unit_consume
FROM `4dim_billing`.billing_rule_common where place_id='**************';
billing_rule_acc 自动累加计费规则
SELECT created, creater, deleted, updated, acc_price, area_id, card_type_id, end_time, place_id, rule_id, rule_name, start_time, weekend_disabled
FROM `4dim_billing`.billing_rule_acc where place_id='**************';
billing_rule_package_time 包时规则
SELECT created, creater, deleted, updated, area_id, card_type_id, duration_time, end_time, limit_end_time, package_flag, place_id, price, rule_id, rule_name, start_time, limit_must_cash, limit_client_show
FROM `4dim_billing`.billing_rule_package_time where place_id='**************';
bookseats 订座
SELECT created, creater, deleted, updated, actual_cost, actual_end_time, area_id, area_name, card_id, card_type_id, card_type_name, client_ids, duration, estimated_cost, finish_type, free_time, id_name, id_number, idcard_num, place_id, price, room_flag, rule_id, start_time, status, unlock_card_id, unlock_code, unlock_id_name, unlock_id_number, updater
FROM `4dim_billing`.book_seats where place_id='**************';
收银台操作权限
SELECT created, creater, deleted, updated, account_id, account_name, place_id, card_type_authority, op_authority
FROM `4dim_billing`.cashier_authority where place_id='**************';
收银台任务
SELECT created, creater, deleted, updated, account_id, account_name, place_id, card_type_authority, op_authority
FROM `4dim_billing`.cashier_authority where place_id='**************';
客户端升级记录 ，导入所有数据
SELECT created, creater, deleted, updated, client_type, place_id, version_id
FROM `4dim_billing`.client_upgrade';
客户端版本，导入所有数据
SELECT created, creater, deleted, updated, client_type, file_md5, url, version_id, version_number, description
FROM `4dim_billing`.client_version;
客户端锁屏壁纸，导入所有数据
SELECT created, creater, deleted, updated, description, md5, wallpaper_name, wallpaper_url
FROM `4dim_billing`.client_wallpaper';
客户端锁屏壁纸投放
SELECT id, created, creater, deleted, updated, creater_name, end_time, is_default, md5, place_ids, place_names, start_time, wallpaper_url
FROM `4dim_billing`.client_wallpaper_deliver;
积分兑换规则
SELECT created, creater, deleted, updated, exchange_points, exchange_points_rule_id, exchange_present, exchange_type, place_id
FROM `4dim_billing`.exchange_points_rule where place_id='**************';
lgj_billing_card_type_relation表：
SELECT created, creater, deleted, updated, card_type_id, lgj_card_type_id, place_id, type_name
FROM `4dim_billing`.lgj_billing_card_type_relation where place_id='**************';
lgj_data_push_record 龙管家数据推记录表，不需要迁移
会员签到记录表
SELECT id, created, creater, deleted, updated, card_id, card_type_id, card_type_name, count_signed_of_last_day, id_name, id_number, place_id, signed_date, signed_day
FROM `4dim_billing`.log_member_signed where place_id='**************';


操作记录表
log_operation_202408
执行sql查询出要导入的网吧当月今天之前的操作数据（说明：created的参数内容根据实际日期）：
SELECT * FROM log_operation_202408  where place_id='**************'  and created < '2024-08-18 00:00:00';
查询需要25s，导出25ms 16249条数据 导入执行时间:806s

导入后查看操作记录条数是否一致
SELECT count(*) FROM log_operation_202408 where place_id='**************'

然后再把今天的操作数据查询导入：
SELECT * FROM log_operation_202408  where place_id='**************'  and created > '2024-08-18 00:00:00';
导入后查看操作记录条数是否一致
SELECT count(*) FROM log_operation_202408 where place_id='**************'



log_other_income其他收入日志表
SELECT created, creater, deleted, updated, amount, creater_name, details, event_description, log_other_income_id, place_id, shift_id, `type`
FROM `4dim_billing`.log_other_income where place_id='**************' ;
log_place_chain 场所连锁日志
根据当前场所名字字段查询出数据：
SELECT created, creater, deleted, updated, card_id, chain_id, cost_cash_account, cost_place_id, cost_present_account, cost_uid, curr_place_id, login_id, cost_temporary_online_account
FROM 4dim_billing.log_place_chain where curr_place_id = '**************' or cost_place_id='**************';
查询时间222s 该网吧没有相关数据.
二维码记录，导入1.92s
SELECT created, creater, deleted, updated, area_id, cashier_id, client_id, deadline, params, place_id, token, used, uuid FROM `4dim_billing`.log_qrcode where place_id='**************' ;
赠送分期记录表
SELECT created, creater, deleted, updated, id_number, once_present_amortized, once_present_amount, place_id, rule_id, status, sum_present_amortized, sum_present_amount, topup_order_id
FROM `4dim_billing`.log_record_reward_installment where place_id='**************';
退款日志记录
SELECT created, creater, deleted, updated, card_id, card_type_id, card_type_name, cash_refund, id_name, id_number, ldorderid, online_refund, operator_name, place_id, refund_desc, refund_order_id, return_message, shift_id, source_type, refund_type, order_id, status
FROM `4dim_billing`.log_refund where place_id='**************';
冲正日志记录
SELECT created, creater, deleted, updated, card_id, cash_reversal, id_name, id_number, operator, place_id, present_reversal, remark, shift_id, topup_order_id
FROM `4dim_billing`.log_reversal where place_id='**************';
当班日志
SELECT created, creater, deleted, updated, account_id, account_name, account_type, place_id, shift_id, login_account_id, login_account_name, cashier_id, status
FROM `4dim_billing`.log_shift where place_id='**************';
本地模式业务数据日志
SELECT created, creater, deleted, updated, cost, id_number, offline_data_id, operation_time, operation_type, place_id, present, request_id, shift_id, status
FROM `4dim_billing`.log_syn_offline_data where place_id='**************';
充值日志，4296条数据,插入时间176.25s，导入sql文件形式时间35s
SELECT created, creater, deleted, updated, card_id, cash_amount, cash_balance, card_type_name, card_type_id, client_id, id_name, id_number, ld_order_id, login_id, operator, opt_type, order_id, pay_type, pay_url, place_id, present_amount, present_balance, qrcode_url, refund_status, shift_id, status, source_type, topup_rule_id, topup_time, remark, operator_name, rule_id, topup_source_type, temporary_online_balance
FROM `4dim_billing`.log_topup where place_id='**************';
营销大师充值/扣款 日志表， 查询无数据
SELECT created, creater, deleted, updated, card_id, cash_amount, cash_balance, client_id, id_name, id_number, name, opt_type, order_id, place_id, present_amount, present_balance, remark, third_account_id
FROM `4dim_billing`.market_log_operation where place_id='**************';
预约包时信息，用户上机之前通过收银台或者其他方式预定的包时
SELECT created, creater, deleted, updated, area_id, card_id, end_time, id_name, id_number, place_id, price, rule_id, shift_id, start_time, status, limited_login, package_pay_flag, cost_temporary_online_account
FROM `4dim_billing`.package_time_reserve where place_id='**************';
场所业务参数配置表
SELECT created, creater, deleted, updated, billing_type, cash_package_time, client_logout_operation, client_shutdown_time, disable_client_package_time, member_day, member_day_num, place_id, place_realname_disabled, surcharge, client_qrcode_auth, `type`, anti_addiction, force_password, january_registration_card, march_registration_card, modify_password, non_id_number, forbidden_client_active_directly, query_password, deduct_minimum_spending_upon_login, lost_client_logout, package_end_logout_flag, alipay_app_active_flag, alipay_app_face_flag, alipay_app_scancode_flag, book_seats_flag, book_seats_free_time, cannot_book_client_ids, self_exchange_flag, culture_box_flag, third_account_id, app_open_card_auth, downgrade_user_level_flag, pay_patter, self_checkout_flag_for_temp_card, temp_card_points_upgrade, upgrade_user_level_flag, temp_card_wechat_upgrade_flag, two_years_registration_card, book_seats_flag_third, book_seats_flag_v8, use_reg_password FROM `4dim_billing`.place_biz_config where place_id='**************';
连锁场所表
SELECT created, creater, deleted, updated, chain_id, place_id, place_name, share_cash_account, share_member_point, share_present_account, subsidiary_ledger
FROM `4dim_billing`.place_chain_stores where place_id='**************';
连锁卡类型，如果连锁场所表place_chain_stores查询有数据，则复制该场所的chain_id值作为参数查询
SELECT created, creater, deleted, updated, card_type_id, chain_id, type_name, min_create_card_amount, min_points_requirement
FROM `4dim_billing`.place_chain_card_type where chain_id='xxxx';
网吧接口请求次数配置表
SELECT created, creater, deleted, updated, `number`, `type`, place_id, cashier_number, place_card_id_number, place_log_login_details_number, place_log_login_number, cashier_log_login_number
FROM `4dim_billing`.security_request_config where place_id='**************';
statistics_active_by_day 这个表当前系统代码中不存在，但数据库中存在，可删除。
操作记录日统计表
SELECT created, creater, deleted, updated, count_day, place_id, sum_consumption_total, sum_cost_cash_income, sum_cost_cash_outcome, sum_cost_online_income, sum_cost_online_outcome, sum_online_time, sum_online_visits, sum_cost_total_reversal, sum_online_num
FROM `4dim_billing`.statistics_by_day where place_id='**************';
操作记录按小时统计表
SELECT created, creater, deleted, updated, count_day, display_name, online_num, place_id
FROM `4dim_billing`.statistics_online_by_hour where place_id='**************';
按日统计表
SELECT created, creater, deleted, updated, count_day, count_package, count_reversal, count_topup, create_member_card_num, create_temporary_card_num, place_id, sum_cash_out_income, sum_cash_topup_income, sum_client_topup_income, sum_member_topup_income, sum_mp_topup_income, sum_package_total, sum_present_income, sum_temporary_topup_income, sum_total_income, sum_total_reversal, count_refund, sum_present_reversal_total, sum_present_topup_total, other_income, other_outcome, sum_online_out_income, sum_app_topup_income, sum_cashier_online_topup_income
FROM `4dim_billing`.statistics_operation_by_day where place_id='**************';
附加费配置
SELECT created, creater, deleted, updated, active_types, deduct_account, deduct_member_card, deduct_temp_card, place_id
FROM `4dim_billing`.surcharge_config where place_id='**************';
同步离线数据表
SELECT created, creater, deleted, updated, file_name, file_url, place_id, request_id
FROM `4dim_billing`.syn_offline_data where place_id='**************';
系统接口调用信息表`4dim_billing`.sys_interface，不需要迁移
SELECT created, creater, deleted, updated, avg_use_time, indexes, last_use_time, name, times
FROM `4dim_billing`.sys_interface;
修改会员卡类型临时表
SELECT created, creater, deleted, updated, card_id, card_type_id, card_type_name, place_id, chain_card, chain_card_type_id
FROM `4dim_billing`.temp_billing_card_type_modify where place_id='**************';
嘟嘟牛用户
SELECT created, creater, deleted, updated, card_id, card_id_show, cash_account, dis, heap_can_use, name_show, place_id, present_account
FROM `4dim_billing`.temp_duduniu_user where place_id='**************';
计费卡激活时，附加费记录临时表
SELECT created, creater, deleted, updated, card_id, deduct_account, deduct_member_card, deduct_temp_card, place_id, source_type, id_number
FROM `4dim_billing`.temp_record_surcharge where place_id='**************';
temp_topup表没有使用，不用迁移
万象网吧转新计费，万现消费的临时表
SELECT created, creater, deleted, updated, card_id, memo, note, place_id, received, remain_after, remain_before, time1, time2
FROM `4dim_billing`.temp_wanxiang_consume where place_id='**************';
万象网吧转新计费，万现充值的临时表
SELECT created, creater, deleted, updated, card_id, place_id, remain, remain_after, remain_before, topup_time, `type`
FROM `4dim_billing`.temp_wanxiang_topup where place_id='**************';
万象网吧转新计费，万现用户的临时表
SELECT created, creater, deleted, updated, card_id, card_id_show, dis, heap_can_use, name_show, place_id, remain, remain_cash_account
FROM `4dim_billing`.temp_wanxiang_user where place_id='**************';
第三方账号已迁移好，后续新增第三方账号两个中心都要同步加到表里 `4dim_billing`.third_account;
第三方账号权限已迁移好，后续新增第三方账号权限两个中心都要同步加到表里` `4dim_billing`.third_authority;
第三方场所配置表 `4dim_billing`.third_place_config;
SELECT created, creater, deleted, updated, active_notify_url, billing_type, encryp_key, login_check_url, place_id, third_account_id, `type`
FROM `4dim_billing`.third_place_config where place_id='**************';
第三方请求日志，查询12s，2266条数据，导入sql文件运行30s
SELECT created, creater, deleted, updated, request_desc, request_index, request_name, request_params, response_code, response_data, response_msg, spend_time, stack_trace, place_id, third_account_id
FROM `4dim_billing`.third_request_log where place_id='**************';
充值规则
SELECT created, creater, deleted, updated, amount, card_type_id, place_id, present_amount, topup_rule_id, card_type_name, effective_days, topup_type, limit_client_show, cashier_fixed, cashier_usage, client_usage, effective_mode, effective_weekdays, mobile_usage, new_card_reward, status, topup_mode, member_recharge, present_amortized
FROM `4dim_billing`.topup_rule where place_id='**************';
log_points 积分日志（放到后面，因为上机过程中会产生积分）
SELECT created, creater, deleted, updated, card_id, card_type_id, card_type_name, creater_name, details, exchange_points, exchange_points_type, id_name, id_number, place_id, total_points
FROM 4dim_billing.log_points WHERE place_id = '**************';
保存查询结果为文件，然后导入到贵州中心数据库，navicate导入6s，执行35s
#4dim_billing数据库表全部迁移完成。


4dim_regcard数据库：
注册卡表
1、先在`4dim_place`数据库的place_profile表中查询当前场所的audit_id字段值；
SELECT audit_id FROM `4dim_place`.place_profile where place_id='**************';
2、使用上一步查询到的audit_id 的值作为place_id 参数进行查询注册卡数据.
SELECT area_id, place_id, serial_number, card_number, order_id, agent_id, agent_name, card_type, price, used, valid_days, buy_time, valid_time, created, updated, swgj_place_id
FROM `4dim_regcard`.regcard where place_id='xxx'
注册卡日志表
SELECT created, updated, card_number, serial_number, id_number, mobile, place_id, order_id, agent_id, agent_name, card_type, price, buy_time, reg_time, valid_time, is_physical_card, remaining, shift_id, valid_days, id_name
FROM `4dim_regcard`.regcard_log where place_id='**************';
注册卡服务配置
SELECT place_id, server_group_name, addresses, created, updated
FROM `4dim_regcard`.regcard_server_config where place_id='**************';
#注册卡服务数据库表数据复制迁移完成



关键步骤：
开始log_room、log_hb、log_acc、log_login、billing_card、billing_online 这几个表的数据，影响用户上下机和扣费余额问题。以下是sql脚本：

1、log_room表查询包间数据，并导入到贵州中心
SELECT created, creater, deleted, updated, area_id, billing_time, card_id, card_type_id, client_id, finished, finished_time, is_master, place_id
FROM 4dim_billing.log_room where place_id='**************';
2、根据place_id 去 log_acc 表查询出该网吧所有数据，并导入到贵州中心
SELECT created, creater, deleted, updated, acc_price, acc_rule_id, curr_price, end_time, finish_time, login_id, place_id, start_time FROM 4dim_billing.log_acc where place_id='**************';
3、根据place_id去log_hb 表查询出该网吧所有数据，并导入到贵州中心
SELECT created, creater, deleted, updated, billing_flag, client_id, hb_time, place_id, status FROM 4dim_billing.log_hb where place_id='**************';
4、根据placeId去log_login表查询已经下机的数据并导入贵州中心，导入耗时320s
SELECT created, creater, deleted, updated, card_id, client_id, consumption_total, last_client_id, login_time, login_type, logout_time, place_id, card_type_id, login_id, logout_operation_creater, logout_operation_creater_name, online_time, id_number, total_account, logout_type, active_time, chain_flag
FROM 4dim_billing.log_login where place_id='**************' and logout_time is not null;
5、根据placeId去billing_online表查询出当前场所不在线的数据，导入到贵州中心
SELECT created, creater, deleted, updated, area_id, billing_time, card_id, card_type_id, client_id, common_price, deduction, finished, id_name, id_number, login_id, next_time, package_flag, place_id, rule_id, timer_flag, remark, acc_flag, package_pay_flag
FROM 4dim_billing.billing_online where place_id='**************' and finished =1;


6、再去log_login 表查询还没下机的数据，导入到贵州中心。
SELECT created, creater, deleted, updated, card_id, client_id, consumption_total, last_client_id, login_time, login_type, logout_time, place_id, card_type_id, login_id, logout_operation_creater, logout_operation_creater_name, online_time, id_number, total_account, logout_type, active_time, chain_flag
FROM 4dim_billing.log_login where place_id='**************' and logout_time is null;

7、去billing_card 表 查询出当前不在线的会员，并导入到贵州中心。
先查询出在线的人的card_id。
select card_id from 4dim_billing.billing_online where place_id='**************' and finished =0;
根据查询到的card_id 列表，如‘********','********’作为查询参数。

//注意这里的card_id要换成实际查询出来的值
SELECT created, creater, deleted, updated, active_time, address, cash_account, card_id, card_type_id, card_type_name, id_name, id_number, issuing_authority, login_name, login_pass, nation, phone_number, place_id, present_account, valid_period, remark, idcard_num, points, chain_card, active_type, lgj_card_flag, chain_id, temporary_online_account, chain_card_type_id
FROM 4dim_billing.billing_card where place_id='**************' and card_id not in ('********','********' );

8、去billing_card 表 查询出当前在线的会员，并导入到贵州中心。要排除已经导入过的下线的会员card_id。
card_id参数就是第6步查询出来的值。
//注意这里的card_id要换成实际查询出来的值

SELECT created, creater, deleted, updated, active_time, address, cash_account, card_id, card_type_id, card_type_name, id_name, id_number, issuing_authority, login_name, login_pass, nation, phone_number, place_id, present_account, valid_period, remark, idcard_num, points, chain_card, active_type, lgj_card_flag, chain_id, temporary_online_account, chain_card_type_id
FROM 4dim_billing.billing_card where place_id='**************' and card_id in
('********','********');

9、最后根据place_id去 billing_online 表 查询出当前场所会员在线数据，并导入到贵州中心。
SELECT created, creater, deleted, updated, area_id, billing_time, card_id, card_type_id, client_id, common_price, deduction, finished, id_name, id_number, login_id, next_time, package_flag, place_id, rule_id, timer_flag, remark, acc_flag, package_pay_flag
FROM 4dim_billing.billing_online where place_id='**************' and finished =0;

10、表数据迁移完成，调用网吧迁移接口 https://mp.4wgj.com/notify-server/polling/convert?placeId=**************&domain=southwest.4wgj.com



网吧迁移准备工作步骤：
1、广州中心和贵州中心（同步广州中心所有最新的服务）的多中心服务版本。（同时更新config文件dim4.host.domain等新增参数配置）

2、检查迁移的网吧（吉林地区**************）的客户端版本号是否是高版本，符合迁移条件。


3、开始手动备份广州中心网吧数据库数据，注意最后的billing_card和billing_online等表数据实时性高且影响用户下机和卡余额。

4、调用广州中心的网吧转换中心接口（收银台轮询到网吧转换中心标记，改变收银台和客户端配置的中心连接地址）。查看广州中心billing服务日志，是否转换中心地址成功，是否调用app通知接口成功（易上网app方本地数据库中存储的网吧信息中，会增加一个中心域名字段，全部默认广州中心域名地址www.4wgj.com。在调用网吧转换中心接口成功改变中心地址后，易上网app方会改变被迁移网吧的中心域名为目标中心域名地址，如southwest.4wgj.com。易上网app每次执行业务都会拿placeId去和数据库中网吧保存的placeId实时比较，如果扫码的二维码中的placeId和数据库中的不一致，采用数据库中的域名进行请求。避免因网吧被迁移后，用户使用app执行操作依然访问的是旧中心域名地址）。

5、观察网吧上机用户功能是否正常（使用易上网app扫码上机）。

